const float32 DEATH_SPECTATE_BUFFER	   = 3.0;
const float32 DEAD_MENU_BUFFER_SECONDS = 19.0;

enum ERespawnRequestType
{
	UNSPECIFIED,
	AT_BEACON,
	ZIPLINE,
	SHIELD_BREAKER_CARRIER,
	TRAINING_AT_LOCATION,
	INVALID,
}

struct FRespawnRequestPackage
{
	ERespawnRequestType type;
	AAS_BaseRespawnBeacon respawnBeacon;
	FVector spawnLoc;
	// FVector camWorldLoc;		// unused
	FRotator camWorldRot;
	bool autoSwitchToShieldBreakerCarrierAllowed = true;
}

UCLASS( Abstract )
class AAS_GameModeBase_RaidMode : AAS_GameModeBase
{
	UPROPERTY( EditDefaultsOnly, Category = "Default Classes" )
	TSubclassOf<AAS_SpeedGate> speedGateClass;

	UPROPERTY( EditDefaultsOnly, Category = "Default Classes" )
	TSubclassOf<AAS_SBCharger> sbChargerClass;

	UPROPERTY( EditDefaultsOnly, Category = "Default Classes" )
	TSubclassOf<AAS_RespawnTotem> respawnTotemClass;

	default NumTeams		 = 2;
	default PlayersPerTeam	 = 3;
	default TimeLimitMinutes = -1;

	default lootDropsOnDeath.Add( GameplayTags::Loot_Placeables_Shieldbreaker );
	default lootDropsOnDeath.Add( GameplayTags::Loot_Placeables_ShieldBreakerTaubeTest );

	default GamemodeRules_BaseSelectEnabled = true;

	const bool TOTEM_ENABLED						 = true;
	default RespawnRules_RespawnTotemEnabled		 = TOTEM_ENABLED ? true: false;
	default GamemodeRules_EnableOutsideDomeDetection = true;
	default RespawnRules_RaidRespawn_DefenderLives	 = 8;
	default RespawnRules_RaidRespawn_AttackerLives	 = 6;

	default GamemodeRules_RaidBombTimerDefuseTime = 6;
	default GamemodeRules_RaidBombTimerNumStages  = 3;
	default GamemodeRules_RaidBombTimerDurationMS = TO_MILLISECONDS( 20 );

	default GamemodeRules_BaseDestruction_XP = 2;

	default GamemodeRules_ClearBackpacksOnRespawn = false;

	default GameModeRules_CanPlaceWhileInRaid = true;

	default GamemodeRules_StartingHealItems = 8;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset breacherDroppedSound;

	FGameplayTag extenderLootItem = GameplayTags::Loot_Placeables_MiniShieldBreaker;
}

AAS_GameModeBase_RaidMode RaidModeDefaults()
{
	return Cast<AAS_GameModeBase_RaidMode>( GameModeDefaults() );
}

UAS_ServerScript_Mode_RaidMode RaidModeServerScript()
{
	return Cast<UAS_ServerScript_Mode_RaidMode>( GetServerScript() );
}

UCLASS( Abstract )
class UAS_ServerScript_Mode_RaidMode : UAS_ServerScript_Mode
{
	default teamStateManagerClass	  = AAS_TeamStateManager_RaidMode::StaticClass();
	default PREMATCH_DURATION_SECONDS = GameConst::RAID_MODE_PREMATCH_DURATION;

	default sendMatchEndToWinnerOnly = true;
	default waitingForPlayersEnabled = true;

	TArray<ANCPlayerCharacter> playersWhoHaveWalls;

	bool eliminateOnDefenseLives = true;

	// These items are given once, at the start of the game, to each player
	UPROPERTY( EditDefaultsOnly, meta = ( ForceInlineRow ) )
	TMap<FGameplayTag, int> gameStartItemsToGivePlayer;

	TMap<ANCPlayerCharacter, AAS_RespawnTotem> playerTotems;

	UFUNCTION( BlueprintOverride )
	void BlueprintStart()
	{
		Super::BlueprintStart();

		FBackpackItemStruct startingShieldItem;
		startingShieldItem.itemIndex = GameplayTags::Loot_HealItem_SmallShield;
		startingShieldItem.itemCount = GameModeDefaults().GamemodeRules_StartingHealItems;
		DefaultBackpackItems.Add( startingShieldItem );

		ScriptCallbacks().RegisterSignalCallback( Signals::ON_BASE_DESTROYED, this, n"Signal_OnBaseDestroyed" );

		for ( int i = 0; i < GetNumTeams(); i++ )
		{
			AAS_TeamConnectedBox box = Cast<AAS_TeamConnectedBox>( Server_SpawnEntity( AAS_TeamConnectedBox::StaticClass(), nullptr ) );
			box.SetTeam( i );
		}

		ScriptCallbacks().server_onBaseInitialized.AddUFunction( this, n"OnBaseInitialized" );

		ScriptCallbacks().server_onTeamLevelChanged.AddUFunction( this, n"OnTeamLevelChanged" );

		ScriptCallbacks().server_onTeamSpawnedAtBase.AddUFunction( this, n"OnTeamSpawnedAtBase" );

		ScriptCallbacks().server_onBombPlanted.AddUFunction( this, n"OnRaidBombPlanted" );
		ScriptCallbacks().server_onBombDefused.AddUFunction( this, n"OnRaidBombDefused" );
		ScriptCallbacks().server_onBombExplode.AddUFunction( this, n"OnRaidBombExplode" );
		ScriptCallbacks().shared_OnDomeEnemyCountChanged.AddUFunction( this, n"OnDomeEnemyCountChanged" );
		ScriptCallbacks().server_OnBreacherShutdown.AddUFunction( this, n"OnBreacherShutdown" );
		ScriptCallbacks().shared_OnRaidStarted.AddUFunction( this, n"OnRaidStarted" );
		ScriptCallbacks().shared_OnRaidEnded.AddUFunction( this, n"OnRaidEnded" );

		ScriptCallbacks().server_onPlayerDamaged.AddUFunction( this, n"OnPlayerPostDamage" );

		ScriptCallbacks().server_OnBreacherPlanted.AddUFunction( this, n"OnAnyBreacherPlanted" );
		ScriptCallbacks().server_OnShieldBreakerCraftingStarted.AddUFunction( this, n"OnShieldBreakerCraftingStarted" );
		ScriptCallbacks().server_onPlayerHasShieldBreakerChanged.AddUFunction( this, n"OnAnyPlayerHasShieldBreakerChanged" );
		ScriptCallbacks().server_OnShieldBreakerCrafterTrackedItemChanged.AddUFunction( this, n"OnShieldBreakerCrafterTrackedItemChanged" );

		ScriptCallbacks().server_OnDoorOpenedOrClosed_old.AddUFunction( this, n"OnDoorOpenedOrClosed_old" );
		ScriptCallbacks().server_OnDoorOpenedOrClosed.AddUFunction( this, n"OnDoorOpenedOrClosed" );
		UNCDestructionManager::Get().OnDestructibleDamaged_Server.AddUFunction( this, n"OnDestructibleDamaged" );
	}

	UFUNCTION( BlueprintEvent )
	void OnShieldBreakerCraftingStarted( AAS_SBCrafter sbCrafter )
	{
		UpdateTeamObjectives();
	}

	TArray<AFXActor> beams;
	TMap<AAS_RaidDomeShield, AAS_RaidOutsideDomeScanner> domeToScanner;

	UFUNCTION( BlueprintEvent )
	void OnAnyPlayerHasShieldBreakerChanged( ANCPlayerCharacter player, bool oldValue, bool newValue )
	{
		UpdateTeamObjectives();
	}

	UFUNCTION( BlueprintEvent )
	void OnShieldBreakerCrafterTrackedItemChanged( AAS_SBCrafter sbCrafter, AActor oldValue,
												   AActor newValue )
	{
		UpdateTeamObjectives();
	}

	TArray<ANCPlayerCharacter> playersSentHints;

	UFUNCTION()
	private void OnDoorOpenedOrClosed_old( AAS_ScriptDoor door, AAS_PlayerEntity player, bool opened )
	{
		if ( !IsValid( door.baseOwnershipComponent.base ) )
			return;

		if ( opened )
			return;

		if ( playersSentHints.Contains( player ) )
			return;

		if ( IsTeamBeingRaided( player.GetTeam() ) )
			return;

		UNCRemoteScriptCommands::SendServerCommand( player, "DoorCloseHint" );
		playersSentHints.Add( player );
	}

	UFUNCTION()
	private void OnDoorOpenedOrClosed( ANCDoor door, AAS_PlayerEntity player, bool opened )
	{
		if ( opened )
			return;

		if ( !IsValid( player ) )
			return;

		if ( playersSentHints.Contains( player ) )
			return;

		if ( !door.CanHaveTeam() )
			return;

		if ( !IsValid( door.GetBaseOwnershipComponent() ) )
			return;

		if ( !IsValid( door.GetBaseOwnershipComponent().base ) )
			return;

		int team = door.GetTeam();

		if ( team != player.GetTeam() )
			return;

		if ( IsTeamBeingRaided( player.GetTeam() ) )
			return;

		playersSentHints.Add( player );
		UNCRemoteScriptCommands::SendServerCommand( player, "DoorCloseHint" );
	}

	TArray<ANCPlayerCharacter> playersThatHaveUsedRaidTool;
	TMap<ANCPlayerCharacter, int> nextAllowRaidToolHintTime;

	UFUNCTION()
	private void OnDestructibleDamaged( ANCDestructible destructible, const FDamageInfo&in damageInfo )
	{
		ANCPlayerCharacter attacker = damageInfo.attacker;

		FName weaponClassName = damageInfo.GetDamageSourceName();

		if ( weaponClassName != NAME_None && IsValid( attacker ) )
		{
			if ( !IsWeaponLoaded( weaponClassName ) )
				return;

			UWeaponPrimaryAsset asset = GetWeaponAsset( weaponClassName );
			if ( IsValidWeaponScriptDataAsset( asset ) )
			{
				FWeaponLoadoutDataStruct wStruct = GetWeaponScriptData( asset );
				if ( wStruct.playerLoadoutType == EWeaponLoadoutType::PRIMARY || wStruct.playerLoadoutType == EWeaponLoadoutType::MELEE )
				{
					if ( !playersThatHaveUsedRaidTool.Contains( attacker ) && IsTeamRaiding( attacker.GetTeam() ) )
					{
						if ( !nextAllowRaidToolHintTime.Contains( attacker ) || GetGameTimeMS() > nextAllowRaidToolHintTime[attacker] )
						{
							ANCWeapon raidTool = attacker.GetWeaponAtSlot( WeaponSlot::RaidToolsSlot );
							if ( IsValid( raidTool ) )
							{
								if ( ( raidTool.GetClipAmmo() > 0 || raidTool.GetStockpileAmmo() > 0 ) )
								{
									nextAllowRaidToolHintTime.Add( attacker, GetGameTimeMS() + TO_MILLISECONDS( 30.0 ) );
									UNCRemoteScriptCommands::SendServerCommand( attacker, "HintUseRaidTool" );
								}
							}
						}
					}
				}
				else if ( wStruct.playerLoadoutType == EWeaponLoadoutType::RAID )
				{
					playersThatHaveUsedRaidTool.AddUnique( attacker );
				}
			}
		}
	}

	UFUNCTION()
	private void OnAnyBreacherPlanted( ANCPlayerCharacter planter, AAS_ShieldBreaker shieldBreaker, AAS_RaidDomeShield dome )
	{
		if ( GameModeDefaults().GamemodeRules_EnableOutsideDomeDetection )
		{
			AAS_RaidOutsideDomeScanner scanner = Cast<AAS_RaidOutsideDomeScanner>( SpawnActor( AAS_RaidOutsideDomeScanner::StaticClass() ) );
			scanner.Init( shieldBreaker, dome );
			domeToScanner.Add( dome, scanner );
		}

		shieldBreaker.OnBreacherPenetrated.AddUFunction( this, n"OnBreacherPenetrated" );
	}

	UFUNCTION( BlueprintEvent )
	void OnBreacherPenetrated( AAS_ShieldBreaker shieldBreaker, AAS_RaidDomeShield dome )
	{
		shieldBreaker.OnBreacherPenetrated.Unbind( this, n"OnBreacherPenetrated" );

		SendTemplatedMessageToTeam( shieldBreaker.GetTeam(), EPriorityMessageTemplate::V2_ATTACK_DOME_BREACHED );
		SendTemplatedMessageToTeam( dome.GetTeam(), EPriorityMessageTemplate::V2_DEFEND_DOME_BREACHED );
	}

	UFUNCTION()
	private void OnPlayerPostDamage( AAS_PlayerEntity player, const FDamageInfo& damageInfo )
	{
		if ( IsValid( damageInfo.attacker ) )
		{
			AAS_RaidDomeShield dome = GetRaidDomeShieldForTeam( damageInfo.attacker.GetTeam() );
			if ( domeToScanner.Contains( dome ) && IsValid( domeToScanner[dome] ) )
			{
				AAS_PlayerEntity attacker = Cast<AAS_PlayerEntity>( damageInfo.attacker );
				if ( dome.largePlayerDetectionRadius.playersInside.Contains( player ) && !dome.largePlayerDetectionRadius.playersInside.Contains( attacker ) )
				{
					domeToScanner[dome].ScanPlayer( attacker );
					domeToScanner[dome].forcedScanPlayers.AddUnique( attacker );
				}
			}
		}

		const bool downed = Bitflags::HasFlag( damageInfo.scriptDamageFlags, EScriptDamageFlags::DF_DOWNED );
		const bool killed = Bitflags::HasFlag( damageInfo.damageFlags, EDamageFlags::DF_KILLSHOT );
		if ( downed || killed )
		{
			if ( IsValid( damageInfo.attacker ) && damageInfo.attacker != player )
			{
				AActor inflictor	= damageInfo.Inflictor;
				ANCProjectile proj	= Cast<ANCProjectile>( inflictor );
				FGameplayTag rarity = GameplayTags::Loot_Rarity_Basic;
				if ( IsValid( proj ) )
				{
					ANCWeapon weapon = proj.GetWeapon();
					if ( IsValid( weapon ) && IsLootIndexValidForWeapon( weapon ) )
					{
						FLootDataStruct lootData = GetLootDataForWeapon( weapon );
						rarity					 = lootData.rarity;
					}
				}

				if ( killed )
				{
					AAS_PlayerEntity attacker = Cast<AAS_PlayerEntity>( damageInfo.attacker );
					if ( IsValid( attacker ) )
					{
						Server_EmitSoundOnEntity_WithSendFlags( Audio().friendlyPlayerKilled, player, attacker, ESendEventFlags::SEND_TO_ENEMIES ); // Perspectives are a bit weird because of owner, double check
						Server_EmitSoundOnEntity_WithSendFlags( Audio().enemyPlayerKilled, player, attacker, ESendEventFlags::SEND_TO_FRIENDLIES ); // Perspectives are a bit weird because of owner, double check
					}
					else
					{
						Server_EmitSoundOnEntity_WithSendFlags( Audio().friendlyPlayerKilled, player, player, ESendEventFlags::SEND_TO_FRIENDLIES | ESendEventFlags::SEND_TO_OWNER );
						Server_EmitSoundOnEntity_WithSendFlags( Audio().enemyPlayerKilled, player, player, ESendEventFlags::SEND_TO_ENEMIES );
					}
				}
			}
		}
	}

	bool IsPlantFinalGamePoint( AAS_RaidDomeShield dome )
	{
		return false;
	}

	const float32 DOME_ENEMY_NAG_DEBOUNCE = 30;
	int DomeEnemyNagMS					  = TO_MILLISECONDS( -DOME_ENEMY_NAG_DEBOUNCE );
	UFUNCTION()
	private void OnDomeEnemyCountChanged( AAS_RaidDomeShield dome, int oldEnemyCount, int newEnemyCount )
	{
		// don't bother if dome is broken
		if ( dome.IsBroken() )
			return;

		if ( newEnemyCount == 0 )
			return;

		// don't get annoying
		if ( TO_SECONDS( GetGameTimeMS() - DomeEnemyNagMS ) < DOME_ENEMY_NAG_DEBOUNCE )
			return;

		DomeEnemyNagMS	 = GetGameTimeMS();
		int defenderTeam = dome.GetTeam();
		PlayAnnouncerDialogueToTeam( defenderTeam, GameplayTags::Audio_VO_GameUpdates_Intruders_YourBase );
	}

	UFUNCTION()
	private void OnBreacherShutdown( AAS_ShieldBreaker shieldBreaker, AAS_RaidDomeShield dome )
	{
		if ( GetGamePhase() >= GamePhase::WINNER_DETERMINED )
			return;

		int enemyTeam	   = dome.GetTeam();
		FGameplayTag alias = dome.largePlayerDetectionRadius.playersInside.Num() > 0 ? GameplayTags::Audio_VO_GameUpdates_Enemy_SiegeTowerDown_EnemyInside : GameplayTags::Audio_VO_GameUpdates_Enemy_SiegeTowerDown;
		PlayAnnouncerDialogueToTeam( enemyTeam, alias, 0.5 );
		PlayAnnouncerDialogueToTeam( shieldBreaker.GetTeam(), GameplayTags::Audio_VO_GameUpdates_SiegeTower_Down, 0.5 );
	}

	UFUNCTION( BlueprintEvent )
	protected void OnRaidEnded( AAS_RaidEventManager_v2 eventManager )
	{
		onRaidEndedSignal.Emit();

		SetNextGamePhaseTime( 0 );

		AAS_RaidDomeShield dome = GetRaidDomeShieldForTeam( eventManager.GetDefenderTeam() );

		TArray<UAS_DomeShieldCrumbleEndThread> copyArray = dome.inProgressCrumbleThreads;
		for ( UAS_DomeShieldCrumbleEndThread thread : copyArray )
		{
			thread.Cancel();
		}

		SetTeamObjectives( EObjectiveState::Hidden );

		if ( GetGamePhase() < GamePhase::WINNER_DETERMINED )
			TrySpawnOOBForBase( GetBaseForTeam( eventManager.GetDefenderTeam() ) );

		int defenderTeam = eventManager.GetDefenderTeam();
		RestoreDamagedWallsForTeam( defenderTeam );
	}

	void RestoreDamagedWallsForTeam( int team )
	{
		TArray<ANCDestructible> destructibles;
		GetAllActorsOfClass( destructibles );
		for ( ANCDestructible destructible : destructibles )
		{
			if ( destructible.GetTeam() != team )
				continue;

			if ( destructible.GetCurrentHealth() == 0 )
				continue;

			if ( destructible.GetHealth() == destructible.GetMaxHealth() )
				continue;

			destructible.StartDestructCategoryTransition_Server( destructible.GetDestructibleCategory().DestructibleCategoryID, 0.5, true );
			// destructible.GetHealthComponent().SetHealth( destructible.GetMaxHealth() );
			destructible.RepairDestructible();
		}
	}

	UFUNCTION( BlueprintEvent )
	void OnRaidBombPlanted( UAS_RaidBombInterfaceComponent bombInterface, AAS_RaidBomb bomb, ANCPlayerCharacter planterOrDefuser )
	{
	}

	UFUNCTION( BlueprintEvent )
	void OnRaidBombDefused( UAS_RaidBombInterfaceComponent bombInterface, AAS_RaidBomb bomb,
							ANCPlayerCharacter planterOrDefuser )
	{
	}

	UFUNCTION( BlueprintEvent )
	void OnRaidBombExplode( UAS_RaidBombInterfaceComponent bombInterface, int attackerTeam,
							int defenderTeam )
	{
	}

	FNCCoroutineSignal onRaidEndedSignal;

	UFUNCTION()
	protected void OnRaidStarted( AAS_RaidEventManager_v2 eventManager )
	{
		SetNextGamePhaseTime( 0 );

		InitializeRaidLives( eventManager.GetAttackerTeam(), eventManager.GetDefenderTeam() );
		AAS_TeamStateManager_RaidMode attackerManager = GetTeamStateManager_RaidMode( eventManager.GetAttackerTeam() );
		attackerManager.SetRaidObjectiveState( EObjectiveState::Hidden );
		AAS_TeamStateManager_RaidMode defenderManager = GetTeamStateManager_RaidMode( eventManager.GetDefenderTeam() );
		defenderManager.SetRaidObjectiveState( EObjectiveState::Hidden );

		baseOOBtriggers[eventManager.GetDefenderTeam()].Destroy();

		Thread( this, n"TrackPlayersOutsideRaid", eventManager );
	}

	UFUNCTION()
	private void TrackPlayersOutsideRaid( UNCCoroutine co, AAS_RaidEventManager_v2 eventManager )
	{
		co.EndOn( this, onRaidEndedSignal );

		while ( true )
		{
			co.Wait( 15.0 );

			AAS_RaidDomeShield dome = GetRaidDomeShieldForTeam( eventManager.GetDefenderTeam() );
			for ( ANCPlayerCharacter player : GetPlayersOfTeam( eventManager.GetAttackerTeam() ) )
			{
				AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( player );
				if ( dome.mapSwapDetectionTrigger.playersInside.Contains( asPlayer ) )
					continue;

				if ( !IsAlive(asPlayer) )
					continue;

				if ( asPlayer.selectableRespawnManager.ziplineSpawnTarget == dome )
					continue;

				PlayAnnouncerDialogueToPlayer( asPlayer, GameplayTags::Audio_VO_GameUpdates_Teammates_Raiding );
			}
		}
	}

	void OnPlayerReconnected( ANCPlayerCharacter player ) override
	{
		Super::OnPlayerReconnected( player );

		if ( !IsAlive( player ) )
		{
			UpdatePlayerSpectateCamera( player );
		}
	}

	void TransitionToPrematch() override
	{
		InitTeamScores();

		Super::TransitionToPrematch();
	}

	void OnPlayerCanSpawnChanged( AAS_PlayerEntity player, bool canSpawn ) override
	{
		Super::OnPlayerCanSpawnChanged( player, canSpawn );
		if ( canSpawn )
		{
			if ( !IsValid( player ) || !IsValid( player.selectableRespawnManager ) )
			{
				ScriptError_Silent_WithBug( "OnPlayerCanSpawnChanged called with invalid player/manager", "cpineda", f"player - {IsValid(player)}\nmanager - {IsValid(player) && IsValid(player.selectableRespawnManager)}" );
				return;
			}
			Thread( this, n"OnPlayerTrySpawnThread", player );
		}
	}

	UFUNCTION()
	void OnPlayerTrySpawnThread( UNCCoroutine co, AAS_PlayerEntity player )
	{
		co.EndOn( player, player.OnDestroyedSignal );
		co.EndOn( player.selectableRespawnManager, player.selectableRespawnManager.onPlayerCanSpawnChangedSignal );

		while ( PlayerIsRespawning( player ) )
			co.Wait( 0.1 );

		if ( !IsAlive( player ) )
			OnPlayerTrySpawnThreadActivate( player );
	}

	void RequestRespawn( AAS_PlayerEntity asPlayer, FRespawnRequestPackage _respawnPackage ) override
	{
		FRespawnRequestPackage modifiedRespawnPackage = _respawnPackage;

		// if the shield breaker carrier is alive at the time of the reuqest then the player intentionally wants to avoid them
		if ( GameModeDefaults().GamemodeRules_SpawnOnShieldBreakerCarrier && IsAlive( GetShieldBreakerCarrierForTeam( asPlayer.GetTeam() ) ) )
		{
			modifiedRespawnPackage.autoSwitchToShieldBreakerCarrierAllowed = false;
		}

		sv_RequestRespawn( asPlayer, modifiedRespawnPackage );
	}

	bool PlayerIsRespawning( AAS_PlayerEntity player )
	{
		return ( playerTotems.Contains(player) && IsValid( playerTotems[player] ) && playerTotems[player].isRespawning );
	}

	void OnPlayerTrySpawnThreadActivate( AAS_PlayerEntity player )
	{
		FRespawnRequestPackage package = player.selectableRespawnManager.GetSavedRespawnPackage();

		if ( package.type != ERespawnRequestType::INVALID )
		{
			if ( package.autoSwitchToShieldBreakerCarrierAllowed && GameModeDefaults().GamemodeRules_SpawnOnShieldBreakerCarrier && IsAlive( GetShieldBreakerCarrierForTeam( player.GetTeam() ) ) )
			{
				// if you are spawning at a mara spawn, you probably want to still do that
				if ( package.type == ERespawnRequestType::AT_BEACON && ( !IsValid( package.respawnBeacon ) || !package.respawnBeacon.Class.IsChildOf( AAS_MaraRespawnZone::StaticClass() ) ) )
				{
					FRespawnRequestPackage p;
					p.type = ERespawnRequestType::SHIELD_BREAKER_CARRIER;
					sv_RequestRespawn( player, p );
					return;
				}
			}
			sv_RequestRespawn( player, package );
		}
	}

	protected void sv_RequestRespawn( AAS_PlayerEntity asPlayer, FRespawnRequestPackage _respawnPackage )
	{
		Print( f"sv_RequestRespawn - {asPlayer.GetPlayerName()}" );

		asPlayer.selectableRespawnManager.SetSavedRespawnPackage( _respawnPackage );

		if ( !asPlayer.selectableRespawnManager.Sv_CanSpawn() )
		{
			Print( f"sv_RequestRespawn - {asPlayer.GetPlayerName()} - failed - can't spawn" );
			return;
		}

		AAS_TeamStateManager_RaidMode teamMgr = GetTeamStateManager_RaidMode( asPlayer.GetTeam() );
		if ( teamMgr.IsRespawnLocked() )
		{
			Print( f"sv_RequestRespawn - {asPlayer.GetPlayerName()} - failed - team manager respawn locked" );
			return;
		}

		int team = asPlayer.GetTeam();

		FRespawnRequestPackage respawnPackage = asPlayer.selectableRespawnManager.GetSavedRespawnPackage();

		if ( !GameModeDefaults().GamemodeRules_SpawnOnShieldBreakerCarrier )
		{
			if ( respawnPackage.type == ERespawnRequestType::SHIELD_BREAKER_CARRIER )
			{
				Print( f"sv_RequestRespawn - {asPlayer.GetPlayerName()} - failed - spawn on sb carrier not supported by mode" );
				return;
			}
		}

		switch ( respawnPackage.type )
		{
			case ERespawnRequestType::AT_BEACON:
			{
				if ( !IsValid( respawnPackage.respawnBeacon ) )
				{
					// maybe a mara spawn got destroyed
					return;
				}

				if ( respawnPackage.respawnBeacon.sv_CanSpawn() )
				{
					RespawnCharacterAtPoint( asPlayer, asPlayer.FootLocationToActorLocation( respawnPackage.respawnBeacon.spawnDirection.GetWorldLocation() ), respawnPackage.respawnBeacon.spawnDirection.GetWorldRotation() );
					respawnPackage.respawnBeacon.PlayerRespawnRequested( asPlayer );
				}
			}
			break;

			case ERespawnRequestType::ZIPLINE:
			{
				if ( !IsValid( respawnPackage.respawnBeacon ) )
				{
					// maybe a dome slicer spawn got destroyed
					return;
				}

				AAS_ShieldBreaker siegeTower = GetBreacherForTeam( team );
				if ( IsValid( siegeTower ) )
					siegeTower.respawnComponent.sv_ZiplineSpawn( asPlayer, respawnPackage );
				else
					RespawnCharacter( asPlayer );
				respawnPackage.respawnBeacon.PlayerRespawnRequested( asPlayer );
				break;
			}

			case ERespawnRequestType::UNSPECIFIED:
				RespawnCharacter( asPlayer );
				break;

			case ERespawnRequestType::SHIELD_BREAKER_CARRIER:
			{
				AAS_PlayerEntity carrier = GetShieldBreakerCarrierForTeam( asPlayer.GetTeam() );
				if ( IsValid( carrier ) && carrier != asPlayer )
					SpawnOnShieldBreakerCarrier( asPlayer, carrier );
				else
					Print( f"sv_RequestRespawn - {asPlayer.GetPlayerName()} - could not find sb carrier: {carrier}" );
			}
			break;

			case ERespawnRequestType::TRAINING_AT_LOCATION:
			{
				RespawnCharacterAtPoint( asPlayer, respawnPackage.spawnLoc, respawnPackage.camWorldRot );
			}
			break;

			case ERespawnRequestType::INVALID:
				ScriptError( "Trying to respawn with invalid type!" );
				break;
		}

		switch ( respawnPackage.type )
		{
			case ERespawnRequestType::AT_BEACON:
			case ERespawnRequestType::ZIPLINE:
			{
				if ( asPlayer.wasRaidAttackDeath )
				{
					bool isRespawningOnRelevantMaraSpawn = false;
					if ( IsValid( respawnPackage.respawnBeacon ) )
					{
						AAS_RaidDomeShield enemyDome = GetRaidDomeShieldForTeam( GetOtherTeam( team ) );
						AAS_MaraRespawnZone maraZone = Cast<AAS_MaraRespawnZone>( respawnPackage.respawnBeacon );
						if ( IsValid( enemyDome ) && IsValid( maraZone ) && IsActorRelevantToRaid( enemyDome, maraZone ) )
							isRespawningOnRelevantMaraSpawn = true;
					}

					AAS_ShieldBreaker breacher = GetBreacherForTeam( team );
					if ( IsValid( breacher ) && !isRespawningOnRelevantMaraSpawn )
					{
						breacher.ReduceBreacherEnergy();
					}
				}
			}
			break;

			case ERespawnRequestType::SHIELD_BREAKER_CARRIER:
			case ERespawnRequestType::UNSPECIFIED:
			case ERespawnRequestType::TRAINING_AT_LOCATION:
				break;

			case ERespawnRequestType::INVALID:
				ScriptError( "Trying to respawn with invalid type!" );
				break;
		}
	}

	AAS_PlayerEntity GetShieldBreakerCarrierForTeam( int team )
	{
		TArray<ANCPlayerCharacter> teammates = GetPlayersOfTeam( team );
		for ( ANCPlayerCharacter t : teammates )
		{
			if ( !IsAlive( t ) )
				continue;

			AAS_PlayerEntity teammate = Cast<AAS_PlayerEntity>( t );
			if ( !PlayerHasShieldBreaker( teammate ) )
				continue;

			return teammate;
		}

		return nullptr;
	}

	bool wasPIEstart = false;

	UFUNCTION( BlueprintOverride )
	bool BlueprintChooseSpawnPoint( ANCPlayerCharacter CharacterEntity, FVector& OutSpawnLocation, FRotator& OutSpawnRotation )
	{
		return ChooseBackupSpawnPoint( CharacterEntity, OutSpawnLocation, OutSpawnRotation );
	}

	void OnPlayerDisconnected( ANCPlayerCharacter Pawn ) override
	{
		Super::OnPlayerDisconnected( Pawn );
	}

	UFUNCTION( BlueprintOverride, Meta = ( NoSuperCall ) )
	void BlueprintPlayerDied( FDamageInfo& DamageInfo, ANCPlayerCharacter Victim )
	{
		AAS_PlayerEntity player		= Cast<AAS_PlayerEntity>( Victim );
		bool isSuicide				= false;
		ANCPlayerCharacter attacker = DamageInfo.attacker;

		player.saveLootOnDeathComponent.preventNextDegrade = Bitflags::HasFlag( DamageInfo.scriptDamageFlags, EScriptDamageFlags::DF_NO_DEGRADE_SHIELD );

		if ( GetCvarBool( "ScriptDebug.InstantRespawn" ) )
		{
			Thread( this, n"DevDelayedRespawnCharacter", 1.0, player );
		}

		player.PlayBattleChatter( GameplayTags::Audio_VO_BattleChatter_I_am_Downed );

		if ( IsValid( attacker ) )
		{
			if ( attacker == player )
				isSuicide = true;
			else
			{
				attacker.PlayBattleChatter( GameplayTags::Audio_VO_BattleChatter_DownedEnemy );
				GiveKillRewards( attacker );
			}

			OnPlayerDied_Obituary( player, attacker, DamageInfo, isSuicide );
		}

		// Workaround to players dying during prematch
		if ( !Bitflags::HasFlag( DamageInfo.damageFlags, EDamageFlags::DF_KEEP_CONTENTS ) )
		{
			if ( IsValid( attacker ) )
			{
				if ( attacker == player )
					isSuicide = true;
			}

			player.saveLootOnDeathComponent.SaveLootFromBackpacks();
			OnPlayerDeath_SpawnDeathPickups( player );

			int timeElapsed = player.GetTimeMilliseconds() - player.respawnTotem_lastReviveTimeMS;
			if ( GameModeDefaults().RespawnRules_RespawnTotemEnabled )
			{
				if ( timeElapsed > TO_MILLISECONDS( GameModeDefaults().RespawnRules_RespawnTotemCooldownSeconds ) && !TeamIsEliminated( player.GetTeam() ) )
				{
					AAS_RespawnTotem totem = SpawnRespawnTotemForPlayer( player, DamageInfo );
					totem.isSuicide		   = isSuicide;
					playerTotems.Add( player, totem );
				}
				else
				{
					FVector loc	 = player.GetActorLocation() + FVector( 0, 0, 20 );
					FRotator rot = player.GetActorRotation();
					Server_SpawnEffectAtLocation_OneShot( Abilities().destroyOrbShatter, loc, rot );
					Server_EmitSoundAtLocation( Abilities().destroyOrbShatterSound, loc, rot );
				}

				player.saveLootOnDeathComponent.DropDeathboxOrLooseLoot( isSuicide );
			}
			else
			{
				player.saveLootOnDeathComponent.DropDeathboxOrLooseLoot( isSuicide );
				player.ClearAllBackpackContents();
			}
		}

		// going to call this in the thread
		// Super::BlueprintPlayerDied( DamageInfo, Victim );
		Thread( this, n"PostDeathThread", player );
	}

	void OnPlayerDeath_SpawnDeathPickups( AAS_PlayerEntity player )
	{
		player.saveLootOnDeathComponent.SpawnDeathPickUps();
	}

	UFUNCTION()
	void PostDeathThread( UNCCoroutine co, AAS_PlayerEntity player )
	{
		co.EndOnDestroyed( player );
		co.EndOn( player, player.OnSpawnedSignal );

		if ( IsPlayerTeamEliminated( player ) )
		{
			TArray<ANCPlayerCharacter> players = GetPlayersOfTeam( player.GetTeam() );
			for ( ANCPlayerCharacter PS : players )
			{
				AAS_PlayerEntity teammate = Cast<AAS_PlayerEntity>( PS );
				UpdatePlayerSpectateCamera( teammate );
			}
		}

		co.Wait( DEATH_SPECTATE_BUFFER );

		if ( IsPlayerTeamEliminated( player ) )
			return;

		int team = player.GetTeam();

		// update any teammates watching this player
		TArray<ANCPlayerCharacter> teammates = GetPlayersOfTeam( team );
		for ( ANCPlayerCharacter PS : teammates )
		{
			ANCPlayerCharacter teammate = PS;
			if ( teammate == player )
				continue;
			if ( IsAlive( teammate ) )
				continue;

			AActor spectateEntity = teammate.GetSpectateEntity();
			if ( spectateEntity == player )
			{
				UpdatePlayerSpectateCamera( teammate );
			}
		}

		// this sets the dead menu variable and updates the spectate camera
		BeginDeadMenu( player );
		UpdatePlayerSpectateCamera( player );
	}

	protected void GiveKillRewards( ANCPlayerCharacter killer )
	{
		EventRewardManager().Sv_GivePlayerReward( killer, EEventRewardType::PLAYER_ELIMINATION );
	}

	float32 GetRespawnTimeLeft( AAS_PlayerEntity player )
	{
		return TO_SECONDS( player.selectableRespawnManager.net_nextRespawnTime - GetGameTimeMS() );
	}

	UFUNCTION()
	void DevDelayedRespawnCharacter( UNCCoroutine co, float delay, AAS_PlayerEntity player )
	{
		co.EndOnDestroyed( player );
		co.EndOn( player, player.OnSpawnedSignal );

		co.Wait( delay );

		RespawnCharacter( player );
	}

	UFUNCTION()
	protected void Signal_OnBaseDestroyed( FName signaName, UObject signalActor )
	{
		AAS_BaseSystem baseSystem = Cast<AAS_BaseSystem>( signalActor );
		if ( !IsValid( baseSystem ) )
			return;

		int team = baseSystem.net_teamID;
		CheckTeamElimination( team );
	}

	void CheckTeamElimination( int team )
	{
		if ( team < 0 )
			return;

		if ( ShouldTeamBeEliminated( team ) )
			EliminateTeam( team );
	}

	bool ShouldTeamBeEliminated( int team )
	{
		if ( TeamIsEliminated( team ) ) // This can happen if a team is eliminated from defender lives and then base destruction is triggered.
			return false;

		AAS_BaseSystem base = GetBaseForTeam( team );
		if ( IsValid( base ) )
			return base.isBaseDestroyed;
		else
			return false;
	}

	void OnPlayerFirstJoined( ANCPlayerCharacter player ) override
	{
		Super::OnPlayerFirstJoined( player );

		int team = player.GetTeam();

		if ( TeamIsEliminated( team ) && InGracePeriod() )
		{
			SetTeamEliminated( team, false );
			SetTeamLevel( team, 1 );
		}

		playerTotems.Add( player, nullptr );
	}

	void InitTeamScores()
	{
		for ( int i = 0; i < GetNumTeams(); i++ )
		{
			TArray<ANCPlayerCharacter> teamPlayers = GetPlayersOfTeam( i );

// so we can plant on teams with no players for testing
#if !EDITOR
			if ( teamPlayers.Num() > 0 )
#endif
			{
				SetTeamEliminated( i, false );
				SetTeamLevel( i, 1 );
			}
		}
	}

	TMap<int, AAS_Sv_OutOfBoundsTrigger_Sphere> baseOOBtriggers;

	UFUNCTION()
	protected void OnBaseInitialized( AAS_BaseSystem base )
	{
		TrySpawnOOBForBase( base );
	}

	void TrySpawnOOBForBase( AAS_BaseSystem base )
	{
		int team = base.GetTeam();

		if ( baseOOBtriggers.Contains( team ) )
		{
			if ( IsValid( baseOOBtriggers[team] ) )
			{
				return;
			}
		}

		AAS_Sv_OutOfBoundsTrigger_Sphere trigger = Cast<AAS_Sv_OutOfBoundsTrigger_Sphere>( SpawnActor( AAS_Sv_OutOfBoundsTrigger_Sphere::StaticClass(), base.GetActorLocation(), base.GetActorRotation(), NAME_None, true ) );
		trigger.teamFilter						 = GetOtherTeam( base.GetTeam() );
		trigger.trigger.SetRadius( base.domeShield.GetPlayerDetectionRadius( base.domeShield.GetRadius() ) );
		FinishSpawningActor( trigger );
		baseOOBtriggers.Add( team, trigger );
	}

	UFUNCTION()
	void OnTeamLevelChanged( int teamIndex, int oldScore, int newScore )
	{
	}

	UFUNCTION( BlueprintOverride )
	bool ShouldPlayerRespawn( ANCPlayerCharacter CharacterEntity )
	{
		bool gamePhaseAllowsRespawn = Super::ShouldPlayerRespawn( CharacterEntity );
		if ( gamePhaseAllowsRespawn == false )
			return false;

		if ( IsValid( playerTotems[CharacterEntity] ) && playerTotems[CharacterEntity].respawnActivated )
			return true;

		if ( GetCvarBool( "ScriptDebug.InstantRespawn" ) )
			return true;

		int team = CharacterEntity.GetTeam();
		if ( TeamIsEliminated( team ) )
			return false;

		return true;
	}

	void EliminateTeam( int team )
	{
		SetTeamEliminated( team, true );
		OnTeamEliminated( team );
		CheckWinner();
	}

	void CheckWinner()
	{
		if ( GetGamePhase() >= GamePhase::WINNER_DETERMINED )
			return;

		TArray<int> teamsNotEliminated;

		for ( int i = 0; i < GetNumTeams(); i++ )
		{
			if ( !TeamIsEliminated( i ) )
			{
				teamsNotEliminated.Add( i );
			}
		}

		if ( !GetCvarBool( "ScriptDebug.EnableMatchEnding" ) )
			return;

		if ( teamsNotEliminated.Num() == 1 )
		{
			SetWinner( teamsNotEliminated[0] );
		}
	}

	void OnTeamEliminated( int team )
	{
		if ( !GetCvarBool( "ScriptDebug.EnableMatchEnding" ) )
			return;

		ScriptCallbacks().server_OnTeamEliminated.Broadcast( team );

		TArray<ANCPlayerCharacter> teamPlayers = GetPlayersOfTeam( team );
		for ( ANCPlayerCharacter playerCharacter : teamPlayers )
		{
			if ( !IsValid( playerCharacter ) )
				continue;

			AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( playerCharacter );
			if ( IsValid( asPlayer ) )
				asPlayer.MoveAllItemsToCloudStorage();

			if ( IsAlive( playerCharacter ) )
			{
				playerCharacter.KillMe();
			}

			if ( playerTotems.Contains( playerCharacter ) )
			{
				AAS_RespawnTotem totem = playerTotems[playerCharacter];
				if ( IsValid( totem ) )
				{
					totem.Expire();
				}
			}
		}
	}

	UFUNCTION( BlueprintOverride )
	int ChooseTeam( ANCPlayerCharacter PS )
	{
		int teamChosen = MultiTeamMode_ChooseTeam( this, PS );
		return teamChosen;
	}

	void OnPlayerEndPlay( AAS_PlayerEntity playerEntity, EEndPlayReason EndPlayReason ) override
	{
		Super::OnPlayerEndPlay( playerEntity, EndPlayReason );

		int teamCount						   = 0;
		TArray<ANCPlayerCharacter> teamPlayers = GetPlayersOfTeam( playerEntity.GetTeam() );
		for ( ANCPlayerCharacter PS : teamPlayers )
		{
			ANCPlayerCharacter player = PS;
			if ( IsValid( player ) && player != playerEntity )
			{
				teamCount++;
			}
		}

		if ( teamCount == 0 )
		{
			CheckTeamElimination( playerEntity.GetTeam() );
		}

		CheckAutoEndFromEmptyTeam();
	}

	void CheckAutoEndFromEmptyTeam()
	{
		if ( !GetCvarBool( "ScriptDebug.AutoEndOnEmptyTeam" ) )
			return;

		System::SetTimer( this, n"CheckAutoEndFromEmptyTeamInternal", 0.5, true );
	}

	void OnSetPlayerNotComingBack( ANCPlayerCharacter player ) override
	{
		Super::OnSetPlayerNotComingBack( player );

		CheckAutoEndFromEmptyTeam();
	}

	UFUNCTION()
	void CheckAutoEndFromEmptyTeamInternal()
	{
		if ( gameIsAutoEnding )
			return;

		if ( GetGamePhase() >= GamePhase::WINNER_DETERMINED )
			return;

		TArray<int> validTeams;

		for ( int team : GetAllTeams() )
		{
			if ( GetPlayersOfTeam( team ).Num() > 0 )
			{
				for ( ANCPlayerCharacter player : GetPlayersOfTeam( team ) )
				{
					AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( player );
					if ( !asPlayer.IsPlayerConnected() )
						continue;

					validTeams.Add( team );
					break;
				}
			}
		}

		if ( validTeams.Num() == 1 )
		{
			gameIsAutoEnding = true;
			Thread( this, n"AutoWinBecauseEmptyTeamThread", validTeams[0] );
		}
		else if ( validTeams.Num() == 0 )
		{
			gameIsAutoEnding = true;
			SetDraw();
		}
	}

	bool gameIsAutoEnding = false;

	UFUNCTION()
	void AutoWinBecauseEmptyTeamThread( UNCCoroutine co, int team )
	{
		Server_SendAnnouncementToTeam( team, Localization::MatchEnd, "other_team_disconnected" );
		co.Wait( 5.0 );
		SetWinner( team );
	}

	//
	void OnGamePhaseChanged( int oldState, int newState ) override
	{
		Super::OnGamePhaseChanged( oldState, newState );

		if ( oldState == GamePhase::PREMATCH )
		{
			TArray<AAS_RaidDomeShield> allDomes;
			GetAllActorsOfClass( AAS_RaidDomeShield::StaticClass(), allDomes );
			for ( AAS_RaidDomeShield dome : allDomes )
			{
				dome.SetAllowExits( true );
			}
		}

		if ( newState == GamePhase::PREMATCH )
		{
			SetTeamObjectives( EObjectiveState::DefensePhase );
			for ( int team : GetAllTeams() )
			{
				PlayTeamConversation( team, 10 );
			}
		}

		if ( newState == GamePhase::PLAYING )
		{
			Signal( n"Global_ForceRestartCooldown" );
			SendTitleMessage();
		}

		if ( newState >= GamePhase::WINNER_DETERMINED )
		{
			SetTeamObjectives( EObjectiveState::Hidden );
		}

		TArray<AAS_SBCrafter> allCrafters;
		GetAllActorsOfClass( allCrafters );
		for ( AAS_SBCrafter crafter : allCrafters )
		{
			Crafter_OnGamePhaseChanged( crafter, oldState, newState );
		}
	}

	void UpdateTeamObjectives()
	{
		SetTeamObjectives( EObjectiveState::PickupShieldBreaker );

		for ( int team : GetAllTeams() )
		{
			AAS_TeamStateManager_RaidMode mgr = GetTeamStateManager_RaidMode( team );
			if ( GetTeamShieldBreacherCountFromTeamID( team ) > 0 || IsValid( GetSBChargerForTeam( team ) ) || IsValid( GetPlantPointTeamIsAttacking( team ) ) )
			{
				mgr.SetRaidObjectiveState( EObjectiveState::Raiding );

				AAS_TeamStateManager_RaidMode otherMgr = GetTeamStateManager_RaidMode( GetOtherTeam( team ) );
				otherMgr.SetRaidObjectiveState( EObjectiveState::InterceptShieldBreaker );
				return;
			}
		}
	}

	void SetTeamObjectives( EObjectiveState state )
	{
		for ( int team : GetAllTeams() )
		{
			AAS_TeamStateManager_RaidMode mgr = GetTeamStateManager_RaidMode( team );
			mgr.SetRaidObjectiveState( state );
		}
	}

	void Crafter_OnGamePhaseChanged( AAS_SBCrafter crafter, int oldState, int newState )
	{
		if ( crafter.GetSBCrafterState() == ESBCrafterState::HIDDEN )
			return;

		int GAMESTART_DELAY = TO_MILLISECONDS( GameModeDefaults().GameModeRules_ShieldBreakerCraftingGameStartDelay );

		if ( newState == GamePhase::PLAYING && GAMESTART_DELAY > 0 )
		{
			crafter.resetCount = -1;
			crafter.ResetCrafterTimer( TO_SECONDS( GAMESTART_DELAY ) );
			OnSBCrafterCooldownAssigned( crafter );
		}
		else
		{
			crafter.SetCrafterState( ESBCrafterState::READY );
			crafter.net_SBCrafterStateEndTime.SetNetValue( 0 );
		}
	}

	void OnSBCrafterCooldownAssigned( AAS_SBCrafter crafterToCooldown )
	{
	}

	void SendTitleMessage()
	{
	}

	UFUNCTION()
	protected void GameEvent_UpgradeLootZones()
	{
		Sv_LootDistributionManager().UpgradeLootZones();
		_UpgradeVendors();
	}

	UFUNCTION()
	private void _UpgradeVendors()
	{
		VendorManager().Server_UpgradeVendor();
	}

	void SkipCurrentPhase() override
	{
		Super::SkipCurrentPhase();
	}

	UFUNCTION( BlueprintOverride )
	void BlueprintPlayerSpawned( ANCPlayerCharacter player )
	{
		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( player );

		bool preventDegrade									 = asPlayer.saveLootOnDeathComponent.preventNextDegrade;
		asPlayer.saveLootOnDeathComponent.preventNextDegrade = false;

		if ( !IsValid( playerTotems[asPlayer] ) || !playerTotems[asPlayer].respawnActivated )
		{
			if ( GetGamePhase() == GamePhase::PLAYING )
				asPlayer.PlayBattleChatter( GameplayTags::Audio_VO_BattleChatter_BeingRespawned );

			bool degradeArmor = !preventDegrade;
			if ( asPlayer.HasTrinketPassive( GameplayTags::Classes_Passives_Armorer ) )
			{
				asPlayer.Server_OnTrinketUsed();
				degradeArmor = false;
			}

			if ( degradeArmor )
				asPlayer.saveLootOnDeathComponent.DegradeWeaponsAndShield();

			GiveStartingLoadout( asPlayer );
		}
		else
		{
			GiveStartingLoadout( asPlayer );
			OnPlayerRevived( player );
		}

		TArray<ANCPlayerCharacter> teammates = GetPlayersOfTeam( player.GetTeam() );
		for ( ANCPlayerCharacter teammate : teammates )
		{
			if ( IsAlive( teammate ) )
				continue;

			AAS_PlayerEntity asTeammate = Cast<AAS_PlayerEntity>( teammate );
			AActor spectate				= teammate.GetSpectateEntity();

			if ( spectate == nullptr ||
				 ( IsValid( playerTotems[asTeammate] ) && asTeammate.GetSpectateEntity() == playerTotems[asTeammate] ) )
			{
				asTeammate.spectateIndex = -1;
				UpdatePlayerSpectateCamera( asTeammate );
			}
		}

		// Server_OnRespawned() gets broadcast here
		Super::BlueprintPlayerSpawned( player );
	}

	void OnPlayerRevived( ANCPlayerCharacter player )
	{
	}

	void GiveStartingLoadout( ANCPlayerCharacter player ) override
	{
		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( player );

		// Validate this works
		for ( FBackpackItemStruct packItem : DefaultBackpackItems )
		{
			const FLootDataStruct& data = GetLootDataByIndex( packItem.itemIndex );
			int existingCount			= asPlayer.CountItemsInAllBackpacks( packItem.itemIndex );
			int defaultCount			= packItem.itemCount <= 0 ? data.defaultDropCount : packItem.itemCount;
			int count					= Math::Max( defaultCount - existingCount, 0 );
			asPlayer.AddToAnyBackpack( MakeBackpackItem_OverrideCount( packItem, count ) );
		}

		/*	mount hack: @davis the mount used to be given in sv_base along with healing tool and other
			weapons that are default. But since the mount can change, I decided to move that to here
			but I'm not confident in the implementation 	*/
		{
			// it's VERY important not to change the mount if it's active ( which can easily happen on reconnect )
			if ( !player.IsPlayerRidingMount() )
			{
				FGameplayTag mountTag = player.GetEquippedMountTag(); // GetGameplayTagForInt( mountIDX );
				if ( player.IsBot() || !IsValidMountTag( mountTag ) )
					mountTag = GameplayTags::Mounts_Horse;

				player.ServerGiveMount( mountTag );
			}
			else
				Print( "Player riding mount: not switching equipped mount" );
		}

		Super::GiveStartingLoadout( player );

		GiveSavedLootToPlayer( player );
	}

	void GiveSavedLootToPlayer( ANCPlayerCharacter player )
	{
		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( player );

		///////////////////////////////////
		// 		First spawn
		///////////////////////////////////
		if ( !HasPlayerSpawnedBefore( asPlayer ) )
		{
			// Give one charge of each raid weapon ammo type
			PerformLootPickupAction( asPlayer, MakeBackpackItem( GameplayTags::Loot_Ammo_Hammer, 1 ) );
			PerformLootPickupAction( asPlayer, MakeBackpackItem( GameplayTags::Loot_Ammo_Rockets, 1 ) );
			PerformLootPickupAction( asPlayer, MakeBackpackItem( GameplayTags::Loot_Ammo_Ziplines, 1 ) );
			PerformLootPickupAction( asPlayer, MakeBackpackItem( GameplayTags::Loot_Ammo_HackerDart, 2 ) );
		}

		///////////////////////////////////
		// 		Saved loot / weapons
		///////////////////////////////////
		asPlayer.saveLootOnDeathComponent.GiveSavedLootToPlayer();

		///////////////////////////////////
		// 		Equip shield if yours broke
		///////////////////////////////////
		if ( asPlayer.IsActorShieldValid() && asPlayer.GetEquippedShieldItem().itemIndex == GameplayTags::Loot_Armor_Level1 )
		{
			TArray<FBackpackItemStruct> backpackContents = asPlayer.backpackComponent.GetBackpackContents();
			for ( FBackpackItemStruct item : backpackContents )
			{
				const FLootDataStruct& lootData = GetLootDataByIndex( item.itemIndex );
				if ( lootData.lootType == ELootType::PlayerShield )
				{
					// Only equip 1 shield and not an entire stack of shields
					FBackpackItemStruct itemToRemove = item;
					itemToRemove.itemCount			 = 1;

					asPlayer.backpackComponent.RemoveFromBackpack( itemToRemove );
					PerformLootPickupAction( asPlayer, item, false );
				}
			}
		}

		///////////////////////////////////
		// 		Loadout weapons
		///////////////////////////////////
		// Also refreshes ammo
		WeaponLoadouts().GiveWeaponsAndAmmo( asPlayer, FGiveWeaponSettings() );

		asPlayer.saveLootOnDeathComponent.GiveSavedOverflowLootToPlayer();

		///////////////////////////////////
		// 		Raid weapon edge cases
		///////////////////////////////////
		// Workaround for missing raid weapon NC1-13579
		if ( !IsValid( asPlayer.GetWeaponAtSlot( WeaponSlot::RaidToolsSlot ) ) )
		{
			const FLootDataStruct& rocketData = GetLootDataByIndex( GameplayTags::Loot_Weapon_RocketLauncher );
			PlayerPickupWeaponLoot( asPlayer, rocketData, MakeBackpackItem( rocketData, 0 ), true );
		}

		// Auto-load raid weapon on spawn, if you have ammo
		AutoLoadRaidWeaponFromBackpack( asPlayer );
	}

	UFUNCTION()
	private void OnTeamSpawnedAtBase( int team, TArray<ANCPlayerCharacter> playersOnTeam )
	{
		// Give game start loadout to players
		int numPlayers = playersOnTeam.Num();
		for ( int i = 0; i < numPlayers; i++ )
		{
			for ( TMapIterator<FGameplayTag, int> entry : gameStartItemsToGivePlayer )
			{
				playersOnTeam[i].AddToAnyBackpack( MakeBackpackItem( entry.Key, entry.Value ) );
			}
		}
	}

	UFUNCTION( BlueprintOverride )
	AActor GetSpectateEntityForPlayer( ANCPlayerCharacter player )
	{
		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( player );

		// Start with bases before even looking for players if we are in base select or the whole team is dead
		if ( GetGamePhase() == GamePhase::BASE_SELECT || IsPlayerTeamEliminated( asPlayer ) )
		{
			UAS_BaseSelectSystem bases = Bases();
			if ( IsValid( bases ) )
			{
				// Return the base camera (it is ok if this is nullptr)
				return bases.Server_GetBaseCameraForTeam( player.GetTeam() );
			}
		}

		// If we are alive or waiting for players we don't need a spectate entity
		if ( IsAlive( asPlayer ) || GetGamePhase() == GamePhase::WAITING_FOR_PLAYERS || asPlayer.net_deadMenuState != int( EDeadMenuState::SHOW ) )
		{
			return nullptr;
		}

		AAS_RespawnTotem totem;

		// This could be a mix of actors depending on the player state
		TArray<AActor> potentialSpectateEntities;

		// Next add all of the players teammates who are alive
		TArray<ANCPlayerCharacter> players = GetPlayersOfTeam( asPlayer.GetTeam() );
		for ( ANCPlayerCharacter teammate : players )
		{
			if ( teammate == asPlayer || !IsAlive( teammate ) )
				continue;

			potentialSpectateEntities.Add( teammate );
		}

		if ( potentialSpectateEntities.Num() > 0 )
		{
			int spectateIndex = asPlayer.spectateIndex;
			{
				// Spectate indicis coming in greater than 0 means we are trying to spectate a different actor
				int numEntities = potentialSpectateEntities.Num();

				// Clamp the index to the number of options we have before modding it below
				int newIndex = Math::Clamp( asPlayer.spectateIndex, 0, numEntities );

				// Get the next index
				spectateIndex		   = ( newIndex + potentialSpectateEntities.Num() ) % potentialSpectateEntities.Num();
				asPlayer.spectateIndex = spectateIndex;

				if ( spectateIndex < 0 || spectateIndex >= potentialSpectateEntities.Num() )
				{
					ScriptError_Silent_WithBug( "spectate tried to give bad index", "cpineda", f"newIndex: {spectateIndex}\nteammates: {potentialSpectateEntities.Num()}\nspectate index: {asPlayer.spectateIndex}" );
					return nullptr;
				}
			}

			if ( IsValid( totem ) && IsValid( totem.currentReviveThread ) && totem.currentReviveThread.IsRunning() )
			{
				ANCPlayerCharacter reviver = totem.currentReviveThread.usingPlayer;
				asPlayer.spectateIndex	   = Math::Max( 0, potentialSpectateEntities.FindIndex( reviver ) );
				return reviver;
			}

			if ( potentialSpectateEntities.IsValidIndex( spectateIndex ) )
			{
				return potentialSpectateEntities[spectateIndex];
			}
		}

		if ( !IsAlive( asPlayer ) || playerTotems.Contains( asPlayer ) )
		{
			// Check respawn totems first since those hold a spectate camera
			totem = playerTotems[asPlayer];
			if ( IsValid( totem ) && !totem.IsActorBeingDestroyed() )
			{
				ANCSpectateCamera cam = totem;
				if ( IsValid( cam ) )
				{
					return cam;
				}
			}
			else
			{
				// Otherwise check for a camera that may be lingering
				TArray<ANCSpectateCamera> outCameras;
				GetAllActorsOfClass( ANCSpectateCamera::StaticClass(), outCameras );

				for ( ANCSpectateCamera camera : outCameras )
				{
					if ( camera.GetOwnerPlayer() == asPlayer )
					{
						return camera;
					}
				}
			}
		}

		return nullptr;
	}

	FRespawnDurationQueryResult OnDeath_ReturnRespawnDuration( UAS_SelectablePlayerRespawnComponent respawnComponent, ANCPlayerCharacter victim, const FDamageInfo&in damageInfo ) const override
	{
		respawnComponent.net_respawnDeathType.SetNetValue( ERespawnDeathType::NONE );

		if ( GetGamePhase() != GamePhase::PLAYING )
		{
			if ( GetGamePhase() == GamePhase::PREMATCH )
				return FRespawnDurationQueryResult( GetGameTimeMS(), GetGameTimeMS() );

			return FRespawnDurationQueryResult( -1, -1 );
		}

		FRespawnDurationQueryResult result = Super::OnDeath_ReturnRespawnDuration( respawnComponent, victim, damageInfo );
		float32 respawnTime				   = TO_SECONDS( result.endTime - result.startTime );

		AAS_PlayerEntity victimPlayer = Cast<AAS_PlayerEntity>( victim );
		bool wasRaidDefenseDeath	  = victimPlayer.wasRaidDefenseDeath;
		bool wasRaidAttackDeath		  = victimPlayer.wasRaidAttackDeath;
		int victimTeam				  = victim.GetTeam();

		bool useWaveSpawn = GameModeDefaults().RespawnRules_WaveSpawnEnabled;
		if ( useWaveSpawn )
		{
			AAS_TeamStateManager_RaidMode teamState = GetTeamStateManager_RaidMode( victimTeam );
			respawnTime								= teamState.teamWaveSpawnManager.GetCurrentOrNewWaveTime();
			respawnComponent.net_extraRespawnTimeCounter.SetNetValue( teamState.teamWaveSpawnManager.currentWaveExtraRespawnTimeCounter );
		}

		float32 extraRespawnTime = GameModeDefaults().RespawnRules_ExtraRespawnTimeForIntecept;
		if ( GameModeDefaults().RespawnRules_ExtraRespawnTimeIncreaseOnDeath )
		{
			if ( !wasRaidDefenseDeath && !wasRaidAttackDeath && extraRespawnTime > 0 )
			{
				respawnComponent.net_extraRespawnTimeCounter.SetNetValue( respawnComponent.net_extraRespawnTimeCounter + 1 );
				respawnTime += float32( respawnComponent.net_extraRespawnTimeCounter - 1 ) * extraRespawnTime;
			}
		}
		else
		{
			AAS_GameState_RaidModeTwoTeam gs = GetGameStateEntity_RaidModeTwoTeam();
			if ( IsValid( gs ) )
				respawnComponent.net_extraRespawnTimeCounter.SetNetValue( gs.net_respawnTimeEscalation );
			respawnTime += float32( respawnComponent.net_extraRespawnTimeCounter - 1 ) * extraRespawnTime;
		}

		if ( wasRaidDefenseDeath )
		{
			respawnComponent.net_respawnDeathType.SetNetValue( ERespawnDeathType::DEFENSIVE_DEATH );

			respawnTime += GetTeamStateManager( victimTeam ).additionalDefenseSpawnTime;
		}
		else if ( wasRaidAttackDeath )
		{
			respawnComponent.net_respawnDeathType.SetNetValue( ERespawnDeathType::ATTACKER_DEATH );

			respawnTime += GetTeamStateManager( victimTeam ).additionalAttackSpawnTime;
		}

		if ( useWaveSpawn )
		{
			AAS_TeamStateManager_RaidMode teamState = GetTeamStateManager_RaidMode( victimTeam );
			result.startTime						= teamState.teamWaveSpawnManager.GetCurrentWaveStartTime();
			result.endTime							= teamState.teamWaveSpawnManager.GetCurrentWaveEndTime();
		}
		else
		{
			result.startTime = GetGameTimeMS();
			result.endTime	 = result.startTime + TO_MILLISECONDS( respawnTime );
		}

		return result;
	}

	void InitializeRaidLives( int attackerTeamIndex, int defenderTeamIndex )
	{
		int attackerLives = GetTeamAttackerMaxLives( attackerTeamIndex );
		SetTeamAttackerLives( attackerTeamIndex, attackerLives );
	}

	void SpawnOnShieldBreakerCarrier( AAS_PlayerEntity player, AAS_PlayerEntity shieldBreakerCarrier )
	{
		TArray<AAS_SpawnOnShieldBreakerSpline> allSplines;
		GetAllActorsOfClass( allSplines );
		FTransform spawn;
		if ( allSplines.Num() == 0 )
			spawn = GetShieldBreakerSpawnPos_Vendors( shieldBreakerCarrier );
		else
			spawn = GetShieldBreakerSpawnPos_Splines( shieldBreakerCarrier );

		Print( f"SpawnOnShieldBreakerCarrier - {player.GetPlayerName()} - spawning at {spawn.Location}" );

		RespawnCharacterAtPoint( player, spawn.Location, spawn.Rotator() );
	}

	void OnAttackerLivesSet( AAS_TeamStateManager teamManager, int lives ) override
	{
		Super::OnAttackerLivesSet( teamManager, lives );
		AAS_TeamStateManager_RaidMode mgr = Cast<AAS_TeamStateManager_RaidMode>( teamManager );
		if ( lives == 0 )
		{
			mgr.LockRespawns( ERespawnLockedReason::NO_ATTACKER_LIVES );
		}
		else
		{
			mgr.UnlockRespawns();
		}
	}

	void OnPlayerDied_Obituary( ANCPlayerCharacter victim, ANCPlayerCharacter attacker, const FDamageInfo& damageInfo, bool isSuicide )
	{
		FServerCommandLocalizedData commandData = FServerCommandLocalizedData();
		int attackerIndex						= attacker.GetPlayerIndex();
		int victimIndex							= victim.GetPlayerIndex();

		commandData.AddParam_PlayerColor( attackerIndex );
		commandData.AddParam_PlayerName( attackerIndex );

		FGameplayTag weaponId;
		FGameplayTag weaponRarityId;
		FString localizationId = isSuicide ? Obituary::GAME_EVENT_PLAYER_KILLED_SELF : Obituary::GAME_EVENT_PLAYER_KILLED_PLAYER;

		if ( IsValidWeaponClassName( damageInfo.damageWeaponClassName ) )
		{
			UWeaponPrimaryAsset weaponClass = GetWeaponClassFromClassName( damageInfo.damageWeaponClassName );
			if ( IsValid( weaponClass ) )
			{
				FWeaponLoadoutDataStruct loadoutData = GetWeaponScriptData( weaponClass );
				weaponId							 = loadoutData.GetWeaponTag();
				weaponRarityId						 = GetWeaponRarity( weaponClass, damageInfo.weaponModBitField );

				// TODO @jmccarty: Uncomment when we are ready to show icons
				// commandData.AddParam_GameplayTag( weaponRarityId );
				// commandData.AddParam_GameplayTag( weaponId );
				// localizationId = Obituary::GAME_EVENT_PLAYER_KILLED_PLAYER_WEAPON;
			}
		}

		commandData.AddParam_PlayerColor( victimIndex );
		commandData.AddParam_PlayerName( victimIndex );

		Server_SendObituaryMessageToAllPlayers( localizationId, commandData );
	}
}
