UCLASS( Abstract )
class UAS_RadialWheelItem : UNC_RadialListItem
{
	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage itemIcon;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage itemBackground;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden )
	float unavailableIconOpacity = 0.25f;

	private UMaterialInstanceDynamic backgroundMaterial;
	private bool isAvailable = false;

	private const FName ITEM_STATE_PARAMETER_NAME = n"ItemState";
	private const int UNAVAILABLE_STATE = GameConst::INDEX_NONE;
	private const int AVAILABLE_STATE = 0;
	private const int SELECTED_STATE = 1;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		backgroundMaterial = CreateDynamicMaterialFromImageBrush( itemBackground );
		if ( IsValid( backgroundMaterial ) )
		{
			itemBackground.SetBrushFromMaterial( backgroundMaterial );
		}
	}

	UFUNCTION( BlueprintOverride )
	void OnDataSet( UNC_RadialListItemData inData )
	{
		UAS_RadialMenuListItemData menuData = Cast<UAS_RadialMenuListItemData>( inData );
		if ( !IsValid( menuData ) )
			return;

		if ( IsValid( inData.Definition.Icon.ResourceObject ) )
			itemIcon.SetBrushFromTexture( Cast<UTexture2D>( inData.Definition.Icon.ResourceObject ), false );

		// TODO @robin @jmccarty: This should be done in code when the widget is created
		UCanvasPanelSlot asSlot = Cast<UCanvasPanelSlot>( Slot );
		if ( IsValid( asSlot ) )
		{
			asSlot.SetAutoSize( true );
			asSlot.SetAlignment( FVector2D( 0.5f, 0.5f ) );
		}

		isAvailable = menuData.isAvailable;
		SetState( isAvailable ? AVAILABLE_STATE : UNAVAILABLE_STATE );
		itemIcon.SetRenderOpacity( isAvailable ? 1.0f : unavailableIconOpacity );
	}

	UFUNCTION( BlueprintOverride )
	void OnSelectedChanged( bool bNewIsSelected )
	{
		if ( isAvailable )
		{
			// TODO @jmccarty: What if the inventory count changes while the wheel is up
			SetState( bNewIsSelected ? SELECTED_STATE : AVAILABLE_STATE );
		}
	}

	private void SetState( int selectedState )
	{
		if ( IsValid( backgroundMaterial ) )
		{
			backgroundMaterial.SetScalarParameterValue( ITEM_STATE_PARAMETER_NAME, selectedState );
			InvalidateLayoutAndVolatility();
		}
	}
}