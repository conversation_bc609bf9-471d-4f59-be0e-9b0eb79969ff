UCLASS( Abstract )
class UAS_BasicNameWidget : UAS_PinnableWidget
{
	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		Super::Construct();
	}
}

UCLASS( Abstract )
class UAS_PlayerNameWidget : UAS_BasicNameWidget
{
	////////// main panel widgets //////////

	UPROPERTY( BlueprintReadOnly, NotVisible, Meta = ( BindWidget ) )
	UCommonTextBlock name_text;

	UPROPERTY( BlueprintReadOnly, NotVisible, Meta = ( BindWidget ) )
	UCommonTextBlock hostile_name_text;

	UPROPERTY( BlueprintReadOnly, NotVisible, Meta = ( BindWidget ) )
	UImage shieldbreaker_icon;

	UPROPERTY( BlueprintReadOnly, NotVisible, Meta = ( BindWidget ) )
	UAS_BleedoutWidget bleedout_status;

	UPROPERTY( BlueprintReadOnly, NotVisible, Meta = ( BindWidget ) )
	UPanelWidget shield_and_health;

	UPROPERTY( BlueprintReadOnly, NotVisible, Meta = ( BindWidget ) )
	UImage health_bar;

	UPROPERTY( BlueprintReadOnly, NotVisible, Meta = ( BindWidget ) )
	UImage shield_bar;

	UPROPERTY( BlueprintReadOnly, NotVisible, Meta = ( BindWidget ) )
	UImage disconnected_icon;

	UPROPERTY( BlueprintReadOnly, NotVisible, Meta = ( BindWidget ) )
	UImage disconnected_icon_offscreen;

	UPROPERTY( BlueprintReadOnly, NotVisible, Meta = ( BindWidget ) )
	UPanelWidget portrait_panel;

	UPROPERTY( BlueprintReadOnly, NotVisible, Meta = ( BindWidget ) )
	UImage indicator;

	UPROPERTY( BlueprintReadOnly, NotVisible, Meta = ( BindWidget ) )
	UImage player_portrait_frame;

	UPROPERTY( BlueprintReadOnly, NotVisible, Meta = ( BindWidget ) )
	UImage player_portrait;

	UPROPERTY( BlueprintReadOnly, NotVisible, Meta = ( BindWidget ) )
	UImage player_portrait_frame_offscreen;

	UPROPERTY( BlueprintReadOnly, NotVisible, Meta = ( BindWidget ) )
	UImage player_portrait_offscreen;

	UPROPERTY( NotVisible, NotVisible, Meta = ( BindWidget ) )
	UCommonTextBlock distance_text;

	UPROPERTY( NotVisible, NotVisible, Meta = ( BindWidget ) )
	UCommonTextBlock distance_text_close;

	UPROPERTY( NotVisible, NotVisible, Meta = ( BindWidget ) )
	UCommonTextBlock distance_text_offscreen;

	UPROPERTY( BlueprintReadOnly, NotVisible, Meta = ( BindWidget ) )
	UWidgetSwitcher minimalModeSwitcher;

	UPROPERTY( BlueprintReadOnly, NotVisible, Meta = ( BindWidget ) )
	UImage partyMemberIndicatorMinimal;

	UPROPERTY( BlueprintReadOnly, NotVisible, Meta = ( BindWidget ) )
	UPanelWidget offscreen_horizontal_box;

	////////// offscreen panel widgets //////////

	UPROPERTY( BlueprintReadOnly, NotVisible, Meta = ( BindWidget ) )
	UAS_BleedoutWidget bleedout_status_offscreen;

	FText playerName;
	int teamNum;

	// targetActor is the one whose name we're showing.
	// targetActor_Player is only valid if target actor is a player. Saves us from doing some casts.
	// ownerActor is the actor this widget is pinned to. This may not always be target actor. (For example, it may be a respawn totem that corresponds to target actor)
	const AActor targetActor;
	AAS_PlayerEntity targetActor_Player;

	bool isVisibleForEnemies = true;
	bool forceHideHealth = false;
	bool forceHideDistance = false;
	protected bool isFriendly;

	//AAS_RespawnTotem totem;

	private bool hasFullHealth = false;
	private bool hasFullShield = false;
	private bool minimalMode   = true;

	private float32 lastDamagedTime = 0;
	private bool healthVisibleBasedOnDistance = true;
	private bool nameVisibleBasedOnDistance = true;
	private float oldTotalHealth = 0;

	private UAS_RespawnTotemComponent targetRespawnTotemComponent;
	const UHealthComponent targetHealthComponent;

	FScriptDelegateHandle hardenStatusStartedHandle;
	FScriptDelegateHandle hardenStatusEndedHandle;

	const float tickInterval = 0.2f;
	float tickTimer = 0.0f;
	bool tickEnabled = false;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		Super::Construct();

		minimalMode   = GameModeDefaults().UI_PlayerNames_MinimalMode;

		ScriptCallbacks().client_onCrosshairActorChanged.AddUFunction( this, n"OnCrosshairActorChanged" );
		FGameplayTag passive = GameplayTags::Classes_Passives_IsJadeDemon;
		ScriptCallbacks().shared_onPassiveAdded[passive].AddUFunction( this, n"OnJadeDemonPassiveAdded" );
		ScriptCallbacks().shared_onPassiveRemoved[passive].AddUFunction( this, n"OnJadeDemonPassiveRemoved" );
		ScriptCallbacks().client_onShieldBreakerStatusChanged.AddUFunction( this, n"OnShieldBreakerStatusChanged" );
		
		UpdateHealthAndShields();
	}

	UFUNCTION()
	private void OnShieldBreakerStatusChanged( AAS_PlayerEntity asPlayer )
	{
		if ( !IsValid( asPlayer ) )
		{
			return;
		}

		bool isHeldByLocalPlayer = PlayerHasShieldBreaker( asPlayer ) && asPlayer == targetActor_Player;
		SetWidgetVisibilitySafe( shieldbreaker_icon, isFriendly && isHeldByLocalPlayer ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
	}

	UFUNCTION()
	private void OnCrosshairActorChanged( AAS_HUD hud, AActor oldCrosshairActor, AActor newCrosshairActor )
	{
		UpdateMainPanelVisibility();
	}

	UFUNCTION()
	private void OnJadeDemonPassiveAdded( ANCPlayerCharacter player, FGameplayTag tag )
	{
		if ( player != targetActor_Player )
			return;

		UpdateHealthAndShields();
	}

	UFUNCTION()
	private void OnJadeDemonPassiveRemoved( ANCPlayerCharacter player, FGameplayTag tag )
	{
		if ( player != targetActor_Player )
			return;

		UpdateHealthAndShields();
	}

	UFUNCTION( BlueprintEvent )
	void OnHealthChanged( float32 healthPercent )
	{
		UpdateHealthAndShields();
	}

	UFUNCTION()
	void OnShieldChanged( float32 shieldPercent, AAS_PlayerEntity asPlayer )
	{
		UpdateHealthAndShields();
	}

	UFUNCTION()
	private void OnHardenAdded( ANCPlayerCharacter player )
	{
		hardenedHealth = HardenConst::HARDEN_MAX_HEALTH;
		UpdateHealthAndShields();
	}

	UFUNCTION()
	private void OnHardenRemoved( ANCPlayerCharacter player )
	{
		hardenedHealth = 0;
		UpdateHealthAndShields();
	}

	float hardenedHealth = 0;

	void UpdateHealthAndShields()
	{
		const AActor owner = GetOwnerActor();
		if ( !IsValid( owner ) )
			return;

		bool isBleedout = owner.GetIsDowned();

		// Health and shields
		bool isJadeDemon = false;
		bool isLocallyControlled = false;
		if ( IsValid( targetActor_Player ) )
		{
			isJadeDemon = targetActor_Player.passivesComponent.HasPassive( GameplayTags::Classes_Passives_IsJadeDemon );
			isLocallyControlled = targetActor_Player.IsLocallyControlled(); // respawn totem
		}
		bool hasHealthComponent = true;
		if ( !IsValid( targetHealthComponent ) )
			hasHealthComponent = false;

		bool isEnemyDemon = !isFriendly && isJadeDemon;
		bool isFriendlyVisible = !isBleedout && isFriendly && healthVisibleBasedOnDistance;
		bool shouldShow = !forceHideHealth && hasHealthComponent && !isLocallyControlled && ( isEnemyDemon || isFriendlyVisible );		
		SetWidgetVisibilitySafe( shield_and_health, shouldShow ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
		
		if(!shouldShow)
			return;

		float shieldPercent = 0;
		float healthPercent = 0;
		// Health
		if ( IsValid( targetHealthComponent ) )
		{
			healthPercent = targetHealthComponent.GetHealthNormalized();
			UMaterialInstanceDynamic dynMat = health_bar.GetDynamicMaterial();
			dynMat.SetScalarParameterValue( n"Frac", healthPercent );
			hasFullHealth = healthPercent == 1.0f;
			shieldPercent = targetHealthComponent.GetShieldHealthNormalized();
		}

		// Shields
		if ( owner.IsActorShieldValid() )
		{
			FShieldItemData shieldData;
			shieldData = owner.GetEquippedShieldData();

			SetWidgetVisibilitySafe( shield_bar, ESlateVisibility::HitTestInvisible );
			UMaterialInstanceDynamic dynMat = shield_bar.GetDynamicMaterial();

			dynMat.SetScalarParameterValue( n"Frac", shieldPercent );
			// TODO @davis - Ask Jeremy, should I set using "get ui color" material func? That'd let me do it with an index.
			const FLootDataStruct& shieldLootData = GetLootDataByIndex( shieldData.gameplayTag );
			dynMat.SetVectorParameterValue( n"FillColor", GetRarityColor( shieldLootData.rarity ) );

			// TODO @jmccarty (copied from cl_hud_playerWidget.as): What's a good way to handle segments per health etc?
			// Is there a HUD globals somewhere? Currently hardcoded based on material used
			const int shieldNumSegments = Math::FloorToInt( shieldData.shieldMaxHealth / PlayerWidgetConst::HEALTH_PER_SEGMENT );
			dynMat.SetScalarParameterValue( n"MaxFrac", shieldNumSegments / PlayerWidgetConst::NUM_SHIELD_SEGMENTS );

			dynMat.SetScalarParameterValue( n"HardenFrac", hardenedHealth / 100.0f );
			dynMat.SetVectorParameterValue( n"HardenedColor", HardenConst::HARDEN_COLOR );
		}
		else
		{
			SetWidgetVisibilitySafe( shield_bar, ESlateVisibility::Collapsed );
		}

		hasFullShield = shieldPercent == 1.0;
	}

	UFUNCTION()
	void UpdateMainPanelVisibility()
	{
		// Owner actor is a pinned widget concept, it's not the target actor
		if ( !IsValid( ownerActor ) )
			return;

		AActor crossHairActor = GetInteractiveWidgetManager().crosshairActor;
		if ( IsValid( crossHairActor ) && crossHairActor.IsA( AAS_VehicleMount::StaticClass() ) )
			crossHairActor = Cast<AAS_VehicleMount>( crossHairActor ).GetPilot();

		bool isCrosshairEntity	= crossHairActor == ownerActor;
		bool isStealthed 		= CL_IsActorStealthed( crossHairActor );
		bool showIfEnemy		= !GetIsPinned() && isVisibleForEnemies && ( isCrosshairEntity ) && !isStealthed;

		if ( showIfEnemy || isFriendly )
		{
			SetWidgetVisibilitySafe( pinnedWidgetPanel, ESlateVisibility::HitTestInvisible );
			tickEnabled = true;
		}
		else
		{
			SetWidgetVisibilitySafe( pinnedWidgetPanel, ESlateVisibility::Hidden );
			tickEnabled = false;
		}

		if ( minimalMode && isFriendly )
		{
			minimalModeSwitcher.SetActiveWidgetIndex( isCrosshairEntity ? 0 : 1 );
			if ( !isCrosshairEntity )
				tickEnabled = false;
		}
	}

	UFUNCTION(BlueprintOverride)
	void Tick(FGeometry MyGeometry, float InDeltaTime)
	{
		if(!tickEnabled)
		{
			return;
		}
		
		if(tickTimer <= 0.0f)
		{
			ManualTick();
			tickTimer = tickInterval;
		}
		else
		{
			tickTimer -= InDeltaTime;
		}
	}

	void ManualTick()
	{
		if ( !IsValid( targetActor ) )
		{
			Warning( f"TargetPawn is invalid in Manual Tick.  Last player name recorded - {playerName}" );
			return;
		}

		if ( !IsValid( targetHealthComponent ) )
		{
			return;
		}

		AAS_PlayerEntity localPlayer = Client_GetLocalASPawn();
		if ( !IsValid( localPlayer ) )
		{
			return;
		}

		// We can't hide the actual widget, otherwise it won't tick. This is a workaround to effectively hide the name on players, which persists even if they're dead or as-yet-unspawned in pregame or something.
		// Instead of controlling state through outside this widget, I opted for a quick and easy solve.
		if ( IsValid( targetRespawnTotemComponent ) )
		{
			// Owner actor is a pinned widget concept, it's not the target actor
			const bool isTargetAlive = IsAlive( targetActor );
			bool showBecauseOwnerIsTotemAndTargetActorIsDead = !isTargetAlive && targetRespawnTotemComponent.net_isTotem && IsValid( ownerActor ) && ownerActor.IsA( AAS_RespawnTotem::StaticClass() ) && !targetRespawnTotemComponent.net_isBeingRevived;
			bool shouldShow = isTargetAlive || ( showBecauseOwnerIsTotemAndTargetActorIsDead );
			SetWidgetVisibilitySafe( minimalModeSwitcher, shouldShow ? ESlateVisibility::SelfHitTestInvisible : ESlateVisibility::Collapsed );
			SetWidgetVisibilitySafe( offscreen_horizontal_box, shouldShow ? ESlateVisibility::SelfHitTestInvisible : ESlateVisibility::Collapsed );
		}

		// Hide if not assigned to a player, otherwise show/hide based on whether assigned player is connected
		const bool targetPlayerValid = IsValid( targetActor_Player );
		bool showDisconnectIcon = !targetPlayerValid || ( targetPlayerValid && targetActor_Player.IsPlayerConnected() );
		SetWidgetVisibilitySafe( disconnected_icon, showDisconnectIcon ? ESlateVisibility::Collapsed : ESlateVisibility::HitTestInvisible );
		SetWidgetVisibilitySafe( disconnected_icon_offscreen, showDisconnectIcon ? ESlateVisibility::Collapsed : ESlateVisibility::HitTestInvisible );

		const float32 curTime = TO_SECONDS( GetTimeMilliseconds() );
		const float newTotalHealth = targetHealthComponent.GetTotalHealthNormalized();
		if ( newTotalHealth < oldTotalHealth )
		{
			lastDamagedTime = curTime;
		}
		oldTotalHealth = newTotalHealth;

		const bool wasRecentlyDamaged 	= curTime - lastDamagedTime < 1.5;
		const bool localPlayerIsDead 	= !IsAlive( localPlayer );		
		const bool isBleedout 			= targetActor.GetIsDowned();
		const float distanceToTarget 	= GetDistanceToTarget();
		const bool isFar 				= distanceToTarget >= GameConst::PARTY_FAR_DISTANCE;
		const bool isNear 				= ( distanceToTarget > GameConst::PARTY_NEAR_DISTANCE && !isFar) || localPlayerIsDead;
		const bool isClose 				= distanceToTarget <= GameConst::PARTY_NEAR_DISTANCE;

		bool showBleedout = isFriendly && isBleedout;
		SetWidgetVisibilitySafe( bleedout_status, showBleedout ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
		SetWidgetVisibilitySafe( bleedout_status_offscreen, showBleedout ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
		
		SetWidgetVisibilitySafe( portrait_panel, isFriendly && !isBleedout && ( isFar ) ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
		SetWidgetVisibilitySafe( indicator, isFriendly && !isBleedout && !isFar ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );

		const bool oldHealthVisibility	   = healthVisibleBasedOnDistance;
		healthVisibleBasedOnDistance = isFriendly && ( isNear || isClose );
		if ( oldHealthVisibility != healthVisibleBasedOnDistance )
		{
			UpdateHealthAndShields();
		}

		const bool oldNameVisibility = nameVisibleBasedOnDistance;
		nameVisibleBasedOnDistance = isNear || isClose;
		if ( oldNameVisibility != nameVisibleBasedOnDistance )
		{
			UpdateNameText();
		}
	}

	void SetDistanceToTarget(float newDistToTarget) override
	{
		Super::SetDistanceToTarget(newDistToTarget);

		AAS_PlayerEntity localPlayer 	= Client_GetLocalASPawn();
		const bool localPlayerIsDead 	= !IsAlive( localPlayer );	
		const float32 curTime 			= TO_SECONDS( GetTimeMilliseconds() );	
		const bool wasRecentlyDamaged 	= curTime - lastDamagedTime < 1.5;
		const bool isFar 				= newDistToTarget >= GameConst::PARTY_FAR_DISTANCE;
		const bool isNear 				= ( newDistToTarget > GameConst::PARTY_NEAR_DISTANCE && !isFar) || wasRecentlyDamaged || localPlayerIsDead;
		const bool isClose 				= newDistToTarget <= GameConst::PARTY_NEAR_DISTANCE;		
		const bool isBleedout 			= targetActor.GetIsDowned();

		//SetWidgetVisibilitySafe( distance_text, isFriendly && !localPlayerIsDead && ( isFar || isNear ) ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
		SetWidgetVisibilitySafe( distance_text, !forceHideDistance && isFriendly && !localPlayerIsDead && ( isFar ) ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
		// TODO: Verify with Luke/UX. Choosing to hide this for better read when player is downed
		//SetWidgetVisibilitySafe( distance_text_close, isFriendly && !isBleedout && !localPlayerIsDead && isClose  ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
		SetWidgetVisibilitySafe( distance_text_close, ESlateVisibility::Collapsed );//!forceHideDistance && isFriendly && !isBleedout && !localPlayerIsDead && ( isClose || isNear ) ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
		SetWidgetVisibilitySafe( distance_text_offscreen, isFriendly && !forceHideDistance ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );

		const FText formattedDistance = GetFormattedDistance( newDistToTarget );
		
		if ( !GetIsPinned() )
		{
			if ( distance_text.IsVisible() && distance_text.GetRenderOpacity() > 0  )
				distance_text.SetText( formattedDistance );
			
			if ( distance_text_close.IsVisible() && distance_text_close.GetRenderOpacity() > 0  )
				distance_text_close.SetText( formattedDistance );

		}
		else
		{
			distance_text_offscreen.SetText( formattedDistance );
		}
	}

	void OnOwnerActorChanged( const AActor oldOwner, const AActor newOwner ) override
	{
		Super::OnOwnerActorChanged( oldOwner, newOwner );

		if ( IsValid( oldOwner ) && oldOwner.IsA( AAS_RespawnTotem::StaticClass() ) )
		{
			bleedout_status.SetBleedoutTime( 0, 0 );
			bleedout_status.DisableWidgetTick();
		}

		// Totem special case - target actor is totem's owner
		AAS_RespawnTotem totem = Cast< AAS_RespawnTotem >( newOwner );
		if ( IsValid( totem ))
		{			
			bleedout_status.EnableWidgetTick();
			bleedout_status_offscreen.EnableWidgetTick();

			// This runs before client begin play on the component, so we gotta check manually instead of using the variable on the totem
			AAS_PlayerEntity ownerPlayer = Cast<AAS_PlayerEntity>( totem.GetOwnerPlayer() );			
			if ( IsValid( ownerPlayer ) )
			{
				SetTargetActor( ownerPlayer );
			}
			else 
			{
				AAS_TargetDummy_Human ownerTargetDummy =  Cast< AAS_TargetDummy_Human >( totem.net_dummyOwner.GetEntity() );
				if ( IsValid( ownerTargetDummy ) )
					SetTargetActor( ownerTargetDummy );
			}
			
			UpdateBleedoutWidgets();
		}
		else
		{
			SetTargetActor( newOwner );
		}
	}

	private void SetTargetActor( const AActor newOwner )
	{
		targetActor		    = newOwner;
		// Clear old bindings
		if ( IsValid( targetRespawnTotemComponent ) )
		{
			targetRespawnTotemComponent.net_totemEndTime.OnReplicated().UnbindObject( this );
			targetRespawnTotemComponent.net_totemStartTime.OnReplicated().UnbindObject( this );
			targetRespawnTotemComponent.onTotemStarted.Unbind( this, n"OnBleedoutChanged" );
			targetRespawnTotemComponent.onTotemEnded.Unbind( this, n"OnBleedoutChanged" );
		}

		targetRespawnTotemComponent = newOwner.TotemComponent();
		if ( IsValid( targetRespawnTotemComponent ) )
		{
			targetRespawnTotemComponent.net_totemEndTime.OnReplicated().AddUFunction( this, n"OnTotemExpireTimeChanged" );
			targetRespawnTotemComponent.net_totemStartTime.OnReplicated().AddUFunction( this, n"OnTotemExpireTimeChanged" );
			targetRespawnTotemComponent.onTotemStarted.AddUFunction( this, n"OnBleedoutChanged" );
			targetRespawnTotemComponent.onTotemEnded.AddUFunction( this, n"OnBleedoutChanged" );
		}

		targetHealthComponent = newOwner.GetHealthComponent();

		// GetPlayerNameAsText will also return the anonymized name so we don't have to worry about differentiating the two
		if ( newOwner.IsA( AAS_PlayerEntity::StaticClass() ) )
		{
			SetTargetPlayer( Cast<AAS_PlayerEntity>( newOwner ) );
			playerName = targetActor_Player.GetPlayerNameAsText();
		}
		else if ( IsValid( Cast<AAS_TargetDummy_Human>( newOwner ) ) )
		{
			playerName = GetLocalizedText( Localization::Training, f"target_dummy_name" );
		}

		if ( newOwner.CanHaveTeam() )
		{
			teamNum = newOwner.GetTeam();
		}

		ANCPlayerCharacter localPlayer = Client_GetLocalPawn();
		if ( IsValid( localPlayer ) )
		{
			if(localPlayer == targetActor)
			{
				isFriendly = true;

				UpdateNameText();
				UpdateMainPanelVisibility();
				UpdateHealthAndShields();
			}
			else
			{
				SetIsFriendly( IsFriendly( teamNum, localPlayer.GetTeam() ) );
			}
		}
		else
		{
			UpdateNameText();
			SetPartyMemberIndicator();
			UpdateMainPanelVisibility();
			UpdateHealthAndShields();
		}
	}

	UFUNCTION( BlueprintEvent )
	void SetTargetPlayer( AAS_PlayerEntity pawn )
	{
		if ( IsValid( targetActor_Player ) )
		{
			targetActor_Player.RemoveStatusEffectHasBegunCallback( GameplayTags::StatusEffect_Hardened, hardenStatusStartedHandle );
			targetActor_Player.RemoveStatusEffectHasEndedCallback( GameplayTags::StatusEffect_Hardened, hardenStatusEndedHandle );
		}

		targetActor_Player 	= pawn;
		
		hardenStatusStartedHandle = targetActor_Player.AddStatusEffectHasBegunCallback( GameplayTags::StatusEffect_Hardened, this, n"OnHardenAdded" );
		hardenStatusEndedHandle = targetActor_Player.AddStatusEffectHasEndedCallback( GameplayTags::StatusEffect_Hardened, this, n"OnHardenRemoved" );
	}

	void SetIsFriendly( bool inIsFriendly )
	{
		isFriendly = inIsFriendly;

		UpdateNameText();
		SetPartyMemberIndicator();
		UpdateMainPanelVisibility();
		UpdateHealthAndShields();
	}

	private void UpdateNameText()
	{
		name_text.SetText( playerName );
		hostile_name_text.SetText( playerName );

		SetWidgetVisibilitySafe( name_text, isFriendly && nameVisibleBasedOnDistance ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
		SetWidgetVisibilitySafe( hostile_name_text, !isFriendly ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
	}

	UFUNCTION()
	private void OnBleedoutChanged( AAS_PlayerEntity asPlayer )
	{
		UpdateNameText();
		UpdateHealthAndShields();
	}

	void SetPartyMemberIndicator(bool shouldRunTimingFix = true )
	{
		if ( isFriendly && IsValid( targetActor_Player ) )
		{
			const FLinearColor color = GetUIColorForPlayer( targetActor_Player );
			player_portrait_frame.SetColorAndOpacity( color );
			player_portrait_frame_offscreen.SetColorAndOpacity( color );
			indicator.SetColorAndOpacity( color );
			partyMemberIndicatorMinimal.SetColorAndOpacity( color );

			if ( IsValid( targetActor_Player.ClassManager() ) )
			{
				FClassDataStruct classData = targetActor_Player.ClassManager().GetClassData();
				player_portrait_offscreen.GetDynamicMaterial().SetTextureParameterValue( n"PortraitTexture", classData.characterSettingsAsset.CharacterIcon );
				player_portrait.GetDynamicMaterial().SetTextureParameterValue( n"PortraitTexture", classData.characterSettingsAsset.CharacterIcon );

				player_portrait_offscreen.GetDynamicMaterial().SetScalarParameterValue( n"PlayerState", float( 0 ) );
				player_portrait.GetDynamicMaterial().SetScalarParameterValue( n"PlayerState", float( 0 ) );
				InvalidateLayoutAndVolatility();
			}
		}

		SetWidgetVisibilitySafe( player_portrait, isFriendly ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
		SetWidgetVisibilitySafe( player_portrait_offscreen, isFriendly ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
		SetWidgetVisibilitySafe( player_portrait_frame_offscreen, isFriendly ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
		SetWidgetVisibilitySafe( player_portrait_frame, isFriendly ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );

		if ( shouldRunTimingFix )
			System::SetTimer( this, n"SetPartyMemberIndicatorTimingFix", 1.0, false );//fixes a timing bug with teammate colors and enemy pinned portraits.
	}

	UFUNCTION()
	void SetPartyMemberIndicatorTimingFix()
	{
		SetPartyMemberIndicator( false );
	}

	UFUNCTION()
	private void OnTotemExpireTimeChanged( int oldValue, int newValue )
	{
		UpdateBleedoutWidgets();
	}

	void UpdateBleedoutWidgets()
	{
		const AActor owner = GetOwnerActor();
		if ( !IsValid( owner ) )
			return;

		bleedout_status.SetOwner( owner );
		bleedout_status_offscreen.SetOwner( owner );

		if ( IsValid( targetRespawnTotemComponent ) )
		{
			int startTime = targetRespawnTotemComponent.net_totemStartTime;
			int endTime = targetRespawnTotemComponent.net_totemEndTime;

			bleedout_status.SetBleedoutTime( startTime, endTime );
			bleedout_status_offscreen.SetBleedoutTime( startTime, endTime );
		}
	}
}