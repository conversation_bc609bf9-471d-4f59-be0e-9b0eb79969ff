
UCLASS(Abstract)
class UAS_Reticle_PaladinSMG : UAS_ReticleWidget
{
	bool isEngaged = false;
	float deployAnimTime = 0.01;
	float deployAnimPlaybackSpeed = 0;

	UPROPERTY( Transient, meta = ( BindWidgetAnimOptional ) )
	UWidgetAnimation anim_basebreaker_deploy;
	
	void InitializeReticle(ANCWeapon weapon) override
	{
		Super::InitializeReticle(weapon);

		if ( !IsValid( weapon ) )
			return;

		deployAnimPlaybackSpeed = 1 / deployAnimTime;

		// Hacks - smg shares this file for now
		if ( IsValid( anim_basebreaker_deploy ) )
		{
			RegisterReticleAnimation( WeaponConst::BaseBreaker::RETICLE_ANIM_KEY, anim_basebreaker_deploy, EUMGSequencePlayMode::Forward, EUMGSequencePlayMode::Reverse, deployAnimPlaybackSpeed );
			PlayReticleAnimation( WeaponConst::BaseBreaker::RETICLE_ANIM_KEY, lookingAtBasebreakerTarget );
		}
	}
	void UpdateStateAnimations() override
	{
		Super::UpdateStateAnimations();
		
		if ( IsValid( anim_basebreaker_deploy ) )
		{
			PlayReticleAnimation( WeaponConst::BaseBreaker::RETICLE_ANIM_KEY, lookingAtBasebreakerTarget );
		}
	}

	void OnAimTargetStateUpdated() override
	{
		Super::OnAimTargetStateUpdated();
		
		if ( IsValid( anim_basebreaker_deploy ) )
		{
			PlayReticleAnimation( WeaponConst::BaseBreaker::RETICLE_ANIM_KEY, lookingAtBasebreakerTarget );
		}
	}
}