UCLASS( Abstract )
class UAS_SocialInviteWidget : UNC_DisplayWidget
{
	default StateOnConstruction = EDisplayConstructVisibility::Hidden;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_CommonTextBlock action;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_CommonTextBlock sender;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UWidgetSwitcher stateSwitcher;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage stateGlow;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_CommonTextBlock stateText;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_CommonButton acceptButton;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_CommonButton rejectButton;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage inviteTimer;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UPanelWidget partyInviteContainer;

	UPROPERTY( NotVisible, BlueprintHidden, Transient, Meta = ( BindWidgetAnim ) )
	private UWidgetAnimation actionTakenAnimation;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden )
	private TSubclassOf<UAS_CommonTextStyle> acceptedTextStyle;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden )
	private TSubclassOf<UAS_CommonTextStyle> declinedTextStyle;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden )
	private UNCAudioAsset acceptedSound;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden )
	private UNCAudioAsset declinedSound;

	private bool pendingAcceptance = false;
	private FSocialInviteData receivedSocialInvite;
	private TOptional<int> optEndTimeMs;

	private const int ACTION_PENDING_INDEX = 0;
	private const int ACTION_TAKEN_INDEX = 1;
	private const float32 SHORT_HOLD_TIME = 3.2f;
	private const float32 LONG_HOLD_TIME = 6.4f;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		acceptButton.OnButtonBaseClicked.AddUFunction( this, n"OnAcceptClicked" );
		rejectButton.OnButtonBaseClicked.AddUFunction( this, n"OnRejectClicked" );
	}

	void SetSocialInvite( FSocialInviteData socialInvite )
	{
		FNCUIPartyMember localPlayer;
		if ( GetLocalPlayerPartyMember( localPlayer ) )
		{
			// Cache the invite for later
			receivedSocialInvite = socialInvite;

			// Update the sender text and set the right action
			const bool localPlayerIsSender = socialInvite.senderPlayerHandle == localPlayer.GetPlayerHandle();
			const bool isPartyInvite	   = receivedSocialInvite.isPartyInvite;
			const FString displayName	   = localPlayerIsSender ? socialInvite.targetDisplayName : socialInvite.senderDisplayName;
			const FString actionKey		   = localPlayerIsSender ? "request_sent" : ( isPartyInvite ? "request_party_received" : "request_friend_received" );

			sender.SetText( Localization::GetUnlocalizedTextFromString( displayName ) );
			action.SetText( GetLocalizedText( Localization::PlayerMessaging, actionKey ) );

			// Reset the buttons and state for the new invite
			SetWidgetVisibilitySafe( partyInviteContainer, isPartyInvite ? ESlateVisibility::SelfHitTestInvisible : ESlateVisibility::Collapsed );
			stateSwitcher.SetActiveWidgetIndex( ACTION_PENDING_INDEX );
		}
	}

	UFUNCTION( BlueprintOverride )
	private void OnShowStart()
	{
		System::SetTimer( this, n"ClearSocialInvite", receivedSocialInvite.isPartyInvite ? LONG_HOLD_TIME : SHORT_HOLD_TIME, false );

		if ( receivedSocialInvite.isPartyInvite )
		{
			// Party invites are timed so we want to kick off and show a timer bar
			optEndTimeMs = GetRealTimeMs() + TO_MILLISECONDS( LONG_HOLD_TIME );
			UpdateInviteTimer();
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void ClearSocialInvite()
	{
		Hide();
	}

	UFUNCTION( BlueprintOverride )
	private void OnHideEnd()
	{
		// We can remove the widget from the container when hide ends and prep for the next message
		RemoveFromParent();
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnAcceptClicked( UCommonButtonBase button )
	{
		// When accept is clicked we want to track that we are pending an accept
		pendingAcceptance = true;

		FNCPartyOperationSuccessCallback onSuccess = FNCPartyOperationSuccessCallback( this, n"OnSuccess" );
		FNCPartyOperationErrorCallback onFailure   = FNCPartyOperationErrorCallback( this, n"OnFailure" );
		NCOnlineParty::JoinParty( receivedSocialInvite.senderPlayerHandle, onSuccess, onFailure );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnRejectClicked( UCommonButtonBase button )
	{
		// When accept is clicked we want to track that we are pending a reject
		pendingAcceptance = false;
		OnSuccess();
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnSuccess()
	{
		// Set up the widget index, text, and glow color
		stateSwitcher.SetActiveWidgetIndex( ACTION_TAKEN_INDEX );

		FLinearColor glowColor;
		GetSafeColor( pendingAcceptance ? n"system_green" : n"system_red", glowColor );
		stateGlow.SetColorAndOpacity( glowColor );

		stateText.SetStyle( pendingAcceptance ? acceptedTextStyle : declinedTextStyle );
		stateText.SetText( GetLocalizedText( Localization::Social, pendingAcceptance ? "state_accepted" : "state_declined" ) );

		// Reset any timers we were showing
		optEndTimeMs.Reset();

		// Then trigger the animation and sound
		PlayAnimationForward( actionTakenAnimation );
		Client_EmitSoundUI( pendingAcceptance ? acceptedSound : declinedSound );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnFailure( const FGameplayTag&in errorMessage )
	{
		stateSwitcher.SetActiveWidgetIndex( ACTION_PENDING_INDEX );
	}

	UFUNCTION( NotBlueprintCallable )
	private void UpdateInviteTimer()
	{
		UMaterialInstanceDynamic inviteMaterial = inviteTimer.GetDynamicMaterial();
		if ( IsValid( inviteMaterial ) )
		{
			if ( !optEndTimeMs.IsSet() )
				return;

			int endTimeMs	  = optEndTimeMs.GetValue();
			int currentTimeMs = GetRealTimeMs();
			int timeLeft	  = Math::Max( endTimeMs - currentTimeMs, 0 );

			float progress = LONG_HOLD_TIME > 0 ? ( float( timeLeft ) / TO_MILLISECONDS( LONG_HOLD_TIME ) ) : 0.0f;
			inviteMaterial.SetScalarParameterValue( MaterialParameter::LERP_ALPHA, progress );

			if ( timeLeft > 0 )
			{
				System::SetTimerForNextTick( this, "UpdateInviteTimer" );
			}
			else
			{
				optEndTimeMs.Reset();
			}
		}
	}
}