UCLASS(Abstract)
class UUserWidgetDefault : UNCUserWidget
{
	UPROPERTY(BlueprintReadOnly, NotVisible)
	FLinearColor GlobalShadowColor = GlobalHUD::ShadowColor;

   	UPROPERTY(BlueprintReadOnly, NotVisible)
	FLinearColor GlobalDisableColor = GlobalHUD::DisabledColor;

	UPROPERTY(BlueprintReadOnly, NotVisible)
	FLinearColor GlobalAltBlurColor = GlobalHUD::GlobalAltBlurColor;

	UPROPERTY(BlueprintReadOnly, NotVisible)
	FLinearColor GlobalGoodHealth   = GlobalHUD::GoodHealth;
	
	UPROPERTY(BlueprintReadOnly, NotVisible)
	FLinearColor GlobalMedHealth    = GlobalHUD::MedHealth;
	
	UPROPERTY(BlueprintReadOnly, NotVisible)
	FLinearColor GlobalBadHealth    = GlobalHUD::BadHealth;
	
}