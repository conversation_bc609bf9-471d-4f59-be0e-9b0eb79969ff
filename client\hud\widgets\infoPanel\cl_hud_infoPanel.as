struct FInfoPanelData
{
	UPROPERTY()
	UTexture2D image;

	UPROPERTY()
	FText title;

	UPROPERTY()
	FText details;
}

UCLASS(Abstract)
class UAS_InfoPanelWidget : UUserWidgetDefault
{
	UPROPERTY(BlueprintReadOnly, Meta=(BindWidget))
	UImage image;

	UPROPERTY(BlueprintReadOnly, Meta=(BindWidget))
	UTextBlock title;

	UPROPERTY(BlueprintReadOnly, Meta=(BindWidget))
	UTextBlock body;

	UPROPERTY(BlueprintReadOnly, Meta=(BindWidgetAnim), Transient)
	UWidgetAnimation appear;

	UUMGSequencePlayer sequence;

	bool isShowing = false;

	void Show()
	{
		if ( isShowing )
			return;

		isShowing = true;
		sequence = PlayAnimation( appear );
	}

	void SetPanelData( FInfoPanelData data, bool show = true )
	{
		SetImageBrushIcon( image, data.image );
		title.SetText( data.title );
		body.SetText( data.details );

		if ( show )
			Show();
	}

	bool Hide()
	{
		if ( !isShowing )
			return false;

		isShowing = false;
		sequence = PlayAnimationReverse( appear );

		return true;
	}
}