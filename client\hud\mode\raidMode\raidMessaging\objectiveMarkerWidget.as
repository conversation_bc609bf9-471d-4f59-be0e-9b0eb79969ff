UCLASS( Abstract )
class UAS_ObjectiveMarker : UNC_DisplayWidget
{
	default StateOnConstruction = EDisplayConstructVisibility::Shown;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_CommonTextBlock name;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage objectiveMarker;

	UPROPERTY( NotVisible, BlueprintHidden, Transient, Meta = ( BindWidgetAnim ) )
	private UWidgetAnimation destroyedAnimation;

	UPROPERTY( NotVisible, BlueprintHidden, Transient, Meta = ( BindWidgetAnim ) )
	private UWidgetAnimation inUseAnimation;

	UPROPERTY( NotVisible, BlueprintHidden, Transient, Meta = ( BindWidgetAnim ) )
	private UWidgetAnimation bombPlantedAnimation;

	UPROPERTY( NotVisible, EditDefaultsOnly )
	private TSubclassOf<UCommonTextStyle> plantingOrDefusingTextStyle;

	UPROPERTY( NotVisible, EditDefaultsOnly )
	private TSubclassOf<UCommonTextStyle> countdownTextStyle;

	UPROPERTY( NotVisible, EditDefaultsOnly )
	private float destroyedOpacity = 0.5f;

	UPROPERTY( NotVisible, EditDefaultsOnly )
	private float postDestroyedAnimationDelay = 2.0f;

	UMaterialInstanceDynamic markerMaterial;
	private TOptional<AAS_BaseVault> optVault;
	private TOptional<AAS_BaseSubObjective> optObjective;
	private FText objectiveName;
	private bool showTimerText = false;
	private bool hasActiveBomb = false;

	private const FName SEQUENCE_INDEX_PARAMETER = n"SequenceIndex";

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			scriptCallbacks.client_OnObjectiveMarkerPlantInProgress.AddUFunction( this, n"OnObjectiveMarkerPlantInProgress" );
			scriptCallbacks.client_OnObjectiveMarkerBombPlanted.AddUFunction( this, n"OnObjectiveMarkerBombPlanted" );
			scriptCallbacks.client_OnObjectiveMarkerDefuseInProgress.AddUFunction( this, n"OnObjectiveMarkerDefuseInProgress" );
			scriptCallbacks.client_OnObjectiveMarkerCountdown.AddUFunction( this, n"OnObjectiveMarkerCountdown" );
			scriptCallbacks.client_OnObjectiveMarkerDestroyed.AddUFunction( this, n"OnObjectiveMarkerDestroyed" );
			scriptCallbacks.client_OnObjectiveMarkerBombDefused.AddUFunction( this, n"OnObjectiveMarkerBombDefused" );
			scriptCallbacks.client_OnObjectiveMarkerPlantStopped.AddUFunction( this, n"OnObjectiveMarkerPlantStopped" );
			scriptCallbacks.client_OnObjectiveMarkerActiveChanged.AddUFunction( this, n"OnObjectiveMarkerActiveChanged" );
		}
	}

	UAS_RaidBombInterfaceComponent _bombInterfaceComponent;

	UFUNCTION()
	private void OnObjectiveMarkerActiveChanged(AActor actor, bool oldValue, bool newValue)
	{
		if ( !IsAffectingTrackedObjective( actor ) )
			return;

		if ( newValue )
		{
			MarkAsUndestroyed();
		}
		else
		{
			MarkAsDestroyed();
		}
	}

	void SetOwningObjective( AActor objective )
	{
		if ( !IsValid( objective ) )
			return;

		bool hasBomb	 = false;
		bool isDestroyed = false;

		AAS_BaseVault baseVault			  = Cast<AAS_BaseVault>( objective );
		AAS_BaseSubObjective subObjective = Cast<AAS_BaseSubObjective>( objective );

		if ( IsValid( baseVault ) )
		{
			optVault = baseVault;
			optObjective.Reset();

			markerMaterial = baseVault.objectiveMarkerComponent.markerMaterial;
			objectiveMarker.SetBrushFromMaterial( markerMaterial );
		}
		else if ( IsValid( subObjective ) )
		{
			optObjective = subObjective;
			optVault.Reset();
			hasBomb = subObjective.GetHasBomb();

			if ( !subObjective.GetIsActiveState() )
			{
				isDestroyed = true;
			}

			markerMaterial = subObjective.objectiveMarkerComponent.markerMaterial;
			objectiveMarker.SetBrushFromMaterial( markerMaterial );
		}

		if ( IsValid(_bombInterfaceComponent) )
		{
			_bombInterfaceComponent.net_bombStage.OnReplicated().Unbind( this, n"OnBombStageChanged" );
		}

		_bombInterfaceComponent = Cast<UAS_RaidBombInterfaceComponent>( objective.GetComponentByClass( UAS_RaidBombInterfaceComponent::StaticClass() ) );

		if ( IsValid(_bombInterfaceComponent) )
		{
			_bombInterfaceComponent.net_bombStage.OnReplicated().AddUFunction( this, n"OnBombStageChanged" );
			OnBombStageChanged( _bombInterfaceComponent.net_bombStage, _bombInterfaceComponent.net_bombStage );
		}
		else
		{
			Print( "No bomb interface??" );
		}

		// Get the player facing data of whatever we are tracking
		FRaidBaseObjectiveData playerFacingData = optVault.IsSet()	   ? optVault.GetValue().playerFacingData :
												  optObjective.IsSet() ? optObjective.GetValue().playerFacingData :
																		 FRaidBaseObjectiveData();

		if ( !isDestroyed )
		{
			// TODO @jmccarty: Init a bomb in progress
			objectiveName = playerFacingData.indicator < EObjectiveMarkerIndicator::VAULT ? playerFacingData.name : Localization::GetUnlocalizedTextFromString( Glyphs::VAULT );
			name.SetText( objectiveName );
		}

		if ( IsValid( markerMaterial ) )
		{
			// We use the indicator as an index into an RGBA vector to change params for each objective
			markerMaterial.SetScalarParameterValue( SEQUENCE_INDEX_PARAMETER, int( playerFacingData.indicator ) );
		}
	}

	UFUNCTION()
	void OnBombStageChanged(int oldValue, int newValue)
	{
		UpdatePinWidgetFrac();
	}

	UFUNCTION()
	void UpdatePinWidgetFrac()
	{
		int bombStage = !IsValid( _bombInterfaceComponent ) ? 0 : _bombInterfaceComponent.net_bombStage;
		AAS_RaidBomb bombClass = Cast<AAS_RaidBomb>( _bombInterfaceComponent.raidBombClass.Get().GetDefaultObject() );
		int numStages = bombClass.Default_GetNumStages();
		float32 frac = float32( bombStage ) / float32( numStages );

		if ( IsValid( markerMaterial ) )
		{
			markerMaterial.SetScalarParameterValue( MaterialParameter::STAGE_ALPHA, frac );
		}
	}

	bool IsBombAffectingTrackedObjective( AAS_RaidBomb bomb )
	{
		bool result = false;

		if ( IsValid( bomb ) )
		{
			AActor entity = bomb.net_VaultHandle.GetEntity();

			result = ( optObjective.IsSet() && optObjective.GetValue() == entity ) ||
					 ( optVault.IsSet() && optVault.GetValue() == entity );
		}

		return result;
	}

	bool IsBombInterfaceAffectingTrackedObjective( UAS_RaidBombInterfaceComponent interface )
	{
		bool result = false;

		if ( IsValid( interface ) )
		{
			AActor owner = interface.GetOwner();

			result = ( optObjective.IsSet() && optObjective.GetValue() == owner ) ||
					 ( optVault.IsSet() && optVault.GetValue() == owner );
		}

		return result;
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnObjectiveMarkerPlantInProgress( AActor actor )
	{
		if ( !IsAffectingTrackedObjective( actor ) )
			return;

		// Play the in use animation if we just got a plant event
		PlayAnimationForward( inUseAnimation, 4.0f );

		showTimerText = false;
		hasActiveBomb = false;
		OnObjectiveMarkerPlantingOrDefusing();
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnObjectiveMarkerBombPlanted( AActor actor )
	{
		if ( !IsAffectingTrackedObjective( actor ) )
			return;

		// Set the text style or icon tint based on the states above, countdown is white, planting and defusing are black
		name.SetStyle( countdownTextStyle );

		// Trigger a unique animation when the bomb is planted
		PlayAnimationForward( bombPlantedAnimation, 2.0f );
		showTimerText = true;
		hasActiveBomb = true;
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnObjectiveMarkerDefuseInProgress( AActor actor )
	{
		if ( !IsAffectingTrackedObjective( actor ) )
			return;

		OnObjectiveMarkerPlantingOrDefusing();
		showTimerText = false;
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnObjectiveMarkerCountdown( AActor actor, float value )
	{
		if ( !IsAffectingTrackedObjective( actor ) )
			return;

		if ( showTimerText )
		{
			name.SetText( FText::AsNumber( value, GetDefaultNumberFormattingOptionsWithGrouping() ) );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnObjectiveMarkerDestroyed( AActor actor )
	{
		if ( !IsAffectingTrackedObjective( actor ) )
			return;

		showTimerText = false;
		hasActiveBomb = false;
		PlayAnimationForward( destroyedAnimation );
		PlayAnimationReverse( inUseAnimation, 6.0f );
		System::SetTimer( this, n"OnDestroyedAnimationFinished", destroyedAnimation.GetEndTime() + postDestroyedAnimationDelay, false );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnObjectiveMarkerBombDefused( AActor actor )
	{
		if ( !hasActiveBomb )
			return;

		// If we no longer have a plant event, make sure the in use animation is reversed
		OnObjectiveMarkerPlantingOrDefusing();
		PlayAnimationReverse( inUseAnimation, 6.0f );
		showTimerText = false;
		hasActiveBomb = false;
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnObjectiveMarkerPlantStopped( AActor actor )
	{
		if ( !IsAffectingTrackedObjective( actor ) )
			return;

		// If we no longer have a plant event, make sure the in use animation is reversed
		PlayAnimationReverse( inUseAnimation, 6.0f );
		showTimerText = false;
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnDestroyedAnimationFinished()
	{
		SetRenderOpacity( destroyedOpacity );
	}

	private void OnObjectiveMarkerPlantingOrDefusing()
	{
		// Set the text style or icon tint based on the states above, countdown is white, planting and defusing are black
		name.SetStyle( plantingOrDefusingTextStyle );
		name.SetText( objectiveName );
	}

	void MarkAsDestroyed()
	{
		objectiveName = Text::EmptyText;
		name.SetText( Text::EmptyText );

		OnDestroyedAnimationFinished();

		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			scriptCallbacks.client_OnObjectiveMarkerPlantInProgress.Unbind( this, n"OnObjectiveMarkerPlantInProgress" );
			scriptCallbacks.client_OnObjectiveMarkerBombPlanted.Unbind( this, n"OnObjectiveMarkerBombPlanted" );
			scriptCallbacks.client_OnObjectiveMarkerDefuseInProgress.Unbind( this, n"OnObjectiveMarkerDefuseInProgress" );
			scriptCallbacks.client_OnObjectiveMarkerCountdown.Unbind( this, n"OnObjectiveMarkerCountdown" );
			scriptCallbacks.client_OnObjectiveMarkerDestroyed.Unbind( this, n"OnObjectiveMarkerDestroyed" );
			scriptCallbacks.client_OnObjectiveMarkerBombDefused.Unbind( this, n"OnObjectiveMarkerBombDefused" );
			scriptCallbacks.client_OnObjectiveMarkerPlantStopped.Unbind( this, n"OnObjectiveMarkerPlantStopped" );
			scriptCallbacks.client_OnObjectiveMarkerActiveChanged.Unbind( this, n"OnObjectiveMarkerActiveChanged" );
		}
	}

	void MarkAsUndestroyed()
	{
		// Get the player facing data of whatever we are tracking
		FRaidBaseObjectiveData playerFacingData = optVault.IsSet()	   ? optVault.GetValue().playerFacingData :
												  optObjective.IsSet() ? optObjective.GetValue().playerFacingData :
																		 FRaidBaseObjectiveData();
		SetRenderOpacity( 1.0 );

		if ( hasActiveBomb )
			name.SetStyle( countdownTextStyle );
		else
			name.SetStyle( plantingOrDefusingTextStyle );

		// TODO @jmccarty: Init a bomb in progress
		objectiveName = playerFacingData.indicator < EObjectiveMarkerIndicator::VAULT ? playerFacingData.name : Localization::GetUnlocalizedTextFromString( Glyphs::VAULT );
		name.SetText( objectiveName );

		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			scriptCallbacks.client_OnObjectiveMarkerPlantInProgress.AddUFunction( this, n"OnObjectiveMarkerPlantInProgress" );
			scriptCallbacks.client_OnObjectiveMarkerBombPlanted.AddUFunction( this, n"OnObjectiveMarkerBombPlanted" );
			scriptCallbacks.client_OnObjectiveMarkerDefuseInProgress.AddUFunction( this, n"OnObjectiveMarkerDefuseInProgress" );
			scriptCallbacks.client_OnObjectiveMarkerCountdown.AddUFunction( this, n"OnObjectiveMarkerCountdown" );
			scriptCallbacks.client_OnObjectiveMarkerDestroyed.AddUFunction( this, n"OnObjectiveMarkerDestroyed" );
			scriptCallbacks.client_OnObjectiveMarkerBombDefused.AddUFunction( this, n"OnObjectiveMarkerBombDefused" );
			scriptCallbacks.client_OnObjectiveMarkerPlantStopped.AddUFunction( this, n"OnObjectiveMarkerPlantStopped" );
			scriptCallbacks.client_OnObjectiveMarkerActiveChanged.AddUFunction( this, n"OnObjectiveMarkerActiveChanged" );
		}
	}

	private bool IsAffectingTrackedObjective( AActor actor )
	{
		return ( optVault.IsSet() && optVault.GetValue() == actor ) || ( optObjective.IsSet() && optObjective.GetValue() == actor );
	}
}