UCLASS( Abstract )
class UAS_ModulePrimaryCta : UAS_CommonButton
{
	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_CurrencyItem currencyCost;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UPanelWidget purchaseInfoContainer;

	private const int LOCKED_INDEX = 0;
	private const int UNLOCKED_INDEX = 1;

	UFUNCTION( BlueprintOverride )
	void PreConstruct( bool IsDesignTime )
	{
		Super::PreConstruct( IsDesignTime );

		UAS_SequenceText sequenceText = Cast<UAS_SequenceText>( buttonText );
		if ( IsValid( sequenceText ) )
		{
			sequenceText.Update();
		}
	}

	void UpdateCtaButton( FNCUIStoreBundle bundle )
	{
		UNCUICollectionManager collectionManager = GetCollectionManager();
		if ( IsValid( collectionManager ) )
		{
			bool isOwned = collectionManager.CheckIsOwned( bundle.Id );

			UAS_SequenceText sequenceText = Cast<UAS_SequenceText>( buttonText );
			if ( IsValid( sequenceText ) )
			{
				// Set the text state based on if the battlepass is owned or not
				sequenceText.SetFrameIndex( isOwned ? UNLOCKED_INDEX : LOCKED_INDEX );
			}

			if ( IsValid( SingleMaterialStyleMID ) )
			{
				SingleMaterialStyleMID.SetScalarParameterValue( n"UseBundleTheme", MaterialParameter::GetTrueFalseFloat( !isOwned ) );
			}

			// Hide the purchase info if the player has purchased it
			SetWidgetVisibilitySafe( purchaseInfoContainer, isOwned ? ESlateVisibility::Collapsed : ESlateVisibility::SelfHitTestInvisible );

			// Only the purchase action is held
			SetButtonRequiresHold( !isOwned );
		}

		// Use the pricing info to update everything on the currency widget
		currencyCost.SetCurrencyType( bundle.PricingInfo.CurrencyType );
		currencyCost.SetCurrencyAmount( bundle.PricingInfo.UserPrice );
	}
}