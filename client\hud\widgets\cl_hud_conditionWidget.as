UCLASS( Abstract )
class UAS_ConditionWidget : UUserWidgetDefault
{
	UPROPERTY( NotVisible, Meta = ( BindWidgetOptional ) )
	UPanelWidget conditionContainer;

	UPROPERTY( NotVisible, Meta = ( BindWidgetOptional ) )
	UImage conditionBackground;

	UPROPERTY( NotVisible, BindWidget )
	UVerticalBox conditionList;

	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<UAS_ConditionSegment> segmentSubclass;

	void UpdateCondition( FGameplayTag weaponIndex, int currentCondition )
	{
		int maxCondition = GetDesiredConditionMax( weaponIndex );

		if ( segmentSubclass.IsValid() )
		{
			for ( int i = conditionList.ChildrenCount; i < maxCondition; i++ )
			{
				// Allocate enough items to meet the max possible
				UUserWidget segment = WidgetBlueprint::CreateWidget( segmentSubclass, GetOwningPlayer() );
				conditionList.AddChild( segment );
			}
		}

		for ( int i = 0; i < conditionList.ChildrenCount; i++ )
		{
			UAS_ConditionSegment widget = Cast<UAS_ConditionSegment>( conditionList.GetChildAt( i ) );
			if ( IsValid( widget ) )
			{
				widget.SetSegmentFilled( i >= maxCondition - currentCondition );
				SetWidgetVisibilitySafe( widget, i < maxCondition ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
			}
		}

		bool showContainer = maxCondition > 0 && currentCondition > 0;
		SetWidgetVisibilitySafe( conditionContainer, showContainer ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Hidden );
	}

	void UpdateRarity( FGameplayTag rarity )
	{
		if ( IsValid( conditionBackground ) )
		{
			// TODO @jmccarty: ScriptError hack
			// Devs can add weapons for testing without registering them in the loot pool
			// resulting in a ScriptError, we still likely want the ScriptError but when weapons
			// are equipped, we should support that and give those items basic rarity
			/////////////////////////////////////////////////////////////////////////////////////
			FGameplayTag newRarity = rarity.IsValid() ? rarity : GameplayTags::Loot_Rarity_Basic;
			/////////////////////////////////////////////////////////////////////////////////////
			conditionBackground.SetColorAndOpacity( GetRarityColor( newRarity ) );
		}
	}
}