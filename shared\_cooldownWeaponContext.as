UCLASS()
class UAS_WeaponContext_Ultimate : UAS_WeaponContext_CooldownAbility
{
	default cooldownTime = 300.0;

	UFUNCTION( BlueprintOverride )
	void CodeCallback_OnWeaponDataSet( ANCWeapon weapon )
	{
		Super::CodeCallback_OnWeaponDataSet( weapon );
	}

	UAS_PlayerCooldownComponent GetCooldownComponent( ANCWeapon weapon ) override
	{
		AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( weapon.GetWeaponOwner() );
		return player.ultCooldownComponent;
	}
}

/////////////////////////////////////////////////////////////////////////////////////////////

UCLASS()
class UAS_WeaponContext_TacticalAbility : UAS_WeaponContext_CooldownAbility
{
	UPROPERTY()
	bool swapToPrimaryWhenHolstered = true;

	UAS_PlayerCooldownComponent GetCooldownComponent( ANCWeapon weapon ) override
	{
		AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( weapon.GetWeaponOwner() );
		return player.tacticalCooldownComponent;
	}

	UFUNCTION(BlueprintOverride)
	void CodeCallback_OnWeaponHolster(ANCWeapon weapon)
	{
		if (swapToPrimaryWhenHolstered && IsValid(weapon) && IsValid(weapon.WeaponOwner))
		{
			weapon.WeaponOwner.EquipLastPrimaryWeapon(false, false);
		}
	}
}

////////////////////////////////////////////////////////////////////////

UCLASS()
class UAS_WeaponContext_CooldownAbility : UNCWeaponScriptContext
{
	FGameplayTag battleChatterLine;
	float32 battleChatterLine_startDelay = 0.0;

	UPROPERTY( EditDefaultsOnly )
	float32 cooldownTime = 15.0f;

	UFUNCTION( BlueprintOverride )
	void CodeCallback_OnWeaponDataSet( ANCWeapon weapon )
	{
		if ( IsServer() )
		{
			Thread( this, n"DelayedWeaponDataSet", weapon );
		}
	}

	UFUNCTION()
	private void DelayedWeaponDataSet( UNCCoroutine co, ANCWeapon weapon )
	{
		co.EndOnDestroyed( weapon );
		while ( weapon.GetClipAmmoMax() < 0 || weapon.GetClipAmmo() < 0 )
		{
			co.Wait(0.1);
		}
		UAS_PlayerCooldownComponent comp = GetCooldownComponent( weapon );
		if ( !comp.restoreAmmoDataSaved && weapon.GetClipAmmo() < weapon.GetClipAmmoMax() && !comp.IsCooldownActive() )
		{
			comp.StartCooldown( weapon, cooldownTime );
		}
	}

	UFUNCTION( BlueprintOverride )
	void CodeCallback_OnWeaponTossRelease( ANCWeapon weapon, FWeaponPrimaryAttackInfo attackInfo,
										   FWeaponPrimaryAttackReturnParams& returnInfo )
	{
		StartCooldown( weapon );
		PlayBattleChatter( weapon );
	}

	UFUNCTION( BlueprintOverride )
	void CodeCallback_OnWeaponPrimaryAttack( ANCWeapon weapon, FWeaponPrimaryAttackInfo attackInfo,
											 FWeaponPrimaryAttackReturnParams& returnInfo )
	{
		StartCooldown( weapon );
		PlayBattleChatter( weapon );
	}

	void PlayBattleChatter( ANCWeapon weapon )
	{
		if ( IsServer() )
		{
			if ( battleChatterLine.IsValid() )
			{
				weapon.GetWeaponOwner().PlayBattleChatter( battleChatterLine, battleChatterLine_startDelay );
			}
		}
	}

	void StartCooldown( ANCWeapon weapon )
	{
		if ( IsServer() )
		{
			UAS_PlayerCooldownComponent comp = GetCooldownComponent( weapon );
			if ( !comp.IsCooldownActive() )
			{
				comp.StartCooldown( weapon, cooldownTime, 0 );
			}
		}
	}

	UAS_PlayerCooldownComponent GetCooldownComponent( ANCWeapon weapon )
	{
		return nullptr;
	}

	UFUNCTION( BlueprintOverride )
	bool CodeCallback_CanSwitchToWeaponCallback( ANCWeapon weapon )
	{
		// you need this until we get an official offhand system
		if ( weapon.GetClipAmmo() < 1 && !weapon.HasInfiniteAmmo() )
		{
			if ( IsClient() )
			{
				UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
				if ( IsValid( scriptCallbacks ) )
				{
					scriptCallbacks.client_OnTriedToUseWeaponOnCooldown.Broadcast( weapon );
				}
			}
			return false;
		}

		return true;
	}
}