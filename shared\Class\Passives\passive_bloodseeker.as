UAS_Passive_Bloodseeker Passive_Bloodseeker()
{
	UAS_Passive_Bloodseeker result = Cast<UAS_Passive_Bloodseeker>( UNCGameplaySystemsSubsystem::Get_SharedSystem( GetCurrentWorld(), UAS_Passive_Bloodseeker::StaticClass() ) );
	return result;
}


struct FWolfFormData
{
	TArray<AFXActor> trailVFXs;
}

UCLASS()
class UAS_WeaponContext_WolfBite : UNCWeaponScriptContext
{
	UPROPERTY()
	float lungeTime = 0.25;
	UPROPERTY()
	float lungeHitBounceStrength = 600.0;

	ANCPlayerCharacter ownerPlayer;
	FVector lungeStartLoc;

	UFUNCTION( BlueprintOverride )
	bool CodeCallback_CanFireWeapon( ANCWeapon weapon )
	{
		// NOTE -- joe - disabling this weapon until steven can test and adjust
		return false;

		ANCPlayerCharacter owner = weapon.GetWeaponOwner();
		AAS_VehicleMount mount = Mount::GetPilotedMount(owner);
		if (mount == nullptr)
			return false;

		return mount.GetLungeTarget() != nullptr;
	}

	UFUNCTION( BlueprintOverride )
	void CodeCallback_OnWeaponPrimaryAttack( ANCWeapon weapon, FWeaponPrimaryAttackInfo attackInfo,
											 FWeaponPrimaryAttackReturnParams& rInfo )
	{
		rInfo.ammoUsed = 0;

		ownerPlayer = weapon.GetWeaponOwner();
		AAS_VehicleMount mount = Mount::GetPilotedMount(ownerPlayer);
		AActor lungeTarget = mount.GetLungeTarget();
		if (mount == nullptr || lungeTarget == nullptr)
		{
			rInfo.weaponFired = false;
			return;
		}

		lungeStartLoc = mount.GetActorLocation();

		FNCVehicleLerpToTargetParams lerpParams;
		lerpParams.DurationSeconds = 0.25f;
		lerpParams.TargetActor = mount.GetLungeTarget();
		lerpParams.EasingFunc = EEasingFunc::Linear;
		FNCOnLerpFinished onLerpFinished(this, n"HandleLerpFinished");
		mount.StartLerpToTarget(lerpParams, onLerpFinished);
	}

	UFUNCTION()
	private void HandleLerpFinished(AActor targetActor)
	{
		ANCPlayerCharacter targetPlayer = Cast<ANCPlayerCharacter>(targetActor);
		if (targetPlayer != nullptr && ownerPlayer != nullptr)
		{
			FVector lungeDir = (ownerPlayer.GetActorLocation() - lungeStartLoc).GetSafeNormal2D();
			if (IsServer())
			{
				if (!ANCPlayerCharacter::UsePredictedMountInput())
				{
					Mount::Dismount(ownerPlayer);
				}
				Mount::Dismount(targetPlayer);
				targetPlayer.BouncePlayer((FVector::UpVector + lungeDir) * lungeHitBounceStrength);
			}

			if (ANCPlayerCharacter::UsePredictedMountInput())
			{
				Mount::Dismount(ownerPlayer);
			}
			ownerPlayer.BouncePlayer((FVector::UpVector - lungeDir) * lungeHitBounceStrength);
		}
	}
}

struct FUltimateVFXData
{
	UNiagaraComponent eyeGlowLeft;
	UNiagaraComponent eyeGlowRight;
}

UCLASS( Abstract )
class UAS_Passive_Bloodseeker : UAS_PassiveBaseClass
{
	default passiveTag = GameplayTags::Classes_Passives_BloodThirst;

	UPROPERTY(EditDefaultsOnly)
	UNCAudioAsset bloodSeekerActivate1P;
	UPROPERTY(EditDefaultsOnly)
	UNCAudioAsset bloodSeekerSustain1P;
	UPROPERTY(EditDefaultsOnly)
	UNCAudioAsset bloodSeekerHeartbeatSlow1P;
	UPROPERTY(EditDefaultsOnly)
	UNCAudioAsset bloodSeekerHeartbeatMedium1P;
	UPROPERTY(EditDefaultsOnly)
	UNCAudioAsset bloodSeekerHeartbeatFast1P;

	UPROPERTY(EditDefaultsOnly)
	UNCAudioAsset bloodSeekerVictim1P;

	UPROPERTY( EditDefaultsOnly )
	const UAkRtpc RTPC_Heartbeat;

	UPROPERTY()
	UNiagaraSystem heartBeatVFX;

	UPROPERTY()
	UNiagaraSystem fearVictimVFX;
	UPROPERTY()
	UNiagaraSystem fearVictimTrailVFX;
	UPROPERTY()
	UNiagaraSystem fearVictimHitVFX;
	UPROPERTY(EditDefaultsOnly)
	UNCAudioAsset HuntersMarkActivateSound1P;
	UPROPERTY(EditDefaultsOnly)
	UNCAudioAsset HuntersMarkActivateSoundFriendly3P;
	UPROPERTY(EditDefaultsOnly)
	UNCAudioAsset HuntersMarkActivateSoundEnemy3P;

	UPROPERTY(EditDefaultsOnly)
	UNCAudioAsset HuntersMarkAppliedSound1P;
	UPROPERTY(EditDefaultsOnly)
	UNCAudioAsset HuntersMarkAppliedSound3P;

	FNCCoroutineSignal endBloodseekerSignal;

	UAS_SphereTrigger sphereTrigger;
	TArray<AAS_PlayerEntity> enemyPlayersInRange;
	TArray<AAS_PlayerEntity> woundedPlayersInRange;

	bool detectingHeartbeat = false;
	float32 mostWoundedPlayerHealthFrac = 0.5; //Used for visuals
	FAudioResultData heartBeatSoundLoop;

	float heartbeatDetectionRadius = 10000;

	TArray<AAS_PlayerEntity> dreadPlayers;

	FScriptDelegateHandle huntModeStartedCallback;
	FScriptDelegateHandle huntModeEndedCallback;

	TMap<ANCPlayerCharacter,FWolfFormData> trailVFXMap;

	UPROPERTY( EditDefaultsOnly )
	FTPPCameraSettings wolfCameraSettings;
	default wolfCameraSettings.Set( ETPPCameraSetting::FOV, 90 );
	default wolfCameraSettings.Set( ETPPCameraSetting::TargetArmLength, 450 );
	default wolfCameraSettings.Set( ETPPCameraSetting::CameraOffsetVector, FVector( 0.f, 100.0f, 0.f ) );

	UPROPERTY()
	int32 appliedCameraOverrideID = -1;

	TMap<AAS_PlayerEntity,int> nextDreadPulseTimes; 
	FScriptDelegateHandle switchHandle1;
	FScriptDelegateHandle switchHandle2;
	FScriptDelegateHandle primaryHandle1;
	FScriptDelegateHandle primaryHandle2;

	UPROPERTY()
	UNiagaraSystem eyeGlowHuman;

	UPROPERTY()
	UNiagaraSystem eyeGlowWolf;

	TMap<ANCPlayerCharacter,FUltimateVFXData> ultVFXMap;

	void Server_OnPassiveStarted( ANCPlayerCharacter player ) override
	{
		Super::Server_OnPassiveStarted( player );
		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>(player);
		CreateSphereTrigger( asPlayer );
		Thread( this, n"SV_BloodSeekerLogic", asPlayer );
        ScriptCallbacks().server_onMountStateChanged.AddUFunction( this, n"OnMountStateChanged" );
		player.GiveWeaponAtSlot( n"Weap_WolfBite", WeaponSlot::AbilityWeaponSwapSlot, GameConst::DEFAULT_GIVE_WEAPON_SETTINGS );
		asPlayer.mountData.ResetHealth();
		FWolfFormData data;
		trailVFXMap.Add( player, data );
	}

	void Server_OnPassiveEnded( ANCPlayerCharacter player ) override
	{
		Super::Server_OnPassiveEnded( player );

		if ( IsValid( sphereTrigger ) )
		{
			sphereTrigger.DestroyComponent(sphereTrigger);
		}
		ScriptCallbacks().server_onMountStateChanged.UnbindObject(this);
		player.TakeWeaponAtSlot( WeaponSlot::AbilityWeaponSwapSlot );
		trailVFXMap.Remove(player);
	}

	UFUNCTION()
	private void OnMountStateChanged(ANCPlayerCharacter player, AAS_VehicleMount mount, bool isRiding)
	{
		bool isEkon = player.ClassManager().GetClassIndex() == GameplayTags::Classes_Class_Ekon;
		if ( !isEkon )
			return;

		if ( isRiding )
		{
			FWolfFormData& data = trailVFXMap[player];
			player.GetPlayerMesh3P().SetHiddenInGame( true);
			mount.GetVehicleMesh3P().SetWorldScale3D(FVector(1.5,1.5,1.5));
			player.TrySwapToWeaponAtSlot(WeaponSlot::AbilityWeaponSwapSlot);
			player.ServerDisableWeaponSlot( WeaponSlot::PrimarySlot0, n"Wolf0" );
			player.ServerDisableWeaponSlot( WeaponSlot::PrimarySlot1, n"Wolf1" );
			player.ServerDisableWeaponSlot( WeaponSlot::RaidToolsSlot, n"Wolf2" );
			player.ServerDisableWeaponSlotsByType( EWeaponDisableSlotType::DisableSlotType_MeleeWeapons, n"Wolf3" );
			player.ServerDisableWeaponSlot( WeaponSlot::GrenadeSlot, n"Wolf4" );
			switchHandle1	= player.AddButtonPressedCallback( n"SwitchWeapon", this, n"DismountOnWeaponSwap" );
			switchHandle2	= player.AddButtonPressedCallback( n"AltUseSwitchWeapon", this, n"DismountOnWeaponSwap" );
			primaryHandle1	= player.AddButtonPressedCallback( n"PrimaryWeapon0", this, n"DismountOnWeaponSwap" );
			primaryHandle2	= player.AddButtonPressedCallback( n"PrimaryWeapon1", this, n"DismountOnWeaponSwap" );

			bool hasHuntMode = player.GetStatusEffectValue( GameplayTags::StatusEffect_HuntMode ) == 1.0;
			if ( hasHuntMode )
			{
				if ( !mount.HasMod(n"ultimate_howl") )
					mount.AddMod(n"ultimate_howl");
			}
			else
			{
				if ( mount.HasMod(n"ultimate_howl") )
					mount.RemoveMod(n"ultimate_howl");
			}

			if ( data.trailVFXs.Num() > 0 )
			{
				for( AFXActor afxAct : data.trailVFXs )
					afxAct.Destroy();
				data.trailVFXs.Empty();
			}
			TArray<ANCPlayerCharacter> enemies = GetPlayersOfTeam( GetOtherTeam( player.GetTeam() ) );
			for( ANCPlayerCharacter enemy : enemies )
			{
				AFXActor fearVictimTrailFX;
				fearVictimTrailFX = Server_SpawnEffectOnEntity_Looping_WithFlags( Passive_Bloodseeker().fearVictimTrailVFX, enemy, player, ESendEventFlags::SEND_TO_OWNER,0);//FVector(0,0,-100) );
				fearVictimTrailFX.AttachToComponent(enemy.GetPlayerMesh3P(), n"spine_chest", EAttachmentRule::SnapToTarget);
				data.trailVFXs.Add( fearVictimTrailFX );
			}

			mount.EnableLungeTargetTracking(true);
		}
		if ( !isRiding && !player.WeaponSlotIsEnabled( WeaponSlot::PrimarySlot0 ) )
		{
			player.GetPlayerMesh3P().SetHiddenInGame( false );
			FWolfFormData& data = trailVFXMap[player];
			//player.ServerEnableWeaponSlotsByType( EWeaponDisableSlotType::DisableSlotType_OffhandWeapons, n"Debug4" );
			player.ServerEnableWeaponSlot( WeaponSlot::PrimarySlot0, n"Wolf0" );
			player.ServerEnableWeaponSlot( WeaponSlot::PrimarySlot1, n"Wolf1" );
			player.ServerEnableWeaponSlot( WeaponSlot::RaidToolsSlot, n"Wolf2" );
			player.ServerEnableWeaponSlotsByType( EWeaponDisableSlotType::DisableSlotType_MeleeWeapons, n"Wolf3" );
			player.ServerEnableWeaponSlot( WeaponSlot::GrenadeSlot, n"Wolf4" );
			Server_SwitchToLastWeapon( player );

			if ( player.ButtonPressedCallback_DoesExist( n"SwitchWeapon", switchHandle1) )
				player.RemoveButtonPressedCallback( n"SwitchWeapon", switchHandle1 );
			if ( player.ButtonPressedCallback_DoesExist( n"AltUseSwitchWeapon", switchHandle2) )
				player.RemoveButtonPressedCallback( n"AltUseSwitchWeapon", switchHandle2 );
			if ( player.ButtonPressedCallback_DoesExist( n"PrimaryWeapon0", primaryHandle1) )
				player.RemoveButtonPressedCallback( n"PrimaryWeapon0", primaryHandle1 );
			if ( player.ButtonPressedCallback_DoesExist( n"PrimaryWeapon1", primaryHandle2) )
				player.RemoveButtonPressedCallback( n"PrimaryWeapon1", primaryHandle2 );

			if ( data.trailVFXs.Num() > 0 )
			{
				for( AFXActor afxAct : data.trailVFXs )
					afxAct.Destroy();
				data.trailVFXs.Empty();
			}

			mount.EnableLungeTargetTracking(false);
		}
	}

	UFUNCTION()
	private void DismountOnWeaponSwap( ANCPlayerCharacter player )
	{
		if ( player.IsPlayerRidingMount() )
			Mount::Dismount(player);
	}

	void Client_OnPassiveStarted( ANCPlayerCharacter player ) override
	{
		Super::Client_OnPassiveStarted( player );

		huntModeStartedCallback = player.AddStatusEffectHasBegunCallback( GameplayTags::StatusEffect_HuntMode, this, n"OnHuntModeStarted" );
		huntModeEndedCallback		= player.AddStatusEffectHasEndedCallback( GameplayTags::StatusEffect_HuntMode, this, n"OnHuntModeEnded" );

		if ( !player.IsLocallyControlled() )
			return;
	
		Thread( this, n"CL_BloodSeekerLogic", player );
	}

	void Client_OnPassiveEnded( ANCPlayerCharacter player ) override
	{
		Super::Client_OnPassiveEnded( player );

		if ( !player.IsLocallyControlled() )
			return;

		endBloodseekerSignal.Emit();
		player.RemoveStatusEffectHasBegunCallback( GameplayTags::StatusEffect_HuntMode, huntModeStartedCallback );
		player.RemoveStatusEffectHasBegunCallback( GameplayTags::StatusEffect_HuntMode, huntModeEndedCallback );
	}

	UFUNCTION()
	void SV_BloodSeekerLogic( UNCCoroutine co, AAS_PlayerEntity hunter )
	{
		co.EndOnDestroyed( hunter );
		//co.EndOn(player, asPlayer.OnDeathSignal);
		//co.EndOn(player, asPlayer.bleedoutComponent.onBleedoutStartedSignal);
		co.EndOn( this, this.endBloodseekerSignal );
		while( true )
		{
			if ( hunter.IsAlive() )
			{
				bool inHuntMode = hunter.GetStatusEffectValue( GameplayTags::StatusEffect_HuntMode ) == 1.0;
				if ( inHuntMode )
				{
					TArray<AAS_PlayerEntity> allPlayers = GetAllPlayers();
					int hunterTeam = hunter.GetTeam();
					for( AAS_PlayerEntity enemyPlayer : allPlayers )
					{
						if ( !IsAlive(enemyPlayer ) )
							continue;
						
						if ( enemyPlayer.GetTeam() == hunterTeam )
							continue;

						if ( woundedPlayersInRange.Contains( enemyPlayer ) )
							continue;

						UAS_SV_BloodseekerHeartbeatThread heartBeatThread = Cast<UAS_SV_BloodseekerHeartbeatThread>( CreateThread( UAS_SV_BloodseekerHeartbeatThread::StaticClass(), this ) );
						heartBeatThread.Init( enemyPlayer, hunter );
						woundedPlayersInRange.Add( enemyPlayer );
					}
				}
				else
				{
					for( AAS_PlayerEntity enemyPlayer : enemyPlayersInRange )
					{
						if ( woundedPlayersInRange.Contains( enemyPlayer ) )
							continue;

						float32 healthFrac = enemyPlayer.GetHealth() / enemyPlayer.GetMaxHealth();
						if ( healthFrac < 1 )
						{
							UAS_SV_BloodseekerHeartbeatThread heartBeatThread = Cast<UAS_SV_BloodseekerHeartbeatThread>( CreateThread( UAS_SV_BloodseekerHeartbeatThread::StaticClass(), this ) );
							heartBeatThread.Init( enemyPlayer, hunter );
							woundedPlayersInRange.Add( enemyPlayer );
						}
					}
				}
			}

			co.Wait(0.1);
		}
	}

	//Remove thread and update detecting heartbeat to be a count.
	UFUNCTION()
	void CL_BloodSeekerLogic( UNCCoroutine co, ANCPlayerCharacter player )
	{
		co.EndOnDestroyed( player );
		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>(player);
		//co.EndOn(player, asPlayer.OnDeathSignal);
		//co.EndOn(player, asPlayer.bleedoutComponent.onBleedoutStartedSignal);
		co.EndOn( this, this.endBloodseekerSignal );
		while( true )
		{
			if ( dreadPlayers.Num() > 0 )
			{
				/*
				float32 newLowestHealthFrac = 0.3;
				for( AAS_PlayerEntity dreadPlayer : dreadPlayers )
				{
					float32 healthFrac = dreadPlayer.GetHealth() / dreadPlayer.GetMaxHealth();
					if( healthFrac < newLowestHealthFrac )
						newLowestHealthFrac = healthFrac;
				}
				mostWoundedPlayerHealthFrac = newLowestHealthFrac;
				float32 inverseFrac = 1 - mostWoundedPlayerHealthFrac + 0.5;
				asPlayer.PlayerCameraComponent.PostProcessSettings.bOverride_SceneColorTint = true;
				asPlayer.PlayerCameraComponent.PostProcessSettings.SceneColorTint = FLinearColor::White + FLinearColor(0.97, 0.19, 0.09) * inverseFrac;
				*/
			}
			else
			{
				//asPlayer.PlayerCameraComponent.PostProcessSettings.bOverride_SceneColorTint = false;
				detectingHeartbeat = false;
			}

			co.Wait(0.1);
		}
	}

	void CreateSphereTrigger( ANCPlayerCharacter player)
	{
		sphereTrigger = Cast<UAS_SphereTrigger>( player.CreateComponent( UAS_SphereTrigger::StaticClass() ) );
		sphereTrigger.SetRadius( heartbeatDetectionRadius );
		sphereTrigger.onPlayerEntered.AddUFunction( this, n"OnProximityTriggerPlayerEntered" );
		sphereTrigger.onPlayerExited.AddUFunction( this, n"OnProximityTriggerPlayerExited" );

		TArray<AActor> overlappingActors;
		sphereTrigger.GetOverlappingActors( overlappingActors, AAS_PlayerEntity::StaticClass() );
		for ( AActor overlappingActor : overlappingActors )
		{
			AAS_PlayerEntity overlappingPlayer = Cast<AAS_PlayerEntity>( overlappingActor );
			if ( IsValid( overlappingPlayer ) )
				OnProximityTriggerPlayerEntered( overlappingPlayer, sphereTrigger );
		}
	}

	UFUNCTION()
	void OnProximityTriggerPlayerEntered( AAS_PlayerEntity newPlayer, UAS_SphereTrigger trigger )
	{
		if ( trigger.GetOwnerPlayer().GetTeam() == newPlayer.GetTeam() )
			return;

		enemyPlayersInRange.AddUnique(newPlayer);
	}

	UFUNCTION()
	void OnProximityTriggerPlayerExited( AAS_PlayerEntity leavingPlayer, UAS_SphereTrigger trigger )
	{
		enemyPlayersInRange.Remove( leavingPlayer );
	}

	UFUNCTION()
	void OnHuntModeStarted( ANCPlayerCharacter p )
	{
		if ( IsClient() )
		{
			if ( p.IsLocallyControlled() )
			{
				CreateScreenEffect( p );
				AAS_PlayerEntity asPawn = Cast<AAS_PlayerEntity>( p );
				asPawn.overrideGoalFOV.AddUFunction( this, n"HuntModeFOVOverride" );

				UCameraComponent Camera = p.GetPlayerCameraComponent();
				Camera.PostProcessSettings.bOverride_VignetteIntensity = true;
				Camera.PostProcessSettings.VignetteIntensity = 0.5;
			}
			else
			{
				if ( !ultVFXMap.Contains(p) )
				{
					FUltimateVFXData data;
					ultVFXMap.Add(p,data);
				}
				AAS_VehicleMount mount				  = Mount::GetPilotedMount( p );
				bool isOnMount				 = IsValid( mount );
				if ( isOnMount )
				{
					ultVFXMap[p].eyeGlowLeft = Client_SpawnEffectOnEntity_Looping( eyeGlowWolf, mount, n"mount_Lf_eye_socket" );
					ultVFXMap[p].eyeGlowLeft.AttachToComponent(mount.GetVehicleMesh3P(), n"mount_Lf_eye_socket", EAttachmentRule::SnapToTarget);
					ultVFXMap[p].eyeGlowRight = Client_SpawnEffectOnEntity_Looping( eyeGlowWolf, mount, n"mount_Rt_eye_socket" );
					ultVFXMap[p].eyeGlowRight.AttachToComponent(mount.GetVehicleMesh3P(), n"mount_Rt_eye_socket", EAttachmentRule::SnapToTarget);
				}
				else
				{
					ultVFXMap[p].eyeGlowLeft = Client_SpawnEffectOnEntity_Looping( eyeGlowHuman, p,  n"Lf_eye" );
					ultVFXMap[p].eyeGlowLeft.AttachToComponent(p.GetPlayerMesh3P(), n"Lf_eye", EAttachmentRule::SnapToTarget);
					ultVFXMap[p].eyeGlowRight = Client_SpawnEffectOnEntity_Looping( eyeGlowHuman, p,  n"Rt_eye" );
					ultVFXMap[p].eyeGlowRight.AttachToComponent(p.GetPlayerMesh3P(), n"Rt_eye", EAttachmentRule::SnapToTarget);
				}
			}
		}
	}

	UFUNCTION()
	void OnHuntModeEnded( ANCPlayerCharacter p )
	{
		if ( IsClient() )
		{
			if ( p.IsLocallyControlled() )
			{
				TeardownScreenEffect( p );
				AAS_PlayerEntity asPawn = Cast<AAS_PlayerEntity>( p );
				asPawn.overrideGoalFOV.Unbind( this, n"HuntModeFOVOverride" );

				UCameraComponent Camera = p.GetPlayerCameraComponent();
				Camera.PostProcessSettings.bOverride_VignetteIntensity = false;
				Camera.PostProcessSettings.VignetteIntensity = 1.0;
			}
			else
			{
				if ( ultVFXMap.Contains(p) )
				{
					if ( IsValid( ultVFXMap[p].eyeGlowLeft ) )
						ultVFXMap[p].eyeGlowLeft.DestroyComponent(ultVFXMap[p].eyeGlowLeft);
					if ( IsValid( ultVFXMap[p].eyeGlowRight ) )
						ultVFXMap[p].eyeGlowRight.DestroyComponent(ultVFXMap[p].eyeGlowRight);
				}
			}
		}
	}

	void SwapVFXToMount( AAS_PlayerEntity p, AAS_VehicleMount mount )
	{
		bool inHuntMode = p.GetStatusEffectValue( GameplayTags::StatusEffect_HuntMode ) > 0;
		if ( !inHuntMode )
			return;

		if ( ultVFXMap.Contains(p) )
		{
			if ( IsValid( ultVFXMap[p].eyeGlowLeft ) )
				ultVFXMap[p].eyeGlowLeft.DestroyComponent(ultVFXMap[p].eyeGlowLeft);
			if ( IsValid( ultVFXMap[p].eyeGlowRight ) )
				ultVFXMap[p].eyeGlowRight.DestroyComponent(ultVFXMap[p].eyeGlowRight);

			ultVFXMap[p].eyeGlowLeft = Client_SpawnEffectOnEntity_Looping( eyeGlowWolf, mount, n"mount_Lf_eye_socket" );
			ultVFXMap[p].eyeGlowLeft.AttachToComponent(mount.GetVehicleMesh3P(), n"mount_Lf_eye_socket", EAttachmentRule::SnapToTarget);
			ultVFXMap[p].eyeGlowRight = Client_SpawnEffectOnEntity_Looping( eyeGlowWolf, mount, n"mount_Rt_eye_socket" );
			ultVFXMap[p].eyeGlowRight.AttachToComponent(mount.GetVehicleMesh3P(), n"mount_Rt_eye_socket", EAttachmentRule::SnapToTarget);
		}
	}

	void SwapVFXToPlayer( AAS_PlayerEntity p )
	{
		bool inHuntMode = p.GetStatusEffectValue( GameplayTags::StatusEffect_HuntMode ) > 0;
		if ( !inHuntMode )
			return;

		if ( ultVFXMap.Contains(p) )
		{
			if ( IsValid( ultVFXMap[p].eyeGlowLeft ) )
				ultVFXMap[p].eyeGlowLeft.DestroyComponent(ultVFXMap[p].eyeGlowLeft);
			if ( IsValid( ultVFXMap[p].eyeGlowRight ) )
				ultVFXMap[p].eyeGlowRight.DestroyComponent(ultVFXMap[p].eyeGlowRight);

			ultVFXMap[p].eyeGlowLeft = Client_SpawnEffectOnEntity_Looping( eyeGlowHuman, p,  n"Lf_eye" );
			ultVFXMap[p].eyeGlowLeft.AttachToComponent(p.GetPlayerMesh3P(), n"Lf_eye", EAttachmentRule::SnapToTarget);
			ultVFXMap[p].eyeGlowRight = Client_SpawnEffectOnEntity_Looping( eyeGlowHuman, p,  n"Rt_eye" );
			ultVFXMap[p].eyeGlowRight.AttachToComponent(p.GetPlayerMesh3P(), n"Rt_eye", EAttachmentRule::SnapToTarget);
		}
	}

	UFUNCTION()
	void HuntModeFOVOverride( float& goal )
	{
		goal = goal + Math::SinusoidalInOut( 0, 15, (float(GetGameTimeMS()) % 2000 ) / 2000.0f );
	}

	int handle = -1;

	UFUNCTION()
	void CreateScreenEffect( ANCPlayerCharacter p )
	{
		if ( !p.IsLocallyControlled() )
			return;
		TeardownScreenEffect( p );
		UAS_ScreenEffect_Redmane screenEffect = GetScreenEffect_Redmane();
		if ( !IsValid( screenEffect ) )
			return;

		FScreenStatusIndicatorData newStatusIndicatorData;
		newStatusIndicatorData.fullscreenAmount		   = 0.5;
		newStatusIndicatorData.fullscreenDepth		   = 0.15; //This setting feels different on Ultrawides
		newStatusIndicatorData.fullscreenGlowIntensity = 0.9;

		newStatusIndicatorData.baseColor			 = FLinearColor(0.97, 0.88, 0.08);
		newStatusIndicatorData.glowColor			 = FLinearColor::White;
		newStatusIndicatorData.modulateTextureAmount = 0.4f;

		newStatusIndicatorData.modulateScaleX = 2.0f;
		newStatusIndicatorData.modulateScaleY = 0.192f;
		newStatusIndicatorData.modulateSpeedX = 0;
		newStatusIndicatorData.modulateSpeedY = -0.12;

		handle = screenEffect.RegisterStatusIndicator( newStatusIndicatorData );
	}

	UFUNCTION()
	void TeardownScreenEffect( ANCPlayerCharacter p )
	{
		UAS_ScreenEffect_Redmane screenEffect = GetScreenEffect_Redmane();
		if ( !IsValid( screenEffect ) )
			return;
		if ( !screenEffect.IsHandleValid( handle ) )
			return;

		FScreenStatusIndicatorData& registeredData = screenEffect.GetStatusIndicatorData( handle );
		float curTime							   = GetGameTimeMS();
		float endTime							   = curTime + TO_MILLISECONDS( 1.5f );
		registeredData.LerpDepth( 0.0, curTime, endTime, true );
	}
}

class UAS_SV_DreadPulse : UAS_Thread
{
	AAS_PlayerEntity enemyPlayer;
	bool setHighlight = false;
	//Passive_Bloodseeker()
	void Init( AAS_PlayerEntity _enemyPlayer )
	{
		enemyPlayer = _enemyPlayer;
		Start();
	}

	void OnThreadStart( UNCCoroutine co ) override
	{
		Super::OnThreadStart( co );

		co.EndOnDestroyed( enemyPlayer );
		co.EndOn(enemyPlayer, enemyPlayer.OnDeathSignal);
		co.EndOn(enemyPlayer, enemyPlayer.totemComponent.onTotemStartedSignal);

		if ( !IsAlive( enemyPlayer ) )
			return;

		float32 duration = 0.5;
		UAS_Passive_Bloodseeker passive = Passive_Bloodseeker();
		if ( passive.nextDreadPulseTimes.Contains( enemyPlayer ) )
		{
			int gameTime = GetGameTimeMS();
			if ( passive.nextDreadPulseTimes[enemyPlayer] > GetGameTimeMS() )
				return;
			else
				passive.nextDreadPulseTimes[enemyPlayer] = GetGameTimeMS() + 2500;
		}
		else
		{
			passive.nextDreadPulseTimes.Add(enemyPlayer, GetGameTimeMS() + 2500);
		}

		int otherTeam = GetOtherTeam( enemyPlayer.GetTeam() ) ;
		enemyPlayer.AddStatusEffect(GameplayTags::StatusEffect_DreadPulse, 1.0, duration,0,0);
		enemyPlayer.highlightManager.AddIsHighlightedForTeam( otherTeam);
		setHighlight = true;

		co.Wait(duration);
	}

	void OnThreadEnd( FNCCoroutineEndParams params ) override
	{
		Super::OnThreadEnd( params );

		if ( setHighlight && IsValid( enemyPlayer ) )
		{
			int otherTeam = GetOtherTeam( enemyPlayer.GetTeam() ) ;
			enemyPlayer.highlightManager.RemoveIsHighlightedForTeam( otherTeam );
		}
	}
}

//For the passive, which has conditions to remove itself.
class UAS_SV_BloodseekerHeartbeatThread : UAS_Thread
{
	AAS_PlayerEntity hunterPlayer;
	AAS_PlayerEntity enemyPlayer;
	UAS_Passive_Bloodseeker passive;
	int enemyStatusEffectIndex;
	//int hunterStatusEffectIndex;
	AFXActor fearVictimFX;
	//Passive_Bloodseeker()
	void Init( AAS_PlayerEntity _enemyPlayer, AAS_PlayerEntity _hunterPlayer )
	{
		enemyPlayer = _enemyPlayer;
		hunterPlayer = _hunterPlayer;
		passive = Passive_Bloodseeker();
		Start();
	}

	void OnThreadStart( UNCCoroutine co ) override
	{
		Super::OnThreadStart( co );
		co.EndOnDestroyed( hunterPlayer );
		co.EndOn(hunterPlayer, hunterPlayer.OnDeathSignal);
		co.EndOn(hunterPlayer, hunterPlayer.totemComponent.onTotemStartedSignal);
		co.EndOnDestroyed( enemyPlayer );
		co.EndOn(enemyPlayer, enemyPlayer.OnDeathSignal);
		co.EndOn(enemyPlayer, enemyPlayer.totemComponent.onTotemStartedSignal);

		enemyStatusEffectIndex = enemyPlayer.AddStatusEffect(GameplayTags::StatusEffect_Dread, 1.0, 100000,0,0);

		//hunterStatusEffectIndex = hunterPlayer.AddStatusEffect( GameplayTags::StatusEffect_MoveFast, 0.15, 100000, 0.0, 0 );

		fearVictimFX = Server_SpawnEffectOnEntity_Looping_WithFlags( Passive_Bloodseeker().fearVictimVFX, enemyPlayer, hunterPlayer,ESendEventFlags::SEND_TO_OWNER,0,FVector(0,0,-100) );
		UAS_SV_DreadPulse dreadPulseThread = Cast<UAS_SV_DreadPulse>( CreateThread( UAS_SV_DreadPulse::StaticClass(), this ) );
		dreadPulseThread.Init( enemyPlayer );
		float nextPulseTime = GetGameTimeMS() + 2500;
		bool inHuntMode = hunterPlayer.GetStatusEffectValue( GameplayTags::StatusEffect_HuntMode ) > 0;
		float healthFrac = enemyPlayer.GetHealth() / enemyPlayer.GetMaxHealth();
		while( inHuntMode || ( healthFrac < 1.0 && passive.enemyPlayersInRange.Contains( enemyPlayer ) ) )
		{
			co.Wait(0.1);
			healthFrac = enemyPlayer.GetHealth() / enemyPlayer.GetMaxHealth();
			inHuntMode = hunterPlayer.GetStatusEffectValue( GameplayTags::StatusEffect_HuntMode ) > 0;
			float currentTime = GetGameTimeMS();
			if ( nextPulseTime < currentTime )
			{
				dreadPulseThread = Cast<UAS_SV_DreadPulse>( CreateThread( UAS_SV_DreadPulse::StaticClass(), this ) );
				dreadPulseThread.Init( enemyPlayer );
				nextPulseTime = currentTime + 2500;
			}
		}
	}

	void OnThreadEnd( FNCCoroutineEndParams params ) override
	{
		Super::OnThreadEnd( params );
		enemyPlayer.ClearStatusEffect( enemyStatusEffectIndex );
		//hunterPlayer.ClearStatusEffect( hunterStatusEffectIndex );
		passive.woundedPlayersInRange.Remove( enemyPlayer );
		if ( IsValid( fearVictimFX ) )
			fearVictimFX.Destroy();
	}
}

//For the tactical and the ultimate, which just apply a simple duration.
class UAS_SV_HeartbeatThread : UAS_Thread
{
	AAS_PlayerEntity hunterPlayer;
	AAS_PlayerEntity enemyPlayer;
	float duration;
	int enemyStatusEffectIndex;
	//int hunterStatusEffectIndex;
	AFXActor fearTrailVFX;
	AFXActor fearVFX;

	//Passive_Bloodseeker()
	void Init( AAS_PlayerEntity _enemyPlayer, AAS_PlayerEntity _hunterPlayer, float _duration )
	{
		enemyPlayer = _enemyPlayer;
		hunterPlayer = _hunterPlayer;
		duration = _duration;
		Start();
	}

	void OnThreadStart( UNCCoroutine co ) override
	{
		Super::OnThreadStart( co );
		co.EndOnDestroyed( hunterPlayer );
		co.EndOn(hunterPlayer, hunterPlayer.OnDeathSignal);
		co.EndOn(hunterPlayer, hunterPlayer.totemComponent.onTotemStartedSignal);
		co.EndOnDestroyed( enemyPlayer );
		co.EndOn(enemyPlayer, enemyPlayer.OnDeathSignal);
		co.EndOn(enemyPlayer, enemyPlayer.totemComponent.onTotemStartedSignal);

		enemyStatusEffectIndex = enemyPlayer.AddStatusEffect(GameplayTags::StatusEffect_Dread, 1.0, 100000,0,0);
		//hunterStatusEffectIndex = hunterPlayer.AddStatusEffect( GameplayTags::StatusEffect_MoveFast, 0.25, 100000, 0.0, 0 );
		UAS_Passive_Bloodseeker passive = Passive_Bloodseeker();
		fearTrailVFX = Server_SpawnEffectOnEntity_Looping_WithFlags( passive.fearVictimTrailVFX, enemyPlayer, hunterPlayer,ESendEventFlags::SEND_TO_OWNER,0);//FVector(0,0,-100) );
		fearTrailVFX.AttachToComponent(enemyPlayer.GetPlayerMesh3P(), n"spine_chest", EAttachmentRule::SnapToTarget);
		fearVFX = Server_SpawnEffectOnEntity_Looping_WithFlags( passive.fearVictimVFX, enemyPlayer, hunterPlayer,ESendEventFlags::SEND_TO_OWNER,0,FVector(0,0,-100) );
		UAS_SV_DreadPulse dreadPulseThread = Cast<UAS_SV_DreadPulse>( CreateThread( UAS_SV_DreadPulse::StaticClass(), this ) );
		dreadPulseThread.Init( enemyPlayer );
		float currentTime = GetGameTimeMS();
		float nextPulseTime = currentTime + 2500;

		float endTime = currentTime + TO_MILLISECONDS( float32( duration ) );
		while ( GetGameTimeMS() < endTime )
		{
			if ( nextPulseTime < currentTime )
			{
				dreadPulseThread = Cast<UAS_SV_DreadPulse>( CreateThread( UAS_SV_DreadPulse::StaticClass(), this ) );
				dreadPulseThread.Init( enemyPlayer );
				nextPulseTime = currentTime + 2500;
			}
			co.Wait( 0.1 );
			currentTime = GetGameTimeMS();
		}
	}

	void OnThreadEnd( FNCCoroutineEndParams params ) override
	{
		Super::OnThreadEnd( params );
		enemyPlayer.ClearStatusEffect( enemyStatusEffectIndex );
		//hunterPlayer.ClearStatusEffect( hunterStatusEffectIndex );
		if ( IsValid( fearVFX ) )
			fearVFX.Destroy();
		if ( IsValid( fearTrailVFX ) )
			fearTrailVFX.Destroy();
	}
}

class UAS_HeartbeatThread : UAS_Thread
{
	AAS_PlayerEntity enemyPlayer;
	UAS_Passive_Bloodseeker passive;
	//Passive_Bloodseeker()
	void Init( AAS_PlayerEntity _enemyPlayer )
	{
		enemyPlayer = _enemyPlayer;
		passive = Passive_Bloodseeker();
		Start();
	}

	void OnThreadStart( UNCCoroutine co ) override
	{
		Super::OnThreadStart( co );
		co.EndOnDestroyed( enemyPlayer );
		co.EndOn(enemyPlayer, enemyPlayer.OnDeathSignal);
		co.EndOn(enemyPlayer, enemyPlayer.totemComponent.onTotemStartedSignal);
		ANCPlayerCharacter localPlayer = Client_GetLocalPawn();
		float32 maxHealth = enemyPlayer.GetMaxHealth();
		if ( !passive.detectingHeartbeat )
		{
			Client_EmitSoundOnEntity( passive.bloodSeekerActivate1P, localPlayer );
			passive.heartBeatSoundLoop = Client_EmitSoundOnEntity( passive.bloodSeekerSustain1P, localPlayer );
		}

		passive.detectingHeartbeat = true;
		int count = 0;

		while( true )
		{
			float32 currentHealth = enemyPlayer.GetHealth();
			float32 healthFrac = currentHealth / maxHealth;
			if ( healthFrac < passive.mostWoundedPlayerHealthFrac )
				passive.mostWoundedPlayerHealthFrac = healthFrac;

			if ( healthFrac > 0.5 )
			{
				if ( count == 0 )
				{
					Client_EmitSoundOnEntity( passive.bloodSeekerHeartbeatSlow1P, enemyPlayer );//RTPC_Character_Hunter_Passive_Enemy_Health
					Client_SetRTPCValue(enemyPlayer, passive.RTPC_Heartbeat, currentHealth, 0 );
					//FVector offset = enemyPlayer.Mesh.GetSocketLocation(n"Lf_chest") - enemyPlayer.GetActorLocation();
					UNiagaraComponent heartbeatComp = Client_SpawnEffectOnEntity_OneShot( passive.heartBeatVFX, enemyPlayer, n"spine_chest" );//, offset );
					heartbeatComp.AttachToComponent(enemyPlayer.GetPlayerMesh3P(), n"spine_chest", EAttachmentRule::SnapToTarget);
				}
			}
			else if ( healthFrac > 0.3 )
			{
				if ( count == 0 || count == 7 )
				{
					Client_EmitSoundOnEntity( passive.bloodSeekerHeartbeatMedium1P, enemyPlayer );
					Client_SetRTPCValue(enemyPlayer, passive.RTPC_Heartbeat, currentHealth, 0 );
					//FVector offset = enemyPlayer.Mesh.GetSocketLocation(n"Lf_chest") - enemyPlayer.GetActorLocation();
					UNiagaraComponent heartbeatComp = Client_SpawnEffectOnEntity_OneShot( passive.heartBeatVFX, enemyPlayer, n"spine_chest");//, offset );
					heartbeatComp.AttachToComponent(enemyPlayer.GetPlayerMesh3P(), n"spine_chest", EAttachmentRule::SnapToTarget);
				}
			}
			else
			{
				if ( count == 0 || count == 5 || count == 10 )
				{
					Client_EmitSoundOnEntity( passive.bloodSeekerHeartbeatFast1P, enemyPlayer );
					Client_SetRTPCValue(enemyPlayer, passive.RTPC_Heartbeat, currentHealth, 0 );
					//FVector offset = enemyPlayer.Mesh.GetSocketLocation(n"Lf_chest") - enemyPlayer.GetActorLocation();
					UNiagaraComponent heartbeatComp = Client_SpawnEffectOnEntity_OneShot( passive.heartBeatVFX, enemyPlayer, n"spine_chest");//, offset );
					heartbeatComp.AttachToComponent(enemyPlayer.GetPlayerMesh3P(), n"spine_chest", EAttachmentRule::SnapToTarget);
				}
			}
			if ( count == 14 )
				count = 0;
			else
				count++;
			co.Wait(0.1);
		}
	}

	void OnThreadEnd( FNCCoroutineEndParams params ) override
	{
		Super::OnThreadEnd( params );
		Client_StopSound( passive.heartBeatSoundLoop.EventID );
	}
}

UCLASS( Abstract )
class UAS_BloodseekerHeartbeatWidget : UUserWidgetDefault
{
	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnimOptional ), Transient )
	UWidgetAnimation appear;

	AAS_PlayerEntity enemyPlayer;
	ANCPlayerCharacter localPlayer;

	UFUNCTION( BlueprintOverride )
	void Tick( FGeometry MyGeometry, float DeltaSeconds )
	{
		// TODO @jmccarty: Can we write a projection function that uses a camera instead of the viewport so that we don't have to do this on tick with camera transitions?
		if ( IsValid( enemyPlayer ) && IsValid( localPlayer ) )
		{
			float distance = Distance( enemyPlayer.GetActorLocation(), localPlayer.GetActorLocation() );
			float renderScale = Math::GetMappedRangeValueClamped(FVector2D(0,10000), FVector2D(1.5,0.25), distance );
			SetRenderScale( FVector2D( renderScale, renderScale ) );
			FVector2D outScreenPosition;
			Gameplay::ProjectWorldToScreen( Client_GetLocalPlayerController(), enemyPlayer.Mesh.GetSocketLocation( n"spine_chest" ), outScreenPosition );
			SetPositionInViewport( outScreenPosition );
		}
	}
}


UCLASS()
class UAS_DreadStatusEffectManagerComponent : UAS_ClientStatusEffectManagerComponent
{
	UAS_HeartbeatThread heartBeatThread;
	AAS_PlayerEntity dreadPlayer;

	int currentPlayingID = -1;

	UPROPERTY( EditDefaultsOnly )
	FName pinWidgetName = NAME_None;
	private UAS_PinnableWidget pinWidget;
	//UNiagaraComponent fearVFX;

	UFUNCTION(BlueprintOverride)
	void ClientBeginPlay()
	{
		Super::ClientBeginPlay();
		if ( ownerPlayer.GetStatusEffectValue( GameplayTags::StatusEffect_DreadPulse ) > 0 )
		{
			OnStatusStarted_Client(ownerPlayer);
		}
	}

	UFUNCTION(BlueprintOverride, Meta = (NoSuperCall))
	void OnStatusStarted_Client( ANCPlayerCharacter p )
	{
		dreadPlayer = Cast<AAS_PlayerEntity>(p);
		if ( !IsValid( dreadPlayer ) )
			return;

		UAS_Passive_Bloodseeker passive = Passive_Bloodseeker();
		if ( passive.dreadPlayers.Contains( dreadPlayer ) )
			return;

		AAS_PlayerEntity localPlayer = Client_GetLocalASPawn();

		if ( localPlayer == dreadPlayer )
		{
			FAudioResultData data = Client_EmitSoundOnEntity( passive.bloodSeekerHeartbeatSlow1P, dreadPlayer );
			currentPlayingID = data.EventID;
			//PulseVictimHeartbeat();
		}

		if ( localPlayer.GetTeam() == dreadPlayer.GetTeam() )
			return;

		if ( localPlayer.classManager.GetClassIndex() != GameplayTags::Classes_Class_Ekon )
			return;

		Client_EmitSoundOnEntity( passive.bloodSeekerHeartbeatSlow1P, dreadPlayer );//RTPC_Character_Hunter_Passive_Enemy_Health
		float32 currentHealth = dreadPlayer.GetHealth();
		Client_SetRTPCValue(dreadPlayer, passive.RTPC_Heartbeat, currentHealth, 0 );
		//FVector offset = enemyPlayer.Mesh.GetSocketLocation(n"Lf_chest") - enemyPlayer.GetActorLocation();
		UNiagaraComponent heartbeatComp = Client_SpawnEffectOnEntity_OneShot( passive.heartBeatVFX, dreadPlayer, n"spine_chest" );//, offset );
		heartbeatComp.AttachToComponent(dreadPlayer.GetPlayerMesh3P(), n"spine_chest", EAttachmentRule::SnapToTarget);
		//fearVFX = Client_SpawnEffectOnEntity_Looping( Passive_Bloodseeker().fearVictimVFX, dreadPlayer );
	}

	UFUNCTION(BlueprintOverride, Meta = (NoSuperCall))
	void OnStatusEnded_Client( ANCPlayerCharacter p )
	{

	}

	UFUNCTION(BlueprintOverride, Meta = (NoSuperCall))
	void EndPlay(EEndPlayReason EndPlayReason)
	{

	}

	void DreadCleanup()
	{
		if ( IsValid( heartBeatThread ) )
			heartBeatThread.Cancel();

		if ( IsValid( dreadPlayer ) )
			Passive_Bloodseeker().dreadPlayers.Remove(dreadPlayer);

		if ( currentPlayingID > -1 )
		{
			Client_StopSound( currentPlayingID );
			currentPlayingID = -1;
		}

		if ( IsValid( pinWidget ) )
		{
			GetLocalHUD().pinnedWidgetManager.RemovePinnableWidget( pinWidget );
			pinWidget = nullptr;
		}

		//if ( IsValid( fearVFX ) )
		//	fearVFX.DestroyComponent( this );
	}

	UFUNCTION()
	void PulseVictimHeartbeat()
	{
		AAS_PlayerEntity localPlayer = Client_GetLocalASPawn();
		if ( localPlayer.GetStatusEffectValue( GameplayTags::StatusEffect_DreadPulse ) > 0 )
		{
			Client_EmitSoundOnEntity( Passive_Bloodseeker().bloodSeekerHeartbeatMedium1P, localPlayer );//RTPC_Character_Hunter_Passive_Enemy_Health
			Client_SetRTPCValue(localPlayer, Passive_Bloodseeker().RTPC_Heartbeat, 20, 0 );
			System::SetTimer( this, n"PulseVictimHeartbeat", 1.5, false );
		}
	}
}