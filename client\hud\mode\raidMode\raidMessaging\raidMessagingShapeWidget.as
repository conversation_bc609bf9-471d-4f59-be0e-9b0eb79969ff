UCLASS( Abstract )
class UAS_RaidMessagingShapeWidget : UNC_DisplayWidget
{
	default StateOnConstruction = EDisplayConstructVisibility::Shown;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage raidMessagingShape;

	private UMaterialInstanceDynamic raidMessagingShapeMaterial;

	private const FName USE_ACCENT_PARAMETER = n"UseAccent";
	private const FName USE_CHEVRONS_PARAMETER = n"UseChevrons";

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		raidMessagingShapeMaterial = CreateDynamicMaterialFromImageBrush( raidMessagingShape );
		if ( IsValid( raidMessagingShapeMaterial ) )
		{
			raidMessagingShape.SetBrushFromMaterial( raidMessagingShapeMaterial );
		}
	}
	
	void ChangeShieldbreakerCarrier( AAS_PlayerEntity player )
	{
		// If we are in overtime, we don't care about any state changes
		AAS_PlayerEntity localPlayer = Client_GetLocalASPawn();
		if ( !IsValid( player ) || !IsValid( localPlayer ) || !IsValid( raidMessagingShapeMaterial ) || GetShotClockState() == EShotClockState::OVERTIME )
			return;

		// When a player has the shieldbreaker we want to use friendly or enemy colors
		bool isPlayerTeam		 = localPlayer.GetTeam() == player.GetTeam();
		FLinearColor accentColor = GetCommonUiMpcColor( isPlayerTeam ? CommonUiColorMpcNames::FRIENDLY_HIGHLIGHT : CommonUiColorMpcNames::ENEMY_HIGHLIGHT );
		raidMessagingShapeMaterial.SetVectorParameterValue( MaterialParameter::ACCENT_COLOR, accentColor );
		raidMessagingShapeMaterial.SetScalarParameterValue( USE_ACCENT_PARAMETER, MaterialParameter::GetTrueFalseFloat( true ) );
		raidMessagingShapeMaterial.SetScalarParameterValue( USE_CHEVRONS_PARAMETER, MaterialParameter::GetTrueFalseFloat( false ) );
	}

	void ChangeRaidObjectiveState( EObjectiveState objectiveState )
	{
		// If we are in overtime, we don't care about any state changes
		if ( !IsValid( raidMessagingShapeMaterial ) || GetShotClockState() == EShotClockState::OVERTIME )
			return;

		if ( objectiveState == EObjectiveState::ShieldBreakerCrafting || objectiveState == EObjectiveState::PickupShieldBreaker )
		{
			// When we are crafting or the shieldbreaker is on the ground, we want to use the gold accent color
			FLinearColor accentColor = GetCommonUiMpcColor( CommonUiColorMpcNames::GOLD );
			raidMessagingShapeMaterial.SetVectorParameterValue( MaterialParameter::ACCENT_COLOR, accentColor );
			raidMessagingShapeMaterial.SetScalarParameterValue( USE_ACCENT_PARAMETER, MaterialParameter::GetTrueFalseFloat( true ) );
		}
		else if ( objectiveState < EObjectiveState::ShieldBreakerCrafting )
		{
			// For raid and pre-shieldbreaker states, we don't want to use the accent
			raidMessagingShapeMaterial.SetScalarParameterValue( USE_ACCENT_PARAMETER, MaterialParameter::GetTrueFalseFloat( false ) );
		}

		raidMessagingShapeMaterial.SetScalarParameterValue( USE_CHEVRONS_PARAMETER, MaterialParameter::GetTrueFalseFloat( false ) );
	}

	void ChangeShotClockState( EShotClockState newState )
	{
		if ( !IsValid( raidMessagingShapeMaterial ) || newState != EShotClockState::OVERTIME )
			return;

		// When we are in overtime, we always show the overtime accent color
		FLinearColor accentColor = GetCommonUiMpcColor( CommonUiColorMpcNames::OVERTIME );
		raidMessagingShapeMaterial.SetVectorParameterValue( MaterialParameter::ACCENT_COLOR, accentColor );
		raidMessagingShapeMaterial.SetScalarParameterValue( USE_CHEVRONS_PARAMETER, MaterialParameter::GetTrueFalseFloat( true ) );
	}
}