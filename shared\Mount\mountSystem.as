UAS_MountSystem Mount()
{
	return Cast<UAS_MountSystem>( UNCGameplaySystemsSubsystem::Get_SharedSystem( GetCurrentWorld(), UAS_MountSystem::StaticClass() ) );
}

UCLASS()
class UAS_MountSystem : UNCGameplaySystem_Shared
{
	access internal = private, Mount;

	UPROPERTY( EditDefaultsOnly )
	TMap<FName, UAS_VehicleMountCoreData> CoreDataLibrary;

	// only 2 slots are not allowed by default ( WeaponSlot::RaidToolsSlot, WeaponSlot::RaidUltSlot ). This list should only contain the weapons for those slots we want to allow.
	UPROPERTY( EditDefaultsOnly )
	TArray<UWeaponPrimaryAsset> allowedOffhandMountWeapons;
	access:internal TArray<FName> allowedOffhandMountWeaponNames;

	// most weapons are allowed by default. This list should only contain weapons we don't want allowed.
	UPROPERTY( EditDefaultsOnly )
	TArray<UWeaponPrimaryAsset> notAllowedOffhandMountWeapons;

	UPROPERTY( EditDefaultsOnly )
	UDataTable mountData;

	access:internal TMap<FGameplayTag, TSubclassOf<UNCVehicleMountDataAsset>> vehicleMountDataAssetClasses;
	access:internal TMap<FGameplayTag, FMountClassData> mountClassData;
	access:internal TMap<TSubclassOf<AAS_VehicleMount>,FGameplayTag> tagByMountClass;
	access:internal TMap<FGameplayTag,TSubclassOf<AAS_VehicleMount>> mountClassesByTag;
	access:internal TMap<TSubclassOf<AAS_VehicleMount>,UAS_VehicleMountAssetData> mountAssetDataByMountClass;
	access:internal TMap<FGameplayTag,UAS_VehicleMountAssetData> mountAssetDataByTag;
	access:internal TArray<FName> notAllowedOffhandMountWeaponNames;	

	UFUNCTION( BlueprintOverride )
	void Initialize()
	{
		if ( IsValid( mountData ) )
		{
			TArray<FMountClassData> rows;
			mountData.GetAllRows( rows );

			for ( FMountClassData row : rows )
			{
				mountClassData.Add( row.id, row );

				if ( IsValid( row.scriptVehicleDataAsset ) )
				{
					tagByMountClass.Add( row.scriptVehicleDataAsset.classBP, row.id );
					mountClassesByTag.Add( row.id, row.scriptVehicleDataAsset.classBP );
					mountAssetDataByTag.Add( row.id, row.scriptVehicleDataAsset );
					mountAssetDataByMountClass.Add( row.scriptVehicleDataAsset.classBP, row.scriptVehicleDataAsset );
				}

				if ( IsValid( row.vehicleDataAsset ) )
				{
					vehicleMountDataAssetClasses.Add( row.id, row.vehicleDataAsset );
				}
			}
		}

		for ( UWeaponPrimaryAsset asset : allowedOffhandMountWeapons )
			allowedOffhandMountWeaponNames.Add( asset.GetName() );
		for ( UWeaponPrimaryAsset asset : notAllowedOffhandMountWeapons )
			notAllowedOffhandMountWeaponNames.Add( asset.GetName() );
	}

	UFUNCTION( BlueprintOverride )
	void BeginPlay()
	{
		if ( IsServer() )
		{
			ScriptAssert( IsServer(), "MUST RUN ON SERVER" );

			ServerCallbacks().OnBotMountInput.AddUFunction( this, n"ServerMountInput" );

			ServerBeginPlayMountStable();
		}
		else
		{
			ScriptAssert( IsClient(), "MUST RUN ON CLIENT" );

			UNCInputComponent ncInput = Client_GetLocalPlayerController().GetNCInputComponent();
			ncInput.BindAction( GameplayTags::Input_Actions_Script_Mount_Embark_Pressed, ETriggerEvent::Triggered, FEnhancedInputActionHandlerDynamicSignature( this, n"cl_OnMountPressed" ) );
			ncInput.BindAction( GameplayTags::Input_Actions_Script_Mount_Embark_KBOnly, ETriggerEvent::Triggered, FEnhancedInputActionHandlerDynamicSignature( this, n"cl_OnMountKBOnly" ) );

			ncInput.BindAction( GameplayTags::Input_Actions_Script_Mount_Embark_Held, ETriggerEvent::Triggered, FEnhancedInputActionHandlerDynamicSignature( this, n"cl_OnMountHoldEmbark" ) );
			ncInput.BindAction( GameplayTags::Input_Actions_Script_Mount_Embark_DoubleClick, ETriggerEvent::Triggered, FEnhancedInputActionHandlerDynamicSignature( this, n"cl_OnMountDblTapEmbark" ) );

			UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"ServerCommand_ShowMountCooldown", n"SC_ShowMountCooldown" );
			ScriptCallbacks().client_onMountStateChanged.AddUFunction( this, n"Client_OnMountStateChanged" );
		}
	}

	//Sorry Ravi, I'll chat with you tomorrow on where you'd like this logic to go for real.
	UFUNCTION()
	private void Client_OnMountStateChanged( ANCPlayerCharacter player, AAS_VehicleMount mount, bool isRiding)
	{
		if ( !IsValid( player ) )
			return; 

		UAS_Passive_Bloodseeker passive = Passive_Bloodseeker();
		if ( !IsValid( passive ) )
			return;

		ANCPlayerCharacter localPlayer = Client_GetLocalPawn();
		bool isEkon = player.ClassManager().GetClassIndex() == GameplayTags::Classes_Class_Ekon;
		bool playerIsLocalEkon = localPlayer == player && isEkon;
		AAS_PlayerEntity asOwnerPlayer = Cast<AAS_PlayerEntity>( player );

		if ( isRiding )
		{
			if ( playerIsLocalEkon )
			{
				passive.appliedCameraOverrideID = player.GetTPPCameraSettings().AddOverrideSettings( n"WolfCamera", passive.wolfCameraSettings );
				asOwnerPlayer.TPPCameraComponent.AddTPPRequest( n"WolfForm" );
			}

			if ( isEkon )
			{
				player.GetPlayerMesh3P().SetHiddenInGame( true );
				if ( IsValid( mount ) )
					mount.GetVehicleMesh3P().SetWorldScale3D(FVector(1.5,1.5,1.5));
				if ( !playerIsLocalEkon )
					passive.SwapVFXToMount( asOwnerPlayer, mount );
			}
		}
		else
		{
			if ( playerIsLocalEkon )
			{
				asOwnerPlayer.GetTPPCameraSettings().ClearOverrideSettingsWithLerp( passive.appliedCameraOverrideID, 0.25f, EEasingFunc::Linear );
				passive.appliedCameraOverrideID = -1;

				asOwnerPlayer.TPPCameraComponent.ClearTPPRequest( n"WolfForm", false );
				asOwnerPlayer.TPPCameraComponent.ForceDisableTPP( 0.f );
				//Thread( this, n"Unhide3PMeshDelayed", asOwnerPlayer );
				// Snap camera to first person mode. The outro animation has by this point pulled the camera in to be in the correct location.
				//if ( !asOwnerPlayer.TPPCameraComponent.HasTPPRequests() )
				//	asOwnerPlayer.TPPCameraComponent.ForceDisableTPP( 0.f );
			}
			else
			{
				if ( isEkon )
				{
					player.GetPlayerMesh3P().SetHiddenInGame( false );
					if ( !playerIsLocalEkon )
						passive.SwapVFXToPlayer( asOwnerPlayer );
				}
			}
		}
	}

	UFUNCTION()
	void Unhide3PMeshDelayed( UNCCoroutine co, AAS_PlayerEntity ownerPlayer )
	{
		co.EndOnDestroyed( ownerPlayer );

		co.Wait( 0.5 );

		ownerPlayer.GetPlayerMesh3P().SetHiddenInGame( false );
	}


	UFUNCTION()
	void SC_ShowMountCooldown( TArray<FString> args )
	{
		if ( args.Num() < 2 )
			return;

		AAS_HUD hud = Cast<AAS_HUD>( HUD );
		hud.mainHUDWidget.DisplayGenericMessage( GetLocalizedText( Localization::Mount, args[0], FFormatArgumentValue( args[1].ToFloat() ) ), EHUDMessageStyle::SMALL_CENTER );
	}

	/****************************************************************\

	██ ███    ██ ██████  ██    ██ ████████
	██ ████   ██ ██   ██ ██    ██    ██
	██ ██ ██  ██ ██████  ██    ██    ██
	██ ██  ██ ██ ██      ██    ██    ██
	██ ██   ████ ██       ██████     ██

	\****************************************************************/
	UFUNCTION()
	void ServerMountInput( ANCPlayerCharacter playerChar )
	{
		if (ANCPlayerCharacter::UsePredictedMountInput())
		{
			return;
		}

		ScriptAssert( IsServer(), "MUST RUN ON SERVER" );

		AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( playerChar );
		if ( !IsValid( player ) )
			return;

		if ( player.IsPlayerRidingMount() )
		{
			Mount::Dismount( player );
			return;
		}

		if ( !player.CanPlayerEmbarkOnVehicle() )
			return;

		FNCPlayerVehicleSpawnParams spawnParams = player.GetVehicleSpawnParamsForSummon();
		if ( !spawnParams.bIsValid )
			return;

		MountInstantEmbark( player, spawnParams );
	}

	UFUNCTION()
	private void cl_OnMountPressed( FInputActionValue ActionValue, float32 ElapsedTime, float32 TriggeredTime, const UInputAction SourceAction )
	{
		if ( !GetCvarBool( f"VehicleMount.InputHold" ) && !GetCvarBool( f"VehicleMount.InputDblTap" ) )
			cl_OnMountInput( ActionValue, ElapsedTime, TriggeredTime, SourceAction );
	}

	UFUNCTION()
	private void cl_OnMountHoldEmbark( FInputActionValue ActionValue, float32 ElapsedTime, float32 TriggeredTime, const UInputAction SourceAction )
	{
		if ( !GetCvarBool( f"VehicleMount.InputHold" ) )
			return;

		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( !IsValid( player ) )
			return;

		if ( !player.IsPlayerRidingMount() )
			cl_OnMountInput( ActionValue, ElapsedTime, TriggeredTime, SourceAction );
	}

	UFUNCTION()
	private void cl_OnMountDblTapEmbark( FInputActionValue ActionValue, float32 ElapsedTime, float32 TriggeredTime, const UInputAction SourceAction )
	{
		if ( !GetCvarBool( f"VehicleMount.InputDblTap" ) )
			return;

		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( !IsValid( player ) )
			return;

		if ( !player.IsPlayerRidingMount() )
			cl_OnMountInput( ActionValue, ElapsedTime, TriggeredTime, SourceAction );
	}

	UFUNCTION()
	private void cl_OnMountKBOnly( FInputActionValue ActionValue, float32 ElapsedTime, float32 TriggeredTime, const UInputAction SourceAction )
	{
		if ( !GetCvarBool( f"VehicleMount.InputHold" ) && !GetCvarBool( f"VehicleMount.InputDblTap" ) )
			return;

		cl_OnMountInput( ActionValue, ElapsedTime, TriggeredTime, SourceAction );
	}

	private void cl_OnMountInput( FInputActionValue ActionValue, float32 ElapsedTime, float32 TriggeredTime, const UInputAction SourceAction )
	{
		if (ANCPlayerCharacter::UsePredictedMountInput())
		{
			return;
		}

		ScriptAssert( IsClient(), "MUST RUN ON CLIENT" );

		AAS_HUD hud = Cast<AAS_HUD>( HUD );
		if ( !IsValid( hud ) )
			return;

		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( !IsValid( player ) )
			return;

		if ( !player.CanPlayerEmbarkOnVehicle() )
			return;

		UNCUseMountSmartInput inputEvent = Cast<UNCUseMountSmartInput>( NewObject( GetCurrentWorld(), UNCUseMountSmartInput::StaticClass() ) );
		inputEvent.SendToServer();
	}

	/****************************************************************\

	██ ███    ██ ███████ ████████  █████  ███    ██ ████████     ███████ ███    ███ ██████   █████  ██████  ██   ██
	██ ████   ██ ██         ██    ██   ██ ████   ██    ██        ██      ████  ████ ██   ██ ██   ██ ██   ██ ██  ██
	██ ██ ██  ██ ███████    ██    ███████ ██ ██  ██    ██        █████   ██ ████ ██ ██████  ███████ ██████  █████
	██ ██  ██ ██      ██    ██    ██   ██ ██  ██ ██    ██        ██      ██  ██  ██ ██   ██ ██   ██ ██   ██ ██  ██
	██ ██   ████ ███████    ██    ██   ██ ██   ████    ██        ███████ ██      ██ ██████  ██   ██ ██   ██ ██   ██

	\****************************************************************/
	UFUNCTION()
	private void MountInstantEmbark( AAS_PlayerEntity player, FNCPlayerVehicleSpawnParams spawnParams )
	{
		ScriptAssert( IsServer(), "MUST RUN ON SERVER" );

		if ( !IsValid( player ) || !IsAlive( player ) )
			return;

		if ( !spawnParams.bIsValid )
		{
			Warning( "MountInstantEmbark got called without a successfull placement result" );
			return;
		}

		AAS_VehicleMount mount	= SpawnOrActivateMount( player, spawnParams.SpawnLocation, spawnParams.SpawnRotation );

		if ( IsValid( mount ) )
			mount.sv_Embark( player );
	}

	/****************************************************************\

	███    ███  ██████  ██    ██ ███    ██ ████████      ██████  ██████       ██ ███████  ██████ ████████
	████  ████ ██    ██ ██    ██ ████   ██    ██        ██    ██ ██   ██      ██ ██      ██         ██
	██ ████ ██ ██    ██ ██    ██ ██ ██  ██    ██        ██    ██ ██████       ██ █████   ██         ██
	██  ██  ██ ██    ██ ██    ██ ██  ██ ██    ██        ██    ██ ██   ██ ██   ██ ██      ██         ██
	██      ██  ██████   ██████  ██   ████    ██         ██████  ██████   █████  ███████  ██████    ██

	\****************************************************************/
	AAS_VehicleMount SpawnOrActivateMount( AAS_PlayerEntity player, FVector summonLocation, FRotator summonRotation, FGameplayTag customTag = GameplayTags::Dev_Invalid )
	{
		ScriptAssert( IsServer(), "MUST RUN ON SERVER" );

		player.UpdateAndSummonOwnedVehicle(summonLocation, summonRotation);
		AAS_VehicleMount mount = player.GetMountComponent().GetCurrentMount();
		
		mount.VehicleIsDormant.SetNetValue(false);
		mount.SetLocation(summonLocation);
		mount.SetRotation(summonRotation);

		FVehicleMountData data = mount.GetMountData();

		if (!ANCVehicle::UsesCodeVocalizations())
			Server_EmitSoundOnEntity( data.Asset.Audio.mountStartSound, player );

		return mount;
	}

	/****************************************************************\

	███    ███  ██████  ██    ██ ███    ██ ████████     ███████ ████████  █████  ██████  ██      ███████ 
	████  ████ ██    ██ ██    ██ ████   ██    ██        ██         ██    ██   ██ ██   ██ ██      ██      
	██ ████ ██ ██    ██ ██    ██ ██ ██  ██    ██        ███████    ██    ███████ ██████  ██      █████   
	██  ██  ██ ██    ██ ██    ██ ██  ██ ██    ██             ██    ██    ██   ██ ██   ██ ██      ██      
	██      ██  ██████   ██████  ██   ████    ██        ███████    ██    ██   ██ ██████  ███████ ███████

	\****************************************************************/
	TMap<int,FMountStableArray> stableMap;
	TMap<ANCPlayerCharacter, UAS_MountStablePosComponent> registeredStables;

	void ServerBeginPlayMountStable()
	{
		ScriptCallbacks().server_OnMountOwnerRespawned.AddUFunction( this, n"OnMountOwnerRespawned" );
		ScriptCallbacks().shared_OnRaidEnded.AddUFunction( this, n"OnRaidEnded");
	}

	void RegisterMountStable( int team, TArray<UAS_MountStablePosComponent> allSpots )
	{
		if ( !stableMap.Contains(team) )
			stableMap.Add( team, FMountStableArray() );

		stableMap[team].stables.Append(allSpots);

		SpawnAllTeamMountsInStables(team);			
	}		

	UFUNCTION()
	private void OnMountOwnerRespawned( AAS_PlayerEntity player )
	{		
		SpawnPlayerMountInStable( player );
	}

	UFUNCTION()
	private void OnMountStateChanged( ANCVehicle vehicle, EVehicleActiveState oldState, EVehicleActiveState newState )
	{
		if ( newState != EVehicleActiveState::Vehicle_Inactive )
			return;

		AAS_PlayerEntity ownerPlayer = Cast<AAS_PlayerEntity>(vehicle.GetVehicleOwner());

		if (!IsValid(ownerPlayer) || !IsAlive(ownerPlayer))
			return;

		if ( IsTeamBeingRaided( ownerPlayer.GetTeam() ) )
			return;
		
		SpawnPlayerMountInStable( ownerPlayer );
	}	

	UFUNCTION()
	private void OnRaidEnded(AAS_RaidEventManager_v2 eventManager)
	{
		int defender = eventManager.GetDefenderTeam();
		if ( !IsTeamBeingRaided( defender ) )	//future proofing for multiple teams raiding one base
			SpawnAllTeamMountsInStables(defender);

		int attacker = eventManager.GetAttackerTeam();
		if ( !IsTeamBeingRaided( attacker ) )	//future proofing for multiple teams raiding one base
			SpawnAllTeamMountsInStables(defender);	
	}

	private void SpawnAllTeamMountsInStables( int team )
	{		
		TArray<ANCPlayerCharacter> players = GetPlayersOfTeam( team );
		for ( ANCPlayerCharacter player : players )
		{
			if ( IsAlive(player) && player.GetMountComponent().GetSavedHealth() > 0 )
				SpawnPlayerMountInStable( Cast<AAS_PlayerEntity>( player ) );
		}
	}

	void SpawnPlayerMountInStable( AAS_PlayerEntity player )
	{		
		if ( IsValid( Mount::GetPilotedMount( player ) ) )
			return;

		bool inRaid = IsTeamBeingRaided( player.GetTeam() );
		if ( inRaid )
			return;

		const float32 startHealth = player.mountData.GetSavedHealth();
		if( startHealth == 0 )
			return;

		UAS_MountStablePosComponent stable		 = GetOrRegisterPlayerStable( player );
		if ( !IsValid(stable) )
			return;

		FVector loc								 = stable.GetWorldLocation() + FVector( 0, 0, 100 );
		FRotator rot							 = stable.GetWorldRotation();
		FGameplayTag tag					 	 = player.GetEquippedMountTag();
		TSubclassOf<AAS_VehicleMount> mountClass = Mount::GetMountClassFromGameplayTag( tag );

		FPlacementResults results = Mount::MountSpawnCapsuleTraceFromPosition( loc, rot, player, mountClass );
		AAS_VehicleMount mount	  = Mount().SpawnOrActivateMount( player, results.location, results.rotation );
		mount.OnActiveStateChanged.UnbindObject(this);
		mount.OnActiveStateChanged.AddUFunction(this, n"OnMountStateChanged");
		mount.SetVehicleState( EVehicleActiveState::Vehicle_Passive );
		mount.SetRotation(rot);
		mount.StopPassiveVehicle();
	}

	UAS_MountStablePosComponent GetOrRegisterPlayerStable( ANCPlayerCharacter player )
	{
		UAS_MountStablePosComponent stable = GetPlayerStable( player );
		if ( IsValid( stable ) )
			return stable;

		stable = GetFreePlayerStable(player.GetTeam());
		if ( IsValid( stable ) )
			RegisterPlayerToStable( player, stable );
		
		return stable;
	}

	UAS_MountStablePosComponent GetPlayerStable( ANCPlayerCharacter player )
	{
		if ( registeredStables.Contains( player ) )
			return registeredStables[player];

		return nullptr;
	}

	UAS_MountStablePosComponent GetFreePlayerStable( int team )
	{
		if ( !stableMap.Contains(team) )
			return nullptr;

		TArray<UAS_MountStablePosComponent> teamStables = stableMap[team].stables;
		for ( UAS_MountStablePosComponent stable : teamStables )
		{
			if ( stable.IsFree() )
				return stable;
		}

		return nullptr;
	}

	void RegisterPlayerToStable( ANCPlayerCharacter player, UAS_MountStablePosComponent stable )
	{
		ScriptAssert( !registeredStables.Contains( player ), f"player [{player}] already registered to stable" );
		ScriptAssert( stable.IsFree(), f"stable [{stable}] is registered to another player" );

		stable.RegisterPlayer( player );
		registeredStables.Add( player, stable );

		player.OnDestroyed.AddUFunction( this, n"UnRegisterPlayerOnDestroy" );
	}

	UFUNCTION()
	private void UnRegisterPlayerOnDestroy( AActor DestroyedActor )
	{
		UnRegisterStable( Cast<ANCPlayerCharacter>( DestroyedActor ) );
	}

	void UnRegisterStable( ANCPlayerCharacter player )
	{
		if ( registeredStables.Contains( player ) )
		{
			registeredStables[player].UnRegisterPlayer();
			registeredStables.Remove( player );
		}

		player.OnDestroyed.Unbind( this, n"UnRegisterPlayerOnDestroy" );
	}
}

UCLASS()
class UNCUseMountSmartInput : UNCNetClientToServerEvent
{
	UFUNCTION( BlueprintOverride )
	void OnEventReceived( ANCNetPlayerController receiver )
	{
		ANCPlayerCharacter player = Cast<ANCPlayerCharacter>( receiver.GetControlledPawn() );
		Mount().ServerMountInput( player );
	}
}

struct FMountStableArray
{
	TArray<UAS_MountStablePosComponent> stables;
}


/****************************************************************\

 ██████  ██       ██████  ██████   █████  ██          ██    ██ ████████ ██ ██
██       ██      ██    ██ ██   ██ ██   ██ ██          ██    ██    ██    ██ ██
██   ███ ██      ██    ██ ██████  ███████ ██          ██    ██    ██    ██ ██
██    ██ ██      ██    ██ ██   ██ ██   ██ ██          ██    ██    ██    ██ ██
 ██████  ███████  ██████  ██████  ██   ██ ███████      ██████     ██    ██ ███████

\****************************************************************/
namespace Mount
{
	AAS_VehicleMount GetPilotedMount( const ANCPlayerCharacter player )
	{
		return Cast<AAS_VehicleMount>( player.GetPilotedVehicle() );
	}

	AAS_VehicleMount GetPilotedOrWorldMount( const ANCPlayerCharacter player )
	{
		AAS_VehicleMount mount = Cast<AAS_VehicleMount>( player.GetPilotedVehicle() );
		if ( IsValid( mount ) )
			return mount;

		return player.GetMountComponent().GetCurrentMount();
	}

	FPlacementResults MountSpawnCapsuleTraceFromPosition( FVector position, FRotator rotation, ANCPlayerCharacter player, TSubclassOf<AAS_VehicleMount> vehicleMountClass, EDrawDebugTrace debug = EDrawDebugTrace::None )
	{
		FPlacementResults result;
		result.rotation = rotation;

		TArray<AActor> actorsToIgnore;
		actorsToIgnore.Add( player );

		AAS_VehicleMount currentMount = GetPilotedOrWorldMount(player);
		if (IsValid(currentMount))
			actorsToIgnore.Add(currentMount);

		// doing this so I can call on client and get debug draw info
		float radius	 = 90;
		float halfHeight = 150;

		if ( vehicleMountClass != nullptr )
		{
			AAS_VehicleMount mountObj  = Cast<AAS_VehicleMount>( vehicleMountClass.Get().GetDefaultObject() );
			UCapsuleComponent mountCap = Cast<UCapsuleComponent>( mountObj.GetComponentByClass( UCapsuleComponent::StaticClass() ) );
			radius					   = mountCap.CapsuleRadius;
			halfHeight				   = mountCap.CapsuleHalfHeight;
		}

		const float CEILING_CLEARANCE = 1; // tested pretty confident about
		float playerHalfHeight		  = player.CapsuleComponent.GetScaledCapsuleHalfHeight();
		FVector floor				  = position + FVector( 0, 0, -playerHalfHeight );
		FVector start				  = floor + FVector( 0, 0, halfHeight );
		FVector end					  = start + FVector( 0, 0, CEILING_CLEARANCE );
		FVector addFloorHeight		  = FVector::ZeroVector;

		FHitResult outHit = CapsuleTraceSingleByProfile( start, end, radius, halfHeight, n"Pawn", true, actorsToIgnore, true, debug, FLinearColor::Gray, FLinearColor::White, 5 );

		const float MAX_HEIGHT_ABOVE_PLAYER_TO_SPAWN = 50;
		const int PREVENT_INFINITE_LOOP				 = 10;
		int iteration								 = 0;

		while ( iteration < PREVENT_INFINITE_LOOP && outHit.GetbStartPenetrating() )
		{
			iteration++;
			if ( IsValid( outHit.GetActor() ) && outHit.GetActor().Class.IsChildOf( AAS_ZiplineBubbleShield::StaticClass() ) )
			{
				actorsToIgnore.Add( outHit.GetActor() );
				outHit = CapsuleTraceSingleByProfile( start + addFloorHeight, end + addFloorHeight, radius, halfHeight, n"Pawn", true, actorsToIgnore, true, debug, FLinearColor::Purple, FLinearColor::DPink, 5 );
				continue;
			}

			float addZ = outHit.ImpactPoint.Z - floor.Z;
			if ( addZ > 0 && addZ <= MAX_HEIGHT_ABOVE_PLAYER_TO_SPAWN )
			{
				addFloorHeight = FVector( 0, 0, addZ );
				outHit		   = CapsuleTraceSingleByProfile( start + addFloorHeight, end + addFloorHeight, radius, halfHeight, n"Pawn", true, actorsToIgnore, true, debug, FLinearColor::Purple, FLinearColor::DPink, 5 );
			}
			else
				break;
		}

		result.location = start + addFloorHeight;
		result.success	= !outHit.GetbStartPenetrating() && !outHit.GetbBlockingHit();

		return result;
	}

	FNCPlayerVehicleSpawnParams MountSpawnCapsuleTraceFromPlayer( ANCPlayerCharacter player, TSubclassOf<AAS_VehicleMount> vehicleMountClass, EDrawDebugTrace debug = EDrawDebugTrace::None )
	{
		FVector loc				 = player.GetActorLocation();
		FRotator rot			 = FRotator( 0, player.GetViewRotation().Yaw, 0 );
		FPlacementResults result = MountSpawnCapsuleTraceFromPosition( loc, rot, player, vehicleMountClass, debug );
		FNCPlayerVehicleSpawnParams spawnParams;
		spawnParams.bIsValid = result.success;
		spawnParams.SpawnLocation = result.location;
		spawnParams.SpawnRotation = result.rotation;
		return spawnParams;
	}

	bool CanSwapToMountWithMessageOnClient( AAS_PlayerEntity player )
	{
		if ( !IsValidMountTag( player.GetEquippedMountTag() ) )
			return false;

		if ( !player.IsMountEnabled() )
			return false;

		if ( player.passivesComponent.HasPassive( GameplayTags::Classes_Passives_IsJadeDemon ) )
		{
			Server_SendGenericMessage( player, Localization::Mount, "mount_summonfail_generic" );
			return false;
		}

		if ( IsValid( player.GetActiveWeapon() ) )
		{
			if ( player.GetActiveWeapon().WeaponConfigData.PreventMountWhileActive )
			{
				Server_SendGenericMessage( player, Localization::Mount, "mount_summonfail_generic" );
				return false;
			}
		}

		if ( player.IsTeleportActive() )
		{
			Server_SendGenericMessage( player, Localization::Mount, "mount_summonfail_teleport" );
			return false;
		}

		if ( GetGameTimeMS() <= player.GetMountComponent().GetCooldownEndTime() )
		{
			ScriptCallbacks().client_OnTriedToUseMountOnCooldown.Broadcast();
			return false;
		}

		return true;
	}

	void Dismount( ANCPlayerCharacter player )
	{
		ScriptAssert( IsServer() || ANCPlayerCharacter::UsePredictedMountInput(), "SERVER ONLY" );

		AAS_VehicleMount mount = GetPilotedMount( player );
		if ( !IsValid( mount ) )
			return;

		mount.Dismount( player );
	}

	const FName VEHICLE_CORE_DATA_DEFAULT = n"DEFAULT";
	const FName VEHICLE_CORE_DATA_DEFAULT_EKON = n"Ekon";

	FVehicleMountData GetMountVehicleData( FGameplayTag tag, FName libraryEntry )
	{
		UAS_VehicleMountAssetData dataAsset = Mount::GetMountAssetData( tag );

		FName coreName = libraryEntry;
		if ( !Mount().CoreDataLibrary.Contains( coreName ) )
		{
			Warning( f"Core Library is missing entry: {coreName}" );
			coreName = VEHICLE_CORE_DATA_DEFAULT;
		}

		ScriptAssert( Mount().CoreDataLibrary.Contains( coreName ), f"Core Library is missing entry: {coreName}" );

		FVehicleMountData newData( Mount().CoreDataLibrary[coreName], dataAsset );

		return newData;
	}

	TSubclassOf<UNCVehicleMountDataAsset> GetMountCodeDataAssetClass( FGameplayTag tag )
	{
		FGameplayTag mountTag = tag;
		if ( !Mount().vehicleMountDataAssetClasses.Contains( mountTag ) )
		{
			Warning( f"Code Data Asset Classes missing entry: {mountTag}" );
			mountTag = GameplayTags::Mounts_Horse;
		}

		ScriptAssert( Mount().vehicleMountDataAssetClasses.Contains( mountTag ), f"Code Data Asset Classes missing entry: {mountTag}" );

		return Mount().vehicleMountDataAssetClasses[mountTag];
	}

	////////////////////////////////////////////////////////////
	TSubclassOf<AAS_VehicleMount> GetMountClassFromGameplayTag( FGameplayTag tag )
	{
		ScriptAssert( Mount().mountClassesByTag.Contains( tag ), f"tag: {tag}, does not exist in mountClassesByTag" );
		return Mount().mountClassesByTag[tag];
	}

	FGameplayTag GetGameplayTagFromMount( AAS_VehicleMount mount )
	{
		ScriptAssert( Mount().tagByMountClass.Contains( mount.GetClass() ), f"AAS_VehicleMount: {mount.GetClass()}, does not exist in tagByMountClass" );
		return Mount().tagByMountClass[mount.GetClass()];
	}

	UAS_UMountAnimAsset GetMountAnimData( FGameplayTag tag )
	{
		return GetMountAssetData( tag ).Anim.animSheet;
	}

	UAS_UMountAnimAsset GetMountAnimData( AAS_VehicleMount mount )
	{
		return GetMountAssetData( mount ).Anim.animSheet;
	}

	UAS_VehicleMountAssetData GetMountAssetData( FGameplayTag tag )
	{
		ScriptAssert( Mount().mountAssetDataByTag.Contains( tag ), f"AAS_VehicleMount: {tag}, does not exist in mountAssetDataByTag" );
		return Mount().mountAssetDataByTag[tag];
	}

	UAS_VehicleMountAssetData GetMountAssetData( AAS_VehicleMount mount )
	{
		ScriptAssert( Mount().mountAssetDataByMountClass.Contains( mount.GetClass() ), f"AAS_VehicleMount: {mount.GetClass()}, does not exist in mountAnimByMountClass" );
		return Mount().mountAssetDataByMountClass[mount.GetClass()];
	}

	bool IsAllowedOffhandWeapon( FName weaponName )
	{
		return Mount().allowedOffhandMountWeaponNames.Contains( weaponName );
	}

	bool NotAllowedOffhandWeapon( FName weaponName )
	{
		return Mount().notAllowedOffhandMountWeaponNames.Contains( weaponName );
	}

	FMountClassData GetMountClassData( FGameplayTag mountId )
	{
		FMountClassData result;
		ScriptAssert( Mount().mountClassData.Find( mountId, result ), f"Attempted to find mount {mountId} but it wasn't added to the class data map" );
		return result;
	}
}

/****************************************************************\

███    ███  ██████  ██    ██ ███    ██ ████████     ██     ██ ███████  █████  ██████      ██   ██  █████   ██████ ██   ██
████  ████ ██    ██ ██    ██ ████   ██    ██        ██     ██ ██      ██   ██ ██   ██     ██   ██ ██   ██ ██      ██  ██
██ ████ ██ ██    ██ ██    ██ ██ ██  ██    ██        ██  █  ██ █████   ███████ ██████      ███████ ███████ ██      █████
██  ██  ██ ██    ██ ██    ██ ██  ██ ██    ██        ██ ███ ██ ██      ██   ██ ██          ██   ██ ██   ██ ██      ██  ██
██      ██  ██████   ██████  ██   ████    ██         ███ ███  ███████ ██   ██ ██          ██   ██ ██   ██  ██████ ██   ██

\****************************************************************/
mixin void ServerGiveMount( ANCPlayerCharacter self, FGameplayTag mountType )
{
	ScriptAssert( IsServer(), "SERVER ONLY" );
	AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( self );
	if ( !IsValid( player ) )
		return;

	if ( !IsValidMountTag( mountType ) )
		player.mountData.net_mountType.SetNetValue( -1 );

	int index = GetIntForGameplayTag( mountType );
	player.mountData.net_mountType.SetNetValue( index );

	player.Server_OnMountInventoryChanged.Broadcast();
}

mixin FGameplayTag GetEquippedMountTag( ANCPlayerCharacter self )
{
	AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( self );
	if ( !IsValid( player ) )
		return FGameplayTag();

	if ( player.mountData.net_mountType == -1 )
		return FGameplayTag();

	bool isEkon = player.ClassManager().GetClassIndex() == GameplayTags::Classes_Class_Ekon;
	FGameplayTag tag = isEkon ? GameplayTags::Mounts_Ekon : GetGameplayTagForInt( player.mountData.net_mountType );

	if ( !IsValidMountTag( tag ) )
		return FGameplayTag();

	return tag;
}

bool IsValidMountTag( FGameplayTag mountType )
{
	return mountType.MatchesTag( GameplayTags::Mounts );
}

mixin bool IsMountEnabled( ANCPlayerCharacter self )
{
	AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( self );
	if ( !IsValid( player ) )
		return false;

	return player.mountData.net_mountEnabled;
}

mixin void ServerEnableMount( ANCPlayerCharacter self, FName debugContext )
{
	ScriptAssert( IsServer(), "SERVER ONLY" );
	AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( self );
	if ( !IsValid( player ) )
		return;

	ScriptAssert( player.mountData.mountDebugContext.Contains( debugContext ), f"debugContext {debugContext} not found to re-enable" );
	player.mountData.mountDebugContext.Remove( debugContext );

	if ( player.mountData.mountDebugContext.Num() > 0 )
		return;

	if ( !player.IsMountEnabled() )
		player.mountData.net_mountEnabled.SetNetValue( true );
}

mixin void ServerDisableMount( ANCPlayerCharacter self, FName debugContext )
{
	ScriptAssert( IsServer(), "SERVER ONLY" );
	AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( self );
	if ( !IsValid( player ) )
		return;

	ScriptAssert( !player.mountData.mountDebugContext.Contains( debugContext ), f"too many calls to debugContext {debugContext}" );
	player.mountData.mountDebugContext.Add( debugContext );

	if ( player.IsMountEnabled() )
		player.mountData.net_mountEnabled.SetNetValue( false );
}
//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////