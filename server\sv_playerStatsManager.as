USV_PlayerStatsManager PlayerStatsManager()
{
	return Cast<USV_PlayerStatsManager>( UNCGameplaySystemsSubsystem::Get_ServerSystem( GetCurrentWorld(), USV_PlayerStatsManager::StaticClass() ) );
}

bool IsPlayerStatsEnabled()
{
	return GameModeDefaults().GamemodeRules_PlayerStatsEnabled;
}

FStatDefinition CreateStatDefinition( FGameplayTag name, EAggregationType type )
{
	FStatDefinition newStat;
	newStat.Name			= name;
	newStat.AggregationType = type;
	return newStat;
}

// Data table Row to associate key (FGameplayTag) to string name and any data
USTRUCT()
struct FPlayerStatData
{
	UPROPERTY( Meta = ( Categories = "Progression.PlayerStats" ) )
	FGameplayTag statID;

	UPROPERTY()
	EAggregationType aggegationType = EAggregationType::UNKNOWN;

	// Player facing name for stat
	UPROPERTY()
	FText name;
}

USTRUCT()
struct FCachedPlayerData
{
	// Store class index because pawn is destroyed before player state and class can't be looked up at that time
	UPROPERTY()
	FGameplayTag classIndex;

	// Player data tracking for determining some stats and achievements
	bool hasDied = false;
	int vesperSpent = 0;
	int rareItemsPurchased = 0;
}

UCLASS( Abstract )
class USV_PlayerStatsManager : UNCGameplaySystem_Server
{
	//////////////////////////////////////////
	// Achievement values
	const int BREAK_THE_BANK_AMOUNT = 77;
	const int POCKET_OF_VESPER_AMOUNT = 10;
	/////////////////////////////////////////

	TMap<ANCPlayerCharacter, FCachedPlayerData> cachedPlayerData;

	bool hasEnteredOvertime = false;

	UFUNCTION( BlueprintOverride )
	void Initialize()
	{
		if ( !IsPlayerStatsEnabled() )
		{
			return;
		}

		_InitializeCallbacks();
	}

	private void _InitializeCallbacks()
	{
		// Player
		ServerCallbacks().OnPlayerFirstJoined.AddUFunction( this, n"OnPlayerFirstJoined" );
		ServerCallbacks().OnPlayerReconnected.AddUFunction( this, n"OnPlayerReconnected" );
		ServerCallbacks().OnPlayerDisconnected.AddUFunction( this, n"OnPlayerDisconnected" );
		ServerCallbacks().OnPlayerDestroyed.AddUFunction( this, n"OnPlayerDestroyed" );
		ScriptCallbacks().server_onPlayerAssist.AddUFunction( this, n"OnAssist" );
		ScriptCallbacks().server_onPlayerEquipmentChanged.AddUFunction( this, n"OnPlayerEquipmentChanged");
		ScriptCallbacks().server_onPlayerPickedUpWeapon.AddUFunction( this, n"OnPlayerPickedupWeapon");

		// Mode
		ScriptCallbacks().server_OnBreacherPlanted.AddUFunction( this, n"OnBreacherPlanted" );
		ScriptCallbacks().server_onRaidAttackerWin.AddUFunction( this, n"OnRaidEnd_AttackerWin" );
		ScriptCallbacks().server_onRaidDefenderWin.AddUFunction( this, n"OnRaidEnd_DefenderWin" );

		GetServerScript().onWinnerDeterminedEvent.AddUFunction( this, n"OnWinnerDetermined" );
		ScriptCallbacks().server_OnOvertimeChanged.AddUFunction( this, n"OnOvertimeChanged" );

		ScriptCallbacks().server_onBombPlanted.AddUFunction( this, n"OnBombPlanted" );
		ScriptCallbacks().server_onBombDefused.AddUFunction( this, n"OnBombDefused" );
		ScriptCallbacks().server_onBombExplode.AddUFunction( this, n"OnBombExplode" );

		UNCDestructionManager::Get().OnDestructibleDestroyed_Server.AddUFunction( this, n"OnDestructibleDestroyed" );

		ScriptCallbacks().server_onWallRepaired.AddUFunction( this, n"OnWallRepaired" );
		ScriptCallbacks().server_onWallUpgraded.AddUFunction( this, n"OnWallUpgraded" );

		ScriptCallbacks().server_onVendorPurchase.AddUFunction( this, n"OnVendorPurchase" );
		ScriptCallbacks().server_onResourceGathered.AddUFunction( this, n"OnResourceGathered" );

		ScriptCallbacks().server_onRespawnTotemDenied.AddUFunction( this, n"OnRespawnTotemDenied" );
		ScriptCallbacks().server_onSpeedGateActivated.AddUFunction( this, n"OnSpeedGateUsed" );
		ScriptCallbacks().server_OnMiniShieldBreakerUsed.AddUFunction( this, n"OnSBExtenderUsed" );

		ScriptCallbacks().server_onMountDied.AddUFunction( this, n"OnMountDied" );
		ScriptCallbacks().server_onTimeBombPlanted.AddUFunction( this, n"OnTimeBombPlanted" );
		ScriptCallbacks().server_onPlayerUsedLootChest.AddUFunction( this, n"OnPlayerUsedLootChest" );
		ScriptCallbacks().server_onPlayerTookLootFromChest.AddUFunction( this, n"OnPlayerTookLootFromChest" );

		ScriptCallbacks().server_onRespawnTotemRevived.AddUFunction( this, n"OnPlayerRevived" );
		ScriptCallbacks().server_onMountStateChanged.AddUFunction( this, n"OnPlayerMountStateChanged" );
	}

	UFUNCTION()
	UNCPlayerStat GetPlayerStat( ANCPlayerCharacter playerState, FGameplayTag statID )
	{
		ScriptAssert( IsValid( playerState ), "Invalid player state" );
		UNCPlayerProgressionManagerComponent progMan = playerState.GetPlayerProgressionManagerComponent();
		return progMan.GetStat( statID );
	}

	UFUNCTION()
	float32 GetPlayerStatValue( ANCPlayerCharacter playerState, FGameplayTag statID )
	{
		return GetPlayerStat( playerState, statID ).Get();
	}

	UFUNCTION()
	float32 GetPlayerStatValueWithTag( ANCPlayerCharacter playerState, FGameplayTag statID, FGameplayTag statTag )
	{
		return GetPlayerStat( playerState, statID ).GetTaggedValue( statTag );
	}

	UFUNCTION()
	float32 GetExistingPlayerStatValue( ANCPlayerCharacter playerState, FGameplayTag statID )
	{
		ScriptAssert( IsValid( playerState ), "Invalid player state" );
		UNCPlayerProgressionManagerComponent progMan = playerState.GetPlayerProgressionManagerComponent();
		return progMan.GetExistingStatValue( statID );
	}

	private void IncrementPlayerStat( const ANCPlayerCharacter playerState, FGameplayTag statID, int amount = 1, FGameplayTag statTag = FGameplayTag() )
	{
		FChallengeTrackingTags challengeTags;
		challengeTags.Character = GetCharacterTag( playerState );
		// challengeTags.CharacterCategory = FGameplayTag();
		// challengeTags.Weapon  = GameplayTags::Weapons_Primary_Burst_Rifle;
		// challengeTags.WeaponCategory  = GameplayTags::WeaponCategories_Shotgun;

		IncrementPlayerStat( playerState, statID, challengeTags, amount, statTag );
	}

	private void IncrementPlayerStat( const ANCPlayerCharacter playerState, FGameplayTag statID, const FDamageInfo& damageInfo, int amount = 1, FGameplayTag statTag = FGameplayTag() )
	{
		FChallengeTrackingTags challengeTags;
		challengeTags.Character = GetCharacterTag( playerState );

		// TODO : classes need gameplay tags for categories
		// FClassDataStruct data = Classes().GetClassData( challengeTags.Character );
		// challengeTags.CharacterCategory = data.classArchetype; //FGameplayTag();

		challengeTags.GameMode = GetCurrentModeTag();

		// HACK
		FName weaponClassName = damageInfo.GetDamageSourceName();

		if ( IsValidWeaponClassName( weaponClassName ) )
		{
			UWeaponPrimaryAsset weaponClass		  = GetWeaponClassFromClassName( weaponClassName );
			const FNCWeaponPermutationId weaponId = MakeWeaponId( weaponClass, 0 );
			// Axe does not have valid loot
			if ( IsLootIndexValidForPrimaryWeapon( weaponId ) )
			{
				FLootDataStruct weaponData = GetLootDataForWeapon( MakeWeaponId( weaponClass, 0 ) );
				if ( IsLootIndexValid( weaponData.index ) )
				{
					challengeTags.Weapon					  = weaponData.referencedItemIndex;
					FWeaponLoadoutDataStruct weaponScriptData = GetWeaponScriptData( weaponData.referencedItemIndex );
					challengeTags.WeaponCategory			  = weaponScriptData.category;
				}
			}
		}

		IncrementPlayerStat( playerState, statID, challengeTags, amount, statTag );
	}

	private void IncrementPlayerStat( const ANCPlayerCharacter playerState, FGameplayTag statID, FChallengeTrackingTags challengeTags, int amount, FGameplayTag statTag )
	{
		ScriptAssert( IsValid( playerState ), "Invalid player state" );
		UNCPlayerProgressionManagerComponent progMan = playerState.GetPlayerProgressionManagerComponent();
		UNCPlayerStat stat							 = progMan.GetStat( statID );

		if ( statTag.IsValid() )
			stat.LocalTag = statTag;

		stat.ChallengeTags = challengeTags;

		stat.Increment( amount );

		AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( playerState );
		if ( player.matchEndStats.Contains( statID ) )
		{
			player.matchEndStats[statID] += amount;
		}
	}

	UFUNCTION()
	void IncrementPlayerStatForTeam( int team, FGameplayTag statID, int amount = 1, FGameplayTag statTag = FGameplayTag() )
	{
		if ( !IsPlayerStatsEnabled() )
		{
			return;
		}

		for ( ANCPlayerCharacter player : GetPlayersOfTeam( team ) )
			IncrementPlayerStat( player, statID );
	}

	void IncrementCharacterStatForPlayer( const ANCPlayerCharacter playerState, FGameplayTag characterIndex, FGameplayTag statID, int amount = 1 )
	{
		if ( !IsPlayerStatsEnabled() )
		{
			return;
		}

		IncrementPlayerStat( playerState, statID, FDamageInfo(), amount, FGameplayTag() );
	}

	UFUNCTION()
	FGameplayTag GetCharacterTag( const ANCPlayerCharacter playerState )
	{
		ScriptAssert( IsValid( playerState ), "Invalid player state" );
		return cachedPlayerData[playerState].classIndex;
	}

	UFUNCTION()
	private void UpdateAccountLevelAndRewards( const ANCPlayerCharacter playerState )
	{
		USh_ProgressionGameSystem progGameSys		 = ProgressionGameSystem();
		UNCPlayerProgressionManagerComponent progMan = playerState.GetPlayerProgressionManagerComponent();
		UNCPlayerStat matchStats					 = progMan.GetStat( GameplayTags::Progression_PlayerStats_MatchStats );

		for ( auto statWithTag : matchStats.LocalTaggedValues )
		{
			FMatchStatEventData eventData = progGameSys.GetMatchStatEventData( statWithTag.Key );

			for ( TMapIterator<FGameplayTag, int> reward : eventData.rewardCurrency )
				progMan.AddCurrencyReward( reward.Key, reward.Value * int( statWithTag.Value ) );
		}
	}

	UFUNCTION()
	void EndMatchForPlayer( ANCPlayerCharacter player, bool matchCompleted )
	{
		if ( !IsPlayerStatsEnabled() )
		{
			return;
		}

		if ( !player.IsProgressionDataGoodForUpload() )
		{
			Log( f"LogNCPlayerProgression: Skipping progression data upload for player {player.GetPlayerNameAsString()} as their data is either unavailable or already uploaded." );
			return;
		}

		FString gameMode = f"Raid Mode";
		FString mapName	 = GetMapName();
		if ( matchCompleted )
		{
			IncrementPlayerStat( player, GameplayTags::Progression_PlayerStats_GamesPlayed );
		}

		int placement = GetWinner() == player.GetTeam() ? 0 : 1;
		player.SubmitPlayerInfoForMatchEnd( gameMode, mapName, placement );
	}

	UFUNCTION()
	private void OnPlayerFirstJoined( ANCPlayerCharacter player )
	{
		if ( !IsValid( player ) )
			return;

		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( player );
		InitializeCachedPlayerData( asPlayer );

		asPlayer.Server_OnPawnDeath.AddUFunction( this, n"OnPlayerDeath" );
		asPlayer.HealthComponent.BP_OnPostReceivedDamage.AddUFunction( this, n"OnPlayerDamaged" );
		asPlayer.classManager.server_onClassIndexChangedCallback.AddUFunction( this, n"OnClassChanged" );

		UAS_ShieldHealthComponent shieldComp = Cast<UAS_ShieldHealthComponent>(asPlayer.HealthComponent);
		shieldComp.server_onPlayerShieldChanged.AddUFunction( this, n"OnPlayerShieldChanged" );

		UNCPlayerProgressionManagerComponent progMan = asPlayer.GetPlayerProgressionManagerComponent();
		progMan.Initialize( ProgressionGameSystem().GetPlayerStatDefinitions() );
		progMan.OnPlayerStatsAndChallengesResolved.AddUFunction( this, n"UpdateAccountLevelAndRewards" );
	}

	private void InitializeCachedPlayerData( AAS_PlayerEntity player )
	{
		if ( cachedPlayerData.Contains( player ) )
			return; // Error?

		FCachedPlayerData data;
		data.classIndex = GameConst::INVALID_CLASS;
		cachedPlayerData.Add( player, data );
	}

	UFUNCTION()
	private void OnPlayerReconnected( ANCPlayerCharacter player )
	{
		if ( !IsValid( player ) )
			return;

		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( player );
		asPlayer.Server_OnPawnDeath.AddUFunction( this, n"OnPlayerDeath" );
		asPlayer.HealthComponent.BP_OnPostReceivedDamage.AddUFunction( this, n"OnPlayerDamaged" );
	}

	UFUNCTION()
	private void OnPlayerDisconnected( ANCPlayerCharacter player )
	{
		if ( !IsValid( player ) )
			return;

		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( player );
		asPlayer.Server_OnPawnDeath.UnbindObject( this );
		asPlayer.HealthComponent.BP_OnPostReceivedDamage.UnbindObject( this );
	}

	UFUNCTION()
	private void OnPlayerDestroyed( ANCPlayerCharacter player, EEndPlayReason EndPlayReason )
	{
		if ( player.IsProgressionDataUploading() )
		{
			ScriptError_Silent_WithBug( f"NCPlayerProgression: Upload timed out", f"rramaswamy", f"Player '{player.GetPlayerNameAsString()}' was destroyed while progression data was uploading" );
		}
	}

	UFUNCTION()
	private void OnAssist( ANCPlayerCharacter assistAttacker, ANCPlayerCharacter victim,
				   const FDamageInfo&in damageInfo )
	{
		IncrementPlayerStat( assistAttacker, GameplayTags::Progression_PlayerStats_Assists );
	}

	UFUNCTION()
	private void OnPlayerDeath( const FDamageInfo&in damageInfo, ANCPlayerCharacter victim )
	{
		// Only count kills during gameplay
		if ( GetGamePhase() != GamePhase::PLAYING )
			return;

		if ( !IsValid( victim ) )
			return;

		if( cachedPlayerData.Contains( victim ) )
			cachedPlayerData[victim].hasDied = true;

		IncrementPlayerStat( victim, GameplayTags::Progression_PlayerStats_Deaths );

		ANCPlayerCharacter killer = damageInfo.attacker;
		if ( killer != nullptr && killer != victim && killer.GetTeam() != victim.GetTeam() )
		{
			IncrementPlayerStat( killer, GameplayTags::Progression_PlayerStats_Kills, damageInfo );

			if( killer.IsPlayerRidingMount() )
				IncrementPlayerStat( killer, GameplayTags::Progression_PlayerStats_Kills_WhileMounted, damageInfo );

			FName damageSource = damageInfo.GetDamageSourceName();

			if ( damageSource == n"Weap_UltDaggers" )
			{
				IncrementCharacterStatForPlayer( killer, GameplayTags::Classes_Class_Skye, GameplayTags::Progression_PlayerStats_Skye_DaggerKills );
			}

			if ( IsWeaponLoaded( damageSource ) )
			{
				FNCWeaponPermutationId id = MakeWeaponId( GetWeaponAsset( damageSource ), damageInfo.weaponModBitField );

				if ( IsLootIndexValidForPrimaryWeapon( id ) )
				{
					FLootDataStruct lootdata = GetLootDataForWeapon( id );

					if ( lootdata.index == GameplayTags::Loot_Weapon_NovaShotgun_Doubleshot ) // HACK? Maybe we should have a more formal way of doing these types of things
					{
						IncrementPlayerStat( killer, GameplayTags::Progression_PlayerStats_Kills_WithKrakenDoubleShot, damageInfo );
					}

					if ( lootdata.rarity == GameplayTags::Loot_Rarity_Extraordinary )
					{
						IncrementPlayerStat( killer, GameplayTags::Progression_PlayerStats_Kills_WithOrangeGun );
					}
				}
			}
		}
	}

	UFUNCTION()
	private void OnPlayerDamaged( UHealthComponent damagedComponent, const FDamageInfo&in damageInfo )
	{
		ANCPlayerCharacter attacker = damageInfo.attacker;
		if ( IsValid( attacker ) && attacker != damagedComponent.GetOwner() )
		{
			IncrementPlayerStat( attacker, GameplayTags::Progression_PlayerStats_DamageDealt, damageInfo, int( damageInfo.damage ) );

			if ( Bitflags::HasFlag( damageInfo.damageFlags, EDamageFlags::DF_HEADSHOT ) )
				IncrementPlayerStat( attacker, GameplayTags::Progression_PlayerStats_Headshots, damageInfo );

			FName damageSource = damageInfo.GetDamageSourceName();

			if ( damageSource == n"Weap_LightningRod" )
			{
				IncrementCharacterStatForPlayer( attacker, GameplayTags::Classes_Class_Atticus, GameplayTags::Progression_PlayerStats_Atticus_LightningSpearDamage, int( damageInfo.damage ) );
			}
			else if ( damageSource == n"Fire" )
			{
				IncrementCharacterStatForPlayer( attacker, GameplayTags::Classes_Class_Slade, GameplayTags::Progression_PlayerStats_Slade_FireDamage, int( damageInfo.damage ) );
			}
		}
	}

	UFUNCTION()
	private void OnClassChanged( AAS_PlayerEntity player, FGameplayTag newClass )
	{
		if ( cachedPlayerData.Contains( player ) )
			cachedPlayerData[player].classIndex = newClass;
	}

	UFUNCTION()
	private void OnPlayerShieldChanged(AAS_PlayerEntity ownerPlayer)
	{
		CheckForLegendaryAchievement( ownerPlayer );
	}

	UFUNCTION()
	void AddMatchStatForTeam( int team, FGameplayTag statTag )
	{
		if ( !IsPlayerStatsEnabled() )
		{
			return;
		}

		for ( ANCPlayerCharacter player : GetPlayersOfTeam( team ) )
			AddMatchStatForPlayer( player, statTag );
	}

	UFUNCTION()
	void AddMatchStatForPlayer( ANCPlayerCharacter player, FGameplayTag statTag )
	{
		if ( !IsPlayerStatsEnabled() )
		{
			return;
		}

		USh_ProgressionGameSystem progGameSys = ProgressionGameSystem();
		if ( !IsValid( progGameSys ) )
			return;

		if ( !progGameSys.IsValidMatchStatEvent( statTag ) )
			return;

		FMatchStatEventData eventData = progGameSys.GetMatchStatEventData( statTag );
		IncrementPlayerStat( player, GameplayTags::Progression_PlayerStats_MatchStats, 1, eventData.matchStat );
	}

	UFUNCTION()
	private void OnBreacherPlanted( ANCPlayerCharacter planter, AAS_ShieldBreaker shieldBreaker, AAS_RaidDomeShield dome )
	{
		IncrementPlayerStat( planter, GameplayTags::Progression_PlayerStats_ShieldBreakerPlanted );
	}

	UFUNCTION()
	private void OnRaidEnd_AttackerWin( int attackerTeam, int defenderTeam )
	{
	}

	UFUNCTION()
	private void OnRaidEnd_DefenderWin( int attackerTeam, int defenderTeam )
	{
	}

	UFUNCTION()
	private void OnWinnerDetermined( int winningTeam )
	{
		// Loop over winning team to find is 1 player has survived the whole match, then loop over team again to give stats/entitlements
		bool hasAnyoneNotDied = false;
		for ( ANCPlayerCharacter player : GetPlayersOfTeam( winningTeam ) )
		{
			if( cachedPlayerData.Contains( player ) && !cachedPlayerData[player].hasDied )
			{
				hasAnyoneNotDied = true;
				break;
			}
		}

		const bool isUnder10Minutes = GetPlayingTimeMS() < TO_MILLISECONDS( 60.0f * 10.0f );
		const bool isUnder12Minutes = GetPlayingTimeMS() < TO_MILLISECONDS( 60.0f * 12.0f );

		for ( ANCPlayerCharacter player : GetPlayersOfTeam( winningTeam ) )
		{
			IncrementPlayerStat( player, GameplayTags::Progression_PlayerStats_GamesWon );

			if( !hasEnteredOvertime )
				IncrementPlayerStat( player, GameplayTags::Progression_PlayerStats_GamesWon_WithoutOvertime );

			if( isUnder10Minutes )
				IncrementPlayerStat( player, GameplayTags::Progression_PlayerStats_GamesWon_UnderTenMinutes );

			UNCPlayerProgressionManagerComponent progMan = player.GetPlayerProgressionManagerComponent();

			if( cachedPlayerData.Contains(player) && cachedPlayerData[player].vesperSpent >= BREAK_THE_BANK_AMOUNT )
				progMan.AddEntitlementReward( GameplayTags::Achievements_Ids_BreakTheBank );
			
			if( isUnder12Minutes && hasAnyoneNotDied )
				progMan.AddEntitlementReward( GameplayTags::Achievements_Ids_KingsHand );
		}

		for ( ANCPlayerCharacter player : GetAllPlayers() )
			SpawnStatActorForPlayer( player );
	}

	UFUNCTION()
	private void OnOvertimeChanged(bool oldValue, bool newValue)
	{
		if( newValue )
			hasEnteredOvertime = true;
	}

	void SpawnStatActorForPlayer( ANCPlayerCharacter PS )
	{
		AAS_PlayerEntity player			   = Cast<AAS_PlayerEntity>( PS );
		AAS_MatchEndStatsCarrier statActor = GetStatActorForPlayer( player );
		if ( !IsValid( statActor ) )
			statActor = Cast<AAS_MatchEndStatsCarrier>( Server_SpawnEntity( AAS_MatchEndStatsCarrier::StaticClass(), player ) );

		statActor.net_kills.SetNetValue( player.matchEndStats[GameplayTags::Progression_PlayerStats_Kills] );
		statActor.net_revives.SetNetValue( player.matchEndStats[GameplayTags::Progression_PlayerStats_Revives] );
		statActor.net_statValue_0.SetNetValue( player.matchEndStats[GameModeDefaults().matchEndStat0] );
		statActor.net_statValue_1.SetNetValue( player.matchEndStats[GameModeDefaults().matchEndStat1] );
		statActor.net_statValue_2.SetNetValue( player.matchEndStats[GameModeDefaults().matchEndStat2] );

		statActor.SetActorSendFlags( ESendEventFlags::SEND_TO_OWNER | ESendEventFlags::SEND_TO_FRIENDLIES, player.GetEntityId() );
	}

	UFUNCTION()
	private void OnBombPlanted( UAS_RaidBombInterfaceComponent bombInterface, AAS_RaidBomb bomb,
						ANCPlayerCharacter planterOrDefuser )
	{
		AAS_BaseSubObjective generator = Cast<AAS_BaseSubObjective>( bombInterface.GetOwner() );
		if ( IsValid( generator ) )
		{
			IncrementPlayerStat( planterOrDefuser, GameplayTags::Progression_PlayerStats_GeneratorsSabotaged );
		}
	}

	UFUNCTION()
	private void OnBombDefused( UAS_RaidBombInterfaceComponent bombInterface, AAS_RaidBomb bomb,
						ANCPlayerCharacter planterOrDefuser )
	{
		AAS_BaseSubObjective generator = Cast<AAS_BaseSubObjective>( bombInterface.GetOwner() );
		if ( IsValid( generator ) && IsValid( planterOrDefuser ) )
		{
			IncrementPlayerStat( planterOrDefuser, GameplayTags::Progression_PlayerStats_GeneratorsDefused );
		}
	}

	UFUNCTION()
	private void OnBombExplode( UAS_RaidBombInterfaceComponent bombInterface, int attackerTeam,
						int defenderTeam )
	{
		AAS_RaidBomb bomb			   = bombInterface.GetBomb();
		ANCPlayerCharacter planter	   = bomb.bombPlanter;
		AAS_BaseSubObjective generator = Cast<AAS_BaseSubObjective>( bombInterface.GetOwner() );
		if ( IsValid( generator ) )
		{
			IncrementPlayerStat( planter, GameplayTags::Progression_PlayerStats_GeneratorsDestroyed );
		}
		AAS_BaseVault vault = Cast<AAS_BaseVault>( bombInterface.GetOwner() );
		if ( IsValid( vault ) )
		{
			IncrementPlayerStat( planter, GameplayTags::Progression_PlayerStats_AnchorStoneDestroyed );

			if( !GetTeamStateManager( defenderTeam ).GetBase().HasAnyDestroyedGenerators() )
				IncrementPlayerStat( planter, GameplayTags::Progression_PlayerStats_GamesWon_OnlyAnchorStone );
		}
	}

	bool IsValidDestructibleForStats( ANCDestructible destructible )
	{
		TArray<AActor> linkedActors = destructible.GetLinkedActors();
		if ( !linkedActors.IsEmpty() )
		{
			for ( AActor linkedActor : linkedActors )
			{
				ANCDoor actorAsDoor				  = Cast<ANCDoor>( linkedActor );
				ANCDoorManager actorAsDoorManager = Cast<ANCDoorManager>( linkedActor );
				if ( !IsValid( actorAsDoor ) && !IsValid( actorAsDoorManager ) )
				{
					continue;
				}

				ANCDoorManager manager = actorAsDoorManager;
				if ( IsValid( actorAsDoor ) )
				{
					manager = actorAsDoor.GetManager();
				}

				if ( !IsValid( manager ) )
				{
					continue;
				}

				return ( destructible == manager.GetPrimaryDestructible() );
			}

			return false;
		}

		return true;
	}

	UFUNCTION()
	private void OnDestructibleDestroyed( FNCDestructibleDestroyedContext_Server destructionContext )
	{
		AAS_PlayerEntity destroyer = Cast<AAS_PlayerEntity>( destructionContext.Attacker );
		int dTeam				   = destructionContext.Destructible.GetTeam();

		if ( !IsValidDestructibleForStats( destructionContext.Destructible ) )
			return;

		if ( IsValid( destroyer ) && dTeam != destroyer.GetTeam() )
		{
			IncrementPlayerStat( destroyer, GameplayTags::Progression_PlayerStats_WallsDestroyed, destructionContext.DamageInfo );

			if ( destructionContext.Destructible.IsDestructibleCategorySet() )
			{
				if ( destructionContext.Destructible.GetDestructibleCategory().DestructibleCategoryID == GameplayTags::Destruction_Category_Iron )
				{
					IncrementPlayerStat( destroyer, GameplayTags::Progression_PlayerStats_UpgradedWallsDestroyed, destructionContext.DamageInfo );
				}
			}

			if ( destructionContext.DamageInfo.damageWeaponClassName == n"Weap_RaidGolemSmash" )
			{
				IncrementCharacterStatForPlayer( destroyer, GameplayTags::Classes_Class_Jade, GameplayTags::Progression_PlayerStats_Jade_DemonWallsDestroyed );
			}
			else if ( destructionContext.DamageInfo.damageWeaponClassName == n"Weap_Claw_New" )
			{
				IncrementCharacterStatForPlayer( destroyer, GameplayTags::Classes_Class_Redmane, GameplayTags::Progression_PlayerStats_Redmane_ClawDestroyWalls );
			}
			else if ( destructionContext.DamageInfo.damageWeaponClassName == n"Weap_RaidHammer" )
			{
				FNCWeaponPermutationId id = MakeWeaponId( GetWeaponAsset( destructionContext.DamageInfo.damageWeaponClassName ), destructionContext.DamageInfo.weaponModBitField );
				FLootDataStruct lootdata  = GetLootDataForWeapon( id );
				if ( lootdata.index == GameplayTags::Loot_Weapon_RaidHammerSmoke || lootdata.index == GameplayTags::Loot_Weapon_RaidHammerTronWall )
				{
					IncrementPlayerStat( destroyer, GameplayTags::Progression_PlayerStats_WallsDestroyed_WithPurpleHammers );
				}
			}
		}
	}

	UFUNCTION()
	private void OnWallRepaired( ANCPlayerCharacter player, ANCDestructible destructible )
	{
		IncrementPlayerStat( player, GameplayTags::Progression_PlayerStats_WallsRepaired );
	}

	UFUNCTION()
	private void OnWallUpgraded( ANCPlayerCharacter player, ANCDestructible destructible )
	{
		IncrementPlayerStat( player, GameplayTags::Progression_PlayerStats_WallsUpgraded );
	}

	UFUNCTION()
	private void OnVendorPurchase( AAS_VendorStoreData vendor, FVendorData storeItem,
						   ANCPlayerCharacter player, FBackpackItemStruct item )
	{
		// Track total vesper spent
		int totalVesperCost	   = 0;
		FGameplayTag vesperTag = GameplayTags::Loot_Resource_Opal;
		for ( FBackpackItemStruct costItem : storeItem.cost )
		{
			if ( costItem.itemIndex == vesperTag )
			{
				totalVesperCost += costItem.itemCount;
			}
		}

		if ( totalVesperCost > 0 )
		{
			if( cachedPlayerData.Contains(player) )
				cachedPlayerData[player].vesperSpent += totalVesperCost;

			IncrementPlayerStat( player, GameplayTags::Progression_PlayerStats_VesperSpent, totalVesperCost );
		}

		//Track rare items purchased
		FLootDataStruct lootData = GetLootDataByIndex( item.itemIndex );
		if( GetLootRarityLevel( lootData ) >= GetLootRarityLevel( GameplayTags::Loot_Rarity_Rare ) )
		{
			if( cachedPlayerData.Contains(player) )
			{
				int purchases = cachedPlayerData[player].rareItemsPurchased + 1;
				if( purchases >= POCKET_OF_VESPER_AMOUNT )
				{
					UNCPlayerProgressionManagerComponent progMan = player.GetPlayerProgressionManagerComponent();
					progMan.AddEntitlementReward( GameplayTags::Achievements_Ids_PocketFullofVesper );
				}
				
				cachedPlayerData[player].rareItemsPurchased = purchases;
			}
		}
	}

	UFUNCTION()
	private void OnResourceGathered( ANCPlayerCharacter player, AAS_ResourceNode resourceNode,
							 FGameplayTag resource, int amount )
	{
		FGameplayTag vesperTag = GameplayTags::Loot_Resource_Opal;
		if ( resource == vesperTag )
		{
			PlayerStatsManager().IncrementPlayerStat( player, GameplayTags::Progression_PlayerStats_VesperHarvested, amount );
		}
	}

	UFUNCTION()
	private void OnRespawnTotemDenied( AAS_RespawnTotem totem, AAS_PlayerEntity player )
	{
		IncrementPlayerStat( player, GameplayTags::Progression_PlayerStats_SoulOrbsDestroyed );
	}

	UFUNCTION()
	private void OnSpeedGateUsed( AAS_SpeedGate gate, ANCPlayerCharacter player )
	{
		IncrementPlayerStat( player, GameplayTags::Progression_PlayerStats_SpeedGatesUsed );
	}

	UFUNCTION()
	private void OnSBExtenderUsed( ANCPlayerCharacter player, AAS_MiniShieldBreaker extender,
						   AAS_RaidDomeShield dome )
	{
		IncrementPlayerStat( player, GameplayTags::Progression_PlayerStats_DomeSlicersUsed );
	}

	UFUNCTION()
	private void OnMountDied( ANCPlayerCharacter attacker, AAS_VehicleMount mount )
	{
		if ( IsValid( attacker ) && attacker != mount.GetOwnerPlayer() )
			IncrementPlayerStat( attacker, GameplayTags::Progression_PlayerStats_Dismounts );
	}

	UFUNCTION()
	private void OnTimeBombPlanted( ANCPlayerCharacter player, AAS_TimeBomb_DamageCore timeBomb )
	{
		IncrementPlayerStat( player, GameplayTags::Progression_PlayerStats_TimeBombsUsed );
	}

	UFUNCTION()
	private void OnPlayerUsedLootChest( AAS_DisplayedLootChest chest, ANCPlayerCharacter playerUser )
	{
		IncrementPlayerStat( playerUser, GameplayTags::Progression_PlayerStats_ChestsOpened );
	}

	UFUNCTION()
	private void OnPlayerTookLootFromChest( ANCPlayerCharacter player, AAS_DisplayedLootChest box,
									AAS_LootEntity loot )
	{
		if ( loot.GetLootData().index == GameplayTags::Loot_Armor_Level3 ) // purple
		{
			IncrementPlayerStat( player, GameplayTags::Progression_PlayerStats_PurpleArmorPickedup );
		}
	}

	UFUNCTION()
	private void OnPlayerRevived( AAS_RespawnTotem totem, AAS_PlayerEntity player )
	{
		IncrementPlayerStat( player, GameplayTags::Progression_PlayerStats_Revives );
	}

	UFUNCTION()
	private void OnPlayerMountStateChanged( ANCPlayerCharacter player, AAS_VehicleMount mount,
									bool isRiding )
	{
		if ( !isRiding )
		{
			int lastRideDistance = int( player.LastRideDistance );
			if ( lastRideDistance > 0 )
				IncrementPlayerStat( player, GameplayTags::Progression_PlayerStats_DistanceRidden, lastRideDistance );
		}
	}

	void IncrementUltActivatedStat( AAS_PlayerEntity player )
	{
		if ( !IsPlayerStatsEnabled() )
		{
			return;
		}

		IncrementPlayerStat( player, GameplayTags::Progression_PlayerStats_UltsActivated );
	}

	UFUNCTION()
	private void OnPlayerEquipmentChanged(AAS_PlayerEntity ownerPlayer,
	                                      FPlayerEquipmentData newEquipmentData)
	{
		CheckForLegendaryAchievement( ownerPlayer );
	}

	UFUNCTION()
	private void OnPlayerPickedupWeapon(ANCPlayerCharacter player, FBackpackItemStruct item, int slot)
	{
		CheckForLegendaryAchievement( player );
	}

	private void CheckForLegendaryAchievement( ANCPlayerCharacter player )
	{
		int legendaryCount = 0;
		TArray<int> slotsToCheck;
		slotsToCheck.Add( WeaponSlot::PrimarySlot0 );
		slotsToCheck.Add( WeaponSlot::PrimarySlot1 );
		slotsToCheck.Add( WeaponSlot::RaidToolsSlot );

		for( int slot : slotsToCheck )
		{
			ANCWeapon weapon = player.GetWeaponAtSlot( slot );
			if( !IsValid( weapon ) )
				continue;

			FLootDataStruct lootData = GetLootDataForWeapon( weapon );
			if( GetLootRarityLevel( lootData ) >= GetLootRarityLevel( GameplayTags::Loot_Rarity_Extraordinary ) )
				legendaryCount++;
		}

		FShieldItemData equippedShield = player.GetEquippedShieldData();
		if( GetLootRarityLevel( equippedShield.shieldRarity ) >= GetLootRarityLevel( GameplayTags::Loot_Rarity_Extraordinary ) )
			legendaryCount++;
		
		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( player );
		if( IsValid( asPlayer ) )
		{
			if( IsLegendaryEquipment( asPlayer.helmetEquipmentComponent) )
				legendaryCount++;

			if( IsLegendaryEquipment( asPlayer.saddleEquipmentComponent) )
				legendaryCount++;

			if( IsLegendaryEquipment( asPlayer.trinketEquipmentComponent) )
				legendaryCount++;
		}

		if( legendaryCount > 3 )
		{
			UNCPlayerProgressionManagerComponent progMan = player.GetPlayerProgressionManagerComponent();
			progMan.AddEntitlementReward( GameplayTags::Achievements_Ids_LegendaryArmanments );
		}
	}

	private bool IsLegendaryEquipment( UAS_PlayerEquipmentComponent equipComponent )
	{
		FGameplayTag lootTag = equipComponent.GetCurrentEquipmentData().lootTag;
		if( !IsLootIndexValid( lootTag ) )
			return false;

		FLootDataStruct lootData = GetLootDataByIndex( lootTag );
		return GetLootRarityLevel( lootData.rarity ) >= GetLootRarityLevel( GameplayTags::Loot_Rarity_Extraordinary );
	}

	UFUNCTION()
	void OnOvertimeTeamwipe( int aliveTeam )
	{
		PlayerStatsManager().IncrementPlayerStatForTeam( aliveTeam, GameplayTags::Progression_PlayerStats_OvertimeTeamWipe );
		CheckForLockedInAchievement( aliveTeam );
	}

	// Assumes this is on overtime teamwipe
	private void CheckForLockedInAchievement( int aliveTeam )
	{
		ANCPlayerCharacter lastPlayerAlive = nullptr;
		TArray<ANCPlayerCharacter> plantingTeamPlayers = GetPlayersOfTeam( aliveTeam );
		for( ANCPlayerCharacter player : plantingTeamPlayers )
		{
			if( IsAlive( player ) )
			{
				if( !IsValid( lastPlayerAlive ) )
					lastPlayerAlive = player;
				else
					return; // multiple alive players
			}
		}

		if( IsValid( lastPlayerAlive ) )
		{
			UNCPlayerProgressionManagerComponent progMan = lastPlayerAlive.GetPlayerProgressionManagerComponent();
			progMan.AddEntitlementReward( GameplayTags::Achievements_Ids_LockedIn );
		}
	}
}
