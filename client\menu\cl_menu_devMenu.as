event void FDevMenuButtonBinding( UNCButtonWidget button, FString argument );

UCLASS( Abstract )
class UAS_DevMenu : UAS_MenuWidget
{
	// Tab Header or "rail" that contains the clickable tabs we're working with
	UPROPERTY( BlueprintReadOnly, EditDefaultsOnly, Meta = ( BindWidget ), Category = "NC|Tabbed Inventory|Widgets" )
	UAS_TabWidget tabWidget;

	UFUNCTION( BlueprintOverride )
	void OnActivated()
	{
		Super::OnActivated();

		AAS_HUD hud = GetLocalHUD();
		if ( IsValid( hud ) )
			hud.OnKeyDown.AddUFunction( this, n"OnKeyDownEvent" );
	}

	UFUNCTION()
	private void OnKeyDownEvent( FKey Key )
	{
		if ( Key == EKeys::Zero )
		{
			System::SetTimer( this, n"DelayedClose", 0.1, false );
		}
	}

	UFUNCTION( BlueprintOverride )
	void OnClosed()
	{
		Super::OnClosed();

		AAS_HUD hud = GetLocalHUD();
		if ( IsValid( hud ) )
			hud.OnKeyDown.UnbindObject( this );
	}

	UFUNCTION()
	void DelayedClose()
	{
		AAS_HUD hud = GetLocalHUD();
		if ( IsValid( hud ) )
			hud.GetUIManager().NavigateBack();
	}
}

UCLASS( Abstract )
class UAS_DevMenuPanel : UAS_MenuWidget
{
	UPROPERTY( NotVisible, BlueprintReadOnly, BindWidget )
	UHorizontalBox sectionContainer;

	TArray<UAS_ButtonWidget> dynamicButtons;
	TMap<UNCButtonWidget, FString> buttonToArgMap;
	TMap<UNCButtonWidget, FDevMenuButtonBinding> buttonToBindingMap;

	UFUNCTION( NotBlueprintCallable )
	protected void ToggleCVar( UNCButtonWidget Button, FString argument )
	{
		bool cVarActive = GetCvarBool( argument );
		int newValue	= cVarActive ? 0 : 1;
		FString command = f"{argument} {newValue}";
		Print( command );
		System::ExecuteConsoleCommand( command );
	}

	UFUNCTION( NotBlueprintCallable )
	protected void ToggleCVarOnServer( UNCButtonWidget Button, FString argument )
	{
		ToggleCVar( Button, argument );

		bool cVarActive = GetCvarBool( argument );
		int currentValue	= cVarActive ? 1 : 0;

		FString command = f"ClientCommand SetCVar {argument} {currentValue}";
		Print( command );
		System::ExecuteConsoleCommand( command );
	}

	UFUNCTION( NotBlueprintCallable )
	protected void BasicConsoleCommand( UNCButtonWidget Button, FString argument )
	{
		Print( argument );
		System::ExecuteConsoleCommand( argument );
	}

	UFUNCTION( NotBlueprintCallable )
	private void __ButtonFunction( UNCButtonWidget Button )
	{
		FString argument			  = buttonToArgMap[Button];
		FDevMenuButtonBinding binding = buttonToBindingMap[Button];
		binding.Broadcast( Button, argument );
		DevMenuCheatRecorded( Button );
	}

	UAS_ButtonWidget CreateDynamicButton_ConsoleCommand( UPanelWidget panel, FString buttonText, FString argument = "" )
	{
		return CreateDynamicButton( panel, buttonText, n"BasicConsoleCommand", argument );
	}

	UAS_ButtonWidget CreateDynamicButton_ToggleCVar_OnServer( UPanelWidget panel, FString buttonText, FString argument = "" )
	{
		return CreateDynamicButton( panel, buttonText, n"ToggleCVarOnServer", argument );
	}

	UAS_ButtonWidget CreateDynamicButton_ToggleCVar_ClientOnly( UPanelWidget panel, FString buttonText, FString argument = "" )
	{
		return CreateDynamicButton( panel, buttonText, n"ToggleCVar", argument );
	}

	UAS_ButtonWidget CreateDynamicButton( UPanelWidget panel, FString buttonText, FName buttonFunction, FString argument = "" )
	{
		UAS_TextButtonWidget button = Cast<UAS_TextButtonWidget>( WidgetBlueprint::CreateWidget( DoNotShipGlobals().devMenuButtonClass, GetOwningPlayer() ) );
		button.SetPadding( FMargin( 0, 20, 0, 0 ) );
		button.OnButtonClicked.AddUFunction( this, n"__ButtonFunction" );

		panel.AddChild( button );

		dynamicButtons.Add( button );
		buttonToArgMap.Add( button, argument );

		FDevMenuButtonBinding binding;
		binding.AddUFunction( this, buttonFunction );
		buttonToBindingMap.Add( button, binding );

		button.SetBaseButtonText( Localization::GetUnlocalizedTextFromString( buttonText ) );
		return button;
	}

	UFUNCTION()
	protected void DevMenuCheatRecorded( UNCButtonWidget button )
	{
		Client_EmitSoundUI( Audio().ui_default_select_sound );
	}

	UAS_DevMenuColumn CreateColumn( FText title )
	{
		TSubclassOf<UAS_DevMenuColumn> columnClass = System::LoadClassAsset_Blocking(MenuGlobals().devMenu_columnClass);
		UAS_DevMenuColumn col = Cast<UAS_DevMenuColumn>( WidgetBlueprint::CreateWidget( MenuGlobals().devMenu_columnClass.Get(), GetOwningPlayer() ) );
		sectionContainer.AddChild( col );
		col.titleText.SetText( title );
		UHorizontalBoxSlot slot	 = Cast<UHorizontalBoxSlot>( col.Slot );
		slot.VerticalAlignment	 = EVerticalAlignment::VAlign_Fill;
		slot.HorizontalAlignment = EHorizontalAlignment::HAlign_Fill;
		FMargin margin;
		margin.Top	 = -100;
		slot.Padding = margin;

		FSlateChildSize size;
		size.SizeRule = ESlateSizeRule::Fill;
		size.Value	  = 0.2;
		slot.Size	  = size;
		return col;
	}
}

UCLASS( Abstract )
class UAS_DevMenuColumn : UUserWidgetDefault
{
	UPROPERTY( NotVisible, BindWidget )
	UVerticalBox buttonContainer;

	UPROPERTY( NotVisible, BindWidget )
	UTextBlock titleText;
}

UCLASS( Abstract )
class UAS_DevMenuCommon : UAS_DevMenuPanel
{
	UPROPERTY( NotVisible, BlueprintReadOnly, BindWidget )
	UPanelWidget cheatCommands;

	UPROPERTY( NotVisible, BlueprintReadOnly, BindWidget )
	UPanelWidget playerCommands;

	UPROPERTY( NotVisible, BlueprintReadOnly, BindWidget )
	UPanelWidget debugCommands;

	UPROPERTY( NotVisible, BlueprintReadOnly, BindWidget )
	UPanelWidget botCommands;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		CreateDynamicButton_ConsoleCommand( cheatCommands, "Cancel Auto End Match", "ClientCommand CancelAutoEndMatch" );
		CreateDynamicButton( cheatCommands, "Repeat Last Command", n"RepeatLastCommand" );

		if ( IsValid( TwoTeamModeDefaults() ) )
			CreateDynamicButton_ConsoleCommand( cheatCommands, "Rescue Lost ShieldBreaker", "ClientCommand RescueLostShieldBreaker" );
		CreateDynamicButton_ToggleCVar_OnServer( cheatCommands, "Everything Unlocked", "ScriptDebug.EverythingUnlocked" );
		CreateDynamicButton_ToggleCVar_OnServer( cheatCommands, "All Players Same Team", "ScriptDebug.SameTeam" );
		CreateDynamicButton_ToggleCVar_OnServer( cheatCommands, "Toggle Out of Bounds", "ScriptDebug.EnableOutOfBounds" );
		CreateDynamicButton_ConsoleCommand( cheatCommands, "Toggle 1P / 3P", "TPPToggle" );
		CreateDynamicButton_ConsoleCommand( cheatCommands, "Skip Current Phase", "ClientCommand SkipToNextPhase" );
		CreateDynamicButton_ToggleCVar_OnServer( cheatCommands, "Toggle Join in Progress", "ScriptDebug.AllowJoinInProgress" );
		CreateDynamicButton_ToggleCVar_OnServer( cheatCommands, "Quick Resource Regen", "ScriptDebug.ResourceRegen" );		
		CreateDynamicButton_ConsoleCommand( cheatCommands, "Teleport Players to Me", "ClientCommand TeleportEveryoneToPlayer" );
		CreateDynamicButton_ConsoleCommand( cheatCommands, "Teleport Players to Cursor", "ClientCommand TeleportEveryoneToPlayerView" );
		CreateDynamicButton( cheatCommands, "Destroy All Breachers", n"DestroyAllBreachers" );
		CreateDynamicButton( cheatCommands, "Destroy All Dome Shields", n"DestroyAllDomeShields" );
		CreateDynamicButton_ToggleCVar_OnServer( cheatCommands, "Toggle Unlimited Breacher", "ScriptDebug.UnlimitedBreacherEnergy" );

		CreateDynamicButton_ConsoleCommand( playerCommands, "God Mode", "God" );
		CreateDynamicButton_ConsoleCommand( playerCommands, "Demigod Mode", "DemiGod" );
		CreateDynamicButton_ConsoleCommand( playerCommands, "Heal Player", "ClientCommand ClientCommand_HealPlayer" );
		CreateDynamicButton_ToggleCVar_OnServer( playerCommands, "Unlimited Melee Bonus", "ScriptDebug.MeleeBonus" );
		CreateDynamicButton_ConsoleCommand( playerCommands, "Infinite Ammo", "ToggleInfiniteAmmo" );
		CreateDynamicButton_ConsoleCommand( playerCommands, "Reset Cooldowns", "ClientCommand ResetCooldowns" );
		CreateDynamicButton_ToggleCVar_OnServer( playerCommands, "Toggle Instant Respawn", "ScriptDebug.InstantRespawn" );
		CreateDynamicButton_ConsoleCommand( playerCommands, "Respawn Player", "Respawn" );
		CreateDynamicButton_ConsoleCommand( playerCommands, "Respawn All Players", "RespawnAll" );
		CreateDynamicButton_ToggleCVar_ClientOnly( playerCommands, "Show Velocity Debug", "movement.drawVelocity" );
		CreateDynamicButton( playerCommands, "Play Conversation", n"OnPlayConversationButtonClicked" );

		CreateDynamicButton_ConsoleCommand( debugCommands, "Script Profile", "stat angelscript" );
		CreateDynamicButton_ToggleCVar_ClientOnly( debugCommands, "Toggle Script Profile Details", "as.DetailedProfile" );
		CreateDynamicButton( debugCommands, "Toggle Audio Debugger", n"ToggleAudioDebugger" ); // Use this server command locally to make quick commands.
		CreateDynamicButton_ToggleCVar_ClientOnly( debugCommands, "Toggle Debug Movement", "movement.drawMovementDebug" );
		CreateDynamicButton( debugCommands, "Debug Mount Anim 1P", n"ToggleMountAnimDebug1P" );
		CreateDynamicButton( debugCommands, "Debug Mount Anim 3P", n"ToggleMountAnimDebug3P" );
		CreateDynamicButton( debugCommands, "Cycle Weapon Sight Guide", n"CycleWeaponSightGuide" );
		CreateDynamicButton_ConsoleCommand( debugCommands, "Show Match End Flow", "ClientCommand WinTheGame" ); // Use this server command locally to make quick commands.
		CreateDynamicButton_ConsoleCommand( debugCommands, "Match End Podium", "TestPodium" );
		CreateDynamicButton_ConsoleCommand( debugCommands, "Debug Animation (Server)", "nc.animation.playerDebugEnabled 1" );
		CreateDynamicButton_ConsoleCommand( debugCommands, "Debug Animation (Client)", "nc.animation.playerDebugEnabled 2" );
		CreateDynamicButton_ConsoleCommand( debugCommands, "Debug Animation Off", "nc.animation.playerDebugEnabled 0" );

		CreateDynamicButton_ConsoleCommand( botCommands, "Spawn Bot", "BotsAdd 1" );
		CreateDynamicButton_ConsoleCommand( botCommands, "Destroy All Bots", "BotsDestroy" );
		CreateDynamicButton_ConsoleCommand( botCommands, "Bot Record: Record", "nc.bot.cmds.StartRecord" );
		CreateDynamicButton_ConsoleCommand( botCommands, "Bot Record: Playback", "nc.bot.cmds.StartPlayback" );
		CreateDynamicButton_ConsoleCommand( botCommands, "Bot Record: Stop", "nc.bot.cmds.StopRecord" );
		CreateDynamicButton_ConsoleCommand( botCommands, "Bot Mimic On", "nc.bot.mimic 1" );
		CreateDynamicButton_ConsoleCommand( botCommands, "Bot Mimic Off", "nc.bot.mimic 0" );

		UAS_DevMenuColumn classColumn					= CreateColumn( Localization::GetUnlocalizedTextFromString( "Change Class" ) );
		TMap<FGameplayTag, FClassDataStruct> allClasses = Classes().GetAllClasses();
		for ( TMapIterator<FGameplayTag, FClassDataStruct> elem : allClasses )
		{
			FClassDataStruct data = elem.Value;
			if ( data.shippingMode == EShippingMode::INACTIVE )
				continue;
			CreateDynamicButton_ConsoleCommand( classColumn.buttonContainer, f"{data.className}", f"ClientCommand DevSetClass {elem.Value.index}" );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void RepeatLastCommand( UNCButtonWidget Button, FString argument )
	{
		Print( "Repeat Last Action" );
		DevMenuCheatRecorded( Button );
	}

	UFUNCTION( NotBlueprintCallable )
	private void DestroyAllBreachers( UNCButtonWidget Button, FString argument )
	{
		Print( "Destroy All Breachers" );
		ANCPlayerController playerController = Client_GetLocalPlayerController();
		UNCRemoteScriptCommands::SendClientCheat( playerController, f"ClientCommand_DestroyAllBreachers {}" );
		DevMenuCheatRecorded( Button );
	}

	UFUNCTION( NotBlueprintCallable )
	private void DestroyAllDomeShields( UNCButtonWidget Button, FString argument )
	{
		Print( "Destroy All Breachers" );
		ANCPlayerController playerController = Client_GetLocalPlayerController();
		UNCRemoteScriptCommands::SendClientCheat( playerController, f"ClientCommand_DestroyAllDomeShields {}" );
		DevMenuCheatRecorded( Button );
	}

	UFUNCTION( NotBlueprintCallable )
	private void ShowMatchEndFlow( UNCButtonWidget Button, FString argument )
	{
		Print( "Show Match End Flow" );
		ANCPlayerController playerController = Client_GetLocalPlayerController();
		AAS_HUD hud							 = Cast<AAS_HUD>( GetLocalHUD() );
		if ( !IsValid( hud ) )
			return;
		AAS_PlayerEntity localPlayer = Client_GetLocalASPawn();
		hud.SpawnMatchEndMenu();

		DevMenuCheatRecorded( Button );
	}

	UFUNCTION( NotBlueprintCallable )
	private void CustomServerCommand( UNCButtonWidget Button, FString argument )
	{
		Print( "Custom Server Command" );
		ANCPlayerController playerController = Client_GetLocalPlayerController();
		UNCRemoteScriptCommands::SendClientCheat( playerController, f"ClientCommand_CustomServerCommand {}" );
		DevMenuCheatRecorded( Button );
	}

	UFUNCTION( NotBlueprintCallable )
	private void ToggleAudioDebugger( UNCButtonWidget Button, FString argument )
	{
		ANCPlayerController playerController = Client_GetLocalPlayerController();
		AAS_HUD hud							 = Cast<AAS_HUD>( GetLocalHUD() );
		if ( !IsValid( hud ) )
			return;

		if ( !IsValid( hud.debugAudioDebugger ) )
		{
			hud.debugAudioDebugger = Cast<UUserWidget>( WidgetBlueprint::CreateWidget( Audio().audioDebuggerClass, playerController ) );
			hud.debugAudioDebugger.AddToViewport();
			Print( "Toggle Audio Debugger On" );
		}
		else
		{
			hud.debugAudioDebugger.RemoveFromParent();
			hud.debugAudioDebugger = nullptr;
			Print( "Toggle Audio Debugger Off" );
		}

		DevMenuCheatRecorded( Button );
	}

	UFUNCTION()
	private void OnPlayConversationButtonClicked( UNCButtonWidget Button, FString argument )
	{
		Print( f"Play Conversation" );
		ANCPlayerController playerController = Client_GetLocalPlayerController();
		UNCRemoteScriptCommands::SendClientCheat( playerController, f"PlayConversation {}" );
		DevMenuCheatRecorded( Button );
	}

	UFUNCTION()
	private void ToggleMountAnimDebug1P( UNCButtonWidget Button, FString argument )
	{
		AAS_HUD hud = Cast<AAS_HUD>( GetLocalHUD() );
		if ( !IsValid( hud ) )
			return;

		if ( !IsValid( hud.debugMountAnimWidget1P ) )
		{
			hud.debugMountAnimWidget1P = Cast<UAS_DebugMountAnimWidget1P>( WidgetBlueprint::CreateWidget( DoNotShipGlobals().debugMountAnimWidget1PClass, hud.OwningPlayerController ) );
			hud.debugMountAnimWidget1P.AddToViewport( -100 );
			SetWidgetVisibilitySafe( hud.debugMountAnimWidget1P, ESlateVisibility::Collapsed );
		}

		if ( hud.debugMountAnimWidget1P.IsVisible() )
		{
			ShowOrHidePriorityMessageContainer( true );
			SetWidgetVisibilitySafe( hud.debugMountAnimWidget1P, ESlateVisibility::Collapsed );
		}
		else
		{
			SetWidgetVisibilitySafe( hud.debugMountAnimWidget1P, ESlateVisibility::HitTestInvisible );
			SetWidgetVisibilitySafe( hud.debugMountAnimWidget3P, ESlateVisibility::Collapsed );
			hud.CloseAllMenus();
			ShowOrHidePriorityMessageContainer( false );
		}
	}

	UFUNCTION()
	private void ToggleMountAnimDebug3P( UNCButtonWidget Button, FString argument )
	{
		AAS_HUD hud = Cast<AAS_HUD>( GetLocalHUD() );
		if ( !IsValid( hud ) )
			return;

		if ( !IsValid( hud.debugMountAnimWidget3P ) )
		{
			hud.debugMountAnimWidget3P = Cast<UAS_DebugMountAnimWidget3P>( WidgetBlueprint::CreateWidget( DoNotShipGlobals().debugMountAnimWidget3PClass, hud.OwningPlayerController ) );
			hud.debugMountAnimWidget3P.AddToViewport( -100 );
			SetWidgetVisibilitySafe( hud.debugMountAnimWidget3P, ESlateVisibility::Collapsed );
		}

		if ( hud.debugMountAnimWidget3P.IsVisible() )
		{
			ShowOrHidePriorityMessageContainer( true );
			SetWidgetVisibilitySafe( hud.debugMountAnimWidget3P, ESlateVisibility::Collapsed );
		}
		else
		{
			SetWidgetVisibilitySafe( hud.debugMountAnimWidget3P, ESlateVisibility::HitTestInvisible );
			SetWidgetVisibilitySafe( hud.debugMountAnimWidget1P, ESlateVisibility::Collapsed );
			ShowOrHidePriorityMessageContainer( false );
			hud.mainHUDWidget.Hide();
		}
	}

	UFUNCTION()
	private void CycleWeaponSightGuide( UNCButtonWidget button, FString argument )
	{
		AAS_HUD hud = Cast<AAS_HUD>( GetLocalHUD() );
		if ( !IsValid( hud ) )
			return;

		if ( !IsValid( hud.devWeaponSightGuideWidget ) )
		{
			hud.devWeaponSightGuideWidget = Cast<UAS_WeaponSightGuide>( WidgetBlueprint::CreateWidget( DoNotShipGlobals().devWeaponSightGuideWidgetClass, hud.OwningPlayerController ) );
			hud.devWeaponSightGuideWidget.AddToViewport( GameConst::ZORDER_OVER_HUD );
			SetWidgetVisibilitySafe( hud.devWeaponSightGuideWidget, ESlateVisibility::Collapsed );
		}

		EWeaponSightGuide curGuide = hud.devWeaponSightGuideWidget.CycleGuide();
		if ( curGuide == EWeaponSightGuide::NONE )
		{
			ShowOrHidePriorityMessageContainer( true );
			SetWidgetVisibilitySafe( hud.devWeaponSightGuideWidget, ESlateVisibility::Collapsed );
		}
		else
		{
			ShowOrHidePriorityMessageContainer( false );
			SetWidgetVisibilitySafe( hud.devWeaponSightGuideWidget, ESlateVisibility::HitTestInvisible );
		}
	}

	UFUNCTION()
	private void OnRepeatLastCommandButtonClicked( UNCButtonWidget Button )
	{
		Print( f"Repeat Last Command" );
		// DevMenuCheatRecorded( Button );
	}
}

UCLASS( Abstract )
class UAS_DevMenuResources : UAS_DevMenuPanel
{
	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidget ) )
	UPanelWidget basicResourcesCommands;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidget ) )
	UPanelWidget rareResourcesCommands;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidget ) )
	UPanelWidget combatResourcesCommands;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidget ) )
	UPanelWidget equipmentCommands;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		CreateDynamicButton_ConsoleCommand( combatResourcesCommands, "Small Armor Repair", "ClientCommand GiveLoot HealItem.SmallShield" );
		CreateDynamicButton_ConsoleCommand( combatResourcesCommands, "Blue Armor", "ClientCommand GiveLoot Loot.Armor.Level2" );
		CreateDynamicButton_ConsoleCommand( combatResourcesCommands, "Purple Armor", "ClientCommand GiveLoot Loot.Armor.Level3" );
		CreateDynamicButton_ConsoleCommand( combatResourcesCommands, "Orange Armor", "ClientCommand GiveLoot Loot.Armor.Level4" );
		CreateDynamicButton( combatResourcesCommands, "Supply Pack", n"OnGiveSupplyPack" );
		CreateDynamicButton_ConsoleCommand( combatResourcesCommands, "Ultimate Accelerant", "ClientCommand GiveLoot Loot.HealItem.UltimateAccel" );

		CreateDynamicButton_ConsoleCommand( basicResourcesCommands, "Wood", "ClientCommand GiveLoot Loot.Resource.Wood 500" );
		CreateDynamicButton_ConsoleCommand( basicResourcesCommands, "Vesper", "ClientCommand GiveLoot Loot.Resource.Opal 100" );
		CreateDynamicButton_ConsoleCommand( basicResourcesCommands, "Storm Gold", "ClientCommand GiveLoot Loot.Resource.Coin 100" );

		CreateDynamicButton_ConsoleCommand( rareResourcesCommands, "Wall Upgrade", f"ClientCommand GiveLoot {GameplayTags::Loot_Component_BuildingUpgrade_Iron} 4" );

		CreateDynamicButton_ConsoleCommand( equipmentCommands, "Helmet Lv1", "ClientCommand GiveLoot Loot.Equipment.Helmet.Lv1" );
		CreateDynamicButton_ConsoleCommand( equipmentCommands, "Helmet Lv2", "ClientCommand GiveLoot Loot.Equipment.Helmet.Lv2" );
		CreateDynamicButton_ConsoleCommand( equipmentCommands, "Helmet Lv3", "ClientCommand GiveLoot Loot.Equipment.Helmet.Lv3" );

		CreateDynamicButton_ConsoleCommand( equipmentCommands, "Saddle Lv1", "ClientCommand GiveLoot Loot.Equipment.Saddle.Lv1" );
		CreateDynamicButton_ConsoleCommand( equipmentCommands, "Saddle Lv2", "ClientCommand GiveLoot Loot.Equipment.Saddle.Lv2" );
		CreateDynamicButton_ConsoleCommand( equipmentCommands, "Saddle Lv3", "ClientCommand GiveLoot Loot.Equipment.Saddle.Lv3" );

		CreateDynamicButton_ConsoleCommand( equipmentCommands, "Trinket: Tactical Cooldown", "ClientCommand GiveLoot Loot.Equipment.Trinket.CDReductionTactical" );
		CreateDynamicButton_ConsoleCommand( equipmentCommands, "Trinket: Ult Cooldown", "ClientCommand GiveLoot Loot.Equipment.Trinket.CDReductionUlt" );
		CreateDynamicButton_ConsoleCommand( equipmentCommands, "Trinket: Grenade Cooldown", "ClientCommand GiveLoot Loot.Equipment.Trinket.CDReductionGrenade" );
		CreateDynamicButton_ConsoleCommand( equipmentCommands, "Trinket: BombKit", "ClientCommand GiveLoot Loot.Equipment.Trinket.BombKit" );
		CreateDynamicButton_ConsoleCommand( equipmentCommands, "Trinket: Guardian Angel", "ClientCommand GiveLoot Loot.Equipment.Trinket.Reviver" );
		CreateDynamicButton_ConsoleCommand( equipmentCommands, "Trinket: Armorer", "ClientCommand GiveLoot Loot.Equipment.Trinket.Armorer" );
		CreateDynamicButton_ConsoleCommand( equipmentCommands, "Trinket: Harvest", "ClientCommand GiveLoot Loot.Equipment.Trinket.Harvester" );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnGiveSupplyPack( UNCButtonWidget Button, FString argument )
	{
		ANCPlayerController playerController = Client_GetLocalPlayerController();
		UNCRemoteScriptCommands::SendClientCheat( playerController, f"ClientCommand_ResupplyPlayer {}" );
		DevMenuCheatRecorded( Button );
	}
}

UCLASS( Abstract )
class UAS_DevMenuProgression : UAS_DevMenuPanel
{
	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidget ) )
	UPanelWidget progressionCommands;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidget ) )
	UPanelWidget persistenceCommands;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		CreateDynamicButton_ConsoleCommand( progressionCommands, "Upgrade Loot Level", "ClientCommand UpgradeLootLevel" );
		CreateDynamicButton( progressionCommands, "Spawn Care Package", n"OnSpawnCarePackageButtonClicked" );
		CreateDynamicButton( progressionCommands, "Call Care Package", n"OnCallCarePackageButtonClicked" );

		CreateDynamicButton( persistenceCommands, "Reset Persistence", n"OnResetPersistenceButtonClicked" );
	}

	UFUNCTION()
	private void OnResetPersistenceButtonClicked( UNCButtonWidget Button, FString argument )
	{
		Print( f"Reset Persistence" );
		if ( IsValid( Cl_RaidPersistence() ) )
		{
			Cl_RaidPersistence().ResetPersistence();
			Cl_RaidPersistence().StopSaveLoop();
		}
		DevMenuCheatRecorded( Button );
	}

	UFUNCTION()
	private void OnSpawnCarePackageButtonClicked( UNCButtonWidget Button, FString argument )
	{
		Print( f"Spawn Care Package" );
		ANCPlayerController playerController = Client_GetLocalPlayerController();
		UNCRemoteScriptCommands::SendClientCheat( playerController, f"ClientCommand_SpawnCarePackage {int( ECarePackageType::NORMAL )}" );
		DevMenuCheatRecorded( Button );
	}

	UFUNCTION()
	private void OnCallCarePackageButtonClicked( UNCButtonWidget Button, FString argument )
	{
		Print( f"Call Care Package" );
		ANCPlayerController playerController = Client_GetLocalPlayerController();
		UNCRemoteScriptCommands::SendClientCheat( playerController, f"ClientCommand_CallCarePackage {int( ECarePackageType::NORMAL )}" );
		DevMenuCheatRecorded( Button );
	}
}

UCLASS( Abstract )
class UAS_DevMenuWeapons : UAS_DevMenuPanel
{
	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidget ) )
	UPanelWidget pistolSMGCommands;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidget ) )
	UPanelWidget shotgunARCommands;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidget ) )
	UPanelWidget marksmanSniperCommands;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidget ) )
	UPanelWidget uniqueCommands;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		CreateWeaponAndBodyButtons( pistolSMGCommands, GameplayTags::Loot_Weapon_LonghornRevolver );
		CreateWeaponAndBodyButtons( pistolSMGCommands, GameplayTags::Loot_Weapon_Corsair );
		CreateWeaponAndBodyButtons( pistolSMGCommands, GameplayTags::Loot_Weapon_VectorSMG );
		CreateWeaponAndBodyButtons( pistolSMGCommands, GameplayTags::Loot_Dev_Weapon_Hornet );
		CreateWeaponAndBodyButtons( pistolSMGCommands, GameplayTags::Loot_Dev_Weapon_HeavyPistol );

		CreateWeaponAndBodyButtons( shotgunARCommands, GameplayTags::Loot_Weapon_Paladin_SMG );
		CreateWeaponAndBodyButtons( shotgunARCommands, GameplayTags::Loot_Weapon_NovaShotgun );
		CreateWeaponAndBodyButtons( shotgunARCommands, GameplayTags::Loot_Weapon_DB51 );
		CreateWeaponAndBodyButtons( shotgunARCommands, GameplayTags::Loot_Weapon_Burst_Rifle );
		CreateWeaponAndBodyButtons( shotgunARCommands, GameplayTags::Loot_Weapon_Dynasty );
		CreateWeaponAndBodyButtons( shotgunARCommands, GameplayTags::Loot_Dev_Weapon_TwoStep );
		CreateWeaponAndBodyButtons( shotgunARCommands, GameplayTags::Loot_Weapon_SigLMG );

		CreateWeaponAndBodyButtons( marksmanSniperCommands, GameplayTags::Loot_Weapon_Scout );

		CreateDynamicButton_ConsoleCommand( uniqueCommands, f"Melee Axe - Default", f"ClientCommand GiveMeleeWeapon Weap_Melee_Axe" );
		CreateDynamicButton_ConsoleCommand( uniqueCommands, f"Melee Axe - Multi Target", f"ClientCommand GiveMeleeWeapon Weap_Melee_MultiTargetAxe" );
		CreateDynamicButton_ConsoleCommand( uniqueCommands, f"Melee Axe - Multi Target On Bonus", f"ClientCommand GiveMeleeWeapon Weap_MeleeAxe" );
		CreateDynamicButton_ConsoleCommand( uniqueCommands, f"Melee Axe - Multi Target Skill", f"ClientCommand GiveMeleeWeapon Weap_Melee_MultiTargetSkillAxe" );
		CreateDynamicButton( uniqueCommands, "Cycle weapon mesh", n"CycleWeaponDevMesh" );
		CreateDynamicButton( uniqueCommands, f"Mount - Give Cat", n"SetMountEquipment", f"{GetIntForGameplayTag(GameplayTags::Mounts_Cat)}" );
		CreateDynamicButton( uniqueCommands, f"Mount - Give Horse", n"SetMountEquipment", f"{GetIntForGameplayTag(GameplayTags::Mounts_Horse)}" );
		CreateDynamicButton( uniqueCommands, f"Mount - Give Bear", n"SetMountEquipment", f"{GetIntForGameplayTag(GameplayTags::Mounts_Bear)}" );
		CreateDynamicButton( uniqueCommands, f"Mount - Give Wolf", n"SetMountEquipment", f"{GetIntForGameplayTag(GameplayTags::Mounts_Wolf)}" );
		CreateDynamicButton( uniqueCommands, f"Mount - Give Demigryph", n"SetMountEquipment", f"{GetIntForGameplayTag(GameplayTags::Mounts_Demigryph)}" );
	}

	void CreateWeaponAndBodyButtons( UPanelWidget panel, FGameplayTag lootIndex )
	{
		UHorizontalBox horzPanel = Cast<UHorizontalBox>( ConstructWidget( UHorizontalBox::StaticClass() ) );
		panel.AddChild( horzPanel );

		UVerticalBoxSlot mainBoxSlot	= Cast<UVerticalBoxSlot>( horzPanel.Slot );
		mainBoxSlot.HorizontalAlignment = EHorizontalAlignment::HAlign_Fill;
		mainBoxSlot.SetPadding( FMargin( 0, 16 ) );

		// Button for vanilla weapon
		{
			UAS_ButtonWidget button	  = CreateWeaponButton( horzPanel, lootIndex );
			UHorizontalBoxSlot bSlot  = Cast<UHorizontalBoxSlot>( button.Slot );
			bSlot.HorizontalAlignment = EHorizontalAlignment::HAlign_Fill;

			FSlateChildSize sizeRules;
			sizeRules.SizeRule = ESlateSizeRule::Fill;
			bSlot.SetVerticalAlignment( EVerticalAlignment::VAlign_Top );
			bSlot.SetSize( sizeRules );
		}

		FGameplayTagContainer children = UNCUtils::GetGameplayTagChildren( lootIndex );
		if ( children.GameplayTags.Num() > 0 )
		{
			TArray<FGameplayTag> purpleAndOrangeWeapons;
			TArray<FGameplayTag> blueWeapons;
			for ( FGameplayTag tag : children.GameplayTags )
			{
				const FLootDataStruct& data = GetLootDataByIndex( tag );

				if ( data.rarity == GameplayTags::Loot_Rarity_Uncommon )
				{
					blueWeapons.Add( tag );
					continue;
				}
				else if ( data.rarity == GameplayTags::Loot_Rarity_Rare || data.rarity == GameplayTags::Loot_Rarity_Extraordinary )
				{
					purpleAndOrangeWeapons.Add( tag );
				}
			}

			CreateWeaponButtonSet( blueWeapons, horzPanel );
			CreateWeaponButtonSet( purpleAndOrangeWeapons, horzPanel );
		}
	}

	void CreateWeaponButtonSet( TArray<FGameplayTag> weaponTags, UHorizontalBox ownerPanel )
	{
		UVerticalBox vertPanel			  = Cast<UVerticalBox>( ConstructWidget( UVerticalBox::StaticClass() ) );
		UHorizontalBoxSlot vertPanelSlot  = Cast<UHorizontalBoxSlot>( ownerPanel.AddChild( vertPanel ) );
		vertPanelSlot.HorizontalAlignment = EHorizontalAlignment::HAlign_Fill;

		FSlateChildSize sizeRules;
		sizeRules.SizeRule = ESlateSizeRule::Fill;
		vertPanelSlot.SetSize( sizeRules );

		for ( FGameplayTag weaponTag : weaponTags )
		{
			CreateWeaponButton( vertPanel, weaponTag );
		}
	}

	UAS_ButtonWidget CreateWeaponButton( UPanelWidget panel, FGameplayTag lootIndex, FName optionalName = NAME_None )
	{
		const FLootDataStruct& data = GetLootDataByIndex( lootIndex );
		FString nameToUse			= data.name.ToString();
		if ( !data.weaponMods.IsEmpty() )
		{
			FWeaponAttachmentData attachData = GetAttachmentData( data.weaponMods.Last() );
			nameToUse						 = attachData.name.ToString();
		}

		UAS_TextButtonWidget button = Cast<UAS_TextButtonWidget>( CreateDynamicButton( panel, nameToUse, n"OnGiveWeapon", f"{data.index}" ) );
		button.Button.SetBackgroundColor( GetRarityColor( data.rarity ) );

		button.SetPadding( FMargin( 8, 2 ) );

		return button;
	}

	UFUNCTION()
	void OnGiveWeapon( UNCButtonWidget Button, FString argument )
	{
		FGameplayTag tag			= FGameplayTag::RequestGameplayTag( FName( argument ) );
		const FLootDataStruct& data = GetLootDataByIndex( tag );

		System::ExecuteConsoleCommand( f"ClientCommand GiveLoot {data.index}" );

		if ( IsAmmoValidForLoot( data.ammoSource ) )
		{
			FLootDataStruct ammoData = GetLootDataForAmmo( data.ammoSource );
			System::ExecuteConsoleCommand( f"ClientCommand GiveLoot {ammoData.index}" );
		}
	}

	UFUNCTION()
	private void CycleWeaponDevMesh( UNCButtonWidget button, FString argument )
	{
		ANCPlayerController playerController = Client_GetLocalPlayerController();
		UNCRemoteScriptCommands::SendClientCheat( playerController, f"ClientCommand_CycleWeaponDevMesh" );
		DevMenuCheatRecorded( button );
	}

	UFUNCTION()
	private void SetMountEquipment( UNCButtonWidget button, FString argument )
	{
		ANCPlayerController playerController = Client_GetLocalPlayerController();
		UNCRemoteScriptCommands::SendClientCheat( playerController, f"ClientCommand_SetEquippedMount {argument}" );
		DevMenuCheatRecorded( button );
	}
}

UCLASS( Abstract )
class UAS_DevMenuRaidTools : UAS_DevMenuPanel
{
	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidget ) )
	UPanelWidget offseniveToolsPanel;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidget ) )
	UPanelWidget defensiveToolsPanel;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidget ) )
	UPanelWidget raidWeaponsPanel;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		CreateDynamicButton_ConsoleCommand( offseniveToolsPanel, "Shield Breaker Default", "ClientCommand GiveLoot Placeables.Shieldbreaker" );
		//CreateDynamicButton_ConsoleCommand( offseniveToolsPanel, "Shield Breaker Taube", "ClientCommand GiveLoot Placeables.ShieldBreakerTaubeTest" );
		CreateDynamicButton_ConsoleCommand( offseniveToolsPanel, "Shield Breaker Extender", "ClientCommand GiveLoot Placeables.MiniShieldbreaker" );
		CreateDynamicButton_ConsoleCommand( offseniveToolsPanel, "Frag Grenade", "ClientCommand GiveLoot Ammo.Grenades.Frag" );
		CreateDynamicButton_ConsoleCommand( offseniveToolsPanel, "Time Bomb", "ClientCommand GiveLoot Ammo.Grenades.TimeBomb" );
		CreateDynamicButton_ConsoleCommand( offseniveToolsPanel, "Riot Shield", "ClientCommand GiveLoot Loot.Weapon.RiotShield" );
		CreateDynamicButton_ConsoleCommand( offseniveToolsPanel, "Laser Drill", "ClientCommand GiveLoot Ammo.Grenades.LaserDrill" );
		CreateDynamicButton_ConsoleCommand( offseniveToolsPanel, "Molotov", "ClientCommand GiveLoot Ammo.Grenades.Molotov" );
		CreateDynamicButton_ConsoleCommand( offseniveToolsPanel, "Magnetic Grenade", "ClientCommand GiveLoot Ammo.Grenades.Magnetic" );
		// CreateDynamicButton_ConsoleCommand( offseniveToolsPanel, "[Ult]   Raid Golem", "ClientCommand GiveLoot RaidUltimate.RaidGolem" );

		CreateDynamicButton_ConsoleCommand( defensiveToolsPanel, "Auto turret", "ClientCommand GiveLoot Placeables.AutoTurret" );
		CreateDynamicButton_ConsoleCommand( defensiveToolsPanel, "Barbed Wire", "ClientCommand GiveLoot Placeables.BarbedWire" );
		CreateDynamicButton_ConsoleCommand( defensiveToolsPanel, "Spike Trap", "ClientCommand GiveLoot Placeables.SpikeTrap" );
		CreateDynamicButton_ConsoleCommand( defensiveToolsPanel, "Frag Mines", "ClientCommand GiveLoot Placeables.FragMineLauncher" );
		CreateDynamicButton_ConsoleCommand( defensiveToolsPanel, "Trophy System", "ClientCommand GiveLoot Placeables.TrophySystem" );
		CreateDynamicButton_ConsoleCommand( defensiveToolsPanel, "Building Upgrade (Stone)", "ClientCommand GiveLoot Loot.Component.BuildingUpgrade_Stone" );
		CreateDynamicButton_ConsoleCommand( defensiveToolsPanel, "Building Upgrade (Iron)", "ClientCommand GiveLoot Loot.Component.BuildingUpgrade_Iron" );
		CreateDynamicButton_ConsoleCommand( defensiveToolsPanel, "Vesper Walls", "ClientCommand GiveLoot Loot.Component.BuildingUpgrade_Vesper" );
		CreateDynamicButton_ConsoleCommand( defensiveToolsPanel, "Placeable Furnace", "ClientCommand GiveLoot Loot.Placeables.Furnace" );
		CreateDynamicButton( defensiveToolsPanel, "Repair ALL Destructibles", n"RepairAllDestructibles" );

		CreateDynamicButton( raidWeaponsPanel, "Rocket Launcher", n"GiveRaidWeapon", f"Weap_RS5" );
		CreateDynamicButton( raidWeaponsPanel, "Rocket Launcher Thermite", n"GiveRaidWeapon", f"Weap_RS5 RocketLauncherLightning" );
		CreateDynamicButton( raidWeaponsPanel, "Rocket Launcher Multi-Lock", n"GiveRaidWeapon", f"Weap_RS5 RocketLauncherMultiLock" );
		CreateDynamicButton( raidWeaponsPanel, "Hammer", n"GiveRaidWeapon", f"Weap_RaidHammer" );
		CreateDynamicButton( raidWeaponsPanel, "Hammer Tron Wall", n"GiveRaidWeapon", f"Weap_RaidHammer HammerWall" );
		CreateDynamicButton( raidWeaponsPanel, "Hammer Smoke Cover", n"GiveRaidWeapon", f"Weap_RaidHammer HammerSmoke" );

		CreateDynamicButton( raidWeaponsPanel, "Zipline", n"OnGiveWeapon", f"{GetLootDataByIndex( GameplayTags::Loot_Weapon_ZiplineGun ).index}" );
		CreateDynamicButton( raidWeaponsPanel, "Zipline Unbreakable", n"OnGiveWeapon", f"{GetLootDataByIndex( GameplayTags::Loot_Weapon_ZiplineGunUnbreakable ).index}" );
		CreateDynamicButton( raidWeaponsPanel, "Zipline Invisible", n"OnGiveWeapon", f"{GetLootDataByIndex( GameplayTags::Loot_Weapon_ZiplineGunInvisible ).index}" );

		CreateDynamicButton( raidWeaponsPanel, "Hacker Dart", n"GiveRaidWeapon", f"Weap_HackerDart" );
		CreateDynamicButton( raidWeaponsPanel, "Hacker Dart Zappy", n"GiveRaidWeapon", f"Weap_HackerDart Zappy" );

		// CreateDynamicButton( raidWeaponsPanel, "Mortar Strike", n"GiveRaidWeapon", f"Weap_MortarStrike" );

		CreateDynamicButton( raidWeaponsPanel, "Rocket Ammo", n"OnGiveWeapon", f"{GetLootDataByIndex( GameplayTags::Loot_Ammo_Rockets ).index}" );
		CreateDynamicButton( raidWeaponsPanel, "Zipline Ammo", n"OnGiveWeapon", f"{GetLootDataByIndex( GameplayTags::Loot_Ammo_Ziplines ).index}" );
		CreateDynamicButton( raidWeaponsPanel, "Hammer Ammo", n"OnGiveWeapon", f"{GetLootDataByIndex( GameplayTags::Loot_Ammo_Hammer ).index}" );
		CreateDynamicButton( raidWeaponsPanel, "Hacker Dart Ammo", n"OnGiveWeapon", f"{GetLootDataByIndex( GameplayTags::Loot_Ammo_HackerDart ).index}" );
	}

	UFUNCTION()
	void OnGiveWeapon( UNCButtonWidget Button, FString argument )
	{
		FGameplayTag tag			= FGameplayTag::RequestGameplayTag( FName( argument ) );
		const FLootDataStruct& data = GetLootDataByIndex( tag );

		System::ExecuteConsoleCommand( f"ClientCommand GiveLoot {data.index}" );

		if ( IsAmmoValidForLoot( data.ammoSource ) )
		{
			FLootDataStruct ammoData = GetLootDataForAmmo( data.ammoSource );
			System::ExecuteConsoleCommand( f"ClientCommand GiveLoot {ammoData.index}" );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void RepairAllDestructibles( UNCButtonWidget Button, FString argument )
	{
		ANCPlayerController playerController = Client_GetLocalPlayerController();
		if ( IsValid( playerController ) )
			UNCRemoteScriptCommands::SendClientCheat( playerController, f"RepairAllDestructibles" );
		// GetTeamStateManager(GetOwningPlayer().GetTeam()).SetTeamHasShieldBreaker(true);
	}

	UFUNCTION()
	private void GiveRaidWeapon( UNCButtonWidget button, FString argument )
	{
		ANCPlayerController playerController = Client_GetLocalPlayerController();
		UNCRemoteScriptCommands::SendClientCheat( playerController, f"ClientCommand_GiveRaidWeapon {argument}" );
		DevMenuCheatRecorded( button );
	}
}

UCLASS( Abstract )
class UAS_DevMenuMTXActionBlocks : UAS_DevMenuPanel
{
	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidget ) )
	UPanelWidget baseDecorationPanel;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidget ) )
	UPanelWidget bombPanel;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidget ) )
	UPanelWidget miscPanel;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
	}
}
