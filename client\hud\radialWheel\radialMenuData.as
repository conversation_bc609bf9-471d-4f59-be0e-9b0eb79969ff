UCLASS(Abstract)
class UAS_RadialMenuData : UObject
{
	UPROPERTY(EditDefaultsOnly)
	FRadialMenuData radialData;

	UPROPERTY(EditDefaultsOnly)
	bool handleMouseNatively = true;

	UPROPERTY(EditDefaultsOnly)
	float radialMenuAngleOffset = 0.0;

	UFUNCTION(BlueprintEvent)
	bool Populate( TArray<UAS_RadialMenuListItemData>& results )
	{ return false; }

	UFUNCTION(BlueprintEvent)
	bool SelectItem( UAS_RadialMenuListItemData itemData )
	{ return false; }
}