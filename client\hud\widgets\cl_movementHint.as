UCLASS( Abstract )
class UAS_MovementHintWidget : UAS_InputSwitchingWidget
{
	UPROPERTY( NotVisible, BindWidget )
	UTextBlock key_w;
	UPROPERTY( NotVisible, BindWidget )
	UTextBlock key_a;
	UPROPERTY( NotVisible, BindWidget )
	UTextBlock key_s;
	UPROPERTY( NotVisible, BindWidget )
	UTextBlock key_d;
	UPROPERTY( NotVisible, BindWidget )
	UTextBlock key_ls;
	UPROPERTY( NotVisible, BindWidget )
	UTextBlock key_rs;

	UPROPERTY( EditInstanceOnly )
	FText hint;

	UPROPERTY( NotVisible, BindWidget )
	UAS_CommonTextBlock hintText;

	UPROPERTY( EditInstanceOnly )
	private TSubclassOf<UCommonTextStyle> textStyleOverride = nullptr;

	UFUNCTION( BlueprintOverride )
	void PreConstruct( bool IsDesignTime )
	{
		if ( IsValid( hintText ) )
		{
			if ( IsValid( textStyleOverride ) )
			{
				hintText.SetStyle( textStyleOverride );
			}
			hintText.SetText( hint );
		}
	}

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		Super::Construct();

		key_w.SetText( GetGlyphAsText( EKeys::W ) );
		key_a.SetText( GetGlyphAsText( EKeys::A ) );
		key_s.SetText( GetGlyphAsText( EKeys::S ) );
		key_d.SetText( GetGlyphAsText( EKeys::D ) );
		key_ls.SetText( GetGlyphAsText( EKeys::Gamepad_LeftStick_Left ) );
		key_rs.SetText( GetGlyphAsText( EKeys::Gamepad_LeftStick_Up ) );
	}
}