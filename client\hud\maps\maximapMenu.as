UCLASS( Abstract )
class UAS_MaximapMenu : UAS_MenuWidget
{
	default hidePriorityMesssages = false;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_MaximapLegendWidget maximapLegend;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UNC_DisplayWidget maximap;

	UFUNCTION( BlueprintOverride )
	void OnActivated()
	{
		Super::OnActivated();

		maximapLegend.Show();
		maximap.Show();
	}

	UFUNCTION( BlueprintOverride )
	void OnDeactivated()
	{
		Super::OnDeactivated();

		maximapLegend.Hide();
		maximap.Hide();
	}

}