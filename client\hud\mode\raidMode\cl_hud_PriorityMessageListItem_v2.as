event void FEvent_PriorityMessageListItemOnCallback( UAS_PriorityMessageListItem ListItem );
event void FEvent_PriorityMessageListItemOnCountdownStop( UAS_PriorityMessageListItem ListItem, EPriorityMessageCountdownStopReason Reason );
event void FEvent_PriorityMessageListEvalFunc( UAS_PriorityMessageListData ListData, int RaidFlags, bool& eval );

UCLASS()
class UAS_PriorityMessageListData : UObject
{
	TSubclassOf<UAS_PriorityMessageListItem> WidgetClass;
	EPriorityMessageTheme Theme = EPriorityMessageTheme::USEPARENT;
	FText ListItemTxt;
	// don't automatically show list item when added to parent message
	bool AutoHide = false;

	// an alternate version of the message to display... must have a valid AlternateEval to decide
	EPriorityListTemplate AltTemplate = EPriorityListTemplate::_count;
	// the function to evaluate whether to use the AltTemplate or not, must have valid AltTemplate to use
	FEvent_PriorityMessageListEvalFunc AlternateEval;
	// the function to evaluate whether to set priority to this instead of the first list entry
	FEvent_PriorityMessageListEvalFunc PriorityEval;

	FEvent_PriorityMessageListItemOnCallback OnCreate;
	FEvent_PriorityMessageListItemOnCallback OnStop;
	FEvent_PriorityMessageListItemOnCountdownStop OnCountDownStop;
	FEvent_PriorityMessageListItemOnCountdownStop OnProgressBarStop;

	// for internal use only
	EPriorityListTemplate ListTemplate = EPriorityListTemplate::CUSTOM;
	bool SetPriority				   = false;
}

struct FListUpdateParams
{
	bool differentText;
	bool differentTemplate;
	bool shouldHide;

	void Init( UAS_PriorityMessageListItem listItem, UAS_PriorityMessageListData newData )
	{
		FString oldText	  = listItem.GetListMessage();
		FString newText	  = newData.ListItemTxt.ToString();
		int compare		  = oldText.Compare( newText, ESearchCase::IgnoreCase );
		differentText	  = compare != 0;
		differentTemplate = listItem.listItemData.ListTemplate != newData.ListTemplate;
		shouldHide		  = ( differentTemplate && newData.AutoHide ) || ( !differentTemplate && !differentText && listItem.IsHidden() && newData.AutoHide );
	}

	bool HasDifference()
	{
		return differentText || differentTemplate;
	}
}

/****************************************************************\

██      ██ ███████ ████████     ██ ████████ ███████ ███    ███
██      ██ ██         ██        ██    ██    ██      ████  ████
██      ██ ███████    ██        ██    ██    █████   ██ ████ ██
██      ██      ██    ██        ██    ██    ██      ██  ██  ██
███████ ██ ███████    ██        ██    ██    ███████ ██      ██

\****************************************************************/
UCLASS( Abstract )
class UAS_PriorityMessageListItem : UUserWidgetDefault
{
	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	private UWidgetAnimation list_AppearAnim;
	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	private UWidgetAnimation list_DisappearAnim;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	private UWidgetAnimation list_ShowCountdownAnim;
	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	private UWidgetAnimation list_HideCountdownAnim;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	private UWidgetAnimation list_ShowProgressBarAnim;
	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	private UWidgetAnimation list_HideProgressBarAnim;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	private UWidgetAnimation list_PrioritizeAnim;
	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	private UWidgetAnimation list_DeprioritizeAnim;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	private UWidgetAnimation list_SlideOutAnim;
	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	private UWidgetAnimation list_SlideInAnim;

	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	private UImage list_CheckBox;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	private UTextBlock list_Message;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	private UTextBlock list_Countdown;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidget ) )
	private UImage list_ProgressBar;
	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidget ) )
	private UImage ProgressBar_OffScreen;

	UPROPERTY( EditDefaultsOnly )
	UTexture2D checkBox_NotPriority;
	UPROPERTY( EditDefaultsOnly )
	UTexture2D checkBox_IsPriority;

	// countdown clock
	private FTimerHandle countdownHandle;
	private int countdownStartMS;
	private float32 countdownLength;
	private bool countdownEnabled;
	private bool isCountdownVisible = false;

	// progress bar
	private FTimerHandle progressBarHandle;
	private int progressBarStartMS;
	private float32 progressBarLength;
	private float32 startProgress = 1.0f;
	private float32 endProgress = 0.0f;
	private bool progressBarEnabled;
	private bool isProgressBarVisible = false;

	// misc
	private bool bisHidden = false;
	private bool isPrioritized = false;
	UAS_PriorityMessageListData listItemData;
	UAS_PriorityMessageWidget_v2 parentMessage;
	FNCCoroutineSignal onStopSignal;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		Set_ColorProgressBar( PriorityMessageColor::OTHER_COLOR_CTA_MSG );
	}

	void Init( UAS_PriorityMessageListData data, UAS_PriorityMessageWidget_v2 inParentMessage )
	{
		__Update( data, inParentMessage );

		if ( listItemData.AutoHide )
			Hide();
		else if ( inParentMessage.ListMessageBox.GetChildrenCount() > 1 )
			Appear();

		listItemData.OnCreate.Broadcast( this );
	}

	private void __Update( UAS_PriorityMessageListData data, UAS_PriorityMessageWidget_v2 inParentMessage )
	{
		listItemData  = data;
		parentMessage = inParentMessage;
		SetListMessage( data.ListItemTxt );

		FLinearColor ProgressBarColor = GetPriorityMessageThemeColors( listItemData.Theme ).CTA_BG;
		Set_ColorProgressBar( ProgressBarColor );

		UpdateCheckBoxIcon();
	}

	void Set_ColorProgressBar( FLinearColor newColor )
	{
		list_ProgressBar.SetColorAndOpacity( newColor );
		ProgressBar_OffScreen.SetColorAndOpacity( newColor );
	}

	void SetListMessage( FText string )
	{
		list_Message.SetText( string );
	}

	FString GetListMessage()
	{
		return list_Message.GetText().ToString();
	}

	void UpdateCheckBoxIcon()
	{
		UTexture2D CheckBoxTexture = isPrioritized ? checkBox_IsPriority : checkBox_NotPriority;
		list_CheckBox.SetBrushFromTexture( CheckBoxTexture );
	}

	void Prioritize()
	{
		if ( !isPrioritized )
			PlayAnimation( list_PrioritizeAnim );
		isPrioritized = true;
		UpdateCheckBoxIcon();
	}

	void Deprioritize()
	{
		if ( isPrioritized )
			PlayAnimation( list_DeprioritizeAnim );
		isPrioritized = false;
		UpdateCheckBoxIcon();
	}

	float Appear()
	{
		if ( !IsHidden() )
			return 0;

		bisHidden = false;
		PlayAnimation( list_AppearAnim );

		return list_AppearAnim.GetEndTime();
	}

	void Hide()
	{
		bisHidden = true;
		PlayAnimation( list_DisappearAnim, 0.2, 1, EUMGSequencePlayMode::Forward, 10 );
	}

	float Disappear()
	{
		if ( IsHidden() )
			return 0;

		bisHidden = true;

		PlayAnimation( list_DisappearAnim );

		return list_DisappearAnim.GetEndTime();
	}

	bool IsHidden()
	{
		return bisHidden;
	}

	/****************************************************************\

	 ██████  ██████  ██    ██ ███    ██ ████████ ██████   ██████  ██     ██ ███    ██
	██      ██    ██ ██    ██ ████   ██    ██    ██   ██ ██    ██ ██     ██ ████   ██
	██      ██    ██ ██    ██ ██ ██  ██    ██    ██   ██ ██    ██ ██  █  ██ ██ ██  ██
	██      ██    ██ ██    ██ ██  ██ ██    ██    ██   ██ ██    ██ ██ ███ ██ ██  ██ ██
	 ██████  ██████   ██████  ██   ████    ██    ██████   ██████   ███ ███  ██   ████

	\****************************************************************/
	void StartCountdown_ByDuration( int inStartTimeMS, float duration, float delayShowAnim = 0.0 )
	{
		countdownLength	 = float32( duration );
		countdownStartMS = inStartTimeMS;
		countdownEnabled = true;

		System::ClearAndInvalidateTimerHandle( countdownHandle );

		__CountdownTick();

		Thread( this, n"ShowCountDown", delayShowAnim );
	}

	void StartCountdown_ByEndTime( int inEndTimeMS, float delayShowAnim = 0.0 )
	{
		int startTimeMS	 = GetTimeMilliseconds();
		countdownLength	 = TO_SECONDS( inEndTimeMS - startTimeMS );
		countdownStartMS = startTimeMS;
		countdownEnabled = true;

		System::ClearAndInvalidateTimerHandle( countdownHandle );

		__CountdownTick();

		Thread( this, n"ShowCountDown", delayShowAnim );
	}

	UFUNCTION()
	void __CountdownTick()
	{
		float32 elapsedTimeMS = GetTimeMilliseconds() - countdownStartMS;
		float32 elapsedTime	  = TO_SECONDS( int( elapsedTimeMS ) );
		float32 timeLeft	  = Math::Max( countdownLength - elapsedTime, 0.0f );

		if ( timeLeft > 0 )
		{
			float32 secRemainder = timeLeft % 60.0;
			int secLeft			 = Math::FloorToInt( secRemainder );
			int msLeft			 = Math::RoundToInt( TO_MILLISECONDS( secRemainder - float32( secLeft ) ) * 0.1 );
			if ( msLeft == 100 )
				msLeft = 99;

			FTimespan timespan		= FTimespan( 0, 0, int( timeLeft ) );
			FTimespan timespan_fake = FTimespan( timespan.Minutes, timespan.Seconds, msLeft );
			FText timeString		= Text::AsTimespan_Timespan( timespan_fake );

			list_Countdown.SetText( timeString );

			if ( countdownEnabled )
				countdownHandle = System::SetTimerForNextTick( this, "__CountdownTick" );
		}
		else
		{
			countdownEnabled   = false;
			FTimespan timespan = FTimespan( 0, 0, 0 );
			list_Countdown.SetText( Text::AsTimespan_Timespan( timespan ) );
			listItemData.OnCountDownStop.Broadcast( this, EPriorityMessageCountdownStopReason::ENDED );
		}
	}

	void StopCountdown()
	{
		System::ClearAndInvalidateTimerHandle( countdownHandle );
		countdownEnabled = false;
		listItemData.OnCountDownStop.Broadcast( this, EPriorityMessageCountdownStopReason::DISABLED );

		__CountdownTick();
	}

	UFUNCTION()
	void ShowCountDown( UNCCoroutine co, float delay )
	{
		if ( delay > 0 )
		{
			co.EndOn( this, onStopSignal );
			co.Wait( delay );
		}

		if ( isCountdownVisible )
			return;

		isCountdownVisible = true;
		PlayAnimation( list_ShowCountdownAnim );
	}

	void HideCountdown()
	{
		if ( !isCountdownVisible )
			return;

		isCountdownVisible = false;
		PlayAnimation( list_HideCountdownAnim );
	}

	void StopAndHideCountdown()
	{
		StopCountdown();
		HideCountdown();
	}

	/****************************************************************\

	██████  ██████   ██████   ██████  ██████  ███████ ███████ ███████     ██████   █████  ██████
	██   ██ ██   ██ ██    ██ ██       ██   ██ ██      ██      ██          ██   ██ ██   ██ ██   ██
	██████  ██████  ██    ██ ██   ███ ██████  █████   ███████ ███████     ██████  ███████ ██████
	██      ██   ██ ██    ██ ██    ██ ██   ██ ██           ██      ██     ██   ██ ██   ██ ██   ██
	██      ██   ██  ██████   ██████  ██   ██ ███████ ███████ ███████     ██████  ██   ██ ██   ██

	\****************************************************************/
	void StartProgressbar_ByDuration( int inStartTimeMS, float duration, float32 inStartProgress = 1.0, float32 inEndProgress = 0.0, float delayShowAnim = 0.0 )
	{
		progressBarLength  = float32( duration );
		progressBarStartMS = inStartTimeMS;
		progressBarEnabled = true;
		startProgress	   = inStartProgress;
		endProgress		   = inEndProgress;

		System::ClearAndInvalidateTimerHandle( progressBarHandle );

		__ProgressBarTick();

		Thread( this, n"ShowProgressBar", delayShowAnim );
	}

	void StartProgressbar_ByEndTime( int inEndTimeMS, float32 inStartProgress = 1.0, float32 inEndProgress = 0.0, float delayShowAnim = 0.0 )
	{
		int startTimeMS	   = GetTimeMilliseconds();
		progressBarLength  = TO_SECONDS( inEndTimeMS - startTimeMS );
		progressBarStartMS = startTimeMS;
		progressBarEnabled = true;
		startProgress	   = inStartProgress;
		endProgress		   = inEndProgress;

		System::ClearAndInvalidateTimerHandle( progressBarHandle );

		__ProgressBarTick();

		Thread( this, n"ShowProgressBar", delayShowAnim );
	}

	void UpdateProgressBarInfo( int newProgressBarStartMS, float32 newProgressBarLength )
	{
		progressBarStartMS = newProgressBarStartMS;
		progressBarLength  = newProgressBarLength;
	}

	void SetProgressBarSegments( int numSegments, float gapSizeFrac = 0.2 )
	{
		list_ProgressBar.GetDynamicMaterial().SetScalarParameterValue( n"Segments", numSegments );
		list_ProgressBar.GetDynamicMaterial().SetScalarParameterValue( n"GapSizeFrac", gapSizeFrac );
	}

	UFUNCTION()
	void __ProgressBarTick()
	{
		float32 elapsedTimeMS = GetTimeMilliseconds() - progressBarStartMS;
		float32 elapsedTime	  = TO_SECONDS( int( elapsedTimeMS ) );
		float32 timeLeft	  = Math::Max( progressBarLength - elapsedTime, 0.0f );

		float32 buffer = 0.75f; // just saw the progress bar has time to animate out fully before it starts counting down
		float Frac	   = Math::GetMappedRangeValueClamped( FVector2D( 0, progressBarLength - buffer ), FVector2D( 0, 1 ), timeLeft );
		list_ProgressBar.GetDynamicMaterial().SetScalarParameterValue( n"Fill", Frac );

		if ( timeLeft > 0 )
		{
			if ( progressBarEnabled )
				progressBarHandle = System::SetTimerForNextTick( this, "__progressBarTick" );
		}
		else
		{
			progressBarEnabled = false;
			listItemData.OnProgressBarStop.Broadcast( this, EPriorityMessageCountdownStopReason::ENDED );
		}
	}

	void StopProgressBar()
	{
		System::ClearAndInvalidateTimerHandle( progressBarHandle );
		progressBarEnabled = false;
		listItemData.OnProgressBarStop.Broadcast( this, EPriorityMessageCountdownStopReason::DISABLED );

		__ProgressBarTick();
	}

	UFUNCTION()
	void ShowProgressBar( UNCCoroutine co, float delay )
	{
		if ( delay > 0 )
		{
			co.EndOn( this, onStopSignal );
			co.Wait( delay );
		}

		if ( isProgressBarVisible )
			return;

		isProgressBarVisible = true;
		PlayAnimation( list_ShowProgressBarAnim );
	}

	void HideProgressBar()
	{
		if ( !isProgressBarVisible )
			return;

		isProgressBarVisible = false;
		PlayAnimation( list_HideProgressBarAnim );
	}

	void StopAndHideProgressBar()
	{
		StopProgressBar();
		HideProgressBar();
	}

	/****************************************************************\

	██    ██ ██████  ██████   █████  ████████ ███████
	██    ██ ██   ██ ██   ██ ██   ██    ██    ██
	██    ██ ██████  ██   ██ ███████    ██    █████
	██    ██ ██      ██   ██ ██   ██    ██    ██
	 ██████  ██      ██████  ██   ██    ██    ███████

	\****************************************************************/
	private UAS_PriorityMessageListData __updateData;
	private bool __isUpdating = false;
	void UpdateListItem( UAS_PriorityMessageListData updateData )
	{
		__updateData = updateData;

		if ( !IsUpdating() )
			Thread( this, n"__UpdateListItemThread" );
	}

	private void ClearUpdate()
	{
		__updateData = nullptr;
		__isUpdating = false;
	}

	bool IsUpdating()
	{
		return __isUpdating;
	}

	UAS_PriorityMessageListData GetUpdateData()
	{
		return __updateData;
	}

	UFUNCTION()
	private void __UpdateListItemThread( UNCCoroutine co )
	{
		__isUpdating = true;

		FListUpdateParams params;
		params.Init( this, __updateData );
		if ( !params.HasDifference() )
		{
			ClearUpdate();
			return;
		}

		bool localUpdatePriority = isPrioritized;
		bool onStopCalled		 = false;
		if ( !IsHidden() )
		{
			float duration;

			if ( params.shouldHide )
			{
				duration = list_DisappearAnim.GetEndTime();
				Disappear();
			}
			else
			{
				duration = list_SlideOutAnim.GetEndTime();
				PlayAnimation( list_SlideOutAnim );
			}

			HideCountdown();
			HideProgressBar();
			if ( localUpdatePriority )
				parentMessage.PlayAnimation( parentMessage.Highlight_SlideOutAnim );

			if ( params.differentTemplate )
			{
				onStopCalled = true;
				OnStop();
			}

			co.Wait( duration );
		}

		// do again since changes could happen after the wait
		params.Init( this, __updateData );
		if ( params.HasDifference() )
			__Update( __updateData, parentMessage );

		// if we killed any OnCreate Loops before the wait, or if our template is different... we need run them
		if ( ( params.differentTemplate || onStopCalled ) && listItemData.OnCreate.IsBound() )
		{
			listItemData.OnCreate.Broadcast( this );
			// update again in case the OnCreate function changed __updatedData
			params.Init( this, __updateData );
			if ( params.HasDifference() )
				__Update( __updateData, parentMessage );
		}

		if ( !params.shouldHide )
		{
			if ( IsHidden() )
				Appear();
			else
				PlayAnimation( list_SlideInAnim );
		}

		if ( localUpdatePriority )
			parentMessage.PlayAnimation( parentMessage.Highlight_SlideInAnim );

		ClearUpdate();
	}

	void OnStop()
	{
		onStopSignal.Emit();
		listItemData.OnStop.Broadcast( this );
	}
}
