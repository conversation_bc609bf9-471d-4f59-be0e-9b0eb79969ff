UCLASS(Abstract)
class UAS_BleedoutWidget : UUserWidgetDefault
{
    UPROPERTY()
    float bleedoutFrac = 0.0;

	private const AActor owner;

	UPROPERTY(BindWidget, NotVisible)
	UImage bleedoutIcon;

	int startTime = 0;
	int endTime = 0;

	private UMaterialInstanceDynamic bleedoutMat;

	bool isReviving;

	UFUNCTION(BlueprintOverride)
	void Construct()
	{
		bleedoutMat = bleedoutIcon.GetDynamicMaterial();
	}

    UFUNCTION(BlueprintCallable)
    void EnableWidgetTick()
    {
        System::SetTimer(this, n"WidgetTick", 0.01, true);
    }

    UFUNCTION(BlueprintCallable)
    void DisableWidgetTick()
    {
        System::ClearTimer(this, "WidgetTick");
    }

    UFUNCTION(BlueprintOverride)
    void Destruct()
    {
        DisableWidgetTick();
    }

	void SetOwner( const AActor newOwner )
	{
		if(owner == newOwner)
			return;

		if ( IsValid( newOwner ) && IsValid( newOwner.TotemComponent() ) )
		{
			UAS_RespawnTotemComponent totemComp = newOwner.TotemComponent();
			totemComp.net_totemEndTime.OnReplicated().UnbindObject(this);
			totemComp.net_totemStartTime.OnReplicated().UnbindObject(this);
			totemComp.net_isBeingRevived.OnReplicated().UnbindObject(this);
			totemComp.onTotemStarted.Unbind( this, n"OnBleedoutStatusChanged" );
			totemComp.onTotemEnded.Unbind( this, n"OnBleedoutStatusChanged" );

			startTime = 0;
			endTime = 0;

			// TODO: Generic portrait if not set
			AAS_PlayerEntity ownerAsPlayer = Cast<AAS_PlayerEntity>( newOwner );
			if ( IsValid( ownerAsPlayer ) )
			{
				UAS_PlayerClassManager classManager = ownerAsPlayer.ClassManager();
				if ( IsValid( classManager ) )
				{
					UTexture2D icon = classManager.GetClassData().characterSettingsAsset.CharacterIcon;
					bleedoutMat.SetTextureParameterValue( n"PortraitTexture", icon );
				}
			}
		}

		owner = newOwner;

		if ( IsValid( owner ) && IsValid( owner.TotemComponent() ) )
		{
			UAS_RespawnTotemComponent totemComp = owner.TotemComponent();
			totemComp.net_totemEndTime.OnReplicated().AddUFunction(this, n"OnBleedoutTimeChanged");
			totemComp.net_totemStartTime.OnReplicated().AddUFunction(this, n"OnBleedoutTimeChanged");
			totemComp.onTotemStarted.AddUFunction( this, n"OnBleedoutStatusChanged" );
			totemComp.onTotemEnded.AddUFunction( this, n"OnBleedoutStatusChanged" );
			totemComp.net_isBeingRevived.OnReplicated().AddUFunction(this, n"OnReviveStarted");

			OnBleedoutStatusChanged( Cast<AAS_PlayerEntity>( owner ) );
		}

		OnBleedoutTimeChanged(0, 0);
	}

    UFUNCTION()
    private void OnBleedoutTimeChanged(int oldValue, int newValue)
    {
		if ( !IsValid( owner ) )
			return;

		UAS_RespawnTotemComponent totemComp = owner.TotemComponent();
		if ( IsValid( totemComp ) )
		{
			startTime = totemComp.net_totemStartTime;
			endTime = totemComp.net_totemEndTime;
		}
		else
		{
			startTime = 0;
			endTime = 0;
		}
    }

	void SetBleedoutTime( int inStart, int inEnd )
	{
		startTime = inStart;
		endTime = inEnd;
	}

    UFUNCTION(NotBlueprintCallable)
    void WidgetTick()
    {
        if ( startTime > 0 && endTime > 0 )
        {
            float32 bleedoutDuration = endTime - startTime;
            bleedoutFrac = bleedoutDuration == 0 ? 0 : Math::Clamp( ( GetTimeMilliseconds() - startTime ) / bleedoutDuration, 0.0, 1.0 );
			bleedoutMat.SetScalarParameterValue( n"BleedoutFrac", bleedoutFrac );
        }
    }
	
	UFUNCTION()
	private void OnBleedoutStatusChanged( AAS_PlayerEntity asPlayer )
	{
		ANCPlayerCharacter localPawn = Client_GetLocalPawn();
		if ( !IsValid( localPawn ) )
		{
			return;
		}

		if ( !IsValid( asPlayer ) )
		{
			return;
		}

		// yucky
		bool isDown = asPlayer.GetIsDowned();
		bool isFriendly = IsFriendly( asPlayer.GetTeam(), localPawn.GetTeam() );
		bool shouldShow = isFriendly && isDown && !isReviving;

		if ( shouldShow )
		{
			SetWidgetVisibilitySafe( bleedoutIcon, shouldShow ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
			EnableWidgetTick();
		}
		else
		{
			SetWidgetVisibilitySafe( bleedoutIcon, shouldShow ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
			DisableWidgetTick();
		}
	
	}

    UFUNCTION()
    private void OnReviveStarted(bool oldValue, bool newValue)
    {
		isReviving = newValue;
		OnBleedoutStatusChanged( Cast<AAS_PlayerEntity>( owner ) );
    }
}