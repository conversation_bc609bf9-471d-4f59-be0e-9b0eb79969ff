UCLASS()
class UAS_HUD_MaximapInputManager : UAS_HUDCompoent
{

	UFUNCTION( BlueprintOverride )
	void OnInit()
	{
		Super::OnInit();
		playerController.GetLegacyInputComponent().BindAction( n"maximap", EInputEvent::IE_Pressed, FInputActionHandlerDynamicSignature( this, n"OnMaximapButtonPressed" ) );
	}

	UFUNCTION()
	private void OnMaximapButtonPressed( FKey Key )
	{
		if ( IsValid( HUD ) )
		{
			/* 
				TODO NC1-17137 @jmccarty: Leaving this comment to help track the solution for NC1-17137 in the future.

				NC1-17137 is happening because we are swapping input modes when opening up the maximap and input that was processed on the map widget
				is falling through to this delegate. For example, if you press M, this function is called. Because we are in UI mode when the screen opens,
				all future calls to this function while the map is opened are not dispatched. If you set up the map widget to close when M is pressed, M is handled
				once by the map widget and then it also gets handled here in that same frame. To fix this temporarily, you can see that I wait to close the map until
				the next frame in maximapWidget.as
			*/
			HUD.ToggleMaximap();
		}
	}
}