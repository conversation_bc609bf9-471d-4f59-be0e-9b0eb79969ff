namespace RaidGolemShield
{
	const float32 SHIELD_HEALTH					  = 600.0;
	const float32 SCALE_IN_DURATION				  = 0.8;
	const float32 SCALE_IN_COLLISION_ENABLE_TIME  = 0.8;
	const float32 SCALE_OUT_DURATION			  = 0.0;
	const float32 SHIELD_REGEN_POST_DESTROY_DELAY = 8;
	const float32 SHIELD_REGEN_START_DELAY		  = 3;
}

event void FOnShieldDestroyedCallback();

UCLASS( Abstract )
class AAS_RaidGolemShield : ANCPlayerActivatedActor
{
	// TODO -- joe - This could be refactored to toggle tick on and off at the right times,
	// but given that there can only ever be two Jades in a game, and this is only ticking
	// while they are in demon form, I'd rather not complicate this for a minor optimization
	// just yet.

	default bAutoRegisterServerFixedTick = true;
	default bAutoRegisterClientFixedTick = true;

	UPROPERTY( Transient )
	FNCNetFloat net_savedShieldHealthFrac( 1.0f, 0.0f, 1.0f );
	private float savedHealthRaw = RaidGolemShield::SHIELD_HEALTH;

	UPROPERTY()
	FNCNetBool net_destroyed( false );

	UPROPERTY()
	FOnShieldDestroyedCallback OnShieldDestroyed;

	UPROPERTY( EditDefaultsOnly )
	UCurveVector scaleUpCurve;

	UPROPERTY( EditDefaultsOnly )
	UNiagaraSystem breakFx;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset breakSound;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset activateSound;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset deactivateSound;

	UPROPERTY( DefaultComponent, RootComponent )
	USceneComponent root;

	UPROPERTY( DefaultComponent, Attach = shieldScene )
	USceneComponent explodeFxMarker;

	UPROPERTY( DefaultComponent )
	USceneComponent shieldScene;

	UPROPERTY( DefaultComponent )
	UAS_NCAudioComponent shieldCrackSound;

	UPROPERTY( DefaultComponent, Attach = shieldOffsetScene )
	UStaticMeshComponent shieldMesh;

	UPROPERTY( DefaultComponent, Attach = shieldScene )
	USceneComponent shieldOffsetScene;

	UPROPERTY( EditDefaultsOnly )
	FName attachBoneName = n"Lf_hand_prop";

	UAS_WeaponContext_RaidGolemSmash weaponContext;

	UPROPERTY( DefaultComponent )
	UHealthComponent healthComponent;
	default healthComponent.SetMaxHealth( RaidGolemShield::SHIELD_HEALTH );
	default healthComponent.SetHealth( RaidGolemShield::SHIELD_HEALTH );

	UFUNCTION( BlueprintOverride )
	void ServerBeginPlay()
	{
		healthComponent.BP_OnPreReceivedDamage.BindUFunction( this, n"OnPreDamage" );
		healthComponent.BP_OnPostReceivedDamage.AddUFunction( this, n"OnDamaged" );
	}

	UFUNCTION( BlueprintOverride )
	void ClientBeginPlay()
	{
		SetActorTickEnabled( false );
		SetActorInterpolationTickEnabled( true );

		net_destroyed.OnReplicated().AddUFunction( this, n"OnDestroyedReplicated" );
		net_savedShieldHealthFrac.OnReplicated().AddUFunction( this, n"OnShieldFracChanged" );
	}

	UFUNCTION()
	private void OnShieldFracChanged( float32 oldValue, float32 newValue )
	{
		for ( int i = 0; i < shieldMesh.GetNumMaterials(); i++ )
		{
			UMaterialInstanceDynamic dyn = shieldMesh.CreateDynamicMaterialInstance( i );
			dyn.SetScalarParameterValue( n"ShieldHealth", newValue );
		}

		UpdateShieldCrackSound();
	}

	void UpdateShieldCrackSound()
	{
		bool shouldPlay = ShouldPlayCrackSounds();
		if ( shouldPlay && !shieldCrackSound.IsActive() )
		{
			shieldCrackSound.Activate(true);
		}
		else if ( !shouldPlay && shieldCrackSound.IsActive() )
		{
			shieldCrackSound.Deactivate();
		}
	}

	bool ShouldPlayCrackSounds() const
	{
		return net_savedShieldHealthFrac < 0.5 && !net_destroyed && IsActivated;
	}

	UFUNCTION( BlueprintOverride )
	void BeginPlay()
	{
		if ( !WasEverActivated() )
		{
			SetActorEnableCollision( false );
			SetActorHiddenInGame( true );
		}
	}

	UFUNCTION()
	private void OnDestroyedReplicated( bool oldValue, bool newValue )
	{
		if ( newValue )
		{
			OnShieldDestroyed.Broadcast();
		}

		UpdateShieldCrackSound();
	}

	UFUNCTION( BlueprintOverride )
	void OnIsActivatedChanged( bool newIsActivated )
	{
		SetActorHiddenInGame( !newIsActivated );
		if ( IsClient() )
		{
			if ( newIsActivated )
				Client_EmitSoundAtLocation_WithOwner( activateSound, GetOwnerPlayer(), explodeFxMarker.GetWorldLocation() );
			else
				Client_EmitSoundAtLocation_WithOwner( deactivateSound, GetOwnerPlayer(), explodeFxMarker.GetWorldLocation() );

			UpdateShieldCrackSound();
		}
	}

	UFUNCTION( BlueprintOverride )
	void FixedTick(const FNCFixedTickContext& context)
	{
		UpdateShieldState();
	}

	UFUNCTION( BlueprintOverride )
	void InterpolationTick( FNCInterpolationTickContext context )
	{
		UpdateShieldState();
	}

	void UpdateShieldState()
	{
		if ( !WasEverActivated() )
			return;

		if ( IsActivated )
		{
			SetActorHiddenInGame( false );
			SetActorEnableCollision( GetSecondsSinceActivatedChanged() >= RaidGolemShield::SCALE_IN_COLLISION_ENABLE_TIME );

			float elapsedScale = Math::GetMappedRangeValueClamped( FVector2D( 0.f, RaidGolemShield::SCALE_IN_DURATION ), FVector2D( 0.0f, 1.0f ), GetSecondsSinceActivatedChanged() );
			FVector newScale   = scaleUpCurve.GetVectorValue( elapsedScale );
			shieldOffsetScene.SetRelativeScale3D( newScale );
		}
		else
		{
			SetActorEnableCollision( false );

			if ( GetSecondsSinceActivatedChanged() < RaidGolemShield::SCALE_OUT_DURATION )
			{
				float elapsedScale = Math::GetMappedRangeValueClamped( FVector2D( 0.f, RaidGolemShield::SCALE_OUT_DURATION ), FVector2D( 1.0f, 0.0f ), GetSecondsSinceActivatedChanged() );
				FVector newScale   = scaleUpCurve.GetVectorValue( elapsedScale );
				shieldOffsetScene.SetRelativeScale3D( newScale );
			}
			else
			{
				SetActorHiddenInGame( true );
			}
		}
	}

	UFUNCTION()
	private void OnPreDamage( FDamageInfo& damageInfo )
	{
		if ( IsValid( damageInfo.attacker ) )
		{
			if ( damageInfo.attacker.GetTeam() == GetOwnerPlayer().GetTeam() )
			{
				damageInfo.damage = 0;
				return;
			}
		}
	}

	UFUNCTION()
	private void OnDamaged( UHealthComponent damagedComponent, const FDamageInfo&in damageInfo )
	{
		if ( net_destroyed )
			return;

		if ( IsValid( weaponContext ) )
			weaponContext.SetSavedHealth( healthComponent.GetHealth() );

		if ( healthComponent.GetHealth() <= 0 )
		{
			Server_EmitSoundAtLocation_WithOwner( breakSound, GetOwnerPlayer(), explodeFxMarker.GetWorldLocation(), explodeFxMarker.GetWorldRotation() );
			Server_SpawnEffectAtLocation_OneShot( breakFx, explodeFxMarker.GetWorldLocation(), explodeFxMarker.GetWorldRotation() );
			net_destroyed.SetNetValue( true );
			OnShieldDestroyed.Broadcast();
		}
	}
}
