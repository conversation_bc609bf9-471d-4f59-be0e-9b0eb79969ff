UCLASS( Abstract )
class UAS_PlayerWidget : UNC_DisplayWidget
{
	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage playerPortrait;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UTextBlock playerName;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage playerIndicator;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage playerHealth;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage shieldBreakerIndicator;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage disconnectedIndicator;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage playerShield;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage hardenBar;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage coinImage;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UPanelWidget coinContainer;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_CommonTextBlock coinCount;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_CommonTextBlock respawnTimer;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_EquipmentStatusWidget equipmentHelmet;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_EquipmentStatusWidget equipmentSaddle;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_EquipmentStatusWidget equipmentTrinket;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden )
	private float maxBarWidth = 235.0f;

	private AAS_PlayerEntity player;
	private UMaterialInstanceDynamic portraitMaterial;
	private UMaterialInstanceDynamic healthMaterial;
	private UMaterialInstanceDynamic shieldMaterial;
	private UMaterialInstanceDynamic hardenBarMaterial;
	private int lastShieldSegmentCount = 0;
	private int currentState = GameConst::INDEX_NONE;
	private UMaterialInstanceDynamic shieldBreakerMaterial;

	// Incoming health damage thread vars
	private FNCCoroutineSignal incomingHealthDamageEndSignal;
	private TOptional<float> startingIncomingHealthDamage;
	private float currentIncomingHealthDamage = 0.0f;
	private float lastHealthFrac = 0.0f;

	// Incoming shield health damage thread vars
	private FNCCoroutineSignal incomingShieldDamageEndSignal;
	private TOptional<float> startingIncomingShieldDamage;
	private float currentIncomingShieldDamage = 0.0f;
	private float lastShieldFrac = 0.0f;

	private const FName REGENERATE_PARAMETER_NAME = n"RecoveryPercentage";
	private const FName HEALTH_PARAMETER_NAME = n"HealthPercentage";
	private const FName PORTRAIT_PARAMETER_NAME = n"PortraitTexture";
	private const FName PLAYER_STATE_PARAMETER_NAME = n"PlayerState";
	private const FName DAMAGE_PARAMETER_NAME = n"IncomingDamage";
	private const FName THEME_COLOR_PARAMETER = n"ThemeColor";
	private const float DELTA = 0.05f;
	private const float INITIAL_DELAY = 0.5f;
	private const int ALIVE_PLAYER_STATE = 0;
	private const int DOWNED_PLAYER_STATE = 1;
	private const int DEAD_PLAYER_STATE = 2;
	private const int DISCONNECTED_PLAYER_STATE = 3;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		// Client callbacks
		UCL_ScriptCallbacks clientCallbacks = ClientCallbacks();
		if ( IsValid( clientCallbacks ) )
		{
			clientCallbacks.OnPawnHealthChanged.AddUFunction( this, n"OnPawnHealthChanged" );
			clientCallbacks.OnPawnDied.AddUFunction( this, n"OnPawnDied" );
			clientCallbacks.OnPawnRespawned.AddUFunction( this, n"OnPawnRespawned" );
		}

		// Script callbacks
		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			// Respawning
			scriptCallbacks.server_onRespawnUnavailable.AddUFunction( this, n"OnRespawnUnavailable" );

			// Health healing and status effects
			scriptCallbacks.client_onHealthRegenningChanged.AddUFunction( this, n"OnHealthRegenningChanged" );
			scriptCallbacks.client_onHardenedHealthChanged.AddUFunction( this, n"OnHardenedHealthChanged" );
			scriptCallbacks.RegisterSignalCallback( Signals::ON_HEAL_STARTED_CLIENT, this, n"OnHealStarted" );
			scriptCallbacks.RegisterSignalCallback( Signals::ON_HEAL_CANCELLED_CLIENT, this, n"OnHealCancelled" );
			scriptCallbacks.RegisterSignalCallback( Signals::ON_HEAL_COMPLETED_CLIENT, this, n"OnHealCompleted" );

			// Teleporting
			scriptCallbacks.server_onPlayerTeleported.AddUFunction( this, n"OnPlayerTeleported" );

			// Shield breaker
			scriptCallbacks.client_onRaidObjectiveStateChanged.AddUFunction( this, n"OnTeamObjectiveStateChanged" );
			scriptCallbacks.client_onShieldBreakerStatusChanged.AddUFunction( this, n"OnShieldBreakerStatusChanged" );

			// Equipment
			scriptCallbacks.client_onPlayerEquipmentChanged.AddUFunction( this, n"OnPlayerEquipmentChanged" );

			scriptCallbacks.client_onPlayerSettingsChanged.AddUFunction( this, n"OnPlayerSettingsChanged" );
		}

		// Other callbacks
		UAS_ShieldSystem shieldSystem = ShieldSystem();
		if ( IsValid( shieldSystem ) )
		{
			shieldSystem.client_onAnyPlayerShieldChanged.AddUFunction( this, n"OnAnyPlayerShieldChanged" );
		}

		portraitMaterial = CreateDynamicMaterialFromImageBrush( playerPortrait );
		if ( IsValid( portraitMaterial ) )
		{
			playerPortrait.SetBrushFromMaterial( portraitMaterial );
			ChangePlayerState( ALIVE_PLAYER_STATE );
		}

		healthMaterial = CreateDynamicMaterialFromImageBrush( playerHealth );
		if ( IsValid( healthMaterial ) )
		{
			playerHealth.SetBrushFromMaterial( healthMaterial );

			lastHealthFrac = 1.0f;
			UpdateHealthMaterialParameter( HEALTH_PARAMETER_NAME, lastHealthFrac );
			UpdateHealthMaterialParameter( DAMAGE_PARAMETER_NAME, 0.0f );
			UpdateHealthMaterialParameter( REGENERATE_PARAMETER_NAME, 0.0f );
		}

		shieldMaterial = CreateDynamicMaterialFromImageBrush( playerShield );
		if ( IsValid( shieldMaterial ) )
		{
			playerShield.SetBrushFromMaterial( shieldMaterial );

			lastShieldFrac = 1.0f;
			UpdateShieldMaterialParameter( HEALTH_PARAMETER_NAME, lastShieldFrac );
			UpdateShieldMaterialParameter( DAMAGE_PARAMETER_NAME, 0.0f );
			UpdateShieldMaterialParameter( REGENERATE_PARAMETER_NAME, 0.0f );
		}

		hardenBarMaterial = CreateDynamicMaterialFromImageBrush( hardenBar );
		if ( IsValid( hardenBarMaterial ) )
		{
			hardenBar.SetBrushFromMaterial( hardenBarMaterial );
			SetUniqueScalarParameter( hardenBarMaterial, HEALTH_PARAMETER_NAME, 0.0f );
		}

		SetImageBrushIcon( coinImage, GetLootDataByIndex( GameModeDefaults().UI_TrackedResource ).icon );
	}

	UFUNCTION( BlueprintOverride )
	void Destruct()
	{
		// Client callbacks
		UCL_ScriptCallbacks clientCallbacks = ClientCallbacks();
		if ( IsValid( clientCallbacks ) )
		{
			clientCallbacks.OnPawnHealthChanged.Unbind( this, n"OnPawnHealthChanged" );
			clientCallbacks.OnPawnDied.Unbind( this, n"OnPawnDied" );
			clientCallbacks.OnPawnRespawned.Unbind( this, n"OnPawnRespawned" );
		}

		// Script callbacks
		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			// Respawning
			scriptCallbacks.server_onRespawnUnavailable.Unbind( this, n"OnRespawnUnavailable" );

			// Health healing and status effects
			scriptCallbacks.client_onHealthRegenningChanged.Unbind( this, n"OnHealthRegenningChanged" );
			scriptCallbacks.client_onHardenedHealthChanged.Unbind( this, n"OnHardenedHealthChanged" );
			scriptCallbacks.UnRegisterSignalCallback( Signals::ON_HEAL_STARTED_CLIENT, this, n"OnHealStarted" );
			scriptCallbacks.UnRegisterSignalCallback( Signals::ON_HEAL_CANCELLED_CLIENT, this, n"OnHealCancelled" );
			scriptCallbacks.UnRegisterSignalCallback( Signals::ON_HEAL_COMPLETED_CLIENT, this, n"OnHealCompleted" );

			// Teleporting
			scriptCallbacks.server_onPlayerTeleported.Unbind( this, n"OnPlayerTeleported" );

			// Shield breaker
			scriptCallbacks.client_onRaidObjectiveStateChanged.Unbind( this, n"OnTeamObjectiveStateChanged" );
			scriptCallbacks.client_onShieldBreakerStatusChanged.Unbind( this, n"OnShieldBreakerStatusChanged" );

			// Equipment
			scriptCallbacks.client_onPlayerEquipmentChanged.Unbind( this, n"OnPlayerEquipmentChanged" );
		}

		// Other callbacks
		UAS_ShieldSystem shieldSystem = ShieldSystem();
		if ( IsValid( shieldSystem ) )
		{
			shieldSystem.client_onAnyPlayerShieldChanged.Unbind( this, n"OnAnyPlayerShieldChanged" );
		}
	}

	bool wasConnected	 = false;
	bool hasRaidUltReady = false;
	UFUNCTION( BlueprintOverride )
	void Tick( FGeometry MyGeometry, float InDeltaTime )
	{
		if ( !IsValid( player ) )
			return;

		// TODO @kskye NC1-11731: We need to remove this tick when we have a way to detect when a player connects/disconnects
		{
			bool isConnected = player.IsPlayerConnected();
			if ( wasConnected != isConnected )
			{
				OnPlayerConnectionStateChanged( player, isConnected );
				wasConnected = isConnected;
			}
		}

		// TODO @jmccarty: Work with gameplay to get this to be a networked event so we can remove the tick
		{
			bool isRaidUltReady = PlayerCanUseRaidUlt( player );
			if ( hasRaidUltReady != isRaidUltReady )
			{
				OnRaidUltAvailableChanged( player, isRaidUltReady );
				hasRaidUltReady = isRaidUltReady;
			}
		}
	}

	void SetPlayer( ANCPlayerCharacter newPlayer )
	{
		if ( newPlayer == player )
			return;

		OnPlayerSet( Cast<AAS_PlayerEntity>( newPlayer ) );
	}

	FScriptDelegateHandle onHardenAddedHandle;
	FScriptDelegateHandle onHardenRemovedHandle;

	UFUNCTION( BlueprintEvent )
	void OnPlayerSet( AAS_PlayerEntity newPlayer )
	{
		if ( IsValid( player ) )
		{
			UAS_PlayerClassManager classManager = player.ClassManager();
			if ( IsValid( classManager ) )
			{
				player.classManager.client_onClassIndexChangedCallback.Unbind( this, n"OnClassIndexChanged" );
			}

			player.RemoveStatusEffectHasBegunCallback( GameplayTags::StatusEffect_Hardened, onHardenAddedHandle );
			player.RemoveStatusEffectHasEndedCallback( GameplayTags::StatusEffect_Hardened, onHardenRemovedHandle );
			player.client_onAnyBackpackChanged.Unbind( this, n"OnBackpackContentsChanged" );

			player.HealthComponent.OnNetworkedShieldHealthChanged.Unbind( this, n"OnNetworkedShieldHealthChanged" );

			UAS_SelectablePlayerRespawnComponent selectableComp = player.selectableRespawnManager;
			selectableComp.OnRespawnTimeRemainingChanged.Unbind( this, n"OnRespawnTimeRemainingChanged" );
		}

		if ( IsValid( newPlayer ) )
		{
			// Save off the new player here for later
			player = newPlayer;

			// We only care about the coins for the local player
			bool isLocalPlayer = player == Client_GetLocalASPawn();
			bool showCoins	   = isLocalPlayer || GameModeDefaults().GamemodeRules_ShowCoinsOnAll;
			SetWidgetVisibilitySafe( coinContainer, showCoins ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );

			UAS_PlayerClassManager classManager = player.ClassManager();
			if ( IsValid( classManager ) )
			{
				classManager.client_onClassIndexChangedCallback.AddUFunction( this, n"OnClassIndexChanged" );

				// Initialize the class
				FClassDataStruct classData = classManager.GetClassData();
				OnClassIndexChanged( FGameplayTag(), classData.id );
			}

			// Set the player color and name
			FLinearColor color = GetUIColorForPlayer( player );
			playerIndicator.SetColorAndOpacity( color );
			playerName.SetText( player.GetPlayerNameAsText() );

			player.HealthComponent.OnNetworkedShieldHealthChanged.AddUFunction( this, n"OnNetworkedShieldHealthChanged" );

			player.AddStatusEffectHasBegunCallback( GameplayTags::StatusEffect_Hardened, this, n"OnHardenAdded" );
			player.AddStatusEffectHasEndedCallback( GameplayTags::StatusEffect_Hardened, this, n"OnHardenRemoved" );
			player.client_onAnyBackpackChanged.AddUFunction( this, n"OnBackpackContentsChanged" );
			player.net_turnOrder.OnReplicated().AddUFunction( this, n"OnTurnOrderReplicated" );

			UAS_SelectablePlayerRespawnComponent selectableComp = player.selectableRespawnManager;
			selectableComp.OnRespawnTimeRemainingChanged.AddUFunction( this, n"OnRespawnTimeRemainingChanged" );
			OnRespawnTimeRemainingChanged( selectableComp.GetRespawnTimeRemaining(), selectableComp.GetRespawnPercentRemaining() );

			// Initialize health
			FHealthChangedInfo healthInfo;
			if ( GetFilledHealthChangedInfoForPlayer( player, healthInfo ) )
			{
				OnPawnHealthChanged( healthInfo );
			}

			// Initialize the shield breaker icon
			UpdateShieldBreakerDisplay();

			// Initialize the armor and hardened state
			OnBackpackContentsChanged( player );

			float hardenedValue = player.GetStatusEffectValue( GameplayTags::StatusEffect_Hardened );
			if ( hardenedValue > 0.0f )
			{
				OnHardenAdded( player );
			}
			else
			{
				OnHardenRemoved( player );
			}

			// Initialize equipment
			FPlayerEquipmentData helmet = player.GetHelmet();
			equipmentHelmet.SetEquipmentInfo( helmet );

			FPlayerEquipmentData saddle = player.GetSaddle();
			equipmentSaddle.SetEquipmentInfo( saddle );

			FPlayerEquipmentData trinket = player.GetTrinket();
			equipmentTrinket.SetEquipmentInfo( trinket );

			// Initialize the player state
			int state = ALIVE_PLAYER_STATE;
			if ( !player.IsPlayerConnected() )
			{
				state = DISCONNECTED_PLAYER_STATE;
			}
			else if ( !player.IsAlive() )
			{
				state = player.GetCanBeRespawned() ? DOWNED_PLAYER_STATE : DEAD_PLAYER_STATE;
			}

			ChangePlayerState( state );
			OnPlayerSettingsChanged( player, player.GetPlayerSettingsAsset() );
		}

		InvalidateLayoutAndVolatility();
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnClassIndexChanged( FGameplayTag oldValue, FGameplayTag newValue )
	{
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnAnyPlayerShieldChanged( AAS_PlayerEntity inPlayer, FShieldItemData newShieldData )
	{
		if ( !ValidatePlayer( inPlayer ) )
			return;

		FHealthChangedInfo healthInfo;
		if ( GetFilledHealthChangedInfoForPlayer( player, healthInfo ) )
		{
			UpdateShieldHealth( healthInfo );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnPawnHealthChanged( FHealthChangedInfo healthInfo )
	{
		const ANCPlayerCharacter inPlayer = healthInfo.pawn;
		if ( !ValidatePlayer( inPlayer ) )
			return;

		// TODO NC1-11829 @jmccarty: This bug is causing regen bars to disappear on health because health goes up over time and we are getting misfires
		UpdatePlayerHealth( healthInfo );
		UpdateShieldHealth( healthInfo );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnPawnDied( ANCPlayerCharacter inPlayer )
	{
		if ( !ValidatePlayer( inPlayer ) )
			return;

		ChangePlayerState( DOWNED_PLAYER_STATE );

		// NC1-12113 TODO @jmccarty: Remove this when NC1-11829 has been addressed
		{
			lastHealthFrac = 0.01f;
			FHealthChangedInfo healthInfo;
			if ( GetFilledHealthChangedInfoForPlayer( player, healthInfo ) )
			{
				UpdatePlayerHealth( healthInfo );
			}
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnPawnRespawned( ANCPlayerCharacter inPlayer )
	{
		if ( !ValidatePlayer( inPlayer ) )
			return;

		ChangePlayerState( ALIVE_PLAYER_STATE );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnRespawnUnavailable( AAS_PlayerEntity inPlayer )
	{
		if ( !ValidatePlayer( inPlayer ) )
			return;

		ChangePlayerState( inPlayer.IsAlive() ? ALIVE_PLAYER_STATE : DEAD_PLAYER_STATE );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnHealthRegenningChanged( AAS_PlayerEntity inPlayer, bool isRegenning )
	{
		if ( !ValidatePlayer( inPlayer ) )
			return;

		float32 regenValue = isRegenning ? 1.0f : 0.0f;
		UpdateHealthMaterialParameter( REGENERATE_PARAMETER_NAME, regenValue );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnTeamObjectiveStateChanged( int teamID, int oldValue, int newValue )
	{
		if ( !IsValid( player ) || teamID != player.GetTeam() )
			return;

		UpdateShieldBreakerDisplay();
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnShieldBreakerStatusChanged( AAS_PlayerEntity shieldBreakerPlayer )
	{
		if ( !IsValid( player ) || !IsValid( shieldBreakerPlayer ) || shieldBreakerPlayer.GetTeam() != player.GetTeam() )
			return;

		UpdateShieldBreakerDisplay();
	}

	UFUNCTION()
	private void OnPlayerEquipmentChanged( AAS_PlayerEntity ownerPlayer, FPlayerEquipmentData newEquipmentData )
	{
		if ( !ValidatePlayer( ownerPlayer ) )
			return;

		switch ( newEquipmentData.slot )
		{
			case EEquipmentSlot::HELMET:
				equipmentHelmet.SetEquipmentInfo( newEquipmentData );
				break;
			case EEquipmentSlot::SADDLE:
				equipmentSaddle.SetEquipmentInfo( newEquipmentData );
				break;
			case EEquipmentSlot::TRINKET:
				equipmentTrinket.SetEquipmentInfo( newEquipmentData );
				break;
			default:
				break;
		}
	}

	UFUNCTION()
	private void OnPlayerSettingsChanged( AAS_PlayerEntity _player, UPlayerSettingsAsset newSettings )
	{
		if ( player != _player || !IsValid( portraitMaterial ) )
			return;

		portraitMaterial.SetTextureParameterValue( PORTRAIT_PARAMETER_NAME, newSettings.CharacterIcon );

		UAS_PlayerClassManager classManager = player.ClassManager();
		if ( IsValid( classManager ) )
		{
			FClassDataStruct data = classManager.GetClassData();

			FLinearColor themeColor;
			GetSafeColor( data.themeColorId, themeColor );
			portraitMaterial.SetVectorParameterValue( THEME_COLOR_PARAMETER, themeColor );
		}

		InvalidateLayoutAndVolatility();
	}

	UFUNCTION()
	void OnTrinketUsedFlash( FPlayerEquipmentData trinketData )
	{
		equipmentTrinket.PlayOnUsedFlash( trinketData );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnPlayerConnectionStateChanged( AAS_PlayerEntity inPlayer, bool isConnected )
	{
		if ( !ValidatePlayer( inPlayer ) )
			return;

		SetWidgetVisibilitySafe( disconnectedIndicator, isConnected ? ESlateVisibility::Collapsed : ESlateVisibility::HitTestInvisible );

		if ( !isConnected )
		{
			ChangePlayerState( DISCONNECTED_PLAYER_STATE );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnRaidUltAvailableChanged( AAS_PlayerEntity inPlayer, bool isRaidUltReady )
	{
		if ( !ValidatePlayer( inPlayer ) )
			return;

		SetUniqueScalarParameter( portraitMaterial, n"UltimateReady", isRaidUltReady ? 1.0f : 0.0f );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnHardenAdded( ANCPlayerCharacter inPlayer )
	{
		if ( !ValidatePlayer( inPlayer ) )
			return;

		SetWidgetVisibilitySafe( hardenBar, ESlateVisibility::HitTestInvisible );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnHardenRemoved( ANCPlayerCharacter inPlayer )
	{
		if ( !ValidatePlayer( inPlayer ) )
			return;

		SetWidgetVisibilitySafe( hardenBar, ESlateVisibility::Collapsed );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnHardenedHealthChanged( AAS_PlayerEntity inPlayer, float old, float new )
	{
		if ( !ValidatePlayer( inPlayer ) )
			return;

		float healthFrac = new / HardenConst::HARDEN_MAX_HEALTH;
		SetUniqueScalarParameter( hardenBarMaterial, HEALTH_PARAMETER_NAME, healthFrac );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnBackpackContentsChanged( AAS_PlayerEntity inPlayer )
	{
		if ( !ValidatePlayer( inPlayer ) )
			return;

		bool shieldIsValid = player.IsActorShieldValid();
		SetWidgetVisibilitySafe( playerShield, player.IsActorShieldValid() ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );

		if ( shieldIsValid )
		{
			float32 maxShield	  = player.GetMaxShieldHealth();
			int shieldNumSegments = Math::FloorToInt( maxShield / PlayerWidgetConst::HEALTH_PER_SEGMENT );

			if ( lastShieldSegmentCount != shieldNumSegments )
			{
				// TODO @jmccarty: Make this sizing dynamic instead of hardcoding 58.75f
				FSlateBrush brush = GetNewBrushThatIsCopyOf( playerShield.Brush );
				brush.ImageSize	  = FVector2D( ( maxBarWidth / 4 ) * shieldNumSegments, brush.ImageSize.ToVector2D().Y ).ToFDeprecateSlateVector2D();
				playerShield.SetBrush( brush );
				UpdateShieldMaterialParameter( n"SegmentCount", shieldNumSegments );
				lastShieldSegmentCount = shieldNumSegments;
			}

			if ( IsValid( shieldMaterial ) )
			{
				FLinearColor shieldColor = player.GetEquippedShieldData().GetShieldColor();
				SetUniqueVectorParameter( shieldMaterial, n"BorderColor", shieldColor );
				SetUniqueVectorParameter( shieldMaterial, n"FillColor", shieldColor );
			}
		}

		if ( player == Client_GetLocalASPawn() || GameModeDefaults().GamemodeRules_ShowCoinsOnAll )
		{
			FGameplayTag coinTag = GameModeDefaults().UI_TrackedResource;
			int currentCoinCount = player.CountItemsInAllBackpacks( coinTag );
			coinCount.SetText( FText::AsNumber( currentCoinCount, FNumberFormattingOptions() ) );
			InvalidateLayoutAndVolatility();
		}
	}

	UFUNCTION()
	private void OnNetworkedShieldHealthChanged( float32 oldHealth, float32 newHealth )
	{
		OnBackpackContentsChanged( player );
	}

	UFUNCTION()
	private void OnRespawnTimeRemainingChanged( int respawnTimeRemainingMs,
										float respawnTimeRemainingPercent )
	{
		if ( respawnTimeRemainingMs <= 0 )
		{
			SetWidgetVisibilitySafe( respawnTimer, ESlateVisibility::Collapsed );
			return;
		}

		SetWidgetVisibilitySafe( respawnTimer, ESlateVisibility::HitTestInvisible );

		FNumberFormattingOptions options = GetNumberFormattingOptions();
		if ( respawnTimeRemainingMs < TO_MILLISECONDS( 5 ) )
		{
			options.SetMinMaxFractionalDigits( 1 );
		}
		else
		{
			options.SetMinMaxFractionalDigits( 0 );
		}

		respawnTimer.SetText( FText::AsNumber( TO_SECONDS( respawnTimeRemainingMs ), options ) );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnHealStarted( FName signalName, UObject sender )
	{
		const UAS_HealFromItemComponent healComponent = Cast<UAS_HealFromItemComponent>( sender );
		if ( IsValid( healComponent ) && player == healComponent.ownerPlayer )
		{
			ANCWeapon activeHealWeapon = healComponent.GetActiveHealWeapon();
			if ( IsValid( activeHealWeapon ) )
			{
				TOptional<FHealItemStruct> healItem = healComponent.GetSelectedHealItem();
				if ( healItem.IsSet() )
				{
					float32 maxShield  = player.GetMaxShieldHealth();
					float32 healAmount = player.GetShieldHealth() + healItem.Value.healAmount;
					float32 healFrac   = Math::Clamp( maxShield > 0.0f ? healAmount / maxShield : 0.0f, 0.0f, 1.0f );
					UpdateShieldMaterialParameter( DAMAGE_PARAMETER_NAME, 0.0f );
					UpdateShieldMaterialParameter( REGENERATE_PARAMETER_NAME, healFrac );
				}
			}
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnHealCancelled( FName signalName, UObject sender )
	{
		const UAS_HealFromItemComponent healComponent = Cast<UAS_HealFromItemComponent>( sender );
		if ( IsValid( healComponent ) && player == healComponent.ownerPlayer )
		{
			// If a heal is cancelled, just 0 the param out, this can be triggered by the player or at the end of a full heal
			UpdateShieldMaterialParameter( REGENERATE_PARAMETER_NAME, 0.0f );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnHealCompleted( FName signalName, UObject sender )
	{
		const UAS_HealFromItemComponent healComponent = Cast<UAS_HealFromItemComponent>( sender );
		if ( IsValid( healComponent ) && player == healComponent.ownerPlayer )
		{
			// If a heal is completed, trigger the next heal early for sequential heals
			OnHealStarted( signalName, sender );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnPlayerTeleported( AAS_PlayerEntity inPlayer )
	{
		if ( !ValidatePlayer( inPlayer ) )
			return;

		// TODO @jmccarty: Hook up teleporting for teammates
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnTurnOrderReplicated( int oldValue, int newValue )
	{
		FLinearColor color = GetUIColorForPlayer( player );
		playerIndicator.SetColorAndOpacity( color );
	}

	private void UpdatePlayerHealth( FHealthChangedInfo healthInfo )
	{
		if ( !IsValid( player ) )
			return;

		UpdateHealthValue(
			healthInfo.newHealth,
			player.GetMaxHealth(),
			lastHealthFrac,
			startingIncomingHealthDamage,
			currentIncomingHealthDamage,
			n"IncomingHealthDamageThread",
			healthMaterial );
	}

	private void UpdateShieldHealth( FHealthChangedInfo healthInfo )
	{
		if ( !IsValid( player ) || !player.IsActorShieldValid() )
			return;

		UpdateHealthValue(
			healthInfo.newShieldHealth,
			player.GetMaxShieldHealth(),
			lastShieldFrac,
			startingIncomingShieldDamage,
			currentIncomingShieldDamage,
			n"IncomingShieldDamageThread",
			shieldMaterial );
	}

	/*
		Health and shields are treated the same but with different values and different objects, this removes the copy pasta of the
		value setting, damage thread, and material updating.
	*/
	private void UpdateHealthValue(
		float32 newHealth,
		float32 maxHealth,
		float& lastFraction,
		TOptional<float>& startingDamage,
		float& currentDamage,
		FName damageThread,
		UMaterialInstanceDynamic material )
	{
		float32 healthFrac = maxHealth > 0.0f ? newHealth / maxHealth : 0.0f;
		if ( lastFraction != healthFrac )
		{
			if ( lastFraction > healthFrac )
			{
				if ( !startingDamage.IsSet() )
				{
					// Set up the initial damage state
					startingDamage = lastFraction;
					currentDamage  = lastFraction;
					SetUniqueScalarParameter( material, REGENERATE_PARAMETER_NAME, 0.0f );
					SetUniqueScalarParameter( material, DAMAGE_PARAMETER_NAME, lastFraction );
				}

				Thread( this, damageThread, healthFrac );
			}

			lastFraction = healthFrac;
			SetUniqueScalarParameter( material, HEALTH_PARAMETER_NAME, healthFrac );
		}
	}

	UFUNCTION()
	private void IncomingHealthDamageThread( UNCCoroutine co, float target )
	{
		StartDamageThread( co, incomingHealthDamageEndSignal );
		TickDamage( co, target, healthMaterial, currentIncomingHealthDamage );
		EndDamageThread( co, target, healthMaterial, startingIncomingHealthDamage, lastHealthFrac );
	}

	UFUNCTION()
	private void IncomingShieldDamageThread( UNCCoroutine co, float target )
	{
		StartDamageThread( co, incomingShieldDamageEndSignal );
		TickDamage( co, target, shieldMaterial, currentIncomingShieldDamage );
		EndDamageThread( co, target, shieldMaterial, startingIncomingShieldDamage, lastShieldFrac );
	}

	private void StartDamageThread( UNCCoroutine co, FNCCoroutineSignal& endSignal )
	{
		// Init the thread
		endSignal.Emit();
		co.EndOn( this, endSignal );
		co.EndOnDestroyed( player );
		co.Wait( INITIAL_DELAY );
	}

	private void TickDamage( UNCCoroutine co, float target, UMaterialInstanceDynamic material, float& currentValue )
	{
		// Reduce the incoming damage bar overtime
		while ( currentValue > target )
		{
			// TODO @jmccarty: Move this to evaluate along a curve
			currentValue -= DELTA;
			SetUniqueScalarParameter( material, DAMAGE_PARAMETER_NAME, currentValue );
			co.Wait( Gameplay::GetWorldDeltaSeconds() / 10.0f );
		}
	}

	private void EndDamageThread( UNCCoroutine co, float target, UMaterialInstanceDynamic material, TOptional<float>& startingIncomingDamage, float& lastFraction )
	{
		// At the end, make sure the damage matches the target and free up the initial health value
		SetUniqueScalarParameter( material, DAMAGE_PARAMETER_NAME, target );
		startingIncomingDamage.Reset();
		lastFraction = target;
	}

	private void ChangePlayerState( int playerState )
	{
		if ( currentState != playerState )
		{
			currentState = playerState;
			ChangeMaterialState( portraitMaterial );
			ChangeMaterialState( healthMaterial );
			ChangeMaterialState( shieldMaterial );
		}
	}

	private void ChangeMaterialState( UMaterialInstanceDynamic material )
	{
		if ( IsValid( material ) )
		{
			material.SetScalarParameterValue( PLAYER_STATE_PARAMETER_NAME, float( currentState ) );
			InvalidateLayoutAndVolatility();
		}
	}

	private void UpdateHealthMaterialParameter( FName param, float value )
	{
		SetUniqueScalarParameter( healthMaterial, param, value );
	}

	private void UpdateShieldMaterialParameter( FName param, float value )
	{
		SetUniqueScalarParameter( shieldMaterial, param, value );
	}

	private bool ValidatePlayer( AAS_PlayerEntity inPlayer )
	{
		return IsValid( inPlayer ) && IsValid( player ) && player == inPlayer;
	}

	private bool ValidatePlayer( const ANCPlayerCharacter inPlayer )
	{
		return IsValid( inPlayer ) && IsValid( player ) && player == inPlayer;
	}

	private void UpdateShieldBreakerDisplay()
	{
		if ( !IsValid( shieldBreakerMaterial ) && IsValid( player ) )
		{
			shieldBreakerMaterial = CreateDynamicMaterialFromImageBrush( shieldBreakerIndicator );
			if ( IsValid( shieldBreakerMaterial ) )
			{
				// We need to make a material instance that matches our player when we have one
				shieldBreakerMaterial.SetVectorParameterValue( MaterialParameter::FILL_COLOR, GetUIColorForPlayer( player ) );
				shieldBreakerMaterial.SetScalarParameterValue( MaterialParameter::SHOW_SCALAR, MaterialParameter::GetTrueFalseFloat( true ) );
				shieldBreakerIndicator.SetBrushFromMaterial( shieldBreakerMaterial );
			}
		}

		bool playerHasShieldBreaker = PlayerHasShieldBreaker( player );
		SetWidgetVisibilitySafe( shieldBreakerIndicator, playerHasShieldBreaker ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
		InvalidateLayoutAndVolatility();
	}
}