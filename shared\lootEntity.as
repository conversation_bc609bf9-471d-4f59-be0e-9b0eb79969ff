event void LootPickedUpEvent( AAS_LootEntity loot, ANCPlayerCharacter user );
event void LootAutoKilledEvent( AAS_LootEntity loot );
event void FOnLootPickedUpGlobalCallback( AAS_LootEntity loot, ANCPlayerCharacter user, int count );

UCLASS()
class AAS_LootEntity : ANCDefaultActor
{
	UPROPERTY( DefaultComponent, RootComponent )
	UStaticMeshComponent MeshComponent;
	default MeshComponent.bCanEverAffectNavigation = false;
	default MeshComponent.CollisionProfileName	   = n"PhysicsActor";

	UPROPERTY( DefaultComponent )
	UNCNetMovementComponent ncNetMovementComponent;

	UPROPERTY( EditAnywhere, BlueprintHidden )
	FGameplayTag lootTag;

	UPROPERTY( DefaultComponent, Attach = MeshComponent )
	UUsableItemComponent useComponent;
	default useComponent.StartEnabled		  = false;
	default useComponent.HasExtendedUseAction = true;
	default useComponent.SphereRadius		  = 16;
	default useComponent.LookCollisionMethod  = EUseLookCollisionMethod::VerticalLineCollision;
	default useComponent.UsePromptPosition	  = EUsePromptPosition::LookCollisionPoint;
	default useComponent.UsePromptAlignment	  = EUsePromptAlignment::TopLeft;
	float32 useDuration						  = 0.0;

	UPROPERTY( DefaultComponent )
	UAS_SimpleClientHighlightComponent cl_highlightComponent;
	default cl_highlightComponent.highlightValue = EHighlightStencilValue::HIGHLIGHT_LOOT_STENCIL_VALUE;

	UPROPERTY( DefaultComponent )
	UNCLootMovementComponent LootMovementComponent;
	default LootMovementComponent.SetComponentTickEnabled( false );

	UPROPERTY( DefaultComponent )
	UAS_PingableComponent pingableComponent;
	default pingableComponent.pingType = EPlayerPingType::LOOT;

	UPROPERTY( DefaultComponent )
	private UAS_MapMarkerComponent mapMarkerComponent;

	UPROPERTY()
	private FNCNetInt lootDataIndex( -1, -1, 10000 );

	UPROPERTY()
	private FNCNetInt lootCount( 0, 0, 1000 );

	UPROPERTY()
	private FNCNetInt customInt( 0, 0, 256 );

	UPROPERTY()
	private FNCNetInt customInt2( 0, -1, MAX_int32 );

	UPROPERTY( EditAnywhere, BlueprintHidden )
	int count = -1;

	UPROPERTY( EditAnywhere, BlueprintHidden )
	int condition = -1;

	// Used to preserve weapon mods when dropping / picking up weapons
	UPROPERTY( EditAnywhere, BlueprintHidden )
	int bitfield = -1;

	UPROPERTY()
	FNCNetBool net_hasGravity( true );

	UPROPERTY( EditDefaultsOnly )
	UNiagaraSystem beamFX;

	UNiagaraComponent beam;

	FTimerHandle autoKillHandle;

	LootPickedUpEvent onPickedup;

	ELootPickUpContext pickUpContext;
	default pickUpContext = ELootPickUpContext::GROUND_LOOT;

	float nextAllowedUseTime = 0.0;

	FOnLootPickedUpGlobalCallback server_OnLootPickedUp;

	FRotator originalRotation;
	FRotator rotationOffset;

	FNCCoroutineSignal endPlaySignal;

	UFUNCTION( BlueprintOverride )
	void EndPlay( EEndPlayReason EndPlayReason )
	{
		endPlaySignal.Emit();

		if ( ItemIndexIsShieldBreaker( lootTag ) )
		{
			if ( IsValid( Loot() ) )
				Loot().shieldBreakers.Remove( this );
		}
	}

	UFUNCTION( BlueprintOverride )
	void BeginPlay()
	{
		MeshComponent.CollisionEnabled = ECollisionEnabled::PhysicsOnly;
		System::SetTimer( this, n"RestoreCollision", 0.75, false );
	}

	UFUNCTION( NotBlueprintCallable )
	private void RestoreCollision()
	{
		MeshComponent.CollisionEnabled = ECollisionEnabled::QueryAndPhysics;
	}

	UFUNCTION( BlueprintOverride )
	void ServerBeginPlay()
	{
		SharedBeginPlay();

		System::SetTimer( this, n"CheckForUnderWorld", 1.0, true, 0.5 );

		ReenableUsable( Math::RandRange( 0.3, 0.6 ) );

		LootMovementComponent.SetUpdatedComponent( GetRootComponent() );
		LootMovementComponent.OnLootStopMove.AddUFunction( this, n"OnLootStopMove" );
	}

	UFUNCTION( BlueprintOverride )
	void ClientBeginPlay()
	{
		SharedBeginPlay();

		useComponent.GetUseHintDelegate.BindUFunction( this, n"GetUseHint" );
		useComponent.GetExtendedUseHintDelegate.BindUFunction( this, n"GetUseHint" );
		useComponent.GetAltUseHintDelegate.BindUFunction( this, n"GetAltUseHint" );
		useComponent.OnEnabledChanged.AddUFunction( this, n"OnUseChanged" );

		lootDataIndex.OnReplicated().AddUFunction( this, n"Client_OnLootDataIndexChanged" );
		net_hasGravity.OnReplicated().AddUFunction( this, n"CL_OnHasGravityChanged" );
		Client_OnLootDataIndexChanged( -1, lootDataIndex );

		cl_highlightComponent.OnHighlightSet.AddUFunction( this, n"cl_OnHighlightChanged" );
		cl_highlightComponent.SetIsHighlighted(cl_highlightComponent.GetIsHighlighted());
	}

	UFUNCTION()
	private void cl_OnHighlightChanged(AActor actor, bool isHighlighted)
	{
		SetBaseBeamColor();
		if ( isHighlighted )
		{
			FLinearColor highlightColor = GetLootRarityColor();
			Material::SetVectorParameterValue( GetGlobalParameters().hightlightParameters, n"loot_highlight_color", highlightColor * 7 );
		}
	}

	UPROPERTY( EditDefaultsOnly )
	bool dynamicallyPositionUseComponent = true;

	void OnStaticMeshChanged()
	{
		if ( dynamicallyPositionUseComponent )
		{
			// set use component to center of mesh
			FBoxSphereBounds bounds = MeshComponent.GetBounds();
			useComponent.SetWorldLocation( bounds.Origin );
		}
	}	

	void SharedBeginPlay()
	{
		originalRotation = GetActorRotation();

		SetPropertiesFromLootTag();

		useComponent.OnUsed.AddUFunction( this, n"OnUsed" );
		useComponent.OnUsed_Extended.AddUFunction( this, n"OnUsed" );
		useComponent.OnAltUsed.AddUFunction( this, n"OnAltUsed" );
		useComponent.GetUseDurationDelegate.BindUFunction( this, n"GetUseDuration" );
		useComponent.GetUsabilityStateDelegate.BindUFunction( this, n"GetUsabilityState" );

		MeshComponent.SetCollisionResponseToChannel( ECollisionChannel::WeaponMelee, ECollisionResponse::ECR_Ignore );
	}

	UFUNCTION( BlueprintEvent )
	protected FInputUsabilityStates GetUsabilityState( const UUsableItemComponent Component, const ANCPlayerCharacter PlayerUser )
	{
		FInputUsabilityStates usabilityStates;

		// Should be a cached value on the team persistence. Needs to be cached on server and client.
		if ( GetTimeMilliseconds() > nextAllowedUseTime )
		{
			if ( GetUseDuration( Component, PlayerUser, true ) > 0 )
			{
				usabilityStates.NormalExtendedInput = EUsabilityState::USABLE;
			}
			else
			{
				usabilityStates.NormalInstantInput = EUsabilityState::USABLE;
			}
			usabilityStates.AlternateInput = EUsabilityState::USABLE;
			return usabilityStates;
		}
		return usabilityStates;
	}

	UFUNCTION( NotBlueprintCallable )
	void OnAltUsed( UUsableItemComponent component, ANCPlayerCharacter user )
	{
		if ( IsValid( beam ) )
		{
			beam.AttachTo( beam.GetOwner().RootComponent );
		}
		_OnUsed( component, user, true );
	}

	UFUNCTION( NotBlueprintCallable )
	void OnUsed( UUsableItemComponent component, ANCPlayerCharacter user )
	{
		_OnUsed( component, user, false );
	}

	protected void _OnUsed( UUsableItemComponent component, ANCPlayerCharacter user, bool isAltUse )
	{
		const int initialCount = GetCount();

		FLootDataStruct lootData = GetLootData();
		bool isUseValild		 = IsLootActionValid( component, user, lootData, initialCount, isAltUse );

		if ( !isUseValild )
			return;

		int newCount = PerformLootPickupAction( user, MakeBackpackItem( lootData.index, initialCount, GetCondition(), GetBitField() ), isAltUse, pickUpContext );

		if ( IsServer() )
		{
			NotifyPlayerGather( user, initialCount - newCount );

			SetCount( newCount );
			if ( GetCount() <= 0 )
			{
				onPickedup.Broadcast( this, user );
				OnLootPickedup( user );
				server_OnLootPickedUp.Broadcast( this, user, newCount );

				if ( lootData.lootType == ELootType::ShieldBreacher )
				{
					Loot().shieldBreakers.Remove( this );
				}

				Destroy();
			}
		}
		else
		{
			if ( newCount > 0 )
			{
				AAS_HUD hud = GetLocalHUD();
				hud.OnBackpackFull( GetLootData().index );
			}
			else
			{
				nextAllowedUseTime = GetTimeMilliseconds() + TO_MILLISECONDS( 1.0 );
			}
		}
	}

	void OnLootPickedup( ANCPlayerCharacter user )
	{
	}

	protected void NotifyPlayerGather( ANCPlayerCharacter user, int pickupAmount )
	{
		FLootDataStruct lootData = GetLootData();

		FString command;
		switch( lootData.lootType )
		{
			case ELootType::PrimaryWeapon:
				command = f"ServerCommand_NotifyGatherResource {lootData.intIndex} {1} {pickupAmount}";
			break;

			case ELootType::Equipment:
				command = f"ServerCommand_NotifyGatherResource {lootData.intIndex} {1} {1}";
			break;

			default:
				command = f"ServerCommand_NotifyGatherResource {lootData.intIndex} {pickupAmount} {CountItemsInBackpackAndValidBoxes(user, lootData.index, user, CraftingSource::BACKPACK)}";
			break;
		}

		UNCRemoteScriptCommands::SendServerCommand( user, command );
	}

	UFUNCTION()
	void SetPropertiesFromLootTag( UDataTable customDT = nullptr )
	{
		TArray<FLootDataStruct> Rows;

		UDataTable DT = customDT;
		if ( customDT != nullptr )
		{
			DT.GetAllRows( Rows );
			for ( auto R : Rows )
			{
				if ( R.index == lootTag )
				{
					MeshComponent.SetStaticMesh( R.mesh );
					if ( !R.onlyUseOffsetWhenPlaced )
						TrySetMeshRotationOffset( R.meshRotationOffset );
					OnStaticMeshChanged();
				}
			}

			return;
		}

		if ( !IsLootIndexValid( lootTag ) )
			return;

		const FLootDataStruct& data = GetLootDataByIndex( lootTag );
		MeshComponent.SetStaticMesh( data.mesh );
		if ( !data.onlyUseOffsetWhenPlaced )
			TrySetMeshRotationOffset( data.meshRotationOffset );
		OnStaticMeshChanged();

		UpdatePingableObjectInfo();

		if ( data.lootType == ELootType::PrimaryWeapon )
			pingableComponent.pingType = EPlayerPingType::LOOT_WEAPON;

		if ( IsServer() )
		{
			lootDataIndex.SetNetValue( data.intIndex );
			int DesiredCount = GetDesiredLootCount( data, count );
			lootCount.SetNetValue( DesiredCount );
			SetCondition( condition );
			SetBitfield( bitfield );
		}

		if ( ItemIndexIsShieldBreaker( lootTag ) )
			Loot().shieldBreakers.Add( this );

		SetBaseBeamColor();

		if ( IsClient() )
		{
			mapMarkerComponent.UnRegisterMapMarker( EMapMarkerVisibility::CUSTOM, MapMarker() );
			mapMarkerComponent.ClearCustomMapMarkerVisibilityCallback();

			mapMarkerComponent.mapMarkerVisibilityOptions.Custom = data.mapMarkerId;
			mapMarkerComponent.SetCustomMapMarkerVisibilityCallback( this, n"OnMapMarkerVisibilityRefresh" );
			mapMarkerComponent.RefreshMapMarkerComponent();
		}
	}

	void TrySetMeshRotationOffset( FRotator offset )
	{
		if ( offset != FRotator::ZeroRotator || rotationOffset != offset )
		{
			rotationOffset = offset;
			SetActorRotation( originalRotation + offset );
			// Print( f"Rotating! Old {originalRotation} Offset {offset} new {GetActorRotation()}" );
		}
	}

	void SetBaseBeamColor()
	{
		if ( IsValid( beam ) )
		{
			FLinearColor rarityColor = GetLootRarityColor();
			// Boost the color so it pops more
			rarityColor *= 3;
			beam.SetNiagaraVariableLinearColor( "Color", rarityColor );
		}
	}

	access Ping = private, AAS_PingObject;
	access:Ping void HideBeam()
	{
		ScriptAssert(IsClient(), "Client ONLY" );
		
		if ( IsValid(beam) && beam.IsVisible() )
			beam.SetVisibility(false);
	}

	access:Ping void ShowBeam()
	{
		ScriptAssert(IsClient(), "Client ONLY" );
		
		if ( IsValid(beam) && !beam.IsVisible() )
			beam.SetVisibility(true);
	}

	FLinearColor GetLootRarityColor()
	{
		FLinearColor result = FLinearColor::White;

		if ( IsLootIndexValid( lootTag ) )
		{
			const FLootDataStruct& data = GetLootDataByIndex( lootTag );
			result						= GetRarityColor( data.rarity );
		}

		return result;
	}

	UFUNCTION()
	void OnSleep()
	{
		FVector Loc = GetUseComponent().GetWorldLocation();
		TArray<AActor> IgnoreActors;
		FHitResult OutHit = LineTraceSingle( Loc + FVector( 0, 0, 50 ), Loc - FVector( 0, 0, 10 ), ETraceTypeQuery::Visibility, true, IgnoreActors, true );
		if ( OutHit.bBlockingHit )
		{
			FVector location = OutHit.Location + FVector( 0, 0, 5 );
			GetUseComponent().SetWorldLocation( location );
			Server_EmitLootDropImpactSound( OutHit.GetPhysMaterial().SurfaceType, location );
		}
		else
		{
			FVector location = Loc + FVector( 0, 0, 10 );
			GetUseComponent().SetWorldLocation( location );

			// No actor hit on trace, use default surface type
			Server_EmitLootDropImpactSound( EPhysicalSurface::SurfaceType_Default, location );
		}
	}

	UFUNCTION()
	void Server_EmitLootDropImpactSound( EPhysicalSurface surfaceType, FVector location )
	{
		if ( !IsLootIndexValid( lootTag ) )
			return;

		const FLootDataStruct& data = GetLootDataByIndex( lootTag );
		UNCAudioAsset impactSound;

		// If material surface type is not found, look for default type
		if ( !data.impactSoundTable.Find( surfaceType, impactSound ) && surfaceType != EPhysicalSurface::SurfaceType_Default )
			data.impactSoundTable.Find( EPhysicalSurface::SurfaceType_Default, impactSound );

		if ( IsValid( impactSound ) )
			Server_EmitSoundAtLocation( impactSound, location );
	}

	UFUNCTION()
	void SetCondition( int num )
	{
		customInt.SetNetValue( num == -1 ? GameConst::LOOT_CONDITION_NETVAL_NO_CONDITION : num );
	}

	UFUNCTION()
	int GetCondition()
	{
		return customInt == GameConst::LOOT_CONDITION_NETVAL_NO_CONDITION ? -1 : customInt;
	}

	UFUNCTION()
	void SetCount( int num )
	{
		lootCount.SetNetValue( num );
	}

	UFUNCTION()
	int GetCount()
	{
		return lootCount;
	}

	UFUNCTION()
	void SetLootTag( FGameplayTag NewLootTag )
	{
		const FLootDataStruct& Data = GetLootDataByIndex( NewLootTag );
		lootDataIndex.SetNetValue( Data.intIndex );
		lootTag = NewLootTag;
		SetPropertiesFromLootTag();
	}

	FLootDataStruct GetLootData()
	{
		return GetLootDataByIntIndex( lootDataIndex );
	}

	bool HasValidLootIndex()
	{
		return IsLootIntIndexValid( lootDataIndex );
	}

	UFUNCTION()
	void SetBitfield( int inBitfield )
	{
		customInt2.SetNetValue( inBitfield );
	}

	UFUNCTION()
	int GetBitField()
	{
		return customInt2;
	}

	UFUNCTION( BlueprintEvent )
	float32 GetUseDuration( const UUsableItemComponent Component, const ANCPlayerCharacter PlayerUser, bool isNormalUseInput )
	{
		return GetUseDurationForLoot( PlayerUser, GetLootData(), isNormalUseInput, useDuration );
	}

	UFUNCTION( NotBlueprintCallable )
	protected FText GetAltUseHint( const UUsableItemComponent Component, const ANCPlayerCharacter PlayerUser, const EUsabilityState UsabilityState )
	{
		FLootDataStruct data	= GetLootData();
		AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( PlayerUser );
		return GetAltUseHintForLoot( player, data.lootType, ELootTooltipContext::GROUND_LOOT );
	}

	UFUNCTION()
	private void OnUseChanged( UUsableItemComponent component, bool isEnabled )
	{
		if ( component.IsEnabled() )
		{
			if ( !IsValid( beam ) && IsValid( beamFX ) )
			{
				beam = Client_SpawnEffectOnEntity_OneShot( beamFX, this );
				if ( IsValid( beam ) )
				{
					beam.SetNiagaraVariableVec2( "Sprite Size", FVector2D( 10, 100 ) );
				}

				SetBaseBeamColor();
			}
		}
	}

	UFUNCTION( NotBlueprintCallable )
	FText GetUseHint( const UUsableItemComponent Component, const ANCPlayerCharacter PlayerUser, const EUsabilityState UsabilityState )
	{
		FLootDataStruct data	= GetLootData();
		AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( PlayerUser );
		return GetUseHintForLoot( player, data.lootType, ELootTooltipContext::GROUND_LOOT );
	}

	UFUNCTION( NotBlueprintCallable )
	void Client_OnLootDataIndexChanged( int OldValue, int NewValue )
	{
		if ( !IsLootIntIndexValid( NewValue ) )
			return;

		FLootDataStruct Data = GetLootDataByIntIndex( NewValue );
		lootTag				 = Data.index;
		SetPropertiesFromLootTag();
	}

	UFUNCTION( NotBlueprintCallable )
	void CheckForUnderWorld()
	{
		if ( GetActorLocation().Z < -100000 )
		{
			Destroy();
		}
	}

	UFUNCTION()
	private void OnLootStopMove()
	{
		OnSleep();
	}

	UFUNCTION( NotBlueprintCallable )
	void AutoKillMyself()
	{
		Destroy();
	}

	UUsableItemComponent GetUseComponent() const
	{
		return useComponent;
	}

	void AutoKill( float seconds )
	{
		System::ClearAndInvalidateTimerHandle( autoKillHandle );

		if ( seconds <= 0 )
			return;

		autoKillHandle = System::SetTimer( this, n"AutoKillMyself", seconds, false, 0.5 );
	}

	void ReenableUsable( float delay )
	{
		System::SetTimer( this, n"_ReenableUsable", delay, false, 0.0 );
	}

	UFUNCTION()
	private void _ReenableUsable()
	{
		useComponent.EnableUsable();
	}

	void ClearReenableTimer()
	{
		if ( System::TimerExists( this, "_ReenableUsable" ) )
			System::ClearTimer( this, "_ReenableUsable" );
	}

	void DelayedEnable( float delay )
	{
		ClearReenableTimer();
		ReenableUsable( delay );
	}

	UFUNCTION( NotBlueprintCallable )
	protected bool OnMapMarkerVisibilityRefresh( UAS_MapMarkerComponent mapMarker )
	{
		return false;
	}

	UFUNCTION()
	void DisableGravity()
	{
		LootMovementComponent.GravityScale = 0;
		LootMovementComponent.SetComponentTickEnabled( false );
		net_hasGravity.SetNetValue( false );
	}

	UFUNCTION()
	protected void CL_OnHasGravityChanged( bool oldVal, bool newVal )
	{
		LootMovementComponent.GravityScale = 0;
		LootMovementComponent.SetComponentTickEnabled( false );
	}

	void UpdatePingableObjectInfo()
	{
		if ( HasValidLootIndex() )
		{
			FLootDataStruct lootData	   = GetLootData();
			FPingableObjectInfo info	   = GetPingableObjectInfoFromLootData( EPlayerPingType::LOOT, lootData );
			pingableComponent.pingableInfo = info;
		}
		else
		{
			pingableComponent.pingableInfo = FPingableObjectInfo();
		}
	}
}

FPingableObjectInfo GetPingableObjectInfoFromLootData( EPlayerPingType pingType, const FLootDataStruct& lootData )
{
	FPingableObjectInfo info;
	if ( IsClient() )
	{
		info._fillAlpha		= Ping::GetFillAlphaForPing( lootData.rarity );
		info._frameColor 	= GetRarityColor( lootData.rarity );
		info.icon			= lootData.icon;
		info.name			= lootData.name;
		info.voLine			= DialogueUtil::GetDialogueIndexForLootPing( pingType, lootData.intIndex );
	}
	return info;
}