AAS_SoulShieldManager GetSoulShieldManagerForPlayer( AAS_PlayerEntity player )
{
	UAS_SharedScriptList list = SharedScriptList();
	if ( IsValid( list ) && list.playerSoulShieldManagers.Contains( player ) )
		return list.playerSoulShieldManagers[player];
	return nullptr;
}






UCLASS( Abstract )
class AAS_SoulShieldManager : ANCDefaultActor
{
    UPROPERTY( EditDefaultsOnly )
    TSubclassOf<AAS_SoulShield> shieldClass;

    UPROPERTY( EditDefaultsOnly )
	FTPPCameraSettings soulShieldCameraSettings;
	default soulShieldCameraSettings.Set(ETPPCameraSetting::FOV, 90);
	default soulShieldCameraSettings.Set(ETPPCameraSetting::TargetArmLength, 250);
	default soulShieldCameraSettings.Set(ETPPCameraSetting::CameraOffsetVector, FVector(0.f, 25.0f, 10.f));

    UPROPERTY( Transient )
	FNCNetBool net_IsOnCooldown( false );

    UPROPERTY( Transient )
	FNCNetBool net_IsShieldDeployed( false );

    UPROPERTY( Transient )
    FNCNetEntityHandle net_myShield;

    UPROPERTY( Transient )
	FNCNetFloat net_shieldHealth;

    FTimerHandle shieldCooldownTimer;
    AAS_SoulShield myShield;
    AAS_PlayerEntity asOwner;
    ANCWeapon myWeapon;

    bool isLunging;



    UFUNCTION(BlueprintOverride)
    void ClientBeginPlay()
    {
        SharedBeginPlay();

        net_IsShieldDeployed.OnReplicated().AddUFunction( this, n"OnIsShieldDeployedChanged" );
        OnIsShieldDeployedChanged( false, net_IsShieldDeployed );

        System::SetTimer( this, n"Delayed_SetupClientPresentation", 0.25f, false );

        if ( asOwner.IsLocallyControlled() )
            SetupHUD();
    }

    UFUNCTION(BlueprintOverride)
    void ServerBeginPlay()
    {
        net_shieldHealth.SetNetValue( SoulShieldConst::SHIELD_HEALTH );
        SharedBeginPlay();
    }

    UFUNCTION()
	void SharedBeginPlay()
	{
		asOwner = Cast<AAS_PlayerEntity>( GetOwnerPlayer() );
        myWeapon = asOwner.GetWeaponAtSlot( WeaponSlot::TacticalSlot );

		UAS_SharedScriptList list = SharedScriptList();
		list.playerSoulShieldManagers.Add( asOwner, this );
	}

    UFUNCTION(BlueprintOverride)
    void EndPlay(EEndPlayReason EndPlayReason)
    {
        UAS_SharedScriptList list = SharedScriptList();
		if ( IsValid( list ) && list.playerSoulShieldManagers.Contains( asOwner ) )
			list.playerSoulShieldManagers.Remove( asOwner );

        if ( IsValid( widget ) )
            widget.RemoveFromParent();
    }





    //Mark: this is hacky but for some reason on the first frame the character 3p mesh is redmane for first spawn even though the passive is assigned correctly

    UFUNCTION()
    void Delayed_SetupClientPresentation()
    {
        UNCSkeletalMeshComponent playerMesh = asOwner.GetPlayerMesh3P();
        SetupClientPresentation( playerMesh );
    }

    UFUNCTION()
    void SetupClientPresentation( UNCSkeletalMeshComponent playerMesh )
    {
        bool isFriendly = UNCUtils::GetRelationshipBetweenTeams( asOwner.GetTeam(), Client_GetLocalASPawn().GetTeam() ) == EAffinity::Friendly;

        FLinearColor outSafeColor = FLinearColor::White;
        GetSafeColor( isFriendly ? n"friendly_highlight" : n"enemy_highlight", outSafeColor );
        
        TArray<UMaterialInterface> m = playerMesh.GetMaterials();
		for ( int i = 0; i < m.Num(); i++ )
		{
			UMaterialInstanceDynamic dynMat = playerMesh.CreateDynamicMaterialInstance( i );
            if ( IsValid( dynMat ) )
            {
			    dynMat.SetVectorParameterValue( n"emissiveTint_PX", outSafeColor );
            }
		}
    }

    UPROPERTY( EditDefaultsOnly )
	TSubclassOf<UAS_SoulShieldWidget> widgetClass;
	UAS_SoulShieldWidget widget;

	UFUNCTION()
	void SetupHUD()
	{
		widget = Cast<UAS_SoulShieldWidget>( WidgetBlueprint::CreateWidget( widgetClass, GetLocalHUD().GetOwningPlayerController() ) );
        widget.Init( this );

		GetLocalHUD().mainHUDWidget.alivePanel.AddChild( widget );

		FAnchors anchors;
		anchors.Minimum = FVector2D( 0.5, 0.5 );
		anchors.Maximum = FVector2D( 0.5, 0.5 );

		UCanvasPanelSlot slot = Cast<UCanvasPanelSlot>( widget.Slot );
		slot.SetAlignment( FVector2D( 0.5, 0.5 ) );
		slot.SetAnchors( anchors );

		// chargeWidget.AddToViewport( ZORDER_HUD );
		// chargeWidget.SetPadding( FMargin( 0, 0, 150, 0 ) );
		/*	HACK -> I can't figure out why, but this widget is being scaled down by default. Need to scale it up to get it to
			display at the right size. I can verify this by taking a screenshot and comparing the size of the keybind widget - mo	*/
		widget.SetRenderScale( FVector2D( 1.175, 1.175 ) );
	}


    UAS_ShieldThread shieldThread;
    FNCCoroutineSignal holsterShield;

    UFUNCTION()
    void CreateShield( bool shouldDrainShieldHealth )
    {
        if ( net_IsOnCooldown )
            return;

        if ( IsValid( shieldThread ) && shieldThread.IsRunning() )
            TeardownShield();

        shieldThread = Cast<UAS_ShieldThread>( CreateThread( UAS_ShieldThread::StaticClass(), this ) );
        shieldThread.Init( asOwner, this );

        if ( shouldDrainShieldHealth )
            Thread( this, n"Thread_DrainShieldHealth" );
    }

    UFUNCTION()
    void TeardownShield()
    {
        holsterShield.Emit();

        Thread( this, n"Thread_RegenShieldHealth" );
    }






    UFUNCTION()
    void UpdateSavedHealth( float32 health )
    {
        net_shieldHealth.SetNetValue( health );
    }

    FNCCoroutineSignal terminateShieldHealthDrainSignal;

    UFUNCTION()
    void Thread_DrainShieldHealth( UNCCoroutine co )
    {
        terminateShieldHealthRegenSignal.Emit();
        terminateShieldHealthDrainSignal.Emit();
        co.EndOn( this, terminateShieldHealthDrainSignal );

        float32 tickTime = 0.05;
        while ( true )
        {
            if ( net_shieldHealth <= 0 )
                break;

            float32 drainPerTick = tickTime * SoulShieldConst::SHIELD_HEALTH_DRAIN_RATE;

            float32 newhealth = net_shieldHealth - drainPerTick;
            if ( IsValid( myShield ) )
                myShield.healthComponent.SetHealth( newhealth );
            UpdateSavedHealth( newhealth );

            co.Wait( tickTime );
        }

        if ( IsValid( myShield ) )
            myShield.OnShieldDestroyed.Broadcast();
    }


    FNCCoroutineSignal terminateShieldHealthRegenSignal;

    UFUNCTION()
    void Thread_RegenShieldHealth( UNCCoroutine co )
    {
        terminateShieldHealthDrainSignal.Emit();
        terminateShieldHealthRegenSignal.Emit();
        co.EndOn( this, terminateShieldHealthRegenSignal );

        co.Wait( SoulShieldConst::SHIELD_HEALTH_REGEN_DELAY );

        float32 tickTime = 0.05;
        while ( true )
        {
            if ( net_shieldHealth >= SoulShieldConst::SHIELD_HEALTH )
                break;

            float32 regenPerTick = tickTime * SoulShieldConst::SHIELD_HEALTH_REGEN_RATE;

            float32 newhealth = net_shieldHealth + regenPerTick;
            if ( IsValid( myShield ) )
                myShield.healthComponent.SetHealth( newhealth );
            UpdateSavedHealth( newhealth );

            co.Wait( tickTime );
        }
    }

    UFUNCTION()
	void OnShieldDestroyed()
	{
        net_IsOnCooldown.SetNetValue( true );
		shieldCooldownTimer = System::SetTimer( this, n"RepairShield", SoulShieldConst::SHIELD_REGEN_TIME, false );

        TeardownShield();
        if ( !IsValid( asOwner ) )
            return;

        asOwner.EquipLastPrimaryWeapon();
        ANCWeapon tacticalWep = asOwner.GetWeaponAtSlot( EWeaponSlot::TacticalSlot );
        if ( IsValid( tacticalWep ) )
        {
            tacticalWep.SetClipAmmo( 0 );

            if ( IsValid( asOwner ) && IsValid( tacticalWep ) )
                asOwner.tacticalCooldownComponent.StartCooldown( tacticalWep, SoulShieldConst::SHIELD_REGEN_TIME );
        }
	}

    UFUNCTION()
	void RepairShield()
	{
        net_IsOnCooldown.SetNetValue( false );
		UpdateSavedHealth( SoulShieldConst::SHIELD_HEALTH );

        ANCWeapon tacticalWep = asOwner.GetWeaponAtSlot( EWeaponSlot::TacticalSlot );
        if ( IsValid( tacticalWep ) )
            tacticalWep.SetClipAmmo( 1 );

		if ( IsValid( myShield ) )
			myShield.healthComponent.SetHealth( SoulShieldConst::SHIELD_HEALTH );
	}






    private int32 appliedCameraOverrideID = -1;
    bool isPresentationSetup = false;

    UFUNCTION()
    void OnIsShieldDeployedChanged( bool old, bool new )
    {
        if ( !IsValid( asOwner ) )
            return;

        if ( !asOwner.IsLocallyControlled() )
            return;

        if ( old == new )
            return;

        if ( new && !isPresentationSetup )
        {
            //tpp camera
            appliedCameraOverrideID = asOwner.GetTPPCameraSettings().AddOverrideSettings(n"SoulShieldCamera", soulShieldCameraSettings);
            if ( !asOwner.TPPCameraComponent.tppRequests.Contains( n"SoulShield" ) )
			    asOwner.TPPCameraComponent.AddTPPRequest( n"SoulShield" );

            isPresentationSetup = true;
        }
        else if ( !new && isPresentationSetup )
        {
            asOwner.GetTPPCameraSettings().ClearOverrideSettingsWithLerp(appliedCameraOverrideID, 0.5f, EEasingFunc::Linear);
			appliedCameraOverrideID = -1;

            //todo: need to do some special anim stuff here to hide the TPP snap
			asOwner.TPPCameraComponent.ClearTPPRequest( n"SoulShield", false );
			if ( !asOwner.TPPCameraComponent.HasTPPRequests() )
				asOwner.TPPCameraComponent.ForceDisableTPP(0.f);

            isPresentationSetup = false;
        }
    }





    UFUNCTION()
    AAS_SoulShield GetMySoulShield()
    {
        return Cast<AAS_SoulShield>( net_myShield.Entity );
    }
}







UCLASS()
class UAS_ShieldThread : UAS_Thread
{
    AAS_PlayerEntity asOwner;
    AAS_SoulShieldManager manager;
    AAS_SoulShield myShield;

    int turnSlowEffectID;
    int moveSlowEffectID;

    UFUNCTION()
    void Init( AAS_PlayerEntity a, AAS_SoulShieldManager m )
    {
        asOwner = a;
        manager = m;
        Start();
    }

    void OnThreadStart(UNCCoroutine co) override
    {
        Super::OnThreadStart(co);
        
        co.EndOn( manager, manager.holsterShield, n"OnHolsterShield" );
        co.EndOn( asOwner, asOwner.OnDeathSignal, n"OnDeath" );

        co.EndOn( asOwner, asOwner.OnMountSignal, n"OnMount" );
        co.EndOn( asOwner, asOwner.OnTeleportStartSignal, n"OnTeleport" );
        co.EndOn( asOwner, asOwner.OnDestroyOrbStartedSignal, n"OnDestroyOrb" );
        co.EndOn( asOwner, asOwner.OnStartPlantBreaker, n"OnStartPlant" );
        co.EndOn( asOwner, asOwner.OnStartPlantExtender, n"OnStartExtender" );
        co.EndOn( asOwner, asOwner.OnStartZipline, n"OnStartZipline" );

        myShield = Cast<AAS_SoulShield>( Server_SpawnEntity( manager.shieldClass, asOwner, asOwner.GetActorLocation(), asOwner.GetViewRotationFlat() ) );
        myShield.SetNCNetOwnerId( asOwner.GetEntityId() );
        myShield.AttachToComponent( asOwner.RootComponent );

        myShield.healthComponent.SetHealth( manager.net_shieldHealth );
        myShield.OnShieldDestroyed.AddUFunction( this, n"OnShieldDestroyed" );
        myShield.myManager = manager;
        manager.myShield = myShield;
        myShield.teamComponent.SetTeam( asOwner.GetTeam() );

        manager.net_IsShieldDeployed.SetNetValue( true );

        turnSlowEffectID = asOwner.AddStatusEffect( GameplayTags::StatusEffect_TurnSlow, 0.75, 99999.0f, 0, 0 );
        //moveSlowEffectID = myPlayer.AddStatusEffect( GameplayTags::StatusEffect_MoveSlow, 0.3, 99999.0f, 0, 0 );

        co.WaitForever();
    }

    UFUNCTION()
    void OnShieldDestroyed()
    {
        if ( IsValid( manager ) )
            manager.OnShieldDestroyed();
    }

    void OnThreadEnd(FNCCoroutineEndParams params) override
    {
        Super::OnThreadEnd(params);

        if ( params.EndTag == n"OnStartZipline" && IsValid( asOwner ) )
            asOwner.TryEquipLastPrimary();

        myShield.Destroy();

        asOwner.ClearStatusEffect( turnSlowEffectID );
        //myPlayer.ClearStatusEffect( moveSlowEffectID );

        if ( !IsValid( manager ) )
            return;
        manager.net_IsShieldDeployed.SetNetValue( false );
    }
}



























UCLASS()
class UAS_SoulShieldWidget : UUserWidget
{
    AAS_SoulShieldManager myManager;

    UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UImage chargeFill;

    UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	USizeBox sizeBox;

    UPROPERTY(BlueprintReadOnly, Meta=(BindWidgetAnim), Transient)
	protected UWidgetAnimation ScaleUp;
    UPROPERTY(BlueprintReadOnly, Meta=(BindWidgetAnim), Transient)
	protected UWidgetAnimation ScaleDown;
    UPROPERTY(BlueprintReadOnly, Meta=(BindWidgetAnim), Transient)
	protected UWidgetAnimation Flash;

	UFUNCTION()
    void Init( AAS_SoulShieldManager manager )
    {
        myManager = manager;
        manager.net_shieldHealth.OnReplicated().AddUFunction( this, n"OnShieldHealthUpdated" );
        manager.net_IsShieldDeployed.OnReplicated().AddUFunction( this, n"OnShieldDeployedChanged" );
        manager.net_IsOnCooldown.OnReplicated().AddUFunction( this, n"OnShieldCooldownChanged" );

        Client_GetLocalASPawn().AddStatusEffectHasBegunCallback( GameplayTags::StatusEffect_SoulBonded, this, n"OnSoulBondStarted" );
        Client_GetLocalASPawn().AddStatusEffectHasEndedCallback( GameplayTags::StatusEffect_SoulBonded, this, n"OnSoulBondEnded" );
    }

    UFUNCTION()
    void OnShieldHealthUpdated( float32 old, float32 new )
    {
        if ( myManager.net_IsOnCooldown )
            return;

        float healthFrac = new / SoulShieldConst::SHIELD_HEALTH;
        float invFrac = 1 - healthFrac;
        chargeFill.GetDynamicMaterial().SetScalarParameterValue( n"Fill", invFrac );

        FLinearColor barColor = FLinearColor::White;
        if ( new > old )
            barColor = FLinearColor::LucBlue;
        else if ( healthFrac <= 0.5 )
        {
            float alpha = healthFrac / 0.5;
            barColor = Math::Lerp( FLinearColor::Yellow, FLinearColor::White, alpha );
        }
        chargeFill.GetDynamicMaterial().SetVectorParameterValue( n"Color", barColor );
    }

    UFUNCTION()
    void OnShieldDeployedChanged( bool old, bool new )
    {
        if ( new )
            PlayAnimation( ScaleUp );
        else
            PlayAnimation( ScaleDown );
    }

    UFUNCTION()
    void OnShieldCooldownChanged( bool old, bool new )
    {
        int numLoops = int( SoulShieldConst::SHIELD_REGEN_TIME / 0.5 );
        if ( new )
            PlayAnimation( Flash, 0.0f, numLoops, EUMGSequencePlayMode::Forward, 1.0f, true );
        else
           chargeFill.GetDynamicMaterial().SetScalarParameterValue( n"Fill", 0 ); 
    }

    UFUNCTION()
    void OnSoulBondStarted()
    {
        SetWidgetVisibilitySafe( this, ESlateVisibility::Hidden );
    }

    UFUNCTION()
    void OnSoulBondEnded()
    {
        SetWidgetVisibilitySafe( this, ESlateVisibility::Visible );
    }
}