UCLASS( Abstract )
class UAS_CoinDepositWidget : UUserWidgetDefault
{
	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UTextBlock objectiveText;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	USpacer objectiveTextSpacer;

	const AActor owner;
	AAS_CoinDeposit ownerCoinDeposit;
	AAS_GoldRushDepositBox ownerCourier;

	UMaterialInstanceDynamic iconMaterial;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UAS_CommonTextBlock iconText;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UImage dynIcon;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	USpacer dynSpacer;

	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidgetAnim ), Transient )
	UWidgetAnimation startDeposit;

	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidgetAnim ), Transient )
	UWidgetAnimation completeDeposit;

	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidgetAnim ), Transient )
	UWidgetAnimation animateInText;

	UPROPERTY(EditAnywhere)
	bool animateOnCompleteDeposit = false;

	UPROPERTY( EditAnywhere )
	bool showText = true;

	UFUNCTION( BlueprintOverride )
	void PreConstruct( bool IsDesignTime )
	{
		objectiveText.SetVisibility( showText ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
		dynSpacer.SetVisibility( showText ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
	}

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		iconMaterial = dynIcon.GetDynamicMaterial();

		UAS_ClientScript_GoldRush goldRushScript = Cast<UAS_ClientScript_GoldRush>( GetModeClientScript() );
		if ( IsValid( goldRushScript ) )
		{
			goldRushScript.onDepositSuccessful.AddUFunction( this, n"OnDepositSuccessful" );
		}
	}

	UFUNCTION()
	private void OnDepositSuccessful( AAS_GoldRushDepositBox box, int amount )
	{
		if ( box == ownerCourier && animateOnCompleteDeposit )
			PlayAnimation( completeDeposit );
	}

	void SetOwnerActor( const AActor newOwner )
	{
		if ( owner == newOwner )
			return;

		const AActor oldOwner = owner;
		owner				  = newOwner;
		OnOwnerActorChanged( oldOwner, newOwner );
	}

	void OnOwnerActorChanged( const AActor oldOwner, const AActor newOwner )
	{
		AAS_CoinDeposit old = Cast<AAS_CoinDeposit>( oldOwner );
		AAS_CoinDeposit new = Cast<AAS_CoinDeposit>( newOwner );
		if ( IsValid( old ) )
		{
			old.NCNetComponent.OnPostReplication.Unbind( this, n"OnOwnerPostReplication" );
			old.net_currentCourier.OnReplicated().Unbind( this, n"OnCourierUpdated" );
		}
		ownerCoinDeposit = new;
		if ( IsValid( new ) )
		{
			new.NCNetComponent.OnPostReplication.AddUFunction( this, n"OnOwnerPostReplication" );
			new.net_currentCourier.OnReplicated().AddUFunction( this, n"OnCourierUpdated" );
			OnOwnerCourierUpdated( new.GetCurrentCourier() );
		}
		else
		{
			OnOwnerCourierUpdated( nullptr );
		}
		Update();
	}

	void OnOwnerCourierUpdated( AAS_GoldRushDepositBox new )
	{
		AAS_GoldRushDepositBox old = ownerCourier;
		if ( old == new )
			return;

		if ( IsValid( old ) )
		{
			old.NCNetComponent.OnPostReplication.Unbind( this, n"OnOwnerPostReplication" );
			old.courierBackpackComponent.OnContentsChangedDelegate.Unbind( this, n"OnBackpackContentsChanged" );
		}
		ownerCourier = new;
		if ( IsValid( new ) )
		{
			new.NCNetComponent.OnPostReplication.AddUFunction( this, n"OnOwnerPostReplication" );
			new.courierBackpackComponent.OnContentsChangedDelegate.AddUFunction( this, n"OnBackpackContentsChanged" );
			SetDepositIndex( new.net_depositIndex );
		}
		Update();
		currentFillFrac = targetFillFrac;
		SetScalarParameterValueOnIcons( n"Fill", float32( currentFillFrac ) );
	}

	UFUNCTION()
	private void OnBackpackContentsChanged( UBackpackComponent backpack )
	{
		Update();
	}

	UFUNCTION()
	private void OnOwnerPostReplication( const FNCNetReplicationChanges&in changes )
	{
		if ( changes.ChangedProperties.Num() == 2 && changes.ChangedProperties.Contains( n"ReplicatedLocation" ) && changes.ChangedProperties.Contains( n"ReplicatedMovementTick" ) )
			return;

		Update();

		if ( ( ownerCoinDeposit != nullptr || ownerCourier != nullptr ) && changes.ChangedProperties.Contains( n"net_depositIndex" ) )
		{
			const int idx = ownerCoinDeposit != nullptr ? ownerCoinDeposit.net_depositIndex : ownerCourier.net_depositIndex;
			SetDepositIndex( idx );
		}
	}

	UFUNCTION()
	private void OnCourierUpdated( const AActor oldValue, const AActor newValue )
	{
		OnOwnerCourierUpdated( Cast<AAS_GoldRushDepositBox>( newValue ) );
	}

	UFUNCTION()
	void SetDepositIndex( int index )
	{
		FText text = GetLocalizedLocationNameFromIndex( Localization::GoldRush, index );

		iconText.SetText( text );
	}

	void SetScalarParameterValueOnIcons( FName paramName, float32 value )
	{
		if ( IsValid( iconMaterial ) )
			iconMaterial.SetScalarParameterValue( paramName, value );
	}

	void SetVectorParameterValueOnIcons( FName paramName, FLinearColor value )
	{
		if ( IsValid( iconMaterial ) )
			iconMaterial.SetVectorParameterValue( paramName, value );
	}

	bool isFilling 		  = false;
	bool deposit		  = false;
	float currentFillFrac = 0.0;
	float targetFillFrac  = 0.0;
	float currentFillSpeed = 0.0;
	float maxFillSpeed = 1.0;
	float fillAccel = 0.3;
	float startFillSpeed = 0.1;

	UFUNCTION( BlueprintOverride )
	void Tick( FGeometry MyGeometry, float InDeltaTime )
	{
		IncrementFill( InDeltaTime );
	}

	void IncrementFill( float InDeltaTime )
	{
		if ( currentFillFrac == targetFillFrac )
		{
			isFilling = false;
			currentFillSpeed = startFillSpeed;
			return;
		}

		isFilling = true;
		float maxDelta = currentFillSpeed * InDeltaTime;
		currentFillSpeed += fillAccel;
		currentFillSpeed = Math::Min( currentFillSpeed, maxFillSpeed );

		if ( currentFillFrac < targetFillFrac )
		{
			float delta = Math::Min( targetFillFrac - currentFillFrac, maxDelta );
			currentFillFrac += delta;
		}
		else
		{
			currentFillFrac = targetFillFrac;
		}

		SetScalarParameterValueOnIcons( n"Fill", float32( currentFillFrac ) );
	}

	void Update()
	{
		if ( !IsValid( GetLocalHUD() ) )
			return;

		tickEndSignal.Emit();

		bool inDeposit = false;

		int usingTeam = -1;
		SetRenderScale( FVector2D( 1.0, 1.0 ) );
		SetRenderOpacity( 1.0 );

		if ( IsValid( ownerCourier ) )
		{
			usingTeam = ownerCourier.GetTeam();

			int coinsInCourier = ownerCourier.GetCurrentGold();
			int maxCoins	   = ownerCourier.GetGoldMaxCapacity();
			float32 fillFrac   = float32( coinsInCourier ) / maxCoins;
			targetFillFrac	   = fillFrac;
			if ( !isFilling )
				currentFillSpeed = startFillSpeed;
			IncrementFill( 1.0 );

			inDeposit = false;

			if ( usingTeam == -1 )
			{
				SetScalarParameterValueOnIcons( n"IsNeutral", 1.0 );
				SetVectorParameterValueOnIcons( n"FillColor", FLinearColor( 1.000000, 0.812178, 0.000000, 1.000000 ) );
				if ( showText )
					objectiveText.SetText( GetLocalizedText( Localization::GoldRush, "deposit" ) );
				PlayDepositAnim( inDeposit );
				return;
			}

			SetScalarParameterValueOnIcons( n"IsNeutral", 0.0 );

			if ( usingTeam == Client_GetLocalPawn().GetTeam() )
			{
				inDeposit = true;
				SetScalarParameterValueOnIcons( n"EnemyTeamState", 0.0 );
				if ( showText )
					objectiveText.SetText( GetLocalizedText( Localization::GoldRush, "defend" ) );
			}
			else
			{
				inDeposit = true;
				SetScalarParameterValueOnIcons( n"EnemyTeamState", 1.0 );
				if ( showText )
					objectiveText.SetText( GetLocalizedText( Localization::GoldRush, "contest" ) );
			}

			if ( ownerCourier.net_sendTimeEnd > 0 )
				Thread( this, n"UpdateCourierSendingThread" );
		}
		else
		{
			SetRenderScale( FVector2D( 0.9, 0.9 ) );
			SetRenderOpacity( 0.5 );
			SetScalarParameterValueOnIcons( n"IsNeutral", 1.0 );
			targetFillFrac = 0.0;
			inDeposit	   = false;
			objectiveText.SetText( FText() );
		}

		PlayDepositAnim( inDeposit );
	}

	void PlayDepositAnim( bool inDeposit )
	{
		if ( inDeposit == deposit )
			return;

		deposit = inDeposit;

		if ( deposit )
		{
			float32 time = GetAnimationCurrentTime( startDeposit );
			PlayAnimation( startDeposit, time, 1, EUMGSequencePlayMode::Forward );
		}
		else
		{
			float32 time = GetAnimationCurrentTime( startDeposit );
			PlayAnimation( startDeposit, time, 1, EUMGSequencePlayMode::Reverse );
		}
	}

	FNCCoroutineSignal tickEndSignal;

	UFUNCTION()
	void UpdateCourierSendingThread( UNCCoroutine co )
	{
		co.EndOnDestroyed( ownerCourier );
		co.EndOn( this, tickEndSignal );

		int usingTeam = ownerCourier.GetTeam();
		if ( usingTeam == -1 )
			return;

		int timeLeftMS	= ownerCourier.net_sendTimeEnd - ownerCourier.GetTimeMilliseconds();
		int totalTimeMS = int( ownerCourier.net_sendTimeEnd ) - int( ownerCourier.net_sendTimeStart );
		while ( timeLeftMS > 0 )
		{
			if ( showText )
			{
				if ( usingTeam == Client_GetLocalPawn().GetTeam() )
				{
					objectiveText.SetText( GetLocalizedText( Localization::GoldRush, "defend" ) );
				}
				else
				{
					objectiveText.SetText( GetLocalizedText( Localization::GoldRush, "contest" ) );
				}
			}

			float32 timerFrac = float32( timeLeftMS ) / float32( totalTimeMS );
			SetScalarParameterValueOnIcons( n"LerpAlpha", timerFrac );

			co.Wait( 0.05f );
			timeLeftMS = ownerCourier.net_sendTimeEnd - ownerCourier.GetTimeMilliseconds();
		}
	}
}

UCLASS( Abstract )
class UAS_PinnableWidget_CoinDeposit : UAS_InteractiveWidget
{
	UPROPERTY( NotEditable, BlueprintReadOnly, BindWidget )
	UAS_CoinDepositWidget mainWidget;

	UPROPERTY( NotEditable, BlueprintReadOnly, BindWidget )
	UAS_CoinDepositWidget pinWidget;
	
	UPROPERTY( NotEditable, BlueprintReadOnly, Meta = ( BindWidgetOptional ) )
	UAS_CoinDepositWidget pinWidgetShadow;

	AAS_CoinDeposit ownerCoinDeposit;
	AAS_GoldRushDepositBox ownerCourier;

	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidgetAnim ), Transient )
	UWidgetAnimation detailsAppear;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UTextBlock coinAmountText;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		Super::Construct();
		GetGameStateEntity_GoldRush().net_walletsLocked.OnReplicated().AddUFunction( this, n"OnWalletsLockedChanged" );

		mainWidget.objectiveText.SetRenderOpacity( 0 );
	}

	void OnHoverChanged( ANCPlayerCharacter user, bool selected ) override
	{
		Super::OnHoverChanged( user, selected );
		UpdateDetails();
	}

	void UpdateDetails()
	{
		ToggleDetails( IsValid( ownerCourier ) && ( GetHovered() || ownerCourier.net_isSendingPackage ) );
	}

	bool detailsOut = false;
	void ToggleDetails( bool _appear )
	{
		if ( _appear && !detailsOut )
		{
			detailsOut = true;
			mainWidget.PlayAnimationForward( mainWidget.animateInText );
			PlayAnimationForward( detailsAppear );
		}
		else if ( !_appear && detailsOut )
		{
			detailsOut = false;
			PlayAnimationReverse( detailsAppear );
			mainWidget.PlayAnimationReverse( mainWidget.animateInText );
		}
	}

	UFUNCTION()
	private void OnWalletsLockedChanged( bool oldValue, bool newValue )
	{
		Update();
	}

	void Update()
	{
		if ( !GetLocalHUD().pinnedWidgetManager.HasPinnableWidgetData( this ) )
			return;

		SetWidgetVisibilitySafe( this, IsValid( ownerCourier ) ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Hidden );

		if ( IsValid( ownerCourier ) )
		{
			int coinsInCourier = ownerCourier.GetCurrentGold();
			coinAmountText.SetText( FText::AsNumber( coinsInCourier, GetNumberFormattingOptions( ERoundingMode::HalfToEven, false, true, 0, 0 ) ) );
			GetLocalHUD().pinnedWidgetManager.GetPinnableWidgetData( this ).SetIsPinnable( ownerCourier.GetTeam() > -1 );
		}
		else
		{
			GetLocalHUD().pinnedWidgetManager.GetPinnableWidgetData( this ).SetIsPinnable( false );
			coinAmountText.SetText( FText::AsNumber( 0, GetNumberFormattingOptions( ERoundingMode::HalfToEven, false, true, 0, 0 ) ) );
		}

		UpdateDetails();
	}

	void OnOwnerActorChanged( const AActor oldOwner, const AActor newOwner ) override
	{
		Super::OnOwnerActorChanged( oldOwner, newOwner );

		{
			AAS_CoinDeposit old = Cast<AAS_CoinDeposit>( oldOwner );
			AAS_CoinDeposit new = Cast<AAS_CoinDeposit>( newOwner );
			if ( IsValid( old ) )
			{
				old.NCNetComponent.OnPostReplication.Unbind( this, n"OnOwnerPostReplication" );
				old.net_currentCourier.OnReplicated().Unbind( this, n"OnCourierUpdated" );
			}
			ownerCoinDeposit = new;
			if ( IsValid( new ) )
			{
				new.NCNetComponent.OnPostReplication.AddUFunction( this, n"OnOwnerPostReplication" );
				new.net_currentCourier.OnReplicated().AddUFunction( this, n"OnCourierUpdated" );
				OnOwnerCourierUpdated( new.GetCurrentCourier() );
				Update();
			}
			else
			{
				OnOwnerCourierUpdated( nullptr );
			}
		}

		mainWidget.SetOwnerActor( newOwner );
		pinWidget.SetOwnerActor( newOwner );
		if ( IsValid(pinWidgetShadow ) )
			pinWidgetShadow.SetOwnerActor( newOwner );
	}

	UFUNCTION()
	private void OnCourierUpdated( const AActor oldValue, const AActor newValue )
	{
		OnOwnerCourierUpdated( Cast<AAS_GoldRushDepositBox>( newValue ) );
	}

	void OnOwnerCourierUpdated( AAS_GoldRushDepositBox new )
	{
		AAS_GoldRushDepositBox old = ownerCourier;
		if ( IsValid( old ) )
		{
			old.NCNetComponent.OnPostReplication.Unbind( this, n"OnOwnerPostReplication" );
			old.courierBackpackComponent.OnContentsChangedDelegate.Unbind( this, n"OnBackpackContentsChanged" );
		}
		ownerCourier = new;
		if ( IsValid( new ) )
		{
			new.NCNetComponent.OnPostReplication.AddUFunction( this, n"OnOwnerPostReplication" );
			new.courierBackpackComponent.OnContentsChangedDelegate.AddUFunction( this, n"OnBackpackContentsChanged" );
		}

		Update();
	}

	UFUNCTION()
	private void OnBackpackContentsChanged( UBackpackComponent backpack )
	{
		Update();
	}

	UFUNCTION()
	private void OnOwnerPostReplication( const FNCNetReplicationChanges&in changes )
	{
		if ( changes.ChangedProperties.Num() == 2 && changes.ChangedProperties.Contains( n"ReplicatedLocation" ) && changes.ChangedProperties.Contains( n"ReplicatedMovementTick" ) )
			return;

		Update();
		UpdateDetails();
	}
}