UCLASS( Abstract )
class UAS_RaidMessagingManager_TwoTeam : UAS_RaidMessagingManager
{
	private EPriorityMessageTemplate pendingMessageTemplate = EPriorityMessageTemplate::_count;
	private UAS_ShieldBreakerCraftingAndCooldownThread shieldBreakerThread;
	private UAS_WaitForShieldbreakerCarrierThread shieldbreakerCarrierThread;
	private TArray<ERaidEventFlag> raidQueue;
	private bool holdForRaidReport = false;
	private TOptional<EObjectiveState> optPendingNewState;
	private UAS_PingPongTextThread pingPongThread;

	// These are all of the raid flags that this widget cares about, everything else can be ignored
	private TArray<ERaidEventFlag> validRaidFlags;
	default validRaidFlags.Add( ERaidEventFlag::ATTACKER_BREACHER_PLANTED );
	default validRaidFlags.Add( ERaidEventFlag::DEFENDER_BREACHER_PLANTED );
	default validRaidFlags.Add( ERaidEventFlag::ATTACKER_RAID_STARTED );
	default validRaidFlags.Add( ERaidEventFlag::ATTACKER_BREACHER_PENETRATED );
	default validRaidFlags.Add( ERaidEventFlag::DEFENDER_RAID_STARTED );
	default validRaidFlags.Add( ERaidEventFlag::DEFENDER_DOME_BREACHED );
	default validRaidFlags.Add( ERaidEventFlag::ATTACKER_BOMB_PLANTING );
	default validRaidFlags.Add( ERaidEventFlag::ATTACKER_BOMB_PLANTING_GENERATOR );
	default validRaidFlags.Add( ERaidEventFlag::DEFENDER_BOMB_BEING_PLANTED );
	default validRaidFlags.Add( ERaidEventFlag::DEFENDER_BOMB_BEING_PLANTED_GENERATOR );
	default validRaidFlags.Add( ERaidEventFlag::DEFENDER_BOMB_PLANTED );
	default validRaidFlags.Add( ERaidEventFlag::ATTACKER_BOMB_PLANTED );
	default validRaidFlags.Add( ERaidEventFlag::DEFENDER_BOMB_PLANTED_GENERATOR );
	default validRaidFlags.Add( ERaidEventFlag::ATTACKER_BOMB_PLANTED_GENERATOR );
	default validRaidFlags.Add( ERaidEventFlag::ATTACKER_BOMB_BEING_DEFUSED );
	default validRaidFlags.Add( ERaidEventFlag::DEFENDER_BOMB_DEFUSING );
	default validRaidFlags.Add( ERaidEventFlag::ATTACKER_BOMB_BEING_DEFUSED_GENERATOR );
	default validRaidFlags.Add( ERaidEventFlag::DEFENDER_BOMB_DEFUSING_GENERATOR );
	default validRaidFlags.Add( ERaidEventFlag::DEFENDER_RAID_AUTOENDING );
	default validRaidFlags.Add( ERaidEventFlag::ATTACKER_RAID_AUTOENDING );
	default validRaidFlags.Add( ERaidEventFlag::DEFENDER_RAID_MARA_SPAWN_PRESENT );
	default validRaidFlags.Add( ERaidEventFlag::ATTACKER_RAID_MARA_SPAWN_PRESENT );

	private TArray<ERaidState> raidStatePriority;
	default raidStatePriority.Add( ERaidState::MARA_SPAWN_PRESENT );
	default raidStatePriority.Add( ERaidState::RAID_AUTOENDING );
	default raidStatePriority.Add( ERaidState::BOMB_BEING_DEFUSED_GENERATOR );
	default raidStatePriority.Add( ERaidState::BOMB_BEING_DEFUSED );
	default raidStatePriority.Add( ERaidState::BOMB_PLANTED_GENERATOR );
	default raidStatePriority.Add( ERaidState::BOMB_PLANTED );
	default raidStatePriority.Add( ERaidState::BOMB_PLANTING_GENERATOR );
	default raidStatePriority.Add( ERaidState::BOMB_PLANTING );
	default raidStatePriority.Add( ERaidState::BREACHER_PENETRATED );
	default raidStatePriority.Add( ERaidState::BREACHER_PLANTED );

	UFUNCTION( BlueprintOverride )
	void Initialize()
	{
		Super::Initialize();

		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"TriggerRaidReport", n"SC_TriggerRaidReport" );

		// NEW RAID MESSAGING (WIP)
		if ( GetCvarBool( "ScriptDebug.NewRaidMessaging" ) )
			UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"TriggerRaidStarted", n"SC_TriggerRaidStarted" );

		// Initialize the game phase
		OnGamePhaseOrTimeChanged();
	}

	void InitializeRaidMessaging() override final
	{
		Super::InitializeRaidMessaging();

		if ( !IsValid( localPlayer ) )
			return;

		int playerTeam = localPlayer.GetTeam();
		int otherTeam  = GetOtherTeam( playerTeam );

		// "Health" is the number of points the other team has subtracted from the score limit, so we track the opposite team
		AAS_GameModeBase_Raidmode_TwoTeams raidMode = TwoTeamModeDefaults();
		if ( IsValid( raidMode ) )
		{
			int scoreLimit = raidMode.POINTS_INITIAL;
			SetScoreLimits( scoreLimit );
			SetTrackedTeams( playerTeam, otherTeam );
		}
	}

	private void OnNextPhaseTimeChanged( int oldValue, int newValue ) override final
	{
		Super::OnNextPhaseTimeChanged( oldValue, newValue );
		OnGamePhaseOrTimeChanged();
	}

	private void OnGamePhaseChanged( int oldValue, int newValue ) override final
	{
		Super::OnGamePhaseChanged( oldValue, newValue );

		if ( newValue > GamePhase::PLAYING )
		{
			// When we move out of playing, we can clean up the raid messaging
			CleanUpRaidMessaging();
		}

		OnGamePhaseOrTimeChanged();
	}

	private void OnRaidObjectiveStateChanged( int team, int oldValue, int newValue ) override final
	{
		Super::OnRaidObjectiveStateChanged( team, oldValue, newValue );
		if ( newValue == int( EObjectiveState::Hidden ) )
			return;

		if ( GetGamePhase() >= GamePhase::WINNER_DETERMINED )
			return;

		EObjectiveState newState = EObjectiveState( newValue );

		UpdateObjectiveText();
		UpdateObjectivePriorityMessage();
		UpdateObjectiveState();
		CheckForCriticalAlert();

		raidMessagingHud.SetRaidObjectiveState( newState );

		if ( teamObjectiveAudio.Contains( newState ) )
		{
			// Play any cues associated with the event changes from the hud
			Client_EmitSoundUI( teamObjectiveAudio[newState] );
		}
	}

	private void OnShotClockStateChanged( EShotClockState oldValue, EShotClockState newValue ) override final
	{
		Super::OnShotClockStateChanged( oldValue, newValue );

		if ( GetGamePhase() >= GamePhase::WINNER_DETERMINED )
			return;

		raidMessagingHud.SetRaidObjectiveState( GetCurrentObjectiveState() );
		raidMessagingHud.SetShotClockState( newValue );

		if ( shotClockAudio.Contains( newValue ) )
		{
			// Play any cues associated with the event changes from the hud
			Client_EmitSoundUI( shotClockAudio[newValue] );
		}
	}

	private void OnOvertimeChanged( bool oldValue, bool newValue ) override final
	{
		Super::OnOvertimeChanged( oldValue, newValue );
		if ( newValue )
		{
			// When overtime starts, an objective state doesn't trigger so we set the game phase timer here instead
			StartCountdownTimer( isCountdownAudioQueued );
		}

		ShowOrHideCharacterPortraits( newValue );
	}

	private void OnTeamScoreChanged( int teamId, int newValue ) override final
	{
		Super::OnTeamScoreChanged( teamId, newValue );
		CheckForCriticalAlert();
	}

	private void OnRaidEvent( AAS_RaidEventManager_v2 eventManager, ERaidEventFlag flag ) override final
	{
		Super::OnRaidEvent( eventManager, flag );
		if ( !IsValid( localPlayer ) || GetCurrentObjectiveState() == EObjectiveState::PostRaid )
			return;

		bool matchesValidRaidFlag = validRaidFlags.Contains( flag );
		if ( matchesValidRaidFlag )
		{
			bool isRaiding = IsTeamRaiding( localPlayer.GetTeam() );
			if ( ( isRaiding && IsRaidEventAffectingAttackers( flag ) ) || ( !isRaiding && IsRaidEventAffectingDefenders( flag ) ) )
			{
				raidQueue.AddUnique( flag );
			}

			UpdateRaidText( eventManager, flag );
		}

		CheckForActivePlant();
		CheckForCriticalAlert();
	}

	ERaidState lastRaidState = ERaidState::RAID_ENDED;

	void OnRaidStateChanged( AAS_RaidEventManager_v2 eventManager, int oldState, int newState ) override
	{
		Super::OnRaidStateChanged( eventManager, oldState, newState );
		ERaidState bestState = GetHighestPriorityRaidState( newState );

		if ( lastRaidState == bestState )
			return;

		bool isAttacker = eventManager.GetAttackerTeam() == localPlayer.GetTeam();

		FString subheaderId;
		FString headerId;
		TArray<FFormatArgumentValue> headerArgs;

		// These variables allow for case specific but shared logic
		bool addPlantedBombSiteToArgs = false;
		bool canPingPong			  = false;

		ClearCountdownTimer();

		switch ( bestState )
		{
			case ERaidState::BREACHER_PLANTED:
				TriggerRaidStartingThread();
				subheaderId = "raid_starting";
				break;
			case ERaidState::BREACHER_PENETRATED:

				StartCountdownTimer( false );
				if ( isAttacker )
					subheaderId = "raid_raiding";
				else
					subheaderId = "raid_defending";
				break;
			case ERaidState::RAID_AUTOENDING:
				StartCountdownTimer( true );
				subheaderId = "raid_auto_ending";
				break;

			case ERaidState::MARA_SPAWN_PRESENT:
				if ( isAttacker )
				{
					headerId = "raid_friendly_spawn_point_present";
				}
				else
				{
					headerId = "raid_enemy_spawn_point_present";
				}
				break;

			case ERaidState::BOMB_PLANTING:
				if ( isAttacker )
				{
					subheaderId = "raid_planting_bomb";
					headerId	= "subheader_vault";
				}
				else
				{
					subheaderId = "raid_bomb_being_planted";
					headerId	= "subheader_vault";
				}
				break;
			case ERaidState::BOMB_PLANTING_GENERATOR:
				addPlantedBombSiteToArgs = true;
				if ( isAttacker )
				{
					subheaderId = "raid_planting_bomb";
					headerId	= "subheader_subobjective";
				}
				else
				{
					subheaderId = "raid_bomb_being_planted";
					headerId	= "subheader_subobjective";
				}
				break;
			case ERaidState::BOMB_PLANTED:
				canPingPong = true;
				subheaderId = "raid_bomb_planted";
				headerId	= "subheader_vault";
				break;
			case ERaidState::BOMB_PLANTED_GENERATOR:
				addPlantedBombSiteToArgs = true;
				canPingPong				 = true;
				subheaderId				 = "raid_bomb_planted";
				headerId				 = "subheader_subobjective";
				break;
			case ERaidState::BOMB_BEING_DEFUSED:
				if ( isAttacker )
				{
					subheaderId = "raid_bomb_being_defused";
					headerId	= "subheader_vault";
				}
				else
				{
					subheaderId = "raid_defusing_bomb";
					headerId	= "subheader_vault";
				}
				break;
			case ERaidState::BOMB_BEING_DEFUSED_GENERATOR:
				if ( isAttacker )
				{
					subheaderId = "raid_bomb_being_defused";
					headerId	= "subheader_subobjective";
				}
				else
				{
					subheaderId = "raid_defusing_bomb";
					headerId	= "subheader_subobjective";
				}
				addPlantedBombSiteToArgs = true;
				break;
			default:
				return; // Anything we don't care about can just be ignored
		}

		if ( addPlantedBombSiteToArgs )
		{
			FRaidBaseObjectiveData data;
			if ( GetPlayerFacingDataOfCurrentBombPlant( data ) )
			{
				// Add the player facing name to the string if we want to
				headerArgs.Add( FFormatArgumentValue( data.name ) );
			}
		}

		// Always set the header (if ping pong isn't running) and subheader text, sometimes it may be empty though
		FText subheaderText = GetLocalizedText( Localization::Raid, subheaderId );
		if ( !IsValid( pingPongThread ) || !pingPongThread.IsRunning() )
		{
			SetSubheaderText( subheaderId.IsEmpty() ? Text::EmptyText : subheaderText );
		}

		SetHeaderText( headerId.IsEmpty() ? Text::EmptyText : GetLocalizedText( Localization::Raid, headerId, headerArgs ), true );

		// Some states need to show ping ponging text when the critical alert is happening
		bool shouldShowCriticalAlert = ShouldShowCriticalAlert();
		if ( canPingPong && shouldShowCriticalAlert )
		{
			if ( IsValid( localPlayer ) && IsValid( raidMessagingHud ) )
			{
				// Determing if the player will win or lose and trigger the ping pong thread
				FString pingPongId = IsTeamRaiding( localPlayer.GetTeam() ) ? "win_imminent" : "loss_imminent";
				pingPongThread	   = raidMessagingHud.TriggerSubheaderPingPongThread( subheaderText, GetLocalizedText( Localization::Raid, pingPongId ) );
			}
		}
		else if ( !canPingPong && IsValid( pingPongThread ) && pingPongThread.IsRunning() )
		{
			pingPongThread.Cancel();
		}
	}

	ERaidState GetHighestPriorityRaidState( int currentStateFlags )
	{
		for ( ERaidState s : raidStatePriority )
		{
			if ( Bitflags::HasEnumFlag( currentStateFlags, s ) )
				return s;
		}

		return ERaidState::RAID_ENDED;
	}

	private void OnRaidEventCleared( AAS_RaidEventManager_v2 eventManager, ERaidEventFlag flag ) override final
	{
		Super::OnRaidEventCleared( eventManager, flag );
		if ( !IsValid( localPlayer ) || GetCurrentObjectiveState() == EObjectiveState::PostRaid )
			return;

		bool matchesValidRaidFlag = validRaidFlags.Contains( flag );
		if ( matchesValidRaidFlag )
		{
			bool isRaiding = IsTeamRaiding( localPlayer.GetTeam() );
			if ( ( isRaiding && IsRaidEventAffectingAttackers( flag ) ) || ( !isRaiding && IsRaidEventAffectingDefenders( flag ) ) )
			{
				raidQueue.Remove( flag );
			}

			if ( !raidQueue.IsEmpty() )
			{
				// Go back to the last raid message
				ERaidEventFlag lastFlag = raidQueue.Last();

				// If the last flag being cleared is the raid started flag, we don't need to update anything else, we can just bail out early since other events handle the raid ending
				if ( lastFlag != ERaidEventFlag::ATTACKER_RAID_STARTED && lastFlag != ERaidEventFlag::DEFENDER_RAID_STARTED )
				{
					UpdateRaidText( eventManager, lastFlag );
				}
			}
		}

		CheckForActivePlant();
		CheckForCriticalAlert();
	}

	private void OnRaidStarted( AAS_RaidEventManager_v2 eventManager ) override final
	{
		Super::OnRaidStarted( eventManager );

		ShowOrHideObjectiveMarkers( true );
		ShowOrHideShieldbreakerTracker( false );
		ShowOrHideRaidingTeamRaidState( true );
		ShowOrHideRaidingTeamRespawns( true );
		ShowOrHideShieldbreakerIndicator( false );
	}

	private void OnRaidEnded( AAS_RaidEventManager_v2 eventManager ) override final
	{
		Super::OnRaidEnded( eventManager );

		CheckForActivePlant();
		CheckForCriticalAlert();

		ShowOrHideRaidingTeamRaidState( false );
		ShowOrHideRaidingTeamRespawns( false );
		ShowOrHideObjectiveMarkers( false );
		ShowOrHideShieldbreakerTracker( false );
		ShowOrHideShieldbreakerIndicator( false );

		raidQueue.Empty();
	}

	UFUNCTION()
	void SC_TriggerRaidStarted( TArray<FString> args )
	{
		bool isAttacker	  = args[0].ToBool();
		bool wasAutoPlant = args[1].ToBool();

		UAS_PriorityMessageData data = isAttacker ? GetTemplateMessageData( EPriorityMessageTemplate::V2_ATTACK_RAID_STARTED ) : GetTemplateMessageData( EPriorityMessageTemplate::V2_DEFEND_RAID_STARTED );

		int points = wasAutoPlant ? 0 : TwoTeamModeDefaults().POINTS_SB_PLANT;
		FString stringKey = isAttacker ? "raid_enemy_base_damage_done" : "raid_base_damage_done";
		data.subheader = GetLocalizedText( Localization::Raid, stringKey, FFormatArgumentValue( points ) );

		AddSplashMessage( data );
	}

	UFUNCTION()
	void SC_TriggerRaidReport( TArray<FString> args )
	{
		if ( args.Num() < 5 )
			return;

		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( IsValid( player ) )
		{
			// Save off the values from the server command
			int scoreEarned			= args[0].ToInt();
			int attackerTeam		= args[1].ToInt();
			int defenderTeam		= args[2].ToInt();
			int generatorsDestroyed = args[3].ToInt();
			int coinsAwarded		= args[4].ToInt();

			// Get info about our local player
			int teamId		  = player.GetTeam();
			bool wasAttacking = attackerTeam == teamId;

			int scoreToTest									= 0;
			AAS_GameModeBase_Raidmode_TwoTeams modeDefaults = TwoTeamModeDefaults();
			if ( IsValid( modeDefaults ) )
			{
				// Check to see what score they needed to "win"
				scoreToTest = modeDefaults.POINTS_GOOD_DEFENSE > 0 ? modeDefaults.POINTS_SB_PLANT : 0;
			}

			// Use the score earned and compare it to the points awarded by a shieldbreaker plant to see if the local player won the encounter
			bool localPlayerWon = ( scoreEarned > scoreToTest && wasAttacking ) || ( scoreEarned <= scoreToTest && !wasAttacking );

			EPriorityMessageTemplate template;
			if ( localPlayerWon )
			{
				// If the player had a successful raid, use the win template
				template = wasAttacking ? EPriorityMessageTemplate::V2_ATTACK_WIN : EPriorityMessageTemplate::V2_DEFEND_WIN;
			}
			else
			{
				// If the player lost, use the lose template
				template = wasAttacking ? EPriorityMessageTemplate::V2_ATTACK_LOSE : EPriorityMessageTemplate::V2_DEFEND_LOSE;
			}

			UAS_PriorityMessageData data = GetTemplateMessageData( template );
			if ( IsValid( data ) )
			{
				// Set the header and calculate the number of raid coins to show
				data.header = FText::Format( data.header, coinsAwarded );

				// For the subheader, show how many objectives were destroyed or damage done to the raiding team if they failed
				int teamBeingRaided = wasAttacking ? teamId : GetOtherTeam( teamId );
				FString subheaderId = generatorsDestroyed > 0 ? "raid_enemy_base_destroyed" : "raid_base_damage_done";

				TArray<FFormatArgumentValue> subheaderArgs;
				if ( generatorsDestroyed > 0 )
				{
					subheaderArgs.Add( FFormatArgumentValue( generatorsDestroyed ) );
					subheaderArgs.Add( FFormatArgumentValue( GetPossiblePlantPointCountForTeam( teamBeingRaided ) ) );
				}
				else
				{
					AAS_GameModeBase_Raidmode_TwoTeams raidMode = TwoTeamModeDefaults();
					if ( IsValid( raidMode ) )
					{
						subheaderArgs.Add( FFormatArgumentValue( raidMode.POINTS_GOOD_DEFENSE ) );
					}
				}

				data.subheader = GetLocalizedText( Localization::Raid, subheaderId, subheaderArgs );

				// When the player wins, we want to play some audio on the message
				if ( wasAttacking && localPlayerWon )
				{
					data.onAppearSound = raidSuccessAudio;
				}
				else if ( !wasAttacking && localPlayerWon )
				{
					data.onAppearSound = defendSuccessAudio;
				}

				if ( GetCvarBool( "ScriptDebug.NewRaidMessaging" ) )
					AddSplashMessage( data );
				else
					AddPriorityMessage( data );
			}
		}
	}

	void SC_TransitionToPlaying( TArray<FString> args ) override final
	{
		Super::SC_TransitionToPlaying( args );
		if ( args.Num() != 1 )
			return;

		Dialogue().PlayAnnouncerDialogue( GameplayTags::Audio_VO_GameUpdates_Match_Start_Ten );

		UAS_PriorityMessageData data = GetTemplateMessageData( EPriorityMessageTemplate::GAME_STARTING );
		if ( IsValid( data ) )
		{
			int endTime	   = args[0].ToInt();
			data.endTimeMs = endTime;

			if ( GetCvarBool( "ScriptDebug.NewRaidMessaging" ) )
			{
				data.autoDissappearDelay = TO_SECONDS( data.endTimeMs.GetValue() - GetTimeMilliseconds() );
				AddSplashMessage( data );
			}
			else
			{
				AddPriorityMessage( data );
			}
		}
	}

	private void SC_CarePackageIncoming( TArray<FString> args ) override final
	{
		Super::SC_CarePackageIncoming( args );

		if ( args.Num() != 1 )
			return;

		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			Print( f"SC_CarePackageIncoming" );
			scriptCallbacks.client_OnCarePackageIncoming.Broadcast( GameConst::INDEX_NONE, args[0].ToInt() );
		}
	}

	void SC_CarePackageLanded( TArray<FString> args ) override final
	{
		Super::SC_CarePackageLanded( args );

		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			Print( f"SC_CarePackageLanded" );
			scriptCallbacks.client_OnCarePackageLanded.Broadcast();
		}
	}

	void SC_CarePackageOpened( TArray<FString> args ) override final
	{
		Super::SC_CarePackageOpened( args );

		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			Print( f"SC_CarePackageOpened" );
			scriptCallbacks.client_OnCarePackageOpened.Broadcast();
		}
	}

	private void OnGamePhaseOrTimeChanged() override final
	{
		Super::OnGamePhaseOrTimeChanged();

		UCL_PriorityMessageManager_v2 messageManager = PriorityMessage();
		if ( !IsValid( messageManager ) )
			return;

		if ( countdownStartMs.IsSet() )
		{
			// If the next phase time changed and we have a countdown running, we need to update the duration
			ClearCountdownTimer();
			StartCountdownTimer( false );
		}

		int currentPhase = GetGamePhase();
		if ( currentPhase == GamePhase::WINNER_DETERMINED )
		{
			// Clear all messages at this point, we are done
			messageManager.ClearActiveCenterMessages( EPriorityMessageLevel::BASE );
		}

		CheckForCriticalAlert();
	}

	private void UpdateObjectivePriorityMessage()
	{
		if ( !IsValid( localPlayer ) )
			return;

		AAS_TeamStateManager_RaidMode teamState = GetTeamStateManager_RaidMode( localPlayer.GetTeam() );
		if ( IsValid( teamState ) )
		{
			EPriorityMessageTemplate priorityMessage = EPriorityMessageTemplate::_count;

			EObjectiveState objectiveState = teamState.GetRaidObjectiveState();
			switch ( objectiveState )
			{
				case EObjectiveState::DefensePhase:
					priorityMessage = EPriorityMessageTemplate::PREPARE_BASE_CENTER;
					break;
				case EObjectiveState::InterceptShieldBreaker:
				{
					priorityMessage = EPriorityMessageTemplate::SHIELDBREAKER_PICKED_UP_ENEMY;
				}
				break;
				case EObjectiveState::PickupShieldBreaker:
					priorityMessage = EPriorityMessageTemplate::SHIELDBREAKER_CRAFTED;
					break;
				case EObjectiveState::ShieldBreakerCrafting:
					priorityMessage = EPriorityMessageTemplate::SHIELDBREAKER_CRAFTING;
					break;
				case EObjectiveState::Raiding:
				{
					if ( PlayerHasShieldBreaker( localPlayer ) )
					{
						priorityMessage = EPriorityMessageTemplate::SHIELDBREAKER_PICKED_UP;
					}
					else
					{
						pendingMessageTemplate = EPriorityMessageTemplate::SHIELDBREAKER_PICKED_UP;
					}
				}
				break;
				case EObjectiveState::ExploreAndGearUp:
					priorityMessage = EPriorityMessageTemplate::EXPLORE_PHASE;
					break;
				case EObjectiveState::PostRaid:

					fallthrough;
				default:
					return; // Anything we don't care about can just be ignored
			}

			if ( priorityMessage != EPriorityMessageTemplate::_count )
			{
				// Send out any valid messages

				// NEW RAID MESSAGING (WIP)
				if ( GetCvarBool( "ScriptDebug.NewRaidMessaging" ) )
				{
					AddSplashMessage( GetTemplateMessageData( priorityMessage ) );
				}
				else
				{
					AddTemplateMessage( priorityMessage );
				}
			}
		}
	}

	private	void UpdateObjectiveText()
	{
		if ( !IsValid( localPlayer ) )
			return;

		AAS_TeamStateManager_RaidMode teamState = GetTeamStateManager_RaidMode( localPlayer.GetTeam() );
		if ( IsValid( teamState ) )
		{
			EObjectiveState objectiveState = teamState.GetRaidObjectiveState();
			if ( GetShotClockState() == EShotClockState::NONE )
			{
				// When a new state starts, clear any previous threads or timers we had, unless we are in overtime area
				ClearShieldBreakerThread();
				ClearCountdownTimer();
				ClearShieldbreakerCarrierThread();
			}

			// Store out data based on the states
			FString subheaderId;

			switch ( objectiveState )
			{
				case EObjectiveState::DefensePhase:
					StartCountdownTimer( false );
					subheaderId = "secure_your_base_state_subheader";
					break;
				case EObjectiveState::ShieldBreakerCrafting:
					TriggerShieldBreakerThread( true );
					subheaderId = "shieldbreaker_crafting_state_header";
					break;
				case EObjectiveState::ExploreAndGearUp:
					TriggerShieldBreakerThread( false );
					subheaderId = "explore_gather_state_subheader";
					break;
				case EObjectiveState::PickupShieldBreaker:
					StartCountdownTimer( false );
					subheaderId = "pickup_shieldbreaker_state_subheader";
					break;
				case EObjectiveState::InterceptShieldBreaker:
					TriggerShieldbreakerCarrierThread();
					subheaderId = "intercept_shieldbreaker_state_subheader";
					break;
				case EObjectiveState::Raiding:
					TriggerShieldbreakerCarrierThread();
					subheaderId = "raid_base_state_subheader";
					break;
				case EObjectiveState::PostRaid:
					subheaderId = "raid_ended";
					SetHeaderText( Text::EmptyText );
					break;
				default:
					return; // Anything we don't care about can just be ignored
			}

			// Update the subheader text based on the game state
			SetSubheaderText( GetLocalizedText( Localization::GameState, subheaderId ) );
		}
	}

	private void UpdateObjectiveState()
	{
		if ( !IsValid( localPlayer ) )
			return;

		AAS_TeamStateManager_RaidMode teamState = GetTeamStateManager_RaidMode( localPlayer.GetTeam() );
		if ( IsValid( teamState ) )
		{
			EObjectiveState objectiveState = teamState.GetRaidObjectiveState();
			ShowOrHideShieldbreakerTracker( IsObjectiveStateIntercept( objectiveState ) );
			ShowOrHideShieldbreakerIndicator( DoesObjectiveStateInvolveShieldbreaker( objectiveState ) );
		}
	}

	private void UpdateRaidText( AAS_RaidEventManager_v2 eventManager, ERaidEventFlag flag )
	{
		FString subheaderId;
		FString headerId;
		TArray<FFormatArgumentValue> headerArgs;

		// These variables allow for case specific but shared logic
		bool addPlantedBombSiteToArgs = false;
		bool canPingPong			  = false;

		ClearCountdownTimer();

		switch ( flag )
		{
			case ERaidEventFlag::ATTACKER_BREACHER_PLANTED:
			case ERaidEventFlag::DEFENDER_BREACHER_PLANTED:
				TriggerRaidStartingThread();
				subheaderId = "raid_starting";
				break;
			case ERaidEventFlag::ATTACKER_RAID_STARTED:
			case ERaidEventFlag::ATTACKER_BREACHER_PENETRATED:
				StartCountdownTimer( false );
				subheaderId = "raid_raiding";
				break;
			case ERaidEventFlag::DEFENDER_RAID_STARTED:
			case ERaidEventFlag::DEFENDER_DOME_BREACHED:
				StartCountdownTimer( false );
				subheaderId = "raid_defending";
				break;
			case ERaidEventFlag::ATTACKER_RAID_AUTOENDING:
			case ERaidEventFlag::DEFENDER_RAID_AUTOENDING:
				StartCountdownTimer( true );
				subheaderId = "raid_auto_ending";

				{
					EPriorityMessageTemplate template = flag == ERaidEventFlag::ATTACKER_RAID_AUTOENDING ? EPriorityMessageTemplate::V2_ATTACK_DOME_REPAIR : EPriorityMessageTemplate::V2_DEFEND_DOME_REPAIR;
					UAS_PriorityMessageData data	  = GetTemplateMessageData( template );
					if ( IsValid( data ) )
					{
						data.header			= GetLocalizedText( Localization::Raid, "raid_breaker_down" );
						data.subheader		= GetLocalizedText( Localization::Raid, "raid_breaker_down_sub" );
						data.autoDissappear = false;
						AddPriorityMessage( data );
					}
				}

				break;

			case ERaidEventFlag::ATTACKER_RAID_MARA_SPAWN_PRESENT:
				if ( Bitflags::HasEnumFlag( eventManager.GetAttackerFlags(), ERaidEventFlag::ATTACKER_RAID_AUTOENDING ) )
				{
					headerId = "raid_friendly_spawn_point_present";
				}
				else
				{
					return;
				}
				break;

			case ERaidEventFlag::DEFENDER_RAID_MARA_SPAWN_PRESENT:
				if ( Bitflags::HasEnumFlag( eventManager.GetAttackerFlags(), ERaidEventFlag::DEFENDER_RAID_AUTOENDING ) )
				{
					headerId = "raid_enemy_spawn_point_present";
				}
				else
				{
					return;
				}
				break;

			case ERaidEventFlag::ATTACKER_BOMB_PLANTING:
				subheaderId = "raid_planting_bomb";
				headerId	= "subheader_vault";
				break;
			case ERaidEventFlag::ATTACKER_BOMB_PLANTING_GENERATOR:
				subheaderId				 = "raid_planting_bomb";
				headerId				 = "subheader_subobjective";
				addPlantedBombSiteToArgs = true;
				break;
			case ERaidEventFlag::DEFENDER_BOMB_BEING_PLANTED:
				subheaderId = "raid_bomb_being_planted";
				headerId	= "subheader_vault";
				break;
			case ERaidEventFlag::DEFENDER_BOMB_BEING_PLANTED_GENERATOR:
				subheaderId				 = "raid_bomb_being_planted";
				headerId				 = "subheader_subobjective";
				addPlantedBombSiteToArgs = true;
				break;
			case ERaidEventFlag::DEFENDER_BOMB_PLANTED:
				subheaderId = "raid_bomb_planted";
				headerId	= "subheader_vault";
				canPingPong = true;
				break;
			case ERaidEventFlag::ATTACKER_BOMB_PLANTED:
				subheaderId = "raid_bomb_planted";
				headerId	= "subheader_vault";
				canPingPong = true;
				break;
			case ERaidEventFlag::DEFENDER_BOMB_PLANTED_GENERATOR:
				subheaderId				 = "raid_bomb_planted";
				headerId				 = "subheader_subobjective";
				addPlantedBombSiteToArgs = true;
				canPingPong				 = true;
				break;
			case ERaidEventFlag::ATTACKER_BOMB_PLANTED_GENERATOR:
				subheaderId				 = "raid_bomb_planted";
				headerId				 = "subheader_subobjective";
				addPlantedBombSiteToArgs = true;
				canPingPong				 = true;
				break;
			case ERaidEventFlag::ATTACKER_BOMB_BEING_DEFUSED:
				subheaderId = "raid_bomb_being_defused";
				headerId	= "subheader_vault";
				break;
			case ERaidEventFlag::DEFENDER_BOMB_DEFUSING:
				subheaderId = "raid_defusing_bomb";
				headerId	= "subheader_vault";
				break;
			case ERaidEventFlag::ATTACKER_BOMB_BEING_DEFUSED_GENERATOR:
				subheaderId				 = "raid_bomb_being_defused";
				headerId				 = "subheader_subobjective";
				addPlantedBombSiteToArgs = true;
				break;
			case ERaidEventFlag::DEFENDER_BOMB_DEFUSING_GENERATOR:
				subheaderId				 = "raid_defusing_bomb";
				headerId				 = "subheader_subobjective";
				addPlantedBombSiteToArgs = true;
				break;
			default:
				raidQueue.Remove( flag );
				return; // Anything we don't care about can just be ignored
		}

		if ( addPlantedBombSiteToArgs )
		{
			FRaidBaseObjectiveData data;
			if ( GetPlayerFacingDataOfCurrentBombPlant( data ) )
			{
				// Add the player facing name to the string if we want to
				headerArgs.Add( FFormatArgumentValue( data.name ) );
			}
		}

		// Always set the header (if ping pong isn't running) and subheader text, sometimes it may be empty though
		FText subheaderText = GetLocalizedText( Localization::Raid, subheaderId );
		if ( !IsValid( pingPongThread ) || !pingPongThread.IsRunning() )
		{
			SetSubheaderText( subheaderId.IsEmpty() ? Text::EmptyText : subheaderText );
		}

		SetHeaderText( headerId.IsEmpty() ? Text::EmptyText : GetLocalizedText( Localization::Raid, headerId, headerArgs ), true );

		// Some states need to show ping ponging text when the critical alert is happening
		bool shouldShowCriticalAlert = ShouldShowCriticalAlert();
		if ( canPingPong && shouldShowCriticalAlert )
		{
			if ( IsValid( localPlayer ) && IsValid( raidMessagingHud ) )
			{
				// Determing if the player will win or lose and trigger the ping pong thread
				FString pingPongId = IsTeamRaiding( localPlayer.GetTeam() ) ? "win_imminent" : "loss_imminent";
				pingPongThread	   = raidMessagingHud.TriggerSubheaderPingPongThread( subheaderText, GetLocalizedText( Localization::Raid, pingPongId ) );
			}
		}
		else if ( !canPingPong && IsValid( pingPongThread ) && pingPongThread.IsRunning() )
		{
			pingPongThread.Cancel();
		}
	}

	private void TriggerRaidStartingThread()
	{
		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( !IsValid( player ) )
			return;

		System::ClearAndInvalidateTimerHandle( countdownTimerHandle );

		AAS_ShieldBreaker towerToUse;

		int teamId = player.GetTeam();
		if ( IsTeamRaiding( teamId ) )
		{
			towerToUse = GetBreacherForTeam( teamId );
		}
		else
		{
			TArray<AAS_ShieldBreaker> breakers = GetAllBreachersTargetingDefendingTeam( teamId );
			towerToUse						   = GetHighestPriorityBreacherByPlantTime( breakers );
		}

		if ( IsValid( towerToUse ) )
		{
			countdownStartMs	= towerToUse.GetPlantTimeMS();
			countdownDurationMs = TO_MILLISECONDS( towerToUse.GetBreachTime() );
			TickCountdown();
		}
	}

	private void TriggerShieldBreakerThread( bool runCraftingThread )
	{
		shieldBreakerThread = Cast<UAS_ShieldBreakerCraftingAndCooldownThread>( CreateThread( UAS_ShieldBreakerCraftingAndCooldownThread::StaticClass(), this ) );
		if ( IsValid( shieldBreakerThread ) )
		{
			shieldBreakerThread.OnShieldBreakerThreadUpdate.AddUFunction( this, n"OnShieldBreakerCraftingThreadUpdate" );
			shieldBreakerThread.Init( runCraftingThread );
		}
	}

	private void ClearShieldBreakerThread()
	{
		if ( IsValid( shieldBreakerThread ) )
		{
			shieldBreakerThread.OnShieldBreakerThreadUpdate.Unbind( this, n"OnShieldBreakerCraftingThreadUpdate" );
			shieldBreakerThread.Cancel();
			shieldBreakerThread = nullptr;
		}
	}

	private void TriggerShieldbreakerCarrierThread()
	{
		if ( !IsValid( shieldbreakerCarrierThread ) || !shieldbreakerCarrierThread.IsRunning() )
		{
			// If there isn't a shieldbreaker thread already running, start one
			shieldbreakerCarrierThread = Cast<UAS_WaitForShieldbreakerCarrierThread>( NewObject( this, UAS_WaitForShieldbreakerCarrierThread::StaticClass() ) );
			if ( IsValid( shieldbreakerCarrierThread ) )
			{
				shieldbreakerCarrierThread.OnWaitForShieldbreakerCarrierThreadEnd.AddUFunction( this, n"OnWaitForShieldbreakerCarrierThreadEnd" );
				shieldbreakerCarrierThread.Init();
			}
		}
	}

	private void ClearShieldbreakerCarrierThread()
	{
		if ( IsValid( shieldbreakerCarrierThread ) )
		{
			shieldbreakerCarrierThread.OnWaitForShieldbreakerCarrierThreadEnd.Unbind( this, n"OnWaitForShieldbreakerCarrierThreadEnd" );
			shieldbreakerCarrierThread.Cancel();
			shieldbreakerCarrierThread = nullptr;
			pendingMessageTemplate	   = EPriorityMessageTemplate::_count;
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnShieldBreakerCraftingThreadUpdate( int countdownMs )
	{
		SetHeaderTextMs( countdownMs );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnWaitForShieldbreakerCarrierThreadEnd( AAS_PlayerEntity player )
	{
		if ( !IsValid( player ) || !IsValid( localPlayer ) )
			return;

		if ( player != localPlayer && player.GetTeam() == localPlayer.GetTeam() )
		{
			FName colorId	  = GetUIColorIdForPlayer( player );
			FText displayText = GetLocalizedText( Localization::GameState, "protect_player_message_header", FFormatArgumentValue( FText::FromName( colorId ) ), FFormatArgumentValue( player.GetPlayerNameAsText() ) );

			UAS_PriorityMessageData data = GetTemplateMessageData( pendingMessageTemplate );
			if ( IsValid( data ) && pendingMessageTemplate != EPriorityMessageTemplate::_count )
			{
				data.header	   = displayText;
				data.subheader = GetLocalizedText( Localization::GameState, "protect_player_message_subheader" );
				AddPriorityMessage( data );
			}

			SetSubheaderText( displayText );
		}

		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			scriptCallbacks.localClient_OnShieldbreakerCarrierFound.Broadcast( player );
			raidMessagingHud.SetShieldbreakerCarrier( player );
		}
	}

	private void OnRaidStartedOrEnded() override final
	{
		Super::OnRaidStartedOrEnded();
		UCL_PriorityMessageManager_v2 messageManager = PriorityMessage();
		if ( !IsValid( messageManager ) )
			return;

		messageManager.ClearActiveCenterMessages( EPriorityMessageLevel::BASE );
	}
}