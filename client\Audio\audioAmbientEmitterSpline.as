enum ENCAudioSplineFollowMode
{
	PlayerPosition,
	ViewDirection
};

class AAS_NCAudioAmbientEmitterSpline : AAS_ClientSideActor
{
	UPROPERTY( DefaultComponent, RootComponent )
	USceneComponent root;

	UPROPERTY( BlueprintReadWrite, DefaultComponent )
	USplineComponent audioSpline;

	// Default BP_NCAudioSpline_AudioActor
	UPROPERTY( BlueprintReadWrite )
	TSubclassOf<AActor> splineEmitterAudioClass;

	// PlayerPosition = Default Player Root | ViewDirection = PlayerAim + Root
	UPROPERTY( EditAnywhere )
	ENCAudioSplineFollowMode followMode = ENCAudioSplineFollowMode::PlayerPosition;

	// NCAudio Asset | Set FadeOutMS On NC AudioAsset
	UPROPERTY( EditAnywhere )
	UNCAudioAsset audioEvent;

	// Turn Debug Visualization ON|OFF
	UPROPERTY( EditAnywhere )
	bool debugDraw = false;

	// Max Distance Player Can be from sound before it turns off | Also used to dynamically scale Update Interval
	UPROPERTY( EditAnywhere )
	float maxRange = 3500.0f;

	// Speed at which Audio Updates Spline Position | Lower = Slower, Higher = Faster
	UPROPERTY( EditAnywhere )
	float interpolationSpeed = 2.5f;

	bool isEmitterPlaying = false;
	int eventID			  = 0;

	private ANCPlayerCharacter localPlayer;
	private AActor spawnedEmitter;
	private FVector playerLocation;
	private FVector currentTargetLocation;

	default ActorHiddenInGame = true;
	default bNetLoadOnClient  = true;
	default bCanBeDamaged	  = false;

	UFUNCTION( BlueprintOverride )
	void ClientBeginPlay()
	{
		if ( splineEmitterAudioClass.IsValid() && IsValid( audioSpline ) )
		{
			FVector spawnLocation = audioSpline.GetLocationAtSplinePoint( 0, ESplineCoordinateSpace::World );
			FRotator spawnRot	  = FRotator( 0, 0, 0 );
			spawnedEmitter		  = SpawnActor( splineEmitterAudioClass.Get(), spawnLocation, spawnRot );
		}
		localPlayer = Client_GetLocalPawn();
		Thread( this, n"UpdateInterval" );
	}

	// Doing the main logic here on a dynamic tick based on how far the player is relative to maxRange
	UFUNCTION()
	void UpdateInterval( UNCCoroutine co )
	{
		co.EndOnDestroyed( localPlayer );
		co.EndOnDestroyed( spawnedEmitter );

		while ( true )
		{
			FVector viewLocation;
			FVector bodyLocation   = localPlayer.GetActorLocation();
			FVector closestToBody  = audioSpline.FindLocationClosestToWorldLocation( bodyLocation, ESplineCoordinateSpace::World );
			float distanceToSpline = Distance( bodyLocation, closestToBody );

			currentTargetLocation = closestToBody;
			if ( followMode == ENCAudioSplineFollowMode::ViewDirection )
			{
				FVector playerEyeLocation = localPlayer.GetEyeLocation();
				FVector forward			  = localPlayer.GetViewRotation().GetForwardVector();
				viewLocation			  = playerEyeLocation + forward * 1500.0f;
				currentTargetLocation	  = audioSpline.FindLocationClosestToWorldLocation( viewLocation, ESplineCoordinateSpace::World );
			}

#if EDITOR
			if ( debugDraw )
			{
				FVector emitterLocation = spawnedEmitter.GetActorLocation();
				if ( distanceToSpline < maxRange )
				{
					DrawDebugSphere( currentTargetLocation, 40.0f, 0.5f, FLinearColor::Green, 0.5f );
					DrawDebugSphere( emitterLocation, 40.0f, 0.5f, FLinearColor::Blue, 0.5f );
					DrawDebugString( emitterLocation + FVector( 0.0, 0.0, 20.0 ), audioEvent.ToString(), 0.5, FLinearColor::Yellow );
				}
				else
				{
					DrawDebugSphere( emitterLocation, 40.0f, 1.0f, FLinearColor::Red, 1.5f );
					FString playStatus = IsSoundPlaying() ? "Yes" : "No";
					FString message	   = f"[NCAudioSpline] OUT OF RANGE: {audioEvent.ToString()} | Playing: {playStatus} | Distance: {distanceToSpline}";
					PrintToScreen( message, 1.0f, FLinearColor::Red );
				}
			}
#endif

			if ( !IsSoundPlaying() )
			{
				if ( distanceToSpline < maxRange )
				{
					StartPlayingSound();
				}
			}
			else
			{
				if ( distanceToSpline >= maxRange )
				{
					StopPlayingSound();
				}
			}

			float baseInterval = 0.5f;
			float tickScale	   = distanceToSpline > ( maxRange * 2 ) ? 12.0f : 1.0f;
			float newInterval  = baseInterval * tickScale;
			co.Wait( newInterval );
		}
	}

	// ONLY USING THIS TICK TO MOVE THE spawnedEmitter Smoothly. Doing this in the UpdateInterval caused it to Teleport (gross)
	UFUNCTION( BlueprintOverride )
	void Tick( float DeltaTime )
	{
		if ( !IsValid( spawnedEmitter ) )
			return;
		FVector currentLoc = spawnedEmitter.GetActorLocation();
		if ( !currentLoc.Equals( currentTargetLocation, 10.0f ) )
		{
			FVector newLoc = Math::VInterpTo( currentLoc, currentTargetLocation, DeltaTime, interpolationSpeed );
			spawnedEmitter.SetActorLocation( newLoc );
		}
	}

	UFUNCTION()
	void StartPlayingSound()
	{
		if ( !IsSoundPlaying() && IsValid( spawnedEmitter ) )
		{
			FAudioResultData resultData = Client_EmitSoundOnEntity( audioEvent, spawnedEmitter );
			eventID						= resultData.EventID;
			isEmitterPlaying			= true;
			SetActorTickEnabled( true );
		}
	}

	UFUNCTION()
	void StopPlayingSound()
	{
		if ( IsSoundPlaying() )
		{
			Client_StopSound( eventID );
			isEmitterPlaying = false;
			SetActorTickEnabled( false );
		}
	}

	UFUNCTION()
	bool IsSoundPlaying()
	{
		return isEmitterPlaying;
	}

	UFUNCTION( BlueprintOverride )
	void EndPlay( EEndPlayReason EndPlayReason )
	{
		Super::EndPlay( EndPlayReason );
		if ( IsClient() )
		{
			Client_StopSound( eventID );
			isEmitterPlaying = false;
			SetActorTickEnabled( false );
			if ( IsValid( spawnedEmitter ) )
				spawnedEmitter.Destroy();
		}
	}
};