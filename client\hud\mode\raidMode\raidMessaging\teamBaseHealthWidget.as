UCLASS( Abstract )
class UAS_TeamBaseHealth : UNC_DisplayWidget
{
	default StateOnConstruction = EDisplayConstructVisibility::Hidden;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage baseHealth;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage baseHealthBorder;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UPanelWidget baseHealthBorderAttachment;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_CommonTextBlock baseHealthValue;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UPanelWidget damageTailIndicatorAttachment;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private USpacer sizingSpacer;

	UPROPERTY( NotVisible, BlueprintHidden, Transient, Meta = ( BindWidgetAnim ) )
	private UWidgetAnimation healthChangedAnimation;

	UPROPERTY( NotVisible, BlueprintHidden, Transient, Meta = ( BindWidgetAnim ) )
	private UWidgetAnimation raidStartedAnimation;

	UPROPERTY( EditInstanceOnly, BlueprintHidden )
	private bool trackEnemyTeam = false;

	UPROPERTY( EditInstanceOnly, BlueprintHidden )
	private UMaterialInterface teamMaterial;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden )
	private FLinearColor friendlyColor;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden )
	private FLinearColor enemyColor;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden )
	private TSubclassOf<UCommonTextStyle> raidTextStyle;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden )
	private TSubclassOf<UCommonTextStyle> nonRaidTextStyle;

	access Training = private, UAS_RaidMessagingTrainingHud;
	access:Training int trackedTeamId = GameConst::INDEX_NONE;
	access:Training UMaterialInstanceDynamic healthMaterial;
	private UAS_DamageTailThread damageTailThread;
	private int lastTeamScore = 0;
	private int trackedScoreLimit = 100;
	private bool showSmallWidgetOutOfRaid = true;

	private const FName ALLOW_TIMER_PULSE_PARAMETER = n"AllowTimerPulse";
	private const FName SHOW_BORDER_PARAMETER = n"ShowBorder";
	private const float SPACER_RAID_SIZE_Y = 50.0f;
	private const float SPACER_NON_RAID_SIZE_Y = 24.0f;

	bool scoreAnimationEnabled = false;

	UFUNCTION( BlueprintOverride )
	void PreConstruct( bool isDesignTime )
	{
		FVector2D scale = trackEnemyTeam ? FVector2D::UnitVector : FVector2D( -1.0f, 1.0f );

		baseHealth.SetRenderScale( scale );
		if ( IsValid( teamMaterial ) )
		{
			baseHealth.SetBrushFromMaterial( teamMaterial );
		}

		baseHealthBorderAttachment.SetRenderScale( scale );
		baseHealthBorder.SetColorAndOpacity( trackEnemyTeam ? enemyColor : friendlyColor );

		baseHealthValue.SetJustification( trackEnemyTeam ? ETextJustify::Left : ETextJustify::Right );
		UOverlaySlot healthAsSlot = Cast<UOverlaySlot>( baseHealthValue.Slot );
		if ( IsValid( healthAsSlot ) )
		{
			healthAsSlot.SetHorizontalAlignment( trackEnemyTeam ? EHorizontalAlignment::HAlign_Left : EHorizontalAlignment::HAlign_Right );
		}

		damageTailIndicatorAttachment.SetRenderTransformAngle( 25.0f * ( trackEnemyTeam ? 1.0f : -1.0f ) );
		UOverlaySlot damageTailIndicatorAsSlot = Cast<UOverlaySlot>( damageTailIndicatorAttachment.Slot );
		if ( IsValid( damageTailIndicatorAsSlot ) )
		{
			damageTailIndicatorAsSlot.SetHorizontalAlignment( trackEnemyTeam ? EHorizontalAlignment::HAlign_Right : EHorizontalAlignment::HAlign_Left );
		}

		// In the editor, use the raid (full) size for the spacer
		sizingSpacer.SetSize( FVector2D( sizingSpacer.Size.X, SPACER_RAID_SIZE_Y ) );
		baseHealthValue.SetStyle( raidTextStyle );
	}

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		healthMaterial = CreateDynamicMaterialFromImageBrush( baseHealth );
		if ( IsValid( healthMaterial ) )
		{
			baseHealth.SetBrushFromMaterial( healthMaterial );
		}

		scoreAnimationEnabled = GameModeDefaults().GamemodeRules_ScoreAnimationsEnabled;

		// When the game starts however, use the correct starting size and hide the border
		if ( showSmallWidgetOutOfRaid )
		{
			sizingSpacer.SetSize( FVector2D( sizingSpacer.Size.X, SPACER_NON_RAID_SIZE_Y ) );
			SetWidgetVisibilitySafe( baseHealthBorderAttachment, ESlateVisibility::Collapsed );
			baseHealthValue.SetStyle( nonRaidTextStyle );
		}
	}

	UFUNCTION( BlueprintOverride )
	void OnShowStart()
	{
		UCL_ScriptCallbacks clientCallbacks = ClientCallbacks();
		if ( IsValid( clientCallbacks ) )
		{
			clientCallbacks.OnTeamScoreChanged.AddUFunction( this, n"OnTeamScoreChanged" );
		}

		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			scriptCallbacks.shared_OnRaidEvent.AddUFunction( this, n"OnRaidEvent" );
			scriptCallbacks.shared_OnRaidEventCleared.AddUFunction( this, n"OnRaidEventCleared" );
			scriptCallbacks.client_onPendingScoreChanged.AddUFunction( this, n"OnPendingScoreChanged" );
			scriptCallbacks.shared_OnRaidStarted.AddUFunction( this, n"OnRaidStarted" );
			scriptCallbacks.shared_OnRaidEnded.AddUFunction( this, n"OnRaidEnded" );
		}
	}

	UFUNCTION( BlueprintOverride )
	void OnHideEnd()
	{
		UCL_ScriptCallbacks clientCallbacks = ClientCallbacks();
		if ( IsValid( clientCallbacks ) )
		{
			clientCallbacks.OnTeamScoreChanged.Unbind( this, n"OnTeamScoreChanged" );
		}

		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			scriptCallbacks.shared_OnRaidEvent.Unbind( this, n"OnRaidEvent" );
			scriptCallbacks.shared_OnRaidEventCleared.Unbind( this, n"OnRaidEventCleared" );
			scriptCallbacks.client_onPendingScoreChanged.Unbind( this, n"OnPendingScoreChanged" );
			scriptCallbacks.shared_OnRaidStarted.Unbind( this, n"OnRaidStarted" );
			scriptCallbacks.shared_OnRaidEnded.Unbind( this, n"OnRaidEnded" );
		}
	}

	void SetTrackedTeam( int teamId )
	{
		if ( trackedTeamId != teamId )
		{
			trackedTeamId = teamId;
			OnTeamScoreChanged( trackedTeamId, GetTeamScore( trackedTeamId ) );
			Show();
		}
	}

	void SetScoreLimit( int scoreLimit )
	{
		if ( trackedScoreLimit != scoreLimit )
		{
			trackedScoreLimit = scoreLimit;
			UpdateBar();
		}
	}

	void SetPendingTeamScore( int pendingScore )
	{
		UpdateBar();
	}

	void ClearPendingTeamScore()
	{
		if ( IsValid( healthMaterial ) )
		{
			healthMaterial.SetScalarParameterValue( MaterialParameter::ALLOW_DAMAGE_TAIL_PARAMETER, MaterialParameter::GetTrueFalseFloat( false ) );
		}
	}

	void SetShowSmallWidgetOutOfRaid( bool shouldShowSmallWidgetOutOfRaid )
	{
		if ( showSmallWidgetOutOfRaid != shouldShowSmallWidgetOutOfRaid )
		{
			showSmallWidgetOutOfRaid = shouldShowSmallWidgetOutOfRaid;

			AAS_PlayerEntity player = Client_GetLocalASPawn();
			if ( IsValid( player ) )
			{
				int playerTeam = player.GetTeam();
				bool isInRaid  = IsTeamBeingRaided( playerTeam ) || IsTeamRaiding( playerTeam );
				UpdateWidgetSizing( isInRaid || !showSmallWidgetOutOfRaid );
			}
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnTeamScoreChanged( int teamId, int newValue )
	{
		if ( trackedTeamId != teamId )
			return;

		UpdateBar();
	}

	void UpdateBar()
	{
		int score		 = GetTeamScore( trackedTeamId );
		int pendingScore = 0;

		AAS_TeamStateManager_RaidMode teamStateManager = GetTeamStateManager_RaidMode( trackedTeamId );
		if ( IsValid( teamStateManager ) )
		{
			pendingScore = teamStateManager.GetPendingScore();
		}

		float pendingPercent = trackedScoreLimit > 0 ? Math::Max( float( pendingScore ) / trackedScoreLimit, 0 ) : 0.0f;
		float actualPercent	 = trackedScoreLimit > 0 ? Math::Max( float( score ) / trackedScoreLimit, 0 ) : 0.0f;

		if ( IsValid( healthMaterial ) )
		{
			if ( score <= pendingScore || pendingScore < 0 )
			{
				healthMaterial.SetScalarParameterValue( MaterialParameter::LERP_ALPHA, actualPercent );
				healthMaterial.SetScalarParameterValue( MaterialParameter::DAMAGE_TAIL_LERP_ALPHA, pendingPercent );
				healthMaterial.SetScalarParameterValue( MaterialParameter::ALLOW_DAMAGE_TAIL_PARAMETER, MaterialParameter::GetTrueFalseFloat( score != pendingScore || pendingScore < 0 ) );
			}
			else
			{
				healthMaterial.SetScalarParameterValue( MaterialParameter::LERP_ALPHA, pendingPercent );
				healthMaterial.SetScalarParameterValue( MaterialParameter::ALLOW_DAMAGE_TAIL_PARAMETER, MaterialParameter::GetTrueFalseFloat( true ) );
				healthMaterial.SetScalarParameterValue( MaterialParameter::DAMAGE_TAIL_LERP_ALPHA, actualPercent );
			}
		}

		baseHealthValue.SetText( FText::AsNumber( score, GetDefaultNumberFormattingOptionsWithGrouping() ) );

		if ( score != trackedScoreLimit && lastTeamScore != score )
		{
			// Only play the animation when the new value is changed
			// PlayAnimationForward( healthChangedAnimation, 1.5f );

			// if ( scoreAnimationEnabled )
			// {
			// 	if ( !IsValid( damageTailThread ) )
			// 	{
			// 		// Create a new damage tail thread for the base health bar
			// 		damageTailThread = Cast<UAS_DamageTailThread>( CreateThread( UAS_DamageTailThread::StaticClass(), this ) );
			// 	}

			// 	if ( IsValid( damageTailThread ) )
			// 	{
			// 		// Kick off the damage thread with the values
			// 		float startPerc = scoreLimit > 0 ? float( lastTeamScore ) / scoreLimit : 0.0f;
			// 		damageTailThread.Init( startPerc, actualPercent, healthMaterial );
			// 	}
			// }

			// UPanelWidget parent = damageTailIndicatorAttachment.GetParent();
			// if ( IsValid( parent ) )
			// {
			// 	// We move the indicator left or right depending on which team is being tracked
			// 	float size		 = parent.CachedGeometry.LocalSize.X;
			// 	float offsetPerc = size * ( 1 - actualPercent );
			// 	float posX		 = trackEnemyTeam ? offsetPerc * -1.0f : offsetPerc;
			// 	damageTailIndicatorAttachment.SetRenderTranslation( FVector2D( posX, 0.0f ) );
			// }

			// // We need to save the last team score for the damage tail thread
			// lastTeamScore = score;
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnRaidEvent( AAS_RaidEventManager_v2 eventManager, ERaidEventFlag flag )
	{
		CheckForCriticalAlert();
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnRaidEventCleared( AAS_RaidEventManager_v2 eventManager, ERaidEventFlag flag )
	{
		CheckForCriticalAlert();
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnPendingScoreChanged( int team, int oldValue, int newValue )
	{
		if ( team == trackedTeamId )
		{
			UpdateBar();
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnRaidStarted( AAS_RaidEventManager_v2 eventManager )
	{
		if ( showSmallWidgetOutOfRaid )
		{
			UpdateWidgetSizing( true );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnRaidEnded( AAS_RaidEventManager_v2 eventManager )
	{
		if ( showSmallWidgetOutOfRaid )
		{
			UpdateWidgetSizing( false );
		}
	}

	private void CheckForCriticalAlert()
	{
		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( !IsValid( player ) )
			return;

		bool isBeingRaided			 = IsTeamBeingRaided( trackedTeamId );
		bool shouldShowCriticalAlert = ShouldShowCriticalAlert();

		if ( IsValid( healthMaterial ) )
		{
			// Pulse the health bar if our base is critical
			healthMaterial.SetScalarParameterValue( ALLOW_TIMER_PULSE_PARAMETER,
													MaterialParameter::GetTrueFalseFloat( player.GetTeam() == trackedTeamId && isBeingRaided && shouldShowCriticalAlert ) );
		}
	}

	private void UpdateWidgetSizing( bool isInRaid )
	{
		// We hide the border when not in a raid and use smaller text
		SetWidgetVisibilitySafe( baseHealthBorderAttachment, isInRaid ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
		baseHealthValue.SetStyle( isInRaid ? raidTextStyle : nonRaidTextStyle );

		if ( IsValid( healthMaterial ) )
		{
			healthMaterial.SetScalarParameterValue( SHOW_BORDER_PARAMETER, MaterialParameter::GetTrueFalseFloat( isInRaid ) );
		}

		// Play the grow or shrink animation
		if ( isInRaid )
		{
			PlayAnimationForward( raidStartedAnimation, 4.0f );
		}
		else
		{
			PlayAnimationReverse( raidStartedAnimation, 6.0f );
		}
	}
}