UAS_ObituarySystem GetObituarySystem()
{
	UAS_ObituarySystem result = Cast<UAS_ObituarySystem>( UNCGameplaySystemsSubsystem::Get_SharedSystem( GetCurrentWorld(), UAS_ObituarySystem::StaticClass() ) );
	return result;
}

event void FQueueObituaryMessage( UAS_QueueItemData data );

UCLASS( Abstract )
class UAS_ObituarySystem : UNCGameplaySystem_Shared
{
	UPROPERTY( EditDefaultsOnly, BlueprintHidden )
	private TSubclassOf<UAS_QueueItemWidget> fallbackClass;

	private UAS_QueueWidget registeredQueueWidget;

	UFUNCTION( BlueprintOverride )
	void BeginPlay()
	{
		if ( IsClient() )
		{
			ClientBeginPlay();
		}
		else
		{
			// ServerBeginPlay();
		}
	}

	void RegisterQueueWidget( UAS_QueueWidget queueWidget )
	{
		if ( IsValid( queueWidget ) && registeredQueueWidget != queueWidget )
		{
			registeredQueueWidget = queueWidget;
		}
	}

	int TryToQueueObituaryMessage( UAS_QueueItemData data )
	{
		int result = GameConst::INVALID;

		ScriptAssert( IsClient(), f"TryToQueueObituaryMessage can only be called on the client" );

		if ( IsValid( data ) && IsValid( registeredQueueWidget ) )
		{
			result = registeredQueueWidget.AddItemToQueue( data );
		}

		return result;
	}

	int TryToQueueObituaryMessage( FText text )
	{
		int result = GameConst::INVALID;

		ScriptAssert( IsClient(), f"TryToQueueObituaryMessage can only be called on the client" );

		if ( !text.IsEmpty() )
		{
			UAS_ObituaryItemData data = UAS_ObituaryItemData( fallbackClass, text );
			if ( IsValid( data ) )
			{
				result = TryToQueueObituaryMessage( data );
			}
		}

		return result;
	}

	int TryToQueueLootObituaryMessage( FLootDataStruct lootData, int amount, int total )
	{
		int result = GameConst::INVALID;

		FBackpackItemStruct newitemData = MakeBackpackItem( lootData, amount );
		FGameplayTag newItemIndex		= newitemData.itemIndex;
		int itemCount					= newitemData.itemCount;
		int trackedId					= GameConst::INVALID;

		// Before we try to queue up a new loot message, we want to check the history of the current queue
		UAS_ObituaryWidget obituaryWidget = Cast<UAS_ObituaryWidget>( registeredQueueWidget );
		if ( IsValid( obituaryWidget ) )
		{
			FBackpackItemStruct trackedBackpackItem;
			if ( obituaryWidget.GetMatchingBackpackItem( newItemIndex, trackedBackpackItem, trackedId ) )
			{
				// If we are already tracking this item, we want to take into account the amount we are already showing
				itemCount += trackedBackpackItem.itemCount;

				// Then we need to create a new item to use because we have modified the item count
				newitemData = MakeBackpackItem( newItemIndex, itemCount );
			}
		}

		// TODO @jmccarty: Change this to be a glyph lookup
		FFormatArgumentValue itemIdParam = FFormatArgumentValue( lootData.name );

		// We can make the new text that shows the correct count and total
		FText text = GetLocalizedText( Localization::Obituary, Obituary::GAME_EVENT_LOOT_PICKED_UP, itemIdParam, FFormatArgumentValue( itemCount ), FFormatArgumentValue( total ) );

		if ( !text.IsEmpty() )
		{
			bool updateItemInQueue = trackedId != GameConst::INVALID;

			// Instead of using TryToQueueObituaryMessage with our text, we want to package in a bit more information to allow us to reuse item widgets for loot pickups
			UAS_ObituaryItemData data = UAS_ObituaryItemData( fallbackClass, text, newitemData );
			if ( IsValid( data ) )
			{
				if ( updateItemInQueue )
				{
					// If we have a tracked id already, we update the existing message and return that tracked id
					obituaryWidget.SetItemData( trackedId, data );
					obituaryWidget.UpdateItemInQueue( trackedId );
					result = trackedId;
				}
				else
				{
					// Otherwise we didn't get a tracked id, we need to queue a new message
					result = TryToQueueObituaryMessage( data );
				}
			}
		}

		return result;
	}

	void Server_SendObituaryMessageToPlayer( ANCPlayerCharacter player, FString key, FServerCommandLocalizedData data = FServerCommandLocalizedData() )
	{
		ScriptAssert( IsServer(), f"TryToQueueObituaryMessage can only be called on the server" );
		UNCRemoteScriptCommands::SendServerCommand( player, f"ServerCommandGenericObituaryMessage {key} {data.GetCommandString()}" );
	}

	void Server_SendObituaryMessageToAllPlayers( FString key, FServerCommandLocalizedData data = FServerCommandLocalizedData() )
	{
		ScriptAssert( IsServer(), f"TryToQueueObituaryMessage can only be called on the server" );

		TArray<ANCPlayerCharacter> players;
		GetAllActorsOfClass( players );

		for ( ANCPlayerCharacter player : players )
		{
			Server_SendObituaryMessageToPlayer( player, key, data );
		}
	}

	private void ClientBeginPlay()
	{
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"ServerCommandGenericObituaryMessage", n"SC_ServerCommandGenericObituaryMessage" );

		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			scriptCallbacks.localClient_OnGatherEvent.AddUFunction( this, n"OnGatherEvent" );
			scriptCallbacks.localClient_OnUltimateCooldownReduced.AddUFunction( this, n"OnUltimateCooldownReduced" );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void SC_ServerCommandGenericObituaryMessage( TArray<FString> args )
	{
		if ( args.Num() < 1 )
			return;

		FString key = args[0];

		TryToQueueObituaryMessage( GetLocalizedText_FromServerCommandString( Localization::Obituary, key, args, 1 ) );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnGatherEvent( FLootDataStruct lootData, int amount, int total )
	{
		TryToQueueLootObituaryMessage( lootData, amount, total );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnUltimateCooldownReduced( float32 amount )
	{
		// ANCPlayerCharacter player = Widget_GetPawnOwner( this );
		// if ( IsValid( player ) )
		// {
		// 	ANCWeapon weapon = player.GetWeaponAtSlot( WeaponSlot::RaidUltSlot );
		// 	if ( IsValid( weapon ) )
		// 	{
		// 		int cooldownReductionAsInt	   = int( cooldownReduction );
		// 		UAS_GatherMessageWidget widget = CreateGatherMessageWidget( GameplayTags::Dev_Invalid, cooldownReductionAsInt );
		// 		if ( IsValid( widget ) )
		// 		{
		// 			FWeaponData weaponData = weapon.GetWeaponData();
		// 			widget.InitializeWithSpecificDisplays( weaponData.Icon, GetLocalizedText( Localization::HUDMainWidget, "gather_percent", FFormatArgumentValue( widget.currentCount - cooldownReductionAsInt ) ), FText() );
		// 			widget.currentCount -= cooldownReductionAsInt;
		// 			System::ClearAndInvalidateTimerHandle( gatherMessageTimer );
		// 			gatherMessageTimer = System::SetTimer( this, n"ClearGatherMessage", 2.0, false );
		// 		}
		// 	}
		// }
	}
}