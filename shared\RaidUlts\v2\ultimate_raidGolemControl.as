UCLASS( Abstract )
class UAS_WeaponContext_RaidUlt_RaidGolemControl : UAS_RaidUltWeaponContext_Base
{
	default cooldownTime = 720;

	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<AAS_JadeDemonTransition> transitionActorClass;

	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<AAS_JadeDemonTransition> transitionOutActorClass;

	UPROPERTY( EditDefaultsOnly )
	UWeaponPrimaryAsset raidGolemSmashWeaponClass;

	UPROPERTY( EditDefaultsOnly )
	UPlayerSettingsAsset demonSettings;

	UPROPERTY( EditDefaultsOnly )
	UNCPlayerMovementSettingsAsset demonMovementSettings;
	
	UPROPERTY( EditDefaultsOnly )
	UAnimMontage transitionOutMontage;

	UPROPERTY( EditDefaultsOnly )
	UAnimMontage transformInMontage;

	UPROPERTY( EditDefaultsOnly )
	UAnimMontage transformInDemonMontage;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset transformInSound;

	UPROPERTY( EditDefaultsOnly )
	UNiagaraSystem transformInExplodeFx;

	FVector pendingTransformLocation;

	UFUNCTION(BlueprintOverride)
	bool CodeCallback_CanSwitchToWeaponCallback(ANCWeapon weapon)
	{
		bool result = Super::CodeCallback_CanSwitchToWeaponCallback(weapon);
		return result && weapon.GetWeaponOwner().IsUseEnabled();
	}

	bool CanRaidUltBeUsedHere( ANCWeapon weapon ) override
	{
		ANCPlayerCharacter player = weapon.GetWeaponOwner();
		return player.TryFindLocationForCapsuleSize( demonMovementSettings.CapsuleRadius, demonMovementSettings.CapsuleHalfHeight, pendingTransformLocation );
	}

	UFUNCTION( BlueprintOverride )
	void CodeCallback_OnWeaponPrimaryAttack( ANCWeapon weapon, FWeaponPrimaryAttackInfo attackInfo,
											 FWeaponPrimaryAttackReturnParams& returnInfo )
	{
		Super::CodeCallback_OnWeaponPrimaryAttack( weapon, attackInfo, returnInfo );

		ANCPlayerCharacter player = weapon.GetWeaponOwner();

		if ( IsServer() )
		{
			if ( player.IsZiplining() )
				player.DetatchFromZipline( EZiplineDetatchActions::ForcedOff );

			Server_EmitSoundAtLocation_WithOwner( transformInSound, player, attackInfo.eyePosition );
			UAS_DemonThread thread = Cast<UAS_DemonThread>( CreateThread( UAS_DemonThread::StaticClass(), player ) );
			thread.Init( player, this );
		}
	}
}

UCLASS( Abstract )
class UAS_WeaponContext_RaidGolemSmash : UNCWeaponScriptContext
{
	ANCPlayerCharacter ownerPlayer;

	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<AAS_ServerSideHammerSlamGolem> groundSlamClass;
	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<AAS_RaidGolemShield> golemShieldClass;

	private float savedHealthRaw = RaidGolemShield::SHIELD_HEALTH;
	int nextAllowGolemShieldTime;
	FTimerHandle shieldHealthRegenTimer;

	UFUNCTION( BlueprintOverride )
	bool CodeCallback_CanFireWeapon( ANCWeapon weapon )
	{
		ANCPlayerCharacter owner = weapon.GetWeaponOwner();
		return !owner.IsADS();
	}

	UFUNCTION( BlueprintOverride )
	void CodeCallback_OnWeaponTossRelease( ANCWeapon weapon, FWeaponPrimaryAttackInfo attackInfo,
										   FWeaponPrimaryAttackReturnParams& returnInfo )
	{
		if ( IsServer() )
		{
			ANCPlayerCharacter owner = weapon.GetWeaponOwner();
			owner.AddStatusEffect( GameplayTags::StatusEffect_TurnSlow, 0.85, 0.5, 0.0, 1.0 );
			owner.AddStatusEffect( GameplayTags::StatusEffect_MoveSlow, 0.75, 0.5, 0.0, 1.0 );
			SpawnHammerSlam( weapon, groundSlamClass );
		}
	}

	UFUNCTION( BlueprintOverride )
	void CodeCallback_OnWeaponActivate( ANCWeapon weapon )
	{
		ownerPlayer = Cast<AAS_PlayerEntity>( weapon.GetWeaponOwner() );

		// The info actor gets registered when the demon is done transforming,
		// wait for that to happen before we spawn the shield.
		if ( IsServer() )
		{
			UAS_Passive_JadeDemon passive = Passive_JadeDemon();
			passive.OnJadeDemonInfoActorRegistered.AddUFunction( this, n"OnJadeDemonInfoActorRegistered" );
		}
	}

	UFUNCTION()
	private void OnJadeDemonInfoActorRegistered( AAS_JadeDemonInfo info )
	{
		if ( IsValid( info ) && info.GetOwnerPlayer() == ownerPlayer)
		{
			AAS_RaidGolemShield golemShield = info.SpawnShield( golemShieldClass );
			golemShield.weaponContext		= this;
			golemShield.healthComponent.SetHealth( savedHealthRaw );
			golemShield.OnShieldDestroyed.AddUFunction( this, n"OnGolemShieldDestroyed" );
		}
	}

	UFUNCTION( BlueprintOverride )
	void CodeCallback_OnWeaponDeactivate( ANCWeapon weapon )
	{
		AAS_RaidGolemShield golemShield = GetGolemShield( ownerPlayer );
		if ( IsValid( golemShield ) )
			golemShield.SetActivated( false );

		UAS_Passive_JadeDemon passive = Passive_JadeDemon();
		passive.OnJadeDemonInfoActorRegistered.Unbind( this, n"OnJadeDemonInfoActorRegistered" );

		if ( IsServer() )
		{

			AAS_JadeDemonInfo infoActor = GetDataActor( ownerPlayer );
			if ( IsValid( infoActor ) )
			{
				infoActor.DestroyShield();
			}
		}
	}

	UFUNCTION( BlueprintOverride )
	bool CodeCallback_CanADS( ANCWeapon weapon )
	{
		if ( weapon.WeaponState == EWeaponState::TossPrep || weapon.WeaponState == EWeaponState::Toss )
			return false;

		AAS_PlayerEntity owner = Cast<AAS_PlayerEntity>( weapon.GetWeaponOwner() );
		if ( !IsValid( owner ) )
			return false;

		AAS_JadeDemonInfo infoActor = GetDataActor( ownerPlayer );
		if ( !IsValid( infoActor ) )
			return false;

		AAS_RaidGolemShield golemShield = Cast<AAS_RaidGolemShield>( infoActor.net_golemShield.GetEntity() );
		if ( !IsValid( golemShield ) )
			return false;

		if ( !golemShield.IsActivated && owner.GetTimeMilliseconds() < nextAllowGolemShieldTime )
			return false;

		return !golemShield.net_destroyed;
	}

	UFUNCTION( BlueprintOverride )
	void CodeCallback_OnWeaponADS( ANCWeapon weapon, bool isADS )
	{
		AAS_PlayerEntity owner = Cast<AAS_PlayerEntity>( weapon.GetWeaponOwner() );
		if ( !IsValid( owner ) )
			return;

		AAS_JadeDemonInfo info = GetDataActor( owner );
		if ( !IsValid( info ) )
			return;

		AAS_RaidGolemShield golemShield = Cast<AAS_RaidGolemShield>( info.net_golemShield.GetEntity() );
		if ( !IsValid( golemShield ) )
			return;

		bool shouldEnable = isADS;
#if EDITOR
		if ( GetCvarBool( "ScriptDebug.JadeDemonToggleShield" ) )
		{
			if ( !isADS )
				return;

			shouldEnable = IsValid( golemShield );
		}
#endif

		if ( shouldEnable )
		{
			if ( golemShield.net_savedShieldHealthFrac == 0 )
				return;

			golemShield.SetActivated( true );
			System::ClearAndInvalidateTimerHandle( shieldHealthRegenTimer );
			nextAllowGolemShieldTime = owner.GetTimeMilliseconds() + 500;

			if ( IsServer() )
			{
				owner.PlayBattleChatter_Global( GameplayTags::Audio_VO_BattleChatter_ADS_Reaction, 0.0 );
			}
		}
		else
		{
			golemShield.SetActivated( false );

			if ( IsServer() && !System::IsTimerActiveHandle( shieldHealthRegenTimer ) )
			{
				shieldHealthRegenTimer = System::SetTimer( this, n"StartShieldRepair", RaidGolemShield::SHIELD_REGEN_START_DELAY, false );
			}
		}
	}

	UFUNCTION()
	private void OnGolemShieldDestroyed()
	{
		shieldHealthRegenTimer = System::SetTimer( this, n"StartShieldRepair", RaidGolemShield::SHIELD_REGEN_POST_DESTROY_DELAY, false );
	}

	UFUNCTION()
	void StartShieldRepair()
	{
		AAS_RaidGolemShield golemShield = GetGolemShield( ownerPlayer );
		if ( !IsValid( golemShield ) )
			return;

		golemShield.net_destroyed.SetNetValue( false );

		ShieldHealthIncrease();
	}

	UFUNCTION()
	void ShieldHealthIncrease()
	{
		float newHealth = Math::Min( savedHealthRaw + 10, RaidGolemShield::SHIELD_HEALTH );
		SetSavedHealth( newHealth );
		AAS_RaidGolemShield golemShield = GetGolemShield( ownerPlayer );
		if ( IsValid( golemShield ) )
			golemShield.healthComponent.SetHealth( newHealth );

		if ( savedHealthRaw < RaidGolemShield::SHIELD_HEALTH )
		{
			shieldHealthRegenTimer = System::SetTimer( this, n"ShieldHealthIncrease", 0.1, false );
		}
	}

	void SetSavedHealth( float newHealth )
	{
		savedHealthRaw = newHealth;

		if ( !IsValid( ownerPlayer ) )
			return;

		AAS_RaidGolemShield shield = GetGolemShield( ownerPlayer );
		if ( !IsValid( shield ) )
			return;

		shield.net_savedShieldHealthFrac.SetNetValue( newHealth / RaidGolemShield::SHIELD_HEALTH );
	}

	AAS_ServerSideHammerSlam SpawnHammerSlam( ANCWeapon weapon, TSubclassOf<AAS_ServerSideHammerSlamGolem> slamClass )
	{
		ANCPlayerCharacter weaponOwner = weapon.GetWeaponOwner();

		TArray<AActor> ignoreActors;
		ignoreActors.Add( weaponOwner );

		FVector direction  = weaponOwner.GetViewRotationFlat().ForwardVector;
		FVector upOffset   = FVector( 0, 0, 0 );
		FVector downOffset = FVector( 0, 0, -( weaponOwner.CapsuleComponent.GetScaledCapsuleHalfHeight() * 2.f ) );
		/*	raid golem capsule is a little too small and the slam can start through walls ( like the dome )
			if the golem is right up against it. This pushes back the starting point a little bit 	*/
		FVector start = weaponOwner.GetActorLocation() + upOffset;
		FVector end	  = weaponOwner.GetActorLocation() + downOffset;

		FHitResult meleeHit = LineTraceSingle( start, end, ETraceTypeQuery::WeaponFine, true, ignoreActors, true );

		if ( !meleeHit.GetbBlockingHit() )
			return nullptr;

		FVector origin = meleeHit.ImpactPoint;

		if ( GetCvarBool( f"ScriptDebug.DebugHammerSlam" ) )
			DrawDebugLine( meleeHit.TraceEnd, meleeHit.TraceEnd + ( direction * 100 ), 10, FLinearColor::Red, 2 );

		AAS_ServerSideHammerSlamGolem serverActor = Cast<AAS_ServerSideHammerSlamGolem>( Server_SpawnEntity( slamClass, weaponOwner, origin, FRotator::ZeroRotator ) );
		serverActor.ServerInit_FromGolem( weaponOwner, weapon, origin, direction );
		return serverActor;
	}

	AAS_JadeDemonInfo GetDataActor( ANCPlayerCharacter owner )
	{
		TArray<AAS_JadeDemonInfo> all;
		GetAllActorsOfClass( all );
		for ( AAS_JadeDemonInfo info : all )
		{
			if ( info.GetOwnerPlayer() == owner )
				return info;
		}

		return nullptr;
	}

	AAS_RaidGolemShield GetGolemShield( ANCPlayerCharacter owner )
	{
		if ( !IsValid( owner ) )
			return nullptr;

		AAS_JadeDemonInfo infoActor = GetDataActor( owner );
		if ( !IsValid( infoActor ) )
			return nullptr;

		return Cast<AAS_RaidGolemShield>( infoActor.net_golemShield.GetEntity() );
	}
}

const float32 SLAM_BACK_OFFSET = 15;

UCLASS( Abstract )
class AAS_ServerSideHammerSlamGolem : AAS_ServerSideHammerSlam
{
	default HAMMER_SLAM_FX_ITERATIONS = 6;
	default HAMMER_SLAM_TRAVEL_DIST	  = 700 + SLAM_BACK_OFFSET;
	default HAMMER_SLAM_TRAVEL_TIME	  = 0.25;

	default HAMMER_SLAM_FX_MIN_SCALE = 0.5;
	default HAMMER_SLAM_FX_MAX_SCALE = 3;
	default HAMMER_SLAM_MIN_RADIUS	 = 75;
	default HAMMER_SLAM_MAX_RADIUS	 = 300;

	void ServerInit_FromGolem( ANCPlayerCharacter owner, ANCWeapon weapon, FVector location, FVector direction )
	{
		startLoc = location;
		startDir = direction;

		cachedWeaponData = FHammerSlamWeaponData();

		cachedWeaponData.damageFar			  = 50;	 // data.damageFar;
		cachedWeaponData.damageNear			  = 50;	 // data.damageNear;
		cachedWeaponData.damageFarDistanceMax = 100; // data.damageFarDistanceMax;
		cachedWeaponData.damageFarDistanceMin = 100; // data.damageFarDistanceMin;
		cachedWeaponData.weaponName = weapon.GetWeaponClass();

		TArray<AActor> ignoreActors;
		ignoreActors.Add( owner );
		CalcSlamDataTraces( cachedWeaponData, ignoreActors );
		Server_EmitSoundAtLocation_WithOwner( slamAssetData.SlamSFXAsset, owner, net_SlamData[0].sv_HitResult.ImpactPoint );

		Thread( this, n"sh_DoSlamThread" );
	}

	void Server_OnSlamHitActor( AAS_PlayerEntity player, FHammerSlamWeaponData slamWeaponData, float32 Damage, FHitResult MeleeHit ) override
	{
		AActor actor = MeleeHit.GetActor();
		if ( !IsValid( actor ) )
			return;

		UHealthComponent healthComponent = actor.GetHealthComponent();
		if ( !IsValid( healthComponent ) )
			return;

		FDamageInfo damageInfo;

		damageInfo.attacker		  = player;
		damageInfo.damage		  = Damage;
		damageInfo.traceHitResult = MeleeHit;
		damageInfo.damageFlags	  = 0;
		// don't add DF_STRUCTURAL so predamage doesn't take weapon's structural damage ( which is for the regular impact )
		damageInfo.scriptDamageFlags		= EScriptDamageFlags::DF_MELEE /*| EScriptDamageFlags::DF_STRUCTURAL*/ | EScriptDamageFlags::DF_KNOCKBACK;
		damageInfo.damageSourceLocation		= damageInfo.attacker.GetPawnViewLocation();
		damageInfo.Inflictor 				= slamWeaponData.weapon;
		damageInfo.damageWeaponClassName	= slamWeaponData.weaponName;
		damageInfo.weaponModBitField 		= slamWeaponData.weaponModBitfield;

		healthComponent.ReceiveDamageSimple( damageInfo );
	}
}