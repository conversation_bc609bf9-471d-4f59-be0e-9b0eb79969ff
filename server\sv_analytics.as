USV_Analytics Sv_Analytics()
{
	return Cast<USV_Analytics>( UNCGameplaySystemsSubsystem::Get_ServerSystem(GetCurrentWorld(), USV_Analytics::StaticClass() ) );
}

bool IsAnalyticsEnabled()
{
	return GameModeDefaults().GamemodeRules_AnalyticsEnabled;
}

USTRUCT()
struct FSV_PlayerData
{
	FSV_PlayerData( ANCPlayerCharacter playerState )
	{
		playerName = playerState.GetPlayerName();
		anonymizedName = playerState.GetAnonymizedPlayerName();
		onlineId = playerState.NCOnlineId;
		team = playerState.GetTeam();	
	}

	UPROPERTY()
	FString playerName;

	UPROPERTY()
	FString anonymizedName;

	UPROPERTY()
	FString onlineId;

	UPROPERTY()
	int team;
}

USTRUCT()
struct FSV_PlayerCombatData
{
	FSV_PlayerCombatData( ANCPlayerCharacter playerState )
	{
		player = FSV_PlayerData ( playerState );
		locationX = float32( playerState.GetActorLocation().X );
		locationY = float32( playerState.GetActorLocation().Y );
		locationZ = float32( playerState.GetActorLocation().Z );

		// Grab active weapon
		ANCWeapon activeWeapon = playerState.GetActiveWeapon();
		if( IsValid( activeWeapon ) )
		{
			weapon = activeWeapon.WeaponClass.ToString();
			weaponRarity = GetWeaponRarity( activeWeapon ).ToString();
		}

		// Grab equipped shield
		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( playerState );
		if( asPlayer.IsActorShieldValid() )
			shield = asPlayer.GetEquippedShieldItem().itemIndex.GetTagName().GetPlainNameString();

		className = asPlayer.ClassManager().GetClassData().className.ToString();
	}

	UPROPERTY()
	FSV_PlayerData player;

	UPROPERTY()
	float32 locationX;

	UPROPERTY()
	float32 locationY;

	UPROPERTY()
	float32 locationZ;

	UPROPERTY()
	FString weapon;

	UPROPERTY()
	FString weaponRarity;

	UPROPERTY()
	FString shield;

	UPROPERTY()
	FString className;
}

USTRUCT()
struct FSV_PlayerDeathEvent
{
	FSV_PlayerDeathEvent ( ANCPlayerCharacter killerPlayer, ANCPlayerCharacter victimPlayer )
	{
		if (killerPlayer != nullptr )
		{
			killer = FSV_PlayerCombatData( killerPlayer );
		}

		if (victimPlayer != nullptr )
		{
			victim = FSV_PlayerCombatData( victimPlayer );
		}
	}

	UPROPERTY()
	FSV_PlayerCombatData killer;

	UPROPERTY()
	FSV_PlayerCombatData victim;
}
	
USTRUCT()
struct FSV_NewPhaseEvent
{
	UPROPERTY()
	int previousPhase;

	UPROPERTY()
	int newPhase;
}

USTRUCT()
struct FSV_RaidStartEvent
{
	UPROPERTY()
	int attackerTeam = -1;

	UPROPERTY()
	int defenderTeam = -1;
}

USTRUCT()
struct FSV_RaidEndEvent
{
	UPROPERTY()
	int attackerTeam = -1;

	UPROPERTY()
	int defenderTeam = -1;

	UPROPERTY()
	bool attackerWon = false;
}

USTRUCT()
struct FSV_TeamScoreLossEvent
{
	UPROPERTY()
	int team;

	UPROPERTY()
	int oldScore;

	UPROPERTY()
	int newScore;

	UPROPERTY()
	FString lossReason;
}

USTRUCT()
struct FSV_GameEndEvent
{
	UPROPERTY()
	FString mapName;

	UPROPERTY()
	int winningTeam;
}

USTRUCT()
struct FSV_BaseSelectedEvent
{
	UPROPERTY()
	FString mapName;

	UPROPERTY()
	int team;

	UPROPERTY()
	FString baseIndex;
}

UCLASS()
class USV_Analytics : UNCGameplaySystem_Server
{
	UFUNCTION(BlueprintOverride)
	void Initialize()
	{
		// Don't register for callbacks if disabled
		if( !IsAnalyticsEnabled() )
			return;

		// Player
		ServerCallbacks().OnPlayerFirstJoined.AddUFunction( this, n"OnPlayerFirstJoined" );
		ServerCallbacks().OnPlayerReconnected.AddUFunction( this, n"OnPlayerReconnected" );
		ServerCallbacks().OnPlayerDisconnected.AddUFunction( this, n"OnPlayerDisconnected" );
		
		// Mode
		ServerCallbacks().OnGamePhaseChanged.AddUFunction( this, n"OnGamePhaseChanged" );
		ScriptCallbacks().server_OnBreacherPlanted.AddUFunction( this, n"OnBreacherPlanted" );
		ScriptCallbacks().server_onRaidAttackerWin.AddUFunction( this, n"OnRaidEnd_AttackerWin");
		ScriptCallbacks().server_onRaidDefenderWin.AddUFunction( this, n"OnRaidEnd_DefenderWin");

		Print("[DebugAnalytic]  Analytics system initialized");
	}

	UFUNCTION()
	private void OnPlayerFirstJoined( ANCPlayerCharacter player )
	{
		if( !IsValid( player ) )
			return;

		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( player );
		asPlayer.Server_OnPawnDeath.AddUFunction( this, n"OnPlayerDeath" );

		// Old analytics system event
		FNCAnalyticsEvent logEvent;
		logEvent.EventType = "player_join";
		_AddPlayerData( logEvent, player );

		UNCAnalytics::RecordEvent(logEvent);

		// New analytics system event
		FSV_PlayerData playerData = FSV_PlayerData( player );
		UNCAnalytics::RecordPlayerEvent( "playerJoin", player.NCOnlineId, playerData );
	}

	UFUNCTION()
	private void OnPlayerReconnected( ANCPlayerCharacter player )
	{
		if( !IsValid( player ) )
			return;

		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( player );
		asPlayer.Server_OnPawnDeath.AddUFunction( this, n"OnPlayerDeath" );

		// Old analytics system event
		FNCAnalyticsEvent logEvent;
		logEvent.EventType = "player_reconnected";
		_AddPlayerData( logEvent, player );

		UNCAnalytics::RecordEvent(logEvent);

		// New analytics system event
		FSV_PlayerData playerData = FSV_PlayerData( player );
		UNCAnalytics::RecordPlayerEvent( "playerReconnected", player.NCOnlineId, playerData );
	}

	UFUNCTION()
	private void OnPlayerDisconnected( ANCPlayerCharacter player )
	{
		if( !IsValid( player ) )
			return;

		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( player );
		asPlayer.Server_OnPawnDeath.UnbindObject( this );

		// Old analytics system event		
		FNCAnalyticsEvent logEvent;
		logEvent.EventType = "player_disconnected";
		_AddPlayerData( logEvent, player );

		UNCAnalytics::RecordEvent(logEvent);

		// New analytics system event
		FSV_PlayerData playerData = FSV_PlayerData( player );
		UNCAnalytics::RecordPlayerEvent( "playerDisconnected", player.NCOnlineId, playerData );
	}

	UFUNCTION()
	private void OnPlayerDeath( const FDamageInfo&in damageInfo, ANCPlayerCharacter victim )
	{
		if( !IsValid( victim ) )
			return;

		// Old analytics system event
		FNCAnalyticsEvent logEvent;
		logEvent.EventType = "player_death";
		_AddPlayerCombatData( logEvent, victim, "victim_" );

		ANCPlayerCharacter killer = damageInfo.attacker;
		if( killer != nullptr )
			_AddPlayerCombatData( logEvent, killer, "killer_" );
		
		UNCAnalytics::RecordEvent(logEvent);

		// New analytics system event
		FSV_PlayerDeathEvent deathEvent = FSV_PlayerDeathEvent( killer, victim );
		if ( killer != nullptr )
			UNCAnalytics::RecordPlayerEvent( "playerKill", killer.NCOnlineId, deathEvent );

		if ( victim != nullptr )
			UNCAnalytics::RecordPlayerEvent( "playerDeath", victim.NCOnlineId, deathEvent );
	}

	private void _AddPlayerData( FNCAnalyticsEvent& logEvent, ANCPlayerCharacter playerState, FString prefix = "" )
	{
		logEvent.Properties.Add( prefix + "player_name", playerState.GetPlayerName());
		logEvent.Properties.Add( prefix + "anonymized_name", playerState.GetAnonymizedPlayerName());
		logEvent.Properties.Add( prefix + "online_id", playerState.NCOnlineId);
		logEvent.NumericProperties.Add( prefix + "team", playerState.GetTeam());
	}

	private void _AddPlayerCombatData( FNCAnalyticsEvent& logEvent, ANCPlayerCharacter playerState, FString prefix = "" )
	{
		_AddPlayerData( logEvent, playerState, prefix );

		logEvent.NumericProperties.Add( prefix + "locationX", float32( playerState.GetActorLocation().X ) );
		logEvent.NumericProperties.Add( prefix + "locationY", float32( playerState.GetActorLocation().Y ) );
		logEvent.NumericProperties.Add( prefix + "locationZ", float32( playerState.GetActorLocation().Z ) );

		// Grab active weapon
		FString weaponName = "";
		FString weaponRarityName = "";
		ANCWeapon weapon = playerState.GetActiveWeapon();
		if( IsValid( weapon ) )
		{
			weaponName = weapon.WeaponClass.ToString();
			weaponRarityName = GetWeaponRarity( weapon ).ToString();
		}

		logEvent.Properties.Add( prefix + "weapon", weaponName );
		logEvent.Properties.Add( prefix + "weapon_rarity", weaponRarityName );


		// Grab equipped shield
		FString shieldString = "";
		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( playerState );
		if( asPlayer.IsActorShieldValid() )
			shieldString = asPlayer.GetEquippedShieldItem().itemIndex.GetTagName().GetPlainNameString();

		logEvent.Properties.Add( prefix + "shield", shieldString );

		logEvent.Properties.Add( "class", asPlayer.ClassManager().GetClassData().className.ToString() );
	}

	UFUNCTION( NotBlueprintCallable )
	void OnGamePhaseChanged( int oldState, int newState )
	{
		// Old analytics system event
		FNCAnalyticsEvent logEvent;
		logEvent.EventType = "gamephase";
		logEvent.NumericProperties.Add( "new_phase", newState );
		UNCAnalytics::RecordEvent(logEvent);

		// New analytics system event
		FSV_NewPhaseEvent phaseEvent;
		phaseEvent.previousPhase = oldState;
		phaseEvent.newPhase = newState;
		UNCAnalytics::RecordMatchEvent( "newPhase", phaseEvent );

		if( newState == GamePhase::PREMATCH )
			_LogEvent_GameStart();
		else if( newState == GamePhase::WINNER_DETERMINED )
			_LogEvent_GameEnd();
	}

	UFUNCTION()
	private void OnBreacherPlanted( ANCPlayerCharacter planter, AAS_ShieldBreaker shieldBreaker, AAS_RaidDomeShield dome )
	{
		// Old analytics system event
		FNCAnalyticsEvent logEvent;
		logEvent.EventType = "raid_start";

		if( IsValid( shieldBreaker) )
			logEvent.NumericProperties.Add( "attacker_team", shieldBreaker.GetTeam() );

		if( IsValid( dome) )
			logEvent.NumericProperties.Add( "defender_team", dome.GetTeam() );

		UNCAnalytics::RecordEvent(logEvent);

		// New analytics system event
		FSV_RaidStartEvent event;
		if( IsValid( shieldBreaker ) )
			event.attackerTeam = shieldBreaker.GetTeam();

		if ( IsValid( dome ) )
			event.defenderTeam = dome.GetTeam();
		
		if ( event.attackerTeam >= 0)
			UNCAnalytics::RecordTeamEvent( "raidStart", event.attackerTeam, event );

		if ( event.defenderTeam >= 0)
			UNCAnalytics::RecordTeamEvent( "raidStart", event.defenderTeam, event );
	}

	UFUNCTION()
	private void OnRaidEnd_AttackerWin( int attackerTeam, int defenderTeam )
	{
		_LogEvent_RaidEnd( attackerTeam, defenderTeam, true );
	}

	UFUNCTION()
	private void OnRaidEnd_DefenderWin( int attackerTeam, int defenderTeam )
	{
		_LogEvent_RaidEnd( attackerTeam, defenderTeam, false );
	}

	private void _LogEvent_GameStart()
	{
		// Old analytics system event
		FNCAnalyticsEvent logEvent;
		logEvent.EventType = "game_start";

		logEvent.Properties.Add( "map_id", GetMapName() );

		AAS_GameModeBase gameMode = Cast<AAS_GameModeBase>( Gameplay::GetGameMode() );
		logEvent.Properties.Add( "game_mode", gameMode.GetName().ToString() );
		
		//teams

		UNCAnalytics::RecordEvent(logEvent);

		// New analytics system event
		// N/A - This event will be generated by the backend
	}

	private void _LogEvent_GameEnd()
	{
		// Old analytics system event
		FNCAnalyticsEvent logEvent;
		logEvent.EventType = "game_end";

		logEvent.Properties.Add( "map_id", GetMapName() );		
		logEvent.NumericProperties.Add( "winning_team", GetWinner() );

		UNCAnalytics::RecordEvent(logEvent);

		// New analytics system event
		FSV_GameEndEvent gameEndEvent;
		gameEndEvent.mapName = GetMapName();
		gameEndEvent.winningTeam = GetWinner();

		UNCAnalytics::RecordMatchEvent("gameEnd", gameEndEvent);
	}

	void LogEvent_BaseSelected( int team, FGameplayTag baseIndex )
	{		 
		// Old analytics system event
		FNCAnalyticsEvent logEvent;
		logEvent.EventType = "base_selection";

		logEvent.Properties.Add( "map_id", GetMapName() );
		logEvent.Properties.Add( "base", baseIndex.ToString() );

		logEvent.NumericProperties.Add( "team_id", team );
		
		UNCAnalytics::RecordEvent(logEvent);

		// New analytics system event
		FSV_BaseSelectedEvent baseSelectedEvent;
		baseSelectedEvent.mapName = GetMapName();
		baseSelectedEvent.team = team;
		baseSelectedEvent.baseIndex = baseIndex.ToString();

		UNCAnalytics::RecordTeamEvent( "baseSelection", team, baseSelectedEvent );
	}

	UFUNCTION()
	void LogEvent_TeamScoreLoss( int teamID, int oldScore, int newScore, FName lossReason )
	{
		// Old analytics system event
		FNCAnalyticsEvent logEvent;
		logEvent.EventType = "health_lost";

		logEvent.Properties.Add( "reason", lossReason.ToString() );

		logEvent.NumericProperties.Add( "team_id", teamID );
		logEvent.NumericProperties.Add( "previous_health", oldScore );
		logEvent.NumericProperties.Add( "new_health", newScore );
		logEvent.NumericProperties.Add( "health_lost", oldScore - newScore );

		UNCAnalytics::RecordEvent(logEvent);

		// New analytics system event
		FSV_TeamScoreLossEvent scoreLossEvent;
		scoreLossEvent.team = teamID;
		scoreLossEvent.oldScore = oldScore;
		scoreLossEvent.newScore = newScore;
		scoreLossEvent.lossReason = lossReason.ToString();

		UNCAnalytics::RecordTeamEvent( "healthLost", teamID, scoreLossEvent );
	}

	private void _LogEvent_RaidEnd( int attackerTeam, int defenderTeam, bool attackerWon )
	{
		// Old analytics system event
		FNCAnalyticsEvent logEvent;
		logEvent.EventType = "raid_end";

		logEvent.Properties.Add( "attacker_won", attackerWon ? "t" : "f" ); // What's better 1 character string or float as 0 or 1?

		logEvent.NumericProperties.Add( "attacker_team", attackerTeam );
		logEvent.NumericProperties.Add( "defender_team", defenderTeam );

		UNCAnalytics::RecordEvent(logEvent);

		// New analytics system event
		FSV_RaidEndEvent analyticsEvent;
		analyticsEvent.attackerTeam = attackerTeam;
		analyticsEvent.defenderTeam = defenderTeam;
		analyticsEvent.attackerWon = attackerWon;

		UNCAnalytics::RecordTeamEvent( "raidEnd", attackerTeam, analyticsEvent );
		UNCAnalytics::RecordTeamEvent( "raidEnd", defenderTeam, analyticsEvent );
	}
}
