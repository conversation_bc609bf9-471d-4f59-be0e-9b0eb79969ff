UCLASS( Abstract )
class UAS_SearchFieldWidget : UNC_DisplayWidget
{
	default StateOnConstruction = EDisplayConstructVisibility::Shown;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UEditableTextBox searchBox;

	UPROPERTY( NotVisible, BlueprintHidden, Transient, Meta = ( BindWidgetAnim ) )
	private UWidgetAnimation hoveredAnimation;

	UPROPERTY( NotVisible, BlueprintHidden, Transient, Meta = ( BindWidgetAnim ) )
	private UWidgetAnimation idleAnimation;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden )
	private TSubclassOf<UAS_SearchResultsWidget> searchResultsClass;

	UPROPERTY( EditInstanceOnly, BlueprintHidden )
	private bool autoOpenSearchResults = true;

	private TOptional<FNCPlayerSearchSuccessCallback> optSuccessDelegate;
	private TOptional<FNCPlayerSearchNotFoundCallback> optFailureDelegate;

	UFUNCTION( BlueprintOverride )
	void OnShowStart()
	{
		searchBox.OnTextCommitted.AddUFunction( this, n"OnTextCommitted" );
	}

	UFUNCTION( BlueprintOverride )
	void OnHideEnd()
	{
		searchBox.OnTextCommitted.Unbind( this, n"OnTextCommitted" );
	}

	void InitializeText( FText text )
	{
		searchBox.SetText( text );
	}

	void BindSuccessDelegate( FNCPlayerSearchSuccessCallback successDelegate )
	{
		optSuccessDelegate = successDelegate;
	}

	void BindFailureDelegate( FNCPlayerSearchNotFoundCallback failureDelegate )
	{
		optFailureDelegate = failureDelegate;
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnTextCommitted( const FText&in text, ETextCommit method )
	{
		if ( method == ETextCommit::OnEnter )
		{
			FNCPlayerSearchSuccessCallback onSuccess  = FNCPlayerSearchSuccessCallback( this, n"OnSuccess" );
			FNCPlayerSearchErrorCallback onError	  = FNCPlayerSearchErrorCallback( this, n"OnError" );
			FNCPlayerSearchNotFoundCallback onFailure = FNCPlayerSearchNotFoundCallback( this, n"OnFailure" );
			NCOnlineFriends::FindPlayerWithCrossPlatformName( text.ToString(), onSuccess, onFailure, onError );
		}
		else if ( method == ETextCommit::OnCleared && IsValid( Parent ) )
		{
			// When the search is cleared with esc, we can just give focus back to the parent so this doesn't hog it
			Parent.SetFocus();
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnSuccess( FNCOnlinePlayerCard playerCard )
	{
		if ( autoOpenSearchResults )
		{
			UAS_SearchResultsWidget searchResults = Cast<UAS_SearchResultsWidget>( OpenMenu( n"search_results", false, false, true ) );
			if ( IsValid( searchResults ) )
			{
				searchResults.ShowValidResult( playerCard );
				searchBox.SetText( Text::EmptyText );
			}
		}
		else if ( optSuccessDelegate.IsSet() )
		{
			optSuccessDelegate.Value.ExecuteIfBound( playerCard );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnError( FString searchString, const FGameplayTag&in errorMessage )
	{
		Print( f"Unable to find {searchString}, {errorMessage}" );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnFailure( FString searchString )
	{
		if ( autoOpenSearchResults )
		{
			UAS_SearchResultsWidget searchResults = Cast<UAS_SearchResultsWidget>( OpenMenu( n"search_results", false, false, true ) );
			if ( IsValid( searchResults ) )
			{
				searchResults.ShowInvalidResult( searchString );
				searchBox.SetText( Text::EmptyText );
			}
		}
		else if ( optFailureDelegate.IsSet() )
		{
			optFailureDelegate.Value.ExecuteIfBound( searchString );
		}
	}

	UFUNCTION( BlueprintOverride )
	void OnMouseEnter( FGeometry MyGeometry, FPointerEvent MouseEvent )
	{
		PlayAnimationForward( hoveredAnimation );
	}

	UFUNCTION( BlueprintOverride )
	void OnMouseLeave( FPointerEvent MouseEvent )
	{
		StopAnimation( hoveredAnimation );
		PlayAnimationForward( idleAnimation );
	}
}