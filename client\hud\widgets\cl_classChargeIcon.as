UCLASS( Abstract )
class UAS_ClassChargeIconGroup : UUserWidgetDefault
{
	AAS_PlayerEntity ownerPlayer;

	int chargeIndex = 0;

	TArray<UAS_ClassChargeIcon> icons;

	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<UAS_ClassChargeIcon> iconClass;

	UPROPERTY( BindWidget, NotVisible )
	UHorizontalBox iconContainer;

	UAS_ClassChargeIcon currentIcon;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		ownerPlayer = Cast<AAS_PlayerEntity>( Widget_GetPawnOwner( this ) );
		ScriptCallbacks().client_onPlayerChangedClass.AddUFunction( this, n"OnPlayerChangedClass" );
		ownerPlayer.classManager.net_characterSpecificCooldownCharges.OnReplicated().AddUFunction( this, n"OnChargeCountChanged" );
		OnPlayerChangedClass( ownerPlayer, GameplayTags::Classes_Class, ownerPlayer.classManager.GetClassIndex() );
	}

	UFUNCTION()
	private void OnPlayerChangedClass( ANCPlayerCharacter player, FGameplayTag oldValue,
							   FGameplayTag newValue )
	{
		if ( player != ownerPlayer )
			return;

		FClassDataStruct classData = Classes().GetClassData( newValue );
		SetVisibility( classData.classSpecificChargesMax == 0 ? ESlateVisibility::Collapsed : ESlateVisibility::HitTestInvisible );

		for ( int i = 0; i < classData.classSpecificChargesMax; i++ )
		{
			UAS_ClassChargeIcon icon;
			if ( icons.Num() <= i )
			{
				icon = Cast<UAS_ClassChargeIcon>( WidgetBlueprint::CreateWidget( iconClass, GetOwningPlayer() ) );
				iconContainer.AddChild( icon );
				icons.Add( icon );
				UHorizontalBoxSlot slot = Cast<UHorizontalBoxSlot>(icon.Slot);
				slot.SetPadding( FMargin( 5, 10 ) );
			}
			else
				icon = icons[i];

			icon.chargeIndex = i;
			icon.maxDuration = classData.classSpecificChargesCooldownDuration;
			SetImageBrushIcon( icon.icon, classData.classSpecificChargeIcon );
		}

		for ( int i = icons.Num() - 1; i >= classData.classSpecificChargesMax; i-- )
		{
			icons[i].RemoveFromParent();
			icons.RemoveAt( i );
		}

		OnChargeCountChanged( -1, ownerPlayer.classManager.net_characterSpecificCooldownCharges );
	}

	UFUNCTION()
	private void OnChargeCountChanged( int oldValue, int newValue )
	{
		for ( int i = 0; i < icons.Num(); i++ )
		{
			if ( i < newValue )
				icons[i].SetFill( 1 );
			else if ( i > newValue )
				icons[i].SetFill( 0 );
			else
				currentIcon = icons[i];
		}
	}

	UFUNCTION( BlueprintOverride )
	void Tick( FGeometry MyGeometry, float InDeltaTime )
	{
		if ( IsValid( currentIcon ) )
			currentIcon.SetFill( currentIcon.GetFrac() );
	}
}

UCLASS( Abstract )
class UAS_ClassChargeIcon : UUserWidgetDefault
{
	UPROPERTY( NotVisible, BindWidget )
	UImage radial;

	UPROPERTY( NotVisible, BindWidget )
	UImage icon;

	UPROPERTY( Transient, NotVisible, Meta=(BindWidgetAnim) )
	UWidgetAnimation pulse;

	float32 maxDuration;
	AAS_PlayerEntity ownerPlayer;

	int chargeIndex = 0;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		ownerPlayer = Cast<AAS_PlayerEntity>( Widget_GetPawnOwner( this ) );
	}

	float prevFrac = 1;

	void SetFill( float frac )
	{
		if ( frac >= 1 && prevFrac < 1 )
		{
			PlayAnimation(pulse);
		}

		prevFrac = frac;

		icon.SetColorAndOpacity( frac < 1 ? FLinearColor::Gray : FLinearColor::White );
		radial.GetDynamicMaterial().SetScalarParameterValue( n"fill", frac );
	}

	float GetFrac()
	{
		if ( ownerPlayer.classManager.net_characterSpecificCooldownCharges < chargeIndex )
			return 0;

		if ( ownerPlayer.classManager.net_characterSpecificCooldownCharges == chargeIndex )
		{
			int endTime		= ownerPlayer.classManager.net_characterSpecificCooldownEndTime;
			int startTime	= endTime - TO_MILLISECONDS( maxDuration );
			int currentTime = ownerPlayer.GetTimeMilliseconds();
			return maxDuration == 0 ? 1.0f : Math::GetMappedRangeValueClamped( FVector2D( startTime, endTime ), FVector2D( 0, 1 ), currentTime );
		}

		return 1;
	}
}