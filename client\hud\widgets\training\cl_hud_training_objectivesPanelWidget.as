UCLASS( Abstract )
class UAS_Training_ObjectivesPanelWidget : UNCUserWidget
{
	UPROPERTY( NotVisible, BindWidget )
	UCommonListView objectivesListView;

	UPROPERTY( NotVisible, BindWidget )
	UCommonTextBlock titleText;
	
	UPROPERTY( NotVisible, BindWidget )
	URichTextBlock descriptionText;
	
	UPROPERTY( NotVisible, Transient, meta=(BindWidgetAnim ) )
	UWidgetAnimation anim_show;
	
	UPROPERTY( NotVisible, Transient, meta=(BindWidgetAnim ) )
	UWidgetAnimation anim_objective_complete;

	UFUNCTION(BlueprintOverride)
	void Construct()
	{
	}

	void SetTrainingObjectiveGroup( AAS_TrainingObjectiveGroup group )
	{
		if ( !IsValid( group ) )
		{
			ClearObjectiveGroup( ETrainingObjectiveEndContext::INCOMPLETE );
			return;
		}

		if ( group.settings.objectiveSetName.IsNone() )
		{
			titleText.SetText( group.settings.titleText );
		}
		else if ( !group.settings.titleTextLocKey.IsEmpty() )
		{
			UAS_ObjectiveSystem objectiveSys = Objectives();
			if ( IsValid( objectiveSys ) )
			{
				int numObjectivesInSet = objectiveSys.GetTotalObjectivesInSet( group.settings.objectiveSetName );
				if ( numObjectivesInSet > 0 )
				{
					titleText.SetText( GetLocalizedText( Localization::Training, group.settings.titleTextLocKey, FFormatArgumentValue( numObjectivesInSet ), FFormatArgumentValue( group.settings.objectiveSetNumber ) ) );
				}
			}
		}
		
		descriptionText.SetText( group.settings.descriptionText );

		SetWidgetVisibilitySafe( titleText, !group.settings.titleText.IsEmpty() ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
		SetWidgetVisibilitySafe( descriptionText, !group.settings.descriptionText.IsEmpty() ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );

		TArray<UAS_TrainingObjectiveListObject> objectiveListObjects;
		for( UAS_TrainingObjective objective : group.objectives )
		{
			if ( objective.hiddenObjective )
			{
				continue;
			}
			
			UAS_TrainingObjectiveListObject listObject = Cast<UAS_TrainingObjectiveListObject>( NewObject( this, UAS_TrainingObjectiveListObject::StaticClass() ) );
			listObject.objective = objective;
			objectiveListObjects.Add( listObject );
		}
		
		objectivesListView.SetListItems( objectiveListObjects );

		//StopAllAnimations();
		StopAnimation( anim_objective_complete );
		PlayAnimationForward( anim_show );
	}

	void ClearObjectiveGroup( ETrainingObjectiveEndContext reason )
	{
		StopAnimation( anim_show );
		PlayAnimationForward( anim_objective_complete, 2 );

		//switch( reason )
		//{
		//	case ETrainingObjectiveEndReason::INCOMPLETE:
		//	{
		//		break;
		//	}
		//	case ETrainingObjectiveEndReason::COMPLETED:
		//	{
		//		break;
		//	}
		//}
	}
}