UCLASS( Abstract )
class UAS_PinnableWidget_ObjectiveMarker : UAS_InteractiveWidget
{
	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	UAS_ObjectiveMarker objectiveMarker;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	UAS_ObjectiveMarker offscreenObjectiveMarker;

	void SetOwningObjective( AActor objective )
	{
		objectiveMarker.SetOwningObjective( objective );
		offscreenObjectiveMarker.SetOwningObjective( objective );
	}
}