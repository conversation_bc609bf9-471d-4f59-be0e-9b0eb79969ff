UCLASS( Abstract )
class UAS_HealItemProgressWidget : UUserWidgetDefault
{
	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidget ) )
	UImage healProgressCircle;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidget ) )
	UImage healProgressCircleShadow;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidget ) )
	UImage itemIcon;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidget ) )
	UImage itemIconShadow;

	UPROPERTY( BlueprintReadOnly )
	FLinearColor shieldItemColor;

	UPROPERTY( BlueprintReadOnly )
	FLinearColor healthItemColor;

	FNCCoroutineSignal fakeTickEnd;
	float endTime;
	float startTime;

	UFUNCTION(BlueprintOverride)
	void Construct()
	{
		ScriptCallbacks().RegisterSignalCallback( Signals::ON_HEAL_STARTED_CLIENT, this, n"OnHealStarted" );
		ScriptCallbacks().RegisterSignalCallback( Signals::ON_HEAL_ENDED_CLIENT, this, n"OnHealEnded" );
	}

	UFUNCTION()
	private void OnHealStarted( FName signalName, UObject sender )
	{
		SetActive();

		const UAS_HealFromItemComponent callerComp = Cast<UAS_HealFromItemComponent>( sender );
		if ( !IsValid( callerComp ) )
		{
			SetInactive();
			return;
		}

		TOptional< FHealItemStruct > optCurHealItem = callerComp.GetSelectedHealItem();
		if ( !optCurHealItem.IsSet() )
		{
			SetInactive();
			return;
		}

		const FHealItemStruct& healItem = optCurHealItem.GetValue();
		const FLootDataStruct& healItemData = GetLootDataByIndex( healItem.tag );
		SetIcon( healItemData.icon, healItem.shieldHeal );
		Thread( this, n"FakeTick", callerComp.GetActiveHealWeapon() );
	}
	
	UFUNCTION()
	private void OnHealEnded( FName signalName, UObject sender )
	{
		SetInactive();
	}

	void SetIcon( UObject icon, bool isShieldItem )
	{
		FSlateBrush newBrush	= GetNewBrushThatIsCopyOf( itemIcon.GetBrush() );
		newBrush.ResourceObject = icon;
		itemIcon.SetBrush( newBrush );

		FLinearColor colorToUse = isShieldItem ? shieldItemColor : healthItemColor;

		newBrush				= GetNewBrushThatIsCopyOf( itemIconShadow.GetBrush() );
		newBrush.ResourceObject = icon;
		newBrush.TintColor		= colorToUse;
		itemIconShadow.SetBrush( newBrush );

		newBrush		   = GetNewBrushThatIsCopyOf( healProgressCircleShadow.GetBrush() );
		newBrush.TintColor = colorToUse;
		healProgressCircleShadow.SetBrush( newBrush );
	}

	UFUNCTION()
	void FakeTick( UNCCoroutine co, ANCWeapon activeHealWeapon )
	{
		if ( !IsValid( activeHealWeapon ) )
		{
			return;
		}

		fakeTickEnd.Emit();
		co.EndOn( this, fakeTickEnd );
		co.EndOnDestroyed( activeHealWeapon );

		while ( true )
		{
			float progress	  = activeHealWeapon.GetChargeFraction();

			healProgressCircle.GetDynamicMaterial().SetScalarParameterValue( n"Fill", progress );
			healProgressCircleShadow.GetDynamicMaterial().SetScalarParameterValue( n"Fill", progress );

			if ( progress >= 1 )
				return;

			co.Wait( 0.01 );
		}
	}

	private void SetActive()
	{
		SetWidgetVisibilitySafe(this, ESlateVisibility::HitTestInvisible);
	}

	private void SetInactive()
	{
		SetWidgetVisibilitySafe(this, ESlateVisibility::Collapsed);
		fakeTickEnd.Emit();
	}

	UFUNCTION(BlueprintOverride)
	void Destruct()
	{
		ScriptCallbacks().UnRegisterSignalCallback( Signals::ON_HEAL_STARTED_CLIENT, this, n"OnHealStarted" );
		ScriptCallbacks().UnRegisterSignalCallback( Signals::ON_HEAL_ENDED_CLIENT, this, n"OnHealEnded" );
	}
}