UCLASS( Abstract )
class UAS_AbilityStatusWidget : UUserWidgetDefault
{
	UPROPERTY( EditAnywhere )
	FName inputAction;

	UPROPERTY( EditAnywhere )
	FName inputActionKBM;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UImage abilityIcon;
	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UImage abilityIconShadow;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UHorizontalBox progressContainer;
	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UAS_AbilityStatus_ProgressBar progressBar;

	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidgetOptional ) )
	UOverlay activeOverlay;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidgetOptional ) )
	UImage activeTimeRemaining;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidgetOptional ) )
	UTextBlock activeChargesRemaining;

	TSubclassOf<UAS_AbilityStatus_ProgressBar> barClass;
	TArray<UAS_AbilityStatus_ProgressBar> bars;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UAS_KeybindWidget keybind;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnimOptional ), Transient )
	UWidgetAnimation chargeGained;
	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetOptional ) )
	UImage chargeIcon;

	FNCCoroutineSignal cooldownResetSignal;
	FTimerHandle cooldownTimer;
	int cooldownStartTime;
	int cooldownEndTime;
	int barToFill = 0;

	FNCCoroutineSignal activeTimeResetSignal;
	FTimerHandle activeTimeTimer;
	int activeTimeStartTime;
	int activeTimeEndTime;

	UFUNCTION( BlueprintOverride )
	void PreConstruct( bool IsDesignTime )
	{
		keybind.SetInputAction( inputAction );
		keybind.SetInputActionKBM( inputActionKBM );
	}

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		barClass = progressBar.Class;
		bars.Add( progressBar );
		progressBar = nullptr;

		if ( IsValid( activeOverlay ) )
			SetWidgetVisibilitySafe( activeOverlay, ESlateVisibility::Hidden );
		if ( IsValid( activeChargesRemaining ) )
			SetWidgetVisibilitySafe( activeChargesRemaining, ESlateVisibility::Hidden );
	}

	int lastClipAmmo = 0;

	void SetBarCountFromWeapon( ANCWeapon weapon )
	{
		if ( !IsValid( weapon ) )
		{
			lastClipAmmo = 0;
			SetBarFillCount( 0 );
			return;
		}

		int count = weapon.GetClipAmmoMax();

		if ( count != bars.Num() )
			__CreateSegmentedBar( count );

		SetBarFillCount( weapon.GetClipAmmo() );
		if ( IsValid( chargeGained ) && ( weapon.GetClipAmmo() > lastClipAmmo ) )
			PlayChargeGained( nullptr );
		lastClipAmmo = weapon.GetClipAmmo();
	}

	UFUNCTION()
	void PlayChargeGained( UTexture2D chargeTex )
	{
		PlayAnimation( chargeGained );
	}

	private void __CreateSegmentedBar( int count )
	{
		for ( UAS_AbilityStatus_ProgressBar bar : bars )
		{
			bar.RemoveFromParent();
		}
		bars.Empty();

		for ( int i = 0; i < count; i++ )
		{
			UAS_AbilityStatus_ProgressBar bar = Cast<UAS_AbilityStatus_ProgressBar>( WidgetBlueprint::CreateWidget( barClass, GetOwningPlayer() ) );
			progressContainer.AddChild( bar );
			UHorizontalBoxSlot slot = Cast<UHorizontalBoxSlot>( bar.Slot );
			FSlateChildSize size;
			size.SizeRule = ESlateSizeRule::Fill;
			size.Value	  = 1.0;
			slot.SetSize( size );
			bars.Add( bar );
		}
	}

	void SetBarCountForHealth( float32 curHealth, float32 maxHealth )
	{
		const float32 HEALTH_PER_BAR = 25;
		int count					 = Math::CeilToInt( maxHealth / HEALTH_PER_BAR );

		if ( count != bars.Num() )
			__CreateSegmentedBar( count );

		float32 realHealthPerBar = ( maxHealth / count );
		int fullBars			 = Math::FloorToInt( curHealth / realHealthPerBar );

		SetBarFillCount( fullBars );
		SetIsActive( curHealth > 0 ); // SetBarFillCount will set the icon to inactive on health below 1 bar
		if ( fullBars < count )
		{
			float32 remaining = curHealth - ( fullBars * realHealthPerBar );
			float32 minPixel  = curHealth > 0 ? 2.0f : 0.0f;
			float32 percent	  = Math::Min( minPixel, remaining / realHealthPerBar );
			int index		  = fullBars;
			bars[index].SetPercent( percent );
		}
	}

	private void SetBarFillCount( int count )
	{
		barToFill = -1;
		for ( int i = 0; i < bars.Num(); i++ )
		{
			if ( i < count )
				bars[i].SetPercent( 1 );
			else if ( i == count )
			{
				bars[i].SetPercent( 0 );
				barToFill = i;
			}
			else
				bars[i].SetPercent( 0 );
		}

		SetIsActive( count > 0 );
	}

	void SetCooldown( int startTime, int endTime )
	{
		cooldownStartTime = startTime;
		cooldownEndTime	  = endTime;
		if ( cooldownEndTime == 0 || cooldownEndTime <= cooldownStartTime )
		{
			SetIsActive( true );
		}
		else
		{
			Thread( this, n"CooldownThread" );
		}
	}

	UFUNCTION()
	void CooldownThread( UNCCoroutine co )
	{
		cooldownResetSignal.Emit();
		co.EndOn( this, cooldownResetSignal );

		float32 duration = TO_SECONDS( cooldownEndTime - cooldownStartTime );
		while ( GetTimeMilliseconds() < cooldownEndTime )
		{
			float32 frac = TO_SECONDS( cooldownEndTime - GetTimeMilliseconds() ) / duration;
			SetProgressFrac( 1.0 - frac );
			co.Wait( 0.1 );
		}
	}

	void SetActiveTime( int startTime, int endTime )
	{
		if ( startTime == endTime )
		{
			activeTimeResetSignal.Emit();
			if ( IsValid( activeOverlay ) )
				SetWidgetVisibilitySafe( activeOverlay, ESlateVisibility::Hidden );
			return;
		}

		if ( IsValid( activeOverlay ) )
			SetWidgetVisibilitySafe( activeOverlay, ESlateVisibility::HitTestInvisible );

		activeTimeStartTime = startTime;
		activeTimeEndTime	= endTime;
		Thread( this, n"ActiveAbilityThread" );
	}

	void SetActiveCharges( int num )
	{
		if ( !IsValid( activeChargesRemaining ) )
			return;

		if ( num <= 0 )
			SetWidgetVisibilitySafe( activeChargesRemaining, ESlateVisibility::Hidden );
		else
		{
			activeChargesRemaining.SetText( GetLocalizedText( Localization::Utilities, "standard_multiplier", FFormatArgumentValue( num ) ) );
			SetWidgetVisibilitySafe( activeChargesRemaining, ESlateVisibility::HitTestInvisible );
		}
	}

	UFUNCTION()
	void ActiveAbilityThread( UNCCoroutine co )
	{
		activeTimeResetSignal.Emit();
		co.EndOn( this, activeTimeResetSignal );

		float32 duration = TO_SECONDS( activeTimeEndTime - activeTimeStartTime );
		if ( duration == 0 )
			duration = 0.01;
		while ( GetTimeMilliseconds() < activeTimeEndTime )
		{
			float32 frac = TO_SECONDS( activeTimeEndTime - GetTimeMilliseconds() ) / duration;
			if ( IsValid( activeTimeRemaining ) )
				activeTimeRemaining.GetDynamicMaterial().SetScalarParameterValue( n"Fill", frac );
			co.Wait( 0.1 );
		}

		if ( IsValid( activeOverlay ) )
			SetWidgetVisibilitySafe( activeOverlay, ESlateVisibility::Hidden );
	}

	void OnActiveAbilityThreadEnd( FNCCoroutineEndParams endParams )
	{
		if ( IsValid( activeOverlay ) )
			SetWidgetVisibilitySafe( activeOverlay, ESlateVisibility::Hidden );
	}

	UFUNCTION()
	void SetInputAction( FName input )
	{
		keybind.SetInputAction( input );
	}

	UFUNCTION()
	void SetInputActionKBM( FName input )
	{
		keybind.SetInputActionKBM( input );
	}

	UFUNCTION()
	void SetAbility( ANCWeapon weapon )
	{
		activeTimeResetSignal.Emit();

		if ( weapon == nullptr )
		{
			SetImageBrushIcon( abilityIcon, nullptr );
			SetImageBrushIcon( abilityIconShadow, nullptr );
			return;
		}

		FWeaponData data = weapon.GetWeaponData();

		UTexture2D icon = data.Icon;
		SetImageBrushIcon( abilityIcon, icon );
		SetImageBrushIcon( abilityIconShadow, icon );

		if ( IsValid( activeChargesRemaining ) )
			SetWidgetVisibilitySafe( activeChargesRemaining, ESlateVisibility::Hidden );
		if ( IsValid( activeOverlay ) )
			SetWidgetVisibilitySafe( activeOverlay, ESlateVisibility::Hidden );
	}

	UFUNCTION()
	void SetIsActive( bool isActive )
	{
		if ( isActive )
		{
			SetWidgetVisibilitySafe( abilityIcon, ESlateVisibility::HitTestInvisible );
		}
		else
		{
			SetWidgetVisibilitySafe( abilityIcon, ESlateVisibility::Hidden );
		}
	}

	UFUNCTION()
	void SetProgressFrac( float32 manaFrac )
	{
		if ( barToFill >= 0 )
			bars[barToFill].SetPercent( manaFrac );
	}
}

UCLASS( Abstract )
class UAS_AbilityStatus_ProgressBar : UUserWidgetDefault
{
	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UProgressBar progressBar;

	void SetPercent( float32 frac )
	{
		progressBar.SetPercent( frac );
	}
}