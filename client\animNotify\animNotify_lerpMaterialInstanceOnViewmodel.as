UCLASS()
class UAnimNotify_LerpMaterialInstanceOnViewmodelArms : UAnimNotify_LerpMaterialInstance
{
	default animNotifyTrackerClass = UAS_AnimNotifyTracker_LerpMaterialInstanceOnViewmodelArms::StaticClass();
}

UCLASS()
class UAS_AnimNotifyTracker_LerpMaterialInstanceOnViewmodelArms : UAS_AnimNotifyTrackerBase
{
	void OnNotify( USkeletalMeshComponent meshComp, UAnimSequenceBase animation,
				   const UAS_ScriptAnimNotifyBase animNotify ) override
	{
		Super::OnNotify( meshComp, animation, animNotify );

		AAS_PlayerEntity myPlayer = Cast<AAS_PlayerEntity>( meshComp.GetOwner() );
		if ( !IsValid( myPlayer ) )
			return;

		UAS_LerpMaterialInstance_Thread thread = Cast<UAS_LerpMaterialInstance_Thread>( CreateThread( UAS_LerpMaterialInstance_Thread::StaticClass(), myPlayer.GetFirstPersonArmsComponent() ) );
		thread.Init( Cast<UAnimNotify_LerpMaterialInstance>( animNotify ), myPlayer.GetFirstPersonArmsComponent() );
	}
}