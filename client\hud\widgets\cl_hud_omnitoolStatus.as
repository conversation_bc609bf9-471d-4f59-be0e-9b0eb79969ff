UCLASS(Abstract)
class UAS_OmniToolWidget : UUserWidgetDefault
{
	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
	UImage AbilityIconEnabled;
	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
	UImage AbilityIconEnabledShadow;
		
	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
    UAS_KeybindWidget WBP_Keybind;

	FName Key;

	UFUNCTION(BlueprintOverride)
	void Tick(FGeometry MyGeometry, float InDeltaTime)
	{
		AAS_PlayerEntity player = GetPlayer();
		if ( !IsValid(player))
		{
			return;
		}	

		bool hasBonus = false;
		{
			ANCMeleeWeapon meleeWeapon = Cast<ANCMeleeWeapon>(player.GetMeleeWeapon());
			if(IsValid(meleeWeapon))
			{
				hasBonus = meleeWeapon.GetSkillCheckState() == ESkillCheckState::BONUS;
			}
		}
		SetBonusStatus(hasBonus);				
	}

	AAS_PlayerEntity GetPlayer()
	{
		ANCPlayerController Controller = Cast<ANCPlayerController>( GetOwningPlayer() );
		if ( !IsValid( Controller ) )
            return nullptr;
        
		ANCPlayerCharacter PlayerPawn =	Controller.GetNCPawn();
		if ( !IsValid( PlayerPawn ) )
            return nullptr;

		AAS_PlayerEntity ASPawn = Cast<AAS_PlayerEntity>(PlayerPawn);
		return ASPawn;
	}

	UFUNCTION()
	void SetInputAction( FName InputAction )
	{
		WBP_Keybind.SetInputAction( InputAction );
	}

	UFUNCTION()
	void SetTool( FName WeaponClass )
	{
		Key = WeaponClass;
		FLootDataStruct weaponData = GetLootDataForWeapon( MakeWeaponId( GetWeaponAsset( WeaponClass ), 0 ) );

		SetImageBrushIcon( AbilityIconEnabled, weaponData.icon );
		SetImageBrushIcon( AbilityIconEnabledShadow, weaponData.icon );
		SetBonusAxeVisibility( false );
		SetBonusHammerVisibility( false );
	}

	UFUNCTION(BlueprintEvent)
	void SetBonusAxeVisibility( bool value ) {}

	UFUNCTION(BlueprintEvent)
	void SetBonusHammerVisibility( bool value ) {}

	void SetBonusStatus(bool value)
	{
		SetBonusAxeVisibility(value);
	}
}