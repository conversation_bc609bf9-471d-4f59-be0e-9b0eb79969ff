UCLASS( Abstract )
class UAS_MissionObjectivePanelWidget : UNCUserWidget
{		
	UPROPERTY( NotVisible, BindWidget )
	private UCommonListView objectivesListView;

	UPROPERTY( NotVisible, BindWidget )
	private UAS_CommonTextBlock objectiveHintText;
	
	UPROPERTY( NotVisible, Transient, meta=(BindWidgetAnim))
	private UWidgetAnimation animAppear;

	UPROPERTY( NotVisible, Transient, meta=(BindWidgetAnim))
	private UWidgetAnimation animHintAppear;


	ESlateVisibility MAIN_OBJECTIVE_OFF = ESlateVisibility::Collapsed;
	ESlateVisibility MAIN_OBJECTIVE_ON 	= ESlateVisibility::SelfHitTestInvisible;
	bool objectiveHintOn;
	

	UFUNCTION(BlueprintOverride)
	void Construct()
	{		
		SetWidgetVisibilitySafe(this, MAIN_OBJECTIVE_OFF );
		ObjectiveHintInit();

	//	System::SetTimer( this, n"FakeTick", 0.1, true );
	}

	UFUNCTION(BlueprintOverride)
	void Destruct()
	{
		TArray<UObject> items = objectivesListView.GetListItems();
		for ( UObject data : items )
		{
			UAS_MissionObjectiveListObject listItem = Cast<UAS_MissionObjectiveListObject>(data);
			if ( IsValid(listItem) )
				OnDeleteListEntry( listItem);
		}
	}

	UAS_MissionObjectiveListObject test;
	UAS_MissionObjectiveListObject test2;

///////////////////////////////////////////////////////////////////////////////
//	FAST DEV TESTING

	UFUNCTION()
	void FakeTick()
	{		
	//	UpdateObjectivePanel();
	//	objectivesListView.ClearListItems();
	//	AddObjectiveHint( FText::FromString( "lets try an objective hint that is relatively long. YOu can shoot this and this and this and one more") );
	//	ClearObjectiveHint();
	//	if ( !IsValid(test))
	//		test = AddProgressbarObjective( FText::FromString( "lets try an objective"), true );
	//	if ( !IsValid(test2))
	//		test2 = AddCheckboxObjective( FText::FromString( "objective 2 goes here boii!!"), false );
	//	test.SetObjectiveCompletedAndInactive();
	//	test2.SetActive();
	//	if ( !test2.GetIsActive() )
	//		test2.SetActive();

	//	if ( IsValid(test))
	//		test.UpdateObjectiveProgressSilent(1000);
	
	//	SetAllObjectivesCompletedAndRemove();
	//	test = nullptr;
	//	test2 = nullptr;
	}


/****************************************************************\

██   ██ ██ ███    ██ ████████ 
██   ██ ██ ████   ██    ██    
███████ ██ ██ ██  ██    ██    
██   ██ ██ ██  ██ ██    ██    
██   ██ ██ ██   ████    ██ 
                                                                                                 
\****************************************************************/
	private void ObjectiveHintInit()
	{
		float32 startTime = animHintAppear.GetEndTime();
		PlayAnimation( animHintAppear, startTime, 1, EUMGSequencePlayMode::Reverse, 2 );
		objectiveHintOn = false;
	}

	void AddObjectiveHint( FText text )
	{
		objectiveHintText.SetText( text );

		if ( objectiveHintOn )
			return;
		objectiveHintOn = true;
		
		float32 startTime = GetAnimationCurrentTime(animHintAppear);
		PlayAnimation( animHintAppear, startTime, 1, EUMGSequencePlayMode::Forward, MissionObjectiveAnim::ANIM_PLAYBACK );

		UpdateObjectivePanel();
	}

	void ClearObjectiveHint()
	{
		if ( !objectiveHintOn )
			return;
		objectiveHintOn = false;

		float32 startTime = GetAnimationCurrentTime(animHintAppear);
		PlayAnimation( animHintAppear, startTime, 1, EUMGSequencePlayMode::Reverse, MissionObjectiveAnim::ANIM_PLAYBACK );

		//UpdateObjectivePanel(); -> let this update in the OnAnimationFinished callback
	}

/****************************************************************\

 █████  ██████  ██████  
██   ██ ██   ██ ██   ██ 
███████ ██   ██ ██   ██ 
██   ██ ██   ██ ██   ██ 
██   ██ ██████  ██████ 
                                                                                                 
\****************************************************************/
	UAS_MissionObjectiveListObject AddCheckboxObjective( FText text, bool isActive )
	{
		UAS_MissionObjectiveListObject listObject = Cast<UAS_MissionObjectiveListObject>( NewObject( this, UAS_MissionObjectiveListObject::StaticClass() ) );
		listObject.AddCheckboxObjective(text, isActive );
		
		_AddObjectiveToList( listObject );
		UpdateObjectivePanel();

		return listObject;
	}

	UAS_MissionObjectiveListObject AddCheckboxObjectiveSilent( FText text, bool isActive )
	{
		UAS_MissionObjectiveListObject listObject = Cast<UAS_MissionObjectiveListObject>( NewObject( this, UAS_MissionObjectiveListObject::StaticClass() ) );
		listObject.AddCheckboxObjectiveSilent(text, isActive );
		
		_AddObjectiveToList( listObject );
		UpdateObjectivePanel();
		
		return listObject;
	}

	UAS_MissionObjectiveListObject AddProgressbarObjective( FText text, bool isActive, FText optionalProgressText = FText() )
	{
		UAS_MissionObjectiveListObject listObject = Cast<UAS_MissionObjectiveListObject>( NewObject( this, UAS_MissionObjectiveListObject::StaticClass() ) );
		listObject.AddProgressbarObjective(text, isActive, optionalProgressText );
		
		_AddObjectiveToList( listObject );
		UpdateObjectivePanel();
		
		return listObject;
	}

	UAS_MissionObjectiveListObject AddProgressbarObjectiveSilent( FText text, bool isActive, FText optionalProgressText = FText() )
	{
		UAS_MissionObjectiveListObject listObject = Cast<UAS_MissionObjectiveListObject>( NewObject( this, UAS_MissionObjectiveListObject::StaticClass() ) );
		listObject.AddProgressbarObjectiveSilent(text, isActive, optionalProgressText );
		
		_AddObjectiveToList( listObject );
		
		return listObject;
	}

	private void _AddObjectiveToList( UAS_MissionObjectiveListObject listObject )
	{		
		objectivesListView.AddItem( listObject );

		listObject.OnDeleteListEntry.AddUFunction(this, n"OnDeleteListEntry");
		UpdateObjectivePanel();
	}

/****************************************************************\

██    ██ ██████  ██████   █████  ████████ ███████ 
██    ██ ██   ██ ██   ██ ██   ██    ██    ██      
██    ██ ██████  ██   ██ ███████    ██    █████   
██    ██ ██      ██   ██ ██   ██    ██    ██      
 ██████  ██      ██████  ██   ██    ██    ███████
                                                                                                 
\****************************************************************/
	UFUNCTION()
	private void OnDeleteListEntry(UAS_MissionObjectiveListObject data)
	{
		data.OnDeleteListEntry.Unbind(this, n"OnDeleteListEntry");
		objectivesListView.RemoveItem(data);
				
		UpdateObjectivePanel();
	}
	
	private void UpdateObjectivePanel()
	{
		bool listOn 		= objectivesListView.GetNumItems() > 0;
		bool oldVisible 	= this.GetVisibility() == MAIN_OBJECTIVE_ON;
		bool newVisible 	= objectiveHintOn || listOn;

		if( !oldVisible && newVisible )
		{
			float32 startTime = GetAnimationCurrentTime(animAppear);
			PlayAnimation( animAppear, startTime, 1, EUMGSequencePlayMode::Forward, MissionObjectiveAnim::ANIM_PLAYBACK );
		}
		else if ( oldVisible && !newVisible )
		{
			float32 startTime = GetAnimationCurrentTime(animAppear);
			PlayAnimation( animAppear, startTime, 1, EUMGSequencePlayMode::Reverse, MissionObjectiveAnim::ANIM_PLAYBACK );
		}

		ESlateVisibility mainObjectiveOn = GetCvarBool( "ScriptDebug.TrainingNewObj" ) ? MAIN_OBJECTIVE_ON : ESlateVisibility::Collapsed;
		
		SetWidgetVisibilitySafe(this, newVisible ? mainObjectiveOn : MAIN_OBJECTIVE_OFF );
	}

	UFUNCTION(BlueprintOverride)
	private void OnAnimationFinished(const UWidgetAnimation Animation)
	{
		if ( Animation == animHintAppear )
		{
			UpdateObjectivePanel();
		}
	}

/****************************************************************\

 ██████  ██████  ███    ███ ██████  ██      ███████ ████████ ███████ 
██      ██    ██ ████  ████ ██   ██ ██      ██         ██    ██      
██      ██    ██ ██ ████ ██ ██████  ██      █████      ██    █████   
██      ██    ██ ██  ██  ██ ██      ██      ██         ██    ██      
 ██████  ██████  ██      ██ ██      ███████ ███████    ██    ███████ 
                                                                                                 
\****************************************************************/
	void SetAllObjectivesCompleted()
	{
		TArray<UAS_MissionObjectiveListObject> objectives = GetAllObjectives();
		for ( UAS_MissionObjectiveListObject data : objectives )
			data.SetObjectiveCompleted();
	}

	void SetAllObjectivesCompletedAndInactive()
	{
		TArray<UObject> items = objectivesListView.GetListItems();
		for ( UObject item : items )
		{
			UAS_MissionObjectiveListObject data = Cast<UAS_MissionObjectiveListObject>(item);
			if ( IsValid(data) )
			{
				if ( !data.GetIsCompleted() )
					data.SetObjectiveCompletedAndInactive();
				else
					data.SetInactive();
			}
		}
	}

	void SetAllObjectivesCompletedAndRemove()
	{
		TArray<UObject> items = objectivesListView.GetListItems();
		for ( UObject item : items )
		{
			UAS_MissionObjectiveListObject data = Cast<UAS_MissionObjectiveListObject>(item);
			if ( IsValid(data) )
			{
				if ( !data.GetIsCompleted() )
					data.SetObjectiveCompletedAndRemove();
				else
					data.RemoveObjective();
			}
		}
	}

	void RemoveAllCompletedObjectives()
	{
		TArray<UAS_MissionObjectiveListObject> objectives = GetAllCompletedObjectives();
		for ( UAS_MissionObjectiveListObject data : objectives )
			data.RemoveObjective();
	}

	void RemoveAllUncompletedObjectives()
	{
		TArray<UAS_MissionObjectiveListObject> objectives = GetAllIncompletedObjectives();
		for ( UAS_MissionObjectiveListObject data : objectives )
			data.RemoveObjective();
	}


/****************************************************************\

 ██████  ███████ ████████ 
██       ██         ██    
██   ███ █████      ██    
██    ██ ██         ██    
 ██████  ███████    ██
                                                                                                 
\****************************************************************/
	TArray<UAS_MissionObjectiveListObject> GetAllObjectives()
	{
		TArray<UAS_MissionObjectiveListObject> results;
		TArray<UObject> items = objectivesListView.GetListItems();
		for ( UObject item : items )
		{
			UAS_MissionObjectiveListObject data = Cast<UAS_MissionObjectiveListObject>(item);
			if ( IsValid(data) )
				results.Add(data);
		}
		
		return results;
	}

	TArray<UAS_MissionObjectiveListObject> GetAllActiveObjectives()
	{
		TArray<UAS_MissionObjectiveListObject> results;
		TArray<UObject> items = objectivesListView.GetListItems();
		for ( UObject item : items )
		{
			UAS_MissionObjectiveListObject data = Cast<UAS_MissionObjectiveListObject>(item);
			if ( IsValid(data) && data.GetIsActive() )
				results.Add(data);
		}
		
		return results;
	}

	TArray<UAS_MissionObjectiveListObject> GetAllInActiveObjectives()
	{
		TArray<UAS_MissionObjectiveListObject> results;
		TArray<UObject> items = objectivesListView.GetListItems();
		for ( UObject item : items )
		{
			UAS_MissionObjectiveListObject data = Cast<UAS_MissionObjectiveListObject>(item);
			if ( IsValid(data) && !data.GetIsActive() )
				results.Add(data);
		}
		
		return results;
	}

	TArray<UAS_MissionObjectiveListObject> GetAllCompletedObjectives()
	{
		TArray<UAS_MissionObjectiveListObject> results;
		TArray<UObject> items = objectivesListView.GetListItems();
		for ( UObject item : items )
		{
			UAS_MissionObjectiveListObject data = Cast<UAS_MissionObjectiveListObject>(item);
			if ( IsValid(data) && data.GetIsCompleted() )
				results.Add(data);
		}
		
		return results;
	}

	TArray<UAS_MissionObjectiveListObject> GetAllIncompletedObjectives()
	{
		TArray<UAS_MissionObjectiveListObject> results;
		TArray<UObject> items = objectivesListView.GetListItems();
		for ( UObject item : items )
		{
			UAS_MissionObjectiveListObject data = Cast<UAS_MissionObjectiveListObject>(item);
			if ( IsValid(data) && !data.GetIsCompleted() )
				results.Add(data);
		}
		
		return results;
	}

	FText GetObjectiveHint()
	{
		return objectiveHintText.GetText();
	}
}