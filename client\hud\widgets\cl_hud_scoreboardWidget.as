UCLASS( Abstract )
class UAS_ScoreboardWidget : UNC_DisplayWidget
{
	UPROPERTY( NotVisible, BindWidget )
	UGridPanel gridPanel;

	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<UAS_ScoreboardWidgetTeam> teamWidgetClass;

	UPROPERTY( BlueprintReadOnly )
	TArray<UAS_ScoreboardWidgetTeam> teamWidgets;

	// The interval in seconds with which to update the scoreboard with new players
	UPROPERTY( EditAnywhere )
	float updateTime = 0.5f;

	// The number of team objects to prep initially
	UPROPERTY( EditAnywhere )
	int expectedTeams = 6;

	private FTimerHandle updateTimer;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		// Create Team objects
		// TODO: Should we get this off the game state?
		int numTeams	 = GetNumTeams();
		for ( int i = 0; i < numTeams; i++ )
		{
			AddTeamWidget( i );
		}
	}

	UFUNCTION( BlueprintOverride )
	void OnShowStart()
	{
		UpdateTeams();
		updateTimer = System::SetTimer( this, n"UpdateTeams", updateTime, true );
	}

	UFUNCTION( BlueprintOverride )
	void OnHideStart()
	{
		System::ClearAndInvalidateTimerHandle( updateTimer );
	}

	UFUNCTION( BlueprintEvent )
	void AddTeamWidget( int teamNum )
	{
		UAS_ScoreboardWidgetTeam newWidget = Cast<UAS_ScoreboardWidgetTeam>( WidgetBlueprint::CreateWidget( teamWidgetClass, GetOwningPlayer() ) );
		newWidget.SetTeam( teamNum );
		gridPanel.AddChild( newWidget );
		teamWidgets.Add( newWidget );
	}

	UFUNCTION()
	void UpdateTeams()
	{
		// Update team list
		AAS_GameState gs = GetGameStateEntity();
		int numTeams	 = gs.GetNumTeams();

		for ( int i = 0; i < numTeams; i++ )
		{
			TArray<ANCPlayerCharacter> teamPlayers = GetPlayersOfTeam( i );
			UAS_ScoreboardWidgetTeam teamWidget = teamWidgets[i];
			if ( teamPlayers.Num() > 0 )
			{
				teamWidget.Show();
				teamWidget.UpdatePlayerWidgets();
			}
			else
			{
				teamWidget.Hide();
			}
		}
	}
}

UCLASS( Abstract )
class UAS_ScoreboardWidgetTeam : UNC_DisplayWidget
{
	UPROPERTY()
	private int teamNum;

	UPROPERTY()
	int teamScore;

	UPROPERTY()
	TArray<UAS_ScoreboardWidgetRow> widgets;

	UPROPERTY( NotEditable, BindWidget )
	UVerticalBox playerContainer;

	UPROPERTY()
	bool isFriendly;

	void SetTeam( int team )
	{
		teamNum = team;
	}

	int GetTeam()
	{
		return teamNum;
	}

	void UpdatePlayerWidgets()
	{
		ANCPlayerCharacter localPlayer = Widget_GetPawnOwner(this);
		if (!IsValid(localPlayer))
			return;

		int team			= GetTeam();
		bool teamIsFriendly = IsFriendly( localPlayer.GetTeam(), team );

		for ( int j = playerContainer.ChildrenCount - 1; j >= 0; j-- )
		{
			UAS_ScoreboardWidgetRow row = Cast<UAS_ScoreboardWidgetRow>( playerContainer.GetChildAt( j ) );
			SetWidgetVisibilitySafe(row,  ESlateVisibility::Collapsed );
		}

		teamScore  = GetTeamScore( team );
		isFriendly = teamIsFriendly;

		int currentRow = 0;

		TArray<ANCPlayerCharacter> players = GetPlayersOfTeam( team );
		for ( ANCPlayerCharacter ps : players )
		{
			UAS_ScoreboardWidgetRow w = Cast<UAS_ScoreboardWidgetRow>( playerContainer.GetChildAt( currentRow ) );
			if ( w != nullptr )
			{
				SetWidgetVisibilitySafe(w,  ESlateVisibility::HitTestInvisible );
				w.playerName = ps.GetPlayerNameAsString();

				w.kills		 = 0;
				w.score		 = ps.GetScore();
				w.deaths	 = 0;
				w.isFriendly = teamIsFriendly;

				UVerticalBoxSlot slot	 		 = Cast<UVerticalBoxSlot>( w.Slot );
				slot.HorizontalAlignment = EHorizontalAlignment::HAlign_Fill;
				slot.VerticalAlignment	 = EVerticalAlignment::VAlign_Fill;
			}
			else
			{
				Log( f"ERROR: Attempting to reference more players than there are rows for players to fit in" );
			}

			currentRow++;
		}

		SetTeam( teamNum );
	}
}

UCLASS( Abstract )
class UAS_ScoreboardWidgetRow : UNCUserWidget
{
	UPROPERTY()
	FString playerName;

	UPROPERTY()
	int kills;

	UPROPERTY()
	int score;

	UPROPERTY()
	int deaths;

	UPROPERTY()
	bool isFriendly;
}