UCLASS( Abstract )
class UAS_LocationSplashWidget : UNC_DisplayWidget
{
	default StateOnConstruction = EDisplayConstructVisibility::Hidden;
	default HideVisibility		= ESlateVisibility::Collapsed;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UTextBlock locationSplashText;

	private FText currentLocation;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			scriptCallbacks.localClient_onPlayerEnteredLocation.AddUFunction( this, n"OnPlayerEnteredLocation" );
			scriptCallbacks.localClient_onPlayerExitedLocation.AddUFunction( this, n"OnPlayerExitedLocation" );
			scriptCallbacks.client_OnPlayerEnteredMapSwapTrigger.AddUFunction( this, n"OnPlayerEnteredMapSwapTrigger" );
			scriptCallbacks.client_OnPlayerExitedMapSwapTrigger.AddUFunction( this, n"OnPlayerExitedMapSwapTrigger" );
		}
	}

	UFUNCTION( BlueprintOverride )
	void OnHideStart()
	{
		currentLocation = Text::EmptyText;
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnPlayerEnteredLocation( FText locationName )
	{
		SetLocationName( locationName );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnPlayerExitedLocation( FText locationName )
	{
		// Only hide the widget if the player exited a location without entering another
		if ( !currentLocation.IdenticalTo( locationName ) || !IsShown() )
			return;

		Hide();
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnPlayerEnteredMapSwapTrigger( AAS_PlayerEntity player, AAS_BaseSystem base )
	{
		if ( IsValid( base ) )
		{
			FBaseDataStruct baseData;
			if ( base.GetBaseData( baseData ) )
			{
				SetLocationName( baseData.baseName );
			}
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnPlayerExitedMapSwapTrigger( AAS_PlayerEntity player, AAS_BaseSystem base )
	{
		if ( !IsShown() )
			return;

		Hide();
	}

	private void SetLocationName( FText locationName )
	{
		if ( currentLocation.IdenticalTo( locationName ) )
			return;

		currentLocation = locationName;
		locationSplashText.SetText( locationName );
		Show();
	}
}