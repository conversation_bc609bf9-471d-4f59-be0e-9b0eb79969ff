UCLASS( Abstract )
class UAS_BattlepassMenu : UAS_WarTableMenu
{
	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UListView listView;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_CommonTextBlock pageHeader;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage keyArt;

	const FName PURCHASE_CONFIRMATION_NAME = n"purchase_confirmation_overlay";
	const FName CURRENCY_PURCHASE_NAME	   = n"currency_purchase_overlay";

	UFUNCTION( BlueprintOverride )
	void OnActivated()
	{
		Super::OnActivated();

		UAS_CollectionNavigator collectionNavigator = GetCollectionNavigator();
		if ( IsValid( collectionNavigator ) )
		{
			collectionNavigator.OnDisplayBundle.AddUFunction( this, n"OnDisplayBundle" );
			collectionNavigator.OnInspectBundle.AddUFunction( this, n"OnInspectBundle" );
			collectionNavigator.OnInteractWithItem.AddUFunction( this, n"OnInteractWithItem" );
		}

		UNCUIStoreAPI storeApi = GetStoreAPI();
		if ( IsValid( storeApi ) )
		{
			UNCUIGetStoreHomeAction action = storeApi.GetStoreHome( GameplayTags::MTX_StoreTypes_Battlepass, FGameplayTag() );
			if ( IsValid( action ) )
			{
				action.OnComplete.AddUFunction( this, n"OnStoreRequestComplete" );
				action.ExecuteAsync();
			}
		}

		// We want the plural version of the header
		pageHeader.SetText( GetLocalizedText( Localization::MTX, "category_battlepass", FFormatArgumentValue( 2 ) ) );
	}

	UFUNCTION( BlueprintOverride )
	void OnDeactivated()
	{
		Super::OnDeactivated();

		UAS_CollectionNavigator collectionNavigator = GetCollectionNavigator();
		if ( IsValid( collectionNavigator ) )
		{
			collectionNavigator.OnDisplayBundle.Unbind( this, n"OnDisplayBundle" );
			collectionNavigator.OnInspectBundle.Unbind( this, n"OnInspectBundle" );
			collectionNavigator.OnInteractWithItem.Unbind( this, n"OnInteractWithItem" );
		}
	}

	UFUNCTION( BlueprintOverride )
	void OnClosed()
	{
		Super::OnClosed();

		ChangeContextAndCamera( GameplayTags::Screens_MainMenu_Progression );

		UAS_CursorInteractionManager cursorInteractionManager = GetCursorInteractionManager();
		if ( IsValid( cursorInteractionManager ) )
		{
			cursorInteractionManager.TryToEnableCursorInteraction( true );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnStoreRequestComplete( const FNCUIStoreHome& storeHome )
	{
		if ( storeHome.Sections.Num() == 0 )
			return;

		TArray<UAS_GameplayTagObject> tagObjects;
		tagObjects.Reserve( storeHome.Sections.Num() );

		FNCUIStoreSection storeSection = storeHome.Sections[0];
		for ( int i = 0; i < storeSection.Bundles.Num(); i++ )
		{
			UAS_GameplayTagObject tagObject = Cast<UAS_GameplayTagObject>( NewObject( listView, UAS_GameplayTagObject::StaticClass() ) );
			if ( IsValid( tagObject ) )
			{
				// Create objects for the list view to show all of the battlepass modules
				tagObject.tag = storeSection.Bundles[i].Id;
				tagObjects.Add( tagObject );
			}

			if ( i == 0 )
			{
				UAS_CollectionNavigator collectionNavigator = GetCollectionNavigator();
				if ( IsValid( collectionNavigator ) )
				{
					// When the battlepass menu opens, show the first one
					collectionNavigator.TryToDisplayBundle( storeSection.Bundles[0] );
				}
			}
		}

		listView.SetListItems( tagObjects );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnDisplayBundle( FNCUIStoreBundle bundle )
	{
		keyArt.SetBrushFromSoftTexture( bundle.KeyArt, true );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnInspectBundle( FNCUIStoreBundle bundle )
	{
		UAS_BattlepassInspectMenu menu = Cast<UAS_BattlepassInspectMenu>( OpenMenu( n"battlepass_inspect_menu" ) );
		if ( IsValid( menu ) )
		{
			menu.SetInspectData( bundle );
			ChangeContextAndCamera( GameplayTags::Screens_MainMenu_Progression_Battlepass_Inspect );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnInteractWithItem( UObject item )
	{
		UNCUIMTXItem mtxItem = Cast<UNCUIMTXItem>( item );

		if ( IsValid( mtxItem ) )
		{
			if ( CanAffordBundle( mtxItem.Id ) )
			{
				UAS_PurchaseConfirmationOverlay menu = Cast<UAS_PurchaseConfirmationOverlay>( OpenMenu( PURCHASE_CONFIRMATION_NAME ) );
				if ( IsValid( menu ) )
				{
					UNCUIStoreAPI storeApi = GetStoreAPI();
					if ( IsValid( storeApi ) )
					{
						menu.SetPurchasableItems( storeApi.GetBundleInfo( mtxItem.Id ) );
					}
				}
			}
			else
			{
				OpenMenu( CURRENCY_PURCHASE_NAME );
			}
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnBattlepassPurchased( bool success, FGameplayTag purchasedId )
	{
		if ( !success )
			return;

		// TODO @jmccarty
	}
}