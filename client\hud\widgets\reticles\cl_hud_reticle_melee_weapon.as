enum EMeleeKeybindIcon
{
	DEFAULT,
	ENEMY,
}

enum EMeleeBonusIcon
{
	DEFAULT_FIRE = 0,
	ATTICUS
}

enum EMeleeReticleAnim
{
	FINAL_HIT,
	THROW_RET,
	SKILL_RING,
	BONUS,
	KEYBIND,

	_count
}

UCLASS(Abstract)
class UAS_ReticleWidgetMeleeWeapon : UAS_ReticleWidget
{
	const FLinearColor 	WHITE_COLOR			= GetCommonUiMpcColor( CommonUiColorMpcNames::WHITE );	
	const FLinearColor 	WHITE50_COLOR 		= GetCommonUiMpcColor( CommonUiColorMpcNames::WHITE );
	default WHITE50_COLOR.A 				= 0.5;
	const FLinearColor 	WHITE75_COLOR 		= GetCommonUiMpcColor( CommonUiColorMpcNames::WHITE );
	default WHITE50_COLOR.A 				= 0.75;
	const FLinearColor 	SUCCESS_COLOR 		= GetCommonUiMpcColor( CommonUiColorMpcNames::POSITIVE );
	const FLinearColor 	FAIL_COLOR 			= GetCommonUiMpcColor( CommonUiColorMpcNames::NEGATIVE );
	const FLinearColor 	ENEMY_COLOR 		= GetCommonUiMpcColor( CommonUiColorMpcNames::ENEMY_HIGHLIGHT );
	const FLinearColor 	RED_BONUS_COLOR		= GetCommonUiMpcColor( CommonUiColorMpcNames::ENEMY_HIGHLIGHT );
	const FLinearColor 	CLEAR_COLOR 		= GetCommonUiMpcColor( CommonUiColorMpcNames::WHITE );
	default CLEAR_COLOR.A 					= 0.0;
	
	TMap<EMeleeReticleAnim,UWidgetAnimation> showAnim;
	TMap<EMeleeReticleAnim,UWidgetAnimation> hideAnim;
	TMap<EMeleeReticleAnim,UWidgetAnimation> initShowAnim;
	TMap<EMeleeReticleAnim,UWidgetAnimation> initHideAnim;
	TMap<EMeleeReticleAnim,bool> __IsWidgetVisible;
	
	UPROPERTY(BlueprintReadOnly, Meta=(BindWidgetAnim), Transient)
	protected UWidgetAnimation ShowFinalHitAnim;
	UPROPERTY(BlueprintReadOnly, Meta=(BindWidgetAnim), Transient)
	protected UWidgetAnimation HideFinalHitAnim;
	UPROPERTY(BlueprintReadOnly, Meta=(BindWidgetAnim), Transient)
	protected UWidgetAnimation InitFinalHitShowAnim;
	UPROPERTY(BlueprintReadOnly, Meta=(BindWidgetAnim), Transient)
	protected UWidgetAnimation InitFinalHitHideAnim;

	UPROPERTY(BlueprintReadOnly, Meta=(BindWidgetAnim), Transient)
	protected UWidgetAnimation ShowThrowReticleAnim;
	UPROPERTY(BlueprintReadOnly, Meta=(BindWidgetAnim), Transient)
	protected UWidgetAnimation HideThrowReticleAnim;
	UPROPERTY(BlueprintReadOnly, Meta=(BindWidgetAnim), Transient)
	protected UWidgetAnimation InitThrowReticleShowAnim;
	UPROPERTY(BlueprintReadOnly, Meta=(BindWidgetAnim), Transient)
	protected UWidgetAnimation InitThrowReticleHideAnim;

	UPROPERTY(BlueprintReadOnly, Meta=(BindWidgetAnim), Transient)
	protected UWidgetAnimation ShowSkillRingAnim;
	UPROPERTY(BlueprintReadOnly, Meta=(BindWidgetAnim), Transient)
	protected UWidgetAnimation HideSkillRingAnim;
	UPROPERTY(BlueprintReadOnly, Meta=(BindWidgetAnim), Transient)
	protected UWidgetAnimation InitSkillRingShowAnim;
	UPROPERTY(BlueprintReadOnly, Meta=(BindWidgetAnim), Transient)
	protected UWidgetAnimation InitSkillRingHideAnim;

	UPROPERTY(BlueprintReadOnly, Meta=(BindWidgetAnim), Transient)
	protected UWidgetAnimation ShowBonusAnim;
	UPROPERTY(BlueprintReadOnly, Meta=(BindWidgetAnim), Transient)
	protected UWidgetAnimation HideBonusAnim;
	UPROPERTY(BlueprintReadOnly, Meta=(BindWidgetAnim), Transient)
	protected UWidgetAnimation InitBonusShowAnim;
	UPROPERTY(BlueprintReadOnly, Meta=(BindWidgetAnim), Transient)
	protected UWidgetAnimation InitBonusHideAnim;

	UPROPERTY(BlueprintReadOnly, Meta=(BindWidgetAnim), Transient)
	protected UWidgetAnimation ShowKeybindAnim;
	UPROPERTY(BlueprintReadOnly, Meta=(BindWidgetAnim), Transient)
	protected UWidgetAnimation HideKeybindAnim;
	UPROPERTY(BlueprintReadOnly, Meta=(BindWidgetAnim), Transient)
	protected UWidgetAnimation InitKeybindShowAnim;
	UPROPERTY(BlueprintReadOnly, Meta=(BindWidgetAnim), Transient)
	protected UWidgetAnimation InitKeybindHideAnim;
				    
	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
    UOverlay WidgetSkillRing;
	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
    USpacer SpacerSkillRing;
	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
    UImage skillRing1;
	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
    UImage skillRing2;
	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
    UImage skillRing3;
	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
    UImage skillRing4;

	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
    UImage skillRef1;
	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
    UImage skillRef2;
	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
    UImage skillRef3;
	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
    UImage skillRef4;

	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
	UWidgetSwitcher WidgetBonusSwitcher;
	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
	UImage WidgetBonusFire;
	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
	UImage WidgetBonusAtticus;    

	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
	UOverlay FinalHitOverlayNail;
	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
	UOverlay FinalHitOverlayBacking;
	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
    UImage FinalHit1;
	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
    UImage FinalHit2;
	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
    UImage FinalHit3;
	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
    UImage FinalHit4;
	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
    UImage FinalHit1b;
	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
    UImage FinalHit2b;
	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
    UImage FinalHit3b;
	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
    UImage FinalHit4b;

	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
    UWidgetSwitcher KeyBindGroup;
	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
	UAS_KeybindWidget WBP_Keybind;

	UPROPERTY(EditDefaultsOnly)
	UNCAudioAsset PerfectHitStage1SFX;
	UPROPERTY(EditDefaultsOnly)
	UNCAudioAsset PerfectHitStage2SFX;
	UPROPERTY(EditDefaultsOnly)
	UNCAudioAsset PerfectHitStage3SFX;

	UPROPERTY()
	bool UpdatingSkillCheckRing;
	default UpdatingSkillCheckRing = false;

	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
	UImage dot;

	UFUNCTION(BlueprintOverride)
	void Construct()
	{
		Super::Construct();

		showAnim.Add( EMeleeReticleAnim::FINAL_HIT, ShowFinalHitAnim);
		hideAnim.Add( EMeleeReticleAnim::FINAL_HIT, HideFinalHitAnim);
		initShowAnim.Add( EMeleeReticleAnim::FINAL_HIT, InitFinalHitShowAnim);
		initHideAnim.Add( EMeleeReticleAnim::FINAL_HIT, InitFinalHitHideAnim);
		__IsWidgetVisible.Add( EMeleeReticleAnim::FINAL_HIT, false);

		showAnim.Add( EMeleeReticleAnim::THROW_RET, ShowThrowReticleAnim);
		hideAnim.Add( EMeleeReticleAnim::THROW_RET, HideThrowReticleAnim);
		initShowAnim.Add( EMeleeReticleAnim::THROW_RET, InitThrowReticleShowAnim);
		initHideAnim.Add( EMeleeReticleAnim::THROW_RET, InitThrowReticleHideAnim);
		__IsWidgetVisible.Add( EMeleeReticleAnim::THROW_RET, false);

		showAnim.Add( EMeleeReticleAnim::SKILL_RING, ShowSkillRingAnim);
		hideAnim.Add( EMeleeReticleAnim::SKILL_RING, HideSkillRingAnim);
		initShowAnim.Add( EMeleeReticleAnim::SKILL_RING, InitSkillRingShowAnim);
		initHideAnim.Add( EMeleeReticleAnim::SKILL_RING, InitSkillRingHideAnim);
		__IsWidgetVisible.Add( EMeleeReticleAnim::SKILL_RING, false);

		showAnim.Add( EMeleeReticleAnim::BONUS, ShowBonusAnim);
		hideAnim.Add( EMeleeReticleAnim::BONUS, HideBonusAnim);
		initShowAnim.Add( EMeleeReticleAnim::BONUS, InitBonusShowAnim);
		initHideAnim.Add( EMeleeReticleAnim::BONUS, InitBonusHideAnim);
		__IsWidgetVisible.Add( EMeleeReticleAnim::BONUS, false);

		showAnim.Add( EMeleeReticleAnim::KEYBIND, ShowKeybindAnim);
		hideAnim.Add( EMeleeReticleAnim::KEYBIND, HideKeybindAnim);
		initShowAnim.Add( EMeleeReticleAnim::KEYBIND, InitKeybindShowAnim);
		initHideAnim.Add( EMeleeReticleAnim::KEYBIND, InitKeybindHideAnim);
		__IsWidgetVisible.Add( EMeleeReticleAnim::KEYBIND, false);

		for ( int i = 0; i < int(EMeleeReticleAnim::_count); i++ )
			InitWidget( EMeleeReticleAnim( i ) );
		
		Thread( this, n"InitStreak" );

		FLinearColor initFinalHitColor 	= WHITE_COLOR;
		FLinearColor initRingColor 		= WHITE_COLOR;
		FLinearColor initRefColor 		= WHITE50_COLOR;
		FLinearColor initDotColor 		= CLEAR_COLOR;
		FLinearColor initBonusColor 	= WHITE50_COLOR;

		UAS_ReticleWidgetMeleeWeapon master = ClMeleeReticle().GetMasterMeleeReticle();
		if ( IsValid(master) )
		{
			initFinalHitColor	= master.local_FinalHitColor; 
			initRingColor		= master.local_RingColor; 
			initRefColor		= master.local_RefColor; 
			initDotColor		= master.local_DotColor; 
			initBonusColor		= master.local_BonusColor; 
		}
		
		Set_FinalHitColor(initFinalHitColor);
		Set_RingColor( initRingColor );
		Set_RefColor(initRefColor);
	 	Set_DotColor(initDotColor);
		Set_BonusColor(initBonusColor);
	}

	FLinearColor local_FinalHitColor;
	void Set_FinalHitColor( FLinearColor _FinalHitColor )
	{
		if ( local_FinalHitColor == _FinalHitColor )
			return;
		local_FinalHitColor = _FinalHitColor;

		FinalHit1.SetColorAndOpacity(_FinalHitColor);
		FinalHit2.SetColorAndOpacity(_FinalHitColor);
		FinalHit3.SetColorAndOpacity(_FinalHitColor);
		FinalHit4.SetColorAndOpacity(_FinalHitColor);
		FinalHit1b.SetColorAndOpacity(_FinalHitColor);
		FinalHit2b.SetColorAndOpacity(_FinalHitColor);
		FinalHit3b.SetColorAndOpacity(_FinalHitColor);
		FinalHit4b.SetColorAndOpacity(_FinalHitColor);
	}

	FLinearColor local_RingColor;
	void Set_RingColor(FLinearColor _RingColor)
	{
		if ( local_RingColor == _RingColor )
			return;
		local_RingColor = _RingColor;

		skillRing1.SetColorAndOpacity(_RingColor);
		skillRing2.SetColorAndOpacity(_RingColor);
		skillRing3.SetColorAndOpacity(_RingColor);
		skillRing4.SetColorAndOpacity(_RingColor);
	}

	FLinearColor local_RefColor;
	void Set_RefColor(FLinearColor _RefColor)
	{
		if ( local_RefColor == _RefColor )
			return;
		local_RefColor = _RefColor;
		
		skillRef1.SetColorAndOpacity(_RefColor);
		skillRef2.SetColorAndOpacity(_RefColor);
		skillRef3.SetColorAndOpacity(_RefColor);
		skillRef4.SetColorAndOpacity(_RefColor);			
	}

	FLinearColor local_DotColor;
	void Set_DotColor(FLinearColor _DotColor)
	{
		if ( local_DotColor == _DotColor )
			return;
		local_DotColor = _DotColor;

		//the offhand reticle is always displayed now... so this helps not draw a dot in the center all the time
		dot.SetColorAndOpacity( ClMeleeReticle().IsMasterMeleeReticle(this) ? CLEAR_COLOR : _DotColor);
	}

	FLinearColor local_BonusColor;
	void Set_BonusColor(FLinearColor _BonusColor)
	{
		if ( local_BonusColor == _BonusColor )
			return;
		local_BonusColor = _BonusColor;

		WidgetBonusFire.SetColorAndOpacity( _BonusColor);
		WidgetBonusAtticus.SetColorAndOpacity( _BonusColor);
	}

	bool ShouldShowThrowReticle(AAS_PlayerEntity player, ANCMeleeWeapon meleeWeapon, FMeleeReticleTargetData targetData )
	{
		int slot = player.GetActiveWeaponSlot();	

		return slot == WeaponSlot::MeleeSlot && meleeWeapon.IsInPreTossReleaseStates() && !targetData.isLivingTarget;
	}	

	const float32 SKILL_SPACER_SIZE 	= 83.0f;
	const float32 SKILL_START_WIDGET_f 	= 1.5;
	const FVector2D SKILL_WIDGET_FRAC 	= FVector2D(SKILL_START_WIDGET_f, SKILL_START_WIDGET_f);
	const float32 SKILL_START_SPACER_f 	= 1.35;
	const FVector2D SKILL_SPACER_FRAC 	= FVector2D(SKILL_START_SPACER_f, SKILL_START_SPACER_f);
	protected void ScriptReticleTick(ANCWeapon currentWeapon, float32 dt) override
    {
        Super::ScriptReticleTick(currentWeapon, dt);

        ANCMeleeWeapon meleeWeapon = Cast<ANCMeleeWeapon>(GetWeapon());
        if(!IsValid(meleeWeapon))
            return;
		
		AAS_PlayerEntity player = Cast<AAS_PlayerEntity>(meleeWeapon.GetWeaponOwner());
        if(!IsValid(player))
            return;
				
		FMeleeReticleTargetData targetData 	= CodeMeleeUtil::GetMeleeReticleTargetData(meleeWeapon);
		FLinearColor TargetColor 	= targetData.isEnemyPlayer ? ENEMY_COLOR : WHITE_COLOR;
		Set_FinalHitColor( TargetColor );

		bool busy = player.Passives().HasPassive(GameplayTags::Classes_Passives_BusyMeleeClash) ||
					player.Passives().HasPassive(GameplayTags::Classes_Passives_BusyMeleeExecution);

		if ( busy )
		{
			if ( GetVisibility() != ESlateVisibility::Hidden )
				SetVisibility(ESlateVisibility::Hidden);
			return;	
		}
		else if ( GetVisibility() != ESlateVisibility::HitTestInvisible )
			SetVisibility(ESlateVisibility::HitTestInvisible);

		bool shouldShowReticle 	= ClMeleeReticle().ShouldShowAxeReticleForTarget( player, meleeWeapon );
		bool hasBonus 			= meleeWeapon.GetSkillCheckState() == ESkillCheckState::BONUS;
		bool showBonusOnAxeOut 	= player.GetActiveWeaponSlot() == WeaponSlot::MeleeSlot && hasBonus;
		bool atticusModActive 	= IsModActiveOnWeapon( meleeWeapon, GameConst::WEAP_MOD_CHARGED_CURRENT );
		FLinearColor bonusColor = atticusModActive ? WHITE75_COLOR : WHITE50_COLOR;
		
		if( ShouldShowThrowReticle( player, meleeWeapon, targetData) )
		{
			ShowWidget(EMeleeReticleAnim::THROW_RET);
			HideWidget(EMeleeReticleAnim::FINAL_HIT);
			HideWidget(EMeleeReticleAnim::KEYBIND);
			HideSkillRing();

			if(hasBonus)
				ShowWidget(EMeleeReticleAnim::BONUS);
			else
				HideWidget(EMeleeReticleAnim::BONUS);
		}
		else
		{
			HideWidget(EMeleeReticleAnim::THROW_RET);

			if ( targetData.finalHitOn && shouldShowReticle )
				ShowWidget(EMeleeReticleAnim::FINAL_HIT);
			else
				HideWidget(EMeleeReticleAnim::FINAL_HIT);

			if ( targetData.skillRingOn && shouldShowReticle )	
				ShowWidget(EMeleeReticleAnim::SKILL_RING);
			else
				HideSkillRing();
			
			if ( ( targetData.bonusOn && shouldShowReticle ) || showBonusOnAxeOut )
			{
				ShowWidget(EMeleeReticleAnim::BONUS);

				int activeIndex 		= WidgetBonusSwitcher.GetActiveWidgetIndex();
				if ( atticusModActive && activeIndex != int(EMeleeBonusIcon::ATTICUS) )
					WidgetBonusSwitcher.SetActiveWidgetIndex(int(EMeleeBonusIcon::ATTICUS));
				else if ( !atticusModActive && activeIndex != int(EMeleeBonusIcon::DEFAULT_FIRE) )
					WidgetBonusSwitcher.SetActiveWidgetIndex(int(EMeleeBonusIcon::DEFAULT_FIRE));

				Set_BonusColor(bonusColor);
			}
			else
				HideWidget(EMeleeReticleAnim::BONUS);

			bool canSwingAxe 	= CodeMeleeUtil::CanSwingAxe(player);
			bool showKeyBind 	= canSwingAxe && (targetData.finalHitOn || targetData.skillRingOn || targetData.bonusOn);
			
			if ( showKeyBind && shouldShowReticle )
			{				
				ShowWidget(EMeleeReticleAnim::KEYBIND);
				bool redBonus = false;

				if ( targetData.isEnemyPlayer)
				{
					KeyBindGroup.SetActiveWidgetIndex(EMeleeKeybindIcon::ENEMY);
					redBonus = true;
				}
				else
					KeyBindGroup.SetActiveWidgetIndex(EMeleeKeybindIcon::DEFAULT);

				if ( redBonus )
					Set_BonusColor(RED_BONUS_COLOR);
				else
					Set_BonusColor(bonusColor);
			}
			else
				HideWidget(EMeleeReticleAnim::KEYBIND);

			ESkillCheckState skillState = meleeWeapon.GetSkillCheckState();

			switch(skillState)
			{
				case ESkillCheckState::CLOSE:
				{
					Set_RingColor(CLEAR_COLOR);
					FLinearColor DotColor	= targetData.isEnemyPlayer ? ENEMY_COLOR : WHITE_COLOR;
					Set_DotColor(DotColor);
				}break;

				case ESkillCheckState::OPEN:
				{
					Set_RingColor( WHITE_COLOR );
					Set_DotColor( WHITE_COLOR );

					UpdatingSkillCheckRing = true;	
					
					FVector2D NewSizeWidget;
					FVector2D NewSizeSpacer;
					if ( meleeWeapon.IsInTossStates() )
					{
						NewSizeWidget = FVector2D::UnitVector;
						NewSizeSpacer = FVector2D::UnitVector;
					}
					else
					{
						float32 openTime 	= meleeWeapon.LastAttackTime;
						float32 closeTime 	= meleeWeapon.HitTraceTime;
						float32 curTime 	= meleeWeapon.GetCurrentWeaponTime();
						float32 frac 		= (curTime - openTime) / (closeTime - openTime);
						float32 fracClamped = Math::Clamp(frac, 0.0f, 1.0f);
						
						NewSizeWidget		= Math::Lerp(SKILL_WIDGET_FRAC, FVector2D::UnitVector, fracClamped);
						NewSizeSpacer		= Math::Lerp(SKILL_SPACER_FRAC, FVector2D::UnitVector, fracClamped);					
					}
					
					WidgetSkillRing.SetRenderScale(NewSizeWidget);
					SpacerSkillRing.SetSize( FVector2D(NewSizeSpacer * SKILL_SPACER_SIZE) );
				}break;
				
				case ESkillCheckState::SUCCESS:	
				{
					Set_RingColor( SUCCESS_COLOR );
					Set_DotColor( SUCCESS_COLOR );
					
					WidgetSkillRing.SetRenderScale(FVector2D::UnitVector);
					SpacerSkillRing.SetSize( FVector2D(FVector2D::UnitVector * SKILL_SPACER_SIZE) );
					PerfectStreakHudUpdate(meleeWeapon);
				}break;

				case ESkillCheckState::FAIL:
				{
					Set_RingColor( FAIL_COLOR );
					Set_DotColor( FAIL_COLOR );

					const float32 CLAMP_MIN 		= 0.3f;		// don't clamp below this for inputs that are too fast
					/* 	for inputs that are close but too early... we want to make it obvious that it's too early, so 
						anything under the limit, clamp it way before. And anything over the limit, clamp it way over, 
						to teach that the input was too late.	*/
					const float32 CLAMP_UNDER_LIMIT 	= 0.95f;	
					const float32 CLAMP_UNDER_MAX 		= 0.85f;
					const float32 CLAMP_OVER_MAX_WIDGET = 1.25f;
					const float32 CLAMP_OVER_MAX_SPACER = 0.95f;

					if(UpdatingSkillCheckRing)
					{
						float32 openTime 		= meleeWeapon.LastAttackTime;
						float32 closeTime 		= meleeWeapon.HitTraceTime;
						float32 failTime 		= meleeWeapon.AttackButtonDownTime > openTime ? meleeWeapon.AttackButtonDownTime : meleeWeapon.HitTraceTime;
						float32 frac			= (failTime - openTime) / (closeTime - openTime);					
						bool isUnder 			= frac < CLAMP_UNDER_LIMIT;
						float32 fracClampedW 	= isUnder ? Math::Min( frac, CLAMP_UNDER_MAX ) : CLAMP_OVER_MAX_WIDGET;
						float32 fracClampedS 	= isUnder ? Math::Min( frac, CLAMP_UNDER_MAX ) : CLAMP_OVER_MAX_SPACER;
						fracClampedW 			= fracClampedW < CLAMP_MIN ? CLAMP_MIN : fracClampedW;
						fracClampedS 			= fracClampedS < CLAMP_MIN ? CLAMP_MIN : fracClampedS;
						
						FVector2D NewSizeWidget	= Math::Lerp(SKILL_WIDGET_FRAC, FVector2D::UnitVector, fracClampedW);
						FVector2D NewSizeSpacer	= Math::Lerp(SKILL_SPACER_FRAC, FVector2D::UnitVector, fracClampedS);
						
						WidgetSkillRing.SetRenderScale(NewSizeWidget);
						SpacerSkillRing.SetSize( FVector2D(NewSizeSpacer * SKILL_SPACER_SIZE) );						
						UpdatingSkillCheckRing 	= false;

						FString key 	= isUnder ? f"OMNITOOL_HARVESTEARLY" : f"OMNITOOL_HARVESTLATE";
						FText message 	= GetLocalizedText( Localization::OmniTool, key );
						AAS_HUD hud 	= GetLocalHUD();
						hud.OnAxeComboFail( message );
					}
				}break;

				case ESkillCheckState::OVERHEAT:
					PerfectStreakHudUpdate(meleeWeapon);
					break;

				case ESkillCheckState::BONUS:
					PerfectStreakHudUpdate(meleeWeapon);
					fallthrough;
				
				default:
				{
					Set_RingColor( CLEAR_COLOR );
					Set_DotColor( WHITE50_COLOR );
					
				}break;
			}						
		}
    }

	const int STREAK_HUD_MIN 	= 3;
	const int STREAK_SND_MIN 	= 3;
	int HUDStreakCount 			= 0;
	int HUDStreakMax 			= 10;

	UFUNCTION()
	void InitStreak( UNCCoroutine co)
	{
		ANCMeleeWeapon meleeWeapon = Cast<ANCMeleeWeapon>(GetWeapon());

		while( !IsValid(meleeWeapon))
		{
			co.Wait( 0.016 );
			meleeWeapon = Cast<ANCMeleeWeapon>(GetWeapon());
		}
		
		HUDStreakCount 	= GetCurrentHudStreak(meleeWeapon);
		HUDStreakMax 	= meleeWeapon.GetWeaponData().SkillCheckMaxCount;
	}

	void PerfectStreakHudUpdate(ANCMeleeWeapon meleeWeapon)
	{
		if ( !IsValid(GetPlayer()) )
			return;

		int curCount = GetCurrentHudStreak(meleeWeapon);
		
		if ( HUDStreakCount == curCount )
			return;
		HUDStreakCount = curCount;

		if ( HUDStreakCount >= STREAK_SND_MIN && !meleeWeapon.IsFinalHit )
		{
			UNCAudioAsset PerfectHitSFX = nullptr;
			if ( HUDStreakCount <= 4 )
				PerfectHitSFX = PerfectHitStage1SFX;
			else if ( HUDStreakCount <= 6 )
				PerfectHitSFX = PerfectHitStage2SFX;
			else
				PerfectHitSFX = PerfectHitStage3SFX;
		
			if (IsValid(PerfectHitSFX))
				Client_EmitSoundOnEntity(PerfectHitSFX, GetPlayer());
		}

		if ( HUDStreakCount >= STREAK_HUD_MIN )
		{
			FText message 	= GetStreakText(meleeWeapon );
			AAS_HUD hud 	= GetLocalHUD();
			hud.OnPerfectAxeCombo( message );
		}
		else if ( meleeWeapon.GetSkillCheckState() != ESkillCheckState::FAIL && meleeWeapon.GetSkillCheckState() != ESkillCheckState::CLOSE )
		{
			FText message 	= GetLocalizedText( Localization::OmniTool, f"OMNITOOL_HARVESTGOOD" );
			AAS_HUD hud 	= GetLocalHUD();
			hud.OnPerfectAxeCombo( message );
		}
	}

	FText GetStreakText(ANCMeleeWeapon meleeWeapon)
	{
		ESkillCheckState skillState = meleeWeapon.GetSkillCheckState();
		if ( skillState == ESkillCheckState::BONUS )
			return GetLocalizedText( Localization::OmniTool, f"OMNITOOL_HARVESTPERFECT" );
		else if ( HUDStreakCount >= HUDStreakMax )
			return GetLocalizedText( Localization::OmniTool, f"OMNITOOL_HARVESTMAXCOMBO" );
		else 
			return GetLocalizedText( Localization::OmniTool, f"OMNITOOL_HARVESTCOMBO", FFormatArgumentValue(HUDStreakCount) );
	}

	int GetCurrentHudStreak(ANCMeleeWeapon meleeWeapon)
	{
		ESkillCheckState skillState = meleeWeapon.GetSkillCheckState();
		int curStreak = skillState == ESkillCheckState::BONUS ? meleeWeapon.SkillCheckCount + 1 : meleeWeapon.SkillCheckCount;

		return curStreak;
	}

	private void InitWidget( EMeleeReticleAnim animType )
	{
		UAS_ReticleWidgetMeleeWeapon master = GetMasterMeleeReticleSafe();
		__IsWidgetVisible[animType] = master.__IsWidgetVisible[animType];

		if ( !__IsWidgetVisible[animType] && !IsAnimationPlaying(master.hideAnim[animType]) )
			PlayAnimation( initHideAnim[animType] );
		else if ( __IsWidgetVisible[animType] && !IsAnimationPlaying(master.showAnim[animType]) )
			PlayAnimation( initShowAnim[animType] );

		if ( IsAnimationPlaying(master.hideAnim[animType]) )
			PlayAnimation( hideAnim[animType], GetAnimationCurrentTime(master.hideAnim[animType]) );
		else if ( IsAnimationPlaying(master.showAnim[animType]) )
			PlayAnimation( showAnim[animType], GetAnimationCurrentTime(master.showAnim[animType]) );	
	}

	private void ShowWidget(EMeleeReticleAnim animType)
	{
		if ( __IsWidgetVisible[animType] )
			return;
		__IsWidgetVisible[animType] = !__IsWidgetVisible[animType];

		StopAnimation(hideAnim[animType]);
		PlayAnimation(showAnim[animType]);
	}

	private void HideWidget(EMeleeReticleAnim animType)
	{
		if ( !__IsWidgetVisible[animType] )
			return;
		__IsWidgetVisible[animType] = !__IsWidgetVisible[animType];

		StopAnimation(showAnim[animType]);
		PlayAnimation(hideAnim[animType]);
	}

	private bool IsWidgetVisible(EMeleeReticleAnim animType)
	{
		return __IsWidgetVisible[animType];
	}

	protected void HideSkillRing()
	{
		if ( !__IsWidgetVisible[EMeleeReticleAnim::SKILL_RING] )
			return;

		ANCMeleeWeapon meleeWeapon = Cast<ANCMeleeWeapon>(GetWeapon());
		switch ( meleeWeapon.GetSkillCheckState() )
		{
			//don't hide on these two
			case ESkillCheckState::SUCCESS:
			case ESkillCheckState::FAIL:
				return;

			default:
				//do nothing
				break;	
		}

		HideWidget(EMeleeReticleAnim::SKILL_RING);
	}
	
    AAS_PlayerEntity GetPlayer()
	{
		ANCPlayerController Controller = Cast<ANCPlayerController>(GetOwningPlayer());
		if (!IsValid(Controller))
        {
			return nullptr;
        }

		return Cast<AAS_PlayerEntity>(Controller.GetNCPawn());
	}

	ANCMeleeWeapon GetWeapon()
	{
		AAS_PlayerEntity player = GetPlayer();
		if (!IsValid(player))
        {
			return nullptr;
        }

        return Cast<ANCMeleeWeapon>(player.GetWeaponAtSlot(WeaponSlot::MeleeSlot));
	}

	UAS_ReticleWidgetMeleeWeapon GetMasterMeleeReticleSafe()
	{
		UAS_ReticleWidgetMeleeWeapon master = ClMeleeReticle().GetMasterMeleeReticle();
		
		if ( IsValid(master) )
			return master;
		else
			return this;
	}
}
