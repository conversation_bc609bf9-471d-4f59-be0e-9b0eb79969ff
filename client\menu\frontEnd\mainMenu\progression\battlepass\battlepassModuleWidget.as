UCLASS( Abstract )
class UAS_BattlepassModule : UNC_DisplayWidget
{
	default StateOnConstruction = EDisplayConstructVisibility::Hidden;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage background;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage spine;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_BattlepassProgress progress;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UNCButtonWidget button;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_ModulePrimaryCta primaryCta;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_CommonButton secondaryCta;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage thumbnail;

	UPROPERTY( NotVisible, BlueprintHidden, Transient, Meta = ( BindWidgetAnim ) )
	private UWidgetAnimation hoverAnimation;

	UPROPERTY( NotVisible, BlueprintHidden, Transient, Meta = ( BindWidgetAnim ) )
	private UWidgetAnimation ctaHoverAnimation;

	private UMaterialInstanceDynamic backgroundMaterial;
	private UMaterialInstanceDynamic spineMaterial;
	private UMaterialInstanceDynamic thumbnailMaterial;
	private FNCUIStoreBundle battlepassBundle;
	private UNCUIMTXItem battlepassItem;
	private bool ctaButtonHovered = false;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		backgroundMaterial = CreateDynamicMaterialFromImageBrush( background );
		if ( IsValid( backgroundMaterial ) )
		{
			background.SetBrushFromMaterial( backgroundMaterial );
		}

		spineMaterial = CreateDynamicMaterialFromImageBrush( spine );
		if ( IsValid( spineMaterial ) )
		{
			spine.SetBrushFromMaterial( spineMaterial );
		}

		thumbnailMaterial = CreateDynamicMaterialFromImageBrush( thumbnail );
		if ( IsValid( thumbnailMaterial ) )
		{
			thumbnail.SetBrushFromMaterial( thumbnailMaterial );
		}

		// The main button displays the battlepass
		button.OnButtonClicked.AddUFunction( this, n"OnButtonClicked" );
		button.OnButtonHovered.AddUFunction( this, n"OnButtonHovered" );
		button.OnButtonUnhovered.AddUFunction( this, n"OnButtonUnhovered" );

		// The primary cta is used for purchasing or opening the inspect menu
		primaryCta.OnButtonBaseClicked.AddUFunction( this, n"OnPrimaryCtaButtonClicked" );
		primaryCta.OnButtonBaseHovered.AddUFunction( this, n"OnPrimaryCtaButtonHovered" );
		primaryCta.OnButtonBaseUnhovered.AddUFunction( this, n"OnPrimaryCtaButtonUnhovered" );

		// The secondary cta is used to open the inspect menu if this hasn't been purchased yet
		secondaryCta.OnButtonBaseClicked.AddUFunction( this, n"OnSecondaryCtaButtonClicked" );
		secondaryCta.OnButtonBaseHovered.AddUFunction( this, n"OnSecondaryCtaButtonHovered" );
		secondaryCta.OnButtonBaseUnhovered.AddUFunction( this, n"OnSecondaryCtaButtonUnhovered" );

		UNCUIStoreAPI storeApi = GetStoreAPI();
		if ( IsValid( storeApi ) )
		{
			storeApi.OnItemPurchased.AddUFunction( this, n"OnItemPurchased" );
		}
	}

	void SetBattlepassBundleId( FGameplayTag newBattlepassBundleId )
	{
		if ( battlepassBundle.Id != newBattlepassBundleId )
		{
			if ( GetBundleInfo( newBattlepassBundleId, battlepassBundle ) )
			{
				UNCUICollectionManager collectionManager = GetCollectionManager();
				if ( IsValid( collectionManager ) )
				{
					UNCUIGetMTXItemsAction action = collectionManager.GetMTXItems( newBattlepassBundleId.GetSingleTagContainer() );
					if ( IsValid( action ) )
					{
						action.SetLoadUIAssets( true );
						action.OnComplete.AddUFunction( this, n"OnRequestComplete" );
						action.ExecuteAsync();
					}
				}
			}
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnRequestComplete( UNCUIGetMTXItemsAction action )
	{
		// Now that everything has been loaded/requested we can start showing info
		TArray<UNCUIMTXItem> items = action.GetUIMTXItems();
		if ( items.Num() == 0 )
			return;

		for ( UNCUIMTXItem item : items )
		{
			if ( item.AssetType == EMTXAssetType::TreasureTrove )
			{
				battlepassItem = item;
				break;
			}
		}

		UpdateModule();
		Show();
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnButtonClicked( UNCButtonWidget buton )
	{
		UAS_CollectionNavigator collectionNavigator = GetCollectionNavigator();
		if ( IsValid( collectionNavigator ) )
		{
			collectionNavigator.TryToDisplayBundle( battlepassBundle );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnButtonHovered( UNCButtonWidget buton )
	{
		PlayAnimationForward( hoverAnimation, 4.0f );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnButtonUnhovered( UNCButtonWidget buton )
	{
		PlayAnimationReverse( hoverAnimation, 6.0f );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnPrimaryCtaButtonClicked( UCommonButtonBase buton )
	{
		UAS_CollectionNavigator collectionNavigator = GetCollectionNavigator();
		if ( IsValid( collectionNavigator ) )
		{
			if ( IsMtxItemOwned( battlepassItem ) )
			{
				// If this battlepass is owned already, just inspect it
				collectionNavigator.TryToInspectItem( battlepassItem );
			}
			else
			{
				// Otherwise try to interact with (purchase) it
				collectionNavigator.TryToInteractWithItem( battlepassItem );
			}
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnPrimaryCtaButtonHovered( UCommonButtonBase buton )
	{
		PlayAnimationForward( ctaHoverAnimation, 4.0f );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnPrimaryCtaButtonUnhovered( UCommonButtonBase buton )
	{
		PlayAnimationReverse( ctaHoverAnimation, 6.0f );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnSecondaryCtaButtonClicked( UCommonButtonBase buton )
	{
		UAS_CollectionNavigator collectionNavigator = GetCollectionNavigator();
		if ( IsValid( collectionNavigator ) )
		{
			collectionNavigator.TryToInspectBundle( battlepassBundle );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnSecondaryCtaButtonHovered( UCommonButtonBase buton )
	{
		PlayAnimationForward( ctaHoverAnimation, 4.0f );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnSecondaryCtaButtonUnhovered( UCommonButtonBase buton )
	{
		PlayAnimationReverse( ctaHoverAnimation, 6.0f );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnItemPurchased( FGameplayTag purchasedItemId )
	{
		if ( purchasedItemId == battlepassBundle.Id )
		{
			// If we were just purchased, update
			UpdateModule();
		}
	}

	private void UpdateModule()
	{
		if ( !IsValid( battlepassItem ) )
			return;

		// Update the progress widget
		progress.SetBattlepassProgress( battlepassBundle, battlepassItem );

		// Update the materials
		if ( IsValid( spineMaterial ) )
		{
			spineMaterial.SetTextureParameterValue( MaterialParameter::TEXTURE, battlepassItem.Spine );
		}

		if ( IsValid( thumbnailMaterial ) )
		{
			UNCUIUtils::SetTextureParameterToSoftTexture( thumbnailMaterial, battlepassBundle.BannerImage, MaterialParameter::TEXTURE );
		}

		// Determine if we are owned or not (this gets called after purchase so it could change)
		bool isOwned							 = false;
		UNCUICollectionManager collectionManager = GetCollectionManager();
		if ( IsValid( collectionManager ) )
		{
			isOwned = collectionManager.CheckIsOwned( battlepassBundle.Id );
		}

		// Update the primary cta
		primaryCta.UpdateCtaButton( battlepassBundle );

		// Only show the secondary cta if the trove is owned
		SetWidgetVisibilitySafe( secondaryCta, isOwned ? ESlateVisibility::Collapsed : ESlateVisibility::SelfHitTestInvisible );
	}
}