UCLASS( Abstract )
class UAS_VendorPanel : UNCTabPanelWidget
{
	UPROPERTY( NotVisible, BindWidget )
	UAS_VendorWidget vendorWidget;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		int clientTeam = Client_GetLocalPawn().GetTeam();
		vendorWidget.SetVendorTeam( clientTeam );
	}

	UFUNCTION(BlueprintOverride)
	void OnShowStart()
	{
		vendorWidget.OnOpened();
		vendorWidget.RefreshItemList();
	}

	UFUNCTION(BlueprintOverride)
	void OnHideStart()
	{
		vendorWidget.OnClosed();
	}
}

UCLASS( Abstract )
class UAS_VendorQuickPanel : UNC_DisplayWidget
{
	UPROPERTY( NotVisible, BindWidget )
	UAS_VendorItemButton weaponButton0;

	UPROPERTY( NotVisible, BindWidget )
	UAS_VendorItemButton weaponButton1;

	UPROPERTY( NotVisible, BindWidget )
	UAS_VendorItemButton itemButton0;

	UPROPERTY( NotVisible, BindWidget )
	UButton button;

	UPROPERTY( NotVisible, BindWidget )
	UCommonVisualAttachment out_of_shields_warning_container;

	UPROPERTY(Meta=(BindWidgetAnim), Transient)
	private UWidgetAnimation FlashPanelAnim;

	TArray<UAS_VendorItemButton> weaponButtons;

	TArray<UAS_VendorItemButton> itemButtons;

	default bIsFocusable = true;

	int vendorTeam;
	
	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		vendorTeam = Widget_GetPawnOwner( this ).GetTeam();

		weaponButtons.Add( weaponButton0 );
		weaponButtons.Add( weaponButton1 );
		itemButtons.Add( itemButton0 );
		
		for ( UAS_VendorItemButton _button : weaponButtons )
		{
			_button.onClicked.AddUFunction( this, n"OnVendorItemButtonClicked" );
			_button.onRightClicked.AddUFunction( this, n"OnVendorItemButtonRightClicked" );
			_button.onMiddleClicked.AddUFunction( this, n"OnVendorItemButtonMiddleClicked" );
		}

		for ( UAS_VendorItemButton _button : itemButtons )
		{
			_button.onClicked.AddUFunction( this, n"OnVendorItemButtonClicked" );
			_button.onMiddleClicked.AddUFunction( this, n"OnVendorItemButtonMiddleClicked" );
		}
	}

	UFUNCTION( BlueprintOverride )
	void OnShowStart()
	{
		ScriptCallbacks().localClient_onBackpackContentsChanged.AddUFunction( this, n"OnBackpackContentsChanged" );
		ScriptCallbacks().localClient_onCloudStorageContentsChanged.AddUFunction( this, n"OnCloudStorageContentsChanged" );

		RefreshItemList();
	}

	UFUNCTION(BlueprintOverride)
	void OnHideStart()
	{
		ScriptCallbacks().localClient_onBackpackContentsChanged.UnbindObject( this );
		ScriptCallbacks().localClient_onCloudStorageContentsChanged.UnbindObject( this );
	}
	
	UFUNCTION()
	void OnBackpackContentsChanged( AAS_PlayerEntity player )
	{
		RefreshItemList();
	}

	UFUNCTION()
	void OnCloudStorageContentsChanged( AAS_TeamConnectedBox box )
	{
		RefreshItemList();
	}

	UFUNCTION( BlueprintOverride )
	void Destruct()
	{
		ScriptCallbacks().localClient_onBackpackContentsChanged.UnbindObject( this );
		ScriptCallbacks().localClient_onCloudStorageContentsChanged.UnbindObject( this );
	}

	UFUNCTION()
	void OnVendorItemButtonRightClicked( UAS_VendorItemButtonBase vendorItem )
	{
		AAS_VendorStoreData vendorStoreData = VendorManager().GetVendorStockData( vendorTeam );
		if( !IsValid( vendorStoreData ) )
			return;

		vendorStoreData.Client_TryPurchaseItem( vendorItem.vendorIndex, true );
		VendorManager().cl_UpdatePlayerLastMenuInteractTime(Client_GetLocalASPawn());
	}

	UFUNCTION()
	void OnVendorItemButtonClicked( UAS_VendorItemButtonBase vendorItemButton )
	{
		AAS_VendorStoreData vendorStoreData = VendorManager().GetVendorStockData( vendorTeam );
		if( !IsValid( vendorStoreData ) )
			return;

		// Set focus on the whole panel after a button click so that space bar doesnt accidentally purchase more (Space is used to respawn)
		SetFocus();

		vendorStoreData.Client_TryPurchaseItem( vendorItemButton.vendorIndex, false );
		VendorManager().cl_UpdatePlayerLastMenuInteractTime(Client_GetLocalASPawn());
	}

	UFUNCTION()
	void OnVendorItemButtonMiddleClicked( UAS_VendorItemButtonBase vendorItemButton )
	{
		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if( !IsValid( player ) )
			return;

		AAS_VendorStoreData vendorStoreData = VendorManager().GetVendorStockData( vendorTeam );
		if ( !IsValid( vendorStoreData ) )
			return;

		if( vendorItemButton.vendorIndex < 0 || vendorItemButton.vendorIndex >= vendorStoreData.itemsForSale.Num() )
			return;

		int intIndex = vendorStoreData.itemsForSale[ vendorItemButton.vendorIndex ].net_itemIndex;
		FLootDataStruct lootData = GetLootDataByIntIndex( intIndex );

		AAS_Vendor vendor;
		AAS_BaseSystem baseSystem = GetBaseForTeam( vendorTeam );
		if( IsValid( baseSystem ) )
		{
			TArray<AActor> baseVendors = GetAllActorsInRange2D( AAS_Vendor::StaticClass(), baseSystem.GetActorLocation(), baseSystem.baseRadius );
			if( baseVendors.Num() > 0 )
				vendor = Cast<AAS_Vendor>( baseVendors[0] );
		}

		if( !IsValid( vendor ) )
		{
			Warning( "OnVendorItemButtonMiddleClicked - No team vendors found" );
			return;
		}

		EPlayerPingType pingType = lootData.lootType == ELootType::PrimaryWeapon ? EPlayerPingType::LOOT_WEAPON : EPlayerPingType::LOOT;
		FVector loc				 = vendor.GetActorLocation();
		ClPingManager().SendPingAtLocation_WithLoot( pingType, loc, lootData.index, vendor );
	}

	UFUNCTION()
	void RefreshItemList()
	{
		USH_VendorManager vendorManager = VendorManager();
		if ( !IsValid( vendorManager ) )
			return;

		AAS_VendorStoreData vendorStoreData = vendorManager.GetVendorStockData( vendorTeam );
		if ( !IsValid( vendorStoreData ) )
			return;

		TArray<FVendorData> Rows;
		vendorManager.PopulateRowData( Rows, vendorTeam );

		int vendorIndex		  = 0;
		int weaponButtonIndex = 0;
		int itemButtonIndex	  = 0;

		bool canAffordShield = false;

		for ( FVendorItem vendorItem : vendorStoreData.itemsForSale )
		{
			FLootDataStruct lootData = GetLootDataByIntIndex( vendorItem.net_itemIndex );

			if ( lootData.lootType == ELootType::PrimaryWeapon && weaponButtonIndex < weaponButtons.Num() )
			{
				UAS_VendorItemButton newWidget = weaponButtons[weaponButtonIndex++];
				newWidget.Initialize( vendorIndex, GetGameplayTagForInt( vendorItem.net_itemIndex ), Rows[vendorItem.net_rowIndex] );
			}
			else if ( lootData.lootType != ELootType::PrimaryWeapon && itemButtonIndex < itemButtons.Num() )
			{
				UAS_VendorItemButton newWidget = itemButtons[ itemButtonIndex++ ];
				FVendorData vendorData = Rows[vendorItem.net_rowIndex];
				FGameplayTag vendorItemTag = GetGameplayTagForInt( vendorItem.net_itemIndex );
				newWidget.Initialize( vendorIndex, vendorItemTag, vendorData );

				// Technically this might not be a shield item.. even though it likely is set to be in the data.
				const FLootDataStruct& vendorLootData = GetLootDataByIndex( vendorItemTag );
				if ( vendorLootData.lootType == ELootType::PlayerShield )
				{
					canAffordShield = true;
					ANCPlayerCharacter player = Client_GetLocalASPawn();
					for ( FBackpackItemStruct cost : vendorData.cost )
					{
						int inBackpack = CountItemsInBackpackAndValidBoxes( player, cost.itemIndex, player, GameConst::CRAFTING_SOURCE_DEFAULT );
						if ( inBackpack >= cost.itemCount )
							continue;

						canAffordShield = false;
					}
				}
			}

			vendorIndex++;
		}
		
		if ( canAffordShield )
		{
			ANCPlayerCharacter player = Client_GetLocalASPawn();
			TArray<FGameplayTag> shieldsToCheckFor;
			shieldsToCheckFor.Add( GameplayTags::Loot_Armor_Level2 );
			shieldsToCheckFor.Add( GameplayTags::Loot_Armor_Level3 );
			shieldsToCheckFor.Add( GameplayTags::Loot_Armor_Level4 );
			int numShieldsLeft = player.CountItemsInAllBackpacks( shieldsToCheckFor );
			SetWidgetVisibilitySafe( out_of_shields_warning_container, numShieldsLeft < 1 ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
		}
		else
		{
			SetWidgetVisibilitySafe( out_of_shields_warning_container, ESlateVisibility::Collapsed );
		}
	}

	const float32 FLASH_PLAYSPEED = 0.5;
	const float32 FLASH_ANIM_DURATION = 0.2;	//wish I could get the duration from code
	void FlashPanel(float32 duration = 0 )
	{
		if ( IsAnimationPlaying(FlashPanelAnim))
			StopAnimation(FlashPanelAnim);

		if ( duration > 0 )
			Thread( this, n"FlashPanelThread", duration );
		else
			PlayAnimation(FlashPanelAnim, 0, 5, EUMGSequencePlayMode::Forward, FLASH_PLAYSPEED, true );
	}	

	void StopPanelFlash()
	{
		endSignalFlashPanel.Emit();
	}

	FNCCoroutineSignal endSignalFlashPanel;
	UFUNCTION()
	void FlashPanelThread( UNCCoroutine co, float32 duration )
	{						
		endSignalFlashPanel.Emit();		
		co.EndOn( this, endSignalFlashPanel );

		int startTimeMS 	= GetGameTimeMS();
		float32 elapsedTime = 0;

		while( elapsedTime < duration )
		{
			PlayAnimation(FlashPanelAnim, 0, 1, EUMGSequencePlayMode::Forward, FLASH_PLAYSPEED, true );
			
			float32 length = FLASH_ANIM_DURATION / FLASH_PLAYSPEED; //wish I could get the duration from code
			co.Wait( length ); 
			
			if ( this.GetVisibility() == ESlateVisibility::Hidden || this.GetVisibility() == ESlateVisibility::Collapsed )
				return;

			elapsedTime = TO_SECONDS(GetGameTimeMS() - startTimeMS );
		}
	}
}