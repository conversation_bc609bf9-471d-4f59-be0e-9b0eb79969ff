UCLASS()
class UAnimNotifyState_SetMaterialParametersOnViewModel : UAnimNotifyState
{
	UPROPERTY()
	FName parameterName;
	
	UPROPERTY()
	TArray<int> materialIndexes;

	UPROPERTY()
	float32 startValue;
	
	UPROPERTY()
	float32 endValue;

	UFUNCTION(BlueprintOverride)
	bool NotifyBegin(USkeletalMeshComponent MeshComp, UAnimSequenceBase Animation, float TotalDuration,
					 FAnimNotifyEventReference EventReference) const
	{
		AAS_PlayerEntity myPlayer = Cast<AAS_PlayerEntity>( MeshComp.GetOwner() );
		if ( !IsValid( myPlayer ) )
			return false;

		USkeletalMeshComponent arms = myPlayer.GetFirstPersonArmsComponent();
		for ( int materialIndex : materialIndexes )
		{
			UMaterialInstanceDynamic material = arms.CreateDynamicMaterialInstance( materialIndex );
			if (IsValid(material))
				material.SetScalarParameterValue( parameterName, startValue );
		}

		return true;
	}

	UFUNCTION(BlueprintOverride)
	bool NotifyEnd(USkeletalMeshComponent MeshComp, UAnimSequenceBase Animation,
				   FAnimNotifyEventReference EventReference) const
	{
		AAS_PlayerEntity myPlayer = Cast<AAS_PlayerEntity>( MeshComp.GetOwner() );
		if ( !IsValid( myPlayer ) )
			return false;

		USkeletalMeshComponent arms = myPlayer.GetFirstPersonArmsComponent();
		for ( int materialIndex : materialIndexes )
		{
			UMaterialInstanceDynamic material = arms.CreateDynamicMaterialInstance( materialIndex );
			if (IsValid(material))
				material.SetScalarParameterValue( parameterName, endValue );
		}

		return true;
	}
}