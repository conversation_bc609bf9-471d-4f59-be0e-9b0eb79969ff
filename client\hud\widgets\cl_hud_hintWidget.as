enum EHintStyle
{
	DEFAULT,
	CRITICAL_WARN,
	SCOPE_SWAP,
	ICON
}

USTRUCT()
struct FHUDHintData
{
	UPROPERTY()
	FText hintString;

	UPROPERTY()
	FName inputAction;

	UPROPERTY()
	FName inputActionKBM;

	UPROPERTY()
	bool isDblTap_gamepad;
	
	UPROPERTY()
	bool isDblTap_kbm;

	UPROPERTY()
	bool isHold_gamepad;
	
	UPROPERTY()
	bool isHold_kbm;

	UPROPERTY()
	UTexture2D icon;
	
	UPROPERTY()
	EHintStyle hintStyle;

	// Up to 3 additional allowed
	UPROPERTY()
	TArray<FName> additionalKBMInputActions;
	
	UPROPERTY()
	bool prioritizeOverUseText = false;

	FHUDHintData( FText inHintString, FName inInputAction = NAME_None, FName inInputActionKBM = NAME_None )
	{
		hintString = inHintString;
		inputAction = inInputAction;
		inputActionKBM = inInputActionKBM;
	}
}

event void FOnHintWidgetUpdated();

UCLASS( Abstract )
class UAS_HUDHintWidget : UUserWidgetDefault
{
	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	private UPanelWidget hintPanel;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	private UOverlay keybindOverlay;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	private UAS_KeybindWidget hintKeybind_Gamepad;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	private UAS_KeybindWidget hintKeybind_KBM_01;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	private UAS_KeybindWidget hintKeybind_KBM_02;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	private UAS_KeybindWidget hintKeybind_KBM_03;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	private UAS_KeybindWidget hintKeybind_KBM_04;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	private UImage hintIcon;

	UPROPERTY( NotVisible, NotEditable, BindWidget )
	private UTextBlock hintText;

	UPROPERTY( NotVisible, Transient, Meta=( BindWidgetAnim ) )
	private UWidgetAnimation anim_fade;

	UPROPERTY( NotVisible, Transient, Meta=( BindWidgetAnim ) )
	private UWidgetAnimation anim_styles;

	UPROPERTY( NotVisible, Transient, Meta=( BindWidgetAnim ) )
	private UWidgetAnimation anim_progress_bar;

	private FText lastHintSet;

	private FHUDHintData savedHintData;

	private TArray<UAS_KeybindWidget> addlKeybindWidgets;

	FOnHintWidgetUpdated onHintWidgetSet;

	UFUNCTION(BlueprintOverride)
	void Construct()
	{
		addlKeybindWidgets.Add( hintKeybind_KBM_02 );
		addlKeybindWidgets.Add( hintKeybind_KBM_03 );
		addlKeybindWidgets.Add( hintKeybind_KBM_04 );
		ClearHint();
	}

	FHUDHintData GetHint()
	{
		return savedHintData;
	}

	void SetHint( const FHUDHintData data )
	{
		if ( lastHintSet.IdenticalTo( data.hintString ) )
		{
			return;
		}
		
		savedHintData = data;
		lastHintSet = savedHintData.hintString;
		endQueuedThreadsSignal.Emit();
		endQueuedShowThreadSignal.Emit();

		Show();
	}

	private void Show()
	{
		SetWidgetVisibilitySafe( hintPanel, ESlateVisibility::HitTestInvisible );

		hintText.SetText( savedHintData.hintString );

		PlayAnimation( anim_progress_bar, 0, 0, EUMGSequencePlayMode::Forward, 0 );

		const bool actionKBMValid = !savedHintData.inputActionKBM.IsNone();
		const bool actionGamepadValid = !savedHintData.inputAction.IsNone();
		const bool additionalKeybindsValid = !savedHintData.additionalKBMInputActions.IsEmpty();
		const bool hideKeybinds = !actionKBMValid && !actionGamepadValid && !additionalKeybindsValid;
		if ( hideKeybinds )
		{
			SetWidgetVisibilitySafe( keybindOverlay, ESlateVisibility::Collapsed );
		}
		else
		{
			SetWidgetVisibilitySafe( keybindOverlay, ESlateVisibility::HitTestInvisible );

			if ( actionKBMValid )
			{
				hintKeybind_KBM_01.SetInputActionKBM( savedHintData.inputActionKBM );
			}
			else
			{
				hintKeybind_KBM_01.SetInputActionKBM( savedHintData.inputAction );
			}

			hintKeybind_Gamepad.SetInputAction( savedHintData.inputAction );

			hintKeybind_Gamepad.SetHoldButton( savedHintData.isHold_gamepad );
			hintKeybind_Gamepad.SetDblTapButton( savedHintData.isDblTap_gamepad );

			hintKeybind_KBM_01.SetHoldButton( savedHintData.isHold_kbm );
			hintKeybind_KBM_01.SetDblTapButton( savedHintData.isDblTap_kbm );

			const int numAddlKeybinds = addlKeybindWidgets.Num();
			const int numAddlHints = savedHintData.additionalKBMInputActions.Num();
			for( int i = 0; i < numAddlKeybinds; i++ )
			{
				FName addlKeybind = NAME_None;
				if ( i < numAddlHints )
				{
					addlKeybind = savedHintData.additionalKBMInputActions[ i ];
				}

				addlKeybindWidgets[ i ].SetInputAction( addlKeybind );
				if ( !addlKeybind.IsNone() )
				{
					addlKeybindWidgets[ i ].SetInputAction( addlKeybind );
					//SetWidgetVisibilitySafe( addlKeybindWidgets[ i ], ESlateVisibility::HitTestInvisible );
					addlKeybindWidgets[ i ].SetShowGlyph( true );
				}
				else
				{
					SetWidgetVisibilitySafe( addlKeybindWidgets[ i ], ESlateVisibility::Collapsed );
					addlKeybindWidgets[ i ].SetShowGlyph( false );
				}

			}
		}

		if ( IsValid( savedHintData.icon ) )
		{
			hintIcon.SetBrushFromTexture( savedHintData.icon );
			SetWidgetVisibilitySafe( hintIcon, ESlateVisibility::HitTestInvisible );
		}
		else
		{
			SetWidgetVisibilitySafe( hintIcon, ESlateVisibility::Collapsed );
		}

		int animTime = 0;
		switch( savedHintData.hintStyle )
		{
			case EHintStyle::DEFAULT:
			{
				break;
			}
			case EHintStyle::CRITICAL_WARN:
			{
				animTime = 1;
				break;
			}
			case EHintStyle::SCOPE_SWAP:
			{
				animTime = 2;
				break;
			}
			case EHintStyle::ICON:
			{
				animTime =3;
				break;
			}
		}

		PlayAnimation( anim_styles, animTime, 1, EUMGSequencePlayMode::Forward, 0 );

		float fadeRate = 1.0 / DEFAULT_FADE_IN_TIME;
		float fadeInStartTime = hintPanel.RenderOpacity;
		PlayAnimation( anim_fade, fadeInStartTime, 1, EUMGSequencePlayMode::Forward, fadeRate );
	}

	const float DEFAULT_FADE_IN_TIME = 0.2;
	void ClearHint()
	{
		// Commenting this out so we can fade out without having to then collapse after the fade completes (aka I'm lazy)
		//SetWidgetVisibilitySafe( hintPanel, ESlateVisibility::Collapsed );

		lastHintSet = FText();
		savedHintData = FHUDHintData();

		float fadeInStartTime = 1.0 - hintPanel.RenderOpacity;
		PlayAnimation( anim_fade, fadeInStartTime, 1, EUMGSequencePlayMode::Reverse, 8 );

		endQueuedThreadsSignal.Emit();
		endQueuedShowThreadSignal.Emit();
	}

	void SetProgressFrac( float32 newFrac )
	{
		float32 newFracSafe = float32( Math::Clamp( newFrac, 0, 1 ) );
		PlayAnimation( anim_progress_bar, newFracSafe, 0, EUMGSequencePlayMode::Forward, 0 );
	}

	FNCCoroutineSignal endQueuedThreadsSignal;
	FNCCoroutineSignal endQueuedShowThreadSignal;
	UFUNCTION()
	private void QueueHideThread( UNCCoroutine co, float hideDelay )
	{
		endQueuedThreadsSignal.Emit();
		co.EndOn( this, endQueuedThreadsSignal );

		co.Wait( hideDelay );
		ClearHint();
	}

	UFUNCTION()
	private void QueuedShowThread( UNCCoroutine co, float showDelay )
	{
		endQueuedShowThreadSignal.Emit();
		co.EndOn( this, endQueuedThreadsSignal );
		co.EndOn( this, endQueuedShowThreadSignal );

		co.Wait( showDelay );
		Show();
	}

	bool IsHintVisible()
	{
		return RenderOpacity > 0;
	}
}