UCLASS( Abstract )
class UAS_RaidMessagingShieldbreakerIndicatorWidget : UNC_DisplayWidget
{
	default StateOnConstruction = EDisplayConstructVisibility::Shown;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage shieldbreakerIndicator;

	UPROPERTY( NotVisible, BlueprintHidden, Transient, Meta = ( BindWidgetAnim ) )
	private UWidgetAnimation stateChangedAnimation;

	private UMaterialInstanceDynamic shieldbreakerMaterial;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		shieldbreakerMaterial = CreateDynamicMaterialFromImageBrush( shieldbreakerIndicator );
		if ( IsValid( shieldbreakerMaterial ) )
		{
			shieldbreakerIndicator.SetBrushFromMaterial( shieldbreakerMaterial );
		}
	}

	void ChangeShieldbreakerCarrier( AAS_PlayerEntity player )
	{
		AAS_PlayerEntity localPlayer = Client_GetLocalASPawn();
		if ( !IsValid( player ) || !IsValid( localPlayer ) || !IsValid( shieldbreakerMaterial ) )
			return;

		// When a player has the shieldbreaker we want to use friendly or enemy colors
		bool isPlayerTeam		 = localPlayer.GetTeam() == player.GetTeam();
		FLinearColor accentColor = GetCommonUiMpcColor( isPlayerTeam ? CommonUiColorMpcNames::FRIENDLY_HIGHLIGHT : CommonUiColorMpcNames::ENEMY_HIGHLIGHT );
		shieldbreakerMaterial.SetVectorParameterValue( MaterialParameter::ACCENT_COLOR, accentColor );

		// We also want to show the portrait if the player is on our team
		bool showTeammatePortrait = isPlayerTeam && localPlayer != player;
		shieldbreakerMaterial.SetScalarParameterValue( MaterialParameter::SHOW_SCALAR, MaterialParameter::GetTrueFalseFloat( showTeammatePortrait ) );
		if ( showTeammatePortrait )
		{
			UAS_PlayerClassManager classManager = player.ClassManager();
			if ( IsValid( classManager ) )
			{
				FClassDataStruct classData = classManager.GetClassData();
				shieldbreakerMaterial.SetTextureParameterValue( MaterialParameter::PORTRAIT_TEXTURE, classData.characterSettingsAsset.CharacterIcon );
			}
		}

		PlayAnimationForward( stateChangedAnimation );
	}

	void ChangeRaidObjectiveState( EObjectiveState objectiveState )
	{
		if ( !IsValid( shieldbreakerMaterial ) || !DoesObjectiveStateInvolveShieldbreaker( objectiveState ) )
			return;

		// When we are crafting or the shieldbreaker is on the ground, we want to use the gold accent color
		FLinearColor accentColor = GetCommonUiMpcColor( CommonUiColorMpcNames::GOLD );
		shieldbreakerMaterial.SetVectorParameterValue( MaterialParameter::ACCENT_COLOR, accentColor );
		shieldbreakerMaterial.SetScalarParameterValue( MaterialParameter::SHOW_SCALAR, MaterialParameter::GetTrueFalseFloat( false ) );
		PlayAnimationForward( stateChangedAnimation );
	}

	void SetIconMaterial( UMaterialInterface material )
	{
		shieldbreakerIndicator.SetBrushFromMaterial( material );
		shieldbreakerMaterial = CreateDynamicMaterialFromImageBrush( shieldbreakerIndicator );
		if ( IsValid( shieldbreakerMaterial ) )
		{
			shieldbreakerIndicator.SetBrushFromMaterial( shieldbreakerMaterial );
		}
	}

	void SetIconScalarParameter( FName key, float32 value )
	{
		shieldbreakerMaterial.SetScalarParameterValue( key, value );
	}
}