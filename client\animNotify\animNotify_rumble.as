UCLASS()
class UAnimNotify_Rumble : UAnimNotify
{
	UPROPERTY( EditAnywhere )
	UForceFeedbackEffect forceFeedback;

	UFUNCTION( BlueprintOverride )
	bool Notify( USkeletalMeshComponent MeshComp, UAnimSequenceBase Animation,
				 FAnimNotifyEventReference EventReference ) const
	{
		if ( !IsClient() )
			return false;
		Client_PlayRumble( forceFeedback );
		return true;
	}
}

UCLASS()
class UAnimNotify_Rumble_Light : UAnimNotify
{
	UFUNCTION( BlueprintOverride )
	bool Notify( USkeletalMeshComponent MeshComp, UAnimSequenceBase Animation,
				 FAnimNotifyEventReference EventReference ) const
	{
		if ( !IsValid( TempAssets() ) || !IsClient() )
			return false;
		Client_PlayDefaultRumble_Light();
		return true;
	}
}

UCLASS()
class UAnimNotify_Rumble_Medium : UAnimNotify
{
	UFUNCTION( BlueprintOverride )
	bool Notify( USkeletalMeshComponent MeshComp, UAnimSequenceBase Animation,
				 FAnimNotifyEventReference EventReference ) const
	{
		if ( !IsValid( TempAssets() ) || !IsClient() )
			return false;
		Client_PlayDefaultRumble_Medium();
		return true;
	}
}

UCLASS()
class UAnimNotify_Rumble_Heavy : UAnimNotify
{
	UFUNCTION( BlueprintOverride )
	bool Notify( USkeletalMeshComponent MeshComp, UAnimSequenceBase Animation,
				 FAnimNotifyEventReference EventReference ) const
	{
		if ( !IsValid( TempAssets() ) || !IsClient() )
			return false;
		Client_PlayDefaultRumble_Heavy();
		return true;
	}
}