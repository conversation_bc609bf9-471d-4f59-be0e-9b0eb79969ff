UCLASS(Abstract)
class UAS_RadialHealthWidget : UUserWidgetDefault
{
	UPROPERTY(BlueprintReadOnly)
	UMaterialInstance progressBarMaterial;

	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget) )
	UImage progressBar;
	
	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget) )
	UImage progressBarBG;
	
	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget) )
	UImage progressBarRegen;

	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget) )
	UCanvasPanel panel;

	UPROPERTY()
	FVector2D barSize;

	FVector2D defaultBarSize;
	FVector2D currentBarSize;

	float32 curFillFrac = 1.0f;
	float32 curHealthFrac = 0.0f;
	float32 curRegenFrac = 0.0f;

	UFUNCTION(BlueprintOverride)
	void PreConstruct(bool IsDesignTime)
	{
		FSlateBrush newBrush = GetNewBrushThatIsCopyOf( progressBar.GetBrush() );
		newBrush.ResourceObject = progressBarMaterial;
		progressBar.SetBrush( newBrush );

		newBrush = GetNewBrushThatIsCopyOf( progressBarBG.GetBrush() );
		newBrush.ResourceObject = progressBarMaterial;
		progressBarBG.SetBrush( newBrush );

		newBrush = GetNewBrushThatIsCopyOf( progressBarRegen.GetBrush() );
		newBrush.ResourceObject = progressBarMaterial;
		progressBarRegen.SetBrush( newBrush );

		defaultBarSize = barSize;
		SetWidgetToSize( defaultBarSize );
	}

	UFUNCTION()
	void SetHealth( float32 newHealthFrac )
	{
		curHealthFrac 	= newHealthFrac;
		float32 Fill 		= curFillFrac * curHealthFrac;

		//anything that's not zero should show a sliver of health
		const float MIN_FILL_DISPLAY = 0.005;
		if ( Fill > 0 )
			Fill = Math::Max( Fill, MIN_FILL_DISPLAY );

		progressBar.GetDynamicMaterial().SetScalarParameterValue( n"Fill", Fill );
	}

	void SetBarColor( FLinearColor inBarColor )
	{
		progressBar.SetColorAndOpacity( inBarColor );
	}

	void SetMaxHealthFrac( float32 newMaxFrac )
	{
		curFillFrac = newMaxFrac;
		progressBar.GetDynamicMaterial().SetScalarParameterValue( n"Fill", curFillFrac * curHealthFrac );
		progressBarRegen.GetDynamicMaterial().SetScalarParameterValue( n"Fill", curFillFrac * curRegenFrac );
		progressBarBG.GetDynamicMaterial().SetScalarParameterValue( n"Fill", curFillFrac );

		SetPanelClippingFrac( curFillFrac );
	}

	void SetBarSize( FVector2D size )
	{
		currentBarSize = size;

		UCanvasPanelSlot progressBarSlot = Cast<UCanvasPanelSlot>( progressBar.Slot );
		progressBarSlot.SetSize( currentBarSize );

		UCanvasPanelSlot progressBarBGSlot = Cast<UCanvasPanelSlot>( progressBarBG.Slot );
		progressBarBGSlot.SetSize( currentBarSize );

		UCanvasPanelSlot progressBarRegenSlot = Cast<UCanvasPanelSlot>( progressBarRegen.Slot );
		progressBarRegenSlot.SetSize( currentBarSize );
	}

	void SetPanelClippingFrac( float clippingFrac )
	{
		FVector2D newClippingSize = FVector2D( currentBarSize.X * clippingFrac, currentBarSize.Y );
		UCanvasPanelSlot slot = Cast<UCanvasPanelSlot>( panel.Slot );
		slot.SetSize( newClippingSize );
	}

	UFUNCTION()
	void SetHealthRegen( float32 newFrac )
	{
		curRegenFrac = newFrac;
		progressBarRegen.GetDynamicMaterial().SetScalarParameterValue( n"Fill", curFillFrac * curRegenFrac );
	}

	UFUNCTION()
	void SetWidgetToSize( FVector2D size )
	{
		currentBarSize = size;
		SetBarSize( currentBarSize );
		SetPanelClippingFrac( curFillFrac );
	}

	UFUNCTION()
	void ResetWidgetToDefaultSize()
	{
		SetWidgetToSize( defaultBarSize );
	}

	UFUNCTION()
	void CompressWidgetToFrac( float perc )
	{
		SetWidgetToSize( FVector2D( defaultBarSize.X * perc, defaultBarSize.Y ) );
	}
}