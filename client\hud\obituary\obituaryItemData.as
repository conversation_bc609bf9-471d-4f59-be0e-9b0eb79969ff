UCLASS( NotBlueprintable )
class UAS_ObituaryItemData : UAS_QueueItemData
{
	private FText text;

	// This data is explicitly used for loot pick ups to ensure that we aren't overloading the feed
	private TOptional<FBackpackItemStruct> optBackpackItem;

	UAS_ObituaryItemData( TSubclassOf<UAS_QueueItemWidget> inWidgetClass, FText inText )
	{
		widgetClass = inWidgetClass;
		text		= inText;
	}

	UAS_ObituaryItemData( TSubclassOf<UAS_QueueItemWidget> inWidgetClass, FText inText, FBackpackItemStruct inBackpackItem )
	{
		widgetClass		= inWidgetClass;
		text			= inText;
		optBackpackItem = inBackpackItem;
	}

	FText GetText() const
	{
		return text;
	}

	TOptional<FBackpackItemStruct> GetOptionalBackpackItem() const
	{
		return optBackpackItem;
	}
}