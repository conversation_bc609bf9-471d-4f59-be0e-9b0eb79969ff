namespace Training
{
	const FName MELEE_DISABLE_CONTEXT = n"TrainingDisableMelee";
	const FName MOUNT_DISABLE_CONTEXT = n"TrainingDisableMount";
	const FName GRENADE_DISABLE_CONTEXT = n"TrainingDisableGrenade";
	const FName ULTIMATE_DISABLE_CONTEXT = n"TrainingDisableUlt";
	const FName TACTICAL_DISABLE_CONTEXT = n"TrainingDisableAbility";

	// TODO: Need to rework how teams are handled
	const int MAX_NUM_PLAYERS = 1;

	int GetFakeEnemyTeamForPlayer( int playerTeam )
	{
		return playerTeam + MAX_NUM_PLAYERS;
	}

	namespace Flags
	{
		const FName FLAG_COMBAT_ONFIRSTPLATFORM = n"OnCombatPlatform";
		const FName FLAG_OPENWORLD_INSIDEBASE 	= n"OpenWorld_InsideBase";

		const FName FLAG_SKIP_FIRST_OBJECTIVE	= n"FlagSkipFirstObjective";
		const FName FLAG_DOOR_INTRO 			= n"Door_Intro";
		const FName FLAG_DOOR_DESTRUCTION_GATE 	= n"Door_DestructionGate";
		const FName FLAG_DOOR_ABILITIES_GATE 	= n"Door_AbilitiesGate";
		const FName FLAG_DOOR_BLIMP_GATE 		= n"Door_BlimpGate";

		const FName FLAG_ZIPLINE_COMBAT_ARMOR	= n"Zipline_Combat_Armor";
		const FName FLAG_ON_COMBAT_ZIPLINE 		= n"OnCombatZipline";
		const FName FLAG_VENDOR_HIDE_ANIM_DONE 	= n"FlagVendorHideAnimDone";
		const FName FLAG_VENDOR_TELEPORT_DONE 	= n"FlagVendorTeleportDone";

		const FName FLAG_DESTRUCTION_READY_BUY 		= n"FlagDestructionReadyBuy";
		const FName FLAG_DESTRUCTION_WEAPON_BOUGHT	= n"FlagDestructionWeaponBought";
		const FName FLAG_DESTRUCTION_AMMO_BOUGHT	= n"FlagDestructionAmmoBought";
		const FName FLAG_OPENWORLD_READY_BUY 		= n"FlagOpenworldReadyBuy";
		const FName FLAG_OPENWORLD_ARMOR_BOUGHT		= n"FlagOpenworldArmorBought";

		const FName FLAG_BLIMP_READY			= n"FlagReadyForBlimp";		//map trigger
		const FName FLAG_BLIMP_FORCE 			= n"FlagForceSpawnBlimp";	//map trigger
		const FName FLAG_BLIMP_CAN_LOOKAT		= n"FlagCanLookAtBlimp";	//map trigger
		const FName FLAG_BLIMP_LOOKAT 			= n"FlagLookAtBlimpSpawn";
		const FName FLAG_BLIMP_SPAWNED 			= n"FlagBlimpSpawned";

		const FName FLAG_TELEPORT_BLIMP_TRIGGER 	= n"FlagTeleportBlimpTrigger";

		const FName FLAG_OPENWORLD_BLUECHEST_OPEN 	= n"FlagOpenWorldBlueChestOpen";
		const FName FLAG_OPENWORLD_REDCHEST_OPEN 	= n"FlagOpenWorldRedChestOpen";
		const FName FLAG_INVENTORY_OPEN				= n"FlagInventoryOpened";
		const FName FLAG_OPENWORLD_AT_VENDOR 		= n"FlagAtOpenWorldVendor";
		const FName FLAG_OPENWORLD_SHIELDBREAKER_READY	= n"FlagOpenWorldShieldbreakerReady";
	}

	namespace Vendors
	{
		const FName VENDOR_INTRO 		= n"Vendor_Intro";
		const FName VENDOR_COMBAT		= n"Vendor_CombatPlatform";
		const FName VENDOR_RARITY		= n"Vendor_RarityPlatform";		
		const FName VENDOR_REVIVE		= n"Vendor_ArmorPlatform";
		const FName VENDOR_DESTRUCTION	= n"Destruction_Vendor";
		const FName VENDOR_ABILITIES 	= n"Vendor_Abilities";
		const FName VENDOR_OPENWORLD 	= n"Vendor_Openworld";
	}

	// Important to categorize and order these, because order is not easy to follow
	// Ordered numbers are not required but are very very nice for dev QOL, please maintain!
	namespace Objectives
	{
		const FName INTRO_01_BASICCONTROLS 		= n"Intro_BasicControls";
		const FName INTRO_02_EXITCAVE 			= n"Intro_ExitCave";
		const FName INTRO_03_MEETFLYNN 			= n"Intro_MeetFlynn";
		const FName INTRO_04_GOTOCOMBAT 		= n"Intro_GoToCombat";
		const FName INTRO_HINT_CAVEMANTLE 		= n"Intro_CaveMantle";
		const FName INTRO_HINT_OUTDOORMANTLE 	= n"Intro_OutdoorMantle";
		const FName INTRO_HINT_JUMP 			= n"Intro_JumpHint";
		const FName INTRO_HINT_CROUCH 			= n"Intro_CrouchHint";
		const FName INTRO_HINT_SPRINT 			= n"Intro_SprintHint";

		const FName COMBAT_HINT_GOTOFIRSTPLATFORM 		= n"Combat_GoToFirstPlatform";
		const FName COMBAT_HINT_RETURNTOFIRSTPLATFORM 	= n"Combat_ReturnToFirstPlatform";
		const FName COMBAT_01_DAMAGEDUMMIES 			= n"Combat_DamageDummies";
		const FName COMBAT_02_DAMAGEDUMMIES_WITHSCOPE	= n"Combat_DamageDummies_WithScope";
		const FName COMBAT_03_DAMAGEDUMMIES_WITHGRENADE = n"Combat_DamageDummies_WithGrenade";
		const FName COMBAT_HINT_GOTORARITYPLATFORM 		= n"Combat_GoToRarityPlatform";
		const FName COMBAT_04_WEAPONRARITY 				= n"Combat_PickUpRarityWeapon";
		const FName Combat_05_GOTOARMORPLATFORM			= n"Combat_GoToArmorPlatform";
		//const FName COMBAT_05_PICKUPARMOR				= n"Combat_PickUpArmor";
		const FName COMBAT_06_REVIVEFRIENDLY			= n"Combat_ReviveFriendly";
		//const FName COMBAT_06_DIEWITHARMOR				= n"Combat_DieWithArmor";
		const FName COMBAT_07_FALLDAMAGE				= n"Combat_TakeFallDamage";
		const FName COMBAT_08_GOTODESTRUCTION			= n"Combat_GoToDestruction";

		
		const FName DESTRUCTION_HINT_BREAKWALLS				= n"Destruction_BreakWallsHint";
		const FName DESTRUCTION_01_TALKTOVENDOR				= n"Destruction_TalkToVendor_01";
		const FName DESTRUCTION_02_HARVESTORE				= n"Destruction_HarvestOre";
		const FName DESTRUCTION_03_BUYROCKET				= n"Destruction_BuyRocket";
		const FName DESTRUCTION_04_DESTROYWALLS				= n"Destruction_DestroyWalls_01";
		const FName DESTRUCTION_HINT_BUYMOREROCKETS			= n"Destruction_BuyMoreRockets";
		const FName DESTRUCTION_05_DESTROYREINFORCEDWALL	= n"Destruction_DestroyOneReinforcedWall";
		const FName DESTRUCTION_06_HARVESTORE				= n"Destruction_HarvestOre_02";
		const FName DESTRUCTION_07_BUYMULTILOCK				= n"Destruction_BuyPurpleRaidTool";
		const FName DESTRUCTION_08_DESTROYWALLS_02			= n"Destruction_DestroyWalls_02";
		const FName DESTRUCTION_HINT_BUYMOREROCKETS_02		= n"Destruction_BuyMoreRockets_02";
		const FName DESTRUCTION_09_GOTOABILITIES			= n"Destruction_GoToAbilities";
	
		const FName ABILITIES_01_BREAKWALLS_TAC			= n"Abilities_BreakWalls_Tactical";
		const FName ABILITIES_02_LEAP_TAC				= n"Abilities_Leap_Tactical";
		const FName ABILITIES_HINT_RECHARGE				= n"Abilities_RechargeHint";
		const FName ABILITIES_03_BREAKWALLS_ULT			= n"Abilities_BreakWalls_Ultimate";
		const FName ABILITIES_04_GOTOZEPPELIN			= n"Abilities_GoToZeppelin";

		const FName OPENWORLD_01_INTRO					= n"OpenWorld_Intro";
		const FName OPENWORLD_02_REINFORCEWALLS			= n"OpenWorld_ReinforceWalls";
		const FName OPENWORLD_03_GETONMOUNT				= n"OpenWorld_GetOnMount";
		const FName OPENWORLD_04_GEARUP					= n"OpenWorld_GearUp";
		const FName OPENWORLD_05_GETMOREARMOR			= n"OpenWorld_GetMoreArmor";
		const FName OPENWORLD_06_OPENINVENTORY			= n"OpenWorld_OpenInventory";
		const FName OPENWORLD_07_BUYARMOR				= n"OpenWorld_ArmorFromVendor";
		const FName OPENWORLD_08_GETSHIELDBREAKER		= n"OpenWorld_GetShieldBreaker";
		const FName OPENWORLD_09_STARTRAID				= n"OpenWorld_RaidEnemyBase";
		const FName OPENWORLD_10_PLANTONGENERATOR		= n"OpenWorld_PlantOnGenerator";
		const FName OPENWORLD_11_PLANTFINALGENERATOR	= n"OpenWorld_PlantOnFinalGenerator";
	}

	enum ERegenerateDestructiblesStyle
	{
		SURVIVING_TO_IRON,
		DESTROYED_ONLY,
		DESTROYED_TO_IRON,
		ALL_TO_IRON,
	}
}

enum ETrainingObjectiveCategory
{
	DEFAULT = 0,
	// Will not replace default objective, but will show this over default while it's active.
	TEMPORARY_HINT = 1,
}

// Make sure the base is hellmouth, spawn it in late
// Next prio: get the base spawn working, verify you can do all the base interactions

enum ETrainingRespawnStyle
{
	MODE_DEFAULT = 0,
	FADE_OUT_IN = 1,
	RESPAWN_SELECTION = 2,
	TEACH_ARMOR_BREAK = 3,
}

UCLASS( Abstract )
class AAS_TrainingSession_RaidMode : ANCDefaultActor
{
	UPROPERTY()
	TSubclassOf< AAS_TrainingSession_StartPoint_Shore > shoreStartPointClass;

	UPROPERTY()
	TSubclassOf< AAS_TrainingSession_StartPoint_Headwall > headwallStartPointClass;

	UPROPERTY()
	TSubclassOf< AAS_TrainingSession_SharedData > sharedDataClass;

	AAS_TrainingSession_SharedData sharedData;

	UPROPERTY()
	private FNCNetEntityHandle net_sharedDataHandle;

	UPROPERTY()
	private FNCNetEntityHandle net_cubicleDataHandle;

	UPROPERTY( NotVisible )
	private FNCNetInt net_respawnStyle;
	default net_respawnStyle.SetNetValue( int( ETrainingRespawnStyle::FADE_OUT_IN ) );

	float32 respawnTime = 0.0;

	// Should be placed in the level eventually
	private AAS_TrainingSession_Cubicle cubicle;

	UFUNCTION(BlueprintOverride)
	void ServerBeginPlay()
	{
		sharedData = Cast<AAS_TrainingSession_SharedData>( Server_SpawnEntity( sharedDataClass, GetOwnerPlayer() ) );
		net_sharedDataHandle.SetNetValue( sharedData );

		cubicle = Cast<AAS_TrainingSession_Cubicle>( Server_SpawnEntity( AAS_TrainingSession_Cubicle::StaticClass(), nullptr ) );
		net_cubicleDataHandle.SetNetValue( cubicle );

		DataLayer_SetFirstHalfRuntimeState( EDataLayerRuntimeState::Activated );

		// STUBBED until functionality comes online
		SetCubicleData( cubicle.GetCubicleData() );

		// TODO: Can this move to its own gameplay system
		ProcessVendors();

		// TODO: Temporarily putting here, should probably live in the session
		AAS_PlayerEntity asPlayer = GetOwnerPlayer();
		SetTeamEliminated( Training::GetFakeEnemyTeamForPlayer( asPlayer.GetTeam() ), false );
	}

	void SetCubicleData( FTrainingSessionCubicleData inData )
	{
		sharedData.SetCubicleData( inData );
		
		AAS_PlayerEntity asPlayer = GetOwnerPlayer();

		// Supporting old training until we transition over
		if ( GameModeDefaults().Training_DoOldTraining )
		{
			AAS_TrainingSession_StartPoint_Shore shoreStartPoint = Cast<AAS_TrainingSession_StartPoint_Shore>( Server_SpawnEntity( shoreStartPointClass, asPlayer ) );
			shoreStartPoint.SetSessionSharedData( sharedData );
			shoreStartPoint.ServerSetOwnerSession( this );
			shoreStartPoint.ServerStartRunningFromHere( asPlayer );
			shoreStartPoint.ServerRun( asPlayer );
		}
		else
		{
			AAS_TrainingSession_StartPoint_Headwall headwallStartPoint = Cast<AAS_TrainingSession_StartPoint_Headwall>( Server_SpawnEntity( headwallStartPointClass, asPlayer ) );
			headwallStartPoint.SetSessionSharedData( sharedData );
			headwallStartPoint.ServerSetOwnerSession( this );
			headwallStartPoint.ServerStartRunningFromHere( asPlayer );
			headwallStartPoint.ServerRun( asPlayer );
		}
	}

	ETrainingRespawnStyle GetRespawnStyle()
	{
		return ETrainingRespawnStyle( int( net_respawnStyle ) );
	}

	void ServerSetRespawnStyle( ETrainingRespawnStyle newStyle )
	{
		net_respawnStyle.SetNetValue( newStyle );
	}





	UFUNCTION(BlueprintOverride)
	void ClientBeginPlay()
	{
		net_sharedDataHandle.OnReplicated().AddUFunction( this, n"OnSharedDataSet" );
		net_cubicleDataHandle.OnReplicated().AddUFunction( this, n"OnCubicleDataSet" );
		net_respawnStyle.OnReplicated().AddUFunction( this, n"OnRespawnStyleChanged" );

		ScriptCallbacks().RegisterFlag( Training::Flags::FLAG_VENDOR_HIDE_ANIM_DONE );
		ScriptCallbacks().RegisterFlag( Training::Flags::FLAG_VENDOR_TELEPORT_DONE );
	}

	UFUNCTION()
	void OnSharedDataSet( AActor oldActor, AActor newActor )
	{
		sharedData = Cast<AAS_TrainingSession_SharedData>( newActor );
		ClientUpdateSharedData();
	}

	UFUNCTION()
	void OnCubicleDataSet( AActor oldActor, AActor newActor )
	{
		cubicle = Cast<AAS_TrainingSession_Cubicle>( newActor );
		ClientUpdateSharedData();
	}

	// Handle shared data / start points / cubicle getting initialized in any order
	bool clientHasCubicleDataBeenSet = false;
	FTimerHandle waitForStreamingHandle;

	UFUNCTION()
	void RetryClientUpdateSharedDataThread( UNCCoroutine co, float delay )
	{
		co.Wait( delay );
		ClientUpdateSharedData();
	}

	bool debugDelay = false;
	int numHackyTries = 0;
	UFUNCTION()
	void ClientUpdateSharedData()
	{
		if ( IsValid( sharedData ) && IsValid( cubicle ) )
		{
			clientHasCubicleDataBeenSet = true;
			cubicle.GatherActors();
			sharedData.SetCubicleData( cubicle.GetCubicleData() );

			for ( AAS_TrainingSession_StartPoint startPoint : startPointsMissingSharedData )
			{
				startPoint.SetSessionSharedData( sharedData );
			}

			startPointsMissingSharedData.Empty();

			// TODO: Can this move to its own gameplay system
			ProcessVendors();
		}
	}

	// Handle shared data / start points / cubicle getting initialized in any order
	TArray<AAS_TrainingSession_StartPoint> startPointsMissingSharedData;
	void OnClientStartPointCreated( AAS_TrainingSession_StartPoint startPoint )
	{
		if ( !clientHasCubicleDataBeenSet )
		{
			
			startPointsMissingSharedData.Add( startPoint );
			return;
		}

		startPoint.SetSessionSharedData( sharedData );
	}

	UFUNCTION()
	void OnRespawnStyleChanged( int oldValue, int newValue )
	{
		ETrainingRespawnStyle respawnStyle = ETrainingRespawnStyle( newValue );

		switch( respawnStyle )
		{
			case ETrainingRespawnStyle::TEACH_ARMOR_BREAK:
			{
				AAS_HUD hud = GetLocalHUD();
				if ( IsValid( hud ) )
				{
					UAS_RespawnMenuSystem respawnSystem = RespawnMenuSystem();
					respawnSystem.SetDoesDeathRecapAutoClose();
					respawnSystem.ClearShouldSkipDeathRecap();
					respawnSystem.SetDeathRecapDuration( 10.0 );
					respawnSystem.ClearCanInputChangeRespawnMenuTabs();
					// TODO: Disable navigate back
				}
			}
			break;

			default:
			{
				AAS_HUD hud = GetLocalHUD();
				if ( IsValid( hud ) )
				{
					UAS_RespawnMenuSystem respawnSystem = RespawnMenuSystem();
					respawnSystem.SetDoesDeathRecapAutoClose();
					respawnSystem.SetShouldSkipDeathRecap();
					respawnSystem.SetDeathRecapDuration( 5.0 );
					respawnSystem.SetCanInputChangeRespawnMenuTabs();
					// TODO: enable navigate back
				}
			}
			break;
		}
	}

	void RegenerateTaggedDestructiblesAndResources_SetDestructiblesToIron()
	{
		Thread( this, n"RegenerateTaggedDestructibles_Thread", sharedData.destruction_destructiblesToRegenerate, Training::ERegenerateDestructiblesStyle::ALL_TO_IRON, 1 );
		Thread( this, n"RegenerateTaggedResources_Thread", sharedData.destruction_resourcesToRegenerate );
	}

	void Destruction_RegenerateTaggedDestructiblesAndResources()
	{
		Thread( this, n"RegenerateTaggedDestructibles_Thread", sharedData.destruction_destructiblesToRegenerate, Training::ERegenerateDestructiblesStyle::DESTROYED_ONLY, 1 );
		Thread( this, n"RegenerateTaggedResources_Thread", sharedData.destruction_resourcesToRegenerate );
	}

	void Abilities_RegenerateDestructibles()
	{
		Thread( this, n"RegenerateTaggedDestructibles_Thread", sharedData.abilities_destructiblesToRegenerate, Training::ERegenerateDestructiblesStyle::DESTROYED_ONLY, 1 );
	}

	void CancelRegenerateTaggedDestructiblesAndResources()
	{
		stopRegeneratingTaggedDestructiblesSignal.Emit();
		stopRegeneratingTaggedResourcesSignal.Emit();
	}

	FNCCoroutineSignal stopRegeneratingTaggedDestructiblesSignal;
	FNCCoroutineSignal stopRegeneratingTaggedResourcesSignal;
	const int MAX_NUM_TO_REGENERATE_PER_FRAME = 2;
	const float REGENERATION_AMORTIZE_DELAY = 0.033;

	
	UFUNCTION()
	void RegenerateTaggedDestructibles_Thread( UNCCoroutine co, TArray<ANCDestructible> destructibles, Training::ERegenerateDestructiblesStyle style, float32 destructiblesFrac = 1 )
	{
		stopRegeneratingTaggedDestructiblesSignal.Emit();
		co.EndOn( this, stopRegeneratingTaggedDestructiblesSignal );

		co.Wait( 2.0 );

		int numDestructibles = destructibles.Num();
		int currentIndex = 0;
		int numDestructiblesToRegenerate = numDestructibles;
		int numRegeneratedDestructibles = 0;

		// If we shouldn't regen every destructible, adjust count to regen
		if ( destructiblesFrac < 1 )
		{
			numDestructiblesToRegenerate = Math::FloorToInt( float( numDestructibles ) * destructiblesFrac );
		}

		while ( currentIndex < numDestructibles && numRegeneratedDestructibles < numDestructiblesToRegenerate )
		{
			int numDestructiblesRegeneratedThisFrame = 0;
			for( ; currentIndex < numDestructibles; currentIndex++ )
			{
				// At the top so that currentIndex++ gets hit before the break
				if ( numDestructiblesRegeneratedThisFrame == MAX_NUM_TO_REGENERATE_PER_FRAME )
				{
					break;
				}

				bool didRegenerate = false;
				ANCDestructible destructible = destructibles[ currentIndex ];
				if ( !IsValid( destructible ) )
				{
					continue;
				}

				switch( style )
				{
					case Training::ERegenerateDestructiblesStyle::SURVIVING_TO_IRON:
					{
						// TODO: Handle if no walls are left standing
						if ( destructible.GetCurrentHealth() > 0 && !destructible.IsTransitioningCategories() )
						{
							destructible.StartDestructCategoryTransition_Server( GameplayTags::Destruction_Category_Iron, 3, true );
							didRegenerate = true;
						}
						break;
					}
					case Training::ERegenerateDestructiblesStyle::DESTROYED_ONLY:
					{
						if ( destructible.GetCurrentHealth() <= 0 && !destructible.IsTransitioningCategories() )
						{
							FNCDestructibleCategory category = destructible.GetDestructibleCategory();
							destructible.StartDestructCategoryTransition_Server( category.DestructibleCategoryID, 3, true );
							didRegenerate = true;
						}
						break;
					}
					case Training::ERegenerateDestructiblesStyle::DESTROYED_TO_IRON:
					{
						if ( destructible.GetCurrentHealth() <= 0 && !destructible.IsTransitioningCategories() )
						{
							destructible.StartDestructCategoryTransition_Server( GameplayTags::Destruction_Category_Iron, 3, true );
							didRegenerate = true;
						}
						break;
					}
					case Training::ERegenerateDestructiblesStyle::ALL_TO_IRON:
					{
						destructible.StartDestructCategoryTransition_Server( GameplayTags::Destruction_Category_Iron, 3, true );
						didRegenerate = true;
						break;
					}
				}

				if ( didRegenerate )
				{
					numDestructiblesRegeneratedThisFrame++;
					numRegeneratedDestructibles++;
				}
			}

			co.Wait( REGENERATION_AMORTIZE_DELAY );
		}
	}

	UFUNCTION()
	void RegenerateTaggedResources_Thread( UNCCoroutine co, TArray<AAS_ResourceNode> resources )
	{
		stopRegeneratingTaggedResourcesSignal.Emit();
		co.EndOn( this, stopRegeneratingTaggedResourcesSignal );

		// Resources
		int numResources = resources.Num();
		int currentIndex = 0;
		while ( currentIndex < numResources )
		{
			int numResourcesRegenerated = 0;
			for( ; currentIndex < numResources; currentIndex++ )
			{
				AAS_ResourceNode resource = resources[ currentIndex ];
				if ( !IsValid( resource ) )
				{
					continue;
				}
				int entryID = resource.EditorAssetEntryToInt( resource.assetEntry );
				float32 maxResource = resource.GetResourceAmountFromEnum( entryID );
				if ( resource.GetResourceAmount() < maxResource )
				{
					resource.TryReGrowResourceNode();

					numResourcesRegenerated++;

					if ( numResourcesRegenerated == MAX_NUM_TO_REGENERATE_PER_FRAME )
					{
						break;
					}
				}
			}
			co.Wait( REGENERATION_AMORTIZE_DELAY );
		}
	}

	void TrackAndRegenerateResourcesIfTooFewAlive( TArray<AAS_ResourceNode> resources, float32 minAliveFrac )
	{
		Thread( this, n"TrackAndRegenerateResourcesIfTooFewAliveThread", resources, minAliveFrac );
	}

	void StopTrackingAndRegeneratingResources()
	{
		endTrackAndRegenerateResourcesIfTooFewAliveSignal.Emit();
	}

	FNCCoroutineSignal endTrackAndRegenerateResourcesIfTooFewAliveSignal;
	UFUNCTION()
	void TrackAndRegenerateResourcesIfTooFewAliveThread( UNCCoroutine co, TArray<AAS_ResourceNode> resources, float32 minAliveFrac )
	{
		endTrackAndRegenerateResourcesIfTooFewAliveSignal.Emit();
		co.EndOn( this, endTrackAndRegenerateResourcesIfTooFewAliveSignal );

		const int numResources = resources.Num();
		if ( numResources < 0 )
		{
			return;
		}

		while ( true )
		{
			int numResourcesToRegenerate = 0;
			for( AAS_ResourceNode resource : resources )
			{
				if ( !IsValid( resource ) )
				{
					continue;
				}
				
				int entryID = resource.EditorAssetEntryToInt( resource.assetEntry );
				float32 maxResource = resource.GetResourceAmountFromEnum( entryID );
				if ( resource.GetResourceAmount() < maxResource )
				{
					numResourcesToRegenerate++;
				}
			}

			const float32 aliveFrac = float32( numResources - numResourcesToRegenerate ) / float32( numResources );
			if ( aliveFrac <= minAliveFrac )
			{
				Thread( this, n"RegenerateTaggedResources_Thread", resources );
			}
			co.Wait( 3 );
		}


	}

	void TrackAndRegenerateDestructiblesIfTooFewAlive( TArray<ANCDestructible> destructibles, Training::ERegenerateDestructiblesStyle regenStyle, float minAllowedAliveFrac, float regenerateToFrac )
	{
		Thread( this, n"RegenerateDestructiblesIfTooFewAlive_Thread", destructibles, regenStyle, minAllowedAliveFrac, regenerateToFrac );
	}

	void StopTrackingAndRegeneratingDestructiblesIfTooFewAlive()
	{
		endRegenerateDestructiblesIfTooFewAliveSignal.Emit();
	}

	FNCCoroutineSignal endRegenerateDestructiblesIfTooFewAliveSignal;
	TArray<ANCDestructible> trackedDestructiblesForRegen;
	UFUNCTION()
	private void RegenerateDestructiblesIfTooFewAlive_Thread( UNCCoroutine co, TArray<ANCDestructible> destructibles, Training::ERegenerateDestructiblesStyle regenStyle, float minAllowedAliveFrac, float regenerateToFrac )
	{
		endRegenerateDestructiblesIfTooFewAliveSignal.Emit();
		co.EndOn( this, endRegenerateDestructiblesIfTooFewAliveSignal );
		co.OnCoroutineEnd.AddUFunction( this, n"OnRegenerateDestructiblesIfTooFewAliveThreadEnd" );

		trackedDestructiblesForRegen = destructibles;
		int numDestructibles = trackedDestructiblesForRegen.Num();

		if ( numDestructibles == 0 )
		{
			return;
		}

		for( int i = 0; i < numDestructibles; i++ )
		{
			co.EndOnDestroyed( trackedDestructiblesForRegen[ i ] );
		}

		UNCDestructionManager::Get().OnDestructibleDestroyed_Server.AddUFunction( this, n"OnDestructibleDestroyed_TrackForRegeneration" );

		while ( true )
		{
			float aliveFrac = 0;
			int numAlive = 0;
			for( int i = 0; i < numDestructibles; i++ )
			{
				if ( trackedDestructiblesForRegen[ i ].GetCurrentHealth() > 0 )
				{
					numAlive++;
				}
			}

			aliveFrac = float32( numAlive ) / float32( numDestructibles );

			if ( aliveFrac < minAllowedAliveFrac )
			{
				Thread( this, n"RegenerateTaggedDestructibles_Thread", trackedDestructiblesForRegen, regenStyle, regenerateToFrac );
			}

			co.Wait( n"ReEvaluateTrackedDestructiblesForRegen" );
		}
	}

	UFUNCTION()
	private void OnRegenerateDestructiblesIfTooFewAliveThreadEnd( FNCCoroutineEndParams params )
	{
		UNCDestructionManager::Get().OnDestructibleDestroyed_Server.Unbind( this, n"OnDestructibleDestroyed_TrackForRegeneration" );
	}

	UFUNCTION()
	void OnDestructibleDestroyed_TrackForRegeneration( FNCDestructibleDestroyedContext_Server destroyedContext )
	{
		if ( trackedDestructiblesForRegen.Contains( destroyedContext.Destructible ) )
		{
			Signal( n"ReEvaluateTrackedDestructiblesForRegen" );
		}
	}

	void ServerSetRespawnTime( float32 newTime )
	{
		respawnTime = newTime;
	}

	//////////////////////////////////////////////////////////////
	// Active speaking vendor selection -- wish this could be moved to a gameplay system
	//////////////////////////////////////////////////////////////
	
	TArray< AAS_Vendor > dialogueVendors;
	TMap< FName, AAS_Vendor > actorTagToVendor;

	private AAS_Vendor cl_requestedVendorToSpeak;


	UFUNCTION()
	void ProcessVendors()
	{
		TArray<AAS_ObjectiveMarkerTarget> markers = Objectives().GetObjectiveMarkersForName( n"OpenWorld_Vendor" );		
		AAS_ObjectiveMarkerTarget openWorldMarker = markers.Num() > 0 ? markers[0] : nullptr;

		GetAllActorsOfClass( dialogueVendors );

		for( AAS_Vendor vendor : dialogueVendors )
		{
			// Multiple tags for a single vendor = multiple entries. Gross, but tags are used by diff systems to ID actors. Soooooo yeah.
			TArray<FName> tags = vendor.Tags;
			if ( tags.Num() > 0 )
			{
				for( FName tag : tags )
				{
					actorTagToVendor.Add( tag, vendor );
				}
			}
			else if ( IsValid(openWorldMarker) && Distance2D( openWorldMarker.GetActorLocation(), vendor.GetActorLocation() ) < 500 )
			{
				ScriptAssert( !actorTagToVendor.Contains(Training::Vendors::VENDOR_OPENWORLD), "more than one?" );

				vendor.Tags.Add(Training::Vendors::VENDOR_OPENWORLD);
				actorTagToVendor.Add( Training::Vendors::VENDOR_OPENWORLD, vendor );
			}
		}
	}

	FNCCoroutineSignal endSignalClientSetActiveVendor;
	void ClientSetActiveVendor( FName vendorName, float32 fxLifeTime = 0.0, FVector optionalDirFxLoc = FVector::ZeroVector, bool instantHide = true, bool instantUnhide = false, EVendorActivity hideActivity = EVendorActivity::INVALID, EVendorActivity showActivity = EVendorActivity::INVALID )
	{
		ScriptAssert( IsClient(), "CLIENT_ONLY" );
		ScriptAssert( actorTagToVendor.Contains( vendorName ), f"vendorName [{vendorName}] does not exist" );
		
		AAS_Vendor oldVendor = cl_requestedVendorToSpeak;
		AAS_Vendor newVendor = actorTagToVendor[ vendorName ];
		if ( oldVendor == newVendor )
			return;

		endSignalClientSetActiveVendor.Emit();
		
		cl_requestedVendorToSpeak = actorTagToVendor[ vendorName ];

		if ( IsValid(oldVendor) )		
		{
			UAnimMontage montage = oldVendor.FSM_Component.ForceHide(instantHide, true, hideActivity );

			FVector oldLoc 	= oldVendor.vendorMesh.GetWorldLocation() + FVector(0,0,100);
			FVector newLoc 	= optionalDirFxLoc != FVector::ZeroVector ? optionalDirFxLoc : newVendor.vendorMesh.GetWorldLocation() + FVector(0,0,300);
			FVector dir 	= newLoc - oldLoc;
			dir.Normalize();
			
			if ( instantHide )
			{
				ScriptCallbacks().FlagSet( Training::Flags::FLAG_VENDOR_HIDE_ANIM_DONE );				
			}
			else if ( IsValid(montage) )
			{
				ScriptCallbacks().FlagClear( Training::Flags::FLAG_VENDOR_HIDE_ANIM_DONE );

				Anim().animNotifyCallbacks.OnAnimEnd( oldVendor.vendorMesh, montage ).AddUFunction( this, n"OnVendorTeleportAnimDone");				
			}

			Thread( this, n"PlayTeleportDirFXAndShowNewVendor", oldLoc, dir, fxLifeTime, newVendor, instantUnhide, showActivity );
		}
		else
		{
			newVendor.FSM_Component.UnforceHide(instantUnhide, true, showActivity );
		}

		// Gross, but simple. Update vendor actor in any upcoming training lines when vendor switches location
		TArray<UAS_DialogueQueueEntry> upcomingEntries = Dialogue().dialogueQueue;
		FGameplayTagContainer trainingContainer = UNCUtils::GetGameplayTagChildren( GameplayTags::Audio_VO_Training );
		for( UAS_DialogueQueueEntry entry : upcomingEntries )
		{
			if ( trainingContainer.HasTag( entry.dialogueAsset.index ) )
			{
				if ( IsValid( entry.actor ) && entry.actor.IsA( AAS_Vendor::StaticClass() ) )
				{
					entry.actor = cl_requestedVendorToSpeak;
				}
			}
		}
	}

	UFUNCTION()
	private void PlayTeleportDirFXAndShowNewVendor( UNCCoroutine co, FVector oldLoc, FVector dir, float32 fxLifeTime, AAS_Vendor newVendor, bool instantUnhide, EVendorActivity showActivity)
	{
		co.EndOn( this, endSignalClientSetActiveVendor );
		
		co.FlagWait( Training::Flags::FLAG_VENDOR_HIDE_ANIM_DONE );

		UNiagaraComponent fx = Client_SpawnEffectAtLocation_OneShot( sharedData.vendorTeleportDirFX, oldLoc, dir.Rotation() );			
		fx.SetFloatParameter( n"Lifetime", fxLifeTime );

		ScriptCallbacks().FlagClear( Training::Flags::FLAG_VENDOR_TELEPORT_DONE );
		co.Wait( fxLifeTime );
		ScriptCallbacks().FlagSet( Training::Flags::FLAG_VENDOR_TELEPORT_DONE );

		newVendor.FSM_Component.UnforceHide(instantUnhide, true, showActivity );
	}

	UFUNCTION()
	private void OnVendorTeleportAnimDone(USkeletalMeshComponent meshComp, UAnimSequenceBase animation, 
														const UAS_AnimNotifyTrackerBase notifyTracker)
	{
		ScriptCallbacks().FlagSet( Training::Flags::FLAG_VENDOR_HIDE_ANIM_DONE );
	}

	AAS_Vendor GetBestVendorToSpeak()
	{
		ScriptAssert( IsClient(), "CLIENT_ONLY" );

		return cl_requestedVendorToSpeak;
	}

	void DataLayer_SetFirstHalfRuntimeState( EDataLayerRuntimeState newState )
	{
		AAS_GameModeBase mode = GameModeDefaults();
		AAS_GameModeBase_RaidMode_Training_New trainingMode = Cast< AAS_GameModeBase_RaidMode_Training_New >( mode );
		if ( IsValid( trainingMode ) )
		{
			UDataLayerManager manager = WorldPartition::GetDataLayerManager();
			if ( IsValid( manager ) )
			{
				manager.SetDataLayerRuntimeState( trainingMode.dataLayer_trainingFirstHalf, newState );
			}
		}
	}
}