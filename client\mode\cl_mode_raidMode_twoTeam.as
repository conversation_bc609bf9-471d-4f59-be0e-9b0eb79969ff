event void FRaidSplashRequest( FText inHeader, FText inSubheader, bool enemyAction );

UCLASS()
class UAS_ClientScript_RaidMode_TwoTeam : UAS_ClientScript_RaidMode
{
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset countdownAsset;

	private int breacherAcquiredVODebounce = 0;
	private int breacherLostVODebounce = 0;

	UFUNCTION( BlueprintOverride )
	void Initialize()
	{
		Super::Initialize();

		ScriptCallbacks().client_onRaidObjectiveStateChanged.AddUFunction( this, n"OnTeamObjectiveStateChanged" );
		GetGameStateEntity_RaidModeTwoTeam().net_overtime.OnReplicated().AddUFunction( this, n"OnOvertimeChanged" );
		ScriptCallbacks().client_onNextPhaseTimeChanged.AddUFunction( this, n"OnNextPhaseTimeChanged" );
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"NotifyOvertime", n"SC_NotifyOvertime" );
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"NotifyTeleportToBase", n"SC_NotifyTeleportToBase" );
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"NotifyOvertimeExtension", n"SC_NotifyOvertimeExtension" );
	}

	UFUNCTION()
	void SC_NotifyTeleportToBase( TArray<FString> args )
	{
		TryTeleportToBaseHint( 8.0 );
	}

	UFUNCTION()
	void SC_NotifyOvertimeExtension( TArray<FString> args )
	{
		float time	   = args[0].ToFloat();
		FText timeText = FText::AsNumber( time, GetNumberFormattingOptions( ERoundingMode::FromZero, true, true, 0, 0 ) );
		localHUD.mainHUDWidget.DisplayGenericMessage( GetLocalizedText( Localization::RaidMode, "overtime_extension", FFormatArgumentValue( timeText ) ), EHUDMessageStyle::SMALL_CENTER );
	}

	UFUNCTION()
	void SC_NotifyOvertime( TArray<FString> args )
	{
		Dialogue().PlayAnnouncerDialogue( GameplayTags::Audio_VO_GameUpdates_Overtime, 0.5 );
	}

	UFUNCTION()
	void SC_RaidLost_Dialogue( TArray<FString> args )
	{
		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( !IsValid( player ) )
			return;

		FGameplayTag alias = GameplayTags::Audio_VO_GameUpdates_TeamObjective_Raid_Lost;
		Dialogue().PlayAnnouncerDialogue( alias );
	}

	UFUNCTION()
	protected void OnTeamObjectiveStateChanged( int team, int oldValue, int newValue )
	{
#if EDITOR
		if ( System::GetGameTimeInSeconds() < 5 )
			return;
#endif

		int currentTime			 = GetGameTimeMS();
		EObjectiveState newState = EObjectiveState( newValue );
		switch ( newState )
		{
			case EObjectiveState::_count:
			case EObjectiveState::Hidden:
			case EObjectiveState::PickupShieldBreaker:
			case EObjectiveState::PostRaid:
				break;

			case EObjectiveState::ShieldBreakerCrafting:
			{
				bool played								= false;
				AAS_GameModeBase_Raidmode_TwoTeams mode = TwoTeamModeDefaults();
				if ( IsValid( mode ) )
				{
					int myTeamScore	   = GetTeamScore( team );
					int enemyTeamScore = GetTeamScore( GetOtherTeam( team ) );

					if ( myTeamScore <= mode.POINTS_SB_PLANT && enemyTeamScore <= mode.POINTS_SB_PLANT )
					{
						Dialogue().PlayAnnouncerDialogue( GameplayTags::Audio_VO_GameUpdates_ShieldBreakerForming_MP_Both, 0.25 );
						played = true;
					}
					else if ( myTeamScore <= mode.POINTS_SB_PLANT )
					{
						Dialogue().PlayAnnouncerDialogue( GameplayTags::Audio_VO_GameUpdates_ShieldBreakerForming_MP_Enemy, 0.25 );
						played = true;
					}
					else if ( enemyTeamScore <= mode.POINTS_SB_PLANT )
					{
						Dialogue().PlayAnnouncerDialogue( GameplayTags::Audio_VO_GameUpdates_ShieldBreakerForming_MP_You, 0.25 );
						played = true;
					}
				}

				if ( !played )
				{
					FGameplayTag alias = GameplayTags::Audio_VO_GameUpdates_Shieldbreaker_Crafting_AtGeneric;

					TArray<AAS_SBCrafter> allCrafters;
					GetAllActorsOfClass( allCrafters );
					for ( AAS_SBCrafter crafter : allCrafters )
					{
						if ( crafter.GetSBCrafterState() != ESBCrafterState::HIDDEN )
						{
							alias = crafter.locationDialogue;
							break;
						}
					}

					Dialogue().PlayAnnouncerDialogue( alias, 0.25 );
				}
			}
			break;

			case EObjectiveState::ExploreAndGearUp:
			{
				int playingTime	   = GetPlayingTimeMS();
				FGameplayTag alias = GameplayTags::Audio_VO_GameUpdates_StartMatch_Prepare;
				if ( TO_SECONDS( playingTime ) > 10.0 )
					return;

				Dialogue().PlayAnnouncerDialogue( alias, 0.25 );
			}
			break;

			case EObjectiveState::DefensePhase:
			{
				if ( !GetCvarBool( "ScriptDebug.SkipWaitingForPlayers" ) )
				{
					FGameplayTag alias = GameplayTags::Audio_VO_GameUpdates_TeamObjective_DefensePhase;
					Dialogue().PlayAnnouncerDialogue( alias, 0.25 );
				}
			}
			break;
			case EObjectiveState::Raiding:
			{
				if ( breacherAcquiredVODebounce < currentTime )
				{
					breacherAcquiredVODebounce = currentTime + TO_MILLISECONDS( 120.0 );
					FGameplayTag alias		   = GameplayTags::Audio_VO_GameUpdates_TeamObjective_ShieldBreakerAcquired;

					int minScore = TwoTeamModeDefaults().POINTS_SB_PLANT;
					if ( GetTeamScore( GetOtherTeam( team ) ) <= minScore )
					{
						alias = GameplayTags::Audio_VO_GameUpdates_You_ShieldBreaker_Goal_Endgame;
					}

					Dialogue().PlayAnnouncerDialogue( alias );
				}
			}
			break;
			case EObjectiveState::InterceptShieldBreaker:
			{
				if ( breacherLostVODebounce < currentTime )
				{
					breacherLostVODebounce = currentTime + TO_MILLISECONDS( 60.0 );
					FGameplayTag alias	   = GameplayTags::Audio_VO_GameUpdates_EnemyShieldbreaker_Intercept;

					int minScore = TwoTeamModeDefaults().POINTS_SB_PLANT;
					if ( GetTeamScore( team ) <= minScore )
					{
						alias = GameplayTags::Audio_VO_GameUpdates_Enemy_ShieldBreaker_Warning_Endgame;
					}

					Dialogue().PlayAnnouncerDialogue( alias );
				}
			}
			break;
		}

		TryStartCountdownTicking();
	}

	FTimerHandle overtimeAudioHandle;
	FNCCoroutineSignal onOvertimeEndedSignal;

	UFUNCTION()
	void OnOvertimeChanged( bool oldValue, bool newValue )
	{
		System::ClearAndInvalidateTimerHandle( overtimeAudioHandle );

		TryStartCountdownTicking();

		// NEW RAID MESSAGING (WIP)
		if ( GetCvarBool( "ScriptDebug.NewRaidMessaging" ) )
		{
			if ( newValue )
			{
				GetRaidMessagingManager().AddSplashMessage( GetTemplateMessageData( EPriorityMessageTemplate::OVERTIME ) );
				GetRaidMessagingManager().SetPinnedMessage( GetTemplateMessageData( EPriorityMessageTemplate::OVERTIME_LIGHTWEIGHT ) );
			}
			else
			{
				GetRaidMessagingManager().ClearPinnedMessage();
			}
			return;
		}

		if ( newValue )
		{
			Thread( this, n"OvertimeUpdateThread" );
		}
		else
		{
			onOvertimeEndedSignal.Emit();
			PriorityMessage().ClearActiveCenterMessages( EPriorityMessageLevel::ATTACKER );
		}
	}

	UFUNCTION()
	void OvertimeUpdateThread( UNCCoroutine co )
	{
		onOvertimeEndedSignal.Emit();
		co.EndOn( this, onOvertimeEndedSignal );

		PriorityMessage().ClearActiveCenterMessages( EPriorityMessageLevel::ATTACKER );
		PriorityMessage().ClearActiveCenterMessages( EPriorityMessageLevel::DEFENDER );
		AddTemplateMessage( EPriorityMessageTemplate::OVERTIME );
		co.Wait( 5.0 );

		bool firstTime = true;
		UCL_PriorityMessageManager_v2 pmm = PriorityMessage();

		while ( true )
		{
			if ( firstTime || PriorityMessage().GetActiveCenterMessages( EPriorityMessageLevel::ATTACKER ).Num() + PriorityMessage().GetActiveCenterMessages( EPriorityMessageLevel::DEFENDER ).Num() == 0 )
			{
				firstTime = false;
				PriorityMessage().ClearActiveCenterMessages( EPriorityMessageLevel::ATTACKER );
				PriorityMessage().ClearActiveCenterMessages( EPriorityMessageLevel::DEFENDER );
				UAS_PriorityMessageBaseWidget widget = AddTemplateMessage( EPriorityMessageTemplate::OVERTIME_LIGHTWEIGHT );
				UAS_PriorityMessageRaidWidget raidWidget = Cast<UAS_PriorityMessageRaidWidget>(widget);
				if(IsValid(raidWidget))
				{
					raidWidget.LensFlareGroup.SetVisibility(ESlateVisibility::Hidden);
				}
				co.Wait( pmm, pmm.onMessageCreatedSignal );
			}
			co.Wait( pmm, pmm.onMessageClearedSignal );
		}
	}

	UFUNCTION()
	private void OnNextPhaseTimeChanged( int oldValue, int newValue )
	{
		System::ClearAndInvalidateTimerHandle( overtimeAudioHandle );

		TryStartCountdownTicking();
	}

	bool IsShieldBreakerInPlay()
	{
		EObjectiveState objective = GetTeamStateManager_RaidMode( localPlayer.GetTeam() ).GetRaidObjectiveState();
		return objective == EObjectiveState::InterceptShieldBreaker || objective == EObjectiveState::PickupShieldBreaker || objective == EObjectiveState::Raiding;
	}

	void TryStartCountdownTicking()
	{
		int localTeam = localPlayer.GetTeam();

		if ( GetGamePhase() != GamePhase::PLAYING )
			return;

		if ( !IsShieldBreakerInPlay() )
			return;

		if ( IsTeamRaiding( localTeam ) || IsTeamBeingRaided( localTeam ) )
			return;

		int nextPhaseTime = GetGameStateEntity().nextPhaseTime;
		if ( nextPhaseTime <= GetTimeMilliseconds() )
			return;

		int delayMS		 = nextPhaseTime - GetTimeMilliseconds() - TO_MILLISECONDS( 5.0 );
		float32 delaySec = TO_SECONDS( delayMS );
		if ( delaySec > 0 )
			overtimeAudioHandle = System::SetTimer( this, n"OnFiveSecondsLeft", delaySec, false );
		else if ( delaySec < -5 )
		{
			OnSecondsLeft( int( 5 + delaySec ) );
		}
	}

	UFUNCTION()
	private void OnFiveSecondsLeft()
	{
		// OnSecondsLeft( 5 );
		Client_EmitSoundUI( countdownAsset );
	}

	void OnSecondsLeft( int count )
	{
		// Thread( this, n"CountdownThread", count );
	}

	FNCCoroutineSignal countdownSignal;

	UFUNCTION()
	void CountdownThread( UNCCoroutine co, int count )
	{
		countdownSignal.Emit();
		co.EndOn( this, countdownSignal );

		int _count = count;
		while ( _count > 0 )
		{
			Print( f"{_count}..." );
			co.Wait( 1.0 );
			_count--;
		}
	}
}