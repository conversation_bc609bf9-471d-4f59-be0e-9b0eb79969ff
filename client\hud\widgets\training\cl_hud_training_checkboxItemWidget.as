UCLASS( Abstract )
class UAS_Training_CheckboxItemWidget : UNCGenericListEntry
{
	UPROPERTY( NotVisible, BindWidget )
	private UImage checkboxFill;
	
	UPROPERTY( NotVisible, BindWidget )
	private URichTextBlock descriptionText;

	UPROPERTY( NotVisible, BindWidget )
	private UImage progressBar;
	
	UPROPERTY( NotVisible, BindWidget )
	private UOverlay progressBarOverlay;

	UPROPERTY( NotVisible, BindWidget )
	private UHorizontalBox textHorizBox;

	UPROPERTY( NotVisible, Transient, meta=(BindWidgetAnim))
	UWidgetAnimation anim_objective_updated;

	UPROPERTY( NotVisible, Transient, meta=(BindWidgetAnim))
	UWidgetAnimation anim_objective_updated_incorrect;

	UAS_TrainingObjective currentObjective;

	UFUNCTION(BlueprintOverride)
	void OnDataSet( UObject dataObject )
	{
		UAS_TrainingObjectiveListObject listObject = Cast< UAS_TrainingObjectiveListObject >( dataObject );
		if ( !IsValid( listObject ) )
		{
			return;
		}

		if ( !IsValid( listObject.objective ) )
		{
			return;
		}

		if ( IsValid( currentObjective ) )
		{
			currentObjective.onObjectiveEnded.Unbind( this, n"OnTrackedObjectiveEnded" );
			currentObjective.onObjectiveUpdated.Unbind( this, n"OnTrackedObjectiveUpdated" );
		}

		currentObjective = listObject.objective;
		currentObjective.onObjectiveEnded.AddUFunction( this, n"OnTrackedObjectiveEnded" );
		currentObjective.onObjectiveUpdated.AddUFunction( this, n"OnTrackedObjectiveUpdated" );
		currentObjective.client_onObjectiveIncorrectAction.AddUFunction( this, n"OnTrackedObjectiveIncorrectAction" );
		currentObjective.client_onObjectiveCorrectAction.AddUFunction( this, n"OnTrackedObjectiveCorrectAction" );
		SetWidgetVisibilitySafe( progressBarOverlay, currentObjective.doProgressBar ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
		
		// in case objective is already complete by the time it's set
		OnTrackedObjectiveEnded( currentObjective, currentObjective.IsObjectiveComplete() ? ETrainingObjectiveEndContext::COMPLETED : ETrainingObjectiveEndContext::INCOMPLETE );
		
		Update();
	}

	private void Update()
	{
		if ( !IsValid( currentObjective ) )
		{
			return;
		}

		descriptionText.SetText( currentObjective.description );
		progressBar.GetDynamicMaterial().SetScalarParameterValue( n"ManualProgress", currentObjective.GetObjectiveProgress() );
		
		const bool isActiveObjective = currentObjective.ClientGetIsActiveOrderedObjective();
		textHorizBox.SetRenderOpacity( isActiveObjective ? 1 : 0.5 );

	}

	UFUNCTION()
	private void OnTrackedObjectiveEnded( UAS_TrainingObjective objective, ETrainingObjectiveEndContext reason )
	{
		SetWidgetVisibilitySafe( checkboxFill, reason == ETrainingObjectiveEndContext::COMPLETED ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
		Update();
	}
	
	UFUNCTION()
	private void OnTrackedObjectiveUpdated( UAS_TrainingObjective objective )
	{
		Update();
	}

	UFUNCTION()
	private void OnTrackedObjectiveIncorrectAction( UAS_TrainingObjective objective )
	{
		const bool isActiveObjective = currentObjective.ClientGetIsActiveOrderedObjective();
		if ( isActiveObjective )
		{
			PlayAnimationForward( anim_objective_updated_incorrect, 1 );
		}
	}

	UFUNCTION()
	private void OnTrackedObjectiveCorrectAction( UAS_TrainingObjective objective )
	{
		const bool isActiveObjective = currentObjective.ClientGetIsActiveOrderedObjective();
		if ( isActiveObjective )
		{
			PlayAnimationForward( anim_objective_updated, 1 );
		}
	}

	UFUNCTION(BlueprintOverride)
	void Destruct()
	{
		if ( IsValid( currentObjective ) )
		{
			currentObjective.onObjectiveEnded.Unbind( this, n"OnTrackedObjectiveEnded" );
			currentObjective.onObjectiveUpdated.Unbind( this, n"OnTrackedObjectiveUpdated" );
		}
	}
}