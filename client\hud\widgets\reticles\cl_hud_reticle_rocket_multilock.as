UCLASS( Abstract )
class UAS_ReticleWidget_RocketMultiLock : UAS_ReticleWidget
{
	const FName MOD_MULTI_LOCK = n"RocketLauncherMultiLock";

	bool lockTickAsleep = false;
	int numActivePips	= 0;
	TArray<int> lastLockCountPerTarget;
	bool areTargetPipsVisible;

	// widget hooks
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UOverlay WidgetCenterDot;

	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UCommonTextBlock MaxLocksText;

	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UCanvasPanel PipsCanvas;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UOverlay TargetPip_1;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UOverlay TargetPip_2;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UOverlay TargetPip_3;
	TArray<UOverlay> targetPips;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	private UWidgetAnimation AddPipAnim_1;
	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	private UWidgetAnimation AddPipAnim_2;
	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	private UWidgetAnimation AddPipAnim_3;
	TArray<UWidgetAnimation> addPipAnims;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	private UWidgetAnimation DupePipAnim_2;
	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	private UWidgetAnimation DupePipAnim_3;
	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	private UWidgetAnimation DoubleDupePipAnim_3;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	private UWidgetAnimation ShowTargetPipsAnim;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset AddPipSound;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		Super::Construct();
		PipsCanvas.SetRenderOpacity( 0 );

		targetPips.Add( TargetPip_1 );
		targetPips.Add( TargetPip_2 );
		targetPips.Add( TargetPip_3 );

		addPipAnims.Add( AddPipAnim_1 );
		addPipAnims.Add( AddPipAnim_2 );
		addPipAnims.Add( AddPipAnim_3 );

		for ( int i = 0; i < targetPips.Num(); i++ )
		{
			lastLockCountPerTarget.Add( 1 );
		}
	}

	void InitializeReticle( ANCWeapon weapon ) override
	{
		Super::InitializeReticle( weapon );

		areTargetPipsVisible = false;
	}

	protected void ScriptReticleTick( ANCWeapon weapon, float32 dt ) override
	{
		//============== shared ===================
		Super::ScriptReticleTick( weapon, dt );

		AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( weapon.GetWeaponOwner() );
		if ( !IsValid( player ) )
			return;

		UAS_WeaponContext_RocketSingle rocketContext = Cast<UAS_WeaponContext_RocketSingle>( weapon.GetScriptContext() );
		if ( !IsValid( rocketContext ) )
			return;

		TArray<FRocketLockTarget> lockTargets = player.rocketLockTrackerComponent.net_lockTargets;

		float adsFrac = player.GetAdsFraction();
		WidgetCenterDot.SetRenderOpacity( 1.0f - adsFrac );

		if ( ShouldShowTargetPips( weapon, player, rocketContext ) )
			PlayAnimShowTargetPips();
		else
			PlayAnimHideTargetPips();

		if ( !rocketContext.IsMultiLock( weapon ) )
		{
			MaxLocksText.SetOpacity( 0.0f );
			return;
		}

		//=========== multi-lock =================
		int lockCount = player.rocketLockTrackerComponent.GetTotalLockCount();
		// sleep setup to avoid work on tick when theres no lock
		if ( lockCount == 0 )
		{
			if ( !lockTickAsleep )
			{
				MaxLocksText.SetOpacity( 0.0f );
				numActivePips = 0;
				for ( int i = 0; i < targetPips.Num(); i++ )
				{
					lastLockCountPerTarget[i] = 1;
				}

				for ( UOverlay targetPip : targetPips )
				{
					UCanvasPanelSlot pipSlot = WidgetLayout::SlotAsCanvasSlot( targetPip );
					pipSlot.SetPosition( FVector2D::ZeroVector );
					targetPip.SetRenderOpacity( 0.0f );
				}

				lockTickAsleep = true;
			}

			return;
		}
		lockTickAsleep = false;

		// target lock pips
		for ( int pipIdx = 0; pipIdx < targetPips.Num(); pipIdx++ )
		{
			UOverlay targetPip		 = targetPips[pipIdx];
			UCanvasPanelSlot pipSlot = WidgetLayout::SlotAsCanvasSlot( targetPip );
			int lockTargetIdx		 = player.rocketLockTrackerComponent.ConvertShotIndexToLockIndex( pipIdx );
			bool showPip			 = false;

			if ( pipIdx < lockCount )
			{
				FRocketLockTarget lockTarget = lockTargets[lockTargetIdx];
				FVector2D screenPos;
				if ( GetScreenPos( lockTarget.net_targetLocation, this, screenPos ) )
				{
					pipSlot.SetPosition( screenPos );
					showPip = true;

					// check to play standard add animation for new pip
					if ( pipIdx >= numActivePips )
					{
						PlayAnimation( addPipAnims[pipIdx] );
						Client_EmitSoundUI( AddPipSound );
					}

					// check if we're a dupe pip on the same target
					bool playingDupeAnim = false;
					if ( lockTarget.net_numLocks > lastLockCountPerTarget[lockTargetIdx] )
					{
						// Print(f"pipIdx: {pipIdx} | lockTgtIdx: {lockTargetIdx} | numLocks: {lockTarget.net_numLocks} | lastLockCount: {lastLockCountPerTarget[lockTargetIdx]}", 10.0f);
						if ( lockTarget.net_numLocks == 2 )
						{
							UWidgetAnimation anim = pipIdx < 2 ? DupePipAnim_2 : DupePipAnim_3;
							PlayAnimation( anim );
							playingDupeAnim = true;
						}
						else if ( lockTarget.net_numLocks == 3 )
						{
							PlayAnimation( DoubleDupePipAnim_3 );
							playingDupeAnim = true;
						}

						if ( playingDupeAnim )
						{
							Client_EmitSoundUI( AddPipSound );
						}
						lastLockCountPerTarget[lockTargetIdx] = lockTarget.net_numLocks;
					}
				}
			}

			if ( showPip )
			{
				targetPip.SetRenderOpacity( 1.0f );
			}
			else
			{
				pipSlot.SetPosition( FVector2D::ZeroVector );
				targetPip.SetRenderOpacity( 0.0f );
			}
		}

		// max locks text
		if ( lockCount >= 3 )
			MaxLocksText.SetOpacity( adsFrac );
		else
			MaxLocksText.SetOpacity( 0.0f );

		// track last active pip count to play anims on new ones
		numActivePips = lockCount;
	}

	private bool ShouldShowTargetPips( ANCWeapon weapon, AAS_PlayerEntity player, UAS_WeaponContext_RocketSingle rocketContext )
	{
		if ( !IsValid( rocketContext ) )
			return false;

		if ( !rocketContext.IsMultiLock( weapon ) )
			return false;

		return player.rocketLockTrackerComponent.net_lockTargets.Num() > 0;
	}

	private void PlayAnimShowTargetPips()
	{
		if ( areTargetPipsVisible )
			return;

		areTargetPipsVisible = true;
		float32 startTime	 = GetAnimationCurrentTime( ShowTargetPipsAnim );
		PlayAnimation( ShowTargetPipsAnim, startTime, 1 );
	}

	private void PlayAnimHideTargetPips()
	{
		if ( !areTargetPipsVisible )
			return;

		areTargetPipsVisible = false;
		float32 startTime	 = GetAnimationCurrentTime( ShowTargetPipsAnim );
		PlayAnimation( ShowTargetPipsAnim, startTime, 1, EUMGSequencePlayMode::Reverse );
	}
}