UCLASS(Abstract)
class UAS_MountCooldownWidget : UUserWidgetDefault
{
	FTimerHandle tickHandle;
	
	int startTime;
	int endTime;

	UPROPERTY(Meta=(BindWidget))
	UTextBlock ProgressTextBlock;

	UPROPERTY(Meta=(BindWidget))
	UProgressBar ProgressBar;

	UFUNCTION(BlueprintOverride)
	void Construct()
	{
		ANCPlayerCharacter player 	= Widget_GetPawnOwner(this);
		int endTIme 				= player.GetMountComponent().GetCooldownEndTime();
		float32 cooldownTime 		= player.GetMountComponent().GetCooldownDuration();

		OnCooldownChanged( endTIme - TO_MILLISECONDS(cooldownTime), endTIme );
		ScriptCallbacks().RegisterSignalCallback( Signals::ON_MOUNT_COOLDOWN_UPDATE, this, n"OnMountCooldownUpdated" );
	}

	UFUNCTION(NotBlueprintCallable)
	void OnMountCooldownUpdated( FName signalName, UObject sender )
	{
		if ( sender != Widget_GetPawnOwner(this) )
			return;

		ANCPlayerCharacter player 	= Widget_GetPawnOwner(this);
		int endTIme 				= player.GetMountComponent().GetCooldownEndTime();
		float32 cooldownTime 		= player.GetMountComponent().GetCooldownDuration();

		OnCooldownChanged( endTIme - TO_MILLISECONDS(cooldownTime), endTIme );
	}

	void OnCooldownChanged( int start, int end )
	{
		System::ClearAndInvalidateTimerHandle( tickHandle );
		startTime = start;
		endTime = end;

		if ( GetTimeMilliseconds() < end )
		{
			SetWidgetVisibilitySafe(this,  ESlateVisibility::HitTestInvisible );
			tickHandle = System::SetTimer( this, n"UpdateProgress", 0.05, true );
		}
		else
		{
			SetWidgetVisibilitySafe(this,  ESlateVisibility::Hidden );
		}

		UpdateProgress();
	}

	UFUNCTION()
	void UpdateProgress()
	{
		float totalTime = endTime - startTime;
		float timeLeft = (endTime - GetTimeMilliseconds());
		float progressFrac = timeLeft/totalTime;
		ProgressBar.SetPercent(progressFrac);
		FText progressText = GetFormattedCountdownTime( int( timeLeft ) );
		ProgressTextBlock.SetText(progressText);


		if ( timeLeft <= 0 )
		{
			System::ClearAndInvalidateTimerHandle( tickHandle );
			SetWidgetVisibilitySafe(this,  ESlateVisibility::Hidden );
		}
	}
}