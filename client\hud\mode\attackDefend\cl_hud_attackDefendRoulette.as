UCLASS(Abstract)
class UAS_AttackDefendRouletteWidget : UUserWidgetDefault
{
	UPROPERTY( Transient, meta = ( BindWidgetAnim ) )
	UWidgetAnimation rouletteLoop;

	UPROPERTY( Transient, meta = ( BindWidgetAnim ) )
	UWidgetAnimation attackerSettle;

	UPROPERTY( Transient, meta = ( BindWidgetAnim ) )
	UWidgetAnimation defenderSettle;

	UPROPERTY( Transient, meta = ( BindWidgetAnim ) )
	UWidgetAnimation waitingSettle;

	EMatchupState matchupState;
	float32 duration;

	void PlayRoulette(EMatchupState state, float32 dur, bool firstTime)
	{
		matchupState = state;
		duration = dur;
		if ( firstTime )
			Thread( this, n"RouletteThread" );
		else
		{
			PlayFinalAnim();
		}
	}

	void PlayFinalAnim()
	{
		if ( matchupState == EMatchupState::Attacker )
		{
			PlayAnimation( attackerSettle );
		}
		else if ( matchupState == EMatchupState::Defender )
		{
			PlayAnimation( defenderSettle );
		}
		else
		{
			PlayAnimation( waitingSettle );
		}
	}

	UFUNCTION()
	private void RouletteThread( UNCCoroutine co )
	{
		PlayAnimation( rouletteLoop, 0, 0 );
		co.Wait( duration - 7 );
		StopAnimation( rouletteLoop );
		PlayFinalAnim();
	}
}