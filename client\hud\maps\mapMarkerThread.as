UCLASS( NotBlueprintable )
class UAS_MapMarkerThread : UAS_Thread
{
	private int startTime;
	private float duration;
	private UNCMapMarkerWidget marker;

	void Initialize( float inDuration, UNCMapMarkerWidget inMarker )
	{
		if ( !IsValid( inMarker ) )
			return;

		startTime = GetGameTimeMS();
		duration  = inDuration;
		marker	  = inMarker;
		Start();
	}

	void OnThreadStart( UNCCoroutine co ) override
	{
		Super::OnThreadStart( co );

		float lerpAlpha = 0.0f;

		while ( lerpAlpha < 1.0 )
		{
			float time = TO_SECONDS( GetGameTimeMS() - startTime );
			lerpAlpha  = Math::GetMappedRangeValueClamped( FVector2D( 0, duration ), FVector2D( 0, 1 ), time );
			marker.SetMaterialScalarValue( MaterialParameter::LERP_ALPHA, lerpAlpha );
			co.Wait( 0.01f );
		}
	}
}