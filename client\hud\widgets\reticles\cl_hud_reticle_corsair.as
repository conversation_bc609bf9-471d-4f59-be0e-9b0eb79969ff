UCLASS(Abstract)
class UAS_ReticleWidget_Corsair : UAS_ReticleWidget
{

	UPROPERTY( Meta = ( BindWidget ) )
	USpacer spreadSpacer;

	UPROPERTY( Transient, Meta = ( BindWidgetAnimOptional ) )
	UWidgetAnimation anim_spreadFraction;

	UPROPERTY( Transient, Meta = ( BindWidgetAnimOptional ) )
	UWidgetAnimation anim_patternHint;

	void InitializeReticle(ANCWeapon weapon) override
	{
		Super::InitializeReticle(weapon);
	}

	protected void ScriptReticleTick(ANCWeapon currentWeapon, float32 dt) override
	{
		Super::ScriptReticleTick(currentWeapon, dt);

		// TODO @Davis - Corsair reticle hack. Shouldn't have to call this.
		UpdateCrosshairSpreadOffset( currentWeapon );

		spreadRadiusPixels = CrosshairSpreadOffset * spreadNormalizeScalar;
		spreadSpacer.Size = FVector2D( spreadRadiusPixels * 2, spreadRadiusPixels * 2 );

		if ( IsValid( anim_spreadFraction ) )
			PlayAnimation( anim_spreadFraction, spreadFraction, 1, EUMGSequencePlayMode::Forward, 0.0 );

		if ( IsValid( anim_patternHint ) )
		{
			// Gotta fake this since it doesn't update
			float elapsedSinceLastShot = TO_SECONDS( GetTimeMilliseconds() - int( currentWeapon.LastAttackTime ) );
			float32 patternProgressFrac = 0;
			int simulatedPatternIndex = currentWeapon.RecoilPatternIndex;
			float32 regressDelay = currentWeapon.WeaponConfigData.kickPatternRegressDelay;
			if ( elapsedSinceLastShot > regressDelay )
			{
				simulatedPatternIndex = currentWeapon.RecoilPatternIndex - int( currentWeapon.WeaponConfigData.kickPatternRegressRate * ( elapsedSinceLastShot - regressDelay ) * 0.5 );
			}
			patternProgressFrac = float32( Math::Min( simulatedPatternIndex, 6 ) ) / 6.0;
			patternProgressFrac = 1.0f - patternProgressFrac;
			PlayAnimation( anim_patternHint, patternProgressFrac, 1, EUMGSequencePlayMode::Forward, 0.0 );
		}
	}
}