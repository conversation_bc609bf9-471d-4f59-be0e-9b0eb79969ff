UCLASS( Abstract )
class UAS_HUDPromptManager : UUserWidgetDefault
{
	UPROPERTY( BlueprintHidden, BindWidget )
	private UCommonActivatableWidgetSwitcher widgetSwitcher;

	UPROPERTY( BlueprintHidden, BindWidget )
	private UAS_UsePromptWidget usePromptWidget;

	UPROPERTY( BlueprintHidden, BindWidget )
	private UAS_HUDHintWidget hintWidget;

	private UUsableItemComponent lastUseComponent;
	
	FOnHintWidgetUpdated onHintWidgetSet;

	UFUNCTION(BlueprintOverride)
	void Construct()
	{
		ClientCallbacks().OnLocalControllerUseItemChanged.AddUFunction( this, n"OnUseItemChanged" );
		hintWidget.onHintWidgetSet.AddUFunction( this, n"OnHintWidgetWasSet" );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnUseItemChanged( UUsableItemComponent oldUseComponent, UUsableItemComponent newUseComponent )
	{
		usePromptWidget.ScriptSetNewUseItem( newUseComponent );
		lastUseComponent = newUseComponent;
		UpdateVisibility();
	}

	UFUNCTION(BlueprintOverride)
	void Tick(FGeometry MyGeometry, float InDeltaTime)
	{
		// TODO: Possible to expose this from code instead of tick?
		if ( !IsValid( lastUseComponent ) )
		{
			return;
		}

		const ANCPlayerCharacter localPlayer = Client_GetLocalPawn();

		FInputUsabilityStates usabilityStates = lastUseComponent.GetUsabilityState( localPlayer );
		UpdateVisibility();

		usePromptWidget.UpdateUsabilityState( usabilityStates );

		// Update hint texts
		usePromptWidget.UpdateHintText( lastUseComponent.GetHintText( localPlayer ) );
		usePromptWidget.UpdateExtendedUseHintText( lastUseComponent.GetExtendedUseHintText( localPlayer ) );
		usePromptWidget.UpdateAltHintText( lastUseComponent.GetAltHintText( localPlayer ) );
	}

	void SetHint( const FHUDHintData data )
	{
		hintWidget.SetHint( data );
		UpdateVisibility();
	}
	
	void ClearHint()
	{
		hintWidget.ClearHint();
		UpdateVisibility();
	}
	
	void SetHintProgressFrac( float32 newFrac )
	{
		hintWidget.SetProgressFrac( newFrac );
	}

	private void UpdateVisibility()
	{
		AAS_PlayerEntity localPlayer = Client_GetLocalASPawn();
		bool shouldShow = false;
		bool hasUseObject = false;

		if ( IsValid( lastUseComponent ) )
		{
			FInputUsabilityStates usabilityStates = lastUseComponent.GetUsabilityState( localPlayer );
			hasUseObject = usabilityStates.NormalInstantInput != EUsabilityState::NONUSABLE
						|| usabilityStates.NormalExtendedInput != EUsabilityState::NONUSABLE
						|| usabilityStates.AlternateInput != EUsabilityState::NONUSABLE;
		}


		FHUDHintData hint = hintWidget.GetHint();
		bool forcedScriptedHint = hint.prioritizeOverUseText && hintWidget.IsHintVisible();

		if ( usePromptWidget.IsVisible() && hasUseObject && !forcedScriptedHint )
		{
			widgetSwitcher.SetActiveWidget( usePromptWidget );
			shouldShow = true;
		}
		else if ( hintWidget.IsHintVisible() )
		{
			widgetSwitcher.SetActiveWidget( hintWidget );
			shouldShow = true;
		}
		else
		{
			shouldShow = false;
		}

		SetWidgetVisibilitySafe( this, shouldShow ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
	}

	UFUNCTION()
	private void OnHintWidgetWasSet()
	{
		onHintWidgetSet.Broadcast();
	}
}