UCLASS( Abstract )
class UAS_RadialWheelDetails : UNC_DisplayWidget
{
	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_KeybindWidget keybind;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_CommonTextBlock actionText;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_CommonTextBlock subjectText;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage keybindDivider;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_CommonTextBlock unavailableText;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UNamedSlot additionalDetails;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UPanelWidget itemDetailsPanel;

	UPROPERTY( <PERSON>printHidden, EditDefaultsOnly )
	private float unavailableSubjectActionTextOpacity = 0.5f;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		SetWidgetVisibilitySafe( itemDetailsPanel, ESlateVisibility::Collapsed );
	}

	void SetItem( UNC_RadialListItemData item )
	{
		/*
			We convert from a UNC_RadialListItemData to a UAS_RadialMenuListItemData so we can get the menu data set by designers. Since
			UNC_RadialListItemData is set in code, we can't add things to it so we add a layer of inheritance to handle it instead of passing around big structs.
		*/
		UAS_RadialMenuListItemData menuDataItem = Cast<UAS_RadialMenuListItemData>( item );
		if ( IsValid( menuDataItem ) )
		{
			// Now that there is a valid item, we can show the item details
			SetWidgetVisibilitySafe( itemDetailsPanel, ESlateVisibility::HitTestInvisible );

			// Show the subject text based on the definition data passed in
			FNC_RadialListItemDefinition definition = menuDataItem.GetDefinition();
			subjectText.SetText( definition.Label );
			SetWidgetVisibilitySafe( subjectText, ESlateVisibility::HitTestInvisible );

			// Show the action text if it is set by a designer
			FRadialMenuData menuData = menuDataItem.menuData;
			if ( menuData.showActionText )
			{
				actionText.SetText( menuData.actionText );
			}

			float availableOpacity = menuDataItem.isAvailable ? 1.0f : unavailableSubjectActionTextOpacity;
			subjectText.SetRenderOpacity( availableOpacity );
			actionText.SetRenderOpacity( availableOpacity );

			UAS_RadialLootData grenadeItemData = Cast<UAS_RadialLootData>( menuDataItem );
			if ( IsValid( grenadeItemData ) )
			{
				// For pings, we actually use the action as the ping and hide the subject
				actionText.SetText( definition.Label );
				SetWidgetVisibilitySafe( subjectText, ESlateVisibility::Collapsed );
			}

			UAS_RadialPingData pingItemData = Cast<UAS_RadialPingData>( menuDataItem );
			if ( IsValid( pingItemData ) )
			{
				// For pings, we actually use the action as the ping and hide the subject
				actionText.SetText( definition.Label );
				SetWidgetVisibilitySafe( subjectText, ESlateVisibility::Collapsed );
			}
			SetWidgetVisibilitySafe( unavailableText, menuDataItem.isAvailable ? ESlateVisibility::Collapsed : ESlateVisibility::HitTestInvisible );

			UAS_TeleportWheelItemData teleportItemData = Cast<UAS_TeleportWheelItemData>( menuDataItem );
			if ( IsValid( teleportItemData ) )
			{
				ECanTeleportResult teleportResult = ECanTeleportResult( definition.Number );

				if ( !menuDataItem.isAvailable )
				{
					unavailableText.SetText( GetTeleportFailureText( teleportResult, teleportItemData.teleportID ) );
				}

				SetWidgetVisibilitySafe( unavailableText, menuDataItem.isAvailable ? ESlateVisibility::Collapsed : ESlateVisibility::HitTestInvisible );
			}
		}
	}

	private FText GetTeleportFailureText( ECanTeleportResult teleportResult, ETeleportID teleportId )
	{
		FText result = Text::EmptyText;
		FString key;

		switch ( teleportResult )
		{
			case ECanTeleportResult::NO_MISSING_BREAKERTOWER:
				key = "wheel_no_breaker";
				break;
			case ECanTeleportResult::NO_TOO_CLOSE:
			{
				switch ( teleportId )
				{
					case ETeleportID::BASE:
						key = "wheel_already_inside";
						break;
					default:
						key = "wheel_too_close";
						break;
				}
				break;
			}
			case ECanTeleportResult::NO_BLEEDINGOUT:
				key = "wheel_downed";
				break;
			case ECanTeleportResult::NO_HAS_SHIELDBREAKER:
				key = "wheel_shield_breaker";
				break;
			case ECanTeleportResult::NO_MISSING_BASE:
				key = "wheel_no_base";
				break;
			default:
				break;
		}

		if ( !key.IsEmpty() )
		{
			result = GetLocalizedText( Localization::ReturnHome, key );
		}

		return result;
	}
}