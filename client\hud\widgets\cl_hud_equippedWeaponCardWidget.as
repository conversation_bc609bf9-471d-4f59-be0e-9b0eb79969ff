enum EEquippedWeaponCardStyle
{
	DEFAULT = 0,
	WEAPON_NAME_ONLY = 1,
	WEAPON_ICON_ONLY = 2,
}

enum EWeaponCardAmmoState
{
	DEFAULT = 0,
	NO_AMMO,
	LOW_AMMO
}

UCLASS( Abstract )
class UAS_EquippedWeaponCardWidget : UAS_WeaponCardWidget
{
	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage attachIcon;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidgetOptional ) )
	private UTextBlock magazineAmmoText;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidgetOptional ) )
	private UTextBlock stockpileAmmoText;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidgetOptional ) )
	private UTextBlock nameText;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidgetOptional ) )
	private UTextBlock nicknameText;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidgetOptional ) )
	private UAS_CommonTextBlock weaponSerialNumber;

	UPROPERTY( NotVisible, BlueprintHidden, Transient, Meta = ( BindWidgetAnimOptional ) )
	private UWidgetAnimation anim_stockpileAmmoState;

	UPROPERTY( NotVisible, BlueprintHidden, Transient, Meta = ( BindWidgetAnimOptional ) )
	private UWidgetAnimation anim_magAmmoState;

	UPROPERTY( EditInstanceOnly )
	private bool hideOnEmpty;
	default hideOnEmpty = true;

	UPROPERTY( EditInstanceOnly )
	private EEquippedWeaponCardStyle displayStyle;
	default displayStyle = EEquippedWeaponCardStyle::DEFAULT;

	private int magazineAmmo = 0;
	private int magazineAmmoMax = 0;
	private int stockpileAmmo = 0;
	private int stockpileAmmoMax = 0;

	int weaponLootLevel;

	private void Update() override
	{
		Super::Update();

		if ( optWeaponLootData.IsSet() )
		{
			weaponLootLevel = GetLootRarityLevel( optWeaponLootData.Value );
		}
		else
		{
			SetWidgetVisibilitySafe( nameText, ESlateVisibility::Hidden );
		}

		UpdateWeaponName();
		UpdateWeaponAttachments();
		UpdateAmmoTextVisibility();
		UpdateStockpileAmmoText();

		if ( IsValid( equippedWeapon ) )
		{
			UpdateMagazineAmmoText( equippedWeapon );
		}
	}

	private void OnLocalWeaponClipAmmoChanged( ANCWeapon weapon, int oldValue, int newValue ) override
	{
		if ( weapon == equippedWeapon )
		{
			UpdateMagazineAmmoText( weapon );
			if ( weapon.GetAmmoSource() == EAmmoSource::Stockpile )
				UpdateStockpileAmmoText();
		}
	}

	private void OnLocalWeaponStockpileAmmoChanged( ANCWeapon weapon, int oldValue, int newValue ) override
	{
		if ( weapon == equippedWeapon )
		{
			UpdateMagazineAmmoText( weapon );
			UpdateStockpileAmmoText();
		}
	}

	private void OnBackpackContentsChanged( UBackpackComponent newBackpack ) override
	{
		UpdateStockpileAmmoText();
	}

	private void UpdateMagazineAmmoText( ANCWeapon weapon )
	{
		if ( !IsValid( magazineAmmoText ) )
			return;

		if ( !IsValid( weapon ) )
			return;

		magazineAmmo	= weapon.GetClipAmmo();
		magazineAmmoMax = weapon.GetClipAmmoMax();
		FText newText	= FText::AsNumber( magazineAmmo, GetDefaultNumberFormattingOptionsWithGrouping() );
		magazineAmmoText.SetText( newText );

		UpdateAmmoTextState();
	}

	private void UpdateStockpileAmmoText()
	{
		if ( !IsValid( stockpileAmmoText ) )
			return;

		stockpileAmmo	 = GameConst::INDEX_NONE;
		stockpileAmmoMax = GameConst::INDEX_NONE;
		if ( IsValid( equippedWeapon ) )
		{
			stockpileAmmo	 = equippedWeapon.GetStockpileAmmo();
			stockpileAmmoMax = equippedWeapon.GetStockpileAmmoMax();
		}
		else if ( optWeaponData.IsSet() && optWeaponItemData.IsSet() )
		{
			AAS_PlayerEntity asPlayer = Client_GetLocalASPawn();
			if ( IsValid( asPlayer ) )
			{
				FLootDataStruct ammoData = GetLootDataForAmmo( optWeaponData.Value.ammoSource );
				stockpileAmmo			 = asPlayer.ammoBackpackComponent.GetNumItems( ammoData.index );
				stockpileAmmoMax		 = optWeaponData.Value.maxStockpileAmmo;
			}
		}

		FText newText = FText::AsNumber( stockpileAmmo, GetDefaultNumberFormattingOptionsWithGrouping() );
		stockpileAmmoText.SetText( newText );

		UpdateAmmoTextState();
	}

	// Animation contains different states at different times
	private void UpdateAmmoTextState()
	{
		if ( !IsValid( stockpileAmmoText ) || !IsValid( magazineAmmoText ) )
			return;

		float32 stockpileAmmoFrac = 0;
		if ( stockpileAmmoMax > 0 )
		{
			stockpileAmmoFrac = float32( stockpileAmmo ) / float32( stockpileAmmoMax );
		}
		PlayAnimForAmmoState( anim_stockpileAmmoState, stockpileAmmoFrac );

		float magazineAmmoFrac = 0;
		if ( magazineAmmoMax > 0 )
		{
			magazineAmmoFrac = float32( magazineAmmo ) / float32( magazineAmmoMax );
		}
		PlayAnimForAmmoState( anim_magAmmoState, magazineAmmoFrac );
	}

	private void PlayAnimForAmmoState( UWidgetAnimation inAnim, float ammoFrac )
	{
		float startTime = -1;
		if ( ammoFrac > 0.25 )
		{
			startTime = float( EWeaponCardAmmoState::DEFAULT );
		}
		else if ( ammoFrac > 0 )
		{
			startTime = float( EWeaponCardAmmoState::LOW_AMMO );
		}
		else
		{
			startTime = float( EWeaponCardAmmoState::NO_AMMO );
		}

		PlayAnimation( inAnim, startTime, 1, EUMGSequencePlayMode::Forward, 0 );
	}

	// TODO: @Davis - Icons for purple raid tools need to be hooked up
	private void UpdateWeaponAttachments()
	{
		if ( !optWeaponLootData.IsSet() || displayStyle == EEquippedWeaponCardStyle::WEAPON_ICON_ONLY )
		{
			return;
		}

		const FLootDataStruct equippedWeaponLootData = optWeaponLootData.GetValue();

		// TODO @Davis: Find a better way to determine rare weapon.. this seems sketchy. Maybe make a function?
		if ( weaponLootLevel == 2 )
		{
			UMaterialInstanceDynamic attachIconDynMat = attachIcon.GetDynamicMaterial();
			UTexture2D newIcon						  = Cast<UTexture2D>( GetWeaponAttachmentIcon( equippedWeaponLootData ) );
			if ( IsValid( newIcon ) )
			{
				attachIconDynMat.SetTextureParameterValue( n"AttachmentIcon", newIcon );
			}

			attachIconDynMat.SetVectorParameterValue( n"Color", GetRarityColor( equippedWeaponLootData.rarity ) );
			SetWidgetVisibilitySafe( attachIcon, ESlateVisibility::SelfHitTestInvisible );
		}
		else
		{
			SetWidgetVisibilitySafe( attachIcon, ESlateVisibility::Hidden );
		}
	}

	private void UpdateAmmoTextVisibility()
	{
		if ( !optWeaponLootData.IsSet() || displayStyle > EEquippedWeaponCardStyle::DEFAULT )
		{
			SetWidgetVisibilitySafe( stockpileAmmoText, ESlateVisibility::Hidden );
			SetWidgetVisibilitySafe( magazineAmmoText, ESlateVisibility::Hidden );
			return;
		}

		bool showAmmoText = optWeaponLootData.Value.lootType == ELootType::PrimaryWeapon ||
							optWeaponLootData.Value.lootType == ELootType::Grenade;

		ESlateVisibility newAmmoVisibility = showAmmoText ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed;
		if ( IsValid( stockpileAmmoText ) )
		{
			SetWidgetVisibilitySafe( stockpileAmmoText, newAmmoVisibility );
		}

		if ( IsValid( magazineAmmoText ) )
		{
			SetWidgetVisibilitySafe( magazineAmmoText, newAmmoVisibility );
		}
	}

	private void UpdateWeaponName()
	{
		if ( !optWeaponLootData.IsSet() || displayStyle == EEquippedWeaponCardStyle::WEAPON_ICON_ONLY )
			return;

		const FLootDataStruct weaponLootData = optWeaponLootData.GetValue();
		nameText.SetText( weaponLootData.name );
		SetWidgetVisibilitySafe( nameText, ESlateVisibility::HitTestInvisible );

		if ( IsValid( nicknameText ) )
		{
			bool shouldHide = true;
			// TODO @Davis: Find a better way to determine rare weapon.. this seems sketchy. Maybe make a function?
			if ( weaponLootLevel >= 2 )
			{
				// TODO @Davis: Need to change raid tools to use attachments
				TOptional<FWeaponAttachmentData> purpleModAttachData = TryGetNamedAttachmentData( weaponLootData );
				if ( purpleModAttachData.IsSet() )
				{
					nicknameText.SetText( purpleModAttachData.Value.name );
					// TODO @Davis - Get this in a better way
					nicknameText.SetColorAndOpacity( GetRarityColor( weaponLootData.rarity ) );
					SetWidgetVisibilitySafe( nicknameText, ESlateVisibility::HitTestInvisible );
					shouldHide = false;
				}
			}
			
			if ( shouldHide )
			{
				SetWidgetVisibilitySafe( nicknameText, ESlateVisibility::Collapsed );
			}
		}

		if ( IsValid( weaponSerialNumber ) )
		{
			FText serial = GetCvarBool( "UI.Debug.ShowDebugWeaponSerial" ) ? Localization::GetUnlocalizedTextFromString( UNCUtils::GetSerialForTag( this, weaponLootData.index ) ) : Text::EmptyText;
			weaponSerialNumber.SetText( serial );
		}
	}

	protected void EmptyWeaponCard( bool isEmpty ) override
	{
		Super::EmptyWeaponCard( isEmpty );

		if ( hideOnEmpty )
		{
			// Just hide this whole widget if we get to the point that the equipped weapon widget is empty
			SetWidgetVisibilitySafe( this, isEmpty ? ESlateVisibility::Hidden : ESlateVisibility::HitTestInvisible );
		}
		else
		{
			if ( IsValid( stockpileAmmoText ) )
				SetWidgetVisibilitySafe( stockpileAmmoText, ESlateVisibility::Hidden );

			if ( IsValid( magazineAmmoText ) )
				SetWidgetVisibilitySafe( magazineAmmoText, ESlateVisibility::Hidden );
		}
	}
}