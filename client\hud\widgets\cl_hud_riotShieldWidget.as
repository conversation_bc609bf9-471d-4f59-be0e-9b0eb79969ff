UCLASS(Abstract)
class UAS_RiotShieldWidget : UUserWidgetDefault
{
	AAS_HardLightShield riotShieldObject;

	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
	UImage healthBar;

	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
	UTextBlock warningText;

	void UpdateHealthbar()
	{
		if (!IsValid(riotShieldObject))
			return;

		float healthFrac = riotShieldObject.shieldComp.GetHealth() / riotShieldObject.shieldComp.GetMaxHealth();
        healthBar.GetDynamicMaterial().SetScalarParameterValue( n"Fill", healthFrac );
		healthBar.SetColorAndOpacity(Math::Lerp(FLinearColor::Red, FLinearColor::White, healthFrac));
		SetWidgetVisibilitySafe(warningText, healthFrac < 0.3 ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Hidden);
	}
}