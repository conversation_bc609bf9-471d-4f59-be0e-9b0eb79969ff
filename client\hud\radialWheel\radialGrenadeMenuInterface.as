namespace RadialGrenadeMenu
{
	bool Populate( TArray<UAS_RadialMenuListItemData>& results )
	{
		// Empty the array
		results.Empty();

		UAS_LootSystem lootSystem = Loot();
		if ( IsValid( lootSystem ) )
		{
			TArray<FLootDataStruct> allLoot		  = lootSystem.GetAllLootOfType( ELootType::Grenade );
			TArray<FGameplayTag> hack_ignoredList = Hack_GetIgnoredLootList();

			// Now that we have the loot data, reserve the space we might need
			int resultsSize = Math::Max( allLoot.Num() - hack_ignoredList.Num(), 0 );
			results.Reserve( resultsSize );

			AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( Client_GetLocalASPawn() );
			if ( IsValid( player ) )
			{
				for ( FLootDataStruct loot : allLoot )
				{
					if ( hack_ignoredList.Contains( loot.index ) )
						continue;

					// TODO @dstandley How to check this in a better way T__T
					int grenadeCount = 0;
					if ( IsCooldownBased( loot.index ) && loot.index == GameplayTags::Loot_Ammo_Grenades_Frag )
					{
						grenadeCount = player.fragCooldownComponent.IsCooldownActive() ? 0 : 1;
					}
					else
					{
						grenadeCount = player.CountItemsInAllBackpacks( loot.index );
					}

					UAS_RadialLootData item = UAS_RadialLootData();
					if ( IsValid( item ) )
					{
						item.tag = loot.index;

						FNC_RadialListItemDefinition definition;
						definition.Icon				   = FSlateBrush();
						definition.Icon.ResourceObject = loot.icon;
						definition.Number			   = grenadeCount;
						definition.Label			   = loot.name;
						item.SetDefinition( definition );

						item.isAvailable = grenadeCount > 0;

						results.Add( item );
					}
				}
			}
		}

		return !results.IsEmpty();
	}

	bool SelectItem( UAS_RadialLootData grenadeItemData )
	{
		const FLootDataStruct& loot = GetLootDataByIndex( grenadeItemData.tag );

		UNCEquipGrenadeEvent equipEvent = Cast<UNCEquipGrenadeEvent>( NewObject( GetCurrentWorld(), UNCEquipGrenadeEvent::StaticClass() ) );
		equipEvent.net_grenadeLootTag.SetNetValue( loot.index );
		equipEvent.SendToServer();

		return true;
	}
}