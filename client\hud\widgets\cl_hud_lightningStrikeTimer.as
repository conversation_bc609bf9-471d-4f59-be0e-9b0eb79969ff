UCLASS( Abstract )
class UAS_LightningStrikeTimer : UNC_DisplayWidget
{
	UPROPERTY( NotVisible, BlueprintHidden, BindWidget )
	private UImage progress_left;

	UPROPERTY( NotVisible, BlueprintHidden, BindWidget )
	private UImage progress_right;

	private UMaterialInstanceDynamic progressMaterial_l;
	private UMaterialInstanceDynamic progressMaterial_r;

	UFUNCTION(BlueprintOverride)
	void Construct()
	{
		progressMaterial_l = CreateDynamicMaterialFromImageBrush( progress_left );
		if ( IsValid( progressMaterial_l ) )
		{
			progress_left.SetBrushFromMaterial( progressMaterial_l );
		}
		progressMaterial_r = CreateDynamicMaterialFromImageBrush( progress_right );
		if ( IsValid( progressMaterial_r ) )
		{
			progress_right.SetBrushFromMaterial( progressMaterial_r );
		}
		SetPercent( 0.0f );
	}

	void SetPercent( float percent )
	{
		if ( IsValid( progressMaterial_l ) )
		{
			float currentPercent = progressMaterial_l.GetScalarParameterValue( MaterialParameter::LERP_ALPHA );
			if ( !Math::IsNearlyEqual( currentPercent, percent ) )
			{
				progressMaterial_l.SetScalarParameterValue( MaterialParameter::LERP_ALPHA, percent );
			}
		}
		if ( IsValid( progressMaterial_r ) )
		{
			float currentPercent = progressMaterial_r.GetScalarParameterValue( MaterialParameter::LERP_ALPHA );
			if ( !Math::IsNearlyEqual( currentPercent, percent ) )
			{
				progressMaterial_r.SetScalarParameterValue( MaterialParameter::LERP_ALPHA, percent );
			}
		}
	}
}