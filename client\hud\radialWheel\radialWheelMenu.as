UCLASS( Abstract )
class UAS_RadialWheelMenu : UNC_RadialList
{
	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage radialWheel;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_RadialWheelDetails radialWheelDetails;

	private UMaterialInstanceDynamic radialWheelMaterial;
	private int numSegments = 0;

	private const FName SEGMENTS_PARAMETER_NAME = n"Segments";
	private const FName SELECTOR_ANGLE_PARAMETER_NAME = n"SelectorAngle";
	private const FName SEGMENT_ANGLE_PARAMETER_NAME = n"SegmentAngle";
	private const FName UNSELECTED_STATE_PARAMETER_NAME = n"SelectedState";

	float savedAngleOffset = 0;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		radialWheelMaterial = CreateDynamicMaterialFromImageBrush( radialWheel );
		if ( IsValid( radialWheelMaterial ) )
		{
			radialWheel.SetBrushFromMaterial( radialWheelMaterial );
		}

		OnDataUpdated.AddUFunction( this, n"OnDataUpdatedCallback" );
	}

	void SetItemOffset( float newOffset )
	{
		savedAngleOffset = newOffset;
		ItemRingOffset = 270+newOffset;
		radialWheel.SetRenderTransformAngle( newOffset );
	}

	UFUNCTION( BlueprintOverride )
	void Destruct()
	{
		OnDataUpdated.Unbind( this, n"OnDataUpdatedCallback" );
	}

	UFUNCTION( BlueprintOverride )
	void OnShowStart()
	{
		shouldDeactivate = true;
		EnableInputEvents();

		// TODO @jmccarty: There is an optimization here to remove callbacks to our own events
		OnInputPositionChanged.AddUFunction( this, n"OnInputPositionChangedCallback" );
		OnSelectedIndexChanged.AddUFunction( this, n"OnSelectedIndexChangedCallback" );
		OnSelectedItemChanged.AddUFunction( this, n"OnSelectedItemChangedCallback" );
	}

	bool shouldDeactivate = false;

	UFUNCTION( BlueprintOverride )
	void OnHideStart()
	{
		TryDeactivate();
	}

	// HACK because OnHideStart() is not reliably called when this is hidden
	void TryDeactivate()
	{
		if ( shouldDeactivate )
		{
			DisableInputEvents();

			// TODO @jmccarty: There is an optimization here to remove callbacks to our own events
			OnInputPositionChanged.Unbind( this, n"OnInputPositionChangedCallback" );
			OnSelectedIndexChanged.Unbind( this, n"OnSelectedIndexChangedCallback" );
			OnSelectedItemChanged.Unbind( this, n"OnSelectedItemChangedCallback" );

			shouldDeactivate = false;
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void OnInputPositionChangedCallback( UNC_RadialList radialList, float32 angle, FVector2D pos )
	{
		if ( IsValid( radialWheelMaterial ) )
		{
			float selectorAngle = (angle-savedAngleOffset) / 360.0f;
			SetUniqueScalarParameter( radialWheelMaterial, SELECTOR_ANGLE_PARAMETER_NAME, selectorAngle );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void OnSelectedIndexChangedCallback( int oldIndex, int newIndex )
	{
		if ( IsValid( radialWheelMaterial ) )
		{
			float segmentAngle = numSegments > 0 ? float( newIndex ) / numSegments : 0.0f;
			SetUniqueScalarParameter( radialWheelMaterial, SEGMENT_ANGLE_PARAMETER_NAME, segmentAngle );

			float selectedState = newIndex >= 0 ? 1.0f : 0.0f;
			SetUniqueScalarParameter( radialWheelMaterial, UNSELECTED_STATE_PARAMETER_NAME, selectedState );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void OnSelectedItemChangedCallback( UNC_RadialListItemData oldItem, UNC_RadialListItemData newItem )
	{
		if ( !IsValid( newItem ) || oldItem == newItem )
			return;

		radialWheelDetails.SetItem( newItem );
	}

	UFUNCTION( NotBlueprintCallable )
	void OnDataUpdatedCallback( int numWidgets )
	{
		if ( IsValid( radialWheelMaterial ) )
		{
			numSegments = numWidgets;
			SetUniqueScalarParameter( radialWheelMaterial, SEGMENTS_PARAMETER_NAME, float( numSegments ) );
		}
	}
}