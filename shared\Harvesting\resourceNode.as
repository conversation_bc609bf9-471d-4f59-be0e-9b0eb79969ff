
enum EResourceBreakMethod
{
	PROTOTYPE,
	MODELSWAP,
	TREE,
	CHUNKABLE UMETA( DisplayName = "OLD TREE DEPRICATED" ),
}

struct FResourceModelSwapData
{
	UPROPERTY()
	UStaticMesh meshAsset;

	// Swap Mesh Material Overrides
	UPROPERTY( Meta = ( DisplayName = "   ►  MaterialOverride" ) )
	TMap<FName, UMaterialInterface> swapMaterialOverride;

	UPROPERTY()
	UNCAudioAsset swapSFX;
	UPROPERTY()
	UNiagaraSystem swapFX;
	UPROPERTY()
	float32 FXScaleMultiplier = 1.0f;
	UPROPERTY()
	FVector FxOffset = FVector::ZeroVector;
}

struct FResourceAimAssistData
{
	UPROPERTY()
	FVector Offset = FVector( 0, 0, 150 );
	UPROPERTY()
	float32 Radius = 24;
}

struct FResourceMeshElement
{
	UPROPERTY()
	int ResourceAmount = 100;

	// override for how much damage the resource can take from bullets. -1 equals whatever the ResourceAmount is.
	UPROPERTY()
	int HealthAmount = -1;

	UPROPERTY()
	FNCMeleeResponseBehavior MeleeResponseData;

	UPROPERTY()
	FVector StartScale3D = FVector( 1, 1, 1 );

	UPROPERTY()
	UStaticMesh MeshAsset;

	UPROPERTY()
	UStaticMesh ShadowProxy;

	// Mesh Material Overrides
	UPROPERTY( Meta = ( DisplayName = "   ►  MaterialOverride" ) )
	TMap<FName, UMaterialInterface> meshMaterialOverride;

	UPROPERTY()
	UStaticMesh DestructibleMeshAsset;

	UPROPERTY()
	FRME_CategoryBreakable breakableData;

	UPROPERTY( BlueprintReadWrite )
	FRME_CategoryRegrow RegrowData;

	UPROPERTY()
	FResourceAimAssistData aimAssistData;
}

struct FRME_CategoryBreakable
{
	UPROPERTY()
	EResourceBreakMethod breakMethod = EResourceBreakMethod::PROTOTYPE;

	UPROPERTY( Meta = ( EditCondition = "breakMethod == EResourceBreakMethod::CHUNKABLE", EditConditionHides ) )
	TSubclassOf<AAS_ChunkableModelData> ChunkableModelData;

	UPROPERTY( Meta = ( EditCondition = "breakMethod == EResourceBreakMethod::PROTOTYPE", EditConditionHides ) )
	UStaticMesh BrokenMeshAsset;
	UPROPERTY( Meta = ( EditCondition = "breakMethod == EResourceBreakMethod::PROTOTYPE", EditConditionHides ) )
	FVector BrokenScale3D = FVector::OneVector;
	UPROPERTY( Meta = ( EditCondition = "breakMethod == EResourceBreakMethod::PROTOTYPE || breakMethod == EResourceBreakMethod::CHUNKABLE", EditConditionHides ) )
	UNCAudioAsset breakSFX;
	UPROPERTY( Meta = ( EditCondition = "breakMethod == EResourceBreakMethod::PROTOTYPE || breakMethod == EResourceBreakMethod::CHUNKABLE", EditConditionHides ) )
	UNiagaraSystem breakFX;
	UPROPERTY( Meta = ( EditCondition = "breakMethod == EResourceBreakMethod::PROTOTYPE || breakMethod == EResourceBreakMethod::CHUNKABLE", EditConditionHides ) )
	float32 FxScaleMultiplier = 1.0f;
	UPROPERTY( Meta = ( EditCondition = "breakMethod == EResourceBreakMethod::PROTOTYPE || breakMethod == EResourceBreakMethod::CHUNKABLE", EditConditionHides ) )
	FVector FxOffset = FVector::ZeroVector;

	// the order is least broken [0] -> to most broken [n]
	UPROPERTY( Meta = ( EditCondition = "breakMethod == EResourceBreakMethod::MODELSWAP || breakMethod == EResourceBreakMethod::TREE", EditConditionHides ) )
	TArray<FResourceModelSwapData> ModelSwapData;

	UPROPERTY( Meta = ( EditCondition = "breakMethod == EResourceBreakMethod::TREE", EditConditionHides ) )
	UStaticMesh canopyAsset;
	// Canopy Material Overrides
	UPROPERTY( Meta = ( EditCondition = "breakMethod == EResourceBreakMethod::TREE", EditConditionHides, DisplayName = "   ►  MaterialOverride" ) )
	TMap<FName, UMaterialInterface> canopyMaterialOverride;
	UPROPERTY( Meta = ( EditCondition = "breakMethod == EResourceBreakMethod::TREE", EditConditionHides ) )
	TSubclassOf<AAS_PhysicsTree_v2> trunkPhysicsBP;
	// Physics Trunk BP Material Overrides
	UPROPERTY( Meta = ( EditCondition = "breakMethod == EResourceBreakMethod::TREE", EditConditionHides, DisplayName = "   ►  MaterialOverride" ) )
	TMap<FName, UMaterialInterface> trunkMaterialOverride;
	UPROPERTY( Meta = ( EditCondition = "breakMethod == EResourceBreakMethod::TREE || breakMethod == EResourceBreakMethod::CHUNKABLE", EditConditionHides ) )
	FRME_CategoryTree treeFallData;
}

struct FRME_CategoryRegrow
{
	UPROPERTY()
	bool enableDitherShader = true;

	// ONLY fill this out if you want specific materials to play the ditherShader. If left blank, all materials will play the effect
	UPROPERTY( Meta = ( EditCondition = "enableDitherShader == true", EditConditionHides ) )
	TArray<int> regrowMaterialOverrides;

	UPROPERTY( Meta = ( EditCondition = "enableDitherShader == true", EditConditionHides ) )
	float32 ditherShaderDelay = 0.25;
	UPROPERTY( Meta = ( EditCondition = "enableDitherShader == true", EditConditionHides ) )
	float32 ditherShaderDuration = 1.0;

	UPROPERTY()
	UNiagaraSystem regrowFX;
	UPROPERTY()
	FVector regrowFxScale = FVector::OneVector;
}

event void
FEvent_ResourceNodeEvent( AAS_ResourceNode resource );

UCLASS( Abstract )
class AAS_ResourceNode : ANCResourceBase
{
	const float32 RESOURCE_GROWBACK_RETRY_DELAY = 5.0;

	UPROPERTY( DefaultComponent )
	UAS_GamemodeSpecifierComponent modeSpecifier;

	UPROPERTY( DefaultComponent, RootComponent )
	USceneComponent root;

	UPROPERTY( Transient, EditDefaultsOnly, meta = ( GetOptions = "GetEditorAssetEntries" ) )
	FString BP_Preview;

	UPROPERTY( EditDefaultsOnly, Meta = ( DisplayPriority = 0 ) )
	TArray<FResourceMeshElement> meshEntries;

	UPROPERTY( NotEditable )
	bool __canModelSwap = false;

	UPROPERTY( Transient, EditInstanceOnly, Category = "Debug" )
	bool DebugPlayBreakFX;

	UPROPERTY( EditInstanceOnly, Category = "Debug", Meta = ( EditCondition = "__canModelSwap == true", EditConditionHides ) )
	bool DebugModelSwap;

	UPROPERTY( Transient, EditInstanceOnly, Category = "Debug", meta = ( GetOptions = "Debug_GetSwapEntries", EditCondition = "DebugModelSwap == true", EditConditionHides ) )
	FString swapEntry;

	UPROPERTY( EditInstanceOnly, meta = ( GetOptions = "GetEditorAssetEntries" ) )
	FString assetEntry;

	UPROPERTY( DefaultComponent, NotVisible )
	UStaticMeshComponent staticMesh;

	UPROPERTY( DefaultComponent, NotVisible )
	UStaticMeshComponent ditherBrokenMesh;

	UPROPERTY( DefaultComponent, NotVisible )
	UStaticMeshComponent ditherStartMesh;

	UPROPERTY( DefaultComponent, NotVisible )
	UNCDestructibleAnimMeshComponent DestructMeshComponent;

	UPROPERTY( DefaultComponent )
	UHealthComponentNetworked healthComponent;
	default healthComponent.bTakesStructuralDamage = true;

	UPROPERTY( DefaultComponent )
	UNCMaterialImpactComponent materialImapctComponent;

	UPROPERTY( DefaultComponent, NotVisible )
	UAS_MeleeResponseComponent meleeResponseComponent;

	UPROPERTY( DefaultComponent, EditDefaultsOnly )
	UNCMeleeAssistComponent meleeAssistComponent;

	UPROPERTY( DefaultComponent )
	UAS_PingableComponent pingableComponent;
	default pingableComponent.pingType = EPlayerPingType::RESOURCENODE;

	UPROPERTY( EditDefaultsOnly )
	FGameplayTag lootToGive;

	UPROPERTY( EditDefaultsOnly )
	bool disableDistanceFields = false;

	// UPROPERTY( NotVisible )
	// FNCNetInt net_ResourceAmount( 1 ); // should be set by the FResourceMeshElement

	private int _internalResourceAmount;

	FTimerHandle TryRegrowTimerHandle;
	FTransform RegrowTrace;

	FEvent_ResourceNodeEvent sv_OnResourceRegrown;
	FEvent_ResourceNodeEvent sh_OnResourceAmountChanged;

	int GetResourceAmount() const
	{
		return _internalResourceAmount;
	}

	int CalculateResourceAmountFromHealth( float32 currentHealth ) const
	{
		float32 maxHealth = healthComponent.GetMaxHealth();

		int entryID			= EditorAssetEntryToInt( assetEntry );
		float32 maxResource = GetResourceAmountFromEnum( entryID );

		return Math::RoundToInt( ( currentHealth / maxHealth ) * maxResource );
	}

	UFUNCTION( BlueprintOverride )
	void ConstructionScript()
	{
		if ( meshEntries.Num() == 0 )
			return;

		staticMesh.SetWorldPositionOffsetDisableDistance( GameConst::RESOURCE_SHADOW_OFFSET_DISABLE_DIST );
		staticMesh.ShadowCacheInvalidationBehavior = GameConst::RESOURCE_SHADOW_CACHE_INVALIDATION_BEHAVIOR;
		ditherBrokenMesh.SetWorldPositionOffsetDisableDistance( GameConst::RESOURCE_SHADOW_OFFSET_DISABLE_DIST );
		ditherBrokenMesh.ShadowCacheInvalidationBehavior = GameConst::RESOURCE_SHADOW_CACHE_INVALIDATION_BEHAVIOR;
		ditherStartMesh.SetWorldPositionOffsetDisableDistance( GameConst::RESOURCE_SHADOW_OFFSET_DISABLE_DIST );
		ditherStartMesh.ShadowCacheInvalidationBehavior = GameConst::RESOURCE_SHADOW_CACHE_INVALIDATION_BEHAVIOR;

		ParentConstructionScript();
	}

	protected void ParentConstructionScript()
	{
#if EDITOR
		int entryID = EditorAssetEntryToInt( assetEntry );
		ResetModel( entryID );

		if ( InEditor() )
		{
			if ( !Debug_CanModelSwap() )
			{
				__canModelSwap = false;
				DebugModelSwap = false;
			}
			else
			{
				__canModelSwap = true;
			}

			if ( DebugPlayBreakFX )
			{
				Constructor_DebugPlayBreakFX();
				DebugPlayBreakFX = false;
			}

			Debug_CleanMesh();
			if ( DebugModelSwap && Debug_CanModelSwap() )
				Constructor_DebugModelSwap();
		}

		SetActorCustomPrimitiveData();
#endif
	}

	UFUNCTION( BlueprintOverride )
	void ServerBeginPlay()
	{
		int entryID = EditorAssetEntryToInt( assetEntry );

		SharedBeginPlay();
		ResetResourceAmount( entryID );
		svHealthInit();

		for ( int i = 0; i < meshEntries.Num(); i++ )
		{
			EResourceBreakMethod brkMethod = meshEntries[i].breakableData.breakMethod;
			switch ( brkMethod )
			{
				case EResourceBreakMethod::CHUNKABLE:
					ScriptAssert( HasChunkableModelData( i ), f"{this} scale[{i}] set EResourceBreakMethod[{brkMethod}] but no valid chunkable model data" );
					break;

				case EResourceBreakMethod::TREE:
					ScriptAssert( this.IsA( AAS_ResourceTree_v2::StaticClass() ), f"{this} scale[{i}] set EResourceBreakMethod[{brkMethod}], Switch BP class setting to AAS_ResourceTree instead of [{this.Class}]." );
					ScriptAssert( HasCanopyData( i ), f"{this} scale[{i}] set EResourceBreakMethod[{brkMethod}] but no valid canopy mesh set" );
					ScriptAssert( HasTrunkData( i ), f"{this} scale[{i}] set EResourceBreakMethod[{brkMethod}] but no valid trunk mesh set" );
					fallthrough;

				case EResourceBreakMethod::MODELSWAP:
					ScriptAssert( HasModelSwapData( i ), f"{this} scale[{i}] set EResourceBreakMethod[{brkMethod}] but no valid model swap data" );
					break;

				case EResourceBreakMethod::PROTOTYPE:
					break;
			}
		}

		InitRegrowTrace();
	}

	UFUNCTION( BlueprintOverride )
	void ClientBeginPlay()
	{
		SetActorCustomPrimitiveData();
		SharedBeginPlay();

		pingableComponent.pingableInfo = GetPingableObjectInfoFromLootData( EPlayerPingType::LOOT, GetLootDataByIndex( lootToGive ) );

		// On the client, this actor may just be streaming out, but not actually
		// destroyed. Whenever the actor is streamed back in, its BeginPlay()
		// will be called again.
		//
		// Make sure to unbind these in EndPlay()
		// 		net_ResourceAmount.OnReplicated().AddUFunction( this, n"OnResourceAmountChanged_Internal" );
		healthComponent.OnNetworkedHealthChanged.AddUFunction( this, n"OnNetworkedHealthChanged" );

		InitDitherMeshes();
	}

	UFUNCTION( BlueprintOverride )
	void EndPlay( EEndPlayReason EndPlayReason )
	{
		// On the client, this actor may just be streaming out, but not actually
		// destroyed. Whenever the actor is streamed back in, its BeginPlay()
		// will be called again. So, we make sure that its delegates are
		// unbound, to avoid getting an error when rebinding later.
		if ( IsClient() )
		{
			// 			net_ResourceAmount.OnReplicated().UnbindObject( this );

			healthComponent.OnNetworkedHealthChanged.Unbind( this, n"OnNetworkedHealthChanged" );

			__dynamicMats.Empty(); // clear the cache so they can be remade
		}
	}

	UFUNCTION( BlueprintEvent )
	void SharedBeginPlay()
	{
		BeginPlay_CleanupConstructor();

		if ( disableDistanceFields )
		{
			staticMesh.AffectDistanceFieldLighting			  = false;
			ditherBrokenMesh.AffectDistanceFieldLighting	  = false;
			ditherStartMesh.AffectDistanceFieldLighting		  = false;
			DestructMeshComponent.AffectDistanceFieldLighting = false;
		}

		int entryID								 = EditorAssetEntryToInt( assetEntry );
		meleeResponseComponent.MeleeResponseData = GetMeleeResponseBehaviorFromEnum( entryID );

		if ( GetCvarBool( f"ScriptDebug.DestructibleResources" ) )
		{
			DestructMeshComponent.SetStaticMesh( GetDestructAnimMeshFromEnum( entryID ) );
			DestructMeshComponent.InitializeDestructibleMeshComponent( staticMesh.GetStaticMesh(), staticMesh.Bounds.Box.GetCenter() );
		}

		float32 maxHealth = GetHealthFromEnum( entryID );
		healthComponent.SetMaxHealth( maxHealth );
		healthComponent.SetHealth( maxHealth );

		int initialAmount = GetResourceAmountFromEnum( entryID );
		// net_ResourceAmount.SetNetValue( initialAmount );
		_internalResourceAmount = initialAmount;

		NCNetComponent.CreateNCNetBaseSnapshot();

		OnNetworkedHealthChanged( 0, healthComponent.GetHealth() );
	}

	void BeginPlay_CleanupConstructor()
	{
		if ( DebugModelSwap )
		{
			DebugModelSwap = false;
			int entryID	   = EditorAssetEntryToInt( assetEntry );
			ResetModel( entryID );
		}
	}

	/****************************************************************\

	██   ██ ███████  █████  ██      ████████ ██   ██      ██████  ██████  ███    ███ ██████
	██   ██ ██      ██   ██ ██         ██    ██   ██     ██      ██    ██ ████  ████ ██   ██
	███████ █████   ███████ ██         ██    ███████     ██      ██    ██ ██ ████ ██ ██████
	██   ██ ██      ██   ██ ██         ██    ██   ██     ██      ██    ██ ██  ██  ██ ██
	██   ██ ███████ ██   ██ ███████    ██    ██   ██      ██████  ██████  ██      ██ ██

	\****************************************************************/

	void svHealthInit()
	{
		healthComponent.BP_OnPreReceivedDamage.BindUFunction( this, n"sv_OnPreReceivedDamage" );
		healthComponent.BP_OnPostReceivedDamage.AddUFunction( this, n"sv_OnPostReceivedDamage" );
	}

	UFUNCTION( BlueprintEvent )
	void sv_OnPreReceivedDamage( FDamageInfo& damageInfo )
	{
		ScriptAssert( IsServer(), "Must Run On Server" );

		if ( GetCvarBool( f"ScriptDebug.ResourceDisableWeaponDamage" ) )
		{
			damageInfo.damage = 0.0f;
			return;
		}

		bool hasGiveLootFlag = Bitflags::HasFlag( damageInfo.scriptDamageFlags, EScriptDamageFlags::DF_MELEE | EScriptDamageFlags::DF_RESOURCE ) && !Bitflags::HasFlag( damageInfo.scriptDamageFlags, EScriptDamageFlags::DF_RESOURCE_NO_GIVE );
		bool hasDestroyFlag	 = Bitflags::HasFlag( damageInfo.scriptDamageFlags, EScriptDamageFlags::DF_EXPLOSIVE | EScriptDamageFlags::DF_RESOURCE_NO_GIVE );
		bool hasDmgFlag		 = hasGiveLootFlag || hasDestroyFlag;
		int health			 = Math::FloorToInt( GetHealth() );

		if ( !hasDmgFlag || health <= 0 || damageInfo.damage <= 0 )
		{
			damageInfo.damage = 0.0f;
			return;
		}

		int damage		  = Math::FloorToInt( damageInfo.damage );
		int damageMult	  = Bitflags::HasFlag( damageInfo.scriptDamageFlags, EScriptDamageFlags::DF_EXPLOSIVE ) ? 10 : 1;
		damageInfo.damage = damage * damageMult;

		AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( damageInfo.attacker );
		if ( !IsValid( player ) )
			return;

		if ( !hasGiveLootFlag )
			return;

		float maxHealth	 = Math::Max( 1, GetMaxHealth() );
		float healthFrac = Math::Min( damageInfo.damage / maxHealth, 1.0f );

		int entryID		= EditorAssetEntryToInt( assetEntry );
		int resourceMax = GetResourceAmountFromEnum( entryID );

		if ( IsValid( player ) && player.HasTrinketPassive( GameplayTags::Classes_Passives_Harvester ) && damageInfo.damage >= health )
		{
			player.Server_OnTrinketUsed();
			FPlayerEquipmentData trinketData = player.GetTrinket();
			resourceMax						 = Math::RoundToInt( resourceMax * trinketData.dataFloat2 );
		}

		int amountToGive = Math::FloorToInt( healthFrac * resourceMax );
		if ( amountToGive <= 0 )
			return;

		Server_GiveResource( player, lootToGive, amountToGive );

		return;
	}

	UFUNCTION()
	void sv_OnPostReceivedDamage( UHealthComponent damagedComponent, const FDamageInfo&in damageInfo )
	{
		ScriptAssert( IsServer(), "Must Run On Server" );
		ScriptAssert( damagedComponent == healthComponent, f"Expected local health component" );

		float healthLeft = Math::Max( 0, GetHealth() );
		float maxHealth	 = Math::Max( 1, GetMaxHealth() );
		float healthFrac = healthLeft / maxHealth;

		int entryID		 = EditorAssetEntryToInt( assetEntry );
		int resourceMax	 = GetResourceAmountFromEnum( entryID );
		int resourceLeft = Math::RoundToInt( healthFrac * resourceMax );

		AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( damageInfo.attacker );
		ScriptCallbacks().server_OnResourceNodeDamaged.Broadcast( this, damageInfo );

		if ( resourceLeft == 0 )
		{
			if ( GetCvarBool( f"ScriptDebug.DestructibleResources" ) )
			{
				DestructMeshComponent.SetDestructibleToDestroyedState_Server( damageInfo, EDestructionImpactForceDirType::UseDamageSourcePos );
			}
		}

		bool updateHealthComponent = false;
		Server_SetResourceAmount( resourceLeft, updateHealthComponent );
	}

	/****************************************************************\

	 ██████  ██ ██    ██ ███████     ██████  ███████ ███████  ██████  ██    ██ ██████   ██████ ███████
	██       ██ ██    ██ ██          ██   ██ ██      ██      ██    ██ ██    ██ ██   ██ ██      ██
	██   ███ ██ ██    ██ █████       ██████  █████   ███████ ██    ██ ██    ██ ██████  ██      █████
	██    ██ ██  ██  ██  ██          ██   ██ ██           ██ ██    ██ ██    ██ ██   ██ ██      ██
	 ██████  ██   ████   ███████     ██   ██ ███████ ███████  ██████   ██████  ██   ██  ██████ ███████

	\****************************************************************/
	void Server_GiveResource( AAS_PlayerEntity asPawn, FGameplayTag resourceToGive, int amountToGive )
	{
		int amountLeftover = asPawn.AddToAnyBackpack( MakeBackpackItem( resourceToGive, amountToGive ) );

		if ( amountLeftover > 0 && IsServer() )
			Server_DropLootItem( asPawn, MakeBackpackItem( resourceToGive, amountToGive ) );

		int amountGiven = amountToGive - amountLeftover;
		if ( amountGiven > 0 )
		{
			const FLootDataStruct& data = GetLootDataByIndex( resourceToGive );
			FString command				= f"ServerCommand_NotifyGatherResource {data.intIndex} {amountGiven} {CountItemsInBackpackAndValidBoxes(asPawn, data.index, asPawn, CraftingSource::BACKPACK)}";
			UNCRemoteScriptCommands::SendServerCommand( asPawn, command );

			ScriptCallbacks().server_onResourceGathered.Broadcast( asPawn, this, resourceToGive, amountGiven );
		}
		else
		{
			asPawn.NotifyBackpackFull( resourceToGive );
		}
	}

	void Server_SetResourceAmount( int newAmount, bool updateHealthComponent = true )
	{
		int oldAmount = _internalResourceAmount; // net_ResourceAmount;

												 // 		net_ResourceAmount.SetNetValue( newAmount );
		_internalResourceAmount = newAmount;

		OnResourceAmountChanged( oldAmount, newAmount );

		if ( updateHealthComponent )
		{
			int entryID		 = EditorAssetEntryToInt( assetEntry );
			int resourceMax	 = GetResourceAmountFromEnum( entryID );
			float healthFrac = float( newAmount ) / float( resourceMax );

			float maxHealth = Math::Max( 1, GetMaxHealth() );
			float newHealth = maxHealth * healthFrac;

			healthComponent.SetHealth( newHealth );
		}
	}

	void OnEmptied( bool PlayFX = true, bool ReGrow = true )
	{
		int entryID = EditorAssetEntryToInt( assetEntry );
		switch ( GetBreakMethodFromEnum( entryID ) )
		{
			case EResourceBreakMethod::PROTOTYPE:
			{
				if ( IsServer() && PlayFX )
					PlayDefaultFXPackage();

				UStaticMesh mesh = GetBrokenMeshFromEnum( entryID );
				if ( IsValid( mesh ) )
				{
					FVector scale3d						 = GetBrokenScale3DFromEnum( entryID );
					TMap<FName, UMaterialInterface> mats = GetBrokenMeshMaterialOverridesFromEnum( entryID );
					SetAllMeshData( mesh, scale3d, mats );
				}
				else if ( IsServer() )
					Destroy();
			}
			break;

			case EResourceBreakMethod::MODELSWAP:
			case EResourceBreakMethod::TREE:
			{
			}
			break;

			case EResourceBreakMethod::CHUNKABLE:
			{
				if ( IsServer() && PlayFX )
					PlayDefaultFXPackage();
			}
			break;
		}

		if ( IsServer() )
		{
			ScriptCallbacks().server_OnResourceNodeEmptied.Broadcast( this );

			if ( ReGrow )
			{
				System::ClearAndInvalidateTimerHandle( TryRegrowTimerHandle );
				float32 regenDelay	 = GetThenResetRegenDelay();
				TryRegrowTimerHandle = System::SetTimer( this, n"TryReGrowResourceNode", regenDelay, false );
			}
		}
	}

	void PlayDefaultFXPackage()
	{
		int entryID			   = EditorAssetEntryToInt( assetEntry );
		UNiagaraSystem breakFX = GetFxFromEnum( entryID );
		UNCAudioAsset breakSFX = GetSFXFromEnum( entryID );
		FVector location	   = GetActorLocation();
		FRotator rotation	   = GetActorRotation();
		float scale			   = GetFxScaleMultiplierFromEnum( entryID );
		FVector offset		   = GetFxOffsetFromEnum( entryID );

#if EDITOR
		if ( InEditor() )
		{
			UNiagaraComponent editorFX = Client_SpawnEffectAtLocation_OneShot( breakFX, location, rotation );
			editorFX.SetFloatParameter( GameConst::RESOURCE_FXSCALE_PARAM, scale );
			editorFX.SetVectorParameter( GameConst::RESOURCE_FXOFFSET_PARAM, offset );
			return;
		}
#endif

		FNiagaraVariablePackage package;
		package.AddVariableFloat( GameConst::RESOURCE_FXSCALE_PARAM, scale );
		package.AddVariableVec3( GameConst::RESOURCE_FXOFFSET_PARAM, offset );

		Server_SpawnEffectAtLocation_OneShot_SetNiagaraVariables( breakFX, location, rotation, package );
		Server_EmitSoundOnEntity( breakSFX, this );
	}

	void PlaySwapFXPackage( int entryID, int swapIndex )
	{
		TArray<FResourceModelSwapData> modelSwapData = GetModelSwapData( entryID );
		FResourceModelSwapData data					 = modelSwapData[swapIndex];
		UNiagaraSystem breakFX						 = data.swapFX;
		FVector location							 = GetActorLocation();
		FRotator rotation							 = GetActorRotation();
		float scale									 = data.FXScaleMultiplier;
		FVector offset								 = data.FxOffset;

#if EDITOR
		if ( InEditor() )
		{
			if ( breakFX != nullptr )
			{
				UNiagaraComponent editorFX = Client_SpawnEffectAtLocation_OneShot( breakFX, location, rotation );
				editorFX.SetFloatParameter( GameConst::RESOURCE_FXSCALE_PARAM, scale );
				editorFX.SetVectorParameter( GameConst::RESOURCE_FXOFFSET_PARAM, offset );
			}
			return;
		}
#endif

		if ( IsServer() )
		{
			if ( breakFX != nullptr )
			{
				FNiagaraVariablePackage package;
				package.AddVariableFloat( GameConst::RESOURCE_FXSCALE_PARAM, scale );
				package.AddVariableVec3( GameConst::RESOURCE_FXOFFSET_PARAM, offset );
				Server_SpawnEffectAtLocation_OneShot_SetNiagaraVariables( breakFX, location, rotation, package );
			}

			if ( data.swapSFX != nullptr )
				Server_EmitSoundAtLocation( data.swapSFX, location + FVector( 0, 0, 150 ) );
		}
	}

	float32 cachedRegrowTime = 0.0f;
	float32 GetRegenDelay()
	{
		if ( cachedRegrowTime == 0 )
			cachedRegrowTime = GetCvarBool( "ScriptDebug.ResourceRegen" ) ? 10.0f : GameModeDefaults().GameModeRules_ResourceAutoRegrow_Delay;

		return cachedRegrowTime;
	}

	private float32 GetThenResetRegenDelay()
	{
		float32 regrowTime = GetRegenDelay();
		cachedRegrowTime   = 0.0f;

		return regrowTime;
	}

	UFUNCTION( BlueprintEvent )
	void OnResourceAmountChanged( int oldAmount, int newAmount )
	{
		_internalResourceAmount = newAmount;

		int entryID					   = EditorAssetEntryToInt( assetEntry );
		EResourceBreakMethod brkMethod = GetBreakMethodFromEnum( entryID );
		bool isSwapMethod			   = brkMethod == EResourceBreakMethod::MODELSWAP || brkMethod == EResourceBreakMethod::TREE;

		if ( newAmount < oldAmount && isSwapMethod )
			sh_UpdateModelSwap( entryID );

		if ( IsClient() && isSwapMethod && newAmount == 0 )
		{
			if ( GetCvarBool( f"ScriptDebug.DestructibleResources" ) )
				DestructMeshComponent.SetDestructibleToDestroyedState_Client();
		}

		bool regrow = GameModeDefaults().GameModeRules_ResourceAutoRegrow || GetCvarBool( "ScriptDebug.ResourceRegen" );
		if ( newAmount <= 0 )
			OnEmptied( true, regrow );
		else if ( newAmount == GetResourceAmountFromEnum( entryID ) && oldAmount < newAmount )
		{
			ResetModel( entryID );
			if ( IsClient() && oldAmount != 1 ) // old resource amount of 1 means server just started... don't regrow
				cl_RegrowFx();
		}
		
		sh_OnResourceAmountChanged.Broadcast( this );
	}

	UFUNCTION()
	private void OnResourceAmountChanged_Internal( int oldValue, int newValue )
	{
		// Print( f"net_ResourceAmount -- {net_ResourceAmount}" );
		// Print( f"GetResourceAmount() -- {GetResourceAmount()}" );
	}

	UFUNCTION()
	private void OnNetworkedHealthChanged( float32 oldHealth, float32 newHealth )
	{
		_internalResourceAmount = CalculateResourceAmountFromHealth( newHealth );
		OnResourceAmountChanged( CalculateResourceAmountFromHealth( oldHealth ), _internalResourceAmount );
	}

	void sh_UpdateModelSwap( int entryID )
	{
		ScriptAssert( HasModelSwapData( entryID ), f"UpdateModelSwap called on {this} resource entry [{entryID}] without model swap data" );

		TArray<FResourceModelSwapData> modelSwapData = GetModelSwapData( entryID );

		int swapIndex = CalculateSwapIndex( entryID );
		if ( swapIndex < 0 )
			return;

		FResourceModelSwapData data = modelSwapData[swapIndex];
		if ( GetMeshComponent().StaticMesh == data.meshAsset )
			return;

		FVector scale3d						 = GetScale3DFromEnum( entryID );
		TMap<FName, UMaterialInterface> mats = GetSwapMaterialOverridesFromEnum( entryID, swapIndex );
		SetAllMeshData( data.meshAsset, scale3d, mats );

		PlaySwapFXPackage( entryID, swapIndex );
	}

	int CalculateSwapIndex( int entryID )
	{
		TArray<FResourceModelSwapData> modelSwapData = GetModelSwapData( entryID );

		int maxResourceAmount		   = GetResourceAmountFromEnum( entryID );
		float32 defaultResourcePerSwap = maxResourceAmount / float32( modelSwapData.Num() );

		// we have to do a manual calculation here because it can sometimes get called before OnNetworkedHealthChanged; (usually when we update from the physics tree spawning)
		_internalResourceAmount = CalculateResourceAmountFromHealth( healthComponent.GetHealth() );

		float32 resourceAmount = float32( GetResourceAmount() );

		// make sure we swap on the first hit no matter what unless the only version is the broken version
		int swapIndex = modelSwapData.Num() > 1 ? 0 : -1;

		// Print( f"swapIndex {swapIndex}" );

		for ( int i = modelSwapData.Num() - 1; i >= 0; i-- )
		{
			float32 resourceThreshold = maxResourceAmount - ( defaultResourcePerSwap * ( i + 1 ) );

			if ( resourceAmount > resourceThreshold )
				continue;

			swapIndex = i;
			break;
		}

		return swapIndex;
	}

	/****************************************************************\

	██████  ███████  ██████  ██████   ██████  ██     ██
	██   ██ ██      ██       ██   ██ ██    ██ ██     ██
	██████  █████   ██   ███ ██████  ██    ██ ██  █  ██
	██   ██ ██      ██    ██ ██   ██ ██    ██ ██ ███ ██
	██   ██ ███████  ██████  ██   ██  ██████   ███ ███

	\****************************************************************/
	UFUNCTION( NotBlueprintCallable )
	void TryReGrowResourceNode()
	{
		FVector boxExtent  = RegrowTrace.GetScale3D();
		FVector traceStart = RegrowTrace.GetLocation() + RegrowTrace.Rotator().GetUpVector();
		FVector traceEnd   = traceStart - RegrowTrace.Rotator().GetUpVector();
		FRotator rotation  = RegrowTrace.Rotator();

		FHitResult OutHit;
		TArray<AActor> ignoreActors;
		ignoreActors.Add( this );
		TArray<EObjectTypeQuery> ObjectTypes;
		ObjectTypes.Add( EObjectTypeQuery::WorldDynamic );

		bool KeepTracing = true;
		while ( KeepTracing )
		{
			OutHit = BoxTraceSingleForObjects( traceStart, traceEnd, boxExtent, rotation, ObjectTypes, true, ignoreActors, true );
			if ( GetCvarBool( "ScriptDebug.ResourceRegen" ) )
				DrawDebugBox( traceStart, rotation, boxExtent, 5 );

			bool HitSomething = OutHit.GetbBlockingHit() || OutHit.GetbStartPenetrating();
			if ( !HitSomething )
			{
				KeepTracing = false;
				break;
			}

			if ( !IsValidBlockerForRegrowth( OutHit ) )
			{
				ignoreActors.Add( OutHit.GetActor() );
				continue;
			}
			else
			{
				KeepTracing = false;
				break;
			}
		}

		bool GrowthBlocked = OutHit.bBlockingHit && IsValidBlockerForRegrowth( OutHit );
		if ( GrowthBlocked )
		{
			// resource is now intersected by a new object (i.e. player standing on it, player base, etc ) - try to regrow it again later
			System::ClearAndInvalidateTimerHandle( TryRegrowTimerHandle );

			float32 delay		 = GetCvarBool( "ScriptDebug.ResourceRegen" ) ? 5.0f : RESOURCE_GROWBACK_RETRY_DELAY;
			TryRegrowTimerHandle = System::SetTimer( this, n"TryReGrowResourceNode", delay, false );
			return;
		}

		int entryID = EditorAssetEntryToInt( assetEntry );
		ResetResourceAmount( entryID );
		sv_OnResourceRegrown.Broadcast( this );
	}

	bool IsValidBlockerForRegrowth( FHitResult hitresult )
	{
		AActor inActor = hitresult.GetActor();
		if ( !IsValid( inActor ) )
			return false;

		if ( hitresult.IsResultWorldGeo() )
			return false;

		bool isBlocker = true;
		TArray<UClass> blockerExceptions;
		blockerExceptions.Add( AAS_ResourceNode::StaticClass() );
		blockerExceptions.Add( AAS_RespawningLootNode::StaticClass() );
		blockerExceptions.Add( AAS_DigSiteTower::StaticClass() ); // HACCKKKKK

		for ( UClass checkClass : blockerExceptions )
		{
			if ( inActor.IsA( checkClass ) )
			{
				isBlocker = false;
				break;
			}
		}

		return isBlocker;
	}

	void ResetResourceAmount( int entryID )
	{
		ScriptAssert( IsServer(), "SERVER ONLY" );

		Server_SetResourceAmount( GetResourceAmountFromEnum( entryID ) );
		float32 maxHealth = GetHealthFromEnum( entryID );
		healthComponent.SetHealth( maxHealth );
	}

	void InitRegrowTrace()
	{
		if ( IsServer() && RegrowTrace.Equals( FTransform::Identity ) )
		{
			FVector boxExtent;
			FVector boxOrigin;

			GetActorBounds( true, boxOrigin, boxExtent, false );

			RegrowTrace.SetRotation( GetActorRotation() );
			RegrowTrace.SetLocation( boxOrigin );
			RegrowTrace.SetScale3D( boxExtent );
		}
	}

	/****************************************************************\

	███    ███ ███████ ███████ ██   ██     ██       ██████   ██████  ██  ██████
	████  ████ ██      ██      ██   ██     ██      ██    ██ ██       ██ ██
	██ ████ ██ █████   ███████ ███████     ██      ██    ██ ██   ███ ██ ██
	██  ██  ██ ██           ██ ██   ██     ██      ██    ██ ██    ██ ██ ██
	██      ██ ███████ ███████ ██   ██     ███████  ██████   ██████  ██  ██████

	\****************************************************************/
	void ResetModel( int entryID )
	{
		UStaticMesh mesh					 = GetMeshFromEnum( entryID );
		FVector scale3d						 = GetScale3DFromEnum( entryID );
		TMap<FName, UMaterialInterface> mats = GetMeshMaterialOverridesFromEnum( entryID );
		FResourceAimAssistData aimData		 = GetAimAssistDataFromEnum( entryID );

		SetAllMeshData( mesh, scale3d, mats );

		meleeAssistComponent.SetRelativeLocation( aimData.Offset );
		meleeAssistComponent.SetSphereRadius( aimData.Radius );

		if ( !IsClient() )
			return;

#if EDITOR
		bool cvar	= GetCvarBool( "ScriptDebug.DrawMeleeAimAssist" );
		bool ingame = !InEditor();
		if ( cvar && ingame )
			Thread( this, n"DebugDrawMeleeAimAssist", entryID );
#endif

		FRME_CategoryRegrow regrowData = GetRegrowDataFromEnum( entryID );
		if ( regrowData.enableDitherShader )
			Thread( this, n"HideBrokenMeshAfterDitherDelay", regrowData );
	}

	FNCCoroutineSignal endSignalDrawMeleeAimAssist;
	UFUNCTION()
	void DebugDrawMeleeAimAssist( UNCCoroutine co, int entryID )
	{
		endSignalDrawMeleeAimAssist.Emit();
		co.EndOn( this, endSignalDrawMeleeAimAssist );

		while ( true )
		{
			float delay = 1.0;
			FVector pos = meleeAssistComponent.GetWorldLocation();
			float32 rad = meleeAssistComponent.GetUnscaledSphereRadius();
			DrawDebugSphere( pos, rad, delay + 0.1, FLinearColor::Red );
			co.Wait( delay );
		}
	}

	void SetAllMeshData( UStaticMesh mesh, FVector scale3d, TMap<FName, UMaterialInterface>& mats )
	{
		if ( mesh == nullptr )
			return;

		GetMeshComponent().SetStaticMesh( mesh );
		GetMeshComponent().SetRelativeScale3D( scale3d );
		__SetAllMaterialData( GetMeshComponent(), mesh, mats );
	}

	void __SetAllMaterialData( UStaticMeshComponent meshComp, UStaticMesh mesh, TMap<FName, UMaterialInterface>& mats )
	{
		TArray<FStaticMaterial> ogMats;
		if ( IsValid( mesh ) )
		{
			ogMats = mesh.GetStaticMaterials();

			/*	always reset materials to their defaults. I didn't need to do this before I created the
				ability to override materials, but after that change, this became necessary as it seems new
				models are using old model material indexes instead of also swapping to the new models mats.
				not sure where that culprit is... but honestly, this doesn't seem like a terrible thing to do
				so instead of taking the time to track down that bug, this seems like an acceptable fix		*/
			for ( FStaticMaterial mat : ogMats )
				meshComp.SetMaterialByName( mat.MaterialSlotName, mat.MaterialInterface );
		}

		for ( TMapIterator<FName, UMaterialInterface> iter : mats )
		{
			if ( iter.GetValue() == nullptr )
				continue;
			if ( iter.GetKey() == NAME_None )
				continue;

			int index = iter.GetKey().ToString().ToInt();
			if ( index < ogMats.Num() )
				meshComp.SetMaterial( index, iter.GetValue() );
		}
	}

	const FString EDITER_ENTRY_PREFIX = "Entry ";
	UFUNCTION()
	TArray<FString> GetEditorAssetEntries()
	{
		TArray<FString> strings;

		for ( int i = 0; i < meshEntries.Num(); i++ )
		{
			FString newString = f"{EDITER_ENTRY_PREFIX}{i}";
			strings.Add( newString );
		}

		return strings;
	}

	int EditorAssetEntryToInt( FString assetEntryName ) const
	{
		FString numString = assetEntryName.RightChop( EDITER_ENTRY_PREFIX.Len() );

#if EDITOR
		if ( numString == "" && InEditor() )
			numString = BP_Preview.RightChop( EDITER_ENTRY_PREFIX.Len() );
#endif

		return numString.ToInt();
	}

	protected void SetActorCustomPrimitiveData()
	{
		SetMeshCustomPrimitiveData( GetMeshComponent() );
		SetMeshCustomPrimitiveData( staticMesh );
		SetMeshCustomPrimitiveData( ditherBrokenMesh );
		SetMeshCustomPrimitiveData( ditherStartMesh );
	}

	const int PRIMITIVE_INDEX_HUE	   = 0;
	const int PRIMITIVE_INDEX_PIVOT	   = 1;
	const int PRIMITIVE_INDEX_BOUNDS_Z = 5;

	void SetMeshCustomPrimitiveData( UStaticMeshComponent mesh )
	{
		int entryID			  = EditorAssetEntryToInt( assetEntry );
		UStaticMesh meshAsset = GetMeshFromEnum( entryID );
		if ( !IsValid( meshAsset ) )
			return;

		// PRIMITIVE_INDEX_HUE
		float32 hue = GetHue();
		mesh.SetCustomPrimitiveDataFloat( PRIMITIVE_INDEX_HUE, hue );

		// PRIMITIVE_INDEX_PIVOT
		FTransform pivot;
		switch ( GetBreakMethodFromEnum( entryID ) )
		{
			case EResourceBreakMethod::PROTOTYPE:
			case EResourceBreakMethod::MODELSWAP:
				pivot = GetActorTransform();
				break;

			case EResourceBreakMethod::TREE:
			case EResourceBreakMethod::CHUNKABLE:
				pivot = GetTrunkWorldPivotFromEnum( entryID );
				break;
		}

		mesh.SetCustomPrimitiveDataVector3( PRIMITIVE_INDEX_PIVOT, pivot.GetLocation() );
		float32 halfZ	= float32( meshAsset.Bounds.BoxExtent.Z );
		float32 extraZ	= Distance( pivot.GetLocation(), GetActorLocation() );
		FVector scale3d = GetScale3DFromEnum( entryID );

		// PRIMITIVE_INDEX_BOUNDS_Z
		/*	this should be 2... i.e double the halfZ... but to get it to match better to the
			BoundingBoxBase_0-1_UVW node in the material... I scale it by this instead... no idea why	*/
		const float32 MAGIC_NUMBER = 1.8;
		float32 fullZ			   = float32( halfZ * MAGIC_NUMBER * scale3d.Z ) - ( extraZ * MAGIC_NUMBER );

		mesh.SetCustomPrimitiveDataFloat( PRIMITIVE_INDEX_BOUNDS_Z, fullZ );
	}

	private float32 cachedHue = -1;
	private FVector cachedHueOrigin;
	float32 GetHue()
	{
		if ( cachedHue == -1 || !Math::IsNearlyEqual( GetActorLocation(), cachedHueOrigin, 0.1 ) )
		{
			cachedHueOrigin = GetActorLocation();
			float var		= cachedHueOrigin.Size();
			float varmod	= var % 10;
			cachedHue		= float32( varmod * 0.1 );
		}

		return cachedHue;
	}

	/****************************************************************\

██████  ██ ████████ ██   ██ ███████ ██████
██   ██ ██    ██    ██   ██ ██      ██   ██
██   ██ ██    ██    ███████ █████   ██████
██   ██ ██    ██    ██   ██ ██      ██   ██
██████  ██    ██    ██   ██ ███████ ██   ██

	\****************************************************************/
	FNCCoroutineSignal endSignal_HideBrokenMeshAfterDitherDelay;

	UFUNCTION()
	void HideBrokenMeshAfterDitherDelay( UNCCoroutine co, FRME_CategoryRegrow regrowData )
	{
		ScriptAssert( IsClient(), "CLIENT_ONLY" );

		float32 delay = regrowData.ditherShaderDelay + regrowData.ditherShaderDuration;

		ShowDitherMeshes();

		co.EndOn( this, endSignal_HideBrokenMeshAfterDitherDelay );
		co.Wait( delay );

		HideDitherMeshes();
	}

	void HideDitherMeshes()
	{
		ScriptAssert( IsClient(), "CLIENT_ONLY" );

		ditherBrokenMesh.SetHiddenInGame( true );
		ditherStartMesh.SetHiddenInGame( true );
		GetMeshComponent().SetHiddenInGame( false );

		// put at the end
		endSignal_HideBrokenMeshAfterDitherDelay.Emit();
	}

	void ShowDitherMeshes()
	{
		ScriptAssert( IsClient(), "CLIENT_ONLY" );

		ditherBrokenMesh.SetHiddenInGame( false );
		ditherStartMesh.SetHiddenInGame( false );
		GetMeshComponent().SetHiddenInGame( true );

		// put at the end
		endSignal_HideBrokenMeshAfterDitherDelay.Emit();
	}

	void InitDitherMeshes()
	{
		ScriptAssert( IsClient(), "CLIENT_ONLY" );

		int entryID			   = EditorAssetEntryToInt( assetEntry );
		FVector scale3d		   = GetScale3DFromEnum( entryID );
		UStaticMesh brokenMesh = GetBrokenMeshFromEnum( entryID );
		UStaticMesh startMesh  = GetMeshFromEnum( entryID );

		ditherBrokenMesh.SetStaticMesh( brokenMesh );
		ditherStartMesh.SetStaticMesh( startMesh );
		ditherBrokenMesh.SetRelativeScale3D( scale3d );
		ditherStartMesh.SetRelativeScale3D( scale3d );

		ditherBrokenMesh.SetHiddenInGame( true );
		ditherStartMesh.SetHiddenInGame( true );
		ditherBrokenMesh.SetCollisionEnabled( ECollisionEnabled::NoCollision );
		ditherStartMesh.SetCollisionEnabled( ECollisionEnabled::NoCollision );

		TMap<FName, UMaterialInterface> brokenMats = GetBrokenMeshMaterialOverridesFromEnum( entryID );
		__SetAllMaterialData( ditherBrokenMesh, brokenMesh, brokenMats );
		TMap<FName, UMaterialInterface> startMats = GetMeshMaterialOverridesFromEnum( entryID );
		__SetAllMaterialData( ditherStartMesh, startMesh, startMats );
	}

	// don't want these fx playing on beginPlay
	bool onLoad = true;
	void cl_RegrowFx()
	{
		ScriptAssert( IsClient(), "CLIENT ONLY" );
		if ( onLoad )
		{
			onLoad = false;
			return;
		}

		int entryID					   = EditorAssetEntryToInt( assetEntry );
		FRME_CategoryRegrow regrowData = GetRegrowDataFromEnum( entryID );

		if ( IsValid( regrowData.regrowFX ) )
			Client_SpawnEffectAtLocation_OneShot( regrowData.regrowFX, GetActorLocation(), GetActorRotation(), regrowData.regrowFxScale );

		if ( regrowData.enableDitherShader )
		{
			TArray<UMaterialInstanceDynamic>& mats = GetDynamicMats();

			float32 now		  = float32( Gameplay::GetTimeSeconds() );
			float32 startTime = now + regrowData.ditherShaderDelay;
			float32 endTime	  = startTime + regrowData.ditherShaderDuration;
			for ( UMaterialInstanceDynamic mat : mats )
			{
				if ( IsValid( mat ) )
				{
					mat.SetScalarParameterValue( n"RegrowStartTime", startTime );
					mat.SetScalarParameterValue( n"RegrowEndTime", endTime );
				}
			}
		}
	}

	private TArray<UMaterialInstanceDynamic> __dynamicMats;
	TArray<UMaterialInstanceDynamic>& GetDynamicMats()
	{
		ScriptAssert( IsClient(), "GetDynamicMat can only be changed on client" );

		if ( __dynamicMats.Num() > 0 )
			return __dynamicMats;

		int entryID				= EditorAssetEntryToInt( assetEntry );
		TArray<int> matIndecies = GetRegrowDataFromEnum( entryID ).regrowMaterialOverrides;

		if ( matIndecies.Num() == 0 )
		{
			UStaticMesh mesh			 = GetMeshFromEnum( entryID );
			TArray<FStaticMaterial> mats = mesh.GetStaticMaterials();
			for ( FStaticMaterial mat : mats )
				matIndecies.Add( mesh.GetMaterialIndex( mat.MaterialSlotName ) );
		}

		for ( int i = 0; i < matIndecies.Num(); i++ )
		{
			int matIndex = matIndecies[i];
			__dynamicMats.Add( ditherStartMesh.CreateDynamicMaterialInstance( matIndex ) );
		}

		return __dynamicMats;
	}

	/****************************************************************\

	██    ██ ████████ ██ ██      ██ ████████ ██    ██
	██    ██    ██    ██ ██      ██    ██     ██  ██
	██    ██    ██    ██ ██      ██    ██      ████
	██    ██    ██    ██ ██      ██    ██       ██
	 ██████     ██    ██ ███████ ██    ██       ██

	\****************************************************************/
	UStaticMesh GetMeshFromEnum( int entryID )
	{
		return meshEntries[GetSafeIndex( entryID )].MeshAsset;
	}

	UStaticMesh GetDestructAnimMeshFromEnum( int entryID )
	{
		return meshEntries[GetSafeIndex( entryID )].DestructibleMeshAsset;
	}

	UStaticMesh GetShadowProxyForEnum( int entryID )
	{
		return meshEntries[GetSafeIndex( entryID )].ShadowProxy;
	}

	FVector GetScale3DFromEnum( int entryID )
	{
		return meshEntries[GetSafeIndex( entryID )].StartScale3D;
	}

	int GetHealthFromEnum( int entryID )
	{
		int health = meshEntries[GetSafeIndex( entryID )].HealthAmount;
		if ( health != -1 )
			return health;
		else
			return GetResourceAmountFromEnum( entryID );
	}

	FRME_CategoryRegrow GetRegrowDataFromEnum( int entryID )
	{
		return meshEntries[GetSafeIndex( entryID )].RegrowData;
	}

	int GetResourceAmountFromEnum( int entryID ) const
	{
		return meshEntries[GetSafeIndex( entryID )].ResourceAmount;
	}

	FNCMeleeResponseBehavior GetMeleeResponseBehaviorFromEnum( int entryID )
	{
		return meshEntries[GetSafeIndex( entryID )].MeleeResponseData;
	}

	FResourceAimAssistData GetAimAssistDataFromEnum( int entryID )
	{
		return meshEntries[GetSafeIndex( entryID )].aimAssistData;
	}

	UStaticMeshComponent GetMeshComponent()
	{
		return staticMesh;
	}

	UStaticMesh GetBrokenMeshFromEnum( int entryID )
	{
		switch ( GetBreakMethodFromEnum( entryID ) )
		{
			case EResourceBreakMethod::PROTOTYPE:
				return meshEntries[GetSafeIndex( entryID )].breakableData.BrokenMeshAsset;

			case EResourceBreakMethod::MODELSWAP:
			case EResourceBreakMethod::TREE:
			{
				TArray<FResourceModelSwapData> modelSwapData = GetModelSwapData( entryID );
				int index									 = modelSwapData.Num() - 1;
				return modelSwapData[index].meshAsset;
			}

			case EResourceBreakMethod::CHUNKABLE:
				return GetChunkableModelDataFromEnum( entryID ).BrokenBaseAsset;
		}
	}

	FVector GetBrokenScale3DFromEnum( int entryID )
	{
		switch ( GetBreakMethodFromEnum( entryID ) )
		{
			case EResourceBreakMethod::PROTOTYPE:
				return meshEntries[GetSafeIndex( entryID )].breakableData.BrokenScale3D;

			default:
				return GetScale3DFromEnum( entryID );
		}
	}

	float GetFxScaleMultiplierFromEnum( int entryID )
	{
		return meshEntries[GetSafeIndex( entryID )].breakableData.FxScaleMultiplier;
	}

	FVector GetFxOffsetFromEnum( int entryID )
	{
		return meshEntries[GetSafeIndex( entryID )].breakableData.FxOffset;
	}

	UNiagaraSystem GetFxFromEnum( int entryID )
	{
		return meshEntries[GetSafeIndex( entryID )].breakableData.breakFX;
	}

	UNCAudioAsset GetSFXFromEnum( int entryID )
	{
		return meshEntries[GetSafeIndex( entryID )].breakableData.breakSFX;
	}

	int GetSafeIndex( int entryID ) const
	{
		int Index = Math::Min( entryID, meshEntries.Num() - 1 );
		return Math::Max( Index, 0 );
	}

	bool HasChunkableModelData( int entryID ) const
	{
		return GetChunkableModelClassFromEnum( GetSafeIndex( entryID ) ) != nullptr;
	}

	TSubclassOf<AAS_ChunkableModelData> GetChunkableModelClassFromEnum( int entryID ) const
	{
		return meshEntries[GetSafeIndex( entryID )].breakableData.ChunkableModelData;
	}

	AAS_ChunkableModelData GetChunkableModelDataFromEnum( int entryID )
	{
		return Cast<AAS_ChunkableModelData>( GetChunkableModelClassFromEnum( entryID ).Get().GetDefaultObject() );
	}

	void ValidateChunkModelData( int entryID )
	{
		GetChunkableModelDataFromEnum( entryID ).ValidateChunkModelData();
	}

	bool HasModelSwapData( int entryID )
	{
		return GetModelSwapData( entryID ).Num() > 0;
	}

	TArray<FResourceModelSwapData> GetModelSwapData( int entryID )
	{
		return meshEntries[GetSafeIndex( entryID )].breakableData.ModelSwapData;
	}

	bool HasCanopyData( int entryID )
	{
		return IsValid( GetCanopyMeshFromEnum( entryID ) );
	}

	UStaticMesh GetCanopyMeshFromEnum( int entryID )
	{
		return meshEntries[GetSafeIndex( entryID )].breakableData.canopyAsset;
	}

	bool HasTrunkData( int entryID )
	{
		return IsValid( GetTrunkMeshFromEnum( entryID ) );
	}

	AAS_PhysicsTree_v2 GetTrunkPhysicsBP( int entryID )
	{
		TSubclassOf<AAS_PhysicsTree_v2> trunkPhysicsBP = GetTrunkPhysicsSubclass( entryID );
		if ( trunkPhysicsBP.IsValid() )
		{
			UObject physicsBPObject			 = trunkPhysicsBP.Get().GetDefaultObject();
			AAS_PhysicsTree_v2 physicsBPTree = Cast<AAS_PhysicsTree_v2>( physicsBPObject );
			return physicsBPTree;
		}
		return nullptr;
	}

	TSubclassOf<AAS_PhysicsTree_v2> GetTrunkPhysicsSubclass( int entryID )
	{
		TSubclassOf<AAS_PhysicsTree_v2> trunkPhysicsBP = meshEntries[GetSafeIndex( entryID )].breakableData.trunkPhysicsBP;
		return trunkPhysicsBP;
	}

	UStaticMesh GetTrunkMeshFromEnum( int entryID )
	{
		UStaticMesh EmptyMesh;
		AAS_PhysicsTree_v2 classBP = GetTrunkPhysicsBP( entryID );
		if ( IsValid( classBP ) )
			EmptyMesh = classBP.GetStaticMesh();

		return EmptyMesh;
	}

	FRME_CategoryTree GetTreeFallDataFromEnum( int entryID )
	{
		EResourceBreakMethod brkMethod = GetBreakMethodFromEnum( entryID );
		ScriptAssert( brkMethod == EResourceBreakMethod::CHUNKABLE || brkMethod == EResourceBreakMethod::TREE, "Trying to call GetTrunkRelativePivotFromEnum on none tree" );

		return meshEntries[GetSafeIndex( entryID )].breakableData.treeFallData;
	}

	EResourceBreakMethod GetBreakMethodFromEnum( int entryID ) const
	{
		return meshEntries[GetSafeIndex( entryID )].breakableData.breakMethod;
	}

	FTransform GetTrunkRelativePivotFromEnum( int entryID )
	{
		EResourceBreakMethod brkMethod = GetBreakMethodFromEnum( entryID );
		ScriptAssert( brkMethod == EResourceBreakMethod::CHUNKABLE || brkMethod == EResourceBreakMethod::TREE, "Trying to call GetTrunkRelativePivotFromEnum on none tree" );

		FVector pivotOffset;

		// DEPRICATE THIS LATER
		if ( brkMethod == EResourceBreakMethod::CHUNKABLE )
		{
			TSubclassOf<AAS_ChunkableModelData> classOf = GetChunkableModelClassFromEnum( entryID );
			if ( IsValid( classOf ) )
			{
				AAS_PhysicsTreeFall classBP = Cast<AAS_PhysicsTreeFall>( classOf.Get().GetDefaultObject() );
				if ( IsValid( classBP ) )
					pivotOffset = classBP.physicsPivotPoint.GetRelativeLocation();
			}
		}
		else if ( brkMethod == EResourceBreakMethod::TREE )
		{
			AAS_PhysicsTree_v2 classBP = GetTrunkPhysicsBP( entryID );
			if ( IsValid( classBP ) )
				pivotOffset = classBP.physicsPivotPoint.GetRelativeLocation();
		}

		FTransform pivotRelTrans = FTransform( pivotOffset );

		return pivotRelTrans;
	}

	FTransform GetTrunkWorldPivotFromEnum( int entryID )
	{
		FTransform actorWorldTrans = GetActorTransform();

		FVector scale3D = GetScale3DFromEnum( entryID );
		actorWorldTrans.SetScale3D( scale3D );

		FTransform pivotRelTrans   = GetTrunkRelativePivotFromEnum( entryID );
		FTransform pivotWorldTrans = RelativeTransformToWorldTransform( pivotRelTrans, actorWorldTrans );

		return pivotWorldTrans;
	}

	/****************************************************************\

	███    ███  █████  ████████      ██████  ██    ██ ███████ ██████  ██████  ██ ██████  ███████
	████  ████ ██   ██    ██        ██    ██ ██    ██ ██      ██   ██ ██   ██ ██ ██   ██ ██
	██ ████ ██ ███████    ██        ██    ██ ██    ██ █████   ██████  ██████  ██ ██   ██ █████
	██  ██  ██ ██   ██    ██        ██    ██  ██  ██  ██      ██   ██ ██   ██ ██ ██   ██ ██
	██      ██ ██   ██    ██         ██████    ████   ███████ ██   ██ ██   ██ ██ ██████  ███████

	\****************************************************************/
	TMap<FName, UMaterialInterface> GetMeshMaterialOverridesFromEnum( int entryID )
	{
		return meshEntries[GetSafeIndex( entryID )].meshMaterialOverride;
	}

	TMap<FName, UMaterialInterface> GetCanopyMaterialOverridesFromEnum( int entryID )
	{
		return meshEntries[GetSafeIndex( entryID )].breakableData.canopyMaterialOverride;
	}

	TMap<FName, UMaterialInterface> GetSwapMaterialOverridesFromEnum( int entryID, int swapIndex )
	{
		return meshEntries[GetSafeIndex( entryID )].breakableData.ModelSwapData[swapIndex].swapMaterialOverride;
	}

	TMap<FName, UMaterialInterface> GetPhysicsTrunkMaterialOverridesFromEnum( int entryID )
	{
		return meshEntries[GetSafeIndex( entryID )].breakableData.trunkMaterialOverride;
	}

	TMap<FName, UMaterialInterface> GetBrokenMeshMaterialOverridesFromEnum( int entryID )
	{
		switch ( GetBreakMethodFromEnum( entryID ) )
		{
			case EResourceBreakMethod::MODELSWAP:
			case EResourceBreakMethod::TREE:
			{
				TArray<FResourceModelSwapData> modelSwapData = GetModelSwapData( entryID );
				int swapIndex								 = modelSwapData.Num() - 1;
				return GetSwapMaterialOverridesFromEnum( entryID, swapIndex );
			}

			case EResourceBreakMethod::PROTOTYPE:
			case EResourceBreakMethod::CHUNKABLE:
			{
				TMap<FName, UMaterialInterface> empty;
				return empty;
			}
		}
	}

/****************************************************************\

██████  ███████ ██████  ██    ██  ██████
██   ██ ██      ██   ██ ██    ██ ██
██   ██ █████   ██████  ██    ██ ██   ███
██   ██ ██      ██   ██ ██    ██ ██    ██
██████  ███████ ██████   ██████   ██████

\****************************************************************/
#if EDITOR
	void Constructor_DebugModelSwap()
	{
		TArray<FResourceModelSwapData> modelSwapData;
		int entryID	  = EditorAssetEntryToInt( assetEntry );
		modelSwapData = GetModelSwapData( entryID );
		int swapIndex = Debug_SwapEntryToInt( swapEntry );

		FResourceModelSwapData data = modelSwapData[swapIndex];
		GetMeshComponent().SetStaticMesh( data.meshAsset );

		TMap<FName, UMaterialInterface> mats = GetSwapMaterialOverridesFromEnum( entryID, swapIndex );
		__SetAllMaterialData( GetMeshComponent(), data.meshAsset, mats );
	}

	bool Debug_CanModelSwap()
	{
		int entryID				 = EditorAssetEntryToInt( assetEntry );
		EResourceBreakMethod brk = GetBreakMethodFromEnum( entryID );
		return brk == EResourceBreakMethod::MODELSWAP || brk == EResourceBreakMethod::TREE;
	}

	const FString DEBUG_SWAPENTRY_PREFIX = "Model ";
	UFUNCTION()
	TArray<FString> Debug_GetSwapEntries()
	{
		TArray<FString> strings;
		int entryID = EditorAssetEntryToInt( assetEntry );

		TArray<FResourceModelSwapData> swapData = GetModelSwapData( entryID );

		for ( int i = 0; i < swapData.Num(); i++ )
		{
			FString newString = f"{DEBUG_SWAPENTRY_PREFIX}{i}";
			strings.Add( newString );
		}

		return strings;
	}

	int Debug_SwapEntryToInt( FString swapEntryName ) const
	{
		FString numString = swapEntryName.RightChop( DEBUG_SWAPENTRY_PREFIX.Len() );
		return numString.ToInt();
	}

	void Debug_CleanMesh()
	{
		if ( !InEditor() )
			return;

		int entryID = EditorAssetEntryToInt( assetEntry );
		ResetModel( entryID );
	}

	void Constructor_DebugPlayBreakFX()
	{
		int entryID				  = EditorAssetEntryToInt( assetEntry );
		EResourceBreakMethod mthd = GetBreakMethodFromEnum( entryID );

		switch ( mthd )
		{
			case EResourceBreakMethod::PROTOTYPE:
			case EResourceBreakMethod::CHUNKABLE:
				PlayDefaultFXPackage();
				break;

			case EResourceBreakMethod::MODELSWAP:
			case EResourceBreakMethod::TREE:
			{
				TArray<FResourceModelSwapData> modelSwapData = GetModelSwapData( entryID );
				int swapIndex;
				if ( !DebugModelSwap )
					swapIndex = modelSwapData.Num() - 1;
				else
					swapIndex = Debug_SwapEntryToInt( swapEntry );

				PlaySwapFXPackage( entryID, swapIndex );
			}
			break;
		}
	}
#endif
}