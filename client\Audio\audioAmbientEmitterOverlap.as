enum ENCAudioOverlapTriggerMode
{
	Sphere,
	Box
};

class AAS_NCAudioAmbientEmitterOverlap : AAS_ClientSideActor
{
	UPROPERTY( DefaultComponent, RootComponent )
	USceneComponent root;

	// defaults
	default ActorHiddenInGame = true;
	default bNetLoadOnClient  = true;
	default bCanBeDamaged	  = false;

	UPROPERTY( EditAnywhere )
	ENCAudioOverlapTriggerMode triggerShape = ENCAudioOverlapTriggerMode::Sphere;

	UAS_SphereTrigger sphereTrigger;

	UAS_BoxTrigger boxTrigger;

	UPROPERTY( EditAnywhere )
	UNCAudioAsset audioEvent;

	UPROPERTY( EditAnywhere )
	float sphereRadius = 1500.0f;

	UPROPERTY( EditAnywhere )
	FVector boxScale = FVector( 10, 10, 2 );

	bool isPlaying = false;
	int eventID	   = 0;

	UPROPERTY( EditAnywhere )
	bool debugDraw = false;

	private ANCPlayerCharacter localPlayer;

	UFUNCTION( BlueprintOverride )
	void ConstructionScript()
	{
		MakeTriggers();
	}

	void MakeTriggers()
	{
		if ( triggerShape == ENCAudioOverlapTriggerMode::Sphere )
		{
			if ( IsValid( boxTrigger ) )
			{
				boxTrigger.DestroyComponent( this );
			}
			sphereTrigger = Cast<UAS_SphereTrigger>( CreateComponent( UAS_SphereTrigger::StaticClass() ) );
			sphereTrigger.SetRadius( sphereRadius );
			sphereTrigger.SetCollisionProfileName( n"Trigger" );
		}
		else
		{
			if ( IsValid( sphereTrigger ) )
			{
				sphereTrigger.DestroyComponent( this );
			}
			boxTrigger = Cast<UAS_BoxTrigger>( CreateComponent( UAS_BoxTrigger::StaticClass() ) );
			boxTrigger.SetWorldScale3D( boxScale );
			boxTrigger.SetCollisionProfileName( n"Trigger" );
		}
	}

	UFUNCTION( BlueprintOverride )
	void ClientBeginPlay()
	{
		// Binds
		if ( triggerShape == ENCAudioOverlapTriggerMode::Box )
		{
			if ( !IsValid( boxTrigger ) )
				MakeTriggers();
			boxTrigger.onPlayerEntered.AddUFunction( this, n"OnPlayerEnteredBox" );
			boxTrigger.onPlayerExited.AddUFunction( this, n"OnPlayerExitedBox" );
		}
		else
		{
			if ( !IsValid( sphereTrigger ) )
				MakeTriggers();
			sphereTrigger.onPlayerEntered.AddUFunction( this, n"OnPlayerEnteredSphere" );
			sphereTrigger.onPlayerExited.AddUFunction( this, n"OnPlayerExitedSphere" );
		}
	}

	UFUNCTION( BlueprintOverride )
	void OnHUDInit( ANC_HUD hud )
	{
		Super::OnHUDInit( hud );
		localPlayer = Client_GetLocalPawn();
	}

	UFUNCTION()
	void OnPlayerEnteredSphere( AAS_PlayerEntity Player, UAS_SphereTrigger Trigger )
	{
		if ( !IsValid( localPlayer ) )
		{
			return;
		}

		if ( Player == localPlayer )
		{
			StartPlayingSound();
		}
	}

	UFUNCTION()
	void OnPlayerExitedSphere( AAS_PlayerEntity Player, UAS_SphereTrigger Trigger )
	{
		if ( !IsValid( localPlayer ) )
		{
			return;
		}

		if ( Player == localPlayer )
		{
			StopPlayingSound();
		}
	}

	UFUNCTION()
	void OnPlayerEnteredBox( AAS_PlayerEntity Player, UAS_BoxTrigger Trigger )
	{
		if ( !IsValid( localPlayer ) )
		{
			return;
		}

		if ( Player == localPlayer )
		{
			StartPlayingSound();
		}
	}

	UFUNCTION()
	void OnPlayerExitedBox( AAS_PlayerEntity Player, UAS_BoxTrigger Trigger )
	{
		if ( !IsValid( localPlayer ) )
		{
			return;
		}

		if ( Player == localPlayer )
		{
			StopPlayingSound();
		}
	}

	UFUNCTION()
	void StartPlayingSound()
	{

		if ( !isPlaying )
		{
			FAudioResultData resultData = Client_EmitSoundOnEntity( audioEvent, this );
			eventID						= resultData.EventID;
			isPlaying					= true;
		}
#if EDITOR
		if ( debugDraw )
		{
			if ( triggerShape == ENCAudioOverlapTriggerMode::Sphere )
			{
				DrawDebugSphere( GetActorLocation(), sphereRadius, 2, FLinearColor::Yellow, 6.0 );
			}
			else
			{
				DrawDebugBox( this.GetActorLocation(), this.GetActorRotation(), boxScale * 100, 3.0, FLinearColor::Yellow, 4.0 );
			}
			// NC Audio Asset
			DrawDebugSphere( GetActorLocation(), 75, 2, FLinearColor::Blue, 2.0 );
			DrawDebugString( GetActorLocation() + FVector( 0.0, 0.0, 20.0 ), audioEvent.ToString(), 2, FLinearColor::Yellow );
		}
#endif
	}

	UFUNCTION()
	void StopPlayingSound()
	{

		if ( isPlaying )
		{
			Client_StopSound( eventID );
			isPlaying = false;
		}
#if EDITOR
		if ( debugDraw )
		{
			if ( triggerShape == ENCAudioOverlapTriggerMode::Sphere )
			{
				DrawDebugSphere( GetActorLocation(), sphereRadius, 2, FLinearColor::Red, 6.0 );
			}
			else
			{
				DrawDebugBox( this.GetActorLocation(), this.GetActorRotation(), boxScale * 100, 3.0, FLinearColor::Red, 4.0 );
			}
			// NC Audio Asset
			DrawDebugSphere( GetActorLocation(), 75, 2, FLinearColor::Red, 2.0 );
		}
#endif
	}

	UFUNCTION( BlueprintOverride )
	void EndPlay( EEndPlayReason EndPlayReason )
	{
		Super::EndPlay( EndPlayReason );
		if ( IsClient() )
		{
			Client_StopSound( eventID );
			isPlaying = false;

			if ( triggerShape == ENCAudioOverlapTriggerMode::Box )
			{
				if ( IsValid( boxTrigger ) )
				{
					boxTrigger.onPlayerEntered.UnbindObject( this );
					boxTrigger.onPlayerExited.UnbindObject( this );
				}
			}
			else
			{
				if ( IsValid( sphereTrigger ) )
				{
					sphereTrigger.onPlayerEntered.UnbindObject( this );
					sphereTrigger.onPlayerExited.UnbindObject( this );
				}
			}
		}
	}
}
