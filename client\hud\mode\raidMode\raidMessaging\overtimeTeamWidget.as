UCLASS( Abstract )
class UAS_OvertimeTeamWidget : UNC_DisplayWidget
{
	default StateOnConstruction = EDisplayConstructVisibility::Hidden;
	default HideVisibility		= ESlateVisibility::Collapsed;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UWrapBox teamContainer;

	private int trackedTeamId = GameConst::INDEX_NONE;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		UCL_ScriptCallbacks clientCallbacks = ClientCallbacks();
		if ( IsValid( clientCallbacks ) )
		{
			clientCallbacks.OnPlayerCreated.AddUFunction( this, n"OnPlayerCreated" );
			clientCallbacks.OnPlayerDestroyed.AddUFunction( this, n"OnPlayerDestroyed" );
		}
	}

	void SetTeam( int teamId )
	{
		if ( teamId != trackedTeamId )
		{
			// Save the team id we were given
			trackedTeamId = teamId;
			UpdateTeam();
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnPlayerCreated( const ANCPlayerCharacter player )
	{
		UpdateTeam();
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnPlayerDestroyed( const ANCPlayerCharacter player, EEndPlayReason endPlayReason )
	{
		UpdateTeam();
	}

	private void UpdateTeam()
	{
		TArray<ANCPlayerCharacter> players = GetPlayersOfTeam( trackedTeamId );
		for ( int i = 0; i < teamContainer.GetChildrenCount(); i++ )
		{
			UAS_PlayerQuickStatusWidget portrait = Cast<UAS_PlayerQuickStatusWidget>( teamContainer.GetChildAt( i ) );
			if ( IsValid( portrait ) )
			{
				bool validPlayer = players.IsValidIndex( i );
				portrait.SetPlayer( validPlayer ? Cast<AAS_PlayerEntity>( players[i] ) : nullptr );
			}
		}
	}
}