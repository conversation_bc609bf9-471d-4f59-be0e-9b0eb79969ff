UCLASS( Abstract )
class UAS_UsePrompt_AddonWidget_DepositBox : UAS_UsePrompt_AddonWidget
{
	UPROPERTY( NotEditable, BindWidget )
	UAS_CoinDepositWidget coinDepositWidget;

	UPROPERTY( NotEditable, BindWidget )
	UTextBlock coinAmountText;

	UPROPERTY( NotEditable, Meta = ( BindWidgetAnim ), Transient )
	UWidgetAnimation showDepositAmount;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		UAS_ClientScript_GoldRush goldRushScript = Cast<UAS_ClientScript_GoldRush>( GetModeClientScript() );
		if ( IsValid( goldRushScript ) )
		{
			goldRushScript.onLocalPlayerDepositedGold.AddUFunction( this, n"OnLocalPlayerDepositedGold" );
		}
	}

	UFUNCTION()
	private void OnLocalPlayerDepositedGold( AAS_GoldRushDepositBox box, int amount )
	{
		ShowAddedCoins( amount );
	}

	FAddonWidgetDisplayOptions Update( UUsableItemComponent useComponent, AActor ownerActor,
									   ANCPlayerCharacter localPlayer ) override
	{
		coinDepositWidget.OnOwnerCourierUpdated( Cast<AAS_GoldRushDepositBox>( ownerActor ) );
		return FAddonWidgetDisplayOptions();
	}

	void ShowAddedCoins( int amount )
	{
		coinAmountText.SetText( GetLocalizedText( Localization::Core, "plus_count", FFormatArgumentValue( amount ) ) );
		PlayAnimation( showDepositAmount );
	}
}