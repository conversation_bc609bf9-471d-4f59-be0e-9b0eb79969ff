

UAS_PriorityMessageData GetTemplateMessageData( EPriorityMessageTemplate template )
{
	UAS_PriorityMessageData data = NewPriorityMessageData();
	if ( IsValid( data ) )
	{
		UCL_PriorityMessageManager_v2 manager = Cast<UCL_PriorityMessageManager_v2>( PriorityMessage() );
		if ( IsVali<PERSON>( manager ) )
		{
			data.messageTemplate = template;

			// Quick way to step through all of the different templates trying to find a match
			if ( !PriorityMessageRaidTemplates::GetTemplateMessageData( template, manager, data ) )
			{
				if ( !PriorityMessageRaidReportTemplates::GetTemplateMessageData( template, manager, data ) )
				{
					if ( !PriorityMessageGameStateTemplates::GetTemplateMessageData( template, manager, data ) )
					{
						if ( !PriorityMessageCarePackageTemplates::GetTemplateMessageData( template, manager, data ) )
						{
							if ( !PriorityMessageLootTemplates::GetTemplateMessageData( template, manager, data ) )
							{
								if ( !PriorityMessageMiscRaidTemplates::GetTemplateMessageData( template, manager, data ) )
								{
									if ( !PriorityMessageCombatTemplates::GetTemplateMessageData( template, manager, data ) )
									{
										// We failed to find a template, it is probably ok to just not show anything
										data = nullptr;
									}
								}
							}
						}
					}
				}
			}
		}
	}

	return data;
}