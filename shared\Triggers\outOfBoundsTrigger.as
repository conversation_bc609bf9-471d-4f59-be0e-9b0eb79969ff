const float32 OUT_OF_BOUNDS_DURATION = 10.0;

UCLASS()
class AAS_Sv_OutOfBoundsTrigger_Sphere : AActor
{
	UPROPERTY( DefaultComponent, RootComponent )
	UAS_SphereTrigger trigger;

	UPROPERTY( DefaultComponent )
	UBillboardComponent billboard;
	default billboard.bIsEditorOnly = true;
	default billboard.bHiddenInGame = true;

	int teamFilter = -1;

	UFUNCTION( BlueprintOverride )
	void BeginPlay()
	{
		trigger.onPlayerEntered.AddUFunction( this, n"OnPlayerEntered" );
		trigger.onPlayerExited.AddUFunction( this, n"OnPlayerExited" );
	}

	TArray<AAS_PlayerEntity> playersRegistered;

	UFUNCTION()
	private void OnPlayerExited( AAS_PlayerEntity player, UAS_SphereTrigger sphereTrigger )
	{
		if ( playersRegistered.Contains( player ) )
		{
			player.outOfBoundsManager.UnregisterTrigger( this );
			playersRegistered.Remove( player );
		}
	}

	UFUNCTION()
	private void OnPlayerEntered( AAS_PlayerEntity player, UAS_SphereTrigger sphereTrigger )
	{
		if ( !GetCvarBool("ScriptDebug.EnableOutOfBounds") )
		{
			return;
		}

		if ( player.GetTeam() == teamFilter || teamFilter == -1 )
		{
			playersRegistered.AddUnique( player );
			player.outOfBoundsManager.RegisterTrigger( this );
		}
	}
}

UCLASS()
class AAS_OutOfBoundsTrigger_Brush : AAS_BrushTrigger
{
#if EDITOR
	default NCOverrideMaterial = Cast<UMaterialInterface>( LoadObject( this, f"/Game/Environment/Materials/Dev/M_OutOfBounds_Zone.M_OutOfBounds_Zone" ) );
#endif

	UPROPERTY( DefaultComponent )
	UBillboardComponent billboard;
	default billboard.bIsEditorOnly = true;
	default billboard.bHiddenInGame = true;

	UFUNCTION( BlueprintOverride )
	void ServerBeginPlay()
	{
		Super::ServerBeginPlay();
		onPlayerEntered.AddUFunction( this, n"OnPlayerEntered" );
		onPlayerExited.AddUFunction( this, n"OnPlayerExited" );
	}

	UFUNCTION()
	private void OnPlayerExited( AAS_PlayerEntity player, AAS_BrushTrigger trigger )
	{
		if (player.outOfBoundsManager.outOfBoundsTriggers.Contains(trigger))
			player.outOfBoundsManager.UnregisterTrigger( this );
	}

	UFUNCTION()
	private void OnPlayerEntered( AAS_PlayerEntity player, AAS_BrushTrigger trigger )
	{
		if ( !GetCvarBool("ScriptDebug.EnableOutOfBounds") )
		{
			return;
		}

		player.outOfBoundsManager.RegisterTrigger( this );
	}
}

UCLASS( Abstract )
class AAS_OutOfBoundsDataActor : ANCDefaultActor
{
	UPROPERTY( NotEditable, Transient )
	FNCNetInt net_outOfBoundsEndTime;

	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<UAS_OutOfBoundsWidget> outOfBoundsWidgetClass;
	UAS_OutOfBoundsWidget outOfBoundsWidget;

	UFUNCTION( BlueprintOverride )
	void BeginPlay()
	{
		AAS_PlayerEntity ownerPlayer = GetOwnerPlayer();

		Print( "AAS_OutOfBoundsDataActor BeginPlay -- RegisterDataActor" );
		ownerPlayer.outOfBoundsManager.RegisterDataActor( this );
	}

	UFUNCTION( BlueprintOverride )
	void ServerBeginPlay()
	{
		AAS_PlayerEntity ownerPlayer = GetOwnerPlayer();
		ownerPlayer.OnEndPlay.AddUFunction( this, n"OnOwnerEndPlay" );
	}

	UFUNCTION( BlueprintOverride )
	void ClientBeginPlay()
	{
		outOfBoundsWidget = Cast<UAS_OutOfBoundsWidget>( WidgetBlueprint::CreateWidget( outOfBoundsWidgetClass, Client_GetLocalPlayerController() ) );
		outOfBoundsWidget.AddToViewport();
		outOfBoundsWidget.SetAlignmentInViewport( FVector2D( 0.5, 0.5 ) );
		outOfBoundsWidget.SetAnchorsInViewport( FAnchors( 0, 0, 1, 1 ) );
		outOfBoundsWidget.PlayAnimation( outOfBoundsWidget.showAnim );

		net_outOfBoundsEndTime.OnReplicated().AddUFunction( this, n"OnEndTimeChanged" );
	}

	UFUNCTION()
	private void OnEndTimeChanged( int oldValue, int newValue )
	{
		if ( IsValid( outOfBoundsWidget ) )
			outOfBoundsWidget.SetEndTime( newValue );
	}

	UFUNCTION()
	private void OnOwnerEndPlay( AActor Actor, EEndPlayReason EndPlayReason )
	{
		Destroy();
	}

	UFUNCTION( BlueprintOverride )
	void EndPlay( EEndPlayReason EndPlayReason )
	{
		Print( "AAS_OutOfBoundsDataActor EndPlay" );

		if ( IsValid( outOfBoundsWidget ) )
		{
			outOfBoundsWidget.RemoveFromParent();
		}

		AAS_PlayerEntity ownerPlayer = GetOwnerPlayer();
		if ( IsValid( ownerPlayer ) )
		{
			Print( "AAS_OutOfBoundsDataActor EndPlay -- UnregisterDataActor" );
			ownerPlayer.outOfBoundsManager.UnregisterDataActor( this );
		}
	}
}

UCLASS()
class UAS_OutOfBoundsManager : UAS_PlayerActorComponent
{
	access Internal = private, AAS_OutOfBoundsDataActor;

	TArray<AActor> outOfBoundsTriggers;

	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<AAS_OutOfBoundsDataActor> dataActorClass;
	AAS_OutOfBoundsDataActor dataActor;

	FTimerHandle outOfBoundsTimerHandle;

	int outOfBoundsStartTimeMS;
	float32 outOfBoundsStartTimeDuration;
	int lastExitOutOfBoundsTimeMS;
	float32 lastOutOfBoundsTimeLeft = OUT_OF_BOUNDS_DURATION;

	void RegisterTrigger( AActor trigger )
	{
		if ( outOfBoundsTriggers.Num() == 0 )
		{
			AAS_OutOfBoundsDataActor d = Cast<AAS_OutOfBoundsDataActor>( Server_SpawnEntity( dataActorClass, ownerPlayer ) );

			d.SetActorSendFlags( ESendEventFlags::SEND_TO_OWNER, ownerPlayer.GetEntityId() );
			ScriptAssert( IsValid( dataActor ), "data actor should be valid by now" );

			float32 recoverDuration		 = lastOutOfBoundsTimeLeft + TO_SECONDS( GetTimeMilliseconds() - lastExitOutOfBoundsTimeMS );
			float32 duration			 = Math::Min( OUT_OF_BOUNDS_DURATION, recoverDuration );
			outOfBoundsStartTimeDuration = duration;

			outOfBoundsStartTimeMS = GetTimeMilliseconds();
			d.net_outOfBoundsEndTime.SetNetValue( GetTimeMilliseconds() + TO_MILLISECONDS( duration ) );
			outOfBoundsTimerHandle = System::SetTimer( this, n"OnOutOfBoundsTimeEnd", duration, false );
		}
		outOfBoundsTriggers.AddUnique( trigger );
	}

	UFUNCTION()
	private void OnOutOfBoundsTimeEnd()
	{
		FDamageInfo info;
		info.attacker			  = nullptr;
		info.damage				  = 9999999;
		info.traceHitResult		  = FHitResult();
		info.damageFlags		  = EDamageFlags::DF_NONE;
		info.damageSourceLocation = ownerPlayer.GetActorLocation();
		ownerPlayer.Die( info );
	}

	void UnregisterTrigger( AActor trigger )
	{
		outOfBoundsTriggers.Remove( trigger );
		if ( outOfBoundsTriggers.Num() == 0 )
		{
			lastExitOutOfBoundsTimeMS = GetTimeMilliseconds();
			lastOutOfBoundsTimeLeft	  = outOfBoundsStartTimeDuration - TO_SECONDS( GetTimeMilliseconds() - outOfBoundsStartTimeMS );

			System::ClearAndInvalidateTimerHandle( outOfBoundsTimerHandle );
			if (IsValid(dataActor))
			{
				dataActor.Destroy();
			}
		}
	}

	access:Internal void RegisterDataActor( AAS_OutOfBoundsDataActor _dataActor )
	{
		Print( f"UAS_OutOfBoundsManager RegisterDataActor {_dataActor} --> {dataActor}" );

		ScriptAssert( !IsValid( dataActor ), "data actor already registered" );
		dataActor = _dataActor;

		OnDataActorRegistered();
	}

	private void OnDataActorUnregistered()
	{
	}

	private void OnDataActorRegistered()
	{
	}

	access:Internal void UnregisterDataActor( AAS_OutOfBoundsDataActor _dataActor )
	{
		Print( f"UAS_OutOfBoundsManager UnregisterDataActor {_dataActor} --> {dataActor}" );

		ScriptAssert( dataActor == _dataActor, "tried to unregister different data actor" );
		dataActor = nullptr;
		OnDataActorUnregistered();
	}

	UFUNCTION()
	bool IsOutOfBounds()
	{
		if ( !IsValid( dataActor ) )
			return false;
		else
			return true;
	}
}

UCLASS( Abstract )
class UAS_OutOfBoundsWidget : UUserWidgetDefault
{
	UPROPERTY( NotVisible, BlueprintReadOnly, Meta = ( BindWidget ) )
	UAS_CommonTextBlock timerText;

	UPROPERTY( NotVisible, BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	UWidgetAnimation showAnim;

	private int endTime;

	void SetEndTime( int _endTime )
	{
		endTime = _endTime;
	}

	UFUNCTION( BlueprintOverride )
	void Tick( FGeometry MyGeometry, float InDeltaTime )
	{
		int timeLeftMS = Math::Max( 0, endTime - GetGameTimeMS() );
		timerText.SetText( GetFormattedCountdownTime( timeLeftMS, OUT_OF_BOUNDS_DURATION ) );
	}
}