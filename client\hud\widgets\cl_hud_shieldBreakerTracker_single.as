UCLASS()
class UAS_ShieldBreakerTrackerWidget_Single : UUserWidgetDefault
{
	bool isShowing = false;
	AAS_RaidDomeShield localDome;
	int localTeam;

	UPROPERTY( NotVisible, BindWidget )
	UPanelWidget sbIconPanel;

	UWidgetAnimation currentPlayingAnim;

	UPROPERTY( NotVisible, Meta = ( BindWidgetAnim ), Transient )
	UWidgetAnimation appearAnim;

	UPROPERTY( NotVisible, Meta = ( BindWidgetAnim ), Transient )
	UWidgetAnimation pulseAnim;

	UPROPERTY( NotVisible, Meta = ( BindWidgetAnim ), Transient )
	UWidgetAnimation pulseFastAnim;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		localTeam = Widget_GetPawnOwner( this ).GetTeam();
		localDome = GetRaidDomeShieldForTeam( localTeam );
		FindDomes();
	}

	UFUNCTION()
	private void FindDomes()
	{
		localDome = GetRaidDomeShieldForTeam( localTeam );

		if ( !IsValid( localDome ) )
		{
			System::SetTimer( this, n"FindDomes", 0.1, false );
			return;
		}
	}

	void PlayPulseAnim( UWidgetAnimation anim )
	{
		if ( anim == currentPlayingAnim )
			return;

		if ( currentPlayingAnim != nullptr )
			StopAnimation( currentPlayingAnim );

		currentPlayingAnim = anim;
		PlayAnimation( anim, 0, 0 );
	}

	UFUNCTION( BlueprintOverride )
	void Tick( FGeometry MyGeometry, float InDeltaTime )
	{
		if ( !IsValid( localDome ) )
			return;

		TArray<AAS_PlayerEntity> validPlayers;
		TArray<AAS_PlayerEntity> players = GetAllPlayers();
		for ( AAS_PlayerEntity player : players )
		{
			if ( IsFriendly( player.GetTeam(), localTeam ) )
				continue;

			if ( PlayerHasShieldBreaker( player ) )
				validPlayers.Add( player );
		}

		AAS_PlayerEntity closestPlayer;
		float32 maxDist		= localDome.GetLargePlayerDetectionRadius( localDome.GetRadius() );
		float32 closestDist = maxDist;

		for ( AAS_PlayerEntity player : validPlayers )
		{
			float32 dist = Distance2D( player.GetActorLocation(), localDome.GetActorLocation() );
			if ( dist < closestDist )
			{
				closestPlayer = player;
				closestDist	  = dist;
			}
		}

		if ( IsValid( closestPlayer ) )
		{
			if ( !isShowing )
				PlayAnimation( appearAnim );
			isShowing = true;
			PlayPulseAnim( pulseFastAnim );
			SetWidgetVisibilitySafe( sbIconPanel, ESlateVisibility::HitTestInvisible );
		}
		else
		{
			isShowing = false;
			SetWidgetVisibilitySafe( sbIconPanel, ESlateVisibility::Hidden );
		}
	}
}