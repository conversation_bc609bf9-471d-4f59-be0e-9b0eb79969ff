event void FOnWaitForShieldbreakerCarrierThreadEnd( AAS_PlayerEntity player );

class UAS_WaitForShieldbreakerCarrierThread : UAS_Thread
{
	FOnWaitForShieldbreakerCarrierThreadEnd OnWaitForShieldbreakerCarrierThreadEnd;
	private AAS_PlayerEntity player = nullptr;

	void Init()
	{
		Start();
	}

	void OnThreadStart( UNCCoroutine co ) override
	{
		Super::OnThreadStart( co );

		while ( !IsValid( player ) )
		{
			player = GetPlayerWithShieldBreaker();
			co.Wait( 0.01f );
		}
	}

	void OnThreadEnd( FNCCoroutineEndParams params ) override
	{
		Super::OnThreadEnd( params );

		if ( <PERSON><PERSON><PERSON><PERSON>( player ) )
		{
			OnWaitForShieldbreakerCarrierThreadEnd.Broadcast( player );
		}
	}
}