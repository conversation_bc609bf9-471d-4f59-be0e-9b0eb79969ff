UCLASS( Abstract )
class UAS_MaximapLegendWidget : UNC_DisplayWidget
{
	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UListView priorityList;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UListView secondaryList;

	UPROPERTY( NotVisible, EditDefaultsOnly )
	private UDataTable mapElementData;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		UAS_MinimapMarkerHudComponent mapMarkerComponent = MapMarker();
		if ( IsValid( mapMarkerComponent ) )
		{
			TArray<FMapElementWidgetData> mapMarkerData;
			mapElementData.GetAllRows( mapMarkerData );

			TArray<UAS_MaximapLegendObject> priorityLegendItems;
			TArray<UAS_MaximapLegendObject> secondaryLegendItems;

			for ( FMapElementWidgetData markerData : mapMarkerData )
			{
				if ( markerData.showInLegend )
				{
					UAS_MaximapLegendObject legendObject = Cast<UAS_MaximapLegendObject>( NewObject( this, UAS_MaximapLegendObject::StaticClass() ) );
					if ( IsValid( legendObject ) )
					{
						legendObject.widgetData = markerData;

						if ( markerData.legendPriority < 0 )
						{
							// Anything with a -1 priority should be in the first list
							priorityLegendItems.Add( legendObject );
						}
						else
						{
							// Everything else gets sorted in the second list
							secondaryLegendItems.Add( legendObject );
						}
					}
				}
			}

			// Sort the secondary legend items based on the priority
			int n = secondaryLegendItems.Num();
			for ( int i = 0; i < n - 1; ++i )
			{
				for ( int j = 0; j < n - i - 1; ++j )
				{
					if ( secondaryLegendItems[j].widgetData.legendPriority > secondaryLegendItems[j + 1].widgetData.legendPriority )
						secondaryLegendItems.Swap( j, j + 1 );
				}
			}

			// Set each list view with the items
			priorityList.SetListItems( priorityLegendItems );
			secondaryList.SetListItems( secondaryLegendItems );
		}
	}
}