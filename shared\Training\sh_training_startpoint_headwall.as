UCLASS()
class UNCTrainingDialogueEndedEvent : UNCNetClientToServerEvent
{
	UPROPERTY()
	FNCNetGameplayTag net_dialogueTag;

	UFUNCTION( BlueprintOverride )
	void OnEventReceived( ANCNetPlayerController receiver )
	{
		if ( !IsTrainingMode() )
		{
			return;
		}

		if ( !IsValid( ScriptCallbacks() ) )
		{
			return;
		}

		if ( !FGameplayTag( net_dialogueTag ).IsValid() )
		{
			return;
		}

		FName VO_FLAG = GetVOEndedFlag( net_dialogueTag );

		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		scriptCallbacks.RegisterFlagSafe( VO_FLAG );
		scriptCallbacks.FlagSet( VO_FLAG );
	}
}

UCLASS()
class UNCTrainingOpenInventoryEvent : UNCNetClientToServerEvent
{
	UPROPERTY()
	FNCNetBool net_inventoryOpen;

	UFUNCTION( BlueprintOverride )
	void OnEventReceived( ANCNetPlayerController receiver )
	{
		if ( !IsTrainingMode() )
		{
			return;
		}

		if ( !IsValid( ScriptCallbacks() ) )
		{
			return;
		}

		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		scriptCallbacks.RegisterFlagSafe( Training::Flags::FLAG_INVENTORY_OPEN );
		if ( net_inventoryOpen )
		{
			scriptCallbacks.FlagSet( Training::Flags::FLAG_INVENTORY_OPEN );
		}
		else
		{
			scriptCallbacks.FlagClear( Training::Flags::FLAG_INVENTORY_OPEN );
		}
	}
}

UCLASS()
class AAS_TrainingSession_StartPoint_Headwall : AAS_TrainingSession_StartPoint
{
	bool hasTakenRaidWeapon = false;
	AAS_PlayerEntity player;
	
	UAS_ScriptCallbacks scriptCallbacks;

	UPROPERTY()
	FNCNetInt net_trainingDisabledWeaponSlots;
	
	UFUNCTION(BlueprintOverride)
	void ServerBeginPlay()
	{
		scriptCallbacks = ScriptCallbacks();
	}

	void StartRunningFromHere_Server( AAS_PlayerEntity asPlayer ) override
	{
		Super::StartRunningFromHere_Server(asPlayer);
		player = asPlayer;
		
		asPlayer.ClassManager().SetClass( GameplayTags::Classes_Class_Redmane );
	}

	void Run_Server( AAS_PlayerEntity asPlayer ) override
	{
		Super::Run_Server(asPlayer);
		player = asPlayer;

		Thread( this, n"Run_Server_Thread", asPlayer );
	}

	UFUNCTION()
	void OnDataLayerChanged( UDataLayerInstance layer, EDataLayerRuntimeState state )
	{
		Print(f"A data layer has changed! Layer: {layer.Name}, state: {state}");
	}

	UAS_ObjectiveSystem objectiveSys;
	UFUNCTION()
	private void Run_Server_Thread( UNCCoroutine co, AAS_PlayerEntity asPlayer )
	{
		scriptCallbacks.RegisterFlag( Training::Flags::FLAG_TELEPORT_BLIMP_TRIGGER);
		scriptCallbacks.RegisterFlag( Training::Flags::FLAG_SKIP_FIRST_OBJECTIVE);

		co.OnCoroutineEnd.AddUFunction( this, n"OnRunServerThreadEnd" );

		objectiveSys = Objectives();

		sharedData.trigger_intro_basicControls.onPlayerEntered.AddUFunction( this, n"Trigger_Intro_BasicControls" );
		sharedData.trigger_combat_enteredRoom.onPlayerEntered.AddUFunction( this, n"Trigger_Combat_EnteredRoom" );
		sharedData.trigger_combat_onRarityPlatform.onPlayerEntered.AddUFunction( this, n"Trigger_Combat_OnEnterRarityPlatform" );
		sharedData.trigger_combat_onArmorPlatform.onPlayerEntered.AddUFunction( this, n"Trigger_Combat_OnEnterArmorPlatform" );
		
		sharedData.trigger_destruction_enteredRoom.onPlayerEntered.AddUFunction( this, n"Trigger_Destruction_OnEnteredRoom" );

		sharedData.trigger_abilities_enteredRoom.onPlayerEntered.AddUFunction( this, n"Trigger_Abilities_OnEnteredRoom" );
	
		sharedData.respawnInBaseTrigger.onPlayerEntered.AddUFunction( this, n"Trigger_OpenWorld_TeleportToBase" );
		sharedData.trigger_openworld_teleportedToBase.onPlayerEntered.AddUFunction( this, n"Trigger_OpenWorld_OnTeleportedToBase" );

		sharedData.trigger_spawnShieldBreaker.onPlayerEntered.AddUFunction( this, n"OnEnteredCraftShieldBreakerTrigger" );
		
		scriptCallbacks.RegisterSignalCallback( n"Trigger_ShowBreakHint", this, n"OnTriggerShowBreakHint" );
		
		scriptCallbacks.RegisterSignalCallback( n"Trigger_ShowHarvestHint", this, n"OnTriggerShowHarvestHint" );
		scriptCallbacks.RegisterSignalCallback( n"Trigger_HideHarvestHint", this, n"OnTriggerHideHarvestHint" );
		
		scriptCallbacks.RegisterSignalCallback( n"Trigger_ShowMantleHint", this, n"OnTriggerShowMantleHint" );
		scriptCallbacks.RegisterSignalCallback( n"Trigger_OutdoorMantleHint", this, n"OnTriggerShowOutdoorMantleHint" );

		scriptCallbacks.RegisterSignalCallback( n"Trigger_DebugRegenerate", this, n"OnTriggerDebugRegenerate" );

		scriptCallbacks.RegisterSignalCallback( scriptCallbacks.GetFlagSignal( Training::Flags::FLAG_COMBAT_ONFIRSTPLATFORM ), this, n"OnPlayerEnteredShootAndGrenadesPlatform" );
		scriptCallbacks.RegisterSignalCallback( scriptCallbacks.GetFlagClearSignal( Training::Flags::FLAG_COMBAT_ONFIRSTPLATFORM ), this, n"OnPlayerExitedShootAndGrenadesPlatform" );

		scriptCallbacks.FlagSet( Training::Flags::FLAG_DOOR_INTRO );

		// TODO: Commenting out just to save on chugging when playing in PIE

		#if EDITOR
			FGameplayTag enemyBaseIndex = GameplayTags::BaseSelection_Estate;
			if ( GetCvarBool( f"ScriptDebug.OnlyEnemyBaseIsDevBase" ) )
			{
				enemyBaseIndex = GameplayTags::BaseSelection_Dev_TestBase;

			}
		#else
			const FGameplayTag enemyBaseIndex = GameplayTags::BaseSelection_Estate;
		#endif
		
		#if EDITOR
			FGameplayTag friendlyBaseIndex = GameplayTags::BaseSelection_TrainingBase;
			if ( GetCvarBool( f"ScriptDebug.OnlyFriendlyBaseIsDevBase" ) )
			{
				friendlyBaseIndex = GameplayTags::BaseSelection_Dev_TestBase;
			}
		#else
			const FGameplayTag friendlyBaseIndex = GameplayTags::BaseSelection_TrainingBase;
		#endif
		UAS_BaseSelectSystem bases	  = Bases();
		
		for( AAS_BaseSelectMarker marker : sharedData.baseMarkers )
		{
			// To save time, dev version
			#if EDITOR
			if ( GetCvarBool( f"ScriptDebug.ShowDevBasesInTraining" ) )
			{
				bases.SpawnBaseForTeam( marker.GetTeam(), GameplayTags::BaseSelection_Dev_TestBase );
			}
			else
			{
				bases.SpawnBaseForTeam( marker.GetTeam(), marker.GetTeam() == asPlayer.GetTeam() ? friendlyBaseIndex : enemyBaseIndex );
			}
			#else
				bases.SpawnBaseForTeam( marker.GetTeam(), marker.GetTeam() == asPlayer.GetTeam() ? friendlyBaseIndex : enemyBaseIndex );
			#endif
		}

		for( AAS_Vendor vendor : sharedData.disableUsableVendors )
		{
			vendor.useComponent.DisableUsable();
		}
		
		asPlayer.sv_DisableVendorBattleChatter();

		int trainingWeaponSlots = asPlayer.WeaponSlotsDisabled;
		if ( !GetCvarBool( f"ScriptDebug.DebugTraining" ) )
		{
			co.Wait( 1 );
			
			asPlayer.ServerDisableMount( Training::MOUNT_DISABLE_CONTEXT );

			asPlayer.ServerDisableWeaponSlot( WeaponSlot::GrenadeSlot, Training::GRENADE_DISABLE_CONTEXT );
			trainingWeaponSlots = Bitflags::ClearBit( trainingWeaponSlots, WeaponSlot::GrenadeSlot );

			asPlayer.ServerDisableWeaponSlot( WeaponSlot::RaidUltSlot, Training::ULTIMATE_DISABLE_CONTEXT );
			trainingWeaponSlots = Bitflags::ClearBit( trainingWeaponSlots, WeaponSlot::RaidUltSlot );

			asPlayer.ServerDisableWeaponSlot( WeaponSlot::TacticalSlot, Training::TACTICAL_DISABLE_CONTEXT );
			trainingWeaponSlots = Bitflags::ClearBit( trainingWeaponSlots, WeaponSlot::TacticalSlot );
		}

		net_trainingDisabledWeaponSlots.SetNetValue( trainingWeaponSlots );

		co.WaitForever();
	}

	UFUNCTION()
	private void OnRunServerThreadEnd( FNCCoroutineEndParams endParams )
	{
		sharedData.trigger_intro_basicControls.onPlayerEntered.Unbind( this, n"Trigger_Intro_BasicControls" );
		sharedData.trigger_combat_enteredRoom.onPlayerEntered.Unbind( this, n"Trigger_Combat_EnteredRoom" );
		sharedData.trigger_combat_onRarityPlatform.onPlayerEntered.Unbind( this, n"Trigger_Combat_OnEnterRarityPlatform" );
		sharedData.trigger_combat_onArmorPlatform.onPlayerEntered.Unbind( this, n"Trigger_Combat_OnEnterArmorPlatform" );
		
		sharedData.trigger_destruction_enteredRoom.onPlayerEntered.Unbind( this, n"Trigger_Destruction_OnEnteredRoom" );

		sharedData.trigger_abilities_enteredRoom.onPlayerEntered.Unbind( this, n"Trigger_Abilities_OnEnteredRoom" );

		sharedData.trigger_spawnShieldBreaker.onPlayerEntered.Unbind( this, n"OnEnteredCraftShieldBreakerTrigger" );


		sharedData.respawnInBaseTrigger.onPlayerEntered.Unbind( this, n"Trigger_OpenWorld_TeleportToBase" );
		sharedData.trigger_openworld_teleportedToBase.onPlayerEntered.Unbind( this, n"Trigger_OpenWorld_OnTeleportedToBase" );

		scriptCallbacks.UnRegisterSignalCallback( n"Trigger_ShowBreakHint", this, n"OnTriggerShowBreakHint" );
		
		scriptCallbacks.UnRegisterSignalCallback( n"Trigger_ShowHarvestHint", this, n"OnTriggerShowHarvestHint" );
		scriptCallbacks.UnRegisterSignalCallback( n"Trigger_HideHarvestHint", this, n"OnTriggerHideHarvestHint" );
		
		scriptCallbacks.UnRegisterSignalCallback( n"Trigger_ShowMantleHint", this, n"OnTriggerShowMantleHint" );
		scriptCallbacks.UnRegisterSignalCallback( n"Trigger_OutdoorMantleHint", this, n"OnTriggerShowOutdoorMantleHint" );

		scriptCallbacks.UnRegisterSignalCallback( n"Trigger_DebugRegenerate", this, n"OnTriggerDebugRegenerate" );

		scriptCallbacks.UnRegisterSignalCallback( scriptCallbacks.GetFlagSignal( Training::Flags::FLAG_COMBAT_ONFIRSTPLATFORM ), this, n"OnPlayerEnteredShootAndGrenadesPlatform" );
		scriptCallbacks.UnRegisterSignalCallback( scriptCallbacks.GetFlagClearSignal( Training::Flags::FLAG_COMBAT_ONFIRSTPLATFORM ), this, n"OnPlayerExitedShootAndGrenadesPlatform" );
	}

	UFUNCTION()
	private void OnEnteredCraftShieldBreakerTrigger( AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger )
	{
		// TODO: Run when player's ready to raid the base
		sharedData.sbCrafter.CraftShieldBreaker();
	}

	UFUNCTION()
	private void Trigger_Intro_BasicControls( AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger )
	{
		Thread( this, n"Intro_Thread", asPlayer );
	}

	UFUNCTION()
	private void OnTriggerShowBreakHint( FName signalName, UObject sender )
	{
		FName goToDestructionCompleteFlag = objectiveSys.GetObjectiveCompleteFlag( Training::Objectives::COMBAT_08_GOTODESTRUCTION );
		FName firstDestructionObjective = objectiveSys.GetObjectiveCompleteFlag( Training::Objectives::DESTRUCTION_01_TALKTOVENDOR );
		FName breakWallObjectiveFlag = objectiveSys.GetObjectiveCompleteFlag( Training::Objectives::DESTRUCTION_HINT_BREAKWALLS );
		scriptCallbacks.RegisterFlagSafe( goToDestructionCompleteFlag );
		scriptCallbacks.RegisterFlagSafe( firstDestructionObjective );
		scriptCallbacks.RegisterFlagSafe( breakWallObjectiveFlag );
		// Checking first destruction one for PIE QoL
		if ( scriptCallbacks.Flag( goToDestructionCompleteFlag ) || scriptCallbacks.Flag( firstDestructionObjective ) || scriptCallbacks.Flag( breakWallObjectiveFlag ) )
		{
			return;
		}

		if ( objectiveSys.ServerIsObjectiveActive( Training::Objectives::DESTRUCTION_HINT_BREAKWALLS, ETrainingObjectiveCategory::DEFAULT ) )
		{
			return;
		}

		// If sent by a linked trigger that's linked to a destroyed destructible, don't show it.
		AAS_BrushTrigger triggerOwner = Cast<AAS_BrushTrigger>( sender );
		if ( IsValid( triggerOwner ) && !triggerOwner.linkedActors.IsEmpty() )
		{
			ANCDestructible destructible = Cast<ANCDestructible>( triggerOwner.linkedActors[ 0 ] );
			if ( IsValid( destructible ) )
			{
				if ( destructible.GetCurrentHealth() <= 0 )
				{
					return;
				}
			}
		}

		objectiveSys.ServerAddObjectiveGroup( Training::Objectives::DESTRUCTION_HINT_BREAKWALLS, player, ETrainingObjectiveCategory::DEFAULT );
	}

	UFUNCTION()
	private void OnTriggerShowHarvestHint( FName signalName, UObject sender )
	{
		objectiveSys.ServerAddObjectiveGroup( n"HarvestHint", player, ETrainingObjectiveCategory::TEMPORARY_HINT );
	}

	UFUNCTION()
	private void OnTriggerHideHarvestHint( FName signalName, UObject sender )
	{
		objectiveSys.ClearObjectiveGroupByName( n"HarvestHint", ETrainingObjectiveCategory::TEMPORARY_HINT );
	}

/****************************************************************\

███████ ██    ██     ██ ███    ██ ████████ ██████   ██████  
██      ██    ██     ██ ████   ██    ██    ██   ██ ██    ██ 
███████ ██    ██     ██ ██ ██  ██    ██    ██████  ██    ██ 
     ██  ██  ██      ██ ██  ██ ██    ██    ██   ██ ██    ██ 
███████   ████       ██ ██   ████    ██    ██   ██  ██████ 

\****************************************************************/	
	UFUNCTION()
	void SetFlagOnPlayerMove( UNCCoroutine co, AAS_PlayerEntity asPlayer )
	{
		co.Wait( n"Trigger_HasPlayerMoved" );
		scriptCallbacks.FlagSet( Training::Flags::FLAG_SKIP_FIRST_OBJECTIVE );
	}

	UFUNCTION()
	void Intro_Thread( UNCCoroutine co, AAS_PlayerEntity asPlayer )
	{
		// TODO: How to know how long to wait for client to be loaded and all gucci gang? Want to disable input until timing is right.
		// TODO: Server command to tell client to fade in
		TrainingServerDisablePlayerInput( asPlayer );
		Thread( this, n"SetFlagOnPlayerMove", asPlayer );

		co.Wait( 6.0 );
		TrainingServerEnablePlayerInput( asPlayer );
		
		co.Wait( 6.0 );

		if ( !scriptCallbacks.Flag( Training::Flags::FLAG_SKIP_FIRST_OBJECTIVE ) )
		{
			objectiveSys.ServerAddObjectiveGroup( Training::Objectives::INTRO_01_BASICCONTROLS, asPlayer, ETrainingObjectiveCategory::DEFAULT );
			co.Wait( Training::Objectives::INTRO_01_BASICCONTROLS );

		}
		
		objectiveSys.ServerAddObjectiveGroup( Training::Objectives::INTRO_02_EXITCAVE, asPlayer, ETrainingObjectiveCategory::DEFAULT );
		co.Wait( Training::Objectives::INTRO_02_EXITCAVE );

		objectiveSys.ServerAddObjectiveGroup( Training::Objectives::INTRO_03_MEETFLYNN, asPlayer, ETrainingObjectiveCategory::DEFAULT );

		FName VO_FLAG = GetVOEndedFlag( GameplayTags::Audio_VO_Training_IntroRoom_Welcome_Intro );
		scriptCallbacks.RegisterFlagSafe( VO_FLAG );
		co.FlagWaitTimeout( VO_FLAG, 15 );
		
		// Completes objective 3, timing it so it's after dialogue finishes
		scriptCallbacks.FlagSet( n"Intro_TalkedToFlynn" );

		FName objectiveCompleteFlag = objectiveSys.GetObjectiveCompleteFlag( Training::Objectives::INTRO_03_MEETFLYNN );
		scriptCallbacks.RegisterFlagSafe( objectiveCompleteFlag );
		co.FlagWait( objectiveCompleteFlag );
		//co.Wait( Training::Objectives::INTRO_03_MEETFLYNN );

		// From trigger
		AAS_TrainingObjectiveGroup newGroup = objectiveSys.ServerAddObjectiveGroup( Training::Objectives::INTRO_04_GOTOCOMBAT, player, ETrainingObjectiveCategory::DEFAULT );
		TArray<FName> markerNames;
		markerNames.Add( n"Combat_Gate" );

		UAS_TrainingObjective_Location locationObj = Cast<UAS_TrainingObjective_Location>( newGroup.objectives [ 0 ] );
		locationObj.objectiveDelay = 15;
		
		SetUpLocationObjectiveChain( newGroup, markerNames, 512, true, 15 );
	}

	UFUNCTION()
	private void OnTriggerShowMantleHint( FName signalName, UObject sender )
	{
		objectiveSys.ServerAddObjectiveGroup( Training::Objectives::INTRO_HINT_CAVEMANTLE, player, ETrainingObjectiveCategory::TEMPORARY_HINT );
	}

	UFUNCTION()
	private void OnTriggerShowOutdoorMantleHint( FName signalName, UObject sender )
	{
		objectiveSys.ServerAddObjectiveGroup( Training::Objectives::INTRO_HINT_OUTDOORMANTLE, player, ETrainingObjectiveCategory::DEFAULT );
	}

	UFUNCTION()
	private void OnTriggerHideMantleHint( FName signalName, UObject sender )
	{
		objectiveSys.ClearObjectiveGroupByName( Training::Objectives::INTRO_HINT_CAVEMANTLE, ETrainingObjectiveCategory::TEMPORARY_HINT );
	}

	UFUNCTION()
	private void OnTriggerDebugRegenerate( FName signalName, UObject sender )
	{
		ownerSession.RegenerateTaggedDestructiblesAndResources_SetDestructiblesToIron();
	}

/****************************************************************\

███████ ██    ██      ██████  ██████  ███    ███ ██████   █████  ████████ 
██      ██    ██     ██      ██    ██ ████  ████ ██   ██ ██   ██    ██    
███████ ██    ██     ██      ██    ██ ██ ████ ██ ██████  ███████    ██    
     ██  ██  ██      ██      ██    ██ ██  ██  ██ ██   ██ ██   ██    ██    
███████   ████        ██████  ██████  ██      ██ ██████  ██   ██    ██   

\****************************************************************/

	UFUNCTION()
	private void Trigger_Combat_EnteredRoom( AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger )
	{
		Thread( this, n"Combat_ShootingPlatform_Thread" );
	}

	UFUNCTION()
	private void Trigger_Combat_OnEnterRarityPlatform( AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger )
	{
		Thread( this, n"Combat_RarityPlatform_Thread", asPlayer );
	}

	UFUNCTION()
	void OnObjectiveActiveChanged_UseRarityWeapons( AAS_TrainingObjectiveGroup group, bool isActive )
	{
		UpdateShownDummies( sharedData.targetDummies_weaponRarity, isActive );
	}

	FNCCoroutineSignal endOpenRarityChestsThread;
	UFUNCTION()
	private void OpenRarityChestsThread( UNCCoroutine co, float32 delay )
	{
		endOpenRarityChestsThread.Emit();
		co.EndOn( this, endOpenRarityChestsThread );

		co.Wait( delay );

		// Open chests
		for ( AAS_DisplayedLootChest chest : sharedData.combat_weaponRarity_demoChests )
		{
			if ( !chest.isOpen )
			{
				chest.RegenerateLoot();
				chest.OpenFull();
				co.Wait( 0.1 );
			}
		}
	}

	UFUNCTION()
	void OnDummyDamagedIncorrectly_WithWrongRarity( AAS_TargetDummy dummy )
	{
		if ( dummy.healthComponent.GetHealth() <= 0 )
		{
			Thread( this, n"DelayedRespawnDummyThread", dummy, 1.5, Training::Objectives::COMBAT_04_WEAPONRARITY, ETrainingObjectiveCategory::DEFAULT );
		}
	}

	UFUNCTION()
	private void Trigger_Combat_OnEnterArmorPlatform( AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger )
	{
		Thread( this, n"Combat_ArmorPlatform_Thread");
		// TODO: Return to platform hint objective
	}

	bool playerStartedShootObjective = false;
	UFUNCTION()
	private void OnPlayerEnteredShootAndGrenadesPlatform( FName signalName, UObject sender )
	{
		scriptCallbacks.FlagClear( Training::Flags::FLAG_DOOR_INTRO );
	}

	UFUNCTION()
	void Combat_ShootingPlatform_Thread( UNCCoroutine co )
	{

		{
			UpdateEnabledBullseyeDummies( sharedData.targetDummies_group1, false );

			// Comes from animation
			FName VO_FLAG = GetVOEndedFlag( GameplayTags::Audio_VO_Training_CombatRoom_Intro );
			scriptCallbacks.RegisterFlagSafe( VO_FLAG );
			co.FlagWaitTimeout( VO_FLAG, 15.0 );

			// Ends go to combat objective
			scriptCallbacks.RegisterFlagSafe( n"Combat_TalkedToFlynn" );
			scriptCallbacks.FlagSet( n"Combat_TalkedToFlynn" );

			AAS_TrainingObjectiveGroup newGroup = objectiveSys.ServerAddObjectiveGroup( Training::Objectives::COMBAT_01_DAMAGEDUMMIES, player, ETrainingObjectiveCategory::DEFAULT );

			RespawnTargetDummies( sharedData.targetDummies_group1 );
			newGroup.onObjectiveActiveChanged.AddUFunction( this, n"OnObjectiveActiveChanged_DamageDummies" );
			UpdateEnabledBullseyeDummies( sharedData.targetDummies_group1, newGroup.net_isActiveUIObjective );

			UAS_TrainingObjective_DamageTargetDummies damageDummiedObjective = Cast<UAS_TrainingObjective_DamageTargetDummies>( newGroup.objectives[ 0 ] );
			damageDummiedObjective.SetTargetDummiesToTrack( sharedData.targetDummies_group1 );
		}

		co.Wait( Training::Objectives::COMBAT_01_DAMAGEDUMMIES );
		co.Wait( 1 );
		
		{
			UpdateEnabledBullseyeDummies( sharedData.targetDummies_group1, false );
			AAS_TrainingObjectiveGroup newGroup = objectiveSys.ServerAddObjectiveGroup( Training::Objectives::COMBAT_02_DAMAGEDUMMIES_WITHSCOPE, player, ETrainingObjectiveCategory::DEFAULT );
			newGroup.onObjectiveActiveChanged.AddUFunction( this, n"OnObjectiveActiveChanged_DamageDummies_WithScope" );

			UAS_TrainingObjective_DamageTargetDummies damageDummiesObjective = Cast<UAS_TrainingObjective_DamageTargetDummies>( newGroup.objectives[ 1 ] );
			damageDummiesObjective.SetTargetDummiesToTrack( sharedData.targetDummies_group2 );
			damageDummiesObjective.onDummyDamagedIncorrectly.AddUFunction( this, n"OnDummyDamagedIncorrectly_WithoutScope" );
			UpdateEnabledBullseyeDummies( sharedData.targetDummies_group2, false );

			// Waiting for dialogue
			// Start dummies a little before line is over. Yes this'll change with loc, but it should be approx correct
			co.Wait( 2 );

			RespawnTargetDummies( sharedData.targetDummies_group2 );
			UpdateEnabledBullseyeDummies( sharedData.targetDummies_group2, true );
		}
		
		co.Wait( Training::Objectives::COMBAT_02_DAMAGEDUMMIES_WITHSCOPE );
		co.Wait( 1 );
		
		{
			TrackAndRefreshCooldown( player, player.fragCooldownComponent );
			AAS_TrainingObjectiveGroup newGroup = objectiveSys.ServerAddObjectiveGroup( Training::Objectives::COMBAT_03_DAMAGEDUMMIES_WITHGRENADE, player, ETrainingObjectiveCategory::DEFAULT );
			newGroup.onObjectiveActiveChanged.AddUFunction( this, n"OnObjectiveActiveChanged_DamageDummies_WithGrenade" );

			UAS_TrainingObjective_DamageTargetDummies damageDummiesObjective = Cast<UAS_TrainingObjective_DamageTargetDummies>( newGroup.objectives[ 0 ] );
			damageDummiesObjective.SetTargetDummiesToTrack( sharedData.targetDummiesToDamageWithGrenade );
			damageDummiesObjective.onDummyDamagedIncorrectly.AddUFunction( this, n"OnDummyDamagedIncorrectly_WithoutGrenade" );
			
			UpdateEnabledBullseyeDummies( sharedData.targetDummies_group1, false );
			UpdateEnabledBullseyeDummies( sharedData.targetDummies_group2, false );

			// Start dummies a little before line is over. Yes this'll change with loc, but it should be approx correct
			co.Wait( 2 );

			RespawnTargetDummies( sharedData.targetDummiesToDamageWithGrenade );
			UpdateShownDummies( sharedData.targetDummiesToDamageWithGrenade, true );
			if ( !player.WeaponSlotIsEnabled( WeaponSlot::GrenadeSlot ) )
			{
				player.ServerEnableWeaponSlot( WeaponSlot::GrenadeSlot, Training::GRENADE_DISABLE_CONTEXT );
				net_trainingDisabledWeaponSlots.SetNetValue( Bitflags::SetBit( net_trainingDisabledWeaponSlots, WeaponSlot::GrenadeSlot ) );
			}
		}
		
		co.Wait( Training::Objectives::COMBAT_03_DAMAGEDUMMIES_WITHGRENADE );
		
		StopTrackingAndRefreshingCooldown();
		co.Wait( 1 );
		
		objectiveSys.ServerAddObjectiveGroup( Training::Objectives::COMBAT_HINT_GOTORARITYPLATFORM, player, ETrainingObjectiveCategory::DEFAULT );
		sharedData.trigger_combat_onRarityPlatform.Enable();
	}

	UFUNCTION()
	private void Combat_RarityPlatform_Thread( UNCCoroutine co, AAS_PlayerEntity asPlayer )
	{
		objectiveSys.ClearObjectiveGroupByName( Training::Objectives::COMBAT_HINT_GOTORARITYPLATFORM, ETrainingObjectiveCategory::DEFAULT );

		// TODO SILVER: Hint text blurb about what each rarity does when you hold it
		AAS_TrainingObjectiveGroup newGroup = objectiveSys.ServerAddObjectiveGroup( Training::Objectives::COMBAT_04_WEAPONRARITY, asPlayer, ETrainingObjectiveCategory::DEFAULT );
		newGroup.onObjectiveActiveChanged.AddUFunction( this, n"OnObjectiveActiveChanged_UseRarityWeapons" );
		UAS_TrainingObjective_DamageTargetDummies damageDummiesObjective = Cast<UAS_TrainingObjective_DamageTargetDummies>( newGroup.objectives[ 0 ] );
		damageDummiesObjective.SetTargetDummiesToTrack( sharedData.targetDummies_weaponRarity );
		damageDummiesObjective.onDummyDamagedIncorrectly.AddUFunction( this, n"OnDummyDamagedIncorrectly_WithWrongRarity" );
		
		// TODO: DIALOGUE WAIT
		// Wait for first line to be done
		FName VO_FLAG = GetVOEndedFlag( GameplayTags::Audio_VO_Training_CombatRoom_UpgradeTraining_A );
		scriptCallbacks.RegisterFlagSafe( VO_FLAG );
		co.FlagWaitTimeout( VO_FLAG, 15.0 );
		
		Thread( this, n"OpenRarityChestsThread", 0 );

		// Wait for second line to be done
		VO_FLAG = GetVOEndedFlag( GameplayTags::Audio_VO_Training_CombatRoom_UpgradeTraining_B );
		scriptCallbacks.RegisterFlagSafe( VO_FLAG );
		co.FlagWaitTimeout( VO_FLAG, 15.0 );
		Thread( this, n"DelayedRespawnDummiesThread", sharedData.targetDummies_weaponRarity, 0, Training::Objectives::COMBAT_04_WEAPONRARITY, ETrainingObjectiveCategory::DEFAULT );

		co.Wait( Training::Objectives::COMBAT_04_WEAPONRARITY );
		co.Wait( 1 );

		objectiveSys.ServerAddObjectiveGroup( Training::Objectives::Combat_05_GOTOARMORPLATFORM, asPlayer, ETrainingObjectiveCategory::DEFAULT );

		// Activates zipliine
		scriptCallbacks.FlagSet( Training::Flags::FLAG_ZIPLINE_COMBAT_ARMOR );
	}

	UFUNCTION()
	void OnObjectiveActiveChanged_DamageDummies( AAS_TrainingObjectiveGroup group, bool isActive )
	{
		UpdateEnabledBullseyeDummies( sharedData.targetDummies_group1, isActive );
	}

/****************************************************************\

███████ ██    ██      █████  ██████  ███    ███  ██████  ██████  
██      ██    ██     ██   ██ ██   ██ ████  ████ ██    ██ ██   ██ 
███████ ██    ██     ███████ ██████  ██ ████ ██ ██    ██ ██████  
     ██  ██  ██      ██   ██ ██   ██ ██  ██  ██ ██    ██ ██   ██ 
███████   ████       ██   ██ ██   ██ ██      ██  ██████  ██   ██  

\****************************************************************/
	UFUNCTION()
	void Combat_ArmorPlatform_Thread( UNCCoroutine co )
	{
		AAS_TargetDummy_Human reviveDummy = Cast<AAS_TargetDummy_Human>( sharedData.targetDummies_revive[ 0 ] );
		reviveDummy.SetTeam( player.GetTeam() );
		RespawnTargetDummies( sharedData.targetDummies_revive );

		{
			AAS_TrainingObjectiveGroup newGroup = objectiveSys.ServerAddObjectiveGroup( Training::Objectives::COMBAT_06_REVIVEFRIENDLY, player, ETrainingObjectiveCategory::DEFAULT );
			UAS_TrainingObjective_ReviveDummy reviveObjective = Cast<UAS_TrainingObjective_ReviveDummy>( newGroup.objectives[ 0 ] );
			reviveObjective.SetTrackedTargetDummy( reviveDummy );
		}
		
		co.Wait( 1 );
		// There should only be one, lazily leaving as array
		UpdateShownDummies( sharedData.targetDummies_revive, true );

		FName VO_FLAG = GetVOEndedFlag( GameplayTags::Audio_VO_Training_CombatRoom_ReviveTraining_A );
		scriptCallbacks.RegisterFlagSafe( VO_FLAG );
		co.FlagWaitTimeout( VO_FLAG, 15.0 );

		//co.Wait( 5.25 );
		FDamageInfo info;
		info.damage = 1000;
		info.attacker = nullptr;
		info.traceHitResult = FHitResult();
		info.damageFlags = 0;
		info.damageSourceLocation = GetActorLocation();

		reviveDummy.healthComponent.ReceiveDamage( info, IncomingDamage( 1000, 0 ) );
		
		co.Wait( Training::Objectives::COMBAT_06_REVIVEFRIENDLY );

		VO_FLAG = GetVOEndedFlag( GameplayTags::Audio_VO_Training_CombatRoom_CombatComplete );
		scriptCallbacks.RegisterFlagSafe( VO_FLAG );
		co.FlagWaitTimeout( VO_FLAG, 15.0 );
		
		// End the revive objective
		//scriptCallbacks.RegisterFlagSafe( n"Combat_ReviveDialogueComplete" );
		//scriptCallbacks.FlagSet( n"Combat_ReviveDialogueComplete" );

		{
			AAS_TrainingObjectiveGroup newGroup = objectiveSys.ServerAddObjectiveGroup( Training::Objectives::COMBAT_08_GOTODESTRUCTION, player, ETrainingObjectiveCategory::DEFAULT );
			TArray<FName> markerNames;
			markerNames.Add( n"Combat_Entrance" );
			markerNames.Add( n"Destruction_Entrance" );

			SetUpLocationObjectiveChain( newGroup, markerNames, 384, true, 15 );
		}
		Thread( this, n"CheckForPlayerFallDamageAsTheyExitRoom", player, Training::Objectives::COMBAT_08_GOTODESTRUCTION );

		scriptCallbacks.FlagSet( Training::Flags::FLAG_DOOR_DESTRUCTION_GATE );
	}

	UFUNCTION()
	void OnObjectiveActiveChanged_DamageDummies_WithScope( AAS_TrainingObjectiveGroup group, bool isActive )
	{
		UpdateEnabledBullseyeDummies( sharedData.targetDummies_group2, isActive );
	}

	UFUNCTION()
	void OnDummyDamagedIncorrectly_WithoutScope( AAS_TargetDummy dummy )
	{
		if ( dummy.healthComponent.GetHealth() <= 0 )
		{
			Thread( this, n"DelayedRespawnDummyThread", dummy, 0.5, Training::Objectives::COMBAT_02_DAMAGEDUMMIES_WITHSCOPE, ETrainingObjectiveCategory::DEFAULT );
			
			// Force send an incorrect objective, since it can happen when "put scope on" objective is still active, and that objective
			// doesn't track any dummies.
			AAS_TrainingObjectiveGroup currentGroup = objectiveSys.ServerGetActiveObjective( Training::Objectives::COMBAT_02_DAMAGEDUMMIES_WITHSCOPE, ETrainingObjectiveCategory::DEFAULT );
			if ( IsValid( currentGroup ) )
			{
				const int numObjectives = currentGroup.objectives.Num();
				for( int i = 0; i < numObjectives; i++ )
				{
					UAS_TrainingObjective objective = currentGroup.objectives[ i ];

					if ( i == ( currentGroup.net_furthestCompletedObjectiveIndex + 1 ) )
					{
						objective.ServerSendObjectiveIncorrectAction();
					}
				}
			}
		}
	}

	UFUNCTION()
	void OnDummyDamagedIncorrectly_WithoutGrenade( AAS_TargetDummy dummy )
	{
		if ( dummy.healthComponent.GetHealth() <= 0 )
		{
			Thread( this, n"DelayedRespawnDummyThread", dummy, 1.5, Training::Objectives::COMBAT_03_DAMAGEDUMMIES_WITHGRENADE, ETrainingObjectiveCategory::DEFAULT );
		}
	}

	UFUNCTION()
	void DelayedRespawnDummyThread( UNCCoroutine co, AAS_TargetDummy dummy, float delay, FName objectiveToMatch, ETrainingObjectiveCategory categoryToMatch )
	{
		co.EndOnDestroyed( dummy );
		co.Wait( delay );

		UAS_ObjectiveSystem objectives = objectiveSys;
		if ( !IsValid( objectives ) )
		{
			return;
		}

		if ( objectives.ServerIsObjectiveActive( objectiveToMatch, categoryToMatch ) )
		{
			AAS_TargetDummy_Human humanDummy = Cast<AAS_TargetDummy_Human>( dummy );
			if ( IsValid( humanDummy ) )
			{
				humanDummy.ShowDummy();
			}
			dummy.Respawn();
		}
	}

	UFUNCTION()
	void DelayedRespawnDummiesThread( UNCCoroutine co, TArray<AAS_TargetDummy> dummies, float delay, FName objectiveToMatch, ETrainingObjectiveCategory categoryToMatch )
	{	
		co.Wait( delay );

		UAS_ObjectiveSystem objectives = objectiveSys;
		if ( !IsValid( objectives ) )
		{
			return;
		}

		if ( objectives.ServerIsObjectiveActive( objectiveToMatch, categoryToMatch ) )
		{
			RespawnTargetDummies( dummies );
			UpdateShownDummies( dummies, true );
		}
	}

	UFUNCTION()
	void OnObjectiveActiveChanged_DamageDummies_WithGrenade( AAS_TrainingObjectiveGroup group, bool isActive )
	{
		UpdateShownDummies( sharedData.targetDummiesToDamageWithGrenade, isActive );
	}

	UFUNCTION()
	void CheckForPlayerFallDamageAsTheyExitRoom( UNCCoroutine co, AAS_PlayerEntity asPlayer, FName endSignal )
	{
		co.EndOn( endSignal );
		co.OnCoroutineEnd.AddUFunction( this, n"OnCheckForPlayerFallDamageAsTheyExitRoomThreadEnd" );
		asPlayer.HealthComponent.BP_OnPostReceivedDamage.AddUFunction( this, n"OnPlayerPostReceivedDamage" );

		co.WaitForever();
	}

	bool hasPlayedFallDamageLine = false;
	UFUNCTION()
	void OnPlayerPostReceivedDamage( UHealthComponent damagedComponent, const FDamageInfo&in damageInfo )
	{
		// is fall damage? trigger line
		if ( !hasPlayedFallDamageLine && Bitflags::HasFlag( damageInfo.scriptDamageFlags, EScriptDamageFlags::DF_FALL_DAMAGE ) )
		{
			hasPlayedFallDamageLine = true;
			damagedComponent.BP_OnPostReceivedDamage.Unbind( this, n"OnPlayerPostReceivedDamage" );
			
			
			AAS_Vendor vendorToSpeak = ownerSession.actorTagToVendor[ Training::Vendors::VENDOR_DESTRUCTION ];
			PlayVendorDialogue( vendorToSpeak, GameplayTags::Audio_VO_Training_CombatRoom_FallDamageWarning, 0, 0 );
		}
	}

	UFUNCTION()
	void OnCheckForPlayerFallDamageAsTheyExitRoomThreadEnd( FNCCoroutineEndParams endParams )
	{
		Print(f"UNBIND!");
		if ( IsValid( player ) )
		{
			player.HealthComponent.BP_OnPostReceivedDamage.Unbind( this, n"OnPlayerPostReceivedDamage" );
		}
	}

/****************************************************************\

███████ ██    ██     ██████  ███████ ███████ ████████ ██████  ██    ██  ██████ ████████ ██  ██████  ███    ██ 
██      ██    ██     ██   ██ ██      ██         ██    ██   ██ ██    ██ ██         ██    ██ ██    ██ ████   ██ 
███████ ██    ██     ██   ██ █████   ███████    ██    ██████  ██    ██ ██         ██    ██ ██    ██ ██ ██  ██ 
     ██  ██  ██      ██   ██ ██           ██    ██    ██   ██ ██    ██ ██         ██    ██ ██    ██ ██  ██ ██ 
███████   ████       ██████  ███████ ███████    ██    ██   ██  ██████   ██████    ██    ██  ██████  ██   ████

\****************************************************************/	
	UFUNCTION()
	void Trigger_Destruction_OnEnteredRoom( AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger )
	{
		Print(f"Hit destruction room thread!");
		Thread(this, n"Destruction_Room_Thread" );
	}

	UFUNCTION()
	private void Destruction_Room_Thread( UNCCoroutine co )
	{
		// TODO: Add end signal
		// TODO: End thread cleanup?
		scriptCallbacks.FlagClear( Training::Flags::FLAG_DOOR_DESTRUCTION_GATE );		
		
		objectiveSys.ClearObjectiveGroupByName( Training::Objectives::COMBAT_08_GOTODESTRUCTION, ETrainingObjectiveCategory::DEFAULT );
	
		FName objectiveCompleteFlag = objectiveSys.GetObjectiveCompleteFlag( Training::Objectives::DESTRUCTION_01_TALKTOVENDOR );
		{
			AAS_TrainingObjectiveGroup newGroup = objectiveSys.ServerAddObjectiveGroup( Training::Objectives::DESTRUCTION_01_TALKTOVENDOR, player, ETrainingObjectiveCategory::DEFAULT );
			UAS_TrainingObjective_TalkToVendor vendorObjective = Cast< UAS_TrainingObjective_TalkToVendor >( newGroup.objectives[ 0 ] );
			vendorObjective.SetTargetVendor( sharedData.vendor_destruction );
		}
		// Includes talk to vendor
		// Do hud hint?
		scriptCallbacks.RegisterFlagSafe( objectiveCompleteFlag );
		co.FlagWait( objectiveCompleteFlag );
		co.Wait( 1 );

		// Default: Harvest ore
		sharedData.vendor_destruction.useComponent.DisableUsable();
		ownerSession.TrackAndRegenerateResourcesIfTooFewAlive( sharedData.destruction_resourcesToRegenerate, 0.4 );

		bool skipHarvestObjective = false;
		const int vesperInBackpack = CountItemsInBackpackAndValidBoxes( player, GameplayTags::Loot_Resource_Opal, nullptr, CraftingSource::BACKPACK );
		if ( vesperInBackpack > 0 )
		{
			int settingsIdx = Objectives().GetIndexForGroupName( Training::Objectives::DESTRUCTION_02_HARVESTORE );
			const FTrainingObjectiveGroupSettings& settings = Objectives().GetSettingsForIndex( settingsIdx );
			const UAS_TrainingObjective_GetItems itemsObjectiveClass = Cast< UAS_TrainingObjective_GetItems >( settings.objectiveClasses[ 0 ].Get().GetDefaultObject() );
			
			int numVesperRequired = itemsObjectiveClass.requiredItems[ 0 ].itemCount;
			if ( vesperInBackpack >= numVesperRequired )
			{
				skipHarvestObjective = true;
			}
		}

		if ( !skipHarvestObjective )
		{
			objectiveSys.ServerAddObjectiveGroup( Training::Objectives::DESTRUCTION_02_HARVESTORE, player, ETrainingObjectiveCategory::DEFAULT );
			
			co.Wait( Training::Objectives::DESTRUCTION_02_HARVESTORE );
			co.Wait( 1 );
		}
		
		// Default: Return to vendor, buy rocket
		sharedData.vendor_destruction.useComponent.EnableUsable();

		objectiveSys.ServerAddObjectiveGroup( Training::Objectives::DESTRUCTION_03_BUYROCKET, player, ETrainingObjectiveCategory::DEFAULT );
		co.Wait( Training::Objectives::DESTRUCTION_03_BUYROCKET );
		ownerSession.Destruction_RegenerateTaggedDestructiblesAndResources();
		co.Wait( 1 );

		ownerSession.TrackAndRegenerateDestructiblesIfTooFewAlive( sharedData.destruction_destructiblesToRegenerate, Training::ERegenerateDestructiblesStyle::DESTROYED_ONLY, 0.35, 1.0 );

		// TODO: Once you run out of rockets, adjust objective to say you can also break with weapons or axe
		objectiveSys.ServerAddObjectiveGroup( Training::Objectives::DESTRUCTION_04_DESTROYWALLS, player, ETrainingObjectiveCategory::DEFAULT );
		Thread( this, n"Destruction_MonitorRocketAmmoThread", player, 0, Training::Objectives::DESTRUCTION_04_DESTROYWALLS, Training::Objectives::DESTRUCTION_HINT_BUYMOREROCKETS );
		co.Wait( Training::Objectives::DESTRUCTION_04_DESTROYWALLS );
		co.Wait( 1 );

		ownerSession.StopTrackingAndRegeneratingDestructiblesIfTooFewAlive();
		ownerSession.RegenerateTaggedDestructiblesAndResources_SetDestructiblesToIron();
		ownerSession.TrackAndRegenerateDestructiblesIfTooFewAlive( sharedData.destruction_destructiblesToRegenerate, Training::ERegenerateDestructiblesStyle::DESTROYED_TO_IRON, 0.35, 1.0 );

		// Delay before regen: 2 seconds (happens in thread called by RegenerateTaggedDestructiblesAndResources_SetDestructiblesToIron)
		// Wall transition: 3 seconds
		// 1 second to let brain chill
		co.Wait( 6 );

		// TODO: Handle fire axe -- special line?
		objectiveSys.ServerAddObjectiveGroup( Training::Objectives::DESTRUCTION_05_DESTROYREINFORCEDWALL, player, ETrainingObjectiveCategory::DEFAULT );
		Thread( this, n"Destruction_MonitorRocketAmmoThread", player, 6.5, Training::Objectives::DESTRUCTION_05_DESTROYREINFORCEDWALL, Training::Objectives::DESTRUCTION_HINT_BUYMOREROCKETS_02 );
		co.Wait( Training::Objectives::DESTRUCTION_05_DESTROYREINFORCEDWALL );
		co.Wait( 1 );

		ownerSession.StopTrackingAndRegeneratingDestructiblesIfTooFewAlive();
		ownerSession.StopTrackingAndRegeneratingResources();

		// Enter abilities room
		AAS_TrainingObjectiveGroup newGroup = objectiveSys.ServerAddObjectiveGroup( Training::Objectives::DESTRUCTION_09_GOTOABILITIES, player, ETrainingObjectiveCategory::DEFAULT );
		TArray<FName> markerNames;
		markerNames.Add( n"Destruction_Exit" );
		markerNames.Add( n"Abilities_Entrance" );

		SetUpLocationObjectiveChain( newGroup, markerNames, 512, true, 15 );

		scriptCallbacks.FlagSet( Training::Flags::FLAG_DOOR_ABILITIES_GATE );

		// Two of these triggers. One past the Abilities gate will fire on server. Backup for clearing objectives if player is being cheeky when completing destruction room.
		co.FlagWait( n"FlagAbilitiesIntroA_VO" );
		if ( IsValid( newGroup ) )
		{
			newGroup.objectives[ 0 ].ServerMarkAsComplete();
			newGroup.objectives[ 1 ].ServerMarkAsComplete();
		}
	}

	/*
		Shows hint if you're out of rockets.
		Hint stays for 10 seconds, then is removed (to not permanently block objective)
		Hint can be delayed via parameter. Use if you wanna give new objective breathing room.
		After hint has shown and then been hidden, thread ends.
	*/
	UFUNCTION()
	void Destruction_MonitorRocketAmmoThread( UNCCoroutine co, AAS_PlayerEntity asPlayer, float initialDelay, FName endSignal, FName hintObjectiveName )
	{
		// Either of these objectives will kill this
		co.EndOn( endSignal );
		co.OnCoroutineEnd.AddUFunction( this, n"Destruction_OnMonitorRocketAmmoThreadEnd" );
		co.EndOnDestroyed( asPlayer );

		if ( initialDelay > 0 )
		{
			co.Wait( initialDelay );
		}

		float startShowTime = 0;
		while ( true )
		{
			const int numRockets = asPlayer.CountItemsInAllBackpacks( GameplayTags::Loot_Ammo_Rockets );

			// Only check clip ammo if they have a rocket launcher in the raid slot
			int numClipRockets = 0;
			ANCWeapon raidSlotWeapon = asPlayer.GetWeaponAtSlot( WeaponSlot::RaidToolsSlot );
			if ( IsValid( raidSlotWeapon ) )
			{
				const FNCWeaponPermutationId weaponId = MakeWeaponId( raidSlotWeapon );
				if ( IsLootIndexValidForPrimaryWeapon( weaponId ) )
				{
					FLootDataStruct lootData = GetLootDataForPrimaryWeapon( weaponId );
					if ( lootData.index == GameplayTags::Loot_Weapon_RocketLauncher )
					{
						numClipRockets = raidSlotWeapon.ClipAmmo;
					}
				}
			}
			
			const float curTime = TO_SECONDS( asPlayer.GetTimeMilliseconds() );
			const bool buyRocketHintActive = objectiveSys.ServerIsObjectiveActive( hintObjectiveName, ETrainingObjectiveCategory::TEMPORARY_HINT );
			const bool meetsCriteriaForHint = numRockets < 1 && numClipRockets == 0;
			if ( meetsCriteriaForHint && !buyRocketHintActive )
			{
				objectiveSys.ServerAddObjectiveGroup( hintObjectiveName, asPlayer, ETrainingObjectiveCategory::TEMPORARY_HINT );
				startShowTime = TO_SECONDS( asPlayer.GetTimeMilliseconds() );
			}
			
			float elapsedSinceShow = curTime - startShowTime;
			if ( buyRocketHintActive && ( !meetsCriteriaForHint || elapsedSinceShow > 10 ) )
			{
				objectiveSys.ClearObjectiveGroupByName( hintObjectiveName, ETrainingObjectiveCategory::TEMPORARY_HINT );
				break;
			}

			co.Wait( 0.1 );
		}
	}

	UFUNCTION()
	private void Destruction_OnMonitorRocketAmmoThreadEnd( FNCCoroutineEndParams endParams )
	{
		objectiveSys.ClearObjectiveGroupByName( Training::Objectives::DESTRUCTION_HINT_BUYMOREROCKETS, ETrainingObjectiveCategory::TEMPORARY_HINT );
		objectiveSys.ClearObjectiveGroupByName( Training::Objectives::DESTRUCTION_HINT_BUYMOREROCKETS_02, ETrainingObjectiveCategory::TEMPORARY_HINT );
	}

/****************************************************************\

███████ ██    ██      █████  ██████  ██ ██      ██ ████████ ██ ███████ ███████ 
██      ██    ██     ██   ██ ██   ██ ██ ██      ██    ██    ██ ██      ██      
███████ ██    ██     ███████ ██████  ██ ██      ██    ██    ██ █████   ███████ 
     ██  ██  ██      ██   ██ ██   ██ ██ ██      ██    ██    ██ ██           ██ 
███████   ████       ██   ██ ██████  ██ ███████ ██    ██    ██ ███████ ███████

\****************************************************************/	
	
	UFUNCTION()
	void Trigger_Abilities_OnEnteredRoom( AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger )
	{
		Thread( this, n"Abilities_Room_Thread", asPlayer );
	}

	UFUNCTION()
	private void Abilities_Room_Thread( UNCCoroutine co, AAS_PlayerEntity asPlayer )
	{		
		scriptCallbacks.FlagClear( Training::Flags::FLAG_DOOR_ABILITIES_GATE );
		
		// "There's a few things that make a warden a warden...""
		{
			FName VO_FLAG = GetVOEndedFlag( GameplayTags::Audio_VO_Training_AbilitiesRoom_Abilities_Intro_A );
			scriptCallbacks.RegisterFlagSafe( VO_FLAG );
			co.FlagWaitTimeout( VO_FLAG, 15 );
		}

		scriptCallbacks.RegisterFlagSafe( n"Abilities_TalkedToFlynn" );
		scriptCallbacks.FlagSet( n"Abilities_TalkedToFlynn" );

		// "That power is what makes us special, unique..."
		{
			FName VO_FLAG = GetVOEndedFlag( GameplayTags::Audio_VO_Training_AbilitiesRoom_Abilities_Intro_B );
			scriptCallbacks.RegisterFlagSafe( VO_FLAG );
			co.FlagWaitTimeout( VO_FLAG, 15 );
		}
		
		{

			ownerSession.TrackAndRegenerateDestructiblesIfTooFewAlive( sharedData.abilities_destructiblesToRegenerate, Training::ERegenerateDestructiblesStyle::DESTROYED_ONLY, 0.45, 1.0 );
			player.tacticalCooldownComponent.ResetCooldown();
			if ( !asPlayer.WeaponSlotIsEnabled( WeaponSlot::TacticalSlot ) )
			{
				asPlayer.ServerEnableWeaponSlot( WeaponSlot::TacticalSlot, Training::TACTICAL_DISABLE_CONTEXT );
				net_trainingDisabledWeaponSlots.SetNetValue( Bitflags::SetBit( net_trainingDisabledWeaponSlots, WeaponSlot::TacticalSlot ) );
			}
			
			objectiveSys.ServerAddObjectiveGroup( Training::Objectives::ABILITIES_01_BREAKWALLS_TAC, asPlayer, ETrainingObjectiveCategory::DEFAULT );
			
			TrackAndRefreshCooldown( player, player.tacticalCooldownComponent, GameplayTags::Audio_VO_Training_AbilitiesRoom_Abilities_RefreshCharges, 3 );
		}

		co.Wait( Training::Objectives::ABILITIES_01_BREAKWALLS_TAC );
		StopTrackingAndRefreshingCooldown();
		//StopPlaceObjectiveMarkerIfAbilityEmptyThread();
		co.Wait( 1 );
		
		{
			ownerSession.Abilities_RegenerateDestructibles();
			co.Wait( 2 );
			player.classManager.ResetCharacterSpecificCooldown();
			if ( !asPlayer.WeaponSlotIsEnabled( WeaponSlot::RaidUltSlot ) )
			{
				asPlayer.ServerEnableWeaponSlot( WeaponSlot::RaidUltSlot, Training::ULTIMATE_DISABLE_CONTEXT );
				net_trainingDisabledWeaponSlots.SetNetValue( Bitflags::SetBit( net_trainingDisabledWeaponSlots, WeaponSlot::RaidUltSlot ) );
			}

			asPlayer.ultCooldownComponent.ResetCooldown();

			AAS_TrainingObjectiveGroup newGroup = objectiveSys.ServerAddObjectiveGroup( Training::Objectives::ABILITIES_03_BREAKWALLS_ULT, asPlayer, ETrainingObjectiveCategory::DEFAULT );
			TrackAndRefreshCooldown( player, player.ultCooldownComponent, GameplayTags::Audio_VO_Training_AbilitiesRoom_Abilities_Ultimate_nag, 3 );
		}

		co.Wait( Training::Objectives::ABILITIES_03_BREAKWALLS_ULT );
		StopTrackingAndRefreshingCooldown();
		//StopPlaceObjectiveMarkerIfAbilityEmptyThread();
		co.Wait( 1 );

		scriptCallbacks.FlagSet( Training::Flags::FLAG_DOOR_BLIMP_GATE );

		co.Wait( 5.5 );//lets wait a bit to let the moment breath, there's a lot of destruction going on

		ownerSession.StopTrackingAndRegeneratingDestructiblesIfTooFewAlive();
		{
			AAS_TrainingObjectiveGroup newGroup = objectiveSys.ServerAddObjectiveGroup( Training::Objectives::ABILITIES_04_GOTOZEPPELIN, asPlayer, ETrainingObjectiveCategory::DEFAULT );
			
			co.FlagWait( Training::Flags::FLAG_TELEPORT_BLIMP_TRIGGER);
			newGroup.MarkObjectiveGroupAsComplete();
		}
	}
	
	const float32 TELEPORT_DARK_SCREEN_DURATION = 4;
	UFUNCTION()
	private void Trigger_OpenWorld_TeleportToBase( AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger )
	{
		scriptCallbacks.FlagSet( Training::Flags::FLAG_TELEPORT_BLIMP_TRIGGER);
		Thread( this, n"DelayedTeleport", 1.5, TELEPORT_DARK_SCREEN_DURATION, asPlayer );
	}

	UFUNCTION()
	void DelayedTeleport( UNCCoroutine co, float32 disableWeaponDelay, float32 teleportDelay, AAS_PlayerEntity asPlayer )
	{
		Print(f"Running delayed teleport!");
		co.OnCoroutineEnd.AddUFunction( this, n"OnDelayedTeleportThreadEnd" );

		co.Wait( disableWeaponDelay );
		// TODO: Re-enable when server disable weapons works again
		//TrainingServerDisablePlayerInput( asPlayer );

		co.Wait( teleportDelay );
		// Does instant respawn need to be turned off for this?
		
		//FTransform locationTransform = GetTeamBaseTeleportLocation( player.GetTeam() );
		FTransform locationTransform = sharedData.teleportToBaseMarker.GetActorTransform();
		TeleportPlayer( player, locationTransform.Location, locationTransform.Rotator() );
	}

	UFUNCTION()
	void OnDelayedTeleportThreadEnd( FNCCoroutineEndParams params )
	{
		if ( IsValid( player ) )
		{
			TrainingServerEnablePlayerInput( player );
		}
	}

	void TrainingServerDisablePlayerInput( AAS_PlayerEntity asPlayer )
	{
		asPlayer.ServerDisableWeapons();
		asPlayer.ServerDisableMovement();
		asPlayer.DisableJump();
		asPlayer.DisableCrouch();
		asPlayer.ServerDisableAds();
	}

	void TrainingServerEnablePlayerInput( AAS_PlayerEntity asPlayer )
	{
		asPlayer.ServerEnableWeapons();
		asPlayer.ServerEnableMovement();
		asPlayer.EnableJump();
		asPlayer.EnableCrouch();
		asPlayer.ServerEnableAds();
	}

/****************************************************************\

███████ ██    ██      ██████  ██████  ███████ ███    ██     ██     ██  ██████  ██████  ██      ██████  
██      ██    ██     ██    ██ ██   ██ ██      ████   ██     ██     ██ ██    ██ ██   ██ ██      ██   ██ 
███████ ██    ██     ██    ██ ██████  █████   ██ ██  ██     ██  █  ██ ██    ██ ██████  ██      ██   ██ 
     ██  ██  ██      ██    ██ ██      ██      ██  ██ ██     ██ ███ ██ ██    ██ ██   ██ ██      ██   ██ 
███████   ████        ██████  ██      ███████ ██   ████      ███ ███   ██████  ██   ██ ███████ ██████

\****************************************************************/
	UFUNCTION()
	void Trigger_OpenWorld_OnTeleportedToBase( AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger )
	{
		ownerSession.DataLayer_SetFirstHalfRuntimeState( EDataLayerRuntimeState::Loaded );
		Thread( this, n"OpenWorld_Base_Thread", asPlayer );
	}

	// TODO: Perhaps this is a separate startpoint?
	UFUNCTION()
	private void OpenWorld_Base_Thread( UNCCoroutine co, AAS_PlayerEntity asPlayer )
	{		
		AAS_Vendor openWorldVendor = ownerSession.actorTagToVendor[ Training::Vendors::VENDOR_OPENWORLD ];
		openWorldVendor.useComponent.DisableUsable();

		ownerSession.ServerSetRespawnStyle( ETrainingRespawnStyle::MODE_DEFAULT );
		UAS_ServerScript_Mode_RaidMode_Training trainingServerMode = GetTrainingServerScript();
		trainingServerMode.ResetPlayerHasSpawnedBefore( asPlayer );
		trainingServerMode.GiveStartingLoadout( asPlayer );
		
		FTransform locationTransform = sharedData.teleportToBaseMarker.GetActorTransform();
		TeleportPlayer( player, locationTransform.Location, locationTransform.Rotator() );

		// TODO: Want a paused prematch timer or something
		trainingServerMode.BeginTransitionToPrematch( 0.1 );

		TArray<AAS_PlayerEntity> playersOfTeam;
		playersOfTeam.Add( asPlayer );
		ScriptCallbacks().server_onTeamSpawnedAtBase.Broadcast( asPlayer.GetTeam(), playersOfTeam );

		AAS_RaidDomeShield dome = GetRaidDomeShieldForTeam( asPlayer.GetTeam() );
		dome.SetAllowExits( false );

		{
			FName VO_FLAG = GetVOEndedFlag( GameplayTags::Audio_VO_Training_MatchStart_Intro_A );
			scriptCallbacks.RegisterFlagSafe( VO_FLAG );
			co.FlagWaitTimeout( VO_FLAG, 15.0 );

			// Breathing room
			co.Wait( 1 );
		}

		{
			// Only show this objective if they're inside the base still.
			if ( scriptCallbacks.Flag( Training::Flags::FLAG_OPENWORLD_INSIDEBASE ) )
			{
				objectiveSys.ServerAddObjectiveGroup( Training::Objectives::OPENWORLD_01_INTRO, asPlayer, ETrainingObjectiveCategory::DEFAULT );
				co.AddWait( Training::Objectives::OPENWORLD_01_INTRO );
				co.AddWait( 5 );
				co.AwaitAll();
			}
		}

		{
			FName VO_FLAG = GetVOEndedFlag( GameplayTags::Audio_VO_Training_MatchStart_Intro_B );
			scriptCallbacks.RegisterFlagSafe( VO_FLAG );
			co.FlagWaitTimeout( VO_FLAG, 15.0 );

			// Breathing room
			co.Wait( 1.0 );
			AAS_TrainingObjectiveGroup newGroup = objectiveSys.ServerAddObjectiveGroup( Training::Objectives::OPENWORLD_02_REINFORCEWALLS, asPlayer, ETrainingObjectiveCategory::DEFAULT );
			PerformLootPickupAction( asPlayer, MakeBackpackItem( GameplayTags::Loot_Component_BuildingUpgrade_Iron, 4, 0, 0 ) );
			//TArray<FTrainingObjectiveMarkerPingData> markerPingData = objectiveSys.GetObjectiveMarkerDataForName( n"OpenWorld_ReinforceWall");
			//newGroup.ServerSetOverrideObjectiveMarker( markerPingData );

		}
		co.Wait( Training::Objectives::OPENWORLD_02_REINFORCEWALLS );

		// Unlock dome
		trainingServerMode.BeginTransitionToPlaying( 3 );

		FName VO_FLAG = GetVOEndedFlag( GameplayTags::Audio_VO_Training_DefensivePhase_End_DomeUnlocked );
		scriptCallbacks.RegisterFlagSafe( VO_FLAG );
		co.FlagWaitTimeout( VO_FLAG, 15.0 );

		RefreshArmorObjectiveMarkers( asPlayer );

		// Let player run on foot a bit
		co.Wait( 6 );

		objectiveSys.ServerAddObjectiveGroup( Training::Objectives::OPENWORLD_03_GETONMOUNT, asPlayer, ETrainingObjectiveCategory::DEFAULT );
		
		// Mount
		if ( !asPlayer.IsMountEnabled() )
		{
			asPlayer.ServerEnableMount(Training::MOUNT_DISABLE_CONTEXT );
		}

		co.Wait( Training::Objectives::OPENWORLD_03_GETONMOUNT );
		// Wait on dialogue
		co.Wait( 5 );
		
		//triggers_openworld_armorLocation
		// Turn off objective markers when player picks up armor in one of these locations

		{
			int numArmorLocationTriggers = sharedData.triggers_openworld_armorLocation.Num();
			for( int i = 0; i < numArmorLocationTriggers; i++ )
			{
				sharedData.triggers_openworld_armorLocation[ i ].onPlayerEntered.AddUFunction( this, n"OnPlayerEnteredArmorLocationTrigger" );
				sharedData.triggers_openworld_armorLocation[ i ].onPlayerExited.AddUFunction( this, n"OnPlayerExitedArmorLocationTrigger" );
			}

			AAS_TrainingObjectiveGroup newGroup = objectiveSys.ServerAddObjectiveGroup( Training::Objectives::OPENWORLD_04_GEARUP, asPlayer, ETrainingObjectiveCategory::DEFAULT );
			newGroup.onObjectiveGroupEnded.AddUFunction( this, n"OnArmorObjectiveEnded" );
			co.Wait( Training::Objectives::OPENWORLD_04_GEARUP );
			co.Wait( 1 );
		}

		{
			AAS_TrainingObjectiveGroup group = objectiveSys.ServerAddObjectiveGroup( Training::Objectives::OPENWORLD_05_GETMOREARMOR, asPlayer, ETrainingObjectiveCategory::DEFAULT );
			UAS_TrainingObjective_GetItems getItemsObjective = Cast<UAS_TrainingObjective_GetItems>( group.objectives[ 0 ] );
			getItemsObjective.onObjectiveUpdated.AddUFunction( this, n"OnGetMoreArmorObjectiveUpdated" );
			co.Wait( Training::Objectives::OPENWORLD_05_GETMOREARMOR );

			// Clear remaining marker actors
			{
				for( AAS_TrainingObjectiveMarkerActor markerActor : markerActors )
				{
					if ( IsValid( markerActor ) )
					{
						markerActor.Destroy();
					}
				}

				markerActors.Empty();
			}

			// Early match-like gear, only one set of armor
			VendorManager().Server_UpgradeVendor();
			openWorldVendor.useComponent.EnableUsable();

			//VO_FLAG = GetVOEndedFlag( GameplayTags::Audio_VO_Training_GearUp_CheckInventory );
			//scriptCallbacks.RegisterFlagSafe( VO_FLAG );
			//co.FlagWaitTimeout( VO_FLAG, 15.0 );
		}

		objectiveSys.ServerAddObjectiveGroup( Training::Objectives::OPENWORLD_06_OPENINVENTORY, asPlayer, ETrainingObjectiveCategory::DEFAULT );

		Thread( this, n"MonitorOpenInventoryThread", Training::Objectives::OPENWORLD_06_OPENINVENTORY );

		co.Wait( Training::Objectives::OPENWORLD_06_OPENINVENTORY );

		co.Wait( 1 );

		objectiveSys.ServerAddObjectiveGroup( Training::Objectives::OPENWORLD_07_BUYARMOR, asPlayer, ETrainingObjectiveCategory::DEFAULT );

		{
			int numDummyTrapTriggers = sharedData.triggers_openworld_dummyTrap.Num();
			for( int i = 0; i < numDummyTrapTriggers; i++ )
			{
				sharedData.triggers_openworld_dummyTrap[ i ].onPlayerEntered.AddUFunction( this, n"OnPlayerEnteredDummyTrapTrigger" );
				sharedData.triggers_openworld_dummyTrap[ i ].onPlayerExited.AddUFunction( this, n"OnPlayerExitedDummyTrapTrigger" );
			}
		}

		// Player has gotten third piece of armor
		co.Wait( Training::Objectives::OPENWORLD_07_BUYARMOR );
		
		ownerSession.ServerSetRespawnStyle( ETrainingRespawnStyle::TEACH_ARMOR_BREAK );
		ownerSession.ServerSetRespawnTime( 15.0 );

		// Do the dummy trap
		if ( IsValid( activeDummyTrapTrigger ) )
		{
			Thread(this, n"DummyTrapThread", player );
			co.Wait( this, killedByDummySequenceCompleteSignal );
		}
		// If they somehow got armor #3 outside of where they're supposed to, just kill em :(
		else
		{
			player.KillMe();
		}

		co.Wait( 3 );
		//objectiveSys.ServerSetObjectiveGroup( Training::Objectives::OPENWORLD_07_ARMORBROKESTUB, asPlayer, ETrainingObjectiveCategory::DEFAULT );

		// Unbind
		{
			int numDummyTrapTriggers = sharedData.triggers_openworld_dummyTrap.Num();
			for( int i = 0; i < numDummyTrapTriggers; i++ )
			{
				sharedData.triggers_openworld_dummyTrap[ i ].onPlayerEntered.Unbind( this, n"OnPlayerEnteredDummyTrapTrigger" );
				sharedData.triggers_openworld_dummyTrap[ i ].onPlayerExited.Unbind( this, n"OnPlayerExitedDummyTrapTrigger" );
			}
		}

		// Wait on dialogue
		co.Wait( 10 );
		//objectiveSys.ClearObjectiveGroupByName( Training::Objectives::OPENWORLD_07_ARMORBROKESTUB, ETrainingObjectiveCategory::DEFAULT );

		// Handle PIE instant respawn
		if ( !IsAlive( asPlayer ) )
		{
			co.Wait( asPlayer, asPlayer.OnSpawnedSignal );
		}
		
		ownerSession.ServerSetRespawnStyle( ETrainingRespawnStyle::MODE_DEFAULT );
		ownerSession.ServerSetRespawnTime( 5.0 );

		// Start shield breaker
		co.Wait( 2 );

		sharedData.sbCrafter.ResetCrafter();
		objectiveSys.ServerAddObjectiveGroup( Training::Objectives::OPENWORLD_08_GETSHIELDBREAKER, asPlayer, ETrainingObjectiveCategory::DEFAULT );
		co.Wait( Training::Objectives::OPENWORLD_08_GETSHIELDBREAKER );

		// Wait on dialogue
		co.Wait( 3 );

		
		objectiveSys.ServerAddObjectiveGroup( Training::Objectives::OPENWORLD_09_STARTRAID, asPlayer, ETrainingObjectiveCategory::DEFAULT );
		co.Wait( Training::Objectives::OPENWORLD_09_STARTRAID );
		// Wait for shield break sequence
		co.Wait( GameModeDefaults().GamemodeRules_ShieldBreakerDurationSeconds + 2 );

		// TODO: raid generator uses a different callback when exploded???
		// TODO: Wait for raid to start before doing this
		// TODO: Handle trying to plant on vault (not possible + voice line)
		
		objectiveSys.ServerAddObjectiveGroup( Training::Objectives::OPENWORLD_10_PLANTONGENERATOR, asPlayer, ETrainingObjectiveCategory::DEFAULT );
		AAS_BaseVault vault = GetVaultForTeam( Training::GetFakeEnemyTeamForPlayer( asPlayer.GetTeam() ) );
		vault.GetUseComponent().DisableUsable();

		co.Wait( Training::Objectives::OPENWORLD_10_PLANTONGENERATOR );
		co.Wait( 2 );
		
		objectiveSys.ServerAddObjectiveGroup( Training::Objectives::OPENWORLD_11_PLANTFINALGENERATOR, asPlayer, ETrainingObjectiveCategory::DEFAULT );
		co.Wait( Training::Objectives::OPENWORLD_11_PLANTFINALGENERATOR );

		// TODO: Generators have accelerated fuse times
	}

	const float32 KEEP_INVENTORY_OPEN_TIME = 1.f;
	UFUNCTION()
	void MonitorOpenInventoryThread( UNCCoroutine co, FName endOnSignalName )
	{
		co.EndOn( endOnSignalName );

		scriptCallbacks.RegisterFlagSafe( Training::Flags::FLAG_INVENTORY_OPEN );
		while ( true )
		{
			co.FlagWait( Training::Flags::FLAG_INVENTORY_OPEN );
			const float32 startTime = TO_SECONDS( GetTimeMilliseconds() );
			co.FlagWaitClearTimeout( Training::Flags::FLAG_INVENTORY_OPEN, KEEP_INVENTORY_OPEN_TIME );
			const float elapsedTime = TO_SECONDS( GetTimeMilliseconds() ) - startTime;
			if ( elapsedTime > KEEP_INVENTORY_OPEN_TIME )
			{
				Signal( n"InventoryObjectiveComplete" );
			}
		}
	}

	AAS_BrushTrigger activeArmorLocationTrigger = nullptr;
	UFUNCTION()
	void OnPlayerEnteredArmorLocationTrigger( AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger )
	{
		activeArmorLocationTrigger = trigger;
	}

	UFUNCTION()
	void OnPlayerExitedArmorLocationTrigger( AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger )
	{
		activeArmorLocationTrigger = nullptr;
	}

	// Destroy marker so it won't show up for subsequent armor objectives
	UFUNCTION()
	void OnArmorObjectiveEnded( AAS_TrainingObjectiveGroup group, ETrainingObjectiveEndContext reason )
	{
		if ( reason == ETrainingObjectiveEndContext::COMPLETED && IsValid( activeArmorLocationTrigger ) )
		{
			DestroyMarkerLinkedToTrigger( activeArmorLocationTrigger );
			RefreshArmorObjectiveMarkers( player );
		}
	}

	UFUNCTION()
	void OnGetMoreArmorObjectiveUpdated( UAS_TrainingObjective objective )
	{
		// TODO: Dropping armor instead of picking up will trigger an objective update, which would cause this to fire.
		// How to rule that case out?
		bool didDestroy = false;
		if ( IsValid( activeArmorLocationTrigger ) )
		{
			didDestroy = DestroyMarkerLinkedToTrigger( activeArmorLocationTrigger );
		}

		// Should remove the newly invalid marker, and update pings to just be one remaining marker
		if ( didDestroy )
		{
			RefreshArmorObjectiveMarkers( player );
		}
	}

	TArray<AAS_TrainingObjectiveMarkerActor> markerActors;
	void RefreshArmorObjectiveMarkers( AAS_PlayerEntity asPlayer )
	{
		TArray< AAS_TrainingObjectiveMarkerActor > markersToDestroy = markerActors;
		int numMarkersToDestroy = markersToDestroy.Num();

		TArray<FTrainingObjectiveMarkerPingData> markerData = objectiveSys.GetObjectiveMarkerDataForName( n"OpenWorld_Loot" );
		for( FTrainingObjectiveMarkerPingData data : markerData )
		{
			bool matchesExistingMarker = false;
			// Does this data match an existing marker?
			for( int i = numMarkersToDestroy - 1; i >= 0; i-- )
			{
				if ( !IsValid( markersToDestroy[ i ] ) )
				{
					continue;
				}

				if ( markersToDestroy[ i ].GetActorLocation() == data.markerLocation )
				{
					markersToDestroy.RemoveAt( i );
					numMarkersToDestroy--;
					matchesExistingMarker = true;
					break;
				}
			}

			if ( !matchesExistingMarker )
			{
				markerActors.Add( objectiveSys.ServerCreateObjectiveMarker( data, asPlayer ) );
			}
			
			for( AAS_TrainingObjectiveMarkerActor markerToDestroy : markersToDestroy )
			{
				if ( IsValid( markerToDestroy ) )
				{
					markerToDestroy.Destroy();
				}
			}
		}
	}

	bool DestroyMarkerLinkedToTrigger( AAS_BrushTrigger trigger )
	{
		bool destroyedMarkers = false;
		TArray<AActor> linkedActors = trigger.linkedActors;
		for( AActor actor : linkedActors )
		{
			AAS_ObjectiveMarkerTarget marker = Cast< AAS_ObjectiveMarkerTarget >( actor );
			if ( IsValid( marker ) )
			{
				marker.Destroy();
				destroyedMarkers = true;
			}
		}
		return destroyedMarkers;
	}

	AAS_BrushTrigger activeDummyTrapTrigger = nullptr;
	UFUNCTION()
	void OnPlayerEnteredDummyTrapTrigger( AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger )
	{
		activeDummyTrapTrigger = trigger;
	}

	UFUNCTION()
	void OnPlayerExitedDummyTrapTrigger( AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger )
	{
		activeDummyTrapTrigger = nullptr;
	}

	FNCCoroutineSignal killedByDummySequenceCompleteSignal;

	TArray<AAS_TargetDummy_Human> dummiesThatAreAttacking;
	FNCCoroutineSignal endDummyTrapThreadSignal;
	UFUNCTION()
	void DummyTrapThread( UNCCoroutine co, AAS_PlayerEntity asPlayer )
	{
		co.EndOn( this, endDummyTrapThreadSignal );
		co.EndOnDestroyed( asPlayer );
		co.OnCoroutineEnd.AddUFunction( this, n"OnDummyTrapThreadEnd" );
		
		const TArray<AActor> linkedActors = activeDummyTrapTrigger.linkedActors;
		const int numLinkedActors = linkedActors.Num();
		TArray<AAS_TargetDummy_Human> humanDummies;
		AAS_ArrowActor arrowActor;
		for( int i = 0; i < numLinkedActors; i++ )
		{
			AAS_TargetDummy_Human humanDummy = Cast<AAS_TargetDummy_Human>( linkedActors[ i ] );
			if ( IsValid( humanDummy ) )
			{
				humanDummy.Respawn();
				humanDummy.ShowDummy();
				// TODO: Give them weapons??
				humanDummy.FakeEquipWeapon( GameplayTags::Loot_Weapon_Dynasty );

				humanDummies.Add( humanDummy );
			}

			// Should be only one, if at all
			AAS_ArrowActor linkedArrowActor = Cast<AAS_ArrowActor>( linkedActors[ i ] );
			if ( IsValid( linkedArrowActor ) )
			{
				arrowActor = linkedArrowActor;
			}
		}

		// Thread to determine when to start shooting
		//Thread( this, n"WaitForRightTimeForDummiesToAttack", asPlayer, arrowActor );
		// Wait on signal from that thread
		//co.Wait( this, startDummyAttackSignal );
		
		//wait for vo warnings from announcer/vendor to finish... this won't pass localization timing but whatever
		co.Wait( 8.0 );
		
		int numDummies = humanDummies.Num();
		for( int i = 0; i < numDummies; i++ )
		{
			// TODO: Better way to determine which plays sound
			humanDummies[ i ].FakeAttackWithEquippedWeapon( asPlayer, true );
		}
		dummiesThatAreAttacking = humanDummies;

		// DEBUG -- for now, time out
		//co.Wait( 5 );
		co.Wait( asPlayer, asPlayer.OnDeathSignal );
		killedByDummySequenceCompleteSignal.Emit();

		for( int i = 0; i < numDummies; i++ )
		{
			if ( !IsValid( humanDummies[ i ] ) )
			{
				continue;
			}

			humanDummies[ i ].HideDummy();
		}
	}

	UFUNCTION()
	void OnDummyTrapThreadEnd( FNCCoroutineEndParams endParams )
	{
		killedByDummySequenceCompleteSignal.Emit();
		endDummyTrapThreadSignal.Emit();
		dummiesThatAreAttacking.Empty();
	}

	FNCCoroutineSignal startDummyAttackSignal;
	UFUNCTION()
	void WaitForRightTimeForDummiesToAttack( UNCCoroutine co, AAS_PlayerEntity asPlayer, AAS_ArrowActor arrowActor )
	{
		co.EndOn( this, endDummyTrapThreadSignal );
		
		const FVector startingLocation = asPlayer.GetActorLocation();
		const float distSqr2DThreshold = 128 * 128;
		const bool arrowValid = IsValid( arrowActor );
		const FVector arrowForward = arrowValid ? arrowActor.GetForward() : FVector::ZeroVector;
		const float dotThreshold = AnglesToDot( -20.0 );
		// Wait for player to turn around

		// TODO: Play reload sound or gun cock sound to cue player that they're about to get shot
		// TODO: If player shoots a dummy, trigger them to shoot back
		while ( true )
		{
			if ( arrowValid )
			{
				const FVector playerForward = asPlayer.GetCameraComponent().ForwardVector;
				const float facingArrowDot = playerForward.DotProduct( arrowForward );
				if ( facingArrowDot < dotThreshold )
				{
					co.Wait( 1.5 );
					break;
				}
			}
			
			const float distSqr2DToStartingLocation = asPlayer.GetActorLocation().DistSquared2D( startingLocation );
			if ( distSqr2DToStartingLocation > distSqr2DThreshold )
			{
				break;
			}

			co.Wait( 0.1 );
		}

		startDummyAttackSignal.Emit();
	}

	void TrackAndRefreshCooldown( AAS_PlayerEntity asPlayer, UAS_PlayerCooldownComponent cooldownComponent, FGameplayTag dialogueTag = FGameplayTag(), float32 dialogueAndRegenDelay = 0 )
	{
		Thread( this, n"ResetCooldownWhenEmptyThread", asPlayer, cooldownComponent, dialogueTag, dialogueAndRegenDelay );
	}

	void StopTrackingAndRefreshingCooldown()
	{
		endResetCooldownWhenEmptySignal.Emit();
	}

	FNCCoroutineSignal endResetCooldownWhenEmptySignal;
	UFUNCTION()
	void ResetCooldownWhenEmptyThread( UNCCoroutine co, AAS_PlayerEntity asPlayer, UAS_PlayerCooldownComponent cooldownComponent, FGameplayTag dialogueTag = FGameplayTag(), float32 dialogueAndRegenDelay = 0 )
	{
		endResetCooldownWhenEmptySignal.Emit();
		co.EndOn( this, endResetCooldownWhenEmptySignal );
		co.EndOnDestroyed( asPlayer );

		ANCWeapon weaponToTrack = asPlayer.GetWeaponAtSlot( cooldownComponent.weaponSlot );
		co.EndOnDestroyed( weaponToTrack );

		while ( true )
		{
			if ( !weaponToTrack.HasMinAmmo() )
			{				
				co.Wait( dialogueAndRegenDelay );
				
				if ( IsValid( cooldownComponent ) )
				{
					cooldownComponent.ResetCooldown();
				}

				if ( dialogueTag.IsValid() )
				{
					AAS_Vendor vendorToSpeak = ownerSession.actorTagToVendor[ Training::Vendors::VENDOR_ABILITIES ];
					FName VO_FLAG = GetVOEndedFlag( dialogueTag );
					scriptCallbacks.RegisterFlagSafe( VO_FLAG );
					scriptCallbacks.FlagClear(VO_FLAG);

					PlayVendorDialogue( vendorToSpeak, dialogueTag, 0, 0 );

					co.FlagWait(VO_FLAG);
				}								
			}

			co.Wait ( 0.1 ); 
		}
	}

	void PlaceObjectiveMarkerIfAbilityEmpty( AAS_PlayerEntity asPlayer, UAS_PlayerCooldownComponent component, FName markerName, AAS_TrainingObjectiveGroup objective )
	{
		Thread( this, n"PlaceObjectiveMarkerIfAbilityEmptyThread", asPlayer, component, markerName, objective );
	}

	void StopPlaceObjectiveMarkerIfAbilityEmptyThread()
	{
		endPlaceObjectiveMarkerIfAbilityEmptySignal.Emit();
	}


	// Need to do a hint objective instead of this marker, that way client can get it
	/*
	
	const FGameplayTag dialogueTag = GameplayTags::Audio_VO_Training_AbilitiesRoom_Abilities_RefreshCharges;
	UWorld world = GetCurrentWorld();
	UAS_DialogueSystem dialogueSys = Cast<UAS_DialogueSystem>( UNCGameplaySystemsSubsystem::Get_ClientSystem( world, UAS_DialogueSystem::StaticClass() ) );
	if ( IsValid( ownerSession ) && IsValid( dialogueSys ) )
	{
		FVoicePackage vp = dialogueSys.GetAlwaysLoadedVoice( GameplayTags::Audio_VO_Trader );
		AAS_Vendor speakingVendor = ownerSession.GetBestVendorToSpeak();
		dialogueSys.PlayNPCDialogue( speakingVendor, vp, dialogueTag, 0, 0 );
	}
	*/
	FNCCoroutineSignal endPlaceObjectiveMarkerIfAbilityEmptySignal;
	UFUNCTION()
	void PlaceObjectiveMarkerIfAbilityEmptyThread( UNCCoroutine co, AAS_PlayerEntity asPlayer, UAS_PlayerCooldownComponent component, FName markerName, AAS_TrainingObjectiveGroup objective )
	{
		endPlaceObjectiveMarkerIfAbilityEmptySignal.Emit();
		co.EndOn( this, endPlaceObjectiveMarkerIfAbilityEmptySignal );
		co.EndOnDestroyed( asPlayer );
		co.EndOnDestroyed( objective );

		ANCWeapon weaponToTrack = asPlayer.GetWeaponAtSlot( component.weaponSlot );
		co.EndOnDestroyed( weaponToTrack );

		float lastPingTime = 0;
		// Using this to delay the nag ping to use the platform when you first need it. Subsequent nags aren't delayed.
		bool firstPing = true;
		while ( true )
		{
			co.Wait( 0.1 );

			if ( !weaponToTrack.HasMinAmmo() )
			{
				if ( firstPing )
				{
					firstPing = false;
					co.Wait( 3 );
					continue;
				}

				float curTime = TO_SECONDS( asPlayer.GetTimeMilliseconds() );
				float elapsedTime = curTime - lastPingTime;
				if ( elapsedTime < 5.0 )
				{
					continue;
				}

				// Place ping, wait nag (10s?)
				objectiveSys.ServerAddObjectiveGroup( Training::Objectives::ABILITIES_HINT_RECHARGE, asPlayer, ETrainingObjectiveCategory::TEMPORARY_HINT );
				//objective.ServerSetOverrideObjectiveMarker( objectiveSys.GetObjectiveMarkerDataForName( markerName ) );
				lastPingTime = curTime;
				firstPing = false;
			}
			else
			{
				objectiveSys.ClearObjectiveGroupByName( Training::Objectives::ABILITIES_HINT_RECHARGE, ETrainingObjectiveCategory::TEMPORARY_HINT );
				//objective.ServerClearOverrideObjectiveMarker();
				firstPing = true;
			}
		}
	}

	UFUNCTION()
	void OnCooldownWeaponEmpty( UAS_PlayerCooldownComponent cooldownComponent )
	{
		cooldownComponent.ResetCooldown();
	}

	void UpdateEnabledBullseyeDummies( TArray<AAS_TargetDummy> dummies, bool shouldEnable )
	{
		for( AAS_TargetDummy dummy : dummies )
		{
			AAS_TargetDummy_Bullseye bullseyeDummy = Cast<AAS_TargetDummy_Bullseye>( dummy );
			if ( !IsValid( bullseyeDummy ) )
			{
				continue;
			}

			if ( shouldEnable )
			{
				bullseyeDummy.ShowDummy();
			}
			else
			{
				bullseyeDummy.HideDummy();
			}
		}

	}

	void UpdateShownDummies( TArray<AAS_TargetDummy> dummies, bool shouldShow )
	{	
		for( AAS_TargetDummy dummy : dummies )
		{
			if ( !IsValid( dummy ) )
			{
				continue;
			}

			AAS_TargetDummy_Human humanDummy = Cast<AAS_TargetDummy_Human>( dummy );
			if ( IsValid( humanDummy ) )
			{
				if ( shouldShow )
				{
					humanDummy.ShowDummy();
				}
				else
				{
					humanDummy.HideDummy();
				}
			}
		}
	}

	void RespawnTargetDummies( TArray<AAS_TargetDummy> dummies )
	{
		for( AAS_TargetDummy dummy : dummies )
		{
			if ( !IsValid( dummy ) )
			{
				continue;
			}

			dummy.Respawn();
		}
	}

	// Only works if all the objectives in the group are location objectives
	void SetUpLocationObjectiveChain( AAS_TrainingObjectiveGroup objectiveGroup, TArray<FName> markerNames, float radiusToUse, bool loopingNags, float32 loopingNagDelay )
	{
		const int numObjectives = objectiveGroup.objectives.Num();
		const int numMarkerNames = markerNames.Num();
		const int numIterations = Math::Min( numObjectives, numMarkerNames );
		for( int i = 0; i < numIterations; i++ )
		{
			UAS_TrainingObjective_Location locationObjective = Cast<UAS_TrainingObjective_Location>( objectiveGroup.objectives[ i ] );
			locationObjective.markerRadius = radiusToUse;
			const FName markerName = markerNames[ i ];

			locationObjective.objectiveMarkerName = markerName;
			locationObjective.loopingNag = loopingNags;
			locationObjective.loopingNagInterval = loopingNagDelay;
			locationObjective.UpdateMarkerData();
		}
	}

	void CleanUp_Server(AAS_PlayerEntity asPlayer) override
	{
		Super::CleanUp_Server(asPlayer);
		
		if ( !asPlayer.WeaponSlotIsEnabled( WeaponSlot::GrenadeSlot ) )
		{
			asPlayer.ServerEnableWeaponSlot( WeaponSlot::GrenadeSlot, Training::GRENADE_DISABLE_CONTEXT );
		}
		
		if ( !asPlayer.IsMountEnabled() )
		{
			asPlayer.ServerEnableMount(Training::MOUNT_DISABLE_CONTEXT );
		}
	}

	UAS_DialogueSystem dialogueSys;
	UFUNCTION(BlueprintOverride)
	void ClientBeginPlay()
	{
		Super::ClientBeginPlay();

		net_trainingDisabledWeaponSlots.OnReplicated().AddUFunction( this, n"OnTrainingDisabledWeaponSlotsChanged" );
		Dialogue().onDialogueEndedOrRemovedFromQueue.AddUFunction( this, n"OnDialogueEndedOrRemovedFromQueue");
		Dialogue().onDialogueStarted.AddUFunction( this, n"OnDialogueStarted");
	}

	UAS_MainHUDWidget mainHUDWidget;
	void Run_Client(AAS_PlayerEntity asPlayer) override
	{
		Super::Run_Client(asPlayer);

		if (!IsValid(ClientTrainingMode()))
		{
			return;
		}

		client_oldDisabledTrainingSlotState = MAX_int32;
		OnTrainingDisabledWeaponSlotsChanged( 0, net_trainingDisabledWeaponSlots );

		objectiveSys = Objectives();
		dialogueSys = Dialogue();
		scriptCallbacks = ScriptCallbacks();
		
		AAS_HUD localHud = GetLocalHUD();
		if ( IsValid( localHud ) )
		{
			mainHUDWidget = localHud.mainHUDWidget;
		}

		dialogueSys.SetIsAnnouncerGameModeDialogueBlocked( true );
		dialogueSys.SetIsPlayerDialogueBlocked( true );
		// OpenOrCloseRaidMessagingHud( false );

		Thread( this, n"Run_Client_Thread", asPlayer );
	}
	
	UFUNCTION()
	private void Run_Client_Thread( UNCCoroutine co, AAS_PlayerEntity asPlayer )
	{
		scriptCallbacks.RegisterFlag(Training::Flags::FLAG_DESTRUCTION_READY_BUY);
		scriptCallbacks.RegisterFlag(Training::Flags::FLAG_DESTRUCTION_WEAPON_BOUGHT);
		scriptCallbacks.RegisterFlag(Training::Flags::FLAG_DESTRUCTION_AMMO_BOUGHT);
		scriptCallbacks.RegisterFlag(Training::Flags::FLAG_OPENWORLD_READY_BUY);
		scriptCallbacks.RegisterFlag(Training::Flags::FLAG_OPENWORLD_ARMOR_BOUGHT);
		scriptCallbacks.RegisterFlag(Training::Flags::FLAG_OPENWORLD_SHIELDBREAKER_READY);

		sharedData.respawnInBaseTrigger.onPlayerEntered.AddUFunction( this, n"Trigger_OpenWorld_TeleportToBase_Client" );
		sharedData.trigger_intro_basicControls.onPlayerEntered.AddUFunction( this, n"Trigger_Intro_BasicControls_Client" );
		sharedData.trigger_openworld_teleportedToBase.onPlayerEntered.AddUFunction( this, n"Trigger_OpenWorld_OnTeleportedToBase_Client" );
		sharedData.trigger_destruction_vendorGreet.onPlayerEntered.AddUFunction( this, n"Trigger_Destruction_VendorGreet_Client" );
		sharedData.trigger_destruction_enteredRoom.onPlayerEntered.AddUFunction( this, n"Trigger_Destruction_OnEnteredRoom_Client" );
		sharedData.trigger_destruction_moveVendorToDestruction.onPlayerEntered.AddUFunction( this, n"Trigger_Destruction_MoveVendorToDestruction_Client" );		
		sharedData.trigger_combat_enteredRoom.onPlayerEntered.AddUFunction( this, n"Trigger_Combat_EnteredRoom_Client" );
		sharedData.trigger_combat_onRarityPlatform.onPlayerEntered.AddUFunction( this, n"Trigger_Combat_OnEnterRarityPlatform_Client" );
		sharedData.trigger_combat_onArmorPlatform.onPlayerEntered.AddUFunction( this, n"Trigger_Combat_OnEnterArmorPlatform_Client" );
		sharedData.trigger_abilities_enteredRoom.onPlayerEntered.AddUFunction( this, n"Trigger_Abilities_OnEnteredRoom_Client" );
		sharedData.trigger_abilities_afterAbilitiesGate.onPlayerEntered.AddUFunction( this, n"Trigger_Abilities_AfterAbilitiesGate_Client" );
		sharedData.trigger_zeppelin_announcerLine.onPlayerEntered.AddUFunction( this, n"Trigger_Zeppelin_AnnouncerLine_Client" );
		
		scriptCallbacks.RegisterSignalCallback( n"Trigger_Intro_ShowSprintHint", this, n"Client_Trigger_Intro_ShowSprintHint" );
		scriptCallbacks.RegisterSignalCallback( n"Trigger_Intro_HideSprintHint", this, n"Client_Trigger_Intro_HideSprintHint" );

		scriptCallbacks.RegisterSignalCallback( n"Trigger_Intro_ShowCrouchHint", this, n"Client_Trigger_Intro_ShowCrouchHint" );
		scriptCallbacks.RegisterSignalCallback( n"Trigger_Intro_HideCrouchHint", this, n"Client_Trigger_Intro_HideCrouchHint" );

		scriptCallbacks.RegisterSignalCallback( n"Trigger_Intro_ShowJumpHint", this, n"Client_Trigger_Intro_ShowJumpHint" );
		scriptCallbacks.RegisterSignalCallback( n"Trigger_Intro_HideJumpHint", this, n"Client_Trigger_Intro_HideJumpHint" );

		scriptCallbacks.RegisterSignalCallback( n"OpenWorld_NearGenerator" , this, n"Client_OpenWorld_NearGenerator" );
		scriptCallbacks.RegisterSignalCallback( n"OpenWorld_NearAnchorStone", this, n"Client_OpenWorld_NearAnchorStone" );

		scriptCallbacks.shared_OnRaidStarted.AddUFunction( this, n"cl_OnRaidStarted" );
		
		asPlayer.client_OnVendorMenuOpen.AddUFunction(this, n"cl_OnVendorMenuOpen");
		asPlayer.client_OnVendorMenuClose.AddUFunction(this, n"cl_OnVendorMenuClose");

		scriptCallbacks.client_onRaidObjectiveStateChanged.AddUFunction( this, n"OnRaidObjectiveStateChanged" );
		
		for ( AAS_Vendor vendor : sharedData.allVendors )
		{
			vendor.FSM_Component.FSMRules_AllowConfirmPurchase( false );
			vendor.FSM_Component.FSMRules_AllowGetAttention( false );
			vendor.FSM_Component.FSMRules_AllowHello( false );
			vendor.FSM_Component.FSMRules_AllowGoodbye( false );
			vendor.FSM_Component.FSMRules_AllowVignette( false );
			vendor.FSM_Component.FSMRules_AllowTimeout( false );
			
			bool instantHide = true;
			vendor.FSM_Component.ForceHide(instantHide);
		}

		#if EDITOR
		if ( GetCvarBool( f"ScriptDebug.TrainingTitleCard" ) )
		{
			co.Wait( 5 );
		}
		#else
			co.Wait( 5 );
		#endif

		Signal( n"HidingTitleCard" );

		UAS_Training_MessagingHud messagingHud;
		UAS_ClientScript_RaidMode_Training clientMode = ClientTrainingMode();
		if ( IsValid( clientMode ) )
		{
			messagingHud = clientMode.trainingMessagingHud;
			if ( IsValid( messagingHud ) )
			{
				messagingHud.HideTitle();
			}
		}

		Thread( this, n"client_UpdateTrainingProgressThread" );
	}

/****************************************************************\

 ██████ ██          ██ ███    ██ ████████ ██████   ██████  
██      ██          ██ ████   ██    ██    ██   ██ ██    ██ 
██      ██          ██ ██ ██  ██    ██    ██████  ██    ██ 
██      ██          ██ ██  ██ ██    ██    ██   ██ ██    ██ 
 ██████ ███████     ██ ██   ████    ██    ██   ██  ██████ 

\****************************************************************/
	UFUNCTION()
	private void Trigger_Intro_BasicControls_Client( AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger )
	{
		Thread( this, n"Client_Intro_Thread", asPlayer );
	}

	UFUNCTION()
	private void Client_Intro_Thread( UNCCoroutine co, AAS_PlayerEntity asPlayer )
	{
		ownerSession.ClientSetActiveVendor( Training::Vendors::VENDOR_INTRO );
		Music().PlayScriptedEvent( n"Track_Training_Intro" );

		GetLocalHUD().mainHUDWidget.DisableHudAnim();

		co.Wait( n"HidingTitleCard" );
		co.Wait( 2.5 );

		//intro VO from announcer
		
		FGameplayTag dialogueTag = GameplayTags::Audio_VO_Training_IntroRoom_Welcome_A;
		dialogueSys.PlayTrainingAnnouncerDialogue( dialogueTag, 0, 0.0 );

		dialogueTag = GameplayTags::Audio_VO_Training_IntroRoom_Welcome_B;
		dialogueSys.PlayTrainingAnnouncerDialogue( dialogueTag, 0.35, 0.0 );

		co.Wait(2.0);
		GetLocalHUD().mainHUDWidget.EnableHudAnim();

		//wait for objective to talk to flynn to get made
		
		//FName INTRO_FLAG_CREATE = Objectives().GetObjectiveCreateFlag(Training::Objectives::INTRO_02_GOTOCOMBATROOM);
		//ScriptCallbacks().RegisterFlagSafe( INTRO_FLAG_CREATE );
		//co.FlagWait( INTRO_FLAG_CREATE );		
		//AAS_TrainingObjectiveGroup grp = objectiveSys.GetActiveObjectiveGroupByName( Training::Objectives::INTRO_02_GOTOCOMBATROOM );

		FName VO_FLAG = GetVOEndedFlag( GameplayTags::Audio_VO_Training_IntroRoom_Welcome_C );
		scriptCallbacks.RegisterFlagSafe( VO_FLAG );
		
		co.AddFlagWait( VO_FLAG );
		co.AddFlagWait( n"IntroReachVendorFlag" );
		co.AwaitAll();

		AAS_Vendor speakingVendor = ownerSession.GetBestVendorToSpeak();		
		speakingVendor.PlayScriptedMontage( sharedData.vendorAnims.intro_welcome );
		Anim().animNotifyCallbacks.OnAnimEnd( speakingVendor.vendorMesh, sharedData.vendorAnims.intro_welcome ).AddUFunction(this, n"SetCombatVendorOnAnimEnd" );

		// Trader: "Welcome. I'm Flynn..."
		SendEventToServerWhenDialogueComplete( GameplayTags::Audio_VO_Training_IntroRoom_Welcome_Intro );
	}

	UFUNCTION()
	private void SetCombatVendorOnAnimEnd(USkeletalMeshComponent meshComp, UAnimSequenceBase animation,
	                                    const UAS_AnimNotifyTrackerBase notifyTracker)
	{
		TArray<AAS_ObjectiveMarkerTarget> markers = objectiveSys.GetObjectiveMarkersForName( n"Combat_Gate" );
		ScriptAssert( markers.Num() > 0, "did Combat_Gate marker get deleted?" );
		
		FVector fxDirLoc = markers[0].GetActorLocation() + FVector(0,0,500);

		bool instantHide = true;
		ownerSession.ClientSetActiveVendor( Training::Vendors::VENDOR_COMBAT, 1.5, fxDirLoc, instantHide );
	}

	UFUNCTION()
	private void Client_Trigger_Intro_ShowSprintHint( FName signalName, UObject sender )
	{
		FHUDHintData hintData = FHUDHintData( GetLocalizedText( Localization::Training, f"group_intro_sprint_hint" ), n"Sprint" );
		hintData.hintStyle	  = EHintStyle::DEFAULT;
		mainHUDWidget.SetScriptedHint( hintData, -1 );
	}

	UFUNCTION()
	private void Client_Trigger_Intro_HideSprintHint( FName signalName, UObject sender )
	{
		mainHUDWidget.ForceClearScriptedHint();
	}

	UFUNCTION()
	private void Client_Trigger_Intro_ShowCrouchHint( FName signalName, UObject sender )
	{
		FHUDHintData hintData = FHUDHintData( GetLocalizedText( Localization::Training, f"group_intro_crouch_hint" ), n"Crouch" );
		hintData.hintStyle	  = EHintStyle::DEFAULT;
		mainHUDWidget.SetScriptedHint( hintData, -1 );
	}

	UFUNCTION()
	private void Client_Trigger_Intro_HideCrouchHint( FName signalName, UObject sender )
	{
		mainHUDWidget.ForceClearScriptedHint();
	}

	UFUNCTION()
	private void Client_Trigger_Intro_ShowJumpHint( FName signalName, UObject sender )
	{
		FHUDHintData hintData = FHUDHintData( GetLocalizedText( Localization::Training, f"group_intro_jump_hint" ), n"jump" );
		hintData.hintStyle	  = EHintStyle::DEFAULT;
		mainHUDWidget.SetScriptedHint( hintData, -1 );
	}

	UFUNCTION()
	private void Client_Trigger_Intro_HideJumpHint( FName signalName, UObject sender )
	{
		mainHUDWidget.ForceClearScriptedHint();
	}

/****************************************************************\

 ██████ ██           ██████  ██████  ███    ███ ██████   █████  ████████ 
██      ██          ██      ██    ██ ████  ████ ██   ██ ██   ██    ██    
██      ██          ██      ██    ██ ██ ████ ██ ██████  ███████    ██    
██      ██          ██      ██    ██ ██  ██  ██ ██   ██ ██   ██    ██    
 ██████ ███████      ██████  ██████  ██      ██ ██████  ██   ██    ██ 

\****************************************************************/
	UFUNCTION()
	private void Trigger_Combat_EnteredRoom_Client( AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger )
	{
		AAS_Vendor speakingVendor = ownerSession.GetBestVendorToSpeak();
		dialogueSys.PlayAlwaysLoadedDialogue( speakingVendor, GameplayTags::Audio_VO_Trader, GameplayTags::Audio_VO_Training_IntroRoom_Start_Call, 0, 0 );
		Thread( this, n"Client_Combat_Room_Thread", asPlayer );
	}

	UFUNCTION()
	private void Client_Combat_Room_Thread( UNCCoroutine co, AAS_PlayerEntity asPlayer )
	{
		ownerSession.ClientSetActiveVendor( Training::Vendors::VENDOR_COMBAT );

		Music().StopScriptedEvent( n"Track_Training_Intro" );

		co.FlagWait( Training::Flags::FLAG_COMBAT_ONFIRSTPLATFORM );

		//hide the blimp
		sharedData.trainingBlimp.HideBlimp();

		//wait for vo
		FName VO_FLAG = GetVOEndedFlag( GameplayTags::Audio_VO_Training_IntroRoom_Start_Call );
		scriptCallbacks.RegisterFlagSafe( VO_FLAG );
		co.FlagWait(VO_FLAG);

		AAS_Vendor speakingVendor = ownerSession.GetBestVendorToSpeak();
		speakingVendor.PlayScriptedMontage( sharedData.vendorAnims.combat_intro );
		
		SendEventToServerWhenDialogueComplete( GameplayTags::Audio_VO_Training_CombatRoom_Intro );

		VO_FLAG = GetVOEndedFlag( GameplayTags::Audio_VO_Training_CombatRoom_Intro );
		scriptCallbacks.RegisterFlagSafe( VO_FLAG );
		co.FlagWait(VO_FLAG);

		FHUDHintData hudHintData = FHUDHintData( GetLocalizedText( Localization::Training, f"group_combat_damagedummies_hint" ), n"Fire" );
		mainHUDWidget.SetScriptedHint( hudHintData, -1 );
		co.Wait( Training::Objectives::COMBAT_01_DAMAGEDUMMIES );
		mainHUDWidget.ClearScriptedHint( hudHintData );

		VO_FLAG = GetVOEndedFlag( GameplayTags::Audio_VO_Training_CombatRoom_ScopeTraining );
		scriptCallbacks.RegisterFlagSafe( VO_FLAG );
		co.FlagWait(VO_FLAG);

		mainHUDWidget.SetTrainingDisableScopeSwapHint();
		Thread( this, n"ManageScopeSwapHUDHints", asPlayer );
		co.Wait( Training::Objectives::COMBAT_02_DAMAGEDUMMIES_WITHSCOPE );
		manageScopeSwapHUDHintsEndSignal.Emit();
		mainHUDWidget.ForceClearScriptedHint();
		mainHUDWidget.ClearTrainingDisableScopeSwapHint();

		VO_FLAG = GetVOEndedFlag( GameplayTags::Audio_VO_Training_CombatRoom_GrenadeTraining );
		scriptCallbacks.RegisterFlagSafe( VO_FLAG );
		co.FlagWait(VO_FLAG);

		hudHintData = FHUDHintData( GetLocalizedText( Localization::Training, f"group_combat_damagedummies_withgrenade_hint" ), n"SpecialWeapon0" );
		mainHUDWidget.SetScriptedHint( hudHintData, -1 );

		co.Wait( Training::Objectives::COMBAT_03_DAMAGEDUMMIES_WITHGRENADE );
		mainHUDWidget.ClearScriptedHint( hudHintData );

		co.Wait( 2.5 );

		bool instantHide = false;
		ownerSession.ClientSetActiveVendor( Training::Vendors::VENDOR_RARITY, 1.5, FVector::ZeroVector, instantHide );
		sharedData.trigger_combat_onRarityPlatform.Enable();
	}

	FNCCoroutineSignal manageScopeSwapHUDHintsEndSignal;
	UFUNCTION()
	void ManageScopeSwapHUDHints( UNCCoroutine co, AAS_PlayerEntity asPlayer )
	{
		co.EndOn( this, manageScopeSwapHUDHintsEndSignal );

		FHUDHintData hintData;
		// is there a callback when you leave ads? signal?
		while ( true )
		{
			//co.Wait( Signals::WEAP_LOCAL_PLAYER_ADS_CHANGED );
			co.Wait( 0.1 );

			ANCWeapon activeWeapon = asPlayer.GetActiveWeapon();
			if ( !IsValid( activeWeapon ) )
			{
				continue;
			}

			if ( activeWeapon.WeaponIsSwappingScopes() )
			{
				mainHUDWidget.ClearScriptedHint( hintData );
			}

			if ( asPlayer.GetAdsFraction() > 0.75 )
			{
				FName weaponClass = activeWeapon.GetWeaponClass();
				if ( !IsValidWeaponClassName( weaponClass ) )
				{
					continue;
				}

				// Does player have scope equipped?
				TArray<FName> opticMods = Weapons().GetOpticModNamesForWeaponID( MakeWeaponId( activeWeapon ) );
				int numOpticMods		= opticMods.Num();
				if ( numOpticMods <= 1 )
				{
					continue;
				}

				int idx_selectedOptic = 0;
				for ( int i = 0; i < numOpticMods; i++ )
				{
					if ( IsModActiveOnWeapon( activeWeapon, opticMods[i] ) )
					{
						idx_selectedOptic = i;
						break;
					}
				}

				int idx_nextOptic					 = ( idx_selectedOptic + 1 ) % numOpticMods;
				FName nextMod						 = opticMods[idx_nextOptic];
				FWeaponAttachmentData attachmentData = GetAttachmentData( nextMod );

				if ( attachmentData.attachmentIndex != GameplayTags::Weapons_Attachments_Optics_Ironsights )
				{
					// Clear the old hint
					mainHUDWidget.ClearScriptedHint( hintData );
					//mainHUDWidget.ForceClearScriptedHint();

					hintData = FHUDHintData( attachmentData.name, n"CycleScope" );
					hintData.hintStyle	  = EHintStyle::SCOPE_SWAP;
					hintData.isHold_gamepad = true;
					hintData.isHold_kbm = true;
					mainHUDWidget.SetScriptedHint( hintData, -1 );
				}
				// Brute force - clear ADS hint if active
				else
				{
					//mainHUDWidget.ForceClearScriptedHint();
					mainHUDWidget.ClearScriptedHint( hintData );
				}
			}
			else if ( asPlayer.GetAdsFraction() <= 0 )
			{
				
				if ( IsValid( activeWeapon ) && activeWeapon.WeaponIsSwappingScopes() )
				{
					continue;
				}
				// Clear the old hint
				mainHUDWidget.ClearScriptedHint( hintData );
				//mainHUDWidget.ForceClearScriptedHint();	

				hintData = FHUDHintData( GetLocalizedText( Localization::Training, f"group_combat_damagedummieswithscope_hint" ), n"Aim" );
				hintData.hintStyle	  = EHintStyle::DEFAULT;
				hintData.isHold_gamepad = true;
				hintData.isHold_kbm = true;
				mainHUDWidget.SetScriptedHint( hintData, -1 );
			}
		}
	}

/****************************************************************\

 ██████ ██          ██████   █████  ██████  ██ ████████ ██    ██ 
██      ██          ██   ██ ██   ██ ██   ██ ██    ██     ██  ██  
██      ██          ██████  ███████ ██████  ██    ██      ████   
██      ██          ██   ██ ██   ██ ██   ██ ██    ██       ██    
 ██████ ███████     ██   ██ ██   ██ ██   ██ ██    ██       ██  

\****************************************************************/
	UFUNCTION()
	private void Trigger_Combat_OnEnterRarityPlatform_Client(AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger)
	{
		Thread( this, n"Client_RarityPlatform_Thread", asPlayer );
	}

	UFUNCTION()
	private void Client_RarityPlatform_Thread( UNCCoroutine co, AAS_PlayerEntity asPlayer )
	{
		AAS_Vendor speakingVendor = ownerSession.GetBestVendorToSpeak();		
		speakingVendor.PlayScriptedMontage( sharedData.vendorAnims.rarity_intro );

		SendEventToServerWhenDialogueComplete( GameplayTags::Audio_VO_Training_CombatRoom_UpgradeTraining_A );
		SendEventToServerWhenDialogueComplete( GameplayTags::Audio_VO_Training_CombatRoom_UpgradeTraining_B );

		asPlayer.OnCharacterMovementUpdated.AddUFunction( this, n"OnPlayerMovementModeUpdated");

		co.Wait( Training::Objectives::COMBAT_04_WEAPONRARITY );
		
		scriptCallbacks.RegisterFlagSafe(Training::Flags::FLAG_ON_COMBAT_ZIPLINE );
		co.FlagWait( Training::Flags::FLAG_ON_COMBAT_ZIPLINE );

		asPlayer.OnCharacterMovementUpdated.Unbind( this, n"OnPlayerMovementModeUpdated");

		CombatMoveVendorToRevive();
	}

	UFUNCTION()
	private void OnPlayerMovementModeUpdated(float32 DeltaSeconds, FVector OldLocation,
	                                         FVector OldVelocity)
	{		
		AAS_PlayerEntity asPlayer = Client_GetLocalASPawn();
		if ( IsValid(asPlayer) && asPlayer.IsZiplining() )
		{
			scriptCallbacks.RegisterFlagSafe(Training::Flags::FLAG_ON_COMBAT_ZIPLINE );
			scriptCallbacks.FlagSet( Training::Flags::FLAG_ON_COMBAT_ZIPLINE );
		}
	}

	void CombatMoveVendorToRevive()
	{
		bool instantHide 				= true;
		bool instantUnhide 				= false;
		EVendorActivity hideActivity 	= EVendorActivity::INVALID;
		EVendorActivity showActivity 	= EVendorActivity::HIDDEN_2_BUSY;
		AAS_Vendor newVendor 			= ownerSession.actorTagToVendor[ Training::Vendors::VENDOR_REVIVE ];
		FVector endPos 					=  newVendor.GetActorLocation() + FVector( 0, 0, 550 );
		ownerSession.ClientSetActiveVendor( Training::Vendors::VENDOR_REVIVE, 1.75, endPos, instantHide, instantUnhide, hideActivity, showActivity );
	}

/****************************************************************\

 ██████ ██           █████  ██████  ███    ███  ██████  ██████  
██      ██          ██   ██ ██   ██ ████  ████ ██    ██ ██   ██ 
██      ██          ███████ ██████  ██ ████ ██ ██    ██ ██████  
██      ██          ██   ██ ██   ██ ██  ██  ██ ██    ██ ██   ██ 
 ██████ ███████     ██   ██ ██   ██ ██      ██  ██████  ██   ██ 

\****************************************************************/
	UFUNCTION()
	private void Trigger_Combat_OnEnterArmorPlatform_Client(AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger)
	{
		Thread( this, n"Client_Armor_Room_Thread", asPlayer );
	}	

	UFUNCTION()
	private void Client_Armor_Room_Thread( UNCCoroutine co, AAS_PlayerEntity asPlayer )
	{
		CombatMoveVendorToRevive();

		//wait until the armor vendor is the speaking vendor
		AAS_Vendor armorVendor 		= ownerSession.actorTagToVendor[ Training::Vendors::VENDOR_REVIVE ];
		AAS_Vendor speakingVendor 	= ownerSession.GetBestVendorToSpeak();
		while( speakingVendor != armorVendor )
		{
			if ( scriptCallbacks.Flag( Training::Flags::FLAG_VENDOR_TELEPORT_DONE ) )
				co.FlagWaitClear( Training::Flags::FLAG_VENDOR_TELEPORT_DONE );
			co.FlagWait( Training::Flags::FLAG_VENDOR_TELEPORT_DONE );
			
			speakingVendor = ownerSession.GetBestVendorToSpeak();
		}

		//if we're here and the first combat objective is not registered, that means we noclipped here as a "start point"
		FName ObjFlag 	= objectiveSys.GetObjectiveCompleteFlag( Training::Objectives::COMBAT_01_DAMAGEDUMMIES );
		bool devSkipped = !scriptCallbacks.FlagExists(ObjFlag);
				
		//our vendor has been set to the correct one... so lets make sure he's done teleporting before animating him.
		if ( !devSkipped )
		{
			co.FlagWait( Training::Flags::FLAG_VENDOR_TELEPORT_DONE );

			//wait a single frame to make sure that the anim from teleporting finishing doesn't stomp
			//on our scripted anim... is it hacky? yes, will players notice? no... so who cares... this
			//isn't a system that needs to be robust, it just needs to work in this one case.
			co.Wait( 0.01 ); 
		}
			
		speakingVendor.PlayScriptedMontage( sharedData.vendorAnims.revive_intro );
		
		SendEventToServerWhenDialogueComplete( GameplayTags::Audio_VO_Training_CombatRoom_ReviveTraining_A );

		FName VO_FLAG = GetVOEndedFlag( GameplayTags::Audio_VO_Training_CombatRoom_ReviveTraining_A );
		scriptCallbacks.RegisterFlagSafe( VO_FLAG );
		co.FlagWait(VO_FLAG);

		// HUD hint
		// TODO: How to wait for dialogue
		
		FHUDHintData reviveTooltip = FHUDHintData( GetLocalizedText( Localization::Training, f"objective_combat_revivefriendly_hint" ), n"Use" );
		reviveTooltip.isHold_gamepad = true;
		reviveTooltip.isHold_kbm = true;
		ShowScriptedHint( reviveTooltip, -1 );

		co.Wait( Training::Objectives::COMBAT_06_REVIVEFRIENDLY );
		
		ClearScriptedHint( reviveTooltip );

		SendEventToServerWhenDialogueComplete( GameplayTags::Audio_VO_Training_CombatRoom_CombatComplete );

		FName VO_FLAG_COMBAT_COMPLETE = GetVOEndedFlag( GameplayTags::Audio_VO_Training_CombatRoom_CombatComplete );
		scriptCallbacks.RegisterFlagSafe( VO_FLAG_COMBAT_COMPLETE );
		co.FlagWait(VO_FLAG_COMBAT_COMPLETE);
		
		TArray<AAS_ObjectiveMarkerTarget> markers = objectiveSys.GetObjectiveMarkersForName( n"Combat_Entrance" );
		ScriptAssert( markers.Num() > 0, "did Combat_Entrance marker get deleted?" );
		
		FVector fxDirLoc = markers[0].GetActorLocation() + FVector(0,0,1000);

		bool instantHide = false;
		ownerSession.ClientSetActiveVendor( Training::Vendors::VENDOR_DESTRUCTION, 2, fxDirLoc, instantHide );
	}
	
	UFUNCTION()
	void Client_OnTrackedDummyDied( AAS_TargetDummy dummy )
	{
		Signal( n"ClientTrackedDummyDied" );
	}

	UFUNCTION()
	void Client_OnTrackedDummyRespawn( AAS_TargetDummy dummy )
	{
		Signal( n"ClientTrackedDummyRespawned" );
	}

/****************************************************************\

 ██████ ██          ██████  ███████ ███████ ████████ ██████  ██    ██  ██████ ████████ ██  ██████  ███    ██ 
██      ██          ██   ██ ██      ██         ██    ██   ██ ██    ██ ██         ██    ██ ██    ██ ████   ██ 
██      ██          ██   ██ █████   ███████    ██    ██████  ██    ██ ██         ██    ██ ██    ██ ██ ██  ██ 
██      ██          ██   ██ ██           ██    ██    ██   ██ ██    ██ ██         ██    ██ ██    ██ ██  ██ ██ 
 ██████ ███████     ██████  ███████ ███████    ██    ██   ██  ██████   ██████    ██    ██  ██████  ██   ████ 

\****************************************************************/	
	UFUNCTION()
	private void Trigger_Destruction_VendorGreet_Client( AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger )
	{
		// Play first abilities voice line
		AAS_Vendor speakingVendor = ownerSession.GetBestVendorToSpeak();
		dialogueSys.PlayAlwaysLoadedDialogue( speakingVendor, GameplayTags::Audio_VO_Trader, GameplayTags::Audio_VO_Training_DestructionRoom_EntranceWall, 0, 0 );
	}

	UFUNCTION()
	private void Trigger_Destruction_OnEnteredRoom_Client( AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger )
	{
		Thread( this, n"Destruction_Room_Thread_Client", asPlayer );
	}

	UFUNCTION()
	void Trigger_Destruction_MoveVendorToDestruction_Client( AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger )
	{
		ownerSession.ClientSetActiveVendor( Training::Vendors::VENDOR_DESTRUCTION );
	}

	FNCCoroutineSignal endManageAxeTooltipThread;
	TArray<AAS_SphereTriggerActor> clientAxeTooltipTriggers;
	TMap< AAS_ResourceNode, UAS_SphereTrigger > clientAxeTooltipTriggerToResourceNode;
	bool hasHarvestedVesper = false;

	UFUNCTION()
	private void Destruction_ManageAxeTooltip( UNCCoroutine co, AAS_PlayerEntity asPlayer, FName endSignal )
	{
		co.EndOn( this, endManageAxeTooltipThread );
		co.OnCoroutineEnd.AddUFunction( this, n"OnDestruction_ManageAxeTooltipEnd" );

		ANCMeleeWeapon axe = Cast<ANCMeleeWeapon>( asPlayer.GetWeaponAtSlot( WeaponSlot::MeleeSlot ) );
		co.EndOnDestroyed( axe );

		if ( !IsValid( axe ) )
		{
			return;
		}

		asPlayer.resourceBackpackComponent.OnContentsChangedDelegate.AddUFunction( this, n"OnResourceBackpackContentsChanged" );

		for( AAS_ResourceNode node : sharedData.destruction_resourcesToRegenerate )
		{
			AActor spawnedActor = SpawnActor( AAS_SphereTriggerActor::StaticClass(), node.GetActorLocation(), FRotator::ZeroRotator, NAME_None, true );
			AAS_SphereTriggerActor clientTrigger = Cast<AAS_SphereTriggerActor>( spawnedActor );
			clientTrigger.TriggerComponent.SetRadius( 192 );
			FinishSpawningActor( clientTrigger );
			clientTrigger.TriggerComponent.onPlayerEntered.AddUFunction( this, n"OnPlayerEnteredHarvestHintTrigger" );
			clientTrigger.TriggerComponent.onPlayerExited.AddUFunction( this, n"OnPlayerExitedHarvestHintTrigger" );
			clientAxeTooltipTriggers.Add( clientTrigger );
			clientAxeTooltipTriggerToResourceNode.Add( node, clientTrigger.TriggerComponent );
			node.sh_OnResourceAmountChanged.AddUFunction( this, n"OnRegenResourceAmountChanged" );
		}

		// HACKY: Must match thread end version
		//FHUDHintData axeTooltip = FHUDHintData( GetLocalizedText( Localization::Training, f"group_destruction_breakwallhint_hint" ), n"MeleeWeapon" );
		FHUDHintData fireAxeTooltip = FHUDHintData( GetLocalizedText( Localization::Training, f"objective_destruction_harvestore_throwhint" ), n"MeleeWeapon" );
		fireAxeTooltip.isHold_gamepad = true;
		fireAxeTooltip.isHold_kbm = true;

		bool isRegularTooltip = true;
		//ShowScriptedHint( axeTooltip, -1 );

		bool justChanged = false;
		while ( true )
		{
			// If prompt changes, wait a sec, then show next prompt
			if ( justChanged )
			{
				ClearLowPrioHUDHint( fireAxeTooltip );
				co.Wait( 0.5 );
			}
			
			bool isFireAxe = axe.GetSkillCheckState() == ESkillCheckState::BONUS;
			if ( isFireAxe && isRegularTooltip && insideHarvestTriggerCount <= 0 )
			{
				if ( !justChanged )
				{
					justChanged = true;
					continue;
				}

				ShowLowPrioHUDHint( fireAxeTooltip, -1 );
				isRegularTooltip = false;
			}
			else if ( ( !isFireAxe && !isRegularTooltip ) || insideHarvestTriggerCount > 0 )
			{
				if ( !justChanged )
				{
					justChanged = true;
					continue;
				}

				isRegularTooltip = true;
			}

			justChanged = false;
			co.Wait( 0.1 );
		}
	}

	int insideHarvestTriggerCount = 0;
	UFUNCTION()
	private void OnPlayerEnteredHarvestHintTrigger( AAS_PlayerEntity asPlayer, UAS_SphereTrigger trigger )
	{
		insideHarvestTriggerCount++;

		if ( hasHarvestedVesper )
		{
			return;
		}

		FHUDHintData axeTooltip = FHUDHintData( GetLocalizedText( Localization::Training, f"group_destruction_breakwallhint_hint" ), n"MeleeWeapon" );
		ShowScriptedHint( axeTooltip, -1 );
	}

	UFUNCTION()
	private void OnPlayerExitedHarvestHintTrigger( AAS_PlayerEntity asPlayer, UAS_SphereTrigger trigger )
	{
		insideHarvestTriggerCount--;

		if ( !IsValid( mainHUDWidget ) )
		{
			return;
		}

		mainHUDWidget.ForceClearScriptedHint();
	}

	UFUNCTION()
	private void OnRegenResourceAmountChanged( AAS_ResourceNode node )
	{
		if ( !clientAxeTooltipTriggerToResourceNode.Contains( node ) && IsValid( clientAxeTooltipTriggerToResourceNode[ node ] ) )
		{
			return;
		}

		if ( node.GetResourceAmount() > 0 )
		{
			clientAxeTooltipTriggerToResourceNode[ node ].Enable();
		}
		else
		{
			clientAxeTooltipTriggerToResourceNode[ node ].Disable();
		}
	}

	UFUNCTION()
	private void OnResourceBackpackContentsChanged( UBackpackComponent backpack )
	{
		if ( backpack.GetNumItems( GameplayTags::Loot_Resource_Opal ) > 2 )
		{
			hasHarvestedVesper = true;
		}
	}

	UFUNCTION()
	void OnDestruction_ManageAxeTooltipEnd( FNCCoroutineEndParams params )
	{
		int numTriggers = clientAxeTooltipTriggers.Num();
		for( int i = numTriggers - 1; i >= 0; i-- )
		{
			if ( IsValid( clientAxeTooltipTriggers[ i ] ) )
			{
				clientAxeTooltipTriggers[ i ].Destroy();
			}

			clientAxeTooltipTriggers.RemoveAt( i );
		}
		
		clientAxeTooltipTriggers.Empty();

		if ( IsValid( mainHUDWidget ) )
		{
			mainHUDWidget.ForceClearLowPrioScriptedHint();
			mainHUDWidget.ForceClearScriptedHint();
		}
		
		player.resourceBackpackComponent.OnContentsChangedDelegate.Unbind( this, n"OnResourceBackpackContentsChanged" );
	}

	UFUNCTION()
	private void Destruction_AmmoObjective_Thread_Client( UNCCoroutine co, AAS_PlayerEntity asPlayer )
	{
		FName BUYROCKET_FLAG_CREATE 	= Objectives().GetObjectiveCreateFlag(Training::Objectives::DESTRUCTION_03_BUYROCKET);
		ScriptCallbacks().RegisterFlagSafe( BUYROCKET_FLAG_CREATE );
		
		co.FlagWait( BUYROCKET_FLAG_CREATE );

		UAS_TrainingObjective_GetItems rocketAmmoObj;
		UAS_TrainingObjective_PurchaseFromVendor rocketWeaponObj;
		AAS_TrainingObjectiveGroup grp = objectiveSys.cl_GetActiveObjectiveGroupByName( Training::Objectives::DESTRUCTION_03_BUYROCKET );
		for ( UAS_TrainingObjective objective : grp.objectives )
		{
			if ( objective.IsA ( UAS_TrainingObjective_GetItems::StaticClass() ) )
			{
				TArray<FBackpackItemStruct> items = Cast<UAS_TrainingObjective_GetItems>(objective).requiredItems;
				for ( FBackpackItemStruct item : items )
				{
					if( item.itemIndex == GameplayTags::Loot_Ammo_Rockets )
						rocketAmmoObj = Cast<UAS_TrainingObjective_GetItems>(objective);					
				}
			}
			else if ( objective.IsA ( UAS_TrainingObjective_PurchaseFromVendor::StaticClass() ) )
			{
				TArray<FGameplayTag> itemTags = Cast<UAS_TrainingObjective_PurchaseFromVendor>(objective).itemsToBuy;
				for ( FGameplayTag tag : itemTags )
				{
					if( tag == GameplayTags::Loot_Weapon_RocketLauncher )
						rocketWeaponObj = Cast<UAS_TrainingObjective_PurchaseFromVendor>(objective);					
				}					
			}
		}

		if ( IsValid(rocketAmmoObj) )
			rocketAmmoObj.onObjectiveEnded.AddUFunction( this , n"OnRocketAmmoObjectiveEnded");
		if ( IsValid(rocketWeaponObj) )
			rocketWeaponObj.onObjectiveEnded.AddUFunction( this , n"OnRocketWeaponObjectiveEnded");
	}

	UFUNCTION()
	private void OnRocketAmmoObjectiveEnded(UAS_TrainingObjective objective, ETrainingObjectiveEndContext reason)
	{
		scriptCallbacks.FlagSet( Training::Flags::FLAG_DESTRUCTION_AMMO_BOUGHT );
	}

	UFUNCTION()
	private void OnRocketWeaponObjectiveEnded(UAS_TrainingObjective objective, ETrainingObjectiveEndContext reason)
	{
		scriptCallbacks.FlagSet( Training::Flags::FLAG_DESTRUCTION_WEAPON_BOUGHT );
	}

	UFUNCTION()
	private void Destruction_Room_Thread_Client( UNCCoroutine co, AAS_PlayerEntity asPlayer )
	{
		ownerSession.ClientSetActiveVendor( Training::Vendors::VENDOR_DESTRUCTION );
		
		AAS_Vendor speakingVendor = ownerSession.GetBestVendorToSpeak();
		speakingVendor.PlayScriptedMontage( sharedData.vendorAnims.shop_intro );

		scriptCallbacks.FlagSet( Training::Flags::FLAG_DESTRUCTION_READY_BUY );

		// Harvest ore objective might be skipped. Wait for either it, or buy rockets to happen.
		FName HARVESTORE_FLAG_CREATE = Objectives().GetObjectiveCreateFlag(Training::Objectives::DESTRUCTION_02_HARVESTORE);
		ScriptCallbacks().RegisterFlagSafe( HARVESTORE_FLAG_CREATE );

		Thread(  this, n"Destruction_AmmoObjective_Thread_Client", asPlayer );
		
		if ( !scriptCallbacks.Flag( HARVESTORE_FLAG_CREATE ) )
		{
			co.AddWait( scriptCallbacks.GetFlagSignal( HARVESTORE_FLAG_CREATE ) );
		}

		FName BUYROCKET_FLAG_CREATE = Objectives().GetObjectiveCreateFlag(Training::Objectives::DESTRUCTION_03_BUYROCKET);
		ScriptCallbacks().RegisterFlagSafe( BUYROCKET_FLAG_CREATE );
		if ( !scriptCallbacks.Flag( BUYROCKET_FLAG_CREATE ) )
		{
			co.AddWait( scriptCallbacks.GetFlagSignal( BUYROCKET_FLAG_CREATE ) );
		}

		co.AwaitAny();

		// If harvest ore is active, run the tooltip thread
		if ( objectiveSys.ClientIsObjectiveActive( Training::Objectives::DESTRUCTION_02_HARVESTORE ) )
		{
			Thread( this, n"Destruction_ManageAxeTooltip", asPlayer, Training::Objectives::DESTRUCTION_02_HARVESTORE );
			co.Wait( Training::Objectives::DESTRUCTION_02_HARVESTORE );
			endManageAxeTooltipThread.Emit();
		}

		// TODO: Signal for entering vendor menu OR objective complete.. can we do OR with coroutines?
		// May have to spin off coroutine with own signal lol


		//ClearLowPrioHUDHint( hintData );

		AAS_Vendor vendor = sharedData.vendor_destruction;
		if ( IsValid( vendor ) )
		{
			vendor.onPlayerUsedVendor.AddUFunction( this, n"Client_OnPlayerUsedDestructionVendor" );
		}
		
		// HUD hint may need to go on objective HUD
		co.AddWait( n"ClientOpenedVendorMenu" );
		co.AddWait( Training::Objectives::DESTRUCTION_03_BUYROCKET );
		co.AwaitAny();

		// If already bought rocket, don't show hint
		if ( objectiveSys.ClientIsObjectiveActive( Training::Objectives::DESTRUCTION_03_BUYROCKET ) )
		{
			Thread( this, n"Client_Destruction_HandleBuyRocketHint", asPlayer );
		}
		
		Thread( this, n"Client_Destruction_ShowHintIfNotUsingRocket", asPlayer );
		co.Wait( Training::Objectives::DESTRUCTION_04_DESTROYWALLS );
		scriptCallbacks.FlagSet( n"FlagAbilitiesIntroA_EnableTrigger" );

		stopClientShowHintIfNotUsingRocketSignal.Emit();		
		
		co.Wait( Training::Objectives::DESTRUCTION_05_DESTROYREINFORCEDWALL );
		
		FName VO_FLAG_DESTRUCTION_COMPLETE = GetVOEndedFlag( GameplayTags::Audio_VO_Training_DestructionRoom_DestructionOutro );
		scriptCallbacks.RegisterFlagSafe( VO_FLAG_DESTRUCTION_COMPLETE );
		co.FlagWait( VO_FLAG_DESTRUCTION_COMPLETE );

		TArray<AAS_ObjectiveMarkerTarget> markers = objectiveSys.GetObjectiveMarkersForName( n"Destruction_vendorTeleport" );
		ScriptAssert( markers.Num() > 0, "did Destruction_vendorTeleport marker get deleted?" );
		
		FVector fxDirLoc 	= markers[0].GetActorLocation() + FVector(0,0,250);
		bool instantHide 	= false;
		float32 fxLifeTime 	= 1.35;
		ownerSession.ClientSetActiveVendor( Training::Vendors::VENDOR_ABILITIES, fxLifeTime, fxDirLoc, instantHide );

		co.AddWait( fxLifeTime );			
		co.AddFlagWait( n"FlagAbilitiesIntroA_VO" );
		co.AwaitAll();
				
		speakingVendor = ownerSession.GetBestVendorToSpeak();
		dialogueSys.PlayAlwaysLoadedDialogue( speakingVendor, GameplayTags::Audio_VO_Trader, GameplayTags::Audio_VO_Training_AbilitiesRoom_Abilities_Intro_A, 0, 0 );
		Music().PlayScriptedEvent( n"Track_Training_ZeppelinReveal" );
	}

	FHUDHintData buyRocketHintData;
	UFUNCTION()
	void Client_Destruction_HandleBuyRocketHint( UNCCoroutine co, AAS_PlayerEntity asPlayer)
	{
		buyRocketHintData.hintString 		= GetLocalizedText( Localization::Training, "objective_destruction_buyrockethint" );
		buyRocketHintData.inputAction 		= n"Back";
		buyRocketHintData.isHold_gamepad 	= true;
		
		ShowLowPrioHUDHint( buyRocketHintData, -1 );

		// Play dialogue line - 
		AAS_Vendor speakingVendor 		= ownerSession.GetBestVendorToSpeak();
		const FGameplayTag dialogueTag 	= GameplayTags::Audio_VO_Training_DestructionRoom_Purchase_Rocket;
		dialogueSys.PlayAlwaysLoadedDialogue( speakingVendor, GameplayTags::Audio_VO_Trader, dialogueTag, 0, 0 );

		co.OnCoroutineEnd.AddUFunction( this, n"Client_Destruction_HandleBuyRocketHintEnd");

		// Why is this here??????
		co.EndOn( Training::Objectives::DESTRUCTION_05_DESTROYREINFORCEDWALL );


		co.Wait( Training::Objectives::DESTRUCTION_03_BUYROCKET );
	}

	UFUNCTION()
	private void Client_Destruction_HandleBuyRocketHintEnd(FNCCoroutineEndParams endParams)
	{
		ClearLowPrioHUDHint( buyRocketHintData );
	}

	UFUNCTION()
	void Client_OnPlayerUsedDestructionVendor( AAS_PlayerEntity asPlayer, AAS_Vendor asVendor )
	{
		Signal( n"ClientOpenedVendorMenu" );
	}

	FNCCoroutineSignal stopClientShowHintIfNotUsingRocketSignal;
	UFUNCTION()
	private void Client_Destruction_ShowHintIfNotUsingRocket( UNCCoroutine co, AAS_PlayerEntity asPlayer )
	{
		co.EndOn( this, stopClientShowHintIfNotUsingRocketSignal );
		co.EndOnDestroyed( asPlayer );
		
		FHUDHintData hintData;
		hintData.hintString = GetLocalizedText( Localization::Training, "objective_destruction_breakwallshint" );
		hintData.inputAction = n"CycleGrenade";
		hintData.isHold_gamepad = true;

		bool hintActive = false;

		while ( true )
		{
			bool showHint = false;
			const int activeSlot = asPlayer.GetActiveWeaponSlot();
			if ( activeSlot != WeaponSlot::RaidToolsSlot )
			{
				ANCWeapon raidTool = asPlayer.GetWeaponAtSlot( WeaponSlot::RaidToolsSlot );
				if ( IsValid( raidTool ) )
				{
					int totalAmmo = raidTool.GetClipAmmo() + raidTool.GetStockpileAmmo();
					if ( totalAmmo > 0 )
					{
						// Show hint
						showHint = true;
					}
				}
			}

			if ( showHint && !hintActive )
			{
				ShowLowPrioHUDHint( hintData, -1 );
				hintActive = true;
			}
			else if ( !showHint && hintActive )
			{
				ClearLowPrioHUDHint( hintData );
				hintActive = false;
			}
			co.Wait( 1 );
		}
	}

/****************************************************************\

 ██████ ██          ██    ██ ███████ ███    ██ ██████   ██████  ██████      ███    ███ ███████ ███    ██ ██    ██ 
██      ██          ██    ██ ██      ████   ██ ██   ██ ██    ██ ██   ██     ████  ████ ██      ████   ██ ██    ██ 
██      ██          ██    ██ █████   ██ ██  ██ ██   ██ ██    ██ ██████      ██ ████ ██ █████   ██ ██  ██ ██    ██ 
██      ██           ██  ██  ██      ██  ██ ██ ██   ██ ██    ██ ██   ██     ██  ██  ██ ██      ██  ██ ██ ██    ██ 
 ██████ ███████       ████   ███████ ██   ████ ██████   ██████  ██   ██     ██      ██ ███████ ██   ████  ██████ 

\****************************************************************/	
	FNCCoroutineSignal endSignalVendorMenuThread;
	UFUNCTION()
	private void cl_OnVendorMenuOpen(AAS_PlayerEntity asPlayer)
	{
		endSignalVendorMenuThread.Emit();
		
		UNCScreenWidget activeScreen = GetLocalHUD().GetUIManager().GetActiveScreen();
		if ( !IsValid( activeScreen ) || !activeScreen.IsA( UAS_VendorMenu::StaticClass() ) )
			return;

		UAS_VendorMenu vendorMenu = Cast<UAS_VendorMenu>(activeScreen);
		
		if ( scriptCallbacks.Flag(Training::Flags::FLAG_DESTRUCTION_READY_BUY) )
		{
			if ( !scriptCallbacks.Flag(Training::Flags::FLAG_DESTRUCTION_WEAPON_BOUGHT) )
			{
				Thread( this, n"cl_OnVendorMenuOpenPulseItems",	Training::Flags::FLAG_DESTRUCTION_WEAPON_BOUGHT, 
																asPlayer, vendorMenu, vendorMenu.vendorWidget.raidToolColumn01 );
			}
			if ( !scriptCallbacks.Flag(Training::Flags::FLAG_DESTRUCTION_AMMO_BOUGHT) )
			{
				Thread( this, n"cl_OnVendorMenuOpenPulseItems", Training::Flags::FLAG_DESTRUCTION_AMMO_BOUGHT, 
															asPlayer, vendorMenu, vendorMenu.vendorWidget.raidToolColumn03 );
			}
		}

		if ( scriptCallbacks.Flag(Training::Flags::FLAG_OPENWORLD_READY_BUY) )
		{
			if ( !scriptCallbacks.Flag(Training::Flags::FLAG_OPENWORLD_ARMOR_BOUGHT) )
			{
				Thread( this, n"cl_OnVendorMenuOpenPulseItems",	Training::Flags::FLAG_OPENWORLD_ARMOR_BOUGHT, 
																asPlayer, vendorMenu, vendorMenu.vendorWidget.utilityGridPanel );		
			}
		}
	}
	
	UFUNCTION()
	private void cl_OnVendorMenuClose(AAS_PlayerEntity asPlayer)
	{
		endSignalVendorMenuThread.Emit();
	}

	UFUNCTION()
	private void cl_OnVendorMenuOpenPulseItems(	UNCCoroutine co, FName boughtFlag, AAS_PlayerEntity asPlayer, 
												UAS_VendorMenu vendorMenu, UGridPanel panel )
	{
		co.EndOn(this, endSignalVendorMenuThread );

		//the menu often isn't populated correctly on the first frame.
		co.Wait(0.1);

		USH_VendorManager vendorManager = VendorManager();
		if ( !IsValid( vendorManager ) )
			return;
		
		if ( scriptCallbacks.Flag( boughtFlag ) )
			return;

		AAS_VendorStoreData storeData 	= vendorManager.GetVendorStockData( vendorMenu.vendorWidget.vendorTeam );
		TArray<UWidget> children 		= panel.GetAllChildren();	

		TArray<UAS_VendorItemButtonBase> pulsingItems;

		for ( UWidget child : children )
		{
			UAS_VendorItemButtonBase vendorButton = Cast<UAS_VendorItemButtonBase>(child);
			if ( !IsValid(vendorButton) )
				continue;

			int saleIndex = VendorUtil::GetSaleIndexFromGameplayTag( storeData, vendorButton.itemIndex );
			if( !storeData.CanPlayerAffordItem( asPlayer, saleIndex ) )
				continue;

		//	vendorButton.objectiveAnimWidget.PlayPulseAnimLoop();
			vendorButton.objectiveAnimWidget.PlayObjectiveAnimLoop();
			pulsingItems.Add(vendorButton);
		}

		co.FlagWait( boughtFlag );

		for ( UAS_VendorItemButtonBase vendorItem : pulsingItems )
		{
			if ( IsValid(vendorItem))
				vendorItem.objectiveAnimWidget.StopAnim();
		}
	}

/****************************************************************\

 ██████ ██           █████  ██████  ██ ██      ██ ████████ ██ ███████ ███████ 
██      ██          ██   ██ ██   ██ ██ ██      ██    ██    ██ ██      ██      
██      ██          ███████ ██████  ██ ██      ██    ██    ██ █████   ███████ 
██      ██          ██   ██ ██   ██ ██ ██      ██    ██    ██ ██           ██ 
 ██████ ███████     ██   ██ ██████  ██ ███████ ██    ██    ██ ███████ ███████

\****************************************************************/	
	UFUNCTION()
	void Trigger_Abilities_AfterAbilitiesGate_Client( AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger )
	{
		// Play first abilities voice line
		//AAS_Vendor speakingVendor = ownerSession.GetBestVendorToSpeak();
		//dialogueSys.PlayAlwaysLoadedDialogue( speakingVendor, GameplayTags::Audio_VO_Trader, GameplayTags::Audio_VO_Training_AbilitiesRoom_Abilities_Intro_A, 0, 0 );
	}
	
	UFUNCTION()
	void Trigger_Abilities_OnEnteredRoom_Client( AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger )
	{
		Thread( this, n"Abilities_Room_Thread_Client", asPlayer );
	}

	UFUNCTION()
	void Abilities_Room_Thread_Client( UNCCoroutine co, AAS_PlayerEntity asPlayer )
	{
		scriptCallbacks.RegisterFlag( Training::Flags::FLAG_BLIMP_LOOKAT );
		scriptCallbacks.RegisterFlag( Training::Flags::FLAG_BLIMP_SPAWNED );

		//hide the blimp
		sharedData.trainingBlimp.HideBlimp();

		ownerSession.ClientSetActiveVendor( Training::Vendors::VENDOR_ABILITIES );
		SendEventToServerWhenDialogueComplete( GameplayTags::Audio_VO_Training_AbilitiesRoom_Abilities_Intro_A );
		SendEventToServerWhenDialogueComplete( GameplayTags::Audio_VO_Training_AbilitiesRoom_Abilities_Intro_B );

		Thread( this, n"Abilities_BlimpObjectiveMarkerThread", asPlayer );

		AAS_Vendor speakingVendor = ownerSession.GetBestVendorToSpeak();
		speakingVendor.PlayScriptedMontage( sharedData.vendorAnims.ability_intro );	

		co.Wait( Training::Objectives::ABILITIES_01_BREAKWALLS_TAC );
		Music().PlayScriptedEvent( n"Track_Training_ZeppelinApproach" );

		co.Wait( Training::Objectives::ABILITIES_03_BREAKWALLS_ULT );
		
		Thread( this, n"Abilities_SpawnBlimpThread", asPlayer );		
		co.Wait( 5 );//the success line has a long delay start because of the amount of destruction

		//we don't care what's in the queue... even if it's the "well that's it for me" line... 
		//because it's not important whether it's a nag that ran too long or the success line... 
		//we just need to get to the outro.
		dialogueSys.ClearQueue();
		FGameplayTag activeVOTag = dialogueSys.GetCurrentPlayingDialogueTag();
		if ( activeVOTag != GameplayTags::Dev_Invalid )
		{
			FName VO_FLAG = GetVOEndedFlag( activeVOTag );
			scriptCallbacks.RegisterFlagSafe( VO_FLAG );
			co.FlagWait(VO_FLAG);
			
			co.AddWait( 1.0 );	//add another 1.0s wait if we were waiting on a line to finish
			co.AddFlagWait( Training::Flags::FLAG_BLIMP_SPAWNED );	//but skip it if the blimp has spawned
			co.AwaitAny();
		}

		speakingVendor.PlayScriptedMontage( sharedData.vendorAnims.ability_outro );	
	}
	
	UFUNCTION()
	void Abilities_BlimpObjectiveMarkerThread(UNCCoroutine co, AAS_PlayerEntity asPlayer )
	{
		FName StartFlag = objectiveSys.GetObjectiveCreateFlag(Training::Objectives::ABILITIES_04_GOTOZEPPELIN);
		scriptCallbacks.RegisterFlagSafe(StartFlag);
		co.FlagWait( StartFlag );
		
		if ( !IsValid(GetLocalHUD()) || !IsValid(Objectives()) )
			return;

		//if we're not already spawned, put it in the starting position before creating a waypoint
		if ( !scriptCallbacks.Flag(Training::Flags::FLAG_BLIMP_SPAWNED) && !sharedData.trainingBlimp.InArriveStartPosition() )
			sharedData.trainingBlimp.SetArriveStartPosition();

		UAS_PinnableWidget blimpWaypointWidget;	
        UAS_PinnableWidgetSettings pinSettings = Objectives().markerIndexToPinSettings[ ETrainingMarkerType::LOCATION_EXACT ];
		blimpWaypointWidget = GetLocalHUD().pinnedWidgetManager.CreatePinnableWidget( pinSettings, sharedData.trainingBlimp.waypointLoc );
       
        FName EndFlag = objectiveSys.GetObjectiveCompleteFlag(Training::Objectives::ABILITIES_04_GOTOZEPPELIN);
		scriptCallbacks.RegisterFlagSafe(EndFlag);
		co.FlagWait( EndFlag );
		
        if ( IsValid(GetLocalHUD()) && IsValid( blimpWaypointWidget ) )
            GetLocalHUD().pinnedWidgetManager.RemovePinnableWidget( blimpWaypointWidget );
	}

	UFUNCTION()
	void Abilities_SpawnBlimpThread( UNCCoroutine co, AAS_PlayerEntity asPlayer )
	{
		//this turns on the trigger to force the blimp if the player is outside the room
		scriptCallbacks.FlagSet( Training::Flags::FLAG_BLIMP_READY );
		
		Thread( this, n"Abilities_SpawnBlimpLookAtThread", asPlayer );

		co.AddFlagWait( Training::Flags::FLAG_BLIMP_FORCE );
		co.AddFlagWait( Training::Flags::FLAG_BLIMP_LOOKAT );
		co.AwaitAny();

		sharedData.trainingBlimp.ShowBlimp();
		sharedData.trainingBlimp.PlayArriveAnim();

		scriptCallbacks.FlagSet( Training::Flags::FLAG_BLIMP_SPAWNED );
	}

	UFUNCTION()
	void Abilities_SpawnBlimpLookAtThread( UNCCoroutine co, AAS_PlayerEntity asPlayer )
	{
		//if we're not already spawned, put it in the starting position before testing for look
		if ( !scriptCallbacks.Flag(Training::Flags::FLAG_BLIMP_SPAWNED) && !sharedData.trainingBlimp.InArriveStartPosition() )
			sharedData.trainingBlimp.SetArriveStartPosition();

		co.EndOn( Training::Flags::FLAG_BLIMP_SPAWNED );

		while( true )
		{
			if ( scriptCallbacks.Flag(Training::Flags::FLAG_BLIMP_CAN_LOOKAT) )
			{
				//check for looking at blimp
				FVector dir 	= sharedData.trainingBlimp.waypointLoc.GetWorldLocation() - asPlayer.GetEyeLocation();
				dir.Normalize();
				float dot 		= dir.DotProduct( asPlayer.GetEyeForward() );
				float angles 	= DotToAngles(dot);

				if ( angles <= 65 )//wide FOV's
					scriptCallbacks.FlagSet( Training::Flags::FLAG_BLIMP_LOOKAT );	
			}

			co.Wait( 0.01 );
		}
	}

	UFUNCTION()
	void Trigger_Zeppelin_AnnouncerLine_Client( AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger )
	{
		const FGameplayTag dialogueTag = GameplayTags::Audio_VO_Training_Blimp_Pre_Blimp;
		dialogueSys.PlayTrainingAnnouncerDialogue( dialogueTag, 0, 0 );
	}

/****************************************************************\

 ██████ ██           ██████  ██████  ███████ ███    ██     ██     ██  ██████  ██████  ██      ██████  
██      ██          ██    ██ ██   ██ ██      ████   ██     ██     ██ ██    ██ ██   ██ ██      ██   ██ 
██      ██          ██    ██ ██████  █████   ██ ██  ██     ██  █  ██ ██    ██ ██████  ██      ██   ██ 
██      ██          ██    ██ ██      ██      ██  ██ ██     ██ ███ ██ ██    ██ ██   ██ ██      ██   ██ 
 ██████ ███████      ██████  ██      ███████ ██   ████      ███ ███   ██████  ██   ██ ███████ ██████

\****************************************************************/	
	UFUNCTION()
	private void Trigger_OpenWorld_TeleportToBase_Client( AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger )
	{
		FName colorID 				= CommonUiColorMpcNames::GetColorIdFromEnum( ECommonUiMpcColors::BLACK );
		const FLinearColor uiBlack 	= GetCommonUiMpcColor( colorID );
		ScreenFade().ScreenFadeToColor(uiBlack, 0.5 );
	}

	UFUNCTION()
	private void Trigger_OpenWorld_OnTeleportedToBase_Client( AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger )
	{		
		//clean up the blimp
		sharedData.trainingBlimp.CleanupBlimp();

		FName colorID 				= CommonUiColorMpcNames::GetColorIdFromEnum( ECommonUiMpcColors::BLACK );
		FLinearColor uiBlackTrans	= GetCommonUiMpcColor( colorID );
		uiBlackTrans.A = 0;
		ScreenFade().ScreenFadeToColor( uiBlackTrans, 0.5 );
		
		//if ( HasRaidMessagingHudHideRequest( n"Training" ) )
		//	RemoveRaidMessagingHudHideRequest( n"Training" );		
		
		const FGameplayTag dialogueTag = GameplayTags::Audio_VO_Training_MatchStart_Intro_A;
		SendEventToServerWhenDialogueComplete( GameplayTags::Audio_VO_Training_MatchStart_Intro_A );
		dialogueSys.PlayTrainingAnnouncerDialogue( dialogueTag, 2, 0.5 );

		const int playerTeam = asPlayer.GetTeam();
		TArray<AAS_BaseSubObjective> generators;
		GetAllActorsOfClass( generators );
		for( AAS_BaseSubObjective generator : generators )
		{
			if ( IsFriendly( generator.GetTeam(), playerTeam ) )
			{
				generator.mapMarkerComponent.HideMarkers();
			}
		}

		TArray<AAS_BaseVault> baseVaults;
		GetAllActorsOfClass( baseVaults );
		for( AAS_BaseVault vault : baseVaults )
		{
			if ( IsFriendly( vault.GetTeam(), playerTeam ) )
			{
				vault.mapMarkerComponent.HideMarkers();
			}
		}

		ownerSession.DataLayer_SetFirstHalfRuntimeState( EDataLayerRuntimeState::Loaded );
		
		Thread( this, n"Open_World_Thread_Client", asPlayer );
	}
	UFUNCTION()
	void Open_World_Thread_Client( UNCCoroutine co, AAS_PlayerEntity asPlayer )
	{
		dialogueSys.PlayTrainingAnnouncerDialogue( GameplayTags::Audio_VO_Training_MatchStart_Intro_B, 0.35 );
		SendEventToServerWhenDialogueComplete( GameplayTags::Audio_VO_Training_MatchStart_Intro_B );

		// Put intro dialogue here

		//FName VO_FLAG = GetVOEndedFlag( GameplayTags::Audio_VO_Training_MatchStart_Intro_B );
		//scriptCallbacks.RegisterFlagSafe( VO_FLAG );
		//co.FlagWaitTimeout( VO_FLAG, 60.0 );

		//{
		//	const FGameplayTag dialogueTag = GameplayTags::Audio_VO_Training_DefensivePhase_ReinforceWalls;
		//	dialogueSys.PlayTrainingAnnouncerDialogue( dialogueTag, 0, 0.5 );
		//}

		// TODO: End / cancel thread		
		co.Wait( Training::Objectives::OPENWORLD_02_REINFORCEWALLS );
		
		{
			const FGameplayTag dialogueTag = GameplayTags::Audio_VO_Training_DefensivePhase_End_DomeUnlocked;
			dialogueSys.PlayTrainingAnnouncerDialogue( dialogueTag, 0, 0.5 );
		}

		//hide enemy base objective markers ( turn on when dome is destroyed );
		int enemyTeam = GetOtherTeam( Client_GetLocalASPawn().GetTeam() );
		Client_SetBaseObjectiveIconVisibility( enemyTeam, false );
		AAS_RaidDomeShield enemyDome = GetRaidDomeShieldForTeam(enemyTeam);
		enemyDome.net_domeFlags.OnReplicated().AddUFunction( this, n"cl_OnEnemyDomeFlagChanged" );

		SendEventToServerWhenDialogueComplete( GameplayTags::Audio_VO_Training_DefensivePhase_End_DomeUnlocked );

		co.Wait( Training::Objectives::OPENWORLD_03_GETONMOUNT );
		Thread( this, n"Client_OpenWorld_ArmorNagsThread" );

		co.Wait( Training::Objectives::OPENWORLD_05_GETMOREARMOR );
		Thread( this, n"CLient_OpenWorld_VendorThread", asPlayer );
		
		scriptCallbacks.FlagSet( Training::Flags::FLAG_OPENWORLD_READY_BUY );
		Thread( this, n"Client_OpenWorld_BuyArmorThread", asPlayer );

		//SendEventToServerWhenDialogueComplete( GameplayTags::Audio_VO_Training_GearUp_CheckInventory );
		//FName VO_FLAG = GetVOEndedFlag( GameplayTags::Audio_VO_Training_GearUp_CheckInventory );
		//scriptCallbacks.RegisterFlagSafe( VO_FLAG );
		//co.FlagWaitTimeout( VO_FLAG, 10 );

		{
			FName completeFlag = objectiveSys.GetObjectiveCompleteFlag( Training::Objectives::OPENWORLD_05_GETMOREARMOR );
			scriptCallbacks.RegisterFlagSafe( completeFlag );
			co.FlagWait( completeFlag );
		}
		
		{
			scriptCallbacks.onInventoryOpened.AddUFunction( this, n"OnScreenOpened" );
			scriptCallbacks.onInventoryClosed.AddUFunction( this, n"OnScreenClosed" );
			const FText hintText = GetLocalizedText( Localization::Training, f"objective_openworld_openinventory_hint" );
			FHUDHintData hintData = FHUDHintData( hintText, n"Inventory" );
			ShowLowPrioHUDHint( hintData, -1 );
		
			{
				const FGameplayTag dialogueTag = GameplayTags::Audio_VO_Training_GearUp_CheckInventory;
				dialogueSys.PlayTrainingAnnouncerDialogue( dialogueTag, 0.5, 0.0 );
			}
			
			FName completeFlag = objectiveSys.GetObjectiveCompleteFlag( Training::Objectives::OPENWORLD_06_OPENINVENTORY );
			scriptCallbacks.RegisterFlagSafe( completeFlag );

			co.AddFlagWait( completeFlag );
			co.AwaitAny();
			
			ClearLowPrioHUDHint( hintData );
			scriptCallbacks.onInventoryOpened.Unbind( this, n"OnScreenOpened" );
			scriptCallbacks.onInventoryClosed.Unbind( this, n"OnScreenClosed" );
		}

		//co.Wait( Training::Objectives::OPENWORLD_07_ARMORBROKESTUB );
		// TODO: Handle missing these signal
		co.Wait( asPlayer, asPlayer.OnDeathSignal );
		co.Wait( 1.5 );

		{
			FGameplayTag dialogueTag = GameplayTags::Audio_VO_Training_GearUp_Killed_A;
			dialogueSys.PlayTrainingAnnouncerDialogue( dialogueTag, 0, 0.0 );

			dialogueTag = GameplayTags::Audio_VO_Training_GearUp_Killed_B;
			dialogueSys.PlayTrainingAnnouncerDialogue( dialogueTag, 0.35, 0.0 );
			
			dialogueTag = GameplayTags::Audio_VO_Training_GearUp_Respawn;
			dialogueSys.PlayTrainingAnnouncerDialogue( dialogueTag, 2, 0.0 );

			AAS_Vendor asVendor = ownerSession.GetBestVendorToSpeak();
			if ( IsValid( asVendor ) )
			{
				dialogueTag = GameplayTags::Audio_VO_Training_GearUp_DeadMenu_TraderShopOpen;
				dialogueSys.PlayAlwaysLoadedDialogue( asVendor, GameplayTags::Audio_VO_Trader, dialogueTag, 0.5, 0 );
			}
		}
		
		co.FlagWait( Training::Flags::FLAG_OPENWORLD_SHIELDBREAKER_READY );

		{
			const FGameplayTag dialogueTag = GameplayTags::Audio_VO_Training_Shieldbreaker_Shieldbreaker_Ready;
			dialogueSys.PlayTrainingAnnouncerDialogue( dialogueTag, 0, 0.0 );
		}
		
		co.AddWait( n"OpenWorld_EnterCloseToBaseTrigger" );
		co.AddWait( Training::Objectives::OPENWORLD_09_STARTRAID );
		co.AwaitAny();

		if ( objectiveSys.ClientIsObjectiveActive( Training::Objectives::OPENWORLD_09_STARTRAID ) )
		{
			const FGameplayTag dialogueTag = GameplayTags::Audio_VO_Training_Shieldbreaker_FindPlantPoint;
			dialogueSys.PlayTrainingAnnouncerDialogue( dialogueTag, 0, 0.0 );
		}		

		//FName completeFlag = objectiveSys.GetObjectiveCompleteFlag( Training::Objectives::OPENWORLD_09_STARTRAID );
		//scriptCallbacks.RegisterFlagSafe( completeFlag );
		//co.FlagWait( completeFlag );

		FName VO_START_FLAG = GetVOStartedFlag( GameplayTags::Audio_VO_Training_SiegeTower_DamageToBase );
		scriptCallbacks.RegisterFlagSafe( VO_START_FLAG );
		co.FlagWait( VO_START_FLAG );

		UAS_RaidMessagingTrainingHud trainingHud = GetTrainingRaidHud();
		if ( IsValid(trainingHud) )
			trainingHud.HighlightEnemyScoreBoardWidget();
	}

	UFUNCTION()
	void OnScreenOpened()
	{
		// Send event
		UNCTrainingOpenInventoryEvent netEvent = Cast<UNCTrainingOpenInventoryEvent>( NewObject( GetCurrentWorld(), UNCTrainingOpenInventoryEvent::StaticClass() ) );
		netEvent.net_inventoryOpen.SetNetValue( true );
		netEvent.SendToServer();
	}

	UFUNCTION()
	void OnScreenClosed()
	{
		// Send event
		UNCTrainingOpenInventoryEvent netEvent = Cast<UNCTrainingOpenInventoryEvent>( NewObject( GetCurrentWorld(), UNCTrainingOpenInventoryEvent::StaticClass() ) );
		netEvent.net_inventoryOpen.SetNetValue( false );
		netEvent.SendToServer();
	}

	UFUNCTION()
	void Client_OpenWorld_RespawnNagLine( UNCCoroutine co, AAS_PlayerEntity asPlayer )
	{
		co.EndOn( asPlayer, asPlayer.OnSpawnedSignal );

		co.Wait( 15 );
		const FGameplayTag dialogueTag = GameplayTags::Audio_VO_Training_GearUp_Respawn_nag;
		dialogueSys.PlayTrainingAnnouncerDialogue( dialogueTag, 0, 0.0 );
	}

	UFUNCTION()
	private void Client_OpenWorld_BuyArmorThread( UNCCoroutine co, AAS_PlayerEntity asPlayer )
	{
		FName BUYARMOR_FLAG_CREATE 	= Objectives().GetObjectiveCreateFlag(Training::Objectives::OPENWORLD_07_BUYARMOR);
		ScriptCallbacks().RegisterFlagSafe( BUYARMOR_FLAG_CREATE );
		
		co.FlagWait( BUYARMOR_FLAG_CREATE );

		UAS_TrainingObjective_PurchaseFromVendor buyArmorObj;
		AAS_TrainingObjectiveGroup grp = objectiveSys.cl_GetActiveObjectiveGroupByName( Training::Objectives::OPENWORLD_07_BUYARMOR );
		for ( UAS_TrainingObjective objective : grp.objectives )
		{
			if ( objective.IsA ( UAS_TrainingObjective_PurchaseFromVendor::StaticClass() ) )
			{
				TArray<FGameplayTag> itemTags = Cast<UAS_TrainingObjective_PurchaseFromVendor>(objective).itemsToBuy;
				for ( FGameplayTag tag : itemTags )
				{
					if( tag == GameplayTags::Loot_Armor_Level2 )
						buyArmorObj = Cast<UAS_TrainingObjective_PurchaseFromVendor>(objective);					
				}					
			}
		}

		if ( IsValid(buyArmorObj) )
			buyArmorObj.onObjectiveEnded.AddUFunction( this , n"OnBuyArmorObjectiveEnded");
	}

	UFUNCTION()
	private void OnBuyArmorObjectiveEnded(UAS_TrainingObjective objective, ETrainingObjectiveEndContext reason)
	{
		scriptCallbacks.FlagSet( Training::Flags::FLAG_OPENWORLD_ARMOR_BOUGHT );
	}

	UFUNCTION()
	void CLient_OpenWorld_VendorThread(UNCCoroutine co, AAS_PlayerEntity asPlayer )
	{		
		ownerSession.ClientSetActiveVendor( Training::Vendors::VENDOR_OPENWORLD );

		co.FlagWait( Training::Flags::FLAG_OPENWORLD_AT_VENDOR );

		AAS_Vendor speakingVendor = ownerSession.GetBestVendorToSpeak();		
		speakingVendor.PlayScriptedMontage( sharedData.vendorAnims.openworld_armor );

		FName VO_FLAG = GetVOEndedFlag( GameplayTags::Audio_VO_Training_GearUp_BuyArmor_Trader );
		scriptCallbacks.RegisterFlagSafe( VO_FLAG );
		co.FlagWait( VO_FLAG );

		// If player has no vesper
		if ( asPlayer.resourceBackpackComponent.GetNumItems( GameplayTags::Loot_Resource_Opal) < 4 )
		{
			dialogueSys.PlayAlwaysLoadedDialogue( speakingVendor, GameplayTags::Audio_VO_Trader, GameplayTags::Audio_VO_Training_GearUp_HarvestVesper_Reminder, 0.5, 0 );
		}

		FName completeFlag = Objectives().GetObjectiveCompleteFlag( Training::Objectives::OPENWORLD_07_BUYARMOR );
		scriptCallbacks.RegisterFlagSafe( completeFlag );
		co.FlagWait( completeFlag );
		co.Wait( 2.0 );
		
		//kick the player out of the vendor menu
		UNCScreenWidget activeScreen = GetLocalHUD().GetUIManager().GetActiveScreen();
		if ( IsValid( activeScreen ) && activeScreen.IsA( UAS_VendorMenu::StaticClass() ) )
			GetLocalHUD().CloseCurrentMenu();

		const FGameplayTag dialogueTag = GameplayTags::Audio_VO_Training_GearUp_Ready_Kill;
		dialogueSys.PlayTrainingAnnouncerDialogue( dialogueTag, 0, 0.0 );

		VO_FLAG = GetVOEndedFlag( dialogueTag );
		scriptCallbacks.RegisterFlagSafe( VO_FLAG );
		co.FlagWait( VO_FLAG );
		
		speakingVendor.PlayScriptedMontage( sharedData.vendorAnims.openworld_kill );

		//co.Wait( Training::Objectives::OPENWORLD_07_ARMORBROKESTUB );
		co.Wait( asPlayer, asPlayer.OnSpawnedSignal );

		AAS_Vendor openWorldVender = ownerSession.actorTagToVendor[ Training::Vendors::VENDOR_OPENWORLD ];
		openWorldVender.FSM_Component.FSMRules_AllowConfirmPurchase( true );
		openWorldVender.FSM_Component.FSMRules_AllowGetAttention( true );
		openWorldVender.FSM_Component.FSMRules_AllowHello( true );
		openWorldVender.FSM_Component.FSMRules_AllowGoodbye( true );
		openWorldVender.FSM_Component.FSMRules_AllowVignette( true );
		openWorldVender.FSM_Component.FSMRules_AllowTimeout( true );
	}

	const float32 BLUE_CHEST_TRIGGER_RADIUS = 700;
	UFUNCTION()
	void Client_OpenWorld_ArmorNagsThread( UNCCoroutine co )
	{
		scriptCallbacks.RegisterFlag( Training::Flags::FLAG_OPENWORLD_BLUECHEST_OPEN );
		scriptCallbacks.RegisterFlag( Training::Flags::FLAG_OPENWORLD_REDCHEST_OPEN );
		
		TArray<AAS_DisplayedLootChest> lootChests;
		TArray<AAS_DisplayedLootChest> blueChests;		
		TArray<AAS_DisplayedLootChest> redChests;
		TArray<UAS_SphereTrigger> blueTriggers;
		GetAllActorsOfClass( lootChests );

		for ( AAS_DisplayedLootChest chest : lootChests )
		{
			if ( chest.pingableComponent.pingType == EPlayerPingType::WEAPONCHEST )
				redChests.Add( chest );
			else if ( chest.pingableComponent.pingType == EPlayerPingType::LOOT_CONTAINER_UNOPENED )
				blueChests.Add( chest );
		}

		
		for ( AAS_DisplayedLootChest blue : blueChests )
		{
			UAS_SphereTrigger trigger = Cast<UAS_SphereTrigger>(blue.CreateComponent( UAS_SphereTrigger::StaticClass() ));
			trigger.SetRadius(BLUE_CHEST_TRIGGER_RADIUS);
			blueTriggers.Add(trigger);

			trigger.onPlayerEntered.AddUFunction( this, n"cl_OnPlayerNearBlueChest" );
			blue.useComponent.OnUsed.AddUFunction( this, n"cl_OnPlayerOpenBlueChest" );
		}

		for ( AAS_DisplayedLootChest red : redChests )
		{
			red.useComponent.OnUsed.AddUFunction( this, n"cl_OnPlayerOpenRedChest" );
		}

		//as soon as we find a blue chest, we're done nagging
		co.FlagWait(Training::Flags::FLAG_OPENWORLD_BLUECHEST_OPEN);

		for ( AAS_DisplayedLootChest blue : blueChests )
			blue.useComponent.OnUsed.Unbind( this, n"cl_OnPlayerOpenBlueChest" );

		for ( AAS_DisplayedLootChest red : redChests )
			red.useComponent.OnUsed.Unbind( this, n"cl_OnPlayerOpenRedChest" );

		for ( UAS_SphereTrigger trigger : blueTriggers )	
			trigger.onPlayerEntered.Unbind( this, n"cl_OnPlayerNearBlueChest" );
	}

	UFUNCTION()
	private void cl_OnPlayerNearBlueChest(AAS_PlayerEntity asPlayer, UAS_SphereTrigger trigger)
	{
		if ( scriptCallbacks.Flag(Training::Flags::FLAG_OPENWORLD_BLUECHEST_OPEN) )
			return;

		Thread( this, n"Client_OpenWorld_LookAtBlueChestThread", asPlayer, trigger );
	}

	UFUNCTION()
	private void cl_OnPlayerOpenBlueChest(UUsableItemComponent component, ANCPlayerCharacter playerUser)
	{
		if ( scriptCallbacks.Flag(Training::Flags::FLAG_OPENWORLD_BLUECHEST_OPEN) )
			return;
		scriptCallbacks.FlagSet(Training::Flags::FLAG_OPENWORLD_BLUECHEST_OPEN);
	}

	UFUNCTION()
	private void cl_OnPlayerOpenRedChest(UUsableItemComponent component, ANCPlayerCharacter playerUser)
	{
		if ( scriptCallbacks.Flag(Training::Flags::FLAG_OPENWORLD_REDCHEST_OPEN) )
			return;
		scriptCallbacks.FlagSet(Training::Flags::FLAG_OPENWORLD_REDCHEST_OPEN);

		if ( scriptCallbacks.Flag(Training::Flags::FLAG_OPENWORLD_BLUECHEST_OPEN) )
			return;		

		Thread( this, n"Client_OpenWorld_PlayLookForBlueChestVODelayed");
	}

	UFUNCTION()
	void Client_OpenWorld_LookAtBlueChestThread( UNCCoroutine co, AAS_PlayerEntity asPlayer, UAS_SphereTrigger trigger )
	{
		AAS_DisplayedLootChest blueChest = Cast<AAS_DisplayedLootChest>(trigger.GetOwner() );
		if ( !IsValid(blueChest) )
			return;

		while(true )
		{
			co.Wait( 0.2 );

			if ( !trigger.playersInside.Contains(asPlayer) )
				return;
			
			if ( scriptCallbacks.Flag(Training::Flags::FLAG_OPENWORLD_BLUECHEST_OPEN) )
				return;

			FVector dir = blueChest.GetActorLocation() - asPlayer.GetEyeLocation();
			dir.Normalize();
			
			float dot 		= asPlayer.GetEyeForward().DotProduct( dir );
			float angles 	= DotToAngles( dot );

			if ( angles > 30 )
				continue;

			//do we have direct LOS?
			TArray<AActor> IgnoreActors;
			FHitResult LOSResult = LineTraceSingle( asPlayer.GetEyeLocation(), blueChest.GetActorLocation(), ETraceTypeQuery::WeaponBolt, true, IgnoreActors, true );
			if ( LOSResult.GetActor() != blueChest )
				continue;

			FGameplayTag dialogueTag = GameplayTags::Audio_VO_Training_GearUp_BlueFound;
			dialogueSys.PlayTrainingAnnouncerDialogue( dialogueTag, 0, 0.0 );	
			return;
		}
	}

	UFUNCTION()
	void Client_OpenWorld_PlayLookForBlueChestVODelayed( UNCCoroutine co )
	{
		co.Wait( 3.0 );

		if ( scriptCallbacks.Flag(Training::Flags::FLAG_OPENWORLD_BLUECHEST_OPEN) )
			return;		

		FGameplayTag dialogueTag = GameplayTags::Audio_VO_Training_GearUp_OpenRedFirst;
		dialogueSys.PlayTrainingAnnouncerDialogue( dialogueTag, 0, 0.0 );
	}



	bool client_hasPlayedGeneratorInfoLines = false;
	bool client_hasPlayedFinalGeneratorInfoLines = false;
	UFUNCTION()
	void Client_OpenWorld_NearGenerator( FName signalName, UObject sender )
	{
		if ( !client_hasPlayedGeneratorInfoLines && objectiveSys.ClientIsObjectiveActive( Training::Objectives::OPENWORLD_10_PLANTONGENERATOR ) )
		{
			FGameplayTag dialogueTag = GameplayTags::Audio_VO_Training_RaidBase_Generator_First;
			dialogueSys.PlayTrainingAnnouncerDialogue( dialogueTag, 0, 0.0 );

			client_hasPlayedGeneratorInfoLines = true;
		}

		if ( !client_hasPlayedFinalGeneratorInfoLines && objectiveSys.ClientIsObjectiveActive( Training::Objectives::OPENWORLD_11_PLANTFINALGENERATOR ) )
		{
			FGameplayTag dialogueTag = GameplayTags::Audio_VO_Training_RaidBase_BasePower_low;
			dialogueSys.PlayTrainingAnnouncerDialogue( dialogueTag, 0, 0.0 );

			client_hasPlayedFinalGeneratorInfoLines = true;
		}
	}

	bool client_hasPlayedAnchorStoneLine = false;
	UFUNCTION()
	void Client_OpenWorld_NearAnchorStone( FName signalName, UObject sender )
	{
		bool areCorrectObjectivesActive = objectiveSys.ClientIsObjectiveActive( Training::Objectives::OPENWORLD_10_PLANTONGENERATOR ) || objectiveSys.ClientIsObjectiveActive( Training::Objectives::OPENWORLD_11_PLANTFINALGENERATOR );
		if ( !client_hasPlayedAnchorStoneLine && areCorrectObjectivesActive )
		{
			FGameplayTag dialogueTag = GameplayTags::Audio_VO_Training_RaidBase_AnchorStone;
			dialogueSys.PlayTrainingAnnouncerDialogue( dialogueTag, 0, 0.0 );

			client_hasPlayedAnchorStoneLine = true;
		}
	}

	UFUNCTION()
	private void cl_OnRaidStarted(AAS_RaidEventManager_v2 eventManager)
	{
		UAS_RaidMessagingTrainingHud hud = GetTrainingRaidHud();
		if ( IsValid(hud))
			hud.EnableScoreBoard();
	}

	

	UFUNCTION()
	private void cl_OnEnemyDomeFlagChanged(int oldValue, int newValue)
	{
		//enemy dome broken? then show raid objectives.
		if ( Bitflags::HasFlag( newValue, DomeFlags::DF_BROKEN ) )
		{
			int enemyTeam = GetOtherTeam( Client_GetLocalASPawn().GetTeam() );
			Client_SetBaseObjectiveIconVisibility( enemyTeam, true );
		}
	}

	void Client_SetBaseObjectiveIconVisibility( int team, bool isVisible )
	{
		TArray<UAS_BaseObjectiveWidgetVisibilityManager> WVMs;

		TArray<AAS_BaseSubObjective> generators;
		GetAllActorsOfClass( generators );
		for( AAS_BaseSubObjective generator : generators )
		{
			if ( generator.GetTeam()== team )
				WVMs.Add( generator.widgetVisibilityManager );
		}

		TArray<AAS_BaseVault> baseVaults;
		GetAllActorsOfClass( baseVaults );
		for( AAS_BaseVault vault : baseVaults )
		{
			if ( vault.GetTeam()== team )
				WVMs.Add( vault.widgetVisibilityManager );
		}

		for( UAS_BaseObjectiveWidgetVisibilityManager WVM : WVMs )
		{
			if ( isVisible )
				WVM.cl_EnableWaypointIcon();
			else
				WVM.cl_DisableWaypointIcon();
		}
	}


	UFUNCTION()
	void OnRaidObjectiveStateChanged( int team, int oldValue, int newValue )
	{
		EObjectiveState newState = EObjectiveState( newValue );
		if ( newState == EObjectiveState::PickupShieldBreaker )
		{
			scriptCallbacks.FlagSet( Training::Flags::FLAG_OPENWORLD_SHIELDBREAKER_READY );
		}
	}

	int client_oldDisabledTrainingSlotState = MAX_int32;
	UFUNCTION()
	void OnTrainingDisabledWeaponSlotsChanged( int oldValue, int newValue )
	{
		AAS_PlayerEntity asPlayer = Client_GetLocalASPawn();
		if ( !IsValid( asPlayer ) )
		{
			return;
		}

		if ( IsServer() || ( IsClient() && asPlayer.IsLocallyControlled() ) )
		{
			const int currentValues = net_trainingDisabledWeaponSlots;
			const int difference	= currentValues ^ client_oldDisabledTrainingSlotState;
			const int numWeaponInventorySlots = int( EWeaponInventorySlot::Count );

			for ( int i = 0; i < numWeaponInventorySlots; i++ )
			{
				const int bitCheck = 1 << i;
				if ( Bitflags::HasFlag( difference, bitCheck ) )
				{
					bool enabled = Bitflags::HasFlag( currentValues, bitCheck );
					// Casting a bool to an int yields undefined values
					int enabledInt = enabled ? 1 : 0;
					ScriptCallbacks().client_onTrainingWeaponSlotEnabledChanged.Broadcast( asPlayer, i, client_oldDisabledTrainingSlotState & bitCheck, enabledInt );
				}
			}

			client_oldDisabledTrainingSlotState = currentValues;
		}
	}

	UFUNCTION()
	private void OnDialogueEndedOrRemovedFromQueue(UAS_DialogueQueueEntry queue)
	{
		FName VO_END_FLAG = GetVOEndedFlag( queue.dialogueAsset.index );
		scriptCallbacks.RegisterFlagSafe( VO_END_FLAG );
		scriptCallbacks.FlagSet( VO_END_FLAG );

		FName VO_START_FLAG = GetVOStartedFlag( queue.dialogueAsset.index );
		scriptCallbacks.RegisterFlagSafe( VO_START_FLAG );
		scriptCallbacks.FlagClear( VO_START_FLAG );
	}

	UFUNCTION()
	private void OnDialogueStarted(UAS_DialogueQueueEntry queue)
	{
		FName VO_START_FLAG = GetVOStartedFlag( queue.dialogueAsset.index );
		scriptCallbacks.RegisterFlagSafe( VO_START_FLAG );
		scriptCallbacks.FlagSet( VO_START_FLAG );

		FName VO_END_FLAG = GetVOEndedFlag( queue.dialogueAsset.index );
		scriptCallbacks.RegisterFlagSafe( VO_END_FLAG );
		scriptCallbacks.FlagClear( VO_END_FLAG );
	}

	private void SendEventToServerWhenDialogueComplete( FGameplayTag dialogueIndex )
	{
		Thread( this, n"SendEventToServerWhenDialogueCompleteThread", dialogueIndex );
	}

	UFUNCTION()
	private void SendEventToServerWhenDialogueCompleteThread( UNCCoroutine co, FGameplayTag dialogueIndex )
	{
		FName VO_FLAG = GetVOEndedFlag( dialogueIndex );
		scriptCallbacks.RegisterFlagSafe( VO_FLAG );

		co.FlagWaitTimeout( VO_FLAG, 60.0 );
		
		UNCTrainingDialogueEndedEvent netEvent = Cast<UNCTrainingDialogueEndedEvent>( NewObject( GetCurrentWorld(), UNCTrainingDialogueEndedEvent::StaticClass() ) );
		netEvent.net_dialogueTag.SetNetValue( dialogueIndex );
		netEvent.SendToServer();
	}

	void CleanUp_Client(AAS_PlayerEntity asPlayer) override
	{
		Super::CleanUp_Client(asPlayer);
		sharedData.trigger_intro_basicControls.onPlayerEntered.Unbind( this, n"Trigger_Intro_BasicControls" );
		sharedData.trigger_combat_enteredRoom.onPlayerEntered.Unbind( this, n"Trigger_Combat_EnteredRoom_Client" );
		sharedData.trigger_combat_onRarityPlatform.onPlayerEntered.Unbind( this, n"Trigger_Combat_OnEnterRarityPlatform_Client" );
		sharedData.trigger_combat_onArmorPlatform.onPlayerEntered.Unbind( this, n"Trigger_Combat_OnEnterArmorPlatform_Client" );
		sharedData.trigger_destruction_enteredRoom.onPlayerEntered.Unbind( this, n"Trigger_Destruction_OnEnteredRoom_Client" );
		sharedData.trigger_destruction_moveVendorToDestruction.onPlayerEntered.Unbind( this, n"Trigger_Destruction_MoveVendorToDestruction_Client" );
		sharedData.trigger_destruction_vendorGreet.onPlayerEntered.Unbind( this, n"Trigger_Destruction_VendorGreet_Client" );
		sharedData.trigger_abilities_enteredRoom.onPlayerEntered.Unbind( this, n"Trigger_Abilities_OnEnteredRoom_Client" );
		sharedData.trigger_abilities_afterAbilitiesGate.onPlayerEntered.Unbind( this, n"Trigger_Abilities_AfterAbilitiesGate_Client" );
		sharedData.respawnInBaseTrigger.onPlayerEntered.Unbind( this, n"Trigger_OpenWorld_TeleportToBase_Client" );
		sharedData.trigger_openworld_teleportedToBase.onPlayerEntered.Unbind( this, n"Trigger_OpenWorld_OnTeleportedToBase_Client" );
		sharedData.trigger_zeppelin_announcerLine.onPlayerEntered.Unbind( this, n"Trigger_Zeppelin_AnnouncerLine_Client" );

		scriptCallbacks.UnRegisterSignalCallback( n"Trigger_Intro_ShowSprintHint", this, n"OnTrigger_Intro_ShowSprintHint" );
		scriptCallbacks.UnRegisterSignalCallback( n"Trigger_Intro_HideSprintHint", this, n"OnTrigger_Intro_HideSprintHint" );

		scriptCallbacks.UnRegisterSignalCallback( n"Trigger_Intro_ShowCrouchHint", this, n"OnTrigger_Intro_ShowCrouchHint" );
		scriptCallbacks.UnRegisterSignalCallback( n"Trigger_Intro_HideCrouchHint", this, n"OnTrigger_Intro_HideCrouchHint" );

		scriptCallbacks.UnRegisterSignalCallback( n"Trigger_Intro_ShowJumpHint", this, n"OnTrigger_Intro_ShowJumpHint" );
		scriptCallbacks.UnRegisterSignalCallback( n"Trigger_Intro_HideJumpHint", this, n"OnTrigger_Intro_HideJumpHint" );

		scriptCallbacks.UnRegisterSignalCallback( n"OpenWorld_NearGenerator" , this, n"Client_OpenWorld_NearGenerator" );
		scriptCallbacks.UnRegisterSignalCallback( n"OpenWorld_NearAnchorStone", this, n"Client_OpenWorld_NearAnchorStone" );
		scriptCallbacks.shared_OnRaidStarted.Unbind( this, n"cl_OnRaidStarted" );

		asPlayer.client_OnVendorMenuOpen.Unbind(this, n"cl_OnVendorMenuOpen");
		asPlayer.client_OnVendorMenuClose.Unbind(this, n"cl_OnVendorMenuClose");
	}

	UFUNCTION()
	void client_UpdateTrainingProgressThread( UNCCoroutine co )
	{
		UAS_RaidMessagingTrainingHud trainingHud = GetTrainingRaidHud();
		ScriptAssert( IsValid(trainingHud), "wtf?" );

		TArray<FName> allObjectiveFlags;

		/////////////////////////////////////////////////////////////////////////////////////////////////////////////
		// 	@DAVIS -> The script below divides the objective groups into 2 halves ( before and after the blimp ). 
		// 	Objectives are weighted equally within their respected half, but differently between the two haves.
		//	This way adding/removing objectives doesn't change the halfway point.
		//
		//	The percentage complete is always set to the furthest completed objective... it doesn't care what 
		//	previous objectives are/are not completed. This way it works in dev, and if a bug happens with an 
		//	earlier objective, the script can course correct but just completeing a later one.		
		//
		//	Feel free to add more objectives for granularity. I comment out objectives/hints based on what feels right
		
		//allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::INTRO_01_BASICCONTROLS) );
		//allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::INTRO_HINT_SPRINT) );
		//allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::INTRO_HINT_JUMP) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::INTRO_HINT_CROUCH) );
		//allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::INTRO_HINT_MANTLE) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::INTRO_04_GOTOCOMBAT) );
		//allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::COMBAT_HINT_GOTOFIRSTPLATFORM) );
		//allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::COMBAT_HINT_RETURNTOFIRSTPLATFORM) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::COMBAT_01_DAMAGEDUMMIES) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::COMBAT_02_DAMAGEDUMMIES_WITHSCOPE) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::COMBAT_03_DAMAGEDUMMIES_WITHGRENADE) );
		//allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::COMBAT_HINT_GOTORARITYPLATFORM) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::COMBAT_04_WEAPONRARITY) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::Combat_05_GOTOARMORPLATFORM) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::COMBAT_06_REVIVEFRIENDLY) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::COMBAT_07_FALLDAMAGE) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::COMBAT_08_GOTODESTRUCTION) );
		//allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::DESTRUCTION_HINT_BREAKWALLS) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::DESTRUCTION_01_TALKTOVENDOR) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::DESTRUCTION_02_HARVESTORE) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::DESTRUCTION_03_BUYROCKET) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::DESTRUCTION_04_DESTROYWALLS) );
		//allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::DESTRUCTION_HINT_BUYMOREROCKETS) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::DESTRUCTION_05_DESTROYREINFORCEDWALL) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::DESTRUCTION_06_HARVESTORE) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::DESTRUCTION_07_BUYMULTILOCK) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::DESTRUCTION_08_DESTROYWALLS_02) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::DESTRUCTION_HINT_BUYMOREROCKETS_02) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::DESTRUCTION_09_GOTOABILITIES) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::ABILITIES_01_BREAKWALLS_TAC) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::ABILITIES_02_LEAP_TAC) );
		//allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::ABILITIES_HINT_RECHARGE) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::ABILITIES_03_BREAKWALLS_ULT) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::ABILITIES_04_GOTOZEPPELIN) );
		
		/////////////////////////////////////////////////////////////////////////////////////////////////////////////
		//	HALFWAY POINT
		int halfwayIndex = allObjectiveFlags.Num();
		
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::OPENWORLD_01_INTRO) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::OPENWORLD_02_REINFORCEWALLS) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::OPENWORLD_03_GETONMOUNT) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::OPENWORLD_04_GEARUP) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::OPENWORLD_05_GETMOREARMOR) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::OPENWORLD_06_OPENINVENTORY) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::OPENWORLD_07_BUYARMOR) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::OPENWORLD_08_GETSHIELDBREAKER) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::OPENWORLD_09_STARTRAID) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::OPENWORLD_10_PLANTONGENERATOR) );
		allObjectiveFlags.Add(Objectives().GetObjectiveCompleteFlag(Training::Objectives::OPENWORLD_11_PLANTFINALGENERATOR) );

		for ( FName flag : allObjectiveFlags )
			scriptCallbacks.RegisterFlag(flag);

		//score two haves differently	
		float32 fracPerFirstHalf 	= 0.5f / ( halfwayIndex );
		float32 fracPerSecondHalf 	= 0.5f / ( allObjectiveFlags.Num() - halfwayIndex );

		int furthestCompletedIndex 	= 0; 
		TArray<FName> waitFlags;
		waitFlags.Append( allObjectiveFlags );

		while( waitFlags.Num() > 0 )
		{	
			//wait on any remaining flags
			for ( FName flag : waitFlags )
				co.AddFlagWait( flag );

			co.AwaitAny();
			
			//	grab the furthest completed objective. We do it this way so in case we skip 
			//	an objective, we don't get hung up on on waiting for it. Also makes skipping 
			//	around the map in dev work.
			for ( int i = 0; i < allObjectiveFlags.Num(); i++ )
			{
				FName flag = allObjectiveFlags[i];
				if ( !scriptCallbacks.Flag(flag) )
					continue;

				if ( i > furthestCompletedIndex )
					furthestCompletedIndex = i;
			}

			//	remove all the flags from 0 -> furthestCompletedIndex from the waitArray
			for ( int i = 0; i <= furthestCompletedIndex; i++ )
			{
				FName flag = allObjectiveFlags[i];
				if ( waitFlags.Contains(flag) )
					waitFlags.Remove(flag);
			}

			//score two haves differently
			int numCompletedTotal		= furthestCompletedIndex + 1;
			int numCompletedFirstHalf 	= Math::Min( numCompletedTotal, halfwayIndex );
			int numCompletedSecondHalf 	= Math::Max( numCompletedTotal - halfwayIndex, 0 );

			float32 progressFrac = ( numCompletedFirstHalf * fracPerFirstHalf ) + ( numCompletedSecondHalf * fracPerSecondHalf );
			trainingHud.SetTrainingProgressBarSafe(progressFrac);
		}

		trainingHud.SetTrainingProgressBarSafe(1.0);		
	}
}

FName GetVOStartedFlag(FGameplayTag tag)
{
	return FName( f"FLAG_VoStarted_{tag.ToString()}");		
}

FName GetVOEndedFlag(FGameplayTag tag)
{
	return FName( f"FLAG_VoEnded_{tag.ToString()}");		
}

// Used to create a reference facing angle for if player's turned around and looking roughly at dummies
UCLASS()
class AAS_ArrowActor : ANCDefaultActor
{
	UPROPERTY( DefaultComponent, RootComponent )
	UArrowComponent arrowComponent;

	UPROPERTY( DefaultComponent )
	UBillboardComponent billboard;

	FVector GetForward() const
	{
		return arrowComponent.GetForwardVector();
	}
}