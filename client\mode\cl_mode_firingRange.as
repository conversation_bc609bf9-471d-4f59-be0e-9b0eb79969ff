UAS_ClientScript_FiringRange ClientFiringRangeMode()
{
	return Cast<UAS_ClientScript_FiringRange>( UNCGameplaySystemsSubsystem::Get_ModeClientScript( GetCurrentWorld() ) );
}

UCLASS( Abstract )
class UAS_ClientScript_FiringRange : UAS_ClientScript_RaidMode
{
	UPROPERTY( EditDefaultsOnly, BlueprintHidden )
	private UTexture transitionBink;

	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<UAS_MatchIntroTransitionWidget> transitionWidgetClass;

	UAS_GameStateWidget_FiringRange frWidget;
	UAS_MatchIntroTransitionWidget transitionWidget;

	UFUNCTION( BlueprintOverride )
	void Initialize()
	{
		Super::Initialize();
	}

	void OnHudInit( ANC_HUD hud ) override
	{
		Super::OnHudInit( hud );

		GetGameStateEntity_FiringRange().net_friendlyFireEnabled.OnReplicated().AddUFunction( this, n"OnFriendlyFireChanged" );

		ClientCallbacks().OnGamePhaseChanged.AddUFunction( this, n"OnGamephaseChanged" );
	}

	void OnGamephaseChanged( int oldPhase, int newPhase ) override
	{
		Super::OnGamephaseChanged( oldPhase, newPhase );

		if ( newPhase == GamePhase::PREMATCH )
		{
			// These are set to lazy load (don't initialize when brought into memory) so we need to explicitly start them with this function
			ScreenFade().ScreenFadeFromColorToColor( FLinearColor( 1, 1, 1, 1 ), FLinearColor( 1, 1, 1, 0 ), 2.0 );
			transitionWidget = Cast<UAS_MatchIntroTransitionWidget>( WidgetBlueprint::CreateWidget( transitionWidgetClass, HUD.OwningPlayerController ) );
			UNCUtils::StartBinkTexturePlay( transitionBink );
			UNCUtils::RewindBinkTexture( transitionBink );
			transitionWidget.transitionVideo.SetBrushResourceObject( transitionBink );
			transitionWidget.transitionVideo.SetVisibility( ESlateVisibility::HitTestInvisible );
			transitionWidget.AddToViewport( GameConst::ZORDER_SCREEN_FADE + 1 );
			Thread( this, n"CleanupTransitionThread", 2.0f );
		}
	}

	UFUNCTION()
	void CleanupTransitionThread( UNCCoroutine co, float32 delay )
	{
		co.Wait( delay );
		float32 fadeIn = 1.0;
		transitionWidget.RemoveFromParent();
		transitionWidget.AddToViewport( GameConst::ZORDER_SCREEN_FADE - 1 );
		ScreenFade().ScreenFadeFromColorToColor( FLinearColor( 1, 1, 1, 0 ), FLinearColor( 1, 1, 1, 1 ), fadeIn );
		co.Wait( fadeIn );
		ScreenFade().ScreenFadeFromColorToColor( FLinearColor( 1, 1, 1, 1 ), FLinearColor( 1, 1, 1, 0 ), 3.0 );
		transitionWidget.RemoveFromParent();
	}

	UFUNCTION()
	private void OnFriendlyFireChanged( bool oldValue, bool newValue )
	{
		if ( IsValid( frWidget ) )
			frWidget.friendlyFireText.SetText( newValue ? GetLocalizedText( Localization::FiringRange, "friendlyfire_is_on" ) : GetLocalizedText( Localization::FiringRange, "friendlyfire_is_off" ) );
	}

	bool ShouldEnableLoadouts() override
	{
		return true;
	}

	void OnInGameMenuOpened( UAS_InGameMenu menu ) override
	{
		menu.divider.SetVisibility( ESlateVisibility::HitTestInvisible );
		menu.CreateSettingsButton( GetLocalizedText( Localization::FiringRange, "change_character" ), FCommonButtonBaseClickedDelegate( this, n"OnChangeCharacterClicked" ) );
		menu.CreateSettingsButton( GetLocalizedText( Localization::FiringRange, "change_loadout" ), FCommonButtonBaseClickedDelegate( this, n"OnChangeLoadoutClicked" ) );
		menu.CreateSettingsButton( GetLocalizedText( Localization::FiringRange, GetGameStateEntity_FiringRange().net_friendlyFireEnabled ? "toggle_friendlyfire_off" : "toggle_friendlyfire_on" ), FCommonButtonBaseClickedDelegate( this, n"OnFriendlyFireToggleClicked" ) );
		menu.CreateSettingsButton( GetLocalizedText( Localization::FiringRange, "refill_ammo" ), FCommonButtonBaseClickedDelegate( this, n"OnRefillAmmoClicked" ) );
		menu.CreateSettingsButton( GetLocalizedText( Localization::FiringRange, "repair_walls" ), FCommonButtonBaseClickedDelegate( this, n"OnRepairWallsClicked" ) );
	}

	UFUNCTION()
	private void OnChangeCharacterClicked( UCommonButtonBase button )
	{
		GetASUIManager().CloseActiveScreen();
		GetLocalHUD().OpenMenu( n"ClassSelectMenu" );
	}

	UFUNCTION()
	private void OnChangeLoadoutClicked( UCommonButtonBase button )
	{
		GetASUIManager().CloseActiveScreen();
		GetLocalHUD().ToggleLoadoutMenu();
	}

	UFUNCTION()
	private void OnFriendlyFireToggleClicked( UCommonButtonBase button )
	{
		GetASUIManager().CloseActiveScreen();
		UNC_FR_FriendlyFireRequestEvent netEvent = Cast<UNC_FR_FriendlyFireRequestEvent>( NewObject( GetCurrentWorld(), UNC_FR_FriendlyFireRequestEvent::StaticClass() ) );
		netEvent.net_desiredFFState.SetNetValue( !GetGameStateEntity_FiringRange().net_friendlyFireEnabled );
		netEvent.SendToServer();
	}

	UFUNCTION()
	private void OnRefillAmmoClicked( UCommonButtonBase button )
	{
		GetASUIManager().CloseActiveScreen();
		UNC_FR_RefillAmmoEvent netEvent = Cast<UNC_FR_RefillAmmoEvent>( NewObject( GetCurrentWorld(), UNC_FR_RefillAmmoEvent::StaticClass() ) );
		netEvent.SendToServer();
	}

	UFUNCTION()
	private void OnRepairWallsClicked( UCommonButtonBase button )
	{
		GetASUIManager().CloseActiveScreen();
		UNC_FR_RepairWallsEvent netEvent = Cast<UNC_FR_RepairWallsEvent>( NewObject( GetCurrentWorld(), UNC_FR_RepairWallsEvent::StaticClass() ) );
		netEvent.SendToServer();
	}
}

UCLASS()
class UNC_FR_RepairWallsEvent : UNCNetClientToServerEvent
{
	UFUNCTION( BlueprintOverride )
	void OnEventReceived( ANCNetPlayerController receiver )
	{
		UAS_ServerScript_Mode_FiringRange mode = Cast<UAS_ServerScript_Mode_FiringRange>( GetServerScript() );
		if ( !IsValid( mode ) )
			return;

		mode.RepairAllWalls();
	}
}

UCLASS()
class UNC_FR_RefillAmmoEvent : UNCNetClientToServerEvent
{
	UFUNCTION( BlueprintOverride )
	void OnEventReceived( ANCNetPlayerController receiver )
	{
		UAS_ServerScript_Mode_FiringRange mode = Cast<UAS_ServerScript_Mode_FiringRange>( GetServerScript() );
		if ( !IsValid( mode ) )
			return;

		mode.RefillAmmoForPlayer( receiver.GetPlayer() );
	}
}

UCLASS()
class UNC_FR_FriendlyFireRequestEvent : UNCNetClientToServerEvent
{
	UPROPERTY()
	FNCNetBool net_desiredFFState;

	UFUNCTION( BlueprintOverride )
	void OnEventReceived( ANCNetPlayerController receiver )
	{
		UAS_ServerScript_Mode_FiringRange mode = Cast<UAS_ServerScript_Mode_FiringRange>( GetServerScript() );
		if ( !IsValid( mode ) )
			return;

		mode.ToggleFriendlyFire( net_desiredFFState );
	}
}

UCLASS( Abstract )
class UAS_GameStateWidget_FiringRange : UAS_GameStateWidget
{
	UPROPERTY( NotEditable, BindWidget )
	UAS_CommonTextBlock friendlyFireText;

	UPROPERTY( NotEditable, BindWidget )
	UImage transitionVideo;
}