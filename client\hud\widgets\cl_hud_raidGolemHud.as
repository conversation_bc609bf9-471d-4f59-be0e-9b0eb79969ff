UCLASS( Abstract )
class UAS_RaidGolemHudWidget : UUserWidgetDefault
{
	AAS_JadeDemonInfo data;

	UPROPERTY( NotEditable, BindWidget )
	UImage respawnProgress_left;

	UPROPERTY( NotEditable, BindWidget )
	UImage respawnProgress_right;

	UPROPERTY( NotEditable, BindWidget )
	UAS_CommonTextBlock timerText;

	UPROPERTY( NotEditable, BindWidget )
	UAS_CommonTextBlock shieldPercentText;

	UPROPERTY( NotEditable, BindWidget )
	UProgressBar shieldBar;

	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UAS_PlayerWidget localPlayerStatus;

	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UAS_TeammateStatusContainer teammateStatusContainerWidget;

	float32 duration;
	int endTime;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		shieldBar.SetVisibility( ESlateVisibility::Collapsed );

		if ( IsValid( localPlayerStatus ) )
			localPlayerStatus.SetPlayer( Client_GetLocalASPawn() );
	}

	void Init( AAS_JadeDemonInfo inData, float32 inDuration )
	{
		duration = inDuration;
		data	 = inData;

		data.net_controlEndTime.OnReplicated().AddUFunction( this, n"OnEndTimeChanged" );
		data.net_golemShield.OnReplicated().AddUFunction( this, n"OnGolemShieldObjectChanged" );
		OnEndTimeChanged( 0, data.net_controlEndTime );
		OnGolemShieldObjectChanged( nullptr, data.net_golemShield.GetEntity() );
	}

	UFUNCTION()
	private void OnEndTimeChanged( int oldValue, int newValue )
	{
		endTime = newValue;
	}

	UFUNCTION()
	private void OnGolemShieldObjectChanged(const AActor oldValue, const AActor newValue)
	{
		AAS_RaidGolemShield oldShield = Cast<AAS_RaidGolemShield>(newValue);
		if (IsValid(oldShield))
		{
			oldShield.net_savedShieldHealthFrac.OnReplicated().Unbind( this, n"OnShieldFracChanged" );
		}

		AAS_RaidGolemShield shield = Cast<AAS_RaidGolemShield>(newValue);
		if (IsValid(shield))
		{
			shield.net_savedShieldHealthFrac.OnReplicated().AddUFunction( this, n"OnShieldFracChanged" );
			OnShieldFracChanged( 0, shield.net_savedShieldHealthFrac );
		}
	}

	UFUNCTION()
	private void OnShieldFracChanged( float32 oldValue, float32 newValue )
	{
		shieldBar.SetPercent( newValue );
		shieldPercentText.SetText( GetLocalizedText( Localization::Core, "percent", FFormatArgumentValue( FText::AsNumber( newValue * 100.0, GetNumberFormattingOptions( ERoundingMode::FromZero, false, true, 0, 0 ) ) ) ) );
		if ( newValue <= 0 )
		{

		}
		else
		{
			
		}
	}

	UFUNCTION( BlueprintOverride )
	void Tick( FGeometry MyGeometry, float InDeltaTime )
	{
		int startTime = endTime - TO_MILLISECONDS( duration );
		int timeLeft  = endTime - GetTimeMilliseconds();
		if ( timeLeft >= 0 )
		{
			float fill	  = Math::GetMappedRangeValueClamped( FVector2D( startTime, endTime ), FVector2D( 0.0f, 1.0f ), GetTimeMilliseconds() );
			respawnProgress_left.GetDynamicMaterial().SetScalarParameterValue( MaterialParameter::LERP_ALPHA, fill );
			respawnProgress_right.GetDynamicMaterial().SetScalarParameterValue( MaterialParameter::LERP_ALPHA, fill );
			timerText.SetText( GetFormattedCountdownTime( timeLeft ) );
		}
		else
		{
			timerText.SetText( FText() );
			respawnProgress_left.GetDynamicMaterial().SetScalarParameterValue( MaterialParameter::LERP_ALPHA, 0 );
			respawnProgress_right.GetDynamicMaterial().SetScalarParameterValue( MaterialParameter::LERP_ALPHA, 0 );
		}
	}
}