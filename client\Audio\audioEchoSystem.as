struct FThreatData
{
	AAS_PlayerEntity player;
	float32 threatValue;
	float32 distance;
};

UCLASS( Abstract )
class UAudioEchoSystem : UNCGameplaySystem_Client
{
	// Wwise RTPC
	UPROPERTY( EditDefaultsOnly )
	const UAkRtpc RTPC_EchoThreat;

	UPROPERTY( EditDefaultsOnly )
	const UAkRtpc RTPC_EchoVerticalContext;

	private ANCPlayerCharacter localPlayer;

	private float verticalDistance = 300.0f;

	UFUNCTION( BlueprintOverride )
	void BeginPlay()
	{
		if ( !GetCvarBool( f"ScriptDebug.ECHOThreatSystemEnabled" ) )
			return;
		localPlayer = Client_GetLocalPawn();
		System::SetTimer( this, n"Tick", .1, false );
	}
	UFUNCTION()
	void Tick()
	{
		if ( !IsValid( localPlayer ) )
			return;

		TArray<FThreatData> sortedThreats;

		FVector playerLocation	  = localPlayer.GetActorLocation();
		float32 maxThreatDistance = 12000.0f; // Define maximum threat range

		// Calculate threat scores for all players
		for ( AAS_PlayerEntity player : GetAllPlayers() )
		{
			if ( !IsAlive( player ) || IsFriendly( player.GetTeam(), localPlayer.GetTeam() ) )
				continue;

			// Calculate distance
			float32 dist = Distance( playerLocation, player.GetActorLocation() );

			// Skip if beyond the maximum threat range
			if ( dist > maxThreatDistance )
				continue;

			// Calculate the ECHO threat value
			float32 calculatedScore = CalculateEchoThreat( player, playerLocation, dist );

			// Ignore players with 0 threat score (out of range)
			if ( calculatedScore == 0 )
				continue;

			// Add to array
			FThreatData data;
			data.player		 = player;
			data.threatValue = calculatedScore;
			data.distance	 = dist;

			bool added = false;
			for ( int i = 0; i < sortedThreats.Num(); i++ )
			{
				FThreatData threat = sortedThreats[i];
				if ( threat.threatValue < data.threatValue )
				{
					added = true;
					sortedThreats.Insert( data, i );
					break;
				}
			}

			if ( !added )
				sortedThreats.Add( data );
		}

		// DEBUG: Print Threats Before Sorting
		// for ( FThreatData data : sortedThreats )
		// {
		// 	Print( f"Threat Value (Before Sorting): {data.player} {data.threatValue} " );
		// }

		// apply RTPC values
		ApplyRTPC( sortedThreats );
		System::SetTimer( this, n"Tick", .1, false );
	}

	float32 CalculateEchoThreat( AAS_PlayerEntity player, FVector playerLocation, float32 dist )
	{
		// Calculate distance to player
		float32 clampedDist = 10000.0f; // Maximum threat range

		// Ignore enemies out of range
		if ( dist >= clampedDist )
			return 0; // Out of range enemies have no threat value

		// Health Value Calculation
		float32 healthScore = player.GetHealth() > localPlayer.GetHealth() ? 20 : 10;

		// Shield Value Calculation
		float32 shieldScore = player.GetShieldHealth() > localPlayer.GetShieldHealth() ? 20 : 10;

		// Shield Breaker In Possession
		float32 hasShieldBreakerScore = PlayerHasShieldBreaker( player ) ? 20 : 0;

		// Weapon Tier
		float32 weaponScore	   = 0;
		float32 longRangeBonus = 0;
		ANCWeapon weapon	   = player.GetActiveWeapon();
		if ( IsValid( weapon ) )
		{
			FName weaponName = weapon.GetWeaponClass();
			longRangeBonus	 = ( weaponName == n"Weap_DNK" || weaponName == n"Weap_Scout" ) ? 20 : 0;
			weaponScore		 = float32( GetColorCurveIndexForRarity( GetWeaponRarity( weapon ) ) ) / 4 * 20;
		}

		// Proximity Score
		float32 proximityScore = ( 1 - ( dist / clampedDist ) ) * 40; // Weighted more heavily

		// Enemy Targeting Player
		float32 targetingScore = 0;
		FVector eyeLocation	   = player.GetEyeLocation();
		TArray<AActor> ignoreActors;
		ignoreActors.Add( player );

		FVector forwardDir = player.GetEyeForward();
		FVector toPlayer   = ( playerLocation - eyeLocation ).GetSafeNormal();
		float aimAngle	   = DotToAngles( forwardDir.DotProduct( toPlayer ) );
		if ( aimAngle < 10.0 )
		{
			FHitResult result = LineTraceSingle( eyeLocation, playerLocation, ETraceTypeQuery::WeaponFine, true, ignoreActors, false );
			if ( result.Actor == localPlayer )
				targetingScore = 20;
		}

		// DEBUG PRINTS //
		// Print( f"Health Score: {healthScore}" );
		// Print( f"Shield Score: {shieldScore}" );
		// Print( f"Weapon Score: {weaponScore}" );
		// Print( f"Proximity Score: {proximityScore}" );
		// Print( f"Targetting Player Score: {targetingScore}" );
		// Print( f"LongRange Weapon: {longRangeBonus}" );
		// Print( f"Enemy Player: {player}" );
		// Print( f"Aim Angle in Degrees: {aimAngle}" );
		// Print( f"Shield Breaker in Possesion: {hasShieldBreakerScore}" );

		// Return final threat value
		return healthScore + shieldScore + weaponScore + proximityScore + targetingScore + longRangeBonus + hasShieldBreakerScore;
	}

	int CalculateVerticalContext( AAS_PlayerEntity enemy, float32 distance )
	{
		if ( distance > 6500.0f )
			return 0;

		FVector enemyLoc = enemy.GetActorLocation();
		FVector localLoc = localPlayer.GetActorLocation();
		float deltaZ	 = enemyLoc.Z - localLoc.Z;

		if ( Math::Abs( deltaZ ) < verticalDistance )
			return 0;

		FVector playerEye = localPlayer.GetEyeLocation();
		FVector enemyEye  = enemy.GetEyeLocation();

		TArray<AActor> ignored;
		ignored.Add( localPlayer );
		ignored.Add( enemy );

		FHitResult traceEnemyToPlayer = LineTraceSingle( enemyEye, playerEye, ETraceTypeQuery::Visibility, true, ignored, false );
		if ( !traceEnemyToPlayer.bBlockingHit )
			return 0;

		int verticalLevel = 0;
		if ( deltaZ > verticalDistance * 4 )
			verticalLevel = 2;
		else if ( deltaZ > verticalDistance )
			verticalLevel = 1;
		else if ( deltaZ < -verticalDistance * 4 )
			verticalLevel = -2;
		else if ( deltaZ < -verticalDistance )
			verticalLevel = -1;

		return verticalLevel;
	}

	void ApplyRTPC( TArray<FThreatData>& sortedThreats )
	{
		// Maintain a set of processed players
		TSet<AAS_PlayerEntity> processedPlayers;
		// Apply RTPC logic to the top threats
		int count = 0;
		for ( FThreatData data : sortedThreats )
		{
			processedPlayers.Add( data.player );
			float echoThreatValue = 0.0f;

			// Debug
			bool drawDebug				= GetCvarBool( f"ScriptDebug.ECHOThreatDebugEnabled" );
			FLinearColor debugDrawColor = FLinearColor::Red;
			FString debugString			= "";

			if ( count == 0 )
			{
				// Highest threat
				echoThreatValue = 100.0f;
				if ( drawDebug )
				{
					debugString	   = "HIGH Threat";
					debugDrawColor = FLinearColor::Red;
				}
			}
			else if ( count == 1 )
			{
				// Second highest
				echoThreatValue = 60.0f;
				if ( drawDebug )
				{
					debugString	   = "MED Threat";
					debugDrawColor = FLinearColor::Yellow;
				}
			}
			else if ( count == 2 )
			{
				// Third highest
				echoThreatValue = 40.0f;
				if ( drawDebug )
				{
					debugString	   = "Low Threat";
					debugDrawColor = FLinearColor::Blue;
				}
			}
			else
			{
				// Lower Threats
				echoThreatValue = 20.0f;
			}

			ANCVehicle playerMount = Mount::GetPilotedMount( data.player );
			bool isRidingMount	   = data.player.IsPlayerRidingMount() && IsValid( playerMount );
			float currEchoValue	   = Client_GetRTPCValue( data.player, RTPC_EchoThreat );
			if ( currEchoValue != echoThreatValue )
			{
				Client_SetRTPCValue( data.player, RTPC_EchoThreat, echoThreatValue, 500 );
			}

			// VerticalContext CVAR
			bool verticalContextEnabled = GetCvarBool( f"ScriptDebug.ECHOThreatVerticalContextEnabled" );

			float verticalRTPC = 0.0f;
			if ( verticalContextEnabled )
			{
				int verticalContext = CalculateVerticalContext( data.player, data.distance );
				switch ( verticalContext )
				{
					case 2:
						verticalRTPC = 100.0f;
						break; // way above
					case 1:
						verticalRTPC = 50.0f;
						break; // slightly above
					case -1:
						verticalRTPC = -50.0f;
						break; // slightly below
					case -2:
						verticalRTPC = -100.0f;
						break; // way below
					default:
						verticalRTPC = 0.0f;
						break; // same level or visible
				}
			}

			Client_SetRTPCValue( data.player, RTPC_EchoVerticalContext, verticalRTPC, 50 );

			// MOUNTS - ECHOthreat + VerticalContext
			if ( isRidingMount )
			{
				float currMountEchoValue			= Client_GetRTPCValue( playerMount, RTPC_EchoThreat );

				if ( currMountEchoValue != echoThreatValue )
				{
					Client_SetRTPCValue( playerMount, RTPC_EchoThreat, echoThreatValue, 50 );
				}
				Client_SetRTPCValue( playerMount, RTPC_EchoVerticalContext, verticalRTPC, 50 );

			}

			if ( drawDebug && count <= 2 )
			{
				DrawDebugSphere( data.player.GetActorLocation(), 75, .1, debugDrawColor, 2.0 );
				DrawDebugString( data.player.GetActorLocation() + FVector( 0, -50, 150 ), debugString, .1, debugDrawColor );
				DrawDebugString( data.player.GetActorLocation() + FVector( 0, -50, 125 ), f"{data.threatValue}", .1, debugDrawColor );
			}

			count++;
		}

		// Reset RTPC for players outside of max threat range
		for ( AAS_PlayerEntity player : GetAllPlayers() )
		{
			if ( processedPlayers.Contains( player ) )
			{
				continue;
			}
			// If not processed, reset to baseline
			float currEchoValue = Client_GetRTPCValue( player, RTPC_EchoThreat );
			if ( currEchoValue == 50.0f )
			{
				continue;
			}
			Client_SetRTPCValue( player, RTPC_EchoThreat, 50.0f, 500 );
		}
	}
};
