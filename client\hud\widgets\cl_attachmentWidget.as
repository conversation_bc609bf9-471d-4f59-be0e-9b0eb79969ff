UCLASS( Abstract )
class UAS_AttachmentWidget : UNCGenericListEntry
{
	UPROPERTY( NotVisible, Meta = ( BindWidget ) )
	protected UImage attachmentCircle;

	UPROPERTY( NotVisible, Meta = ( BindWidget ) )
	protected UImage attachmentFill;

	UPROPERTY( NotVisible, Meta = ( BindWidget ) )
	protected UImage attachmentImage;

	UPROPERTY( NotVisible, Meta = ( BindWidgetOptional ) )
	protected UTextBlock attachmentTitleText;

	UPROPERTY( NotVisible, Meta = ( BindWidgetOptional ) )
	protected UTextBlock attachmentDescText;

	UPROPERTY( EditDefaultsOnly )
	UObject attachmentSlotEmptyIcon;

	UPROPERTY( EditDefaultsOnly )
	UObject attachmentSlotFullIcon;

	UFUNCTION( BlueprintOverride )
	void OnDataSet( UObject DataObject )
	{
		UAS_AttachmentObject attachObject = Cast<UAS_AttachmentObject>( DataObject );
		SetAttachment( GetAttachmentData( attachObject.attachmentIndex ) );
	}

	void SetAttachment( FWeaponAttachmentData inAttachment )
	{
		FSlateBrush newBrush	= GetNewBrushThatIsCopyOf( attachmentCircle.Brush );
		newBrush.ResourceObject = attachmentSlotFullIcon;
		attachmentCircle.SetBrush( newBrush );

		SetWidgetVisibilitySafe(attachmentImage, ESlateVisibility::HitTestInvisible);
		newBrush				= GetNewBrushThatIsCopyOf( attachmentImage.Brush );
		newBrush.ResourceObject = inAttachment.icon;
		attachmentImage.SetBrush( newBrush );

		attachmentFill.SetBrushTintColor( FLinearColor( 1, 1, 1, 0.1 ) );

		if ( IsValid( attachmentTitleText ) )
		{
			SetWidgetVisibilitySafe(attachmentTitleText, ESlateVisibility::HitTestInvisible);
			attachmentTitleText.SetText( inAttachment.name );
		}
		if ( IsValid( attachmentDescText ) )
		{
			SetWidgetVisibilitySafe(attachmentDescText, ESlateVisibility::HitTestInvisible);
			attachmentDescText.SetText( inAttachment.desc );
		}
	}

	void ClearAttachment()
	{
		FSlateBrush newBrush	= GetNewBrushThatIsCopyOf( attachmentCircle.Brush );
		newBrush.ResourceObject = attachmentSlotEmptyIcon;
		attachmentCircle.SetBrush( newBrush );

		SetWidgetVisibilitySafe(attachmentImage, ESlateVisibility::Collapsed);

		attachmentFill.SetBrushTintColor( FLinearColor( 0, 0, 0, 0.1 ) );

		if ( IsValid( attachmentTitleText ) )
		{
			SetWidgetVisibilitySafe(attachmentTitleText,ESlateVisibility::Collapsed);
		}

		if ( IsValid( attachmentDescText ) )
		{
			SetWidgetVisibilitySafe(attachmentDescText, ESlateVisibility::Collapsed);
		}
	}

	// Remains set until another attachment is applied, or attachment is cleared
	void SetRarityColorOverride( FLinearColor rarityColor )
	{
		attachmentFill.SetBrushTintColor( rarityColor );
		SetWidgetVisibilitySafe(attachmentFill, ESlateVisibility::HitTestInvisible);
	}
}