USv_LootDistributionManager Sv_LootDistributionManager()
{
	return Cast<USv_LootDistributionManager>( UNCGameplaySystemsSubsystem::Get_ServerSystem(GetCurrentWorld(), USv_LootDistributionManager::StaticClass()) );
}

event void FOnLootLevelChanged( int lootLevel );

UCLASS(Abstract)
class USv_LootDistributionManager : UNCGameplaySystem_Server
{	
	UPROPERTY(NotEditable)
	TArray<AAS_LootZone> lootZones;

	UPROPERTY( EditDefaultsOnly, Meta = ( Category = "Loot Tables" ))
	UDataTable lootZonesDataTable;

	UPROPERTY( EditDefaultsOnly, Meta = ( Category = "Loot Tables" ))
	UDataTable lootZonesOverrideTable;

	TMap<FName, FLootZoneStruct> zoneData;

	int lootLevel = 0;
	FOnLootLevelChanged onLootLevelChanged;

	TArray<AActor> lootContainers;

	UFUNCTION(BlueprintOverride)
	void Initialize()
	{
		Init_LootZones();

		TArray< TSubclassOf<AActor> > lootContainerClasses;
		lootContainerClasses.Add( AAS_LootBarrel::StaticClass() );
		lootContainerClasses.Add( AAS_LootDuffelBag::StaticClass() );
		lootContainerClasses.Add( AAS_DisplayedLootChest::StaticClass() );
		lootContainerClasses.Add( AAS_BreakableObject::StaticClass() );
		lootContainerClasses.Add( AAS_ScriptDoor::StaticClass() );
		lootContainerClasses.Add( AAS_ScriptDoorBreakableObject::StaticClass() );

		TArray<AAS_LootSpawnMarker> nodes;
		GetAllActorsOfClass( AAS_LootSpawnMarker::StaticClass(), nodes );

		for ( auto actorClass : lootContainerClasses )
		{
			TArray<AActor> classActors;
			GetAllActorsOfClass( actorClass, classActors );
			lootContainers.Append(classActors);
		}
		
		GetAllActorsOfClass( AAS_LootZone::StaticClass(), lootZones );

		lootZones = SortZonesByRadius( lootZones );

		for ( AAS_LootZone zone : lootZones )
		{
			// DrawDebugSphere( zone.GetActorLocation(), zone.zoneRadius, 60, FLinearColor::Green );
			// DrawDebugString( zone.GetActorLocation(), f"{zone.zoneRadius}", 60, FLinearColor::Green );

			zone.Init();
			TArray<AAS_LootSpawnMarker> nodesSubset;
			for ( int i=nodes.Num()-1; i>=0; i-- )
			{
				if ( Distance2D( nodes[i].GetActorLocation(), zone.GetActorLocation() ) <= zone.zoneRadius )
				{
					nodesSubset.Add(nodes[i]);
					nodes.RemoveAt(i);
				}
			}

			for ( int i=lootContainers.Num()-1; i>=0; i-- )
			{
				if ( Distance2D( lootContainers[i].GetActorLocation(), zone.GetActorLocation() ) <= zone.zoneRadius )
				{
					zone.RegisterLootContainer( lootContainers[i] );
					lootContainers.RemoveAt(i);
				}
			}

			zone.RegisterLootNodes( nodesSubset );
			zone.SpawnLoot();
		}

		// Register any remaining loot containers for loot level changed (POI upgrade) since they are not in a loot zone. Loot zones handle this event
		for ( AActor lootContainer : lootContainers )
		{
			UAS_LootZoneInterfaceComponent lootZoneInterface = Cast<UAS_LootZoneInterfaceComponent>( lootContainer.GetComponentByClass( UAS_LootZoneInterfaceComponent::StaticClass() ) );
			lootZoneInterface.RegisterLootLevelChanged();
		}

		// leftovers get places in a big default zone
		AAS_LootZone defaultZone = Cast<AAS_LootZone>( SpawnActor( AAS_LootZone::StaticClass() ) );
		defaultZone.Init();
		defaultZone.RegisterLootNodes( nodes );
		defaultZone.SpawnLoot();

		lootZones.Add( defaultZone );
	}

	void Init_LootZones()
	{
		// Init base loot table
		_Init_LootZoneTable( lootZonesDataTable, false );

		// Apply any overrides to the table. This can modify base tables or add new ones
		if( IsValid( lootZonesOverrideTable ) )
			_Init_LootZoneTable( lootZonesOverrideTable, true );
	}

	private void _Init_LootZoneTable( const UDataTable& lootZonesDT, bool isOverride = false )
	{
		TArray<FLootZoneStruct> Rows;
		TArray<FName> rowNames = lootZonesDT.GetRowNames();

		lootZonesDT.GetAllRows( Rows );
		for ( int i = 0; i < rowNames.Num(); i++ )
		{
			FLootZoneStruct row = Rows[i];

			float sum = 0;
			for ( auto r : row.itemsToSpawn )
			{
				sum += r.probability;
			}
			row.probabilityTotal = sum;

			// Maybe unneccesary (FindOrAdd always works) but will at least error on existing if override is not set and Add hits a key
			if( isOverride )
				zoneData.FindOrAdd( rowNames[i] ) = row;
			else
				zoneData.Add( rowNames[i], row );
		}
	}

	UFUNCTION()
	void UpgradeLootZones()
	{
		onLootLevelChanged.Broadcast( ++lootLevel );
		
		Server_SendObituaryMessageToAllPlayers( Obituary::GAME_EVENT_LOOT_UPGRADE );
	}

	UFUNCTION()
	TArray<AActor> GetLootContainers()
	{
		return lootContainers;
	}

	void ReenableAllLoot()
	{
		for ( AActor lootContainer : lootContainers )
		{
			UAS_LootZoneInterfaceComponent lootZoneInterface = Cast<UAS_LootZoneInterfaceComponent>( lootContainer.GetComponentByClass( UAS_LootZoneInterfaceComponent::StaticClass() ) );
			lootZoneInterface.Reenable();
		}
	}

	UFUNCTION(BlueprintOverride)
	void ValidateData()
	{
		ValidateLootZones();
	}

	private void ValidateLootZones()
	{
		for ( TMapIterator<FName, FLootZoneStruct> lootZone : zoneData )
		{
			for ( FLootSpawnStruct lootSpawn : lootZone.Value.itemsToSpawn )
			{
				if ( IsLootIndexValid( lootSpawn.lootTag ) || lootSpawn.lootTag == GameplayTags::Loot_Blank )
					continue;

				if ( zoneData.Contains( lootSpawn.lootZone ) )
					continue;

				ScriptError( f"INVALID LOOT ZONE - {lootZone.Key} requires valid loot tag or loot zone - tag: {lootSpawn.lootTag} zone: {lootSpawn.lootZone}" );
				return;
			}
		}
	}
}