namespace PriorityMessageRaidReportTemplates
{
	bool GetTemplateMessageData( EPriorityMessageTemplate template, UCL_PriorityMessageManager_v2 manager, UAS_PriorityMessageData& data )
	{
		bool result = false;

		if ( IsValid( data ) && IsValid( manager ) )
		{
			// Start assuming that we will find the template
			result = true;

			switch ( template )
			{
				case EPriorityMessageTemplate::V2_ATTACK_WIN:
					data.theme		   = EPriorityMessageTheme::ATTACKER_WIN;
					data.priorityLevel = EPriorityMessageLevel::ATTACKER;
					data.header		   = GetLocalizedText( Localization::Raid, f"raid_raid_won" );
					data.subheader	   = GetLocalizedText( Localization::Raid, f"raid_enemy_base_destroyed" );
					data.onAppearSound = manager.centerAttackerWin;
					break;

				case EPriorityMessageTemplate::V2_ATTACK_LOSE:
					data.theme		   = EPriorityMessageTheme::ATTACKER_LOSE;
					data.priorityLevel = EPriorityMessageLevel::ATTACKER;
					data.header		   = GetLocalizedText( Localization::Raid, f"raid_raid_failed" );
					// The subheader is set in raidMessagingHud.as
					data.onAppearSound = manager.centerAttackerLose;
					data.onCreate.AddUFunction( manager, n"OnCreate_AttackerLose" );
					break;

				case EPriorityMessageTemplate::V2_DEFEND_WIN:
					data.theme		   = EPriorityMessageTheme::DEFENDER_WIN;
					data.priorityLevel = EPriorityMessageLevel::DEFENDER;
					data.header		   = GetLocalizedText( Localization::Raid, f"raid_raid_stopped" );
					data.subheader	   = GetLocalizedText( Localization::Raid, f"raid_base_destroyed" );
					data.onAppearSound = manager.centerDefenderWin;
					break;

				case EPriorityMessageTemplate::V2_DEFEND_LOSE:
					data.theme		   = EPriorityMessageTheme::DEFENDER_LOSE;
					data.priorityLevel = EPriorityMessageLevel::DEFENDER;
					data.header		   = GetLocalizedText( Localization::Raid, f"raid_raid_lost" );
					data.subheader	   = GetLocalizedText( Localization::Raid, f"raid_base_destroyed" );
					data.onAppearSound = manager.centerDefenderLose;
					break;

				default:
					return false;
			}

			// All raid reports share some of the same traits
			data.widgetClass	= manager.priorityWidgetRaidReportClass;
			data.placement		= EPriorityMessagePlacement::CENTER;
			data.autoDissappear = false;
		}

		return result;
	}
}