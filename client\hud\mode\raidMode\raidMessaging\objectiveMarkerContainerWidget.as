UCLASS( Abstract )
class UAS_ObjectiveMarkerContainerWidget : UNC_DisplayWidget
{
	default StateOnConstruction = EDisplayConstructVisibility::Hidden;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UPanelWidget objectiveMarkers;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden )
	private TSubclassOf<UAS_ObjectiveMarker> objectiveMarkerClass;

	private FTimerHandle updatePlantPointsTimer;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			scriptCallbacks.shared_OnRaidStarted.AddUFunction( this, n"OnRaidStartedOrEnded" );
			scriptCallbacks.shared_OnRaidEnded.AddUFunction( this, n"OnRaidStartedOrEnded" );
			scriptCallbacks.client_OnBaseActorSpawned.AddUFunction( this, n"OnBaseActorSpawned" );
		}

		UpdatePlantPointStates();
	}

	UFUNCTION( BlueprintOverride )
	void Destruct()
	{
		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			scriptCallbacks.shared_OnRaidStarted.Unbind( this, n"OnRaidStartedOrEnded" );
			scriptCallbacks.shared_OnRaidEnded.Unbind( this, n"OnRaidStartedOrEnded" );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnRaidStartedOrEnded( AAS_RaidEventManager_v2 eventManager )
	{
		// For both defenders and attackers, update plant point states when the raid starts or ends
		UpdatePlantPointStates();
	}

	void ShowMatchingObjective( UAS_RaidBombInterfaceComponent interface )
	{
		UpdatePlantPointStates();

		int numPoints = objectiveMarkers.GetChildrenCount();
		for ( int i = 0; i < numPoints; i++ )
		{
			UAS_ObjectiveMarker marker = Cast<UAS_ObjectiveMarker>( objectiveMarkers.GetChildAt( i ) );
			if ( IsValid( marker ) )
			{
				if ( marker.IsBombInterfaceAffectingTrackedObjective( interface ) )
				{
					marker.Show();
				}
				else
				{
					marker.Hide();
				}
			}
		}
	}

	UFUNCTION()
	private void OnBaseActorSpawned( ANCDefaultActor actor )
	{
		// In NCNet, base generator and vault actors are spawned on the client
		// as part of level streaming and may not exist immediately
		UpdatePlantPointStates();
	}

	UFUNCTION()
	private void UpdatePlantPointStates()
	{
		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( !IsValid( player ) )
			return;

		int playerTeam			   = player.GetTeam();
		TArray<AActor> plantPoints = GetPossiblePlantPointsForTeam( IsTeamRaiding( playerTeam ) ? GetOtherTeam( playerTeam ) : playerTeam );

		// Make sure there are enough objective markers to show all of the plant points
		int numPoints		 = plantPoints.Num();
		APlayerController pc = GetOwningPlayer();

		objectiveMarkers.ClearChildren();

		for ( int i = 0; i < numPoints; i++ )
		{
			UAS_ObjectiveMarker marker = Cast<UAS_ObjectiveMarker>( WidgetBlueprint::CreateWidget( objectiveMarkerClass, pc ) );
			if ( IsValid( marker ) )
			{
				objectiveMarkers.AddChild( marker );
			}
		}

		ScriptAssert( numPoints == objectiveMarkers.GetChildrenCount(), "UAS_RaidMessagingHud: Failed to allocate objective markers" );

		// Quick bubble sort based on priority
		for ( int i = 0; i < numPoints - 1; ++i )
		{
			for ( int j = 0; j < numPoints - i - 1; ++j )
			{
				AAS_BaseVault baseVaultA		   = Cast<AAS_BaseVault>( plantPoints[j] );
				AAS_BaseSubObjective subObjectiveA = Cast<AAS_BaseSubObjective>( plantPoints[j] );
				AAS_BaseVault baseVaultB		   = Cast<AAS_BaseVault>( plantPoints[j + 1] );
				AAS_BaseSubObjective subObjectiveB = Cast<AAS_BaseSubObjective>( plantPoints[j + 1] );

				int priorityA = IsValid( baseVaultA )	 ? int( baseVaultA.playerFacingData.indicator ) :
								IsValid( subObjectiveA ) ? int( subObjectiveA.playerFacingData.indicator ) :
														   GameConst::INDEX_NONE;
				int priorityB = IsValid( baseVaultB )	 ? int( baseVaultB.playerFacingData.indicator ) :
								IsValid( subObjectiveB ) ? int( subObjectiveB.playerFacingData.indicator ) :
														   GameConst::INDEX_NONE;

				if ( priorityA > priorityB )
					plantPoints.Swap( j, j + 1 );
			}
		}

		// Now that the plant points are sorted by priority, we can show data on each of the objective markers
		for ( int i = 0; i < numPoints; i++ )
		{
			UAS_ObjectiveMarker marker = Cast<UAS_ObjectiveMarker>( objectiveMarkers.GetChildAt( i ) );
			if ( IsValid( marker ) )
			{
				marker.SetOwningObjective( plantPoints[i] );
			}
		}
	}
}