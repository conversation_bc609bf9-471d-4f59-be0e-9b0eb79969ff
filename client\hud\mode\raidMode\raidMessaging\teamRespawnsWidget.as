UCLASS( Abstract )
class UAS_TeamRespawnsWidget : UNC_DisplayWidget
{
	default StateOnConstruction = EDisplayConstructVisibility::Hidden;
	default HideVisibility		= ESlateVisibility::Collapsed;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UPanelWidget livesContainer;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UPanelWidget maraLivesContainer;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage divider;

	UPROPERTY( EditInstanceOnly, BlueprintHidden )
	private bool trackEnemyTeam = false;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden )
	private FSlateBrush livesBrush;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden )
	private FSlateBrush maraLivesBrush;

	private int trackedTeamId = GameConst::INDEX_NONE;
	private AAS_MaraRespawnZone trackedZone;

	private const int ENEMY_TEAM_COLUMN = 0;
	private const int LOCAL_TEAM_COLUMN = 2;
	private const EHorizontalAlignment RIGHT_ALIGNED = EHorizontalAlignment::HAlign_Right;
	private const EHorizontalAlignment LEFT_ALIGNED = EHorizontalAlignment::HAlign_Left;
	private const int IS_RAIDING = -1;
	private const int IS_BEING_RAIDED = 1;
	private const float FULL_OPACITY = 1.0f;
	private const float EMPTY_OPACITY = 0.25f;

	UFUNCTION( BlueprintOverride )
	void PreConstruct( bool isDesignTime )
	{
		SetRenderScale( FVector2D( trackEnemyTeam ? 1.0f : -1.0f, 1.0f ) );
	}

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
	}

	void SetTrackedTeamId( int teamId )
	{
		if ( trackedTeamId != teamId )
		{
			trackedTeamId = teamId;

			int maxLives = GetTeamAttackerMaxLives( trackedTeamId );

			if ( livesContainer.GetChildrenCount() > maxLives )
			{
				livesContainer.ClearChildren();
			}

			while ( livesContainer.GetChildrenCount() != maxLives )
			{
				int numChildren = livesContainer.GetChildrenCount();
				if ( numChildren > maxLives )
				{
					livesContainer.RemoveChildAt( numChildren - 1 );
				}
				else
				{
					UImage respawnImage = Cast<UImage>( NewObject( livesContainer, UImage::StaticClass() ) );
					if ( IsValid( respawnImage ) )
					{
						respawnImage.SetBrush( livesBrush );
						SetWidgetVisibilitySafe( respawnImage, ESlateVisibility::HitTestInvisible );
						livesContainer.AddChild( respawnImage );
					}
					else
					{
						break;
					}
				}
			}
		}
	}

	UFUNCTION( BlueprintOverride )
	void OnShowStart()
	{
		bool isRaiding = IsTeamRaiding( trackedTeamId );
		if ( isRaiding )
		{
			UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
			if ( IsValid( scriptCallbacks ) )
			{
				// If the team is raiding, we want to start tracking their attacker lives
				scriptCallbacks.client_onAttackerLivesChanged.AddUFunction( this, n"OnAttackerLivesChanged" );
				OnAttackerLivesChanged( trackedTeamId, GameConst::INDEX_NONE, GetTeamAttackerLives( trackedTeamId ) );

				scriptCallbacks.client_OnMaraSpawnBeaconPlaced.AddUFunction( this, n"OnMaraSpawnBeaconPlaced" );
				scriptCallbacks.client_OnMaraSpawnBeaconDestroyed.AddUFunction( this, n"OnMaraSpawnBeaconDestroyed" );
			}
		}
	}

	UFUNCTION( BlueprintOverride )
	void OnHideStart()
	{
		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			scriptCallbacks.client_onAttackerLivesChanged.Unbind( this, n"OnAttackerLivesChanged" );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnAttackerLivesChanged( int teamId, int oldValue, int newValue )
	{
		if ( teamId != trackedTeamId )
			return;

		UpdateAllChildren( livesContainer, newValue );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnMaraSpawnBeaconPlaced( AAS_MaraRespawnZone zone )
	{
		AAS_PlayerEntity localPlayer = Client_GetLocalASPawn();

		if ( !IsValid( zone ) || !IsValid( localPlayer ) )
			return;

		// We only care about zones that are friendly and spawned within the raid dome of the other team
		AAS_RaidDomeShield otherTeamDome = GetRaidDomeShieldForTeam( GetOtherTeam( localPlayer.GetTeam() ) );
		AAS_PlayerEntity owningPlayer	 = zone.GetOwnerPlayer();
		bool isFriendly					 = IsValid( owningPlayer ) ? owningPlayer.GetTeam() == trackedTeamId : false;

		bool isRelevantToRaid = IsActorRelevantToRaid( otherTeamDome, zone ) && isFriendly;
		if ( isRelevantToRaid )
		{
			// If it matches what we should track, track its uses
			zone.net_lives.OnReplicated().AddUFunction( this, n"OnMaraSpawnBeaconLivesChanged" );
			trackedZone = zone;
		}

		SetWidgetVisibilitySafe( maraLivesContainer, isRelevantToRaid ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
		SetWidgetVisibilitySafe( divider, isRelevantToRaid ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnMaraSpawnBeaconDestroyed( AAS_MaraRespawnZone zone )
	{
		if ( IsValid( zone ) && trackedZone == zone )
		{
			zone.net_lives.OnReplicated().Unbind( this, n"OnMaraSpawnBeaconLivesChanged" );
			SetWidgetVisibilitySafe( maraLivesContainer, ESlateVisibility::Collapsed );
			SetWidgetVisibilitySafe( divider, ESlateVisibility::Collapsed );
			trackedZone = nullptr;
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnMaraSpawnBeaconLivesChanged( int oldValue, int newValue )
	{
		UpdateAllChildren( maraLivesContainer, newValue );
	}

	private void UpdateAllChildren( UPanelWidget panel, int newValue )
	{
		if ( !IsValid( panel ) )
			return;

		for ( int i = 0; i < panel.GetChildrenCount(); i++ )
		{
			UWidget child = panel.GetChildAt( i );
			if ( IsValid( child ) )
			{
				float targetOpacity = i >= newValue ? EMPTY_OPACITY : FULL_OPACITY;
				if ( !Math::IsNearlyEqual( child.GetRenderOpacity(), targetOpacity ) )
				{
					child.SetRenderOpacity( targetOpacity );
				}
			}
		}
	}
}