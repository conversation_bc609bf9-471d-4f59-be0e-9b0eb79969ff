USTRUCT()
struct FNCAudioStateGroup
{
    UPROPERTY( Meta = (ForceInlineRow) )
    TMap<FGameplayTag, UAkStateValue> WwiseStateValues;
}

/****************************************************************\

 ██████ ██       █████  ███████ ███████
██      ██      ██   ██ ██      ██
██      ██      ███████ ███████ ███████
██      ██      ██   ██      ██      ██
 ██████ ███████ ██   ██ ███████ ███████

\****************************************************************/

UCLASS( Abstract )
class UAS_AudioStateManager : UNCGameplaySystem_Client
{
    UPROPERTY( EditAnywhere, Meta = (ForceInlineRow) )
    TMap<FGameplayTag, FNCAudioStateGroup> AudioStates;

    private bool dataInitialized = false;
	UFUNCTION( BlueprintOverride )
	void Initialize()
	{
        InitCallbacks();

		dataInitialized = true;
	}


    /****************************************************************\

     ██████  █████  ██      ██      ██████   █████   ██████ ██   ██ ███████
    ██      ██   ██ ██      ██      ██   ██ ██   ██ ██      ██  ██  ██
    ██      ███████ ██      ██      ██████  ███████ ██      █████   ███████
    ██      ██   ██ ██      ██      ██   ██ ██   ██ ██      ██  ██       ██
     ██████ ██   ██ ███████ ███████ ██████  ██   ██  ██████ ██   ██ ███████

    \****************************************************************/

    private void InitCallbacks()
    {
        // States: InGame_PreparationPhase
        //         InGame_CharacterSelect
        //         InGame_BaseSelect
        //         InGame_Endgame

        // States: GameMode_RaidMode
        //         GameMode_TrainingMode
        ClientCallbacks().OnGamePhaseChanged.AddUFunction( this, n"Callback_OnGamePhaseChanged" );
        Callback_OnGamePhaseChanged( -1, GetGamePhase() );

        // States: InGame_ExploreAndGather
        //         InGame_ShieldbreakerForming
        //         InGame_Intercept
        ScriptCallbacks().client_onRaidObjectiveStateChanged.AddUFunction( this, n"Callback_OnObjectiveStateChanged" );

        // States: InGame_Raiding
        //         InGame_Defending
        ScriptCallbacks().shared_OnRaidStarted.AddUFunction( this, n"Callback_OnRaidStarted" );

        // States: GameTransition_Respawn
        // States: GameTransition_Spectate
        // States: GameTransition_None
        ScriptCallbacks().client_onRespawnMenuModeChanged.AddUFunction( this, n"Callback_OnRespawnMenuModeChanged" );
        ClientCallbacks().OnPawnRespawned.AddUFunction( this, n"Callback_OnPawnRespawned" );

        // States: Combat_InCombat
        //         Combat_InExploration
        ScriptCallbacks().shared_OnCombatEventSystem.AddUFunction( this, n"Callback_OnCombatEventSystem" );

        // States: Mount_OnMount
        //         Mount_OnFoot
        ScriptCallbacks().client_onMountStateChanged.AddUFunction( this, n"Callback_OnMountStateChanged" );

        // States: Trader_Trading
        ScriptCallbacks().shared_onPlayerUsedVendor.AddUFunction( this, n"Callback_OnPlayerUsedVendor" );
        ScriptCallbacks().client_onPlayerClosedVendor.AddUFunction( this, n"Callback_OnPlayerClosedVendor" );

        // States: Player_Alive
        //         Player_Dead
        //         Player_LowHealth
        ClientCallbacks().OnPawnHealthChanged.AddUFunction( this, n"Callback_OnPawnHealthChanged" );

        // States: InMenu_TitleScreen
        //         InMenu_Lobby
        //         InMenu_Headwall
        //         InMenu_Store
        //         InMenu_Customization
        //         InMenu_Loading
        ScriptCallbacks().localClient_onMenuContextChanged.AddUFunction( this, n"Callback_OnMenuContextChanged" );
    }

    UFUNCTION()
    private void Callback_OnMenuContextChanged( FGameplayTag screenId )
    {
        //Print( f"ID: {screenId}" );
        //Print( f"Resetting state" );
        Client_SetState(AudioStates[GameplayTags::Audio_State_InMenu].WwiseStateValues[GameplayTags::Audio_State_InMenu_None]); // Reset menu state

        FGameplayTag stateTag;
        if ( screenId.MatchesTag( GameplayTags::Screens_Login ) )
            stateTag = GameplayTags::Audio_State_InMenu_TitleScreen;
        else if ( screenId.GameplayTagParents.GameplayTags.Contains( GameplayTags::Screens_MainMenu_Store ) )
            stateTag = GameplayTags::Audio_State_InMenu_Store;
        else if ( screenId.GameplayTagParents.GameplayTags.Contains( GameplayTags::Screens_MainMenu_Collection ) )
            stateTag = GameplayTags::Audio_State_InMenu_Customization;
        else if ( screenId.GameplayTagParents.GameplayTags.Contains( GameplayTags::Screens_MainMenu_Progression ) )
            stateTag = GameplayTags::Audio_State_InMenu_Headwall;
        else if ( screenId.MatchesTag( GameplayTags::Screens_Loading ) )
            stateTag = GameplayTags::Audio_State_InMenu_Loading;
        else if ( screenId.GameplayTagParents.GameplayTags.Contains( GameplayTags::Screens_MainMenu_Lobby )
            || screenId.GameplayTagParents.GameplayTags.Contains( GameplayTags::Screens_MainMenu ) )
            stateTag = GameplayTags::Audio_State_InMenu_Lobby; //fallback

        if ( stateTag.IsValid() )
        {
            //Print( f"{stateTag}" );
            Client_SetState(AudioStates[GameplayTags::Audio_State_InMenu].WwiseStateValues[stateTag]);
        }
    }

    UFUNCTION()
	private void Callback_OnGamePhaseChanged( int OldState, int NewState )
    {
        UAS_ClientScript_RaidMode_Training trainingModeScript = ClientTrainingMode();
        UAS_ClientScript_FiringRange firingRangeScript = ClientFiringRangeMode();

        if ( IsValid( trainingModeScript ) )
            Client_SetState(AudioStates[GameplayTags::Audio_State_GameMode].WwiseStateValues[GameplayTags::Audio_State_GameMode_TrainingMode]);
        else if ( IsValid( firingRangeScript ) )
            Client_SetState(AudioStates[GameplayTags::Audio_State_GameMode].WwiseStateValues[GameplayTags::Audio_State_GameMode_FiringRangeMode]);
        else
            Client_SetState(AudioStates[GameplayTags::Audio_State_GameMode].WwiseStateValues[GameplayTags::Audio_State_GameMode_RaidMode]);

        Client_SetState(AudioStates[GameplayTags::Audio_State_InMenu].WwiseStateValues[GameplayTags::Audio_State_InMenu_None]); // Reset game state

        bool didSetStateTag = true;
        FGameplayTag stateTag;
        if ( NewState == GamePhase::PREMATCH )
            stateTag = GameplayTags::Audio_State_InGame_PreparationPhase;
        else if ( NewState == GamePhase::CHARACTER_SELECT )
            stateTag = GameplayTags::Audio_State_InGame_CharacterSelect;
        else if ( NewState == GamePhase::BASE_SELECT )
            stateTag = GameplayTags::Audio_State_InGame_BaseSelect;
        else if ( NewState == GamePhase::WINNER_DETERMINED )
            stateTag = GameplayTags::Audio_State_InGame_Endgame;
        else
            didSetStateTag = false;

        if ( didSetStateTag )
        {
            Print( f"{stateTag}" );
            Client_SetState(AudioStates[GameplayTags::Audio_State_InGame].WwiseStateValues[stateTag]);
        }

        if ( NewState == GamePhase::PREMATCH || OldState == GamePhase::PREMATCH )
		{
			// HACK for NC1-8789.
			// We assume current pawn is local player and all players are alive on phase change to playing
			if ( System::GetGameTimeInSeconds() < 1.0 )
				Client_SetState(AudioStates[GameplayTags::Audio_State_Player].WwiseStateValues[GameplayTags::Audio_State_Player_Alive]);
		}
    }

    UFUNCTION()
    private void Callback_OnObjectiveStateChanged( int Team, int OldValue, int NewValue )
    {
        bool didSetStateTag = true;
        FGameplayTag stateTag;
        EObjectiveState objectiveState = EObjectiveState( NewValue );
        switch ( objectiveState )
        {
            case EObjectiveState::ExploreAndGearUp:
            {
                stateTag = GameplayTags::Audio_State_InGame_ExploreAndGather;
                break;
            }
            case EObjectiveState::ShieldBreakerCrafting:
            {
                stateTag = GameplayTags::Audio_State_InGame_ShieldbreakerForming;
                break;
            }
            case EObjectiveState::PickupShieldBreaker:
            {
                stateTag = GameplayTags::Audio_State_InGame_ShieldbreakerFormed;
                break;
            }
            case EObjectiveState::InterceptShieldBreaker:
            {
                stateTag = GameplayTags::Audio_State_InGame_Intercept;
                break;
            }
            default:
            {
                didSetStateTag = false;
                break;
            }
        }

        if ( didSetStateTag )
        {
            Print( f"{stateTag}" );
            Client_SetState(AudioStates[GameplayTags::Audio_State_InGame].WwiseStateValues[stateTag]);
        }
    }

    UFUNCTION()
	private void Callback_OnRaidStarted( AAS_RaidEventManager_v2 eventManager )
    {
        if ( IsServer() )
            return;

        ANCPlayerCharacter player = Client_GetLocalPawn();
        int attackerID = eventManager.GetAttackerTeam();
		int defenderID = eventManager.GetDefenderTeam();

        // Local player is on neither team
        if ( player.GetTeam() != attackerID && player.GetTeam() != defenderID )
			return;

        FGameplayTag stateTag = player.GetTeam() == attackerID ? GameplayTags::Audio_State_InGame_Raiding : GameplayTags::Audio_State_InGame_Defending;
        Client_SetState(AudioStates[GameplayTags::Audio_State_InGame].WwiseStateValues[stateTag]);
    }

    UFUNCTION()
    private void Callback_OnPlayerUsedVendor( AAS_PlayerEntity player, AAS_Vendor vendor )
    {
        if ( player != Client_GetLocalPawn() )
            return;

        Client_SetState(AudioStates[GameplayTags::Audio_State_Trader].WwiseStateValues[GameplayTags::Audio_State_Trader_Trading]);
    }

    UFUNCTION()
    private void Callback_OnPlayerClosedVendor( AAS_PlayerEntity player, AAS_Vendor vendor )
    {
        if ( player != Client_GetLocalPawn() )
            return;

        Client_SetState(AudioStates[GameplayTags::Audio_State_Trader].WwiseStateValues[GameplayTags::Audio_State_Trader_None]);
    }


    UFUNCTION()
    private void Callback_OnRespawnMenuModeChanged( ERespawnMenuMode oldMode, ERespawnMenuMode newMode )
    {
        switch( newMode )
        {
            case ERespawnMenuMode::SPECTATE:
            {
                Client_SetState(AudioStates[GameplayTags::Audio_State_GameTransition].WwiseStateValues[GameplayTags::Audio_State_GameTransition_Spectate]);
            }
            break;

            case ERespawnMenuMode::SPAWN_SELECTION:
            {
                Client_SetState(AudioStates[GameplayTags::Audio_State_GameTransition].WwiseStateValues[GameplayTags::Audio_State_GameTransition_Respawn]);
            }
            break;

            default:
            {
                Client_SetState(AudioStates[GameplayTags::Audio_State_GameTransition].WwiseStateValues[GameplayTags::Audio_State_GameTransition_None]);
            }
            break;
        }
    }

    UFUNCTION()
    private void Callback_OnPawnRespawned( ANCPlayerCharacter player )
    {
        if ( player != Client_GetLocalPawn() )
            return;

        // Ensure player is set to "alive" state
        Client_SetState(AudioStates[GameplayTags::Audio_State_Player].WwiseStateValues[GameplayTags::Audio_State_Player_Alive]);

        // Reset to neither respawn screen nor spectate mode
        Client_SetState(AudioStates[GameplayTags::Audio_State_GameTransition].WwiseStateValues[GameplayTags::Audio_State_GameTransition_None]);
    }

    UFUNCTION()
    private void Callback_OnCombatEventSystem( ANCPlayerCharacter player, ECombatSystemEvent reason )
    {
        if ( IsServer() )
            return;
        if ( player != Client_GetLocalPawn() )
            return;

        FGameplayTag stateTag;
        switch ( reason )
		{
			case ECombatSystemEvent::ONSTART:
            {
                stateTag = GameplayTags::Audio_State_Combat_InCombat;
				break;
            }

			case ECombatSystemEvent::ONEND_TIMEOUT:
			case ECombatSystemEvent::ONEND_WIN:
			case ECombatSystemEvent::ONEND_LOSE:
            default:
            {
                stateTag = GameplayTags::Audio_State_Combat_InExploration;
				break;
            }
		}

        Client_SetState(AudioStates[GameplayTags::Audio_State_Combat].WwiseStateValues[stateTag]);
    }

    UFUNCTION()
	private void Callback_OnMountStateChanged( ANCPlayerCharacter player, AAS_VehicleMount mount, bool isRiding )
	{
        if ( IsServer() )
            return;
        if ( player != Client_GetLocalPawn() )
            return;

		FGameplayTag stateTag = player.IsPlayerRidingMount() ? GameplayTags::Audio_State_Mount_OnMount : GameplayTags::Audio_State_Mount_OnFoot;
		Client_SetState(AudioStates[GameplayTags::Audio_State_Mount].WwiseStateValues[stateTag]);
	}

    UFUNCTION()
    private void Callback_OnPawnHealthChanged( FHealthChangedInfo healthInfo )
    {
        if ( !IsValid( healthInfo.pawn ) )
            return;
        if ( healthInfo.pawn != Client_GetLocalPawn() )
            return;

        FGameplayTag stateTag;
        if ( healthInfo.newHealth <= 0 )
        {
            stateTag = GameplayTags::Audio_State_Player_Dead;
        }
        else if ( healthInfo.newHealth <= healthInfo.pawn.GetMaxHealth() / 2 )
        {
            stateTag = GameplayTags::Audio_State_Player_LowHealth;
        }
        else
        {
            stateTag = GameplayTags::Audio_State_Player_Alive;
        }

        Client_SetState(AudioStates[GameplayTags::Audio_State_Player].WwiseStateValues[stateTag]);
    }
}
