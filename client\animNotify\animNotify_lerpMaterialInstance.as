UCLASS()
class UAnimNotify_LerpMaterialInstance : UAS_ScriptAnimNotifyBase
{
	UPROPERTY()
	FName parameterName;

	UPROPERTY()
	int materialIndex;

	UPROPERTY()
	float32 startValue;

	UPROPERTY()
	float32 endValue;

	UPROPERTY()
	float32 duration;

	// customize defaults
	default enableServer		   = false;
	default animNotifyTrackerClass = UAS_AnimNotifyTracker_LerpMaterialInstance::StaticClass();
}

UCLASS()
class UAS_AnimNotifyTracker_LerpMaterialInstance : UAS_AnimNotifyTrackerBase
{
	void OnNotify( USkeletalMeshComponent meshComp, UAnimSequenceBase animation,
				   const UAS_ScriptAnimNotifyBase animNotify ) override
	{
		Super::OnNotify( meshComp, animation, animNotify );
		UAS_LerpMaterialInstance_Thread thread = Cast<UAS_LerpMaterialInstance_Thread>( CreateThread( UAS_LerpMaterialInstance_Thread::StaticClass(), meshComp ) );
		thread.Init( Cast<UAnimNotify_LerpMaterialInstance>(animNotify), meshComp );
	}
}

UCLASS()
class UAS_LerpMaterialInstance_Thread : UAS_Thread
{
	UPROPERTY( EditDefaultsOnly )
	FName parameterName;

	UPROPERTY( EditDefaultsOnly )
	int materialIndex;

	UPROPERTY( EditDefaultsOnly )
	float32 startValue;

	UPROPERTY( EditDefaultsOnly )
	float32 endValue;

	UPROPERTY( EditDefaultsOnly )
	float32 duration;

	USkeletalMeshComponent meshComp;

	void Init( const UAnimNotify_LerpMaterialInstance animNotify, USkeletalMeshComponent inMeshComp )
	{
		parameterName = animNotify.parameterName;
		materialIndex = animNotify.materialIndex;
		startValue	  = animNotify.startValue;
		endValue	  = animNotify.endValue;
		duration	  = animNotify.duration;
		meshComp	  = inMeshComp;
		Start();
	}

	void OnThreadStart( UNCCoroutine co ) override
	{
		Super::OnThreadStart( co );

		co.EndOnDestroyed( meshComp.GetOwner() );

		float startTime = System::GetGameTimeInSeconds();
		float endTime	= startTime + duration;

		UMaterialInstanceDynamic material = meshComp.CreateDynamicMaterialInstance( materialIndex );

		if ( !IsValid( material ) )
		{
			ScriptError_Silent_WithBug( f"material {materialIndex} {meshComp.GetMaterial(materialIndex)} on {meshComp.SkeletalMeshAsset} is trying to use LerpMaterialInstance but it doesn't have a dynamic material", "pyeoung", "" );
			return;
		}

		while ( IsValid( meshComp ) && System::GetGameTimeInSeconds() < endTime )
		{
			float value = Math::GetMappedRangeValueClamped( FVector2D( startTime, endTime ), FVector2D( startValue, endValue ), System::GetGameTimeInSeconds() );
			material.SetScalarParameterValue( parameterName, value );
			co.Wait( 0.01 );
		}

		material.SetScalarParameterValue( parameterName, endValue );
	}
}