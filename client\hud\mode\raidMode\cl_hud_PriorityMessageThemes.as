namespace PriorityMessageColor
{
	const FLinearColor ATTACK_COLOR_MAIN_MSG	= FLinearColor( 0.0, 1.0, 0.0, 0.5 );
	const FLinearColor ATTACK_COLOR_CTA_MSG		= FLinearColor( 0.0, 0.25, 0.0, 1.0 );
	const FLinearColor INTERRUPT_COLOR_MAIN_MSG = FLinearColor( 0.85, 0.15, 0.0, 0.75 );
	const FLinearColor INTERRUPT_COLOR_CTA_MSG	= FLinearColor( 0.85, 0.15, 0.0, 1.0 );
	const FLinearColor DEFENDER_COLOR_MAIN_MSG	= FLinearColor( 1.0, 0.0, 0.0, 0.5 );
	const FLinearColor DEFENDER_COLOR_CTA_MSG	= FLinearColor( 0.25, 0.0, 0.0, 1.0 );
	const FLinearColor STOPPING_COLOR_MAIN_MSG	= FLinearColor( 0.85, 0.15, 0.0, 0.75 );
	const FLinearColor STOPPING_COLOR_CTA_MSG	= FLinearColor( 0.85, 0.15, 0.0, 1.0 );

	const FLinearColor WIN_COLOR_MAIN_MSG	   = FLinearColor( 0.0, 0.9, 1.0, 0.5 );
	const FLinearColor WIN_COLOR_CTA_MSG	   = FLinearColor( 0.0, 0.45, 0.65, 1.0 );
	const FLinearColor ATK_LOSE_COLOR_MAIN_MSG = FLinearColor( 0.0, 0.016, 0.0, 0.65 );
	const FLinearColor ATK_LOSE_COLOR_CTA_MSG  = FLinearColor( 0.0, 0.004, 0.0, 1.0 );

	const FLinearColor OTHER_COLOR_MAIN_MSG = FLinearColor( 1.0, 0.5, 0.0, 0.5 );
	const FLinearColor OTHER_COLOR_CTA_MSG	= FLinearColor( 0.25, 0.125, 0.0, 1.0 );

	const FLinearColor RIGHT_COLOR_MAIN_MSG = FLinearColor( 0.15, 0.15, 0.15, 0.75 );
	const FLinearColor RIGHT_COLOR_ICON_BG	= FLinearColor( 0.25, 0.25, 0.25, 1.0 );
	const FLinearColor RIGHT_COLOR_ICON		= FLinearColor::White;

	const FLinearColor GRAY_COLOR_MAIN_MSG = FLinearColor( 0.15, 0.15, 0.15, 0.75 );
	const FLinearColor GRAY_COLOR_ICON_BG  = FLinearColor( 0.25, 0.25, 0.25, 1.0 );
	const FLinearColor GRAY_COLOR_ICON	   = FLinearColor( 0.25, 0.25, 0.25, 1.0 );

	const FLinearColor RIGHT_COLOR_ICON_GEAR2 = FLinearColor( 0.00, 0.72, 1.00 );
	const FLinearColor RIGHT_COLOR_ICON_GEAR3 = FLinearColor( 1.00, 0.00, 1.00 );
	const FLinearColor RIGHT_COLOR_ICON_GEAR4 = FLinearColor( 1.00, 0.37, 0.00 );

	const FLinearColor INVISIBLE = FLinearColor( 0, 0, 0, 0 );

	const FLinearColor GAME_END_MAIN_MSG = FLinearColor( 0, 0.15, 1, 0.75 );
	const FLinearColor GAME_END_CTA_MSG	 = FLinearColor( 0, 0.15, 1, 1.0 );
}

enum EPriorityMessageTheme
{
	DEFENDER_DEFAULT,
	DEFENDER_STOPPING,
	DEFENDER_WIN,
	DEFENDER_LOSE,

	ATTACKER_DEFAULT,
	ATTACKER_INTERRUPTED,
	ATTACKER_WIN,
	ATTACKER_LOSE,

	OTHER,

	RIGHT_DEFAULT,
	RIGHT_GEAR2,
	RIGHT_GEAR3,
	RIGHT_GEAR4,

	INVISIBLE,
	GRAY,
	USEPARENT,	 // only for list items
	USELISTITEM, // only for info bar items
	GAME_END_NAG,
	FRIENDLY_ACTION,
	ENEMY_ACTION,
	NEUTRAL_ACTION,
	OVERTIME_ACTION,
	FRIENDLY_SHIELDBREAKER_ACTION,
	ENEMY_SHIELDBREAKER_ACTION,
	NEUTRAL_SHIELDBREAKER_ACTION,

	_count
}

struct FPriorityMessageColorTheme
{
	FLinearColor MainBG	   = PriorityMessageColor::OTHER_COLOR_MAIN_MSG;
	FLinearColor CTA_BG	   = PriorityMessageColor::OTHER_COLOR_CTA_MSG;
	FLinearColor IconBG	   = PriorityMessageColor::RIGHT_COLOR_ICON_BG;
	FLinearColor IconImage = PriorityMessageColor::RIGHT_COLOR_ICON;
}

FPriorityMessageColorTheme GetPriorityMessageThemeColors( EPriorityMessageTheme Theme )
{
	FPriorityMessageColorTheme NewTheme;

	switch ( Theme )
	{
		case EPriorityMessageTheme::ATTACKER_DEFAULT:
			NewTheme.MainBG = PriorityMessageColor::ATTACK_COLOR_MAIN_MSG;
			NewTheme.CTA_BG = PriorityMessageColor::ATTACK_COLOR_CTA_MSG;
			NewTheme.IconBG = PriorityMessageColor::ATTACK_COLOR_CTA_MSG;
			break;

		case EPriorityMessageTheme::ATTACKER_INTERRUPTED:
			NewTheme.MainBG = PriorityMessageColor::INTERRUPT_COLOR_MAIN_MSG;
			NewTheme.CTA_BG = PriorityMessageColor::INTERRUPT_COLOR_CTA_MSG;
			NewTheme.IconBG = PriorityMessageColor::INTERRUPT_COLOR_CTA_MSG;
			break;

		case EPriorityMessageTheme::DEFENDER_STOPPING:
			NewTheme.MainBG = PriorityMessageColor::STOPPING_COLOR_MAIN_MSG;
			NewTheme.CTA_BG = PriorityMessageColor::STOPPING_COLOR_CTA_MSG;
			NewTheme.IconBG = PriorityMessageColor::STOPPING_COLOR_CTA_MSG;
			break;

		case EPriorityMessageTheme::DEFENDER_DEFAULT:
		case EPriorityMessageTheme::DEFENDER_LOSE:
			NewTheme.MainBG = PriorityMessageColor::DEFENDER_COLOR_MAIN_MSG;
			NewTheme.CTA_BG = PriorityMessageColor::DEFENDER_COLOR_CTA_MSG;
			NewTheme.IconBG = PriorityMessageColor::DEFENDER_COLOR_CTA_MSG;
			break;

		case EPriorityMessageTheme::ATTACKER_WIN:
		case EPriorityMessageTheme::DEFENDER_WIN:
			NewTheme.MainBG = PriorityMessageColor::WIN_COLOR_MAIN_MSG;
			NewTheme.CTA_BG = PriorityMessageColor::WIN_COLOR_CTA_MSG;
			break;

		case EPriorityMessageTheme::ATTACKER_LOSE:
			NewTheme.MainBG = PriorityMessageColor::ATK_LOSE_COLOR_MAIN_MSG;
			NewTheme.CTA_BG = PriorityMessageColor::ATK_LOSE_COLOR_CTA_MSG;
			break;

		case EPriorityMessageTheme::RIGHT_DEFAULT:
			NewTheme.MainBG	   = PriorityMessageColor::RIGHT_COLOR_MAIN_MSG;
			NewTheme.IconBG	   = PriorityMessageColor::RIGHT_COLOR_ICON_BG;
			NewTheme.IconImage = PriorityMessageColor::RIGHT_COLOR_ICON;
			break;

		case EPriorityMessageTheme::RIGHT_GEAR2:
			NewTheme.MainBG	   = PriorityMessageColor::RIGHT_COLOR_MAIN_MSG;
			NewTheme.IconBG	   = PriorityMessageColor::RIGHT_COLOR_ICON_BG;
			NewTheme.IconImage = PriorityMessageColor::RIGHT_COLOR_ICON_GEAR2;
			break;

		case EPriorityMessageTheme::RIGHT_GEAR3:
			NewTheme.MainBG	   = PriorityMessageColor::RIGHT_COLOR_MAIN_MSG;
			NewTheme.IconBG	   = PriorityMessageColor::RIGHT_COLOR_ICON_BG;
			NewTheme.IconImage = PriorityMessageColor::RIGHT_COLOR_ICON_GEAR3;
			break;

		case EPriorityMessageTheme::RIGHT_GEAR4:
			NewTheme.MainBG	   = PriorityMessageColor::RIGHT_COLOR_MAIN_MSG;
			NewTheme.IconBG	   = PriorityMessageColor::RIGHT_COLOR_ICON_BG;
			NewTheme.IconImage = PriorityMessageColor::RIGHT_COLOR_ICON_GEAR4;
			break;

		case EPriorityMessageTheme::INVISIBLE:
			NewTheme.CTA_BG	   = PriorityMessageColor::INVISIBLE;
			NewTheme.IconBG	   = PriorityMessageColor::INVISIBLE;
			NewTheme.IconImage = PriorityMessageColor::INVISIBLE;
			NewTheme.MainBG	   = PriorityMessageColor::INVISIBLE;
			break;

		case EPriorityMessageTheme::GRAY:
			NewTheme.CTA_BG	   = PriorityMessageColor::GRAY_COLOR_ICON_BG;
			NewTheme.IconBG	   = PriorityMessageColor::GRAY_COLOR_ICON_BG;
			NewTheme.IconImage = PriorityMessageColor::GRAY_COLOR_ICON;
			NewTheme.MainBG	   = PriorityMessageColor::GRAY_COLOR_MAIN_MSG;
			break;

		case EPriorityMessageTheme::GAME_END_NAG:
			NewTheme.MainBG = PriorityMessageColor::GAME_END_MAIN_MSG;
			NewTheme.CTA_BG = PriorityMessageColor::GAME_END_CTA_MSG;
			break;
		default:
			break;
	}

	return NewTheme;
}