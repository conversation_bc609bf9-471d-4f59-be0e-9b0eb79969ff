event void FEvent_OnMountVMPlayActivity( AAS_VehicleMount mount, USkeletalMeshComponent mountMesh_1P, USkeletalMeshComponent armsMesh_1P, EMountMontageACT activity );

class AAS_VehicleMountOffhandVM : AActor
{
	access internal = private, UAS_MountAnimComponent;
	UPROPERTY(DefaultComponent, RootComponent)
	USceneComponent root;

	UPROPERTY(DefaultComponent)
	access:internal USkeletalMeshComponent armsVM;
	default armsVM.CastShadow = false;

	UPROPERTY(DefaultComponent)
	access:internal USkeletalMeshComponent mountVM;
	default mountVM.CastShadow = false;
	
	private ANCPlayerCharacter ownerPlayer;
	private AAS_VehicleMount ownerMount;
	private FVehicleMountData mountData;	

/****************************************************************\

██ ███    ██ ██ ████████         ██      ██████ ██      ███████  █████  ███    ██ ██    ██ ██████  
██ ████   ██ ██    ██           ██      ██      ██      ██      ██   ██ ████   ██ ██    ██ ██   ██ 
██ ██ ██  ██ ██    ██          ██       ██      ██      █████   ███████ ██ ██  ██ ██    ██ ██████  
██ ██  ██ ██ ██    ██         ██        ██      ██      ██      ██   ██ ██  ██ ██ ██    ██ ██      
██ ██   ████ ██    ██        ██          ██████ ███████ ███████ ██   ██ ██   ████  ██████  ██

\****************************************************************/
	const FName LEFT_ARM_BONE 		= n"Lf_clav";
	const FName RIGHT_ARM_BONE 		= n"Rt_clav";
	const FRotator ARM_ROTHACK 		= FRotator(0,0,90); 
	FVector weapDefaultOffset 		= FVector(0,0,-166); //player.GetViewmodelWeapon().GetRelativeLocation(); // just hard code it -> looks better

	void InitArms( ANCPlayerCharacter player, AAS_VehicleMount mount )
	{
		ScriptAssert( IsClient(), "CLIENT ONLY" );
		ScriptAssert( player.IsLocallyControlled(), "Only call on local pawn" );

		ownerPlayer = player;
		ownerMount 	= mount;
		ownerMount.animComponent.sh_OnPilotPlayActivity.AddUFunction( this, n"OnPilotPlayActivity");

		Thread( this, n"__HACK_WaitPlayerLoadedToFinishInit", player );
	}	

	UFUNCTION()
	void __HACK_WaitPlayerLoadedToFinishInit( UNCCoroutine co, ANCPlayerCharacter player )
	{
		co.EndOnDestroyed( player );

		//armsMesh will be nullptr on a reconnect if you're on the mount... need to handle that situation
		while( !IsValid(player.GetViewmodelArms().GetSkeletalMeshAsset()) )
			co.Wait( 0.01 );

		InitArms_Finish( player );
	}

	void InitArms_Finish( ANCPlayerCharacter player )
	{
		if(ownerPlayer != player)
			ownerPlayer = player;

		if(!IsValid(ownerMount))
			ownerMount = Mount::GetPilotedMount( player );

		mountData 				= ownerMount.GetMountData();
		USceneComponent parent 	= player.GetViewmodelComponent().ViewModelTranslationRoot;
		FTransform transArms 	= parent.GetRelativeTransform();
		transArms.SetLocation( weapDefaultOffset );
		transArms.SetRotation( transArms.Rotator().Compose(ARM_ROTHACK) );

		FTransform transWeap	= parent.GetRelativeTransform();
		USkeletalMesh armsMesh 	= player.GetViewmodelArms().GetSkeletalMeshAsset();		
		USkeletalMesh weapMesh 	= mountData.Asset.mesh1P;
		
		armsVM.AttachToComponent( parent );
		armsVM.SetRelativeTransform(transArms);
		armsVM.SetHiddenInGame(false);	
		armsVM.SetSkeletalMeshAsset(armsMesh);
		armsVM.SetCollisionEnabled(ECollisionEnabled::NoCollision);

		mountVM.AttachToComponent( parent );
		mountVM.SetRelativeTransform(transWeap);
		mountVM.SetHiddenInGame(false);	
		mountVM.SetSkeletalMeshAsset(weapMesh);
		mountVM.SetCollisionEnabled(ECollisionEnabled::NoCollision);
		
		armsVM.SetAnimClass(mountData.Asset.mesh1P_AnimBP);
		mountVM.SetAnimClass(mountData.Asset.mesh1P_AnimBP);

		//since this "weapon" isn't real, the animBP's regular accessors for player/weapon/etc will not work. These are overrides
		UAS_MountViewModelAnimInstance armsAnimBP 	= Cast<UAS_MountViewModelAnimInstance>(armsVM.GetAnimInstance());
		armsAnimBP.offhandVMProto_OwningActor 		= player;
		armsAnimBP.offhandVMProto_weaponVM 			= mountVM;

		UAS_MountViewModelAnimInstance weapAnimBP 	= Cast<UAS_MountViewModelAnimInstance>(mountVM.GetAnimInstance());
		weapAnimBP.offhandVMProto_OwningActor 		= player;
		weapAnimBP.offhandVMProto_weaponVM 			= mountVM;
						
		//hide player arm		
		player.GetViewmodelWeapon().HideBoneByName(LEFT_ARM_BONE, EPhysBodyOp::PBO_None );
		armsVM.HideBoneByName(RIGHT_ARM_BONE, EPhysBodyOp::PBO_None );

		ScriptCallbacks().localClient_OnTPPCameraChanged.AddUFunction( this, n"OnTPPCamChange" );
		ECameraIndex camIndex = Cast<AAS_PlayerEntity>(player).GetCameraManagerComponent().CurCam;
		OnTPPCamChange(camIndex);
	}

	UFUNCTION()
	void OnTPPCamChange( ECameraIndex camIndex )
	{
		switch( camIndex )
		{
			case ECameraIndex::DEFAULT:
			{
				bool usesCodeAnim = ANCVehicle::Uses1PCodeAnim();
				armsVM.SetHiddenInGame(usesCodeAnim);
				mountVM.SetHiddenInGame(usesCodeAnim);
				break;
			}
			
			case ECameraIndex::TPP:
			{
				armsVM.SetHiddenInGame(true);
				mountVM.SetHiddenInGame(true);
				break;
			}
		}
	}

	UFUNCTION()
	private void OnPilotPlayActivity(EMountMontageACT activity, UAS_VehicleMountAssetData assetData,ANCPlayerCharacter pilot, AAS_VehicleMountOffhandVM cl_offhandVM)
	{
		switch( activity )
		{
			case EMountMontageACT::SCRIPT_DISMOUNT:
			case EMountMontageACT::SCRIPT_DEATH:
			{
				UAnimMontage exitAnim 	= assetData.Anim.animSheet.montages[activity].Mount1P;
				float32 delay 			= exitAnim.GetPlayLength() - 0.2f;
				System::SetTimer( this, n"DestroyOnExitAnimEnd", delay, false );
				
			}break;

			default:
				break;
		}		
	}

	UFUNCTION()
	private void DestroyOnExitAnimEnd()
	{
		this.Destroy();
	}

	UFUNCTION(BlueprintOverride)
	void EndPlay(EEndPlayReason EndPlayReason)
	{		
		if ( !IsValid(ownerPlayer) )
			return;
		
		ScriptCallbacks().localClient_OnTPPCameraChanged.Unbind( this, n"OnTPPCamChange" );

		//show player arm
		ownerPlayer.GetViewmodelWeapon().UnHideBoneByName(LEFT_ARM_BONE);
		armsVM.HideBoneByName(LEFT_ARM_BONE, EPhysBodyOp::PBO_None);
		
		//reset values
		ownerPlayer.CameraRoot.SetRelativeLocation(FVector::ZeroVector);
		
		ownerPlayer = nullptr; //helps kill the tick
	}

/****************************************************************\

██       ██████   ██████  ██  ██████ 
██      ██    ██ ██       ██ ██      
██      ██    ██ ██   ███ ██ ██      
██      ██    ██ ██    ██ ██ ██      
███████  ██████   ██████  ██  ██████

\****************************************************************/	
	UFUNCTION(BlueprintOverride)
	void Tick(float DeltaSeconds)
	{	
		if ( !IsValid(ownerPlayer) || !IsValid(ownerMount) )
			return;

		if ( ownerMount.GetVehicleState() != EVehicleActiveState::Vehicle_Piloted )
			return;

		USkeletalMeshComponent realWeapVM 	= ownerPlayer.GetViewmodelWeapon();
		ANCWeapon activeWeapon 				= ownerPlayer.GetActiveWeapon();
		EWeaponState weapState 				= IsValid(activeWeapon) ? activeWeapon.GetWeaponState() : EWeaponState::ScriptControl;
		int weapSlot 						= ownerPlayer.GetActiveWeaponSlot();
		//making sure the anim is active on reload/rechamber helps get the hand back on the reins faster
		bool isReloading 					= weapState == EWeaponState::Reloading && realWeapVM.GetAnimInstance().GetCurrentActiveMontage() != nullptr;
		bool isReChambering					= weapState == EWeaponState::Rechambering && realWeapVM.GetAnimInstance().GetCurrentActiveMontage() != nullptr;		
		bool showBothRealHands				= isReloading || isReChambering;
		//hack -> need a systemic method for this
		bool isRealLeftHandTac 				= IsValid(activeWeapon) && activeWeapon.WeaponClass == n"Weap_Automated_Drone_Charges";

		//Print( f"weapState{ weapState}");
		
		//real VM left
		if ( (!showBothRealHands && !isRealLeftHandTac) && !realWeapVM.IsBoneHiddenByName(LEFT_ARM_BONE) )
			realWeapVM.HideBoneByName(LEFT_ARM_BONE, EPhysBodyOp::PBO_None );
		else if ( (showBothRealHands || isRealLeftHandTac ) && realWeapVM.IsBoneHiddenByName(LEFT_ARM_BONE) )
			realWeapVM.UnHideBoneByName(LEFT_ARM_BONE );
		
		//offhand VM left
		if ( (showBothRealHands || isRealLeftHandTac ) && !armsVM.IsBoneHiddenByName(LEFT_ARM_BONE) )
			armsVM.HideBoneByName(LEFT_ARM_BONE, EPhysBodyOp::PBO_None );
		else if ( (!showBothRealHands && !isRealLeftHandTac ) && armsVM.IsBoneHiddenByName(LEFT_ARM_BONE) )	
			armsVM.UnHideBoneByName(LEFT_ARM_BONE );

		//offhand VM right
		if ( !isRealLeftHandTac && !armsVM.IsBoneHiddenByName(RIGHT_ARM_BONE))	
			armsVM.HideBoneByName(RIGHT_ARM_BONE, EPhysBodyOp::PBO_None );
		else if ( isRealLeftHandTac && armsVM.IsBoneHiddenByName(RIGHT_ARM_BONE))
			armsVM.UnHideBoneByName(RIGHT_ARM_BONE );

		//capped pitch
		const float MAX_PITCH_ARMS 		= 50;
		const float MIN_PITCH_ARMS_RIDE	= -20;
		const float MIN_PITCH_ARMS_JUMP = -45;
		float minPitchArms 			= ownerMount.VehicleMovementMode == ENCVehicleMovementMode::Falling ? MIN_PITCH_ARMS_JUMP : MIN_PITCH_ARMS_RIDE;
		FRotator armsRot 			= armsVM.GetRelativeRotation();
		FTransform newTransArms 	= GetCappedPitchTransform( armsRot, MAX_PITCH_ARMS, minPitchArms, weapDefaultOffset, ARM_ROTHACK );
		
		const float MAX_PITCH_WEAP 		= 0;
		const float MIN_PITCH_WEAP_RIDE	= -10;
		const float MIN_PITCH_WEAP_JUMP = -40;
		float minPitchWeap 			= ownerMount.VehicleMovementMode == ENCVehicleMovementMode::Falling ? MIN_PITCH_WEAP_JUMP : MIN_PITCH_WEAP_RIDE;
		FRotator weapRot 			= mountVM.GetRelativeRotation();
		//some mounts like horse feel better to shoot over a lower head (weapon) position
		FVector mountHeadOffset 	= weapDefaultOffset;
		FTransform newTransWeap 	= GetCappedPitchTransform( weapRot, MAX_PITCH_WEAP, minPitchWeap, mountHeadOffset, FRotator::ZeroRotator );

		armsVM.SetRelativeTransform(newTransArms);
		mountVM.SetRelativeTransform(newTransWeap);
	}

	FTransform GetCappedPitchTransform( FRotator curRot, float maxPitch, float minPitch, FVector offset, FRotator offsetRot )
	{
		FRotator camRot = ownerPlayer.PlayerCameraComponent.GetRelativeRotation();

		float pitch = 0; 
		if ( camRot.Pitch > maxPitch )
			pitch = maxPitch - camRot.Pitch;
		else if ( camRot.Pitch < minPitch )
			pitch = minPitch - camRot.Pitch;

		FRotator newRot = curRot;
		newRot.Pitch 	= pitch;
		newRot.Roll 	= 0;

		FTransform deltaTrans;
		deltaTrans.SetRotation( newRot );
		FTransform baseTrans;
		baseTrans.SetLocation( offset );
		baseTrans.SetRotation( baseTrans.Rotator().Compose(offsetRot) );
		
		FTransform cappedTrans = RelativeTransformToWorldTransform( baseTrans, deltaTrans );
		return cappedTrans;
	}
}