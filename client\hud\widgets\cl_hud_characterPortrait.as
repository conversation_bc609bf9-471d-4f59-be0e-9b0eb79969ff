UCLASS( Abstract )
class UAS_PlayerQuickStatusWidget : UNC_DisplayWidget
{
	default StateOnConstruction = EDisplayConstructVisibility::Hidden;
	default HideVisibility		= ESlateVisibility::Collapsed;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage playerPortrait;

	private const AAS_PlayerEntity currentPlayer;

	private const int ALIVE_PLAYER_STATE = 0;
	private const int DOWNED_PLAYER_STATE = 1;
	private const int DEAD_PLAYER_STATE = 2;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			// Respawning
			scriptCallbacks.server_onRespawnUnavailable.AddUFunction( this, n"OnRespawnUnavailable" );
		}
	}

	void SetPlayer( const AAS_PlayerEntity player )
	{
		if ( IsValid( currentPlayer ) )
		{
			currentPlayer.HealthComponent.OnNetworkedHealthChanged.Unbind( this, n"OnHealthChanged" );
			currentPlayer.classManager.client_onClassIndexChangedCallback.Unbind( this, n"OnClassChanged" );
		}

		currentPlayer = player;

		bool validPlayer = IsValid( player );
		if ( validPlayer )
		{
			AAS_PlayerEntity localPlayer = Client_GetLocalASPawn();
			if ( IsValid( localPlayer ) )
			{
				UMaterialInstanceDynamic portraitMaterial = playerPortrait.GetDynamicMaterial();
				if ( IsValid( portraitMaterial ) )
				{
					// We want to show red or blue depending on
					portraitMaterial.SetScalarParameterValue( n"UseAffinityColors", MaterialParameter::GetTrueFalseFloat( true ) );
					portraitMaterial.SetScalarParameterValue( n"ShowEnemyState", MaterialParameter::GetTrueFalseFloat( currentPlayer.GetTeam() != localPlayer.GetTeam() ) );
				}
			}

			player.HealthComponent.OnNetworkedHealthChanged.AddUFunction( this, n"OnHealthChanged" );
			player.classManager.client_onClassIndexChangedCallback.AddUFunction( this, n"OnClassChanged" );
			OnClassChanged( GameplayTags::Dev_Invalid, player.classManager.GetClassIndex() );
			OnHealthChanged( 0, player.GetHealth() );
		}

		ShowOrHide( validPlayer );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnHealthChanged( float32 oldHealth, float32 newHealth )
	{
		bool isAlive = newHealth > 0.0f;

		UMaterialInstanceDynamic portraitMaterial = playerPortrait.GetDynamicMaterial();
		if ( IsValid( portraitMaterial ) )
		{
			portraitMaterial.SetScalarParameterValue( n"PlayerState", isAlive ? ALIVE_PLAYER_STATE : DOWNED_PLAYER_STATE );
		}

		SetRenderOpacity( isAlive ? 1.0f : 0.5f );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnClassChanged( FGameplayTag oldValue, FGameplayTag newValue )
	{
		AAS_PlayerEntity localPlayer = Client_GetLocalASPawn();
		if ( !IsValid( currentPlayer ) || !IsValid( localPlayer ) )
			return;

		UMaterialInstanceDynamic portraitMaterial = playerPortrait.GetDynamicMaterial();
		if ( IsValid( portraitMaterial ) )
		{
			UAS_ClassSystem classSystem = Classes();
			if ( IsValid( classSystem ) )
			{
				FClassDataStruct data = classSystem.GetClassData( newValue );
				portraitMaterial.SetTextureParameterValue( MaterialParameter::PORTRAIT_TEXTURE, data.characterSettingsAsset.CharacterIcon );
			}
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnRespawnUnavailable( AAS_PlayerEntity inPlayer )
	{
		if ( inPlayer != currentPlayer )
			return;

		if ( !IsValid( inPlayer ) )
		{
			return;
		}

		UMaterialInstanceDynamic portraitMaterial = playerPortrait.GetDynamicMaterial();
		if ( IsValid( portraitMaterial ) )
		{
			portraitMaterial.SetScalarParameterValue( n"PlayerState", inPlayer.IsAlive() ? ALIVE_PLAYER_STATE : DEAD_PLAYER_STATE );
		}
	}
}