UAS_LevelCameraManager GetLevelCameraManager()
{
	UAS_LevelCameraManager result = Cast<UAS_LevelCameraManager>( UNCGameplaySystemsSubsystem::Get_ClientSystem( GetCurrentWorld(), UAS_LevelCameraManager::StaticClass() ) );
	return result;
}

UCLASS( Abstract )
class UAS_LevelCameraManager : UNCGameplaySystem_Client
{
	UPROPERTY( EditDefaultsOnly, BlueprintHidden )
	private FName levelSpectateCameraTag = n"LevelSpectateCamera";

	UPROPERTY( EditDefaultsOnly, BlueprintHidden )
	private FName characterSelectCameraTag = n"CharacterSelect";

	UPROPERTY( EditDefaultsOnly, BlueprintHidden )
	private FName matchIntroCameraTag = n"MatchIntro";

	UPROPERTY( EditDefaultsOnly, BlueprintHidden )
	private float defaultFadeTime = 2.0f;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden )
	private float quickFadeTime = 0.25f;

	private ACameraActor introCamera;
	private FTimerHandle cameraFallbackTimerHandle;

	UFUNCTION( BlueprintOverride )
	private void Initialize()
	{
		UCL_ScriptCallbacks clientCallbacks = ClientCallbacks();
		if ( IsValid( clientCallbacks ) )
		{
			clientCallbacks.OnGamePhaseChanged.AddUFunction( this, n"OnGamePhaseChanged" );
			OnGamePhaseChanged( GameConst::INDEX_NONE, GetGamePhase() );
		}
	}

	UFUNCTION( BlueprintEvent )
	private void OnGamePhaseChanged( int oldState, int newState )
	{

		if ( System::IsValidTimerHandle( cameraFallbackTimerHandle ) )
		{
			// If we change phases at all and have held on to a handle from another phase, clear it
			System::ClearAndInvalidateTimerHandle( cameraFallbackTimerHandle );
		}

		if ( oldState == GamePhase::WAITING_FOR_PLAYERS )
		{
			ANCPlayerController controller = Client_GetLocalPlayerController();
			if ( IsValid( controller ) )
			{
				controller.ClearClientOverrideSpectateActorWithStencilOutlines();
			}
		}

		if ( newState == GamePhase::WAITING_FOR_PLAYERS )
		{
			EnableLevelSpectateCamera();
		}
		else if ( newState == GamePhase::CHARACTER_SELECT )
		{
			// TODO @robin @jmccarty @tnaselow: We need to make sure we have the level instance loaded before doing this, see UAS_CharacterSelectManager
			EnableCharacterSelectCamera();
		}
		else if ( newState == GamePhase::MATCH_INTRO )
		{
			// TODO @robin @jmccarty @tnaselow @mark: We need to make sure we have the level instance loaded before doing this, see UAS_MatchIntroManager
			EnableMatchIntroCamera();
		}
		else if ( oldState == GamePhase::CHARACTER_SELECT && newState > GamePhase::CHARACTER_SELECT )
		{
			DefaultFadeFromWhite();

			ANCPlayerController controller = Client_GetLocalPlayerController();
			if ( IsValid( controller ) )
			{
				controller.ClearClientOverrideSpectateActorWithStencilOutlines();
			}
		}
		else if ( oldState == GamePhase::MATCH_INTRO && newState > GamePhase::MATCH_INTRO )
		{
			DefaultFadeFromWhite();

			ANCPlayerController controller = Client_GetLocalPlayerController();
			if ( IsValid( controller ) )
			{
				controller.ClearClientOverrideSpectateActorWithStencilOutlines();
			}
		}
	}

	private void EnableLevelSpectateCamera()
	{
		ANCPlayerController controller = Client_GetLocalPlayerController();
		if ( IsValid( controller ) )
		{
			introCamera = GetRandomCineCameraWithTag( levelSpectateCameraTag );
			if ( IsValid( introCamera ) )
			{
				controller.SetClientOverrideSpectateActor( introCamera );
			}
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void EnableCharacterSelectCamera()
	{
		ANCPlayerController controller = Client_GetLocalPlayerController();
		if ( IsValid( controller ) )
		{
			ACameraActor camera = GetRandomCineCameraWithTag( characterSelectCameraTag );
			if ( IsValid( camera ) )
			{
				QuickFadeFromBlack();
				controller.SetClientOverrideSpectateActor( camera );
			}
			else
			{
				InstantFadeToColor( FLinearColor::Black );
				cameraFallbackTimerHandle = System::SetTimer( this, n"EnableCharacterSelectCamera", 0.05f, false );
			}
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void EnableMatchIntroCamera()
	{
		ANCPlayerController controller = Client_GetLocalPlayerController();
		if ( IsValid( controller ) )
		{
			ACameraActor camera = GetRandomCineCameraWithTag( matchIntroCameraTag );
			if ( IsValid( camera ) )
			{
				QuickFadeFromBlack();
				controller.SetClientOverrideSpectateActor( camera );
			}
			else
			{
				InstantFadeToColor( FLinearColor::Black );
				cameraFallbackTimerHandle = System::SetTimer( this, n"EnableMatchIntroCamera", 0.05f, false );
			}
		}
	}

	private ACameraActor GetRandomCineCameraWithTag( FName tag )
	{
		ACameraActor result;

		TArray<AActor> cameraActors;
		GetAllActorsOfClassWithTag( tag, cameraActors );

		if ( cameraActors.Num() > 0 )
		{
			result = Cast<ACameraActor>( cameraActors[RandRangeExlusive( 0, cameraActors.Num() )] );

			if ( !IsValid( result ) )
			{
				ScriptError_Silent_WithBug( "GetRandomCineCameraWithTag failed to cast camera type",
											"jmccarty",
											f"Failed to cast camera with tag {tag}" );
			}
		}

		return result;
	}

	private void DefaultFadeFromWhite()
	{
		UAS_HUD_ScreenFadeManager screenFade = ScreenFade();
		if ( IsValid( screenFade ) )
		{
			screenFade.ScreenFadeFromColorToColor( FLinearColor::White, FLinearColor( 1, 1, 1, 0 ), defaultFadeTime );
		}
	}

	private void DefaultFadeFromBlack()
	{
		UAS_HUD_ScreenFadeManager screenFade = ScreenFade();
		if ( IsValid( screenFade ) )
		{
			screenFade.ScreenFadeFromColorToColor( FLinearColor::Black, FLinearColor( 0, 0, 0, 0 ), defaultFadeTime );
		}
	}

	private void QuickFadeFromBlack()
	{
		UAS_HUD_ScreenFadeManager screenFade = ScreenFade();
		if ( IsValid( screenFade ) )
		{
			screenFade.ScreenFadeFromColorToColor( FLinearColor::Black, FLinearColor( 0, 0, 0, 0 ), quickFadeTime );
		}
	}

	private void InstantFadeToColor( FLinearColor toColor )
	{
		UAS_HUD_ScreenFadeManager screenFade = ScreenFade();
		if ( IsValid( screenFade ) )
		{
			screenFade.ScreenFadeToColor( toColor, 0.0f );
		}
	}

	ACameraActor TryToGetIntroCamera()
	{
		return introCamera;
	}
}