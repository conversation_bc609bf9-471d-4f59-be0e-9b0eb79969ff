enum EPinnableLaneSide
{
	LEFT,
	RIGHT,

	_count	
}

enum EPinnableLanes
{
	Pings,
	NamePlates,
	Objectives,	
	Default,

	_count	
}

struct FPinnableWidgetData
{
	UAS_PinnableWidget widget;

	UPROPERTY()
	FName socketName;

	USceneComponent trackedActorComponent;

	//use the actor bounds to add an in world offset.
	UPROPERTY( DisplayName="Use Actor Bounds as Z offset" )
	bool useActorBounds;
	
	//0 is the bottom of the bounding box, 1 is the top. values outside of that are beyond the bounding box
	UPROPERTY( DisplayName="   ►  Actor Bounds Z Alignment", Meta=(Editcondition = "useActorBounds", EditConditionHides ) ) 
	private float32 actorBoundsFrac;

	UPROPERTY()
	private FVector worldOffset;

	//pixel offset based off a 1920x1080 resolution
	UPROPERTY()
	FVector2D screenOffset;

	UPROPERTY()
	FVector2D alignment = FVector2D(0.5,0.5);

	// If this widget goes offscreen, do we indicate where it is using the offscreen indicator widget?
	UPROPERTY()
	private bool isPinnable;
	
	UPROPERTY()
	EPinnableLanes PinLane = EPinnableLanes::Default;
	//top/bottom padding... not x,y
	UPROPERTY()
	FVector2D pinStackPadding = FVector2D(22,22);
		
	UPROPERTY()
	UAS_PinnableWidgetVisibilitySettings visibilitySettings;

	FPinnableWidgetData()
	{
		isPinnable	 = true;
		socketName	 = NAME_None;
		worldOffset	 = FVector::ZeroVector;
		screenOffset = FVector2D::ZeroVector;
		alignment	 = FVector2D(0.5,0.5);
	}	

	USceneComponent GetTrackedActorComponent() const
	{
		return trackedActorComponent;
	}

	AActor GetTrackedActor() const
	{
		AActor trackedActor = IsValid(trackedActorComponent) ? trackedActorComponent.GetOwner() : nullptr;
		return trackedActor;
	}

	AAS_WaypointMarker GetTrackedWaypointMarker() const
	{
		AActor trackedActor = GetTrackedActor();		
		while( true )//do while
		{
			AActor parentActor 	= IsValid(trackedActor) ? trackedActor.GetAttachParentActor() : nullptr;
			if ( IsValid(parentActor) )
			{
				trackedActor = parentActor;
				if ( trackedActor.IsA(AAS_WaypointMarker::StaticClass()))
					return Cast<AAS_WaypointMarker>(trackedActor);
			}
			else
				break;
		}

		return nullptr;
	}

	AActor GetTrackedRootActor() const
	{
		AActor trackedActor = GetTrackedActor();		
		while( true )//do while
		{
			AActor rootActor 	= IsValid(trackedActor) ? trackedActor.GetAttachParentActor() : nullptr;
			if ( IsValid(rootActor) )
				trackedActor = rootActor;
			else
				break;
		}

		return trackedActor;
	}

	void SetIsPinnable( bool value )
	{
		isPinnable = value;
		if ( IsValid(widget) )
			widget.OnPinnedChanged(widget.GetIsPinned());
	}

	bool GetIsPinnable()
	{
		return isPinnable;
	}

	FVector GetWorldOffset()
	{
		FVector boundsOffset;
		if ( useActorBounds )
		{
			AActor trackedActor = GetTrackedRootActor();
			if ( IsValid(trackedActor) )
			{
				FVector boxOrigin;
				FVector boxExtent;
				trackedActor.GetActorBounds( true, boxOrigin, boxExtent, false );
				boundsOffset = FVector(0,0,boxExtent.Z*2*actorBoundsFrac);
				//DrawDebugBox( boxOrigin, trackedActor.GetActorRotation(), cachedExtent, 0.1 );
			}
		}

		return worldOffset + boundsOffset;
	}

	// TODO: This really should be automated in code or something. DeepCopy() or something?
	FPinnableWidgetData( FPinnableWidgetData dataToCopy )
	{
		CopyDataAndTrackedActor(dataToCopy);
	}

	void CopyDataAndTrackedActor(FPinnableWidgetData dataToCopy)
	{
		trackedActorComponent = dataToCopy.trackedActorComponent;

		CopyData(dataToCopy);		
	}

	void CopyData(FPinnableWidgetData dataToCopy)
	{
		widget				  = dataToCopy.widget;	
		socketName			= dataToCopy.socketName;		
		useActorBounds 		= dataToCopy.useActorBounds;
		actorBoundsFrac		= dataToCopy.actorBoundsFrac;
		worldOffset			= dataToCopy.worldOffset;
		screenOffset		= dataToCopy.screenOffset;
		alignment			= dataToCopy.alignment;
		isPinnable			= dataToCopy.isPinnable;
		PinLane 			= dataToCopy.PinLane;
		pinStackPadding		= dataToCopy.pinStackPadding;
		visibilitySettings 	= dataToCopy.visibilitySettings;
	}

	void CopyOffsetData(FPinnableWidgetData dataToCopy)
	{
		socketName			= dataToCopy.socketName;		
		useActorBounds 		= dataToCopy.useActorBounds;
		actorBoundsFrac		= dataToCopy.actorBoundsFrac;
		worldOffset			= dataToCopy.worldOffset;
		screenOffset		= dataToCopy.screenOffset;
		alignment			= dataToCopy.alignment;
	}
}

UCLASS()
class UAS_PinnableWidgetSettings : UDataAsset
{
	UPROPERTY()
	TSubclassOf<UAS_PinnableWidget> widgetClass;

	UPROPERTY(Meta = (DisplayAfter="widgetClass"))
	bool playAppearAnimation;

	// 1P sound to play when this widget appears
	UPROPERTY( Meta = (DisplayAfter="playAppearAnimation"))
	UNCAudioAsset appearSound;

	UPROPERTY()
	FPinnableWidgetData baseData;

}

UCLASS()
class UAS_PinnableWidgetVisibilitySettings : UDataAsset
{
	UPROPERTY( Meta = ( Category = "Distance" ) )
	bool setAlphaBasedOnDistance = false;

	// -1 means none
	UPROPERTY( Meta = ( EditCondition = "setAlphaBasedOnDistance", EditConditionHides, Category = "Distance" ) )
	float maxVisibleDistance = -1.0f;

	// -1 means none
	UPROPERTY( Meta = ( EditCondition = "setAlphaBasedOnDistance", EditConditionHides, Category = "Distance" ) )
	float minVisibleDistance = -1.0f;

	// Distance over which the alpha will fade in or out
	UPROPERTY( Meta = ( EditCondition = "setAlphaBasedOnDistance", EditConditionHides, Category = "Distance" ) )
	float closeAlphaTransitionDistance = 128.0f;

	// Distance over which the alpha will fade in or out
	UPROPERTY( Meta = ( EditCondition = "setAlphaBasedOnDistance", EditConditionHides, Category = "Distance" ) )
	float farAlphaTransitionDistance = 512.0f;

	UPROPERTY( Meta = ( Category = "FOV" ) )
	bool setAlphaBasedOnAngle = false;

	UPROPERTY( Meta = ( EditCondition = "setAlphaBasedOnAngle", EditConditionHides, Category = "FOV" ) )
	float visibleAngleFOV = 120.0f;

	UPROPERTY( Meta = ( EditCondition = "setAlphaBasedOnAngle", EditConditionHides, Category = "FOV" ) )
	float fadeAngleRange = 10.0f;

	UPROPERTY( Meta = ( EditCondition = "setAlphaBasedOnAngle", EditConditionHides, Category = "FOV" ) )
	bool invertVisibleAngleFOV = false;

	UPROPERTY( Meta = ( EditCondition = "setAlphaBasedOnAngle", EditConditionHides, Category = "FOV" ) )
	bool ignoreFriendlies = false;

	UPROPERTY( Meta = ( EditCondition = "setAlphaBasedOnAngle", EditConditionHides, Category = "FOV" ) )
	bool ignoreEnemies = false;

	UPROPERTY( Meta = ( Category = "ADS" ) )
	bool fadeOnAds = false;

	UPROPERTY( Meta = ( EditCondition = "fadeOnAds", EditConditionHides, Category = "ADS" ) )
	float adsAngleFOV = 120.0f;

	UPROPERTY( Meta = ( EditCondition = "fadeOnAds", EditConditionHides, Category = "ADS" ) )
	float minAdsAlpha = 0.1f;

	UPROPERTY( Meta = ( EditCondition = "fadeOnAds", EditConditionHides, DisplayName = "Ignore Friendlies" ) )
	bool ignoreFriendliesAds = false;

	UPROPERTY( Meta = ( EditCondition = "fadeOnAds", EditConditionHides, DisplayName = "Ignore Enemies" ) )
	bool ignoreEnemiesAds = false;

	UPROPERTY( Category = "Distance" )
	bool setScaleBasedOnDistance;

	// Default scaling rules are the farther away you are, the smaller it is
	UPROPERTY( Meta = ( EditCondition = "setScaleBasedOnDistance", EditConditionHides, Category = "Distance" ) )
	bool removeScaleInversion = false;

	UPROPERTY( Meta = ( EditCondition = "setScaleBasedOnDistance", EditConditionHides, Category = "Distance" ) )
	FFloatInterval scaleDistance;

	UPROPERTY( Meta = ( EditCondition = "setScaleBasedOnDistance", EditConditionHides, ClampMin = 0, ClampMax = 1, Category = "Distance" ) )
	FFloatInterval distanceScale;
	default distanceScale.Min = 0.7f;
	default distanceScale.Max = 1.0f;
}

event void FEvent_OnPinnableWidgetCallback( UAS_PinnableWidget Widget, ANCPlayerCharacter user );

UCLASS( Abstract )
class UAS_PinnableWidget : UUserWidgetDefault
{
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UPanelWidget pinnedWidgetPanel;

	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UPanelWidget mainWidgetPanel;

	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UPanelWidget offscreenIndicatorPanel;

	UPROPERTY( Transient, NotEditable, Meta = ( BindWidgetAnimOptional ) )
	private UWidgetAnimation appear;

	// Must be manually set via SetOwnerActor(). Not guaranteed to be valid!
	protected const AActor ownerActor;

	UPROPERTY( BlueprintReadOnly, NotEditable )
	protected UAS_PinnableWidgetSettings settings;
	default settings = nullptr;

	UPROPERTY( BlueprintReadOnly, NotEditable )
	private bool isPinned;

	UPROPERTY( BlueprintReadOnly, NotEditable )
	bool isOnScreen;

	UPROPERTY( BlueprintReadOnly, NotEditable )
	protected float pinAngle;

	UPROPERTY( BlueprintReadOnly, NotEditable )
	protected float distToTarget;

	protected float alphaFromDistance = 1;
	protected float alphaFromAngle = 1;
	protected float alphaFromAds = 1;
	protected float scaleFromDistance = 1.0f;

	protected FVector lastTrackedPosition;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UImage innerArrowImage;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UOverlay PinnedArrowGroup;

	protected FVector worldLocation;
	protected bool globalIsVisible;

	private FVector2D cachedBoxPosition;
	private FVector2D cachedStackOffset;	//this is a push from the top
	private FVector2D cachedStackClamp;		//this is a push from the bottom
	private FVector2D cachedStackGoal; 		//this is only used for speed calculations
	private FVector2D cachedEllipseOffset;
	int cachedZOrder;

	FEvent_OnPinnableWidgetCallback	OnAppear;	
	FEvent_OnPinnableWidgetCallback OnIsPinnedChanged;

	FVector2D GetRealPosition()
	{
		return cachedBoxPosition + cachedStackOffset + cachedStackClamp + cachedEllipseOffset;
	}	

	FVector2D GetBoxStackEllipseSolve()
	{
		return cachedBoxPosition + cachedStackOffset + cachedEllipseOffset;
	}

	FVector2D GetBoxAndStackSolve()
	{
		return cachedBoxPosition + cachedStackOffset;
	}

	FVector2D GetBoxAndEllipseSolve()
	{
		return cachedBoxPosition + cachedEllipseOffset;
	}

	void SetBoxSolve( FVector2D boxSolve)
	{
		cachedBoxPosition = boxSolve;
	}

	FVector2D GetBoxSolve()
	{
		return cachedBoxPosition;
	}
	
	void SetStackSolve(FVector2D stackSolve )
	{
		cachedStackOffset = stackSolve;
	}

	FVector2D GetStackSolve()
	{
		return cachedStackOffset;
	}

	void SetStackGoal(FVector2D stackGoal )
	{
		cachedStackGoal = stackGoal;
	}

	FVector2D GetStackGoal()
	{
		return cachedStackGoal;
	}

	void SetStackClamp(FVector2D stackClamp )
	{
		cachedStackClamp = stackClamp;
	}

	FVector2D GetStackClamp()
	{
		return cachedStackClamp;
	}
	
	void SetEllipseSolve(FVector2D ellipseSolve )
	{
		cachedEllipseOffset = ellipseSolve;
	}

	FVector2D GetEllipseSolve()
	{
		return cachedEllipseOffset;
	}


	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		OnPinnedChanged( false );		
	}

	void SetIsPinned( bool pinned )
	{
		if ( isPinned == pinned )
			return;

		isPinned = pinned;

		OnPinnedChanged( isPinned );
	}

	void SetOnScreen( bool onScreen )
	{
		if ( isOnScreen == onScreen )
			return;

		isOnScreen = onScreen;
		OnScreenChanged( onScreen );
	}

	protected void OnScreenChanged( bool onScreen )
	{
		if ( IsValid( settings ) && settings.baseData.GetIsPinnable() )
			return;

		if ( onScreen )
			SetWidgetVisibilitySafe( mainWidgetPanel, ESlateVisibility::HitTestInvisible );
		else
			SetWidgetVisibilitySafe( mainWidgetPanel, ESlateVisibility::Collapsed );
	}	

	void OrientPinnedWidget( float pinAngleRadians )
	{
		float pinAngleDegrees = 0.0;
		if ( isPinned )
		{
			pinAngleDegrees = Math::RadiansToDegrees( pinAngleRadians );
		}

		pinAngle = pinAngleDegrees;

		PinnedArrowGroup.SetRenderTransformAngle( pinAngleDegrees );
	}

	access solver = private, UAS_PinnedWidgetManagerComponent;
	bool isPinnedArrowVisible = true;
	access:solver void SetPinnedArrowVisibility( bool on )
	{
		if ( isPinnedArrowVisible == on )
			return;
		isPinnedArrowVisible = on;

		SetWidgetVisibilitySafe( PinnedArrowGroup, isPinnedArrowVisible ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Hidden );
	}

	bool GetIsPinned()
	{
		return isPinned;
	}

	access data = protected, FPinnableWidgetData;
	access:data void OnPinnedChanged( bool pinned )
	{
		if ( pinned )
		{
			SetWidgetVisibilitySafe( mainWidgetPanel, ESlateVisibility::Collapsed );
			SetWidgetVisibilitySafe( offscreenIndicatorPanel, ESlateVisibility::HitTestInvisible );
		}
		else
		{
			SetAlignmentInViewport( FVector2D( 0.5, 0.5 ) );
			SetWidgetVisibilitySafe( mainWidgetPanel, ESlateVisibility::HitTestInvisible );
			SetWidgetVisibilitySafe( offscreenIndicatorPanel, ESlateVisibility::Collapsed );
			if ( IsValid( settings ) )
				SetAlignmentInViewport( settings.baseData.alignment );
		}

		OnIsPinnedChanged.Broadcast( this, Client_GetLocalPawn() );
	}

	void SetDistanceToTarget( float newDistToTarget )
	{
		distToTarget = newDistToTarget;
	}

	float GetDistanceToTarget()
	{
		return distToTarget;
	}

	float SetAlphaFromDistance()
	{
		UAS_PinnableWidgetVisibilitySettings visibilitySettings = settings.baseData.visibilitySettings;
		
		float maxDistance 			= visibilitySettings.maxVisibleDistance;
		float minDistance 			= visibilitySettings.minVisibleDistance;
		float closeTransitionDist 	= visibilitySettings.closeAlphaTransitionDistance;
		float farTransitionDist 	= visibilitySettings.farAlphaTransitionDistance;

		if ( distToTarget < 0 || maxDistance < 0 || minDistance < 0 || closeTransitionDist < 0 || farTransitionDist < 0 )
		{
			ScriptError_Silent_WithBug( "Warning! Called get alpha for distance with a negative value.", "mohammad", 
			f"SetAlphaFromDistance: visibilitySettings: {visibilitySettings.GetPathName()}\n distToTarget: {distToTarget}\n maxDistance: {maxDistance}\n minDistance: {minDistance}\n closeTransitionDist: {closeTransitionDist}\n farTransitionDist: {farTransitionDist}" );
		}

		float alpha				= GetAlphaForDistance( distToTarget, maxDistance, minDistance, closeTransitionDist, farTransitionDist );
		alphaFromDistance 		= alpha;
		float offscreenOpacity = __UpdateAlphaInternal();

		return offscreenOpacity;
	}

	float SetAlphaFromAngleToTarget()
	{
		ANCPlayerCharacter localPawn = Client_GetLocalPawn();
		if ( !IsValid( localPawn ) )
			return 0;

		UAS_PinnableWidgetVisibilitySettings visibilitySettings = settings.baseData.visibilitySettings;

		// We can skip all of the below logic and ignore the alpha change if we are ignoring friendlies or ignoring enemies
		bool ignoreTeamWidget = false;
		if ( IsValid( ownerActor ) && ownerActor.CanHaveTeam() )
		{
			ignoreTeamWidget = ( localPawn.GetTeam() == ownerActor.GetTeam() && visibilitySettings.ignoreFriendlies ) ||
							   ( localPawn.GetTeam() != ownerActor.GetTeam() && visibilitySettings.ignoreEnemies );
		}

		if ( !ignoreTeamWidget )
		{
			FVector targetLocation = IsValid( ownerActor ) ? ownerActor.GetActorLocation() : worldLocation;
			float forwardDotAngle  = GetForwardDotAngle( targetLocation, localPawn.GetCameraRotation().ForwardVector, localPawn.GetActorLocation() );

			float maxDistance 			= visibilitySettings.visibleAngleFOV * 0.5;
			float minDistance 			= 0;
			float closeTransitionDist 	= 0;
			float farTransitionDist 	= visibilitySettings.fadeAngleRange;
			
			if ( distToTarget < 0 || maxDistance < 0 || minDistance < 0 || closeTransitionDist < 0 || farTransitionDist < 0 )
			{
				ScriptError_Silent_WithBug( "Warning! Called get alpha for distance with a negative value.", "mohammad", 
				f"SetAlphaFromAngleToTarget: visibilitySettings: {visibilitySettings.GetPathName()}\n distToTarget: {distToTarget}\n maxDistance: {maxDistance}\n minDistance: {minDistance}\n closeTransitionDist: {closeTransitionDist}\n farTransitionDist: {farTransitionDist}" );
			}

			alphaFromAngle		   = GetAlphaForDistance( forwardDotAngle, maxDistance, minDistance, closeTransitionDist, farTransitionDist );

			if ( visibilitySettings.invertVisibleAngleFOV )
			{
				// If we are inverting the FOV we want to change the alpha for things within the FOV not outside
				alphaFromAngle = Math::Max( 1.0f - alphaFromAngle, 0.0f );
			}
		}

		float offscreenOpacity = __UpdateAlphaInternal();
		return offscreenOpacity;
	}

	float SetAlphaFromAds()
	{
		ANCPlayerCharacter localPawn = Client_GetLocalPawn();
		if ( !IsValid( localPawn ) )
			return 0;

		UAS_PinnableWidgetVisibilitySettings visibilitySettings = settings.baseData.visibilitySettings;

		// We can skip all of the below logic and ignore the alpha change if we are ignoring friendlies or ignoring enemies
		bool ignoreTeamWidget = false;
		if ( IsValid( ownerActor ) && ownerActor.CanHaveTeam() )
		{
			ignoreTeamWidget = ( localPawn.GetTeam() == ownerActor.GetTeam() && visibilitySettings.ignoreFriendlies ) ||
							   ( localPawn.GetTeam() != ownerActor.GetTeam() && visibilitySettings.ignoreEnemies );
		}

		if ( visibilitySettings.fadeOnAds && !ignoreTeamWidget )
		{
			FVector targetLocation = IsValid( ownerActor ) ? ownerActor.GetActorLocation() : worldLocation;
			if ( GetForwardDotAngle( targetLocation, localPawn.GetCameraRotation().ForwardVector, localPawn.GetActorLocation() ) < visibilitySettings.adsAngleFOV * 0.5f )
			{
				alphaFromAds = Math::Clamp( 1.0f - localPawn.AdsFraction, visibilitySettings.minAdsAlpha, 1.0f );
			}
			else
			{
				// TODO @jmccarty: We should play with this a bit and see if we can get a transition back to 1
				alphaFromAds = 1.0f;
			}
		}

		float offscreenOpacity = __UpdateAlphaInternal();
		return offscreenOpacity;
	}

	void SetScaleFromDistance()
	{
		UAS_PinnableWidgetVisibilitySettings visibilitySettings = settings.baseData.visibilitySettings;

		// We can use the alpha logic but remap that to our scale range
		float alpha = GetScaleForDistance( distToTarget, visibilitySettings.scaleDistance.Max, visibilitySettings.scaleDistance.Min );

		// Alpha from the GetAlphaForDistance function returns 0 far away and 1 close, we want to invert that for scale but we may want to undo the inversion
		alpha = visibilitySettings.removeScaleInversion ? alpha : 1.0f - alpha;
		alpha = Math::GetMappedRangeValueClamped( FVector2D( 0.0f, 1.0f ), FVector2D( visibilitySettings.distanceScale.Min, visibilitySettings.distanceScale.Max ), alpha );

		if ( !Math::IsNearlyEqual( scaleFromDistance, alpha, 0.001f ) )
		{
			scaleFromDistance = alpha;
			UpdateScaleInternal();
		}
	}

	private bool __isMouseOverTarget = false;
	access IWM = private, UCl_InteractiveWidgetManager;
	access:IWM void SetIsMouseOverTarget( bool isTarget )
	{
		__isMouseOverTarget = isTarget;
		__UpdateAlphaInternal();
	}

	bool GetIsMouseOverTarget()
	{
		return __isMouseOverTarget;
	}

	private float __UpdateAlphaInternal()
	{
		float newOpacity = __isMouseOverTarget ?  1.0 : alphaFromDistance * alphaFromAngle * alphaFromAds;
		
		if ( mainWidgetPanel.RenderOpacity != newOpacity )
			mainWidgetPanel.RenderOpacity = newOpacity;

		if ( offscreenIndicatorPanel.RenderOpacity != newOpacity )
			offscreenIndicatorPanel.RenderOpacity = newOpacity;

		SetWidgetVisibilitySafe( offscreenIndicatorPanel, newOpacity > 0.35 && isPinned ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );

		return newOpacity;
	}

	bool IsPinVisible()
	{
		return offscreenIndicatorPanel.IsVisible();
	}

	bool IsOnscreenVisible()
	{
		return mainWidgetPanel.IsVisible();
	}

	private void UpdateScaleInternal()
	{
		FVector2D newScale = FVector2D::UnitVector * scaleFromDistance;
		SetRenderScale( newScale );
	}

	FVector GetLastTrackedPosition()
	{
		return lastTrackedPosition;
	}

	void SetTrackedPosition( FVector inTrackedPosition )
	{
		lastTrackedPosition = inTrackedPosition;

		AAS_HUD localHud											 = GetLocalHUD();
		localHud.pinnedWidgetManager.GetPinnableWidgetData( this ).trackedActorComponent = nullptr;
	}

	void SetPinnedWidgetSettings( UAS_PinnableWidgetSettings inSettings )
	{
		settings = inSettings;		
		OnSettingsUpdated();
	}

	UAS_PinnableWidgetSettings GetPinnedWidgetSettings() const
	{
		return settings;
	}

	void SetOwnerActor( const AActor newOwnerActor )
	{
		const AActor oldOwner = ownerActor;
		ownerActor			  = newOwnerActor;

		if ( !IsValid( ownerActor ) )
		{
			OnOwnerActorChanged( oldOwner, nullptr );
			return;
		}

		// Set offscreen indicator according to player owner color (if appliccable)
		AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( newOwnerActor );
		if ( IsValid( player ) )
		{
			innerArrowImage.SetColorAndOpacity( GetUIColorForPlayer( player ) );
		}
		else
		{
			ANCDefaultActor actor 			= Cast<ANCDefaultActor>( newOwnerActor );
			AAS_PlayerEntity ownerPlayer 	= IsValid( actor ) ? actor.GetOwnerPlayer() : nullptr;
			if ( IsValid(ownerPlayer) )
				innerArrowImage.SetColorAndOpacity( GetUIColorForPlayer( ownerPlayer ) );
		}

		FPinnableWidgetData& widgetData = GetLocalHUD().pinnedWidgetManager.GetPinnableWidgetData( this );
		// If the widget wants to follow a socket, and the current tracked component doesn't have this socket, try to find a skeletal mesh with the socket (may not succeed)
		if ( widgetData.socketName != NAME_None && !IsValid( widgetData.trackedActorComponent ) )
		{
			TArray<USkeletalMeshComponent> foundSkelMeshComponents;
			ownerActor.GetComponentsByClass( foundSkelMeshComponents );
			for ( USkeletalMeshComponent skelMeshComp : foundSkelMeshComponents )
			{
				if ( skelMeshComp.DoesSocketExist( widgetData.socketName ) )
				{
					widgetData.trackedActorComponent = skelMeshComp;
					break;
				}
			}
		}

		OnOwnerActorChanged( oldOwner, ownerActor );
	}

	void OnOwnerActorChanged( const AActor oldOwner, const AActor newOwner )
	{
	}

	const AActor GetOwnerActor()
	{
		return ownerActor;
	}

	protected void OnSettingsUpdated() 
	{
		if ( GetPinnedWidgetSettings().playAppearAnimation && IsValid( appear ) && !IsAnimationPlaying(appear) )
		{
			PlayAppear();
		}
	}

	UFUNCTION( BlueprintEvent )
	UImage GetIcon()
	{
		return nullptr;
	}

	private float GetForwardDotAngle( FVector targetLocation, FVector localForwardVector, FVector localActorLocation )
	{
		FVector playerCameraForward = localForwardVector;
		FVector playerToOwner		= ( targetLocation - localActorLocation ).GetSafeNormal();
		float forwardDot			= playerCameraForward.DotProduct( playerToOwner );
		return DotToAngles( forwardDot );
	}

	void SetWorldLocation( FVector inWorldLocation )
	{
		worldLocation = inWorldLocation;
	}

	bool GetPositionFromWidgetData( FVector& outPosition, FPinnableWidgetData data )
	{
		// This can run without a tracked actor, which allows for animated screen offset (damage numbers, for example)
		if ( data.GetTrackedActorComponent() != nullptr )
		{
			if ( data.socketName == NAME_None )
			{
				outPosition = data.GetTrackedActorComponent().GetWorldLocation();
				return true;
			}
			else
			{
				USkeletalMeshComponent mesh = Cast<USkeletalMeshComponent>( data.GetTrackedActorComponent() );
				if ( IsValid( mesh ) )
				{
					outPosition = mesh.GetSocketLocation( data.socketName );
					return true;
				}
			}
		}

		return false;
	}

	void PlayAppear()
	{
		UNCAudioAsset soundToPlay 	= GetAppearSound();
		UWidgetAnimation animToPlay = GetAppearAnim();

		PlayWidgetSound( soundToPlay );
		PlayAnimation( animToPlay );
		OnAppear.Broadcast( this, Client_GetLocalASPawn() );
	}

	UWidgetAnimation GetAppearAnim()
	{
		return appear;
	}

	UNCAudioAsset GetAppearSound()
	{
		return settings.appearSound;
	}

		void PlayWidgetSound( UNCAudioAsset soundToPlay )
	{
		Client_EmitSoundUI( soundToPlay );
	}
}

UCLASS( Abstract )
class UAS_PinnableWidget_DynamicText : UAS_PinnableWidget
{
	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UTextBlock TextBlock;
	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UTextBlock TextPinned;

	void SetText( FText mainText, FText pinnedText )
	{
		TextBlock.SetText( mainText );
		TextPinned.SetText( pinnedText );
	}
}

UCLASS( Abstract )
class UAS_PinnableWidget_IconAndText : UAS_PinnableWidget
{
	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UImage icon;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UImage pinnedIcon;
	
	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	protected UTextBlock objectiveText;

	// doesn't do anything by default, your widget has to menually respond to this
	bool useTeamColors;

	void OnSettingsUpdated() override
	{
		UAS_PinnableWidgetSettings_IconAndText iconSettings = Cast<UAS_PinnableWidgetSettings_IconAndText>( settings );
		
		Super::OnSettingsUpdated();

		useTeamColors = iconSettings.useTeamColors;

		if ( iconSettings.icon == nullptr )
		{
			SetWidgetVisibilitySafe( icon, ESlateVisibility::Hidden );
			SetWidgetVisibilitySafe( pinnedIcon, ESlateVisibility::Hidden );
		}
		else
		{
			icon.SetBrushResourceObject( iconSettings.icon );
			pinnedIcon.SetBrushResourceObject( iconSettings.icon );
			SetWidgetVisibilitySafe( icon, iconSettings.OnlyDrawOffscreenElements ? ESlateVisibility::Collapsed : ESlateVisibility::HitTestInvisible );
			SetWidgetVisibilitySafe( pinnedIcon, ESlateVisibility::HitTestInvisible );
			FName colorId 		= CommonUiColorMpcNames::GetColorIdFromEnum( iconSettings.iconColor );
			FLinearColor color	= GetCommonUiMpcColor( colorId );
			icon.SetColorAndOpacity( color );
			pinnedIcon.SetColorAndOpacity( color );
		}

		objectiveText.SetText( iconSettings.text );
		SetWidgetVisibilitySafe( objectiveText, iconSettings.OnlyDrawOffscreenElements ? ESlateVisibility::Collapsed : ESlateVisibility::HitTestInvisible );		
	}

	UFUNCTION( BlueprintOverride )
	UImage GetIcon()
	{
		return icon;
	}

	void SetObjectiveText(FText objText )
	{
		objectiveText.SetText( objText );		
	}
}

UCLASS()
class UAS_PinnableWidgetSettings_IconAndText : UAS_PinnableWidgetSettings
{
	UPROPERTY()
	UTexture2D icon;
	
	UPROPERTY()
	ECommonUiMpcColors iconColor = ECommonUiMpcColors::WHITE;

	UPROPERTY()
	bool useTeamColors;

	UPROPERTY(Meta=(DisplayAfter="widgetClass"))
	FText text;	

	UPROPERTY(Meta=(DisplayAfter="widgetClass"))
	bool OnlyDrawOffscreenElements = false;
}