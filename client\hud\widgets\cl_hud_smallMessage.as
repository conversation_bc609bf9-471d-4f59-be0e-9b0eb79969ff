UCLASS(Abstract)
class UAS_SmallHUDMessage : UUserWidgetDefault
{
    UPROPERTY( BlueprintReadOnly, NotEditable, Meta=( BindWidget ) )
    private UTextBlock messageText;

    UPROPERTY( BlueprintReadOnly, NotEditable, Transient, Meta=( BindWidgetAnim ) )
    private UWidgetAnimation display;

    UPROPERTY()
    private UNCAudioAsset displaySound;

    UFUNCTION()
    void DisplayMessage( FText inMessageText )
    {
        #if !RELEASE
            // Temp, help with cleanup
            if ( !inMessageText.IsFromStringTable() )
                Warning( f"Displaying non-localized message text {inMessageText}" );
        #endif

        messageText.SetText( inMessageText );

        if ( IsValid( displaySound ) )
            Client_EmitSoundUI( displaySound );

        PlayAnimation( display );
    }
} 