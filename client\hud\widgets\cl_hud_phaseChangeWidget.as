UCLASS(Abstract)
class UAS_PhaseChangeWidget : UUserWidgetDefault
{
	UPROPERTY(NotEditable, BindWidget)
	UTextBlock titleText;

	UPROPERTY(NotEditable, BindWidget)
	UTextBlock nextTitle;

	UPROPERTY(Transient, Meta=(BindWidgetAnim))
	UWidgetAnimation titleChange;

	UPROPERTY(Transient, Meta=(BindWidgetAnim))
	UWidgetAnimation _1to2;

	UPROPERTY(Transient, Meta=(BindWidgetAnim))
	UWidgetAnimation _2to3;

	UPROPERTY(Transient, Meta=(BindWidgetAnim))
	UWidgetAnimation appearAnim;

	UPROPERTY(Transient, Meta=(BindWidgetAnim))
	UWidgetAnimation disappearAnim;

	UFUNCTION(BlueprintOverride)
	void Construct()
	{
		SetWidgetVisibilitySafe( this, ESlateVisibility::Hidden );
	}

	void Init( int currentPhase, FText title )
	{
		SetWidgetVisibilitySafe( this, ESlateVisibility::HitTestInvisible );
		InitPhaseAnim( currentPhase );
		titleText.SetText( title );
		nextTitle.SetText( FText() );
		PlayAnimation( appearAnim );

		System::SetTimer( this, n"Disappear", 4.0, false );
	}

	void TransitionToNextPhase( int currentPhase, FText title, FText next )
	{
		SetWidgetVisibilitySafe( this, ESlateVisibility::HitTestInvisible );
		InitPhaseAnim( currentPhase );
		titleText.SetText( title );
		nextTitle.SetText( next );
		PlayAnimation( appearAnim );

		Thread( this, n"PlayNextPhase", currentPhase );
	}

	UFUNCTION()
	void PlayNextPhase( UNCCoroutine co, int currentPhase )
	{
		co.Wait( 1.5 );
		PlayAnimation( titleChange );
		PlayNextPhaseAnim( currentPhase );
		co.Wait( 5.0 );
		Disappear();
	}

	void PlayNextPhaseAnim(int currentPhase)
	{
		if ( currentPhase == 0 )
		{
			PlayAnimation( _1to2 );
		}
		else if ( currentPhase == 1 )
		{
			PlayAnimation( _2to3 );
		}
		else
		{
			ScriptError( f"Unsupported Phase {currentPhase}" );
		}
	}

	UFUNCTION()
	void Disappear()
	{
		Thread( this, n"DisappearThread" );
	}

	UFUNCTION()
	void DisappearThread( UNCCoroutine co )
	{
		PlayAnimation(disappearAnim);
		co.Wait( 3.0 );
		RemoveFromParent();
	}

	void InitPhaseAnim( int currentPhase )
	{
		if ( currentPhase == 0 )
		{
			PlayAnimation( _1to2 );
			PauseAnimation( _1to2 );
			SetAnimationCurrentTime( _1to2, 0.0 );
		}
		else if ( currentPhase == 1 )
		{
			PlayAnimation( _2to3 );
			PauseAnimation( _2to3 );
			SetAnimationCurrentTime( _2to3, 0.0 );
		}
		else
		{
			ScriptError( f"Unsupported Phase {currentPhase}" );
		}
	}
}
