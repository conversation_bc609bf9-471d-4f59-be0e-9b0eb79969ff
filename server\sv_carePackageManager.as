USTRUCT()
struct FCarePackageData
{
	UPROPERTY()
	TArray<FCarePackageSpawnStruct> loot;

	UPROPERTY()
	ECarePackageType type;
}

USTRUCT()
struct FCarePackageIconOverrideData
{
	UPROPERTY()
	FGameplayTag tag;

	UPROPERTY()
	UTexture2D icon;
}

USTRUCT()
struct FCarePackageSpawnStruct
{
	UPROPERTY()
	FGameplayTag tag;

	UPROPERTY()
	FName lootZone;

	UPROPERTY()
	int count = 1;
}

enum ECarePackageZoneType
{
	NEAR_SHIELD_DOME,
	MID_ZONE,
	POI,
}

// Struct so that an array can be contained in a TMap
USTRUCT()
struct FCarePackageZones
{
	TArray<AAS_CarePackageZone> zones;
}

USV_CarePackageManager CarePackageManager()
{
	return Cast<USV_CarePackageManager>( UNCGameplaySystemsSubsystem::Get_ServerSystem( GetCurrentWorld(), USV_CarePackageManager::StaticClass() ) );
}

delegate int FCarePackageCountDelegate( int aliveTeams );

UCLASS()
class USV_CarePackageManager : UNCGameplaySystem_Server
{
	TArray<FCarePackageData> carePackageData;

	UPROPERTY( NotEditable )
	TMap<ECarePackageZoneType, FCarePackageZones> cpZones;

	UPROPERTY( EditDefaultsOnly, Category = "Default Classes" )
	TSubclassOf<AAS_CarePackage> carePackageClass;

	UPROPERTY( EditAnywhere )
	UDataTable carePackageDataTable;

	TArray<FVector> spawnLocations;
	TArray<AFXActor> spawnLocationFXs;
	TArray<AAS_CarePackageMarker> mapMarkers;

	private int carePackageTier = 0;

	private AAS_Blimp deliveryBlimp;
	const float SPAWN_HEIGHT = 10000.0f;

	const FLinearColor SMOKE_COLOR = FLinearColor( 1.0f, 0.37f, 0.0f, 1.0f );

	UFUNCTION( BlueprintOverride )
	void Initialize()
	{
		Init_CarePackageData();

		TArray<AAS_CarePackageZone> allZones;
		GetAllActorsOfClass( AAS_CarePackageZone::StaticClass(), allZones );
		for ( int i = 0; i < allZones.Num(); ++i )
		{
			if ( !cpZones.Contains( allZones[i].zoneType ) )
				cpZones.Add( allZones[i].zoneType, FCarePackageZones() );

			cpZones[allZones[i].zoneType].zones.Add( allZones[i] );
		}

		TArray<AAS_Blimp> allBlimps;
		GetAllActorsOfClass( AAS_Blimp::StaticClass(), allBlimps );
		if ( allBlimps.Num() == 0 )
		{
			Print( "No blimp found! Care packages will spawn in directly" );
		}
		else
		{
			deliveryBlimp = allBlimps[0];
			deliveryBlimp.sv_onMoveComplete.AddUFunction( this, n"OnBlimpMoveComplete" );
		}

		if ( GameModeDefaults().GamemodeRules_CarePackagesOnOneSide )
			ServerCallbacks().OnGamePhaseChanged.AddUFunction( this, n"OnGamePhaseChanged" );
	}

	// TEMP HACK FOR 1v1 Testing on a 4 Team Map
	UFUNCTION()
	private void OnGamePhaseChanged( int OldState, int NewState )
	{
		if ( NewState != GamePhase::PLAYING )
			return;

		TArray<AAS_Frontier_Gate> allGates;
		GetAllActorsOfClass( AAS_Frontier_Gate::StaticClass(), allGates );
		AAS_Frontier_Gate gate;
		if ( allGates.Num() > 0 )
			gate = allGates[0];

		if ( !IsValid( gate ) )
			return;

		AAS_BaseSystem base;
		TArray<int> allTeams = GetAliveTeams();
		base				 = GetBaseForTeam( allTeams[0] );

		if ( !IsValid( base ) )
			return;

		float dotValue = gate.GetHorizontalDotProductTo( base );

		for ( TMapIterator<ECarePackageZoneType, FCarePackageZones> allZones : cpZones )
		{
			for ( int i = allZones.Value.zones.Num() - 1; i >= 0; --i )
			{
				float zoneDotValue = gate.GetHorizontalDotProductTo( allZones.Value.zones[i] );
				if ( ( dotValue > 0 && zoneDotValue > 0 ) || ( dotValue < 0 && zoneDotValue < 0 ) )
				{
					// DrawDebugSphere( zone.GetActorLocation(), 1000, 20, FLinearColor::Blue );
				}
				else
				{
					// DrawDebugSphere( zone.GetActorLocation(), 1000, 20, FLinearColor::Red );
					allZones.Value.zones.RemoveAt( i );
				}
			}
		}
	}

	void Init_CarePackageData()
	{
		TArray<FCarePackageData> Rows;
		carePackageDataTable.GetAllRows( Rows );

		for ( int i = 0; i < Rows.Num(); i++ )
		{
			carePackageData.Add( Rows[i] );
		}
	}

	// Determine number of care packages and choose locations in advance, to announce on the map where they will spawn
	void SetupCarePackageSpawns()
	{
		FCarePackageData cpData = carePackageData[carePackageTier < carePackageData.Num() ? carePackageTier : carePackageData.Num() - 1];
		SetupCarePackageSpawnsCommon( cpData );
	}

	void SetupCarePackageSpawnsCommon( FCarePackageData cpData )
	{
		AAS_GameState gs = GetGameStateEntity();

		spawnLocations.Empty();
		ClearMapMarkers();

		AddSpawnLocations( cpData, 1 );
		if ( IsValid( deliveryBlimp ) )
		{
			if ( spawnLocations.Num() > 0 )
				deliveryBlimp.SetDropLocation( spawnLocations[0] );
		}
		else
		{
			System::SetTimer( this, n"SpawnCarePackages", GameConst::CARE_PACKAGE_WARNING_TIME, false );
		}

		// Announce that a care package is set to land
		Server_SendObituaryMessageToAllPlayers( Obituary::RAID_MODE_GAME_EVENT_CARE_PACKAGE_INCOMING );
		Server_SendCarePackageIncomingToAllPlayers( GetGameTimeMS() + TO_MILLISECONDS( GameConst::CARE_PACKAGE_WARNING_TIME ) );
		PlayAnnouncerDialogueToAll( GameplayTags::Audio_VO_GameUpdates_CarePackage_Incoming );
	}

	TArray<FVector> GenerateSpawnLocations( int numSpawns )
	{
		TArray<FVector> results;

		FCarePackageData cpData = carePackageData[carePackageTier < carePackageData.Num() ? carePackageTier : carePackageData.Num() - 1];
		TArray<AAS_CarePackageZone> zones;

		if ( cpZones.Contains( ECarePackageZoneType::NEAR_SHIELD_DOME ) )
			zones.Append( cpZones[ECarePackageZoneType::NEAR_SHIELD_DOME].zones );

		if ( cpZones.Contains( ECarePackageZoneType::MID_ZONE ) )
			zones.Append( cpZones[ECarePackageZoneType::MID_ZONE].zones );

		if ( cpZones.Contains( ECarePackageZoneType::POI ) )
			zones.Append( cpZones[ECarePackageZoneType::POI].zones );

		if ( zones.Num() == 0 )
			return results;

		// Handle spawning more care packages than zones exist.
		int numToSpawn = numSpawns;
		if ( numToSpawn > zones.Num() )
		{
			// Spawn any leftovers in a second pass, allowing for duplicates
			results.Append( GenerateSpawnLocations( numToSpawn - zones.Num() ) );
			numToSpawn = zones.Num();
		}

		TArray<AAS_CarePackageZone> tempZones = zones;
		int numSpawned						  = 0;
		// Attempt to find a valid spawn zone until we spawned the desired number of care packages or we've tried every zone
		while ( numSpawned < numToSpawn && tempZones.Num() > 0 )
		{
			int zoneIndex = Math::RandRange( 0, tempZones.Num() - 1 );

			TOptional<FVector> result = FindGroundPosition( tempZones[zoneIndex] );
			if ( result.IsSet() )
			{
				results.Add( result.Get( FVector() ) );
				numSpawned++;
			}

			// Remove zone regardless. We either are spawning one in this zone or were unable to find a valid spot and need to try other zones
			tempZones.RemoveAt( zoneIndex );
		}

		return results;
	}

	// Distributes spawns across care package zones. Do not spawn 2 in 1 zone unless we are spawning more care packages than zones
	private void AddSpawnLocations( FCarePackageData cpData, int numSpawns )
	{
		TArray<FVector> spawnLocs = GenerateSpawnLocations( numSpawns );
		for ( FVector loc : spawnLocs )
		{
			AddSpawnLocation( cpData, loc );
		}
	}

	private TOptional<FVector> FindGroundPosition( AAS_CarePackageZone carePackageZone )
	{
		TOptional<FVector> result;
		if ( !IsValid( carePackageZone ) )
		{
			Warning( "FindGroundPosition - carePackageZone is invalid!" );
			return result;
		}

		FVector groundPosition;
		const int FIND_POS_ATTEMPTS = 10;

		for ( int i = 0; i < FIND_POS_ATTEMPTS; ++i )
		{
			if ( carePackageZone.GetRandomGroundPosition( groundPosition ) )
			{
				// Check if valid ground position, otherwise keep making attempts
				if ( IsValidSpawnLocation( groundPosition ) )
				{
					result.Set( groundPosition );
					return result;
				}
			}
		}

		Warning( "FindGroundPosition - No valid ground position found! Will attempt to spawn in another zone" );
		return result;
	}

	void TrainingAddSpawnLocation( FCarePackageData cpData, FVector spawnLocation )
	{
		AddSpawnLocation( cpData, spawnLocation );
	}

	// Caches spawn location and adds map marker to indicate where it will spawn
	private void AddSpawnLocation( FCarePackageData cpData, FVector spawnLocation )
	{
		spawnLocations.Add( spawnLocation );

		TSubclassOf<AAS_MarkerActor> carePackageIncomingMarkerClass = AAS_CarePackageMarker::StaticClass();
		AAS_CarePackageMarker carePackageMarker						= Cast<AAS_CarePackageMarker>( Server_SpawnEntity( carePackageIncomingMarkerClass, nullptr, spawnLocation ) );
		if ( IsValid( carePackageMarker ) )
		{
			carePackageMarker.SetCarePackageContents( GenerateLootContents( cpData ) );
			mapMarkers.Add( carePackageMarker );
		}

		AFXActor smokeFX = Server_SpawnEffectAtLocation_Looping( DoNotShipGlobals().carePackageSmoke, spawnLocation );
		smokeFX.SetVariableLinearColor( n"Color", SMOKE_COLOR );
		spawnLocationFXs.Add( smokeFX );
		AFXActor warningBeamFX = Server_SpawnEffectAtLocation_Looping( DoNotShipGlobals().carePackageWarningBeam, spawnLocation );
		warningBeamFX.SetVariableLinearColor( n"Color", SMOKE_COLOR );
		spawnLocationFXs.Add( warningBeamFX );
	}

	private bool IsValidSpawnLocation( FVector spawnLocation )
	{
		TArray<AAS_RaidDomeShield> domes;
		GetAllActorsOfClass( AAS_RaidDomeShield::StaticClass(), domes );
		for ( AAS_RaidDomeShield dome : domes )
		{
			// Check if spawnLocation is within any domes radius to prevent spawning on a dome
			float distanceSqrd = DistanceSqrd2D( dome.GetActorLocation(), spawnLocation );
			float radiusSqrd   = dome.GetRadius() * dome.GetRadius();
			if ( distanceSqrd < radiusSqrd )
				return false;
		}

		return true;
	}

	private void ClearMapMarkers()
	{
		for ( int i = 0; i < mapMarkers.Num(); ++i )
		{
			if ( IsValid( mapMarkers[i] ) )
				mapMarkers[i].Destroy();
		}

		mapMarkers.Empty();
	}

	UFUNCTION()
	void SpawnCarePackages()
	{
		FCarePackageData cpData = carePackageData[carePackageTier < carePackageData.Num() ? carePackageTier : carePackageData.Num() - 1];
		SpawnCarePackages_Internal( cpData );
	}

	private void SpawnCarePackages_Internal( FCarePackageData cpData )
	{
		for ( int i = 0; i < spawnLocations.Num(); ++i )
		{
			// These should be the same size
			if ( i >= mapMarkers.Num() )
				break;

			float spawnHeightOffset							 = GetCarePackageSpawnHeight() - spawnLocations[i].Z;
			FRotator spawnRotation							 = FRotator( 0.0f, Math::RandRange( -180.0f, 180.0f ), 0.0f );
			TSubclassOf<AAS_CarePackage> carePackageSubclass = carePackageClass;
			AAS_CarePackage carePackage						 = Cast<AAS_CarePackage>( Server_SpawnEntity( carePackageSubclass, nullptr, spawnLocations[i] + FVector( 0, 0, spawnHeightOffset ), spawnRotation ) );
			carePackage.SetLootSpawnResults( mapMarkers[i].lootSpawns );
			carePackage.SetLandingLocation( spawnLocations[i] );

			// Once the care package timer ends, let players know the care package is in play
			Server_SendObituaryMessageToAllPlayers( Obituary::RAID_MODE_GAME_EVENT_CARE_PACKAGE_LANDED );
			Server_SendCarePackageLandedToAllPlayers();
			PlayAnnouncerDialogueToAll( GameplayTags::Audio_VO_GameUpdates_CarePackage_Marked );
		}

		carePackageTier = Math::Min( carePackageTier + 1, Math::Max( 0, carePackageData.Num() - 2 ) );
		ClearMapMarkers();
		spawnLocations.Empty();

		for ( AFXActor effect : spawnLocationFXs )
		{
			if ( IsValid( effect ) )
			{
				effect.Destroy();
			}
		}
	}

	float GetCarePackageSpawnHeight()
	{
		if ( IsValid( deliveryBlimp ) )
			return deliveryBlimp.GetActorLocation().Z;

		return SPAWN_HEIGHT;
	}

	TArray<FLootSpawnResult> GenerateLootContents( FCarePackageData lootData )
	{
		TArray<FLootSpawnResult> contents;

		for ( FCarePackageSpawnStruct spawnStruct : lootData.loot )
		{
			if ( spawnStruct.lootZone != NAME_None )
			{
				for ( int i = 0; i < spawnStruct.count; ++i )
					contents.Add( GetWeightedRandomRoll( Server_GetLootZoneData( spawnStruct.lootZone ) ) );
			}
			else if ( IsLootIndexValid( spawnStruct.tag ) )
			{
				FLootSpawnResult result;
				result.tag	 = spawnStruct.tag;
				result.count = spawnStruct.count;
				contents.Add( result );
			}
		}

		return contents;
	}

	UFUNCTION()
	private void OnBlimpMoveComplete( AAS_Blimp blimp )
	{
		SpawnCarePackages();
	}

	void DevCallCarePackage()
	{
#if !RELEASE
		SetupCarePackageSpawns();
		System::SetTimer( this, n"DevSpawnCarePackageThread", GameConst::CARE_PACKAGE_WARNING_TIME, false );
#endif
	}

	void DevDropCarePackage()
	{
#if !RELEASE
		SetupCarePackageSpawns();
		SpawnCarePackages();
#endif
	}

	UFUNCTION( NotBlueprintCallable )
	private void DevSpawnCarePackageThread()
	{
#if !RELEASE
		SpawnCarePackages();
#endif
	}
}

UCLASS()
class AAS_CarePackageZone : AActor
{
	default ActorTickEnabled = false;

	UPROPERTY( DefaultComponent, RootComponent )
	USceneComponent root;

	UPROPERTY( DefaultComponent )
	UBillboardComponent billboard;
	default billboard.bIsEditorOnly = true;
	default billboard.bHiddenInGame = true;

	UPROPERTY( DefaultComponent )
	USphereComponent editorCollision;
	default editorCollision.SphereRadius	 = 300;
	default editorCollision.CollisionEnabled = ECollisionEnabled::NoCollision;
	default editorCollision.bIsEditorOnly	 = true;

	UPROPERTY( EditInstanceOnly )
	float zoneRadius = 300;

	UPROPERTY( EditInstanceOnly )
	ECarePackageZoneType zoneType = ECarePackageZoneType::POI;

	UFUNCTION( BlueprintOverride )
	void ConstructionScript()
	{
		if ( IsValid( editorCollision ) )
			editorCollision.SphereRadius = zoneRadius;
	}

	UFUNCTION( BlueprintOverride )
	void BeginPlay()
	{
		zoneRadius = zoneRadius * GetActorScale3D().X;
		DestroyMainWorldAndClientWorldVersion( this );
	}

	// Returns true if groundPosition is found and set. Returns false if no valid position is found
	bool GetRandomGroundPosition( FVector& groundPosition )
	{
		FRotator spawnOffset = FRotator( 0.0f, Math::RandRange( -180.0f, 180.0f ), 0.0f );
		float offsetDistance = Math::RandRange( 0.0f, 1.0f ) * zoneRadius;

		FVector endLocation	  = GetActorLocation() - FVector( 0, 0, 1000 ) + spawnOffset.ForwardVector * offsetDistance;
		FVector startLocation = endLocation + FVector( 0, 0, 11000 );

		FHitResult groundTrace = LineTraceSingleByProfile( startLocation, endLocation, n"BlockAllDynamic", false, TArray<AActor>(), true );
		if ( !groundTrace.bBlockingHit )
			return false;

		TSubclassOf<ANCPlayerCharacter> charClass = GetCharClass();
		ANCPlayerCharacter defaultChar			  = Cast<ANCPlayerCharacter>( charClass.Get().DefaultObject );

		if ( !UNCUtils::IsTraceHitWalkable( defaultChar.CharacterMovement, groundTrace ) )
		{
			bool success = UNavigationSystemV1::GetRandomLocationInNavigableRadius( groundTrace.ImpactPoint, groundPosition, 300 );

			if ( !success )
				return false;
		}
		else
		{
			groundPosition = groundTrace.ImpactPoint;
		}

		FHitResult result = LineTraceSingleByProfile( groundPosition + FVector( 0, 0, 10 ), groundPosition + FVector( 0, 0, 50000 ), n"BlockAllDynamic", false, TArray<AActor>(), true );

		if ( result.GetbBlockingHit() )
			return false;

		return true;
	}

	private TSubclassOf<ANCPlayerCharacter> GetCharClass()
	{
		ANCGameModeBase gameMode = Cast<ANCGameModeBase>( Gameplay::GetGameMode() );
		if ( IsValid( gameMode ) )
		{
			return TSubclassOf<ANCPlayerCharacter>( gameMode.DefaultPawnClass.Get() );
		}

		ScriptError_Silent_WithBug( f"AAS_CarePackageZone invalid ANCGameModeBase. Using players default class", f"rwest", f"gameMode is null " );
		return GetPlayer( 0 ).GetClass();
	}
}

UCLASS()
class AAS_CarePackageMarker : AAS_MarkerActor
{
	default mapMarker.makePinnableWidget = false;
	default mapMarker.pinnableWidget	 = NAME_None;
	default mapMarker.makeMinimapMarker	 = true;
	default mapMarker.mapMarker			 = n"drop_incoming";

	UPROPERTY()
	TArray<FLootSpawnResult> lootSpawns;

	UFUNCTION( BlueprintOverride, Meta = ( NoSuperCall ) )
	void BeginPlay()
	{
	}

	void SetCarePackageContents( TArray<FLootSpawnResult> newContents )
	{
		lootSpawns = newContents;
	}
}

void Server_SendCarePackageIncomingToAllPlayers( int endTimeMs )
{
	TArray<AAS_PlayerEntity> players = GetAllPlayers();
	for ( AAS_PlayerEntity player : players )
	{
		UNCRemoteScriptCommands::SendServerCommand( player, f"ServerCommandCarePackageIncoming {endTimeMs}" );
	}
}

void Server_SendCarePackageLandedToAllPlayers()
{
	TArray<AAS_PlayerEntity> players = GetAllPlayers();
	for ( AAS_PlayerEntity player : players )
	{
		UNCRemoteScriptCommands::SendServerCommand( player, f"ServerCommandCarePackageLanded" );
	}
}

void Server_SendCarePackageOpenedToAllPlayers()
{
	TArray<AAS_PlayerEntity> players = GetAllPlayers();
	for ( AAS_PlayerEntity player : players )
	{
		UNCRemoteScriptCommands::SendServerCommand( player, f"ServerCommandCarePackageOpened" );
	}
}