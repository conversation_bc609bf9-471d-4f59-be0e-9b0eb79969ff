class UAS_GatherMessageWidget : UUserWidgetDefault
{
	UPROPERTY( Meta = ( BindWidgetAnim ), Transient )
	UWidgetAnimation appearAnim;

	UPROPERTY( Meta = ( BindWidgetAnim ), Transient )
	UWidgetAnimation pulseAnim;

	UPROPERTY( BindWidget )
	UAS_CommonTextBlock amountText;

	UPROPERTY( BindWidget )
	UAS_CommonTextBlock totalText;

	UPROPERTY( BindWidget )
	UImage icon;

	int currentCount;

	void Initialize( FBackpackItemStruct itemData, int total )
	{
		if ( currentCount == 0 )
		{
			PlayAnimation( appearAnim );
		}
		else
		{
			PlayAnimation( pulseAnim );
		}

		const FLootDataStruct& lootData = GetLootDataByIndex( itemData.itemIndex );
		int totalCount = total;
		currentCount += itemData.itemCount;

		SetImageBrushIcon( icon, lootData.icon );
		amountText.SetText( GetLocalizedText( Localization::HUDMainWidget, "gather_amount", FFormatArgumentValue(currentCount) ) );
		totalText.SetText( GetLocalizedText( Localization::HUDMainWidget, "gather_total", FFormatArgumentValue(totalCount) ) );
	}

	void InitializeWithSpecificDisplays( UTexture2D newImage, FText amount, FText total )
	{
		if ( currentCount == 0 )
		{
			PlayAnimation( appearAnim );
		}
		else
		{
			PlayAnimation( pulseAnim );
		}
		SetImageBrushIcon( icon, newImage );
		amountText.SetText( amount );
		totalText.SetText( total );
	}
}