UCLASS(Abstract)
class UAS_RadialTeleportMenuData : UAS_RadialMenuData
{
	UFUNCTION( BlueprintOverride )
	bool Populate( TArray<UAS_RadialMenuListItemData>& results )
	{
		// Empty the results but ensure we reserve space for the teleport types given
		int numOptions = int( ETeleportID::_count );
		results.Empty( numOptions );

		// Reverse through the list so that breaker is always first (forward)
		for ( int i = numOptions - 1; i > int( ETeleportID::INVALID ); i-- )
		{
			results.Add( CreateTeleportData( ETeleportID( i ) ) );
		}

		return !results.IsEmpty();
	}

	UFUNCTION( BlueprintOverride )
	bool SelectItem( UAS_RadialMenuListItemData data )
	{
		UAS_TeleportWheelItemData teleportItemData = Cast<UAS_TeleportWheelItemData>(data);
		bool result = false;

		if ( teleportItemData.isEnabled )
		{
			UAS_TeleportSystem teleportSystem = TeleportSystem();
			if ( IsValid( teleportSystem ) )
			{
				teleportSystem.activeTeleportID = teleportItemData.teleportID;
				teleportSystem.RequestTeleport();
				result = true;
			}
		}

		return result;
	}

	// TODO @jmccarty: Clean this up
	UAS_TeleportWheelItemData CreateTeleportData( ETeleportID teleportID )
	{
		AAS_PlayerEntity player		   = Cast<AAS_PlayerEntity>( Client_GetLocalASPawn() );
		UAS_TeleportWheelItemData item = UAS_TeleportWheelItemData();

		ECanTeleportResult canTeleport;
		item.teleportID				   = teleportID;
		switch ( teleportID )
		{
			case ETeleportID::BASE:
				canTeleport	   = TeleportSystem().CanTeleportHome( player );
				item.isEnabled = canTeleport == ECanTeleportResult::YES;
				break;

			case ETeleportID::BREAKER:
			{
				canTeleport	   = TeleportSystem().CanTeleportToBreaker( player );
				item.isEnabled = canTeleport == ECanTeleportResult::YES;
			}
			break;

			default:
				item.isEnabled = false;
				break;
		}

		item.isAvailable = item.isEnabled;

		return item;
	}
}