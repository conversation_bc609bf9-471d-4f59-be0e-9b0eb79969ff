event void FOnPlayerBackpackContentsChangedEvent( UAS_BackpackComponent backpack );
event void FOnPawnTookDamage( FDamageInfo& DamageInfo, ANCPlayerCharacter attacker );
event void FOnPawnDealtDamageToPlayer( const FDamageInfo info, ANCPlayerCharacter victim );
event void FOnCooldownReset( AAS_PlayerEntity asPawn );
event void FOnPreExecutedAnotherPlayer( AAS_PlayerEntity attacker, ANCPlayerCharacter victim );
event void FOnPlayerTurnOrderReplicated( AAS_PlayerEntity asEntity, int turnOrder );

// @<PERSON> need a callback from code for this for the mount
event void FOnPlayerPrimaryAttack( AAS_PlayerEntity player, ANCWeapon weapon );

event void FOnPlayerStartedRedmaneLeap( AAS_PlayerEntity asPlayer );

event void FOverrideGoalFOV( float& goal );
event void FEvent_Client_OnPawnDeath( AAS_PlayerEntity player );
event void FEvent_Client_OnVendorMenu( AAS_PlayerEntity player );
event void FEvent_Client_OnVendorMenuPurchase( AAS_PlayerEntity player, int vendorId, FVendorItem item );

enum EDeadMenuState
{
	HIDE_CLEAR_SPECTATE_ACTOR,
	HIDE_DONT_CLEAR_SPECTATE_ACTOR,
	SHOW,
}

enum ENCSpectateCameraState
{
	Default,
	WhileMounted,
}

UCLASS( Abstract )
class AAS_PlayerEntity : ANCPlayerCharacter
{
	access ScriptedAnim = private, UAS_ScriptAnimSystem;

	FNCCoroutineSignal OnDamagedSignal;
	FNCCoroutineSignal OnDeathSignal;
	FNCCoroutineSignal OnSpawnedSignal;
	FNCCoroutineSignal endSignalLockViewToVehicleHackThread;
	FNCCoroutineSignal OnMountSignal;
	FNCCoroutineSignal OnDismountSignal;
	FNCCoroutineSignal OnStartZipline;
	FNCCoroutineSignal OnResetCooldownsSignal;
	FNCCoroutineSignal OnTeleportStartSignal;
	FNCCoroutineSignal OnCancelTeleportSignal;
	FNCCoroutineSignal OnCancelTeleportInsideSignal;
	FNCCoroutineSignal OnTeleportPointOfNoReturnSignal;
	FNCCoroutineSignal onLifestealHealEndSignal;
	FNCCoroutineSignal onPlayerLandedSignal;
	FNCCoroutineSignal OnWeaponChanged;
	FNCCoroutineSignal OnDestroyOrbStartedSignal;
	FNCCoroutineSignal OnStartPlantBreaker;
	FNCCoroutineSignal OnStartPlantExtender;

	FPlayerGlobalCallback server_onAnyBackpackChanged;
	FPlayerGlobalCallback client_onAnyBackpackChanged;
	FPlayerGlobalCallback OnTeleport;

	FEvent_Client_OnPawnDeath Client_OnPawnDeath; // to mimic Server_OnPawnDeath
	FEvent_Client_OnVendorMenu client_OnVendorMenuOpen;
	FEvent_Client_OnVendorMenu client_OnVendorMenuClose;
	FEvent_Client_OnVendorMenuPurchase client_OnVendorMenuPurchase;

	FOnPlayerTurnOrderReplicated Client_PlayerTurnOrderReplicated;

	FOnPreExecutedAnotherPlayer server_onPreExecutedAnotherPlayer;
	FOnPlayerStartedRedmaneLeap server_onPlayerStartedRedmaneLeap;
	FEvent_OnPlayerDropLoot sv_OnPlayerDropLoot;

	// P O S T    D E A T H //////////////////////////////////////////////////////////

	int spectateIndex;

	UPROPERTY()
	FNCNetInt net_deadMenuState;

	private TOptional<FWhoKilledMeData> optWhoKilledMeData;

	bool GetWhoKilledMe( FWhoKilledMeData& whoKilledMeData )
	{
		bool result = false;

		if ( optWhoKilledMeData.IsSet() )
		{
			whoKilledMeData = optWhoKilledMeData.GetValue();
			result			= true;
			optWhoKilledMeData.Reset();
		}

		return result;
	}

	void TrackWhoKilledMe( float healthPerc, int shieldLevel, FString shieldRarity, float shieldPerc, float hardentPerc )
	{
		optWhoKilledMeData = FWhoKilledMeData( healthPerc, shieldLevel, FGameplayTag::RequestGameplayTag( FName( shieldRarity ) ), shieldPerc, hardentPerc );
	}

	//////////////////////////////////////////////////////////////////////////////////

	int respawnTotem_lastReviveTimeMS = -9999999;
	bool sv_isPlantingShieldBreacher  = false;

	UPROPERTY( OverrideComponent = HealthComponent )
	UAS_ShieldHealthComponent asHealthComponent;

	UPROPERTY( DefaultComponent )
	UAS_PlayerCameraBlendablesManager blendablesManager;

	UPROPERTY( DefaultComponent )
	UAS_PlayerFlashbangScreenManager flashbangScreenManager;

	UPROPERTY( DefaultComponent )
	UAS_NovaIronsightsControllerComponent novaIronsightsComponent_TEMP;

	UPROPERTY( DefaultComponent )
	UAS_WallInteractionListComponent wallInteractionListComponent;

	///////////////////////////////////////////////////////////////////////////////////
	///////////////////////////////////////////////////////////////////////////////////

	UPROPERTY()
	FOnPawnTookDamage Server_OnDamaged;

	UPROPERTY()
	FOnPawnDealtDamageToPlayer Server_OnDealtDamageToPlayer;

	UPROPERTY()
	FNCPawnCallback Server_OnRespawned;

	FNCPawnCallback Server_OnZiplineSpawnBegin;

	// called when the player is removed from the server (usually some minutes after disconnect)
	UPROPERTY()
	FNCPawnCallback Server_OnPlayerRemoved;

	UPROPERTY()
	FOnCooldownReset Server_OnCooldownReset;

	////////////////////////////////////////////////////////////////////////////////////////////////////////////////
	// B A C K P A C K S
	////////////////////////////////////////////////////////////////////////////////////////////////////////////////

	UPROPERTY( DefaultComponent )
	UAS_BackpackComponent backpackComponent;
	default backpackComponent.filterType = EBackpackFilterType::BLACK_LIST;
	default backpackComponent.filterEntries.Add( ELootType::PrimaryWeapon );
	default backpackComponent.filterEntries.Add( ELootType::ResourceItem );
	default backpackComponent.filterEntries.Add( ELootType::GameModeResource );
	default backpackComponent.filterEntries.Add( ELootType::Ammo );
	default backpackComponent.filterEntries.Add( ELootType::HealItem );
	default backpackComponent.filterEntries.Add( ELootType::ShieldBreacher );

	UPROPERTY( DefaultComponent )
	UAS_ResourceBackpackComponent resourceBackpackComponent;
	default resourceBackpackComponent.filterType = EBackpackFilterType::WHITE_LIST;
	default resourceBackpackComponent.filterEntries.Add( ELootType::ResourceItem );
	// TODO: LOCALIZE
	default resourceBackpackComponent.fullMessageKey = f"BACKPACK_RESOURCE_FULLMSG";

	UPROPERTY( DefaultComponent )
	UAS_ResourceBackpackComponent gameModeResourceBackpackComponent;
	default gameModeResourceBackpackComponent.filterType = EBackpackFilterType::WHITE_LIST;
	default gameModeResourceBackpackComponent.filterEntries.Add( ELootType::GameModeResource );
	// TODO: LOCALIZE
	default gameModeResourceBackpackComponent.fullMessageKey = f"BACKPACK_GAME_MODE_RESOURCE_FULLMSG";

	UPROPERTY( DefaultComponent )
	UAS_ResourceBackpackComponent healsBackpackComponent;
	default healsBackpackComponent.filterType = EBackpackFilterType::WHITE_LIST;
	default healsBackpackComponent.filterEntries.Add( ELootType::HealItem );
	// TODO: LOCALIZE
	default healsBackpackComponent.fullMessageKey = f"BACKPACK_MED_FULLMSG";

	UPROPERTY( DefaultComponent )
	UAS_ResourceBackpackComponent ammoBackpackComponent;
	default ammoBackpackComponent.filterType = EBackpackFilterType::WHITE_LIST;
	default ammoBackpackComponent.filterEntries.Add( ELootType::Ammo );
	// TODO: LOCALIZE
	default ammoBackpackComponent.fullMessageKey = f"BACKPACK_AMMO_FULLMSG";

	// Equipped shield changes loot type to exempt it from other checks
	UPROPERTY( DefaultComponent )
	UAS_BackpackComponent equippedShieldBackpackComponent;
	default equippedShieldBackpackComponent.filterType = EBackpackFilterType::WHITE_LIST;
	default equippedShieldBackpackComponent.filterEntries.Add( ELootType::PlayerShield );
	default equippedShieldBackpackComponent.backpackMaxSlots = 1;

	UPROPERTY( DefaultComponent )
	UAS_BackpackComponent loadoutWeaponsBackpackComponent;
	default loadoutWeaponsBackpackComponent.filterType = EBackpackFilterType::WHITE_LIST;
	default loadoutWeaponsBackpackComponent.filterEntries.Add( ELootType::PrimaryWeapon );
	default loadoutWeaponsBackpackComponent.backpackMaxSlots = 2;

	// Equipped shield changes loot type to exempt it from other checks
	UPROPERTY( DefaultComponent )
	UAS_BackpackComponent shieldBreakerBackpackComponent;
	default shieldBreakerBackpackComponent.filterType = EBackpackFilterType::WHITE_LIST;
	default shieldBreakerBackpackComponent.filterEntries.Add( ELootType::ShieldBreacher );
	default shieldBreakerBackpackComponent.backpackMaxSlots = 1;

	// Executed once per frame when contents of any backpack changes to process relevant UI updates.
	FNCDeferredScriptAction deferredBackpackChangeProcessing;

	////////////////////////////////////////////////////////////////////////////////////////////////////////////////

	int nextAllowBackpackFullNotify;

	void NotifyBackpackFull( FGameplayTag loot )
	{
		if ( GetTimeMilliseconds() > nextAllowBackpackFullNotify )
		{
			UNCRemoteScriptCommands::SendServerCommand( this, f"ServerCommand_NotifyBackpackFull {GetIntForGameplayTag( loot )}" );
			nextAllowBackpackFullNotify = GetTimeMilliseconds() + TO_MILLISECONDS( 3.0 );
		}
	}

	////////////////////////////////////////////////////////////////////////////////////////////////////////////////

	UPROPERTY()
	FNCNetBool net_isHidden;

	////////////////////////////////////////////////////////////////////////////////////////////////////////////////

	UPROPERTY( DefaultComponent )
	UAS_EntityHighlightComponent highlightManager;
	default highlightManager.pinWidgetName = n"PinnableWidget_Detection";

	UPROPERTY( DefaultComponent )
	UAS_SelectablePlayerRespawnComponent selectableRespawnManager;

	UPROPERTY( DefaultComponent )
	UAS_SlowzoneComponent slowZoneManager;

	UPROPERTY( DefaultComponent )
	UAS_RespawnTotemComponent totemComponent;

	UPROPERTY( DefaultComponent )
	UAS_MapMarkerComponent mapMarkerComponent;
	default mapMarkerComponent.mapMarkerVisibilityOptions.OwnerAndTeammates = n"player_indicator";
	default mapMarkerComponent.mapMarkerVisibilityOptions.Custom			= n"enemy_indicator";

	UPROPERTY( DefaultComponent )
	UAS_ClientScreenShakeComponent clientScreenShakeComponent;

	UPROPERTY( DefaultComponent )
	UAS_PassivesComponent passivesComponent;

	UPROPERTY( DefaultComponent )
	UAS_PlayerMountCameraManager mountCameraManager;

	UPROPERTY( DefaultComponent )
	UAS_PlayerMountSpeedBoostManager mountSpeedBoostManager;

	UPROPERTY( DefaultComponent )
	UAS_PlayerCooldownComponent tacticalCooldownComponent;
	default tacticalCooldownComponent.weaponSlot			= WeaponSlot::TacticalSlot;
	default tacticalCooldownComponent.keepCooldownOverDeath = GameConst::KEEP_COOLDOWN_OVER_DEATH;
	default tacticalCooldownComponent.playReadySound		= true;

	UPROPERTY( DefaultComponent )
	UAS_PlayerCooldownComponent fragCooldownComponent;
	default fragCooldownComponent.weaponSlot			= WeaponSlot::GrenadeSlot;
	default fragCooldownComponent.keepCooldownOverDeath = GameConst::KEEP_COOLDOWN_OVER_DEATH;
	default fragCooldownComponent.playReadySound		= true;

	UPROPERTY( DefaultComponent )
	UAS_PlayerCooldownComponent ultCooldownComponent;
	default ultCooldownComponent.weaponSlot			   = WeaponSlot::RaidUltSlot;
	default ultCooldownComponent.keepCooldownOverDeath = GameConst::KEEP_COOLDOWN_OVER_DEATH;
	default ultCooldownComponent.playReadySound		   = false;
	default ultCooldownComponent.alwaysRestoreCooldown = true;

	FTimerHandle ultimateReadyNagTimer;

	UPROPERTY( DefaultComponent )
	UAS_ClientStatusEffectManagerComponent toxicTrackerComponent;
	default toxicTrackerComponent.statusEffect = GameplayTags::StatusEffect_Toxic;

	UPROPERTY( DefaultComponent )
	UAS_DreadStatusEffectManagerComponent dreadTrackerComponent;
	default dreadTrackerComponent.statusEffect	= GameplayTags::StatusEffect_DreadPulse;
	default dreadTrackerComponent.pinWidgetName = n"PinnableWidget_HunterDread";

	UPROPERTY( DefaultComponent )
	UAS_ClientStatusEffectManagerComponent mountSpeedGateTrackerComponent;
	default mountSpeedGateTrackerComponent.statusEffect = GameplayTags::StatusEffect_MountSpeedGateBoost;

	/***********************************************************************************\
								A N I M
	\***********************************************************************************/
	UPROPERTY( DefaultComponent )
	UAS_AnimNotifyTrackingManagerComponent animNotifyTrackingComponent;

	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<UAnimInstance> arms1P_proxyAnimClass;

	UPROPERTY( DefaultComponent )
	UAS_FireBombTrackerComponent fireTrackerComponent;

	UPROPERTY( DefaultComponent )
	UAS_StealthAbilityComponent stealthAbilityComponent;

	UPROPERTY( DefaultComponent )
	UAS_DaggersAbilityComponent daggerAbilityComponent;

	UPROPERTY( DefaultComponent )
	UAS_RocketLockTrackerComponent rocketLockTrackerComponent;

	UPROPERTY( DefaultComponent )
	UAS_BolasInteractComponent bolasInteractComponent;

	UPROPERTY()
	FNCNetInt net_killSteakCount( 0, 0, MAX_int32 );

	int playerKillStreakBonusStatusId = -1;

	////////////////////////////////////////////////////////////////////////////////////////////////////////////////
	// H O L S T E R
	////////////////////////////////////////////////////////////////////////////////////////////////////////////////

	private int holsterWeaponStack = 0;

	/*	hack because weapon 3P don't hide when holstered... they play an animation of putting the weapon behind your back,
		but never return from that animation and never hide the weapons... the not hiding the weapons part is especially
		egregious because 3P scripted anims count on that weapon being hidden to look right.	*/
	UPROPERTY( Transient )
	FNCNetBool net_HACK_AreWeaponsHolstered( false );

	UFUNCTION()
	private void cl_HACK_HideWeapon3POnHolster( bool oldValue, bool newValue )
	{
		if ( !IsLocallyPredicted() || TPPCameraComponent.IsTPPEnabled() )
		{
			const bool visible = !newValue;
			SetWeaponClonesVisibility( visible );
		}
	}

	void ScriptHolsterWeapon()
	{
		if ( holsterWeaponStack == 0 )
		{
			HolsterWeapons();
			net_HACK_AreWeaponsHolstered.SetNetValue( true );

			if ( GetBugReproNum() != 6688 )
				ServerDisableWeapons();
		}

		holsterWeaponStack++;
	}

	void ScriptDeployWeapon()
	{
		holsterWeaponStack--;

		ScriptAssert( holsterWeaponStack >= 0, "ScriptDeployWeapon called too many times without matching holster weapon!" );

		if ( holsterWeaponStack == 0 )
		{
			if ( GetBugReproNum() != 6688 )
				ServerEnableWeapons();

			DeployWeapons();
			net_HACK_AreWeaponsHolstered.SetNetValue( false );

			// hack because if the last weapon out was the axe... it will auto swing on deploy weapons
			if ( GetActiveWeaponSlot() == WeaponSlot::MeleeSlot )
				TryEquipLastPrimary();
		}
	}

	float32 onDamaged_nextAllowOnDamageMoveSlow;
	float32 onDamaged_moveSlowCooldown = 3.0f;

	FTimerHandle giveUpTimer;

	UPROPERTY( DefaultComponent )
	UAS_PlayerHealthRegenComponent healthRegenComponent;

	UPROPERTY( DefaultComponent )
	UAS_MeleeResponseComponent MeleeResponseComponent;
	UPROPERTY( DefaultComponent )
	UAS_MeleePlayerComponent meleeData;

	UPROPERTY( DefaultComponent )
	UAS_FallDamageComponent fallDamageComponent;
	UPROPERTY( EditDefaultsOnly )
	UAS_PlayerFallDamagedata playerFallDamageData;

	UPROPERTY( DefaultComponent )
	UAS_ShieldControlManagerComponent shieldControlManagerComponent;

	UPROPERTY( DefaultComponent )
	UAS_OutOfBoundsManager outOfBoundsManager;

	/***********************************************************************************\
				RETURN HOME COOLDOWN
	\***********************************************************************************/
	UPROPERTY()
	FNCNetInt net_returnHomeEndTime		 = FNCNetInt( 0, 0, MAX_int32 );
	int returnHomeDebounceTimeMS		 = 0;
	int teleportToBreacherDebounceTimeMS = 0;

	bool isTeleportingToBreacher;

	bool IsTeleportActive()
	{
		return net_returnHomeEndTime > 0;
	}

	bool IsTeleportToBreacher()
	{
		return IsTeleportActive() && isTeleportingToBreacher;
	}

	bool WeaponAllowsTeleport()
	{
		if ( GetActiveWeaponSlot() == WeaponSlot::RaidUltSlot )
			return false;

		if ( GetActiveWeaponSlot() == WeaponSlot::AbilityWeaponSwapSlot )
		{
			// KLUDGE -- Allow teleport while ult daggers are out
			if ( daggerAbilityComponent.daggersWeapon != nullptr && GetActiveWeapon() == daggerAbilityComponent.daggersWeapon )
				return true;

			return false;
		}

		return true;
	}

	/***********************************************************************************\
	\***********************************************************************************/

	/***********************************************************************************\
								D A M A G E     R I N G
	\***********************************************************************************/
	UPROPERTY()
	TArray<AActor> damageRings;

	/***********************************************************************************\
								M O U N T
	\***********************************************************************************/
	UPROPERTY( DefaultComponent )
	UAS_PlayerMountDataComponent mountData;

	FGenericEvent Client_OnMountInventoryChanged;
	FGenericEvent Server_OnMountInventoryChanged;

	UPROPERTY( DefaultComponent )
	USceneComponent MountAttachRoot;

	/***********************************************************************************\
								E Q U I P M E N T
	\***********************************************************************************/
	UPROPERTY( DefaultComponent )
	UAS_PlayerEquipmentComponentHelmet helmetEquipmentComponent;

	UPROPERTY( DefaultComponent )
	UAS_PlayerEquipmentComponentSaddle saddleEquipmentComponent;

	UPROPERTY( DefaultComponent )
	UAS_PlayerEquipmentComponentTrinket trinketEquipmentComponent;

	/***********************************************************************************\
								Character Screen Related Persistence Vars
	\***********************************************************************************/
	UPROPERTY( DefaultComponent )
	UAS_PlayerClassManager classManager;

	/***********************************************************************************\
						W E A P O N   L O A D O U T   D A T A
	\***********************************************************************************/

	UPROPERTY( DefaultComponent )
	UAS_PlayerLoadoutDataManager loadoutDataManager;

	UPROPERTY( DefaultComponent )
	UAS_SavedLoadoutWeaponManager savedLoadoutWeaponManager;

	access RaidWeaponContext = protected, UAS_WeaponContext_RaidHammerTronWall, UAS_WeaponContext_RaidHammer;
	access:RaidWeaponContext TArray<UAS_DeployableWallManager> tronWallManagers;
	access:RaidWeaponContext bool bExecutedHammerSlam;
	access:RaidWeaponContext int slamAmmoMessageTimeMS;

	access ZiplineContext = protected, AAS_ZiplineRopeMapActor, UAS_ZiplineRopeComponent;

	/***********************************************************************************\
							D R O P P I N G   B A C K P A C K
	\***********************************************************************************/

	UPROPERTY( DefaultComponent )
	UAS_SaveLootOnDeathComponent saveLootOnDeathComponent;

	// Data is stored on player, but not managed by the player. Manage in game mode.
	bool weaponDegradationEnabled = true;

	/***********************************************************************************/

	FOnPlayerPrimaryAttack onPlayerPrimaryAttack;

	FOnPlayerPrimaryAttack onPlayerScopeSwap;

	int lastSpawnTime;

	bool wasRaidDefenseDeath;
	bool wasRaidAttackDeath;

	UPROPERTY( Transient )
	FNCNetInt net_baseSelectVote( -1, -1, 255 );

	UPROPERTY( DefaultComponent, Attach = "CharacterMesh0", AttachSocket = "shieldbreaker" )
	UStaticMeshComponent shieldBreacherRoot;
	default shieldBreacherRoot.SetCollisionEnabled( ECollisionEnabled::NoCollision );
	default shieldBreacherRoot.SetHiddenInGame( true );

	UPROPERTY( DefaultComponent, Attach = "shieldBreacherRoot" )
	UNiagaraComponent shieldBreacherBeam;
	default shieldBreacherBeam.SetHiddenInGame( true );

	UPROPERTY( DefaultComponent, Attach = "shieldBreacherRoot" )
	UNiagaraComponent shieldBreacherTrail;
	default shieldBreacherTrail.SetActive( true );
	default shieldBreacherTrail.SetAutoActivate( false );
	default shieldBreacherTrail.SetHiddenInGame( true );

	TMap<FGameplayTag, int> matchEndStats;
	default matchEndStats.Add( GameplayTags::Progression_PlayerStats_Kills, 0 );
	default matchEndStats.Add( GameplayTags::Progression_PlayerStats_Revives, 0 );

	// Character select
	UPROPERTY( Transient )
	FNCNetInt net_turnOrder = FNCNetInt( GameConst::INDEX_NONE, -1, MAX_int32 );

	private FGameplayTagContainer mapMarkerVisibilityStatusEffects;
	default mapMarkerVisibilityStatusEffects.AddTag( GameplayTags::StatusEffect_Dread );
	default mapMarkerVisibilityStatusEffects.AddTag( GameplayTags::StatusEffect_OutsideDomeScanned );

	UPROPERTY( DefaultComponent )
	UAS_EmoteManagerComponent emoteManager;

	int oldWeaponSlotState;
	const int numWeaponInventorySlots;
	default numWeaponInventorySlots = int( EWeaponInventorySlot::Count );

	private bool _sv_disableVendorBattleChatter = false;
	void sv_DisableVendorBattleChatter()
	{
		ScriptAssert(IsServer(), "SERVER ONLY" );
		_sv_disableVendorBattleChatter = true;
	}
	void sv_EnableVendorBattleChatter()
	{
		ScriptAssert(IsServer(), "SERVER ONLY" );
		_sv_disableVendorBattleChatter = true;
	}
	bool sv_IsVendorBattleChatterEnabled()
	{
		ScriptAssert(IsServer(), "SERVER ONLY" );
		return _sv_disableVendorBattleChatter;
	}

	int GetTurnOrderIndex() const
	{
		return net_turnOrder;
	}

	void SetTurnOrderIndex( int newIndex )
	{
		net_turnOrder.SetNetValue( newIndex );
	}

	UFUNCTION()
	private void OnPlayerTurnOrderReplicated( int oldValue, int newValue )
	{
		Client_PlayerTurnOrderReplicated.Broadcast( this, newValue );
	}
	//

	UFUNCTION( BlueprintOverride )
	void ConstructionScript()
	{
#if EDITOR
		if ( !InEditor() )
#endif
			backpackComponent.backpackMaxSlots = GameModeDefaults().GamemodeRules_PlayerBackpackSize;
	}

	void SharedBeginPlay()
	{
		deferredBackpackChangeProcessing.BindUFunction( this, n"ProcessBackpackChanges" );

		for ( UAS_BackpackComponent backpack : GetBackpackComponents() )
			backpack.OnContentsChangedDelegate.AddUFunction( this, n"OnBackpackContentsChanged" );

		GetProxyArmsComponent().SetAnimClass( arms1P_proxyAnimClass );

		oldWeaponSlotState = WeaponSlotsDisabled;
	}

	void ServerBeginPlay()
	{
		matchEndStats.Add( GameModeDefaults().matchEndStat0, 0 );
		matchEndStats.Add( GameModeDefaults().matchEndStat1, 0 );
		matchEndStats.Add( GameModeDefaults().matchEndStat2, 0 );

		Server_OnRespawned.AddUFunction( this, n"Server_OnSpawned" );
		Server_OnPawnDeath.AddUFunction( this, n"OnDeath" );
		Server_OnDamaged.AddUFunction( this, n"OnDamaged" );
		Server_OnDealtDamageToPlayer.AddUFunction( this, n"OnDealtDamageToPlayer" );
		Server_OnActiveWeaponChanged.AddUFunction( this, n"OnActiveWeaponChanged" );

		OnPlayerJumpStart.AddUFunction( this, n"OnJumpStart" );
		OnPlayerJumpLand.AddUFunction( this, n"OnJumpLand" );
		OnPlayerMantleStart.AddUFunction( this, n"OnMantleStart" );
		OnPlayerSprintStart.AddUFunction( this, n"OnSprintStart" );
		OnPlayerSprintEnd.AddUFunction( this, n"OnSprintEnd" );

		HealthComponent.BP_OnPostReceivedDamage.AddUFunction( this, n"OnPostDamage" );

		if ( IsValid( ScriptCallbacks() ) )
		{
			ScriptCallbacks().server_onPlayerAssist.AddUFunction( this, n"Server_OnPlayerAssist" );
		}

		if ( GameConst::REGEN_ENABLED )
		{
			totemComponent.onTotemStarted.AddUFunction( this, n"OnBleedoutStarted" );
			totemComponent.onTotemEnded.AddUFunction( this, n"OnBleedoutEnded" );
		}

		classManager.SetClass( GameConst::INVALID_CLASS );

		ultCooldownComponent.server_onCooldownChanged.AddUFunction( this, n"OnUltimateCooldownChanged" );

		AddButtonPressedCallback( GameConst::TELEPORT_HOME_ACTION, this, n"OnTeleportPressed" );
		AddButtonReleasedCallback( GameConst::TELEPORT_HOME_ACTION, this, n"OnTeleportReleased" );

		AddButtonPressedCallback( n"Fire", this, n"OnFirePressed" );
		AddButtonReleasedCallback( n"Fire", this, n"OnFireReleased" );

		SharedBeginPlay();

		Thread( this, n"TrackLastGoodPositions" );

#if EDITOR
		if ( IsServer() )
		{
			if ( ShouldSkipCharacterSelect() )
				GetCharacterSelectManager().AssignTurnOrdersToAllPlayers();
		}
#endif
	}

	void ClientBeginPlay()
	{
		SharedBeginPlay();

		mapMarkerComponent.SetCustomMapMarkerVisibilityCallback( this, n"OnEnemyMarkerVisibilityRefresh" );

		for ( FGameplayTag tag : mapMarkerVisibilityStatusEffects.GameplayTags )
		{
			AddStatusEffectHasBegunCallback( tag, this, n"OnMapMarkerVisibilityStatusEffectAddedOrRemoved" );
			AddStatusEffectHasEndedCallback( tag, this, n"OnMapMarkerVisibilityStatusEffectAddedOrRemoved" );
		}

		mapMarkerComponent.RefreshMapMarkerComponent();

		UCL_ScriptCallbacks clientCallbacks = ClientCallbacks();
		if ( IsValid( clientCallbacks ) )
		{
			clientCallbacks.OnPawnDied.AddUFunction( this, n"Client_OnPawnDied" );
			clientCallbacks.OnPawnRespawned.AddUFunction( this, n"OnPawnRespawned" );
		}

		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			scriptCallbacks.server_onRespawnUnavailable.AddUFunction( this, n"OnRespawnUnavailable" );
		}

		TPPSettingsComponent.SetBaseSettings( DefaultTPPCameraSettings );
		SpectateSettingsComponent.SetBaseSettings( DefaultTPPCameraSettings );

		TPP_CameraAnimationArm.SetThirdPersonSpringArm( TPP_SpringArm );
		TPP_CameraAnimationArm.SetThirdPersonCamera( TPP_Camera );

		GetTPPCameraComponent().SetActive( false );
		GetCameraManagerComponent().SetActive( false );
		GetTPP_Camera().SetActive( false );

		OnPilotedMountBrake.AddUFunction(this, n"OnPlayerMountBrake");
		OnPilotedMountSprint.AddUFunction(this, n"OnPlayerMountSprint");

		net_isHidden.OnReplicated().AddUFunction( this, n"OnIsHiddenChanged" );
		OnIsHiddenChanged( false, net_isHidden );

		client_onAnyBackpackChanged.AddUFunction( this, n"OnAnyBackpackChanged" );

		net_HACK_AreWeaponsHolstered.OnReplicated().AddUFunction( this, n"cl_HACK_HideWeapon3POnHolster" );
		cl_HACK_HideWeapon3POnHolster( false, net_HACK_AreWeaponsHolstered );

		net_turnOrder.OnReplicated().AddUFunction( this, n"OnPlayerTurnOrderReplicated" );

		ClassManager().client_onClassIndexChangedCallback.AddUFunction( this, n"OnLocalPlayerClassChanged" );
		OnLocalPlayerClassChanged( FGameplayTag(), ClassManager().GetClassIndex() );

		if ( IsLocallyControlled() )
		{
			const USh_WallInteractionSystem wallInteractionSys = WallInteractionSystem();
			WallInteractionList().AddPossibleWallInteraction( wallInteractionSys.GetInteractionForTag( GameplayTags::WallInteractions_PlantDestructionTool ) );
			WallInteractionList().AddPossibleWallInteraction( wallInteractionSys.GetInteractionForTag( GameplayTags::WallInteractions_WallUpgrade ) );
		}
	}

	TArray<FVector> savedPositions;

	UFUNCTION()
	void TrackLastGoodPositions( UNCCoroutine co )
	{
		while ( true )
		{
			if ( IsAlive() && MovementComponent.IsMovingOnGround() )
			{
				bool shouldAdd = true;
				if ( savedPositions.Num() > 0 )
				{
					shouldAdd = !( GetActorLocation() - savedPositions.Last() ).IsNearlyZero();
				}

				if ( shouldAdd )
				{
					savedPositions.Add( GetActorLocation() );
					if ( savedPositions.Num() > 5 )
					{
						savedPositions.RemoveAt( 0 );
					}
				}
			}
			co.Wait( 2.0 );
		}
	}

	TOptional<FVector> GetSafePosition()
	{
		TOptional<FVector> result;

		if ( savedPositions.Num() > 0 && LastGoodPosition().IsReady() )
			result.Set( LastGoodPosition().GetClosestGoodPosition( savedPositions[0] ) );

		return result;
	}

	void Hide()
	{
		net_isHidden.SetNetValue( true );
	}

	void Show()
	{
		net_isHidden.SetNetValue( false );
	}

	UFUNCTION()
	private void OnIsHiddenChanged( bool oldValue, bool newValue )
	{
		Client_OnHiddenChanged();
	}

	void Cl_HideForAntiPeek()
	{
		cl_antiPeekHidden = true;
		Client_OnHiddenChanged();
	}

	void Cl_ClearHideForAntiPeek()
	{
		cl_antiPeekHidden = false;
		Client_OnHiddenChanged();
	}

	bool cl_antiPeekHidden = false;

	void Client_OnHiddenChanged()
	{
		SetActorHiddenInGame( net_isHidden || cl_antiPeekHidden );
	}

	UFUNCTION()
	private void OnPostDamage( UHealthComponent damagedComponent, const FDamageInfo&in damageInfo )
	{
		ScriptAssert( damagedComponent == HealthComponent, "expected local health component" );

		OnDamagedSignal.Emit();
		ScriptCallbacks().server_onPlayerDamaged.Broadcast( this, damageInfo );
	}

	private bool canBeRespawned = false;
	bool GetCanBeRespawned() const
	{
		return canBeRespawned;
	}

	private void SetCanBeRespawned( bool newValue )
	{
		canBeRespawned = newValue;
	}

	UFUNCTION( NotBlueprintCallable )
	private void Client_OnPawnDied( ANCPlayerCharacter player )
	{
		if ( player != this )
			return;

		AAS_PlayerEntity asPawn = Cast<AAS_PlayerEntity>( player );
		asPawn.OnDeathSignal.Emit();
		Client_OnPawnDeath.Broadcast( asPawn );
		SetCanBeRespawned( true );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnPawnRespawned( ANCPlayerCharacter inPlayer )
	{
		if ( inPlayer != this )
			return;

		OnRespawnUnavailable( this );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnRespawnUnavailable( AAS_PlayerEntity inPlayer )
	{
		if ( inPlayer != this )
			return;

		SetCanBeRespawned( false );
	}

	UFUNCTION()
	void OnDealtDamageToPlayer( FDamageInfo info, ANCPlayerCharacter victim )
	{
		// Print( f"{info.attacker} dealt {info.damage} damage to {victim}" );
	}

	UFUNCTION()
	private void OnActiveWeaponChanged( EViewmodelArm vmArm, ANCWeapon oldWeapon, ANCWeapon newWeapon )
	{
		if ( IsValid( newWeapon ) && newWeapon.ViewmodelArm != EViewmodelArm::AltHand )
			OnWeaponChanged.Emit();
	}

	UFUNCTION()
	private void OnJumpStart( ANCPlayerCharacter PlayerCharacter )
	{
		PlayBattleChatter( GameplayTags::Audio_VO_Efforts_Jump );
	}

	UFUNCTION()
	private void OnJumpLand( ANCPlayerCharacter PlayerCharacter )
	{
		// Can use PlayerCharacter.Velocity to estimate fall distance
		const float JUMP_LAND_VELOCITY_THRESHOLD_SQ = 1500.0f * 1500.0f;
		if ( GetFallVelocity().SizeSquared() >= JUMP_LAND_VELOCITY_THRESHOLD_SQ )
			PlayBattleChatter( GameplayTags::Audio_VO_Efforts_JumpLand );
	}

	UFUNCTION()
	private void OnMantleStart( ANCPlayerCharacter PlayerCharacter )
	{
		PlayBattleChatter( GameplayTags::Audio_VO_Efforts_Mantle );
	}

	UFUNCTION()
	private void OnSprintStart( ANCPlayerCharacter PlayerCharacter )
	{
		PlayBattleChatter( GameplayTags::Audio_VO_Efforts_SprintBegin );
	}

	UFUNCTION()
	private void OnSprintEnd( ANCPlayerCharacter PlayerCharacter )
	{
		PlayBattleChatter( GameplayTags::Audio_VO_Efforts_SprintEnd );
	}

	UFUNCTION()
	private void OnPlayerMountBrake( ANCPlayerCharacter PlayerCharacter, bool isHardBrake )
	{
		FGameplayTag VOTag = isHardBrake 
			? GameplayTags::Audio_VO_Efforts_MountWhoaHard
			: GameplayTags::Audio_VO_Efforts_MountWhoaSoft;		
		AAS_PlayerEntity playerEntity = Cast<AAS_PlayerEntity>(PlayerCharacter);
		Dialogue().PlayPlayerDialogue(playerEntity, VOTag, EDialogueSoundSpace::SOUND_WORLD);
	}

	UFUNCTION()
	private void OnPlayerMountSprint( ANCPlayerCharacter PlayerCharacter )
	{
		AAS_PlayerEntity playerEntity = Cast<AAS_PlayerEntity>(PlayerCharacter);
		Dialogue().PlayPlayerDialogue(playerEntity, GameplayTags::Audio_VO_Efforts_MountHeyah, EDialogueSoundSpace::SOUND_WORLD);
	}

	UFUNCTION()
	void OnDroneWarningStarted( ANCPlayerCharacter player )
	{
		if ( !player.IsLocallyControlled() )
			return;
	}

	UFUNCTION()
	void OnDroneWarningEnded( ANCPlayerCharacter player )
	{
		if ( !player.IsLocallyControlled() )
			return;
	}

	UFUNCTION()
	void OnDroneSurveyStarted( ANCPlayerCharacter player )
	{
		if ( !player.IsLocallyControlled() )
			return;
	}

	UFUNCTION()
	void OnDroneSurveyEnded( ANCPlayerCharacter player )
	{
		if ( !player.IsLocallyControlled() )
			return;
	}

	void DisplayCooldownReadyMessage( FGameplayTag index )
	{
		if ( !IsClient() )
			return;

		const FLootDataStruct& lootData = GetLootDataByIndex( index );
		GetLocalHUD().mainHUDWidget.DisplayGenericMessage( GetLocalizedText( Localization::HUDMainWidget, "hint_item_ready", FFormatArgumentValue( lootData.name ) ), EHUDMessageStyle::SMALL_CENTER );
	}

	UFUNCTION( NotBlueprintCallable )
	void OnTeleportPressed( ANCPlayerCharacter player )
	{
		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( player );
		if ( asPlayer.IsTeleportActive() )
			return;

		if ( player.IsInScriptedAnim() )
			return;

		if ( GameModeDefaults().GamemodeRules_AllowTeleportOnlyWhenRaidActive )
		{
			if ( !IsTeamBeingRaided( GetTeam() ) && !IsTeamRaiding( GetTeam() ) && !PlayerIsInEnemyDome( this ) )
			{
				Server_SendGenericMessage( player, Localization::ReturnHome, "return_home_failed_raid_only" );
				return;
			}
		}

		if ( ShouldTeleportToBreacher( player ) )
			TeleportSystem().TryTeleportToBreacher( asPlayer );
		else
			TeleportSystem().TryReturnHome( asPlayer );
	}

	UFUNCTION( NotBlueprintCallable )
	void OnTeleportReleased( ANCPlayerCharacter player )
	{
		OnCancelTeleportSignal.Emit();
	}

	UFUNCTION( BlueprintOverride )
	bool IsPlayerRidingMount() const
	{
		return IsValid( GetPilotedVehicle() );
	}

	void Server_EmitPrivateConversationSound( UNCAudioAsset sound1P, UNCAudioAsset sound3P, ANCPlayerCharacter speakingPlayer, ANCPlayerCharacter listeningPlayer )
	{
		Server_EmitSoundOnEntity_WithSendFlags( sound3P, speakingPlayer, listeningPlayer, ESendEventFlags::SEND_TO_OWNER );
		Server_EmitSoundOnEntity_WithSendFlags( sound1P, speakingPlayer, speakingPlayer, ESendEventFlags::SEND_TO_OWNER );
	}

	UFUNCTION( NotBlueprintCallable )
	void OnBleedoutStarted( AAS_PlayerEntity player )
	{
		healthRegenComponent.SetRegenEnabled( false );
	}

	UFUNCTION( NotBlueprintCallable )
	void OnBleedoutEnded( AAS_PlayerEntity player )
	{
		healthRegenComponent.SetRegenEnabled( true );
	}

	UFUNCTION( BlueprintOverride )
	void UpdateRidingPoseParameters( UNCPlayerCharacterAnimInstance animInstance ) const
	{
		UAS_PlayerAnimInstance AnimBP = Cast<UAS_PlayerAnimInstance>( animInstance );
		if ( !IsValid( AnimBP ) )
		{
			return;
		}

		/////////////////////////////////////////////
		// Vehicle Mount stuff to override Code
		/* 	this should all be handled in code... especially the blendspace for the pilot head turn,
			that should just be a bone driven by code. I prototyped it this way as a blueprint to follow. */

		AnimBP.vehicleMountSpeed		  = 0.0;
		AnimBP.vehicleMountLookYawDelta	  = 0.0;
		AnimBP.vehicleMountLookPitchDelta = 0.0;

		AAS_VehicleMount mount = Mount::GetPilotedMount( this );
		if ( IsValid( mount ) )
		{
			AnimBP.vehicleMountSpeed		  = mount.motionModel.GetLocomotionSpeed();
			FRotator pilotLookDelta			  = mount.motionModel.GetPilotLookDelta();
			AnimBP.vehicleMountLookYawDelta	  = pilotLookDelta.Yaw;
			AnimBP.vehicleMountLookPitchDelta = pilotLookDelta.Pitch;
		}

		FGameplayTag tag = GetEquippedMountTag();
		UAS_UMountAnimAsset animData;
		if ( IsValid( mount ) )
			animData = Mount::GetMountAnimData( mount );
		else if ( IsValidMountTag( tag ) )
			animData = Mount::GetMountAnimData( tag );

		if ( IsValid( animData ) )
		{
			if ( animData.blendSpaces.Contains( EMountScriptACT::SCRIPT_LOCOMOTION ) )
				AnimBP.riderBlendspace = animData.blendSpaces[EMountScriptACT::SCRIPT_LOCOMOTION].Pilot3P.BlendSpace;
			else
				ScriptError( f"Warning! anim EMountScriptACT::SCRIPT_LOCOMOTION doesn't exist in mount anim asset {animData.Name}" );
		}
	}

	UFUNCTION( BlueprintOverride )
	void OnWeaponConsumedSharedAmmo( EAmmoSource source, int amountConsumed )
	{
		FLootDataStruct data = GetLootDataForAmmo( source );
		RemoveFromAnyBackpack( MakeBackpackItem( data.index, amountConsumed, -1, 0 ), false );
	}

	UFUNCTION( BlueprintOverride )
	void OnWeaponConsumedScriptManagedAmmo( ANCWeapon weapon, int ammoConsumed )
	{
		if ( !IsValid( weapon ) )
			return;

		FLootDataStruct lootData;
		ELootType lootType = GetLootTypeForWeapon( weapon.GetWeaponClass() );
		if ( lootType != ELootType::Grenade )
			return;

		lootData = GetLootDataForGrenade( weapon );
		RemoveFromAnyBackpack( MakeBackpackItem( lootData.index, ammoConsumed, GetDesiredConditionMax( lootData ), 0 ) );
	}

	UFUNCTION()
	private void Server_OnSpawned( ANCPlayerCharacter Pawn )
	{
		savedPositions.Empty();
	}

	UFUNCTION( NotBlueprintCallable )
	void OnDeath( const FDamageInfo&in DamageInfo, ANCPlayerCharacter player )
	{
		OnDeathSignal.Emit();

		ANCPlayerCharacter attacker = DamageInfo.attacker;
		if ( IsValid( attacker ) )
		{
			float healthPerc		  = attacker.GetHealthNormalized();
			int shieldLevel			  = Math::FloorToInt( attacker.GetMaxShieldHealth() / PlayerWidgetConst::HEALTH_PER_SEGMENT );
			FGameplayTag shieldRarity = attacker.GetEquippedShieldData().shieldRarity;
			float shieldPerc		  = attacker.GetShieldHealthNormalized();
			float hardenPerc		  = attacker.GetStatusEffectValue( GameplayTags::StatusEffect_Hardened );
			UNCRemoteScriptCommands::SendServerCommand( player, f"TrackWhoKilledMe {healthPerc} {shieldLevel} {shieldRarity.ToString()} {shieldPerc} {hardenPerc}" );
		}

		TArray<FPlayerDamageAtTime> summary = player.GetRecentDamage();
		TArray<ANCPlayerCharacter> assistPlayers;
		for ( FPlayerDamageAtTime data : summary )
		{
			FSimplifiedDamageEvent e = data.EventDetails;
			ANCPlayerCharacter a	 = GetPlayer( e.Attacker );

			if ( !IsValid( a ) )
				continue;

			if ( a == attacker )
				continue;

			if ( a.GetTeam() == player.GetTeam() )
				continue;

			assistPlayers.AddUnique( a );
		}

		for ( ANCPlayerCharacter a : assistPlayers )
		{
			ScriptCallbacks().server_onPlayerAssist.Broadcast( a, player, DamageInfo );
		}

		PlayBattleChatter( GameplayTags::Audio_VO_Efforts_Death );
	}

	UFUNCTION()
	void ClearAllBackpackContents( bool resetSizes = false )
	{
		for ( UAS_BackpackComponent backpack : GetBackpackComponents() )
		{
			backpack.Empty();
		}

		if ( resetSizes )
		{
			backpackComponent.SetNumSlots( GameModeDefaults().GamemodeRules_PlayerBackpackSize );
		}
	}

	UFUNCTION()
	void ClearAndResetCloudStorage()
	{
		AAS_TeamConnectedBox teamContainer = GetTeamCloudContainerForPlayer( this );
		teamContainer.ClearAndResetCloudInventory();
	}

	UFUNCTION()
	void MoveAllItemsToCloudStorage()
	{
		AAS_TeamConnectedBox teamContainer = GetTeamCloudContainerForPlayer( this );

		// Omit class backpack and move each backpack explicitly instead of iterating through GetBackpackComponents and filtering out class items
		MoveBackpackItemsToCloudStorage( resourceBackpackComponent );
		MoveBackpackItemsToCloudStorage( ammoBackpackComponent );
		MoveBackpackItemsToCloudStorage( healsBackpackComponent );
		MoveBackpackItemsToCloudStorage( backpackComponent );

		// Don't send blue shields to team box
		TArray<FBackpackItemStruct> shields = equippedShieldBackpackComponent.GetBackpackContents();
		for ( FBackpackItemStruct shieldItem : shields )
		{
			if ( shieldItem.itemIndex == GameConst::DEFAULT_SHIELD_TAG )
			{
				continue;
			}

			int leftover = teamContainer.AddToAnyBackpack( shieldItem );
			if ( leftover > 0 )
			{
				continue;
			}

			equippedShieldBackpackComponent.Empty();
		}

		TArray<ANCWeapon> weapons = GetWeaponInventory();
		for ( ANCWeapon weap : weapons )
		{
			if ( IsLootIndexValidForWeapon( weap ) )
			{
				teamContainer.AddToAnyBackpack( MakeBackpackItem( weap ) );
			}

			RemoveWeaponFromPlayer( this, weap.GetWeaponSlot(), ERemoveWeaponType::DESTROY );
		}

		TMap<int, FBackpackItemStruct> savedWeapons = saveLootOnDeathComponent.GetSavedHeldWeaponItems();

		for ( TMapIterator<int, FBackpackItemStruct> savedWeapon : savedWeapons )
		{
			FBackpackItemStruct newItem = FBackpackItemStruct( savedWeapon.Value );
			teamContainer.AddToAnyBackpack( newItem );
		}

		ClearAllBackpackContents();
	}

	UFUNCTION()
	void MoveBackpackItemsToCloudStorage( UAS_BackpackComponent backpack )
	{
		AAS_TeamConnectedBox teamContainer = GetTeamCloudContainerForPlayer( this );

		TArray<FBackpackItemStruct> items	  = backpack.GetBackpackContents();
		TArray<FBackpackItemStruct> leftovers = teamContainer.AddToAnyBackpack_Array( items );

		backpack.Empty();

		backpack.AddToBackpack_Array( leftovers );
	}

	FVector prevPos		   = FVector::ZeroVector;
	float prevZ			   = 0;
	bool wasADSToggled	   = false;
	float32 lastADSOutTime = 0;

	UFUNCTION( BlueprintOverride )
	void Tick( float DeltaSeconds )
	{
		FVector newPos = GetActorLocation();
		if ( GetCvarBool( f"ScriptDebug.DebugSpeed" ) && IsServer() )
		{
			// this will track true speed regardless of riding a mount or not
			float dist = ( newPos - prevPos ).Size2D();

			float speed = dist / DeltaSeconds;
			Print( f"SERVER 2D Speed: {speed}" );
		}
		else if ( GetCvarBool( f"ScriptDebug.DebugZSpeed" ) && IsServer() )
		{
			// this will track true speed regardless of riding a mount or not
			float dist = newPos.Z - prevPos.Z;

			float speed = dist / DeltaSeconds;
			Print( f"SERVER Z Speed: {speed} Accel: {(speed - prevZ) / DeltaSeconds}" );
			prevZ = speed;
		}

		prevPos = newPos;

		if ( !AdsIsToggled && wasADSToggled )
		{
			lastADSOutTime = TO_SECONDS( GetTimeMilliseconds() );
		}
		wasADSToggled = AdsIsToggled;

		if ( IsServer() || ( IsClient() && IsLocallyControlled() ) )
		{
			const int currentValues = int( WeaponSlotsDisabled );
			const int difference	= currentValues ^ oldWeaponSlotState;
			for ( int i = 0; i < numWeaponInventorySlots; i++ )
			{
				const int bitCheck = 1 << i;
				if ( Bitflags::HasFlag( difference, bitCheck ) )
				{
					bool enabled = WeaponSlotIsEnabled( WeaponSlot::TacticalSlot );
					// Casting a bool to an int yields undefined values
					int enabledInt = enabled ? 1 : 0;
					ScriptCallbacks().onPlayerWeaponSlotEnabledChanged.Broadcast( this, i, oldWeaponSlotState & bitCheck, enabledInt );
				}
			}

			oldWeaponSlotState = currentValues;
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void OnBackpackContentsChanged( UBackpackComponent newBackpack )
	{
		// different deferral timings for client and server -- EndOfFixedTick is the only acceptable deferral on the server
		if ( IsServer() )
			deferredBackpackChangeProcessing.ExecuteOnceLater( ENCDeferredScriptActionExecutionTime::EndOfFixedTick );
		else
			deferredBackpackChangeProcessing.ExecuteOnceLater( ENCDeferredScriptActionExecutionTime::UpdateGameObjectsBeforeHUD );
	}

	bool sv_hasShieldBreacher;

	UFUNCTION( NotBlueprintCallable )
	void ProcessBackpackChanges()
	{
		if ( IsServer() )
		{
			bool hadShieldBreaker = sv_hasShieldBreacher;
			sv_hasShieldBreacher  = PlayerHasShieldBreaker( this );

			if ( hadShieldBreaker != sv_hasShieldBreacher )
			{
				ScriptCallbacks().server_onPlayerHasShieldBreakerChanged.Broadcast( this, hadShieldBreaker, sv_hasShieldBreacher );
			}

			server_onAnyBackpackChanged.Broadcast( this );
		}

		if ( IsClient() )
		{
			if ( IsLocallyControlled() )
				ScriptCallbacks().localClient_onBackpackContentsChanged.Broadcast( this );

			client_onAnyBackpackChanged.Broadcast( this );
		}
	}

	UFUNCTION()
	private void OnAnyBackpackChanged( AAS_PlayerEntity player )
	{
		if ( GetBugReproNum() == 8210 )
		{
			Cl_UpdateShieldBreakerState();
		}
		else
		{
			System::SetTimer( this, n"BugRepro_DelayThread_ShieldBreakerModel", 0.1, false );
		}
	}

	UFUNCTION()
	void BugRepro_DelayThread_ShieldBreakerModel()
	{
		Cl_UpdateShieldBreakerState();
	}

	bool cl_hasSB = false;

	void Cl_UpdateShieldBreakerState()
	{
		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			bool hadShieldBreaker  = cl_hasSB; // !shieldBreacherRoot.bHiddenInGame;
			bool hasShieldBreacher = PlayerHasShieldBreaker( this );

			if ( hasShieldBreacher && !hadShieldBreaker )
			{
				shieldBreacherBeam.SetHiddenInGame( false );
				shieldBreacherRoot.SetHiddenInGame( false, false );
				shieldBreacherTrail.Activate( true );
				shieldBreacherTrail.SetHiddenInGame( false );
			}
			else if ( !hasShieldBreacher && hadShieldBreaker )
			{
				// remove cl breacher model;
				shieldBreacherBeam.SetHiddenInGame( true );
				shieldBreacherRoot.SetHiddenInGame( true, false );
				shieldBreacherTrail.Deactivate();
				shieldBreacherTrail.SetHiddenInGame( true );
			}

			if ( hasShieldBreacher != hadShieldBreaker )
			{
				scriptCallbacks.client_onShieldBreakerStatusChanged.Broadcast( this );
				scriptCallbacks.client_OnRefreshAllMapMarkers.Broadcast();
			}
			cl_hasSB = hasShieldBreacher;
		}
	}

	UFUNCTION()
	void OnDamaged( FDamageInfo& DamageInfo, ANCPlayerCharacter attacker )
	{
		float32 time = GetTimeMilliseconds();

		int scriptFlags = DamageInfo.scriptDamageFlags;

		if ( time > onDamaged_nextAllowOnDamageMoveSlow && ( scriptFlags & EScriptDamageFlags::DF_NO_HIT_SLOW == 0 ) )
		{
			AddStatusEffect( GameplayTags::StatusEffect_MoveSlow, 0.4, 0.0, 0.2, 0.2 );
			// AddStatusEffect(GameplayTags::StatusEffect_SprintSlow, 1.0, 1.5, 0.5 );
			onDamaged_nextAllowOnDamageMoveSlow = time + TO_MILLISECONDS( onDamaged_moveSlowCooldown );
		}

		PlayOnDamagedVO( DamageInfo );
	}

	private void PlayOnDamagedVO( FDamageInfo& DamageInfo )
	{
		if ( Bitflags::HasFlag( DamageInfo.scriptDamageFlags, EScriptDamageFlags::DF_FALL_DAMAGE ) )
			return;

		const float DAMAGE_MAJOR_THRESHOLD = 75.0f;
		if ( DamageInfo.damage >= DAMAGE_MAJOR_THRESHOLD )
			PlayBattleChatter( GameplayTags::Audio_VO_Efforts_PainMajor );
		else
			PlayBattleChatter( GameplayTags::Audio_VO_Efforts_PainMinor );

		ANCProjectile inflictorAsProjectile = Cast<ANCProjectile>( DamageInfo.Inflictor );
		if ( IsValid( inflictorAsProjectile ) )
		{
			FName weaponClassName = inflictorAsProjectile.GetWeaponClass();
			if ( IsValidWeaponClassName( weaponClassName ) )
			{
				UWeaponPrimaryAsset weaponClass = GetWeaponAsset( weaponClassName );
				if ( GetWeaponScriptData( weaponClass ).playerLoadoutType == EWeaponLoadoutType::PRIMARY )
				{
					PlayBattleChatter( GameplayTags::Audio_VO_BattleChatter_GettingShot );
					return;
				}
			}
		}

		PlayBattleChatter( GameplayTags::Audio_VO_BattleChatter_TakingDamage );
	}

	UFUNCTION( BlueprintOverride )
	void EndPlay( EEndPlayReason EndPlayReason )
	{
		if ( IsAlive() )
		{
			OnDeathSignal.Emit();
		}
	}

	UFUNCTION( BlueprintOverride )
	void OnLanded( FHitResult Hit )
	{
		fallDamageComponent.OnPlayerLanded( Hit );
		onPlayerLandedSignal.Emit();
	}

	UPROPERTY()
	FNCNetBool net_PlayingFullBodyOverride;

	UPROPERTY()
	FNCNetAsset net_FullBodyOverrideSequence;

	access:ScriptedAnim void __PlayFullbodyLoopingAnim( UAnimSequence sequence )
	{
		net_FullBodyOverrideSequence.SetNetValue( sequence );
		net_PlayingFullBodyOverride.SetNetValue( true );
	}

	access:ScriptedAnim void __StopFullbodyLoopingAnim()
	{
		net_PlayingFullBodyOverride.SetNetValue( false );
	}

	void SetBaseMaster()
	{
		// net_IsBaseMaster.SetNetValue( true );
	}

	////////////////////////////////////////////////////////////////////////
	// Remove these once code does script entry points feature
	////////////////////////////////////////////////////////////////////////
	UFUNCTION( BlueprintOverride )
	void BeginPlay()
	{
		if ( IsServer() )
		{
			ServerBeginPlay();
		}
		else
		{
			BindOrCallWhenHUDInitialized( FOnHudInitializedCallback( this, n"OnHUDInit" ) );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void OnHUDInit( ANC_HUD hud )
	{
		ClientBeginPlay();
	}

	////////////////////////////////////////////////////////////////////////////////////////////////////////////////
	// Copy from renderer(cl_renderPawn)
	////////////////////////////////////////////////////////////////////////////////////////////////////////////////
	UPROPERTY( DefaultComponent, Attach = PlayerCamera, DisplayName = "DamageArrowNode" )
	USceneComponent DamageArrowNode;
	default DamageArrowNode.RelativeLocation = FVector( 80, 0, -15 );
	default DamageArrowNode.RelativeRotation = FRotator( 12.5, 0, 0 );

	UPROPERTY( DefaultComponent )
	UAS_TPPCameraComponent TPPCameraComponent;
	UPROPERTY( DefaultComponent )
	UAS_TPPCameraSettingsManager TPPSettingsComponent;
	UPROPERTY( DefaultComponent )
	UAS_TPPCameraSettingsManager SpectateSettingsComponent;
	UPROPERTY( DefaultComponent )
	UAS_CameraManagerComponent CameraManagerComponent;

	UPROPERTY( EditAnywhere, BlueprintReadOnly )
	FTPPCameraSettings DefaultTPPCameraSettings;
	default DefaultTPPCameraSettings.Set( ETPPCameraSetting::FOV, FOV_DEFAULT );
	const float SPRING_ARM_LENGTH = GetCvarBool( "ScriptDebug.LockTPPCamDist" ) ? GetCvarFloat( "ScriptDebug.LockTPPCamDist" ) : TPPConst::DEFAULT_SPRING_ARM_LENGTH;
	default DefaultTPPCameraSettings.Set( ETPPCameraSetting::TargetArmLength, SPRING_ARM_LENGTH );
	default DefaultTPPCameraSettings.Set( ETPPCameraSetting::CameraLagSpeed, TPPConst::DEFAULT_SPRING_LAG_SPEED );
	default DefaultTPPCameraSettings.Set( ETPPCameraSetting::CameraRotationLagSpeed, TPPConst::DEFAULT_SPRING_ROT_LAG_SPEED );
	default DefaultTPPCameraSettings.Set( ETPPCameraSetting::CameraLagMaxDistance, TPPConst::DEFAULT_SPRING_LAG_MAX_DIST );
	default DefaultTPPCameraSettings.Set( ETPPCameraSetting::SpringArmOffsetVector, TPPConst::DEFAULT_SPRING_OFFSET );
	default DefaultTPPCameraSettings.Set( ETPPCameraSetting::CameraOffsetVector, TPPConst::DEFAULT_TPPCAM_OFFSET );

	UPROPERTY( DefaultComponent, Attach = PlayerCamera )
	UNCSpringArmComponent TPP_SpringArm;

	// This component will manage attaching the 3P camera to a bone during an animation, and
	// prevent the camera from clipping through geometry.
	UPROPERTY( DefaultComponent )
	UNCThirdPersonCameraAnimationArm TPP_CameraAnimationArm;

	UPROPERTY( DefaultComponent, Attach = TPP_SpringArm, AttachSocket = SpringEndpoint )
	UCameraComponent TPP_Camera;
	default TPP_Camera.RelativeLocation = FVector::ZeroVector;

	UPROPERTY( DefaultComponent, Attach = PlayerCamera )
	UNiagaraComponent speedlinesFX;
	default speedlinesFX.SetVisibility( false );
	default speedlinesFX.SetAutoActivate( false );

	// Reticle placement
	FVector lastSocketPos;
	bool lastSocketPosSet = false;

	UPROPERTY( DefaultComponent, Attach = PlayerCamera )
	USceneCaptureComponent2D flashbangCaptureComponent2D;

	UFUNCTION( BlueprintOverride )
	void AngelScript_UpdateViewModelTransformValues( float DeltaSeconds )
	{
		UpdateCameraFov( DeltaSeconds );
	}

	ENCSpectateCameraState GetDesiredSpectateState()
	{
		if ( IsLocallyControlled() || IsServer() )
		{
			return ENCSpectateCameraState::Default;
		}

		if ( IsPlayerRidingMount() )
		{
			return ENCSpectateCameraState::WhileMounted;
		}

		return ENCSpectateCameraState::Default;
	}

	TOptional<FTPPCameraSettings> GetDesiredSpectateSettings( ENCSpectateCameraState newState )
	{
		TOptional<FTPPCameraSettings> tppCameraSettings;

		switch ( newState )
		{
			case ENCSpectateCameraState::Default:
			{
				// do nothing
			}
			break;
			case ENCSpectateCameraState::WhileMounted:
			{
				tppCameraSettings = FTPPCameraSettings();
				tppCameraSettings.GetValue().Set( ETPPCameraSetting::CameraOffsetVector, FVector( 0.f, 25.0f, 50.f ) );
			}
			break;
			default:
			{
				ScriptAssert( false, f"Unhandled spectate camera state!" );
			}
			break;
		}

		return tppCameraSettings;
	}

	ENCSpectateCameraState CurrentSpectateState = ENCSpectateCameraState::Default;
	int32 previousSpectateSettingsId			= -1;
	int32 currentSpectateSettingsId				= -1;

	void UpdateSpectateSettings()
	{
		ENCSpectateCameraState desiredState = GetDesiredSpectateState();
		if ( desiredState == CurrentSpectateState )
		{
			return;
		}

		if ( previousSpectateSettingsId != -1 )
		{
			const bool shouldForceClearPrev = SpectateSettingsComponent.IsOverrideActive( previousSpectateSettingsId );

			if ( shouldForceClearPrev )
			{
				SpectateSettingsComponent.ClearOverrideSettings( previousSpectateSettingsId );
			}

			previousSpectateSettingsId = -1;
		}

		if ( currentSpectateSettingsId != -1 )
		{
			const float DEFAULT_LERP_OUT_TIME		= 0.5f;
			const EEasingFunc DEFAULT_LERP_OUT_EASE = EEasingFunc::EaseIn;
			SpectateSettingsComponent.ClearOverrideSettingsWithLerp( currentSpectateSettingsId, DEFAULT_LERP_OUT_TIME, DEFAULT_LERP_OUT_EASE );

			previousSpectateSettingsId = currentSpectateSettingsId;
			currentSpectateSettingsId  = -1;
		}

		TOptional<FTPPCameraSettings> settingsToApply = GetDesiredSpectateSettings( desiredState );
		if ( settingsToApply.IsSet() )
		{
			const float DEFAULT_LERP_IN_TIME	   = 0.5f;
			const EEasingFunc DEFAULT_LERP_In_EASE = EEasingFunc::EaseIn;
			currentSpectateSettingsId			   = SpectateSettingsComponent.AddOverrideSettingsWithLerp( n"SpectateSettings", settingsToApply.GetValue(), DEFAULT_LERP_IN_TIME, DEFAULT_LERP_In_EASE );
		}

		CurrentSpectateState = desiredState;
	}

	// Called every tick when this character is being spectated
	UFUNCTION( BlueprintOverride )
	void WhileSpectatedTick()
	{
		UpdateSpectateSettings();

		CameraManagerComponent.WhileSpectatedTick();
	}

	private bool isCurrentlyTryingToFire = false;
	private int lastFireInputTimeMS;
	UFUNCTION()
	private void OnFirePressed()
	{
		isCurrentlyTryingToFire = true;
		lastFireInputTimeMS		= GetTimeMilliseconds();
	}

	UFUNCTION()
	private void OnFireReleased()
	{
		isCurrentlyTryingToFire = false;
		lastFireInputTimeMS		= GetTimeMilliseconds();
	}

	UFUNCTION( BlueprintOverride )
	bool WeaponCanSwapScopes( ANCWeapon activeWeapon )
	{
		if ( !IsValid( activeWeapon ) )
		{
			return false;
		}

		if ( GetAdsFraction() < 1.0 )
		{
			return false;
		}

		if ( isCurrentlyTryingToFire )
		{
			return false;
		}

		int curTimeMS = GetTimeMilliseconds();
		if ( curTimeMS - lastFireInputTimeMS <= 0.1 )
		{
			return false;
		}

		if ( TO_SECONDS( curTimeMS - int( activeWeapon.LastAttackTime ) ) < 1 )
		{
			return false;
		}

		return true;
	}

	UFUNCTION( BlueprintOverride )
	EWeaponInventorySlot GetUltimateWeaponSlot()
	{
		return EWeaponInventorySlot( WeaponSlot::RaidUltSlot );
	}

	UFUNCTION( BlueprintOverride )
	EWeaponInventorySlot GetTacticalWeaponSlot()
	{
		return EWeaponInventorySlot( WeaponSlot::TacticalSlot );
	}

	UFUNCTION( BlueprintOverride )
	bool CanPlayerUseUltimateWeapon( bool isChordedInput )
	{
		bool canUseRaidUlt = PlayerCanUseRaidUlt( this );

		if ( isChordedInput && IsClient() )
		{
			UCl_PingManager pingManager = ClPingManager();
			if ( IsValid( pingManager ) )
				pingManager.BlockNextPingInput();
		}

		if ( !canUseRaidUlt )
		{
			int numUltAccel = this.CountItemsInAllBackpacks( GameplayTags::Loot_HealItem_UltimateAccel );
			if ( numUltAccel > 0 && !IsInScriptedAnim() )
			{
				const FLootDataStruct& loot = GetLootDataByIndex( GameplayTags::Loot_HealItem_UltimateAccel );
				if ( IsServer() )
					this.TryUseUltimateAccelFromBackpack( loot );
			}
			else
			{
				if ( IsClient() )
				{
					ScriptCallbacks().localClient_OnTriedToUseUltimateOnCooldown.Broadcast();
					// AAS_HUD hud = Cast<AAS_HUD>( GetHUD() );
					// if ( IsValid( hud ) )
					//	hud.mainHUDWidget.DisplayGenericMessage( GetLocalizedText( Localization::Characters, "ultimate_not_ready" ), EHUDMessageStyle::SMALL_CENTER );
				}
			}

			return false;
		}

		return true;
	}

	UFUNCTION( BlueprintOverride )
	ANCVehicle UpdateAndSummonOwnedVehicle( FVector summonLocation, FRotator summonRotation )
	{
		ScriptAssert( IsServer(), "MUST RUN ON SERVER" );

		AAS_VehicleMount mount = GetMountComponent().GetCurrentMount();
		FGameplayTag mountTag = GetEquippedMountTag();

		if (IsValid(mount) && Mount::GetGameplayTagFromMount(mount) != mountTag)
		{
			mount.Destroy();
		}

		if (!IsValid(mount))
		{
			TSubclassOf<AAS_VehicleMount> mountClass = Mount::GetMountClassFromGameplayTag( mountTag );
			mount = Cast<AAS_VehicleMount>( Server_SpawnEntity( mountClass, this, summonLocation, summonRotation ) );
			GetMountComponent().SetCurrentMount( mount );
		}

		return mount;
	}

	UFUNCTION( BlueprintOverride )
	bool CanPlayerEmbarkOnVehicle()
	{
		if ( Passives().HasPassive( GameplayTags::Classes_Passives_BusyOnZipline ) )
			return false;

		if ( !Mount::CanSwapToMountWithMessageOnClient( this ) )
		{
			if ( IsClient() )
			{
				if ( GetSlowZoneComponent().inSlowZone )
				{
					AAS_HUD hud = Cast<AAS_HUD>( GetHUD() );
					if ( IsValid( hud ) )
					{
						hud.mainHUDWidget.DisplayGenericMessage( GetLocalizedText( Localization::Mount, f"mount_summonfail_dangerzone" ), EHUDMessageStyle::SMALL_CENTER );
					}
				}
			} 
			
			return false;
		}

		if ( IsClient() && !IsPlayerRidingMount() && GetCvarBool( f"VehicleMount.DebugSpawn" ) )
			Mount::MountSpawnCapsuleTraceFromPlayer( this, nullptr, EDrawDebugTrace::ForDuration );

		return true;
	}

	UFUNCTION( BlueprintOverride )
	FNCPlayerVehicleSpawnParams GetVehicleSpawnParamsForSummon()
	{
		FNCPlayerVehicleSpawnParams params;
		params.bIsValid = false;
		
		FGameplayTag tag							   = GetEquippedMountTag();
		TSubclassOf<AAS_VehicleMount> mountClass	   = Mount::GetMountClassFromGameplayTag( tag );
		params = Mount::MountSpawnCapsuleTraceFromPlayer( this, mountClass );

		if (!params.bIsValid)
		{
			if (IsServer())
			{
				// FString message = "Not Enough Room";
				Server_SendGenericMessage( this, Localization::Mount, "mount_summonfail_room" );
			}
		}

		params.SpawnRotation = FRotator( 0, GetViewRotation().Yaw, 0 );
		
		return params;
	}

	void TryUseUltimateAccelFromBackpack( FLootDataStruct lootData, int _numToUse = -1 )
	{
		UAS_UltAccelerantAnimThread thread = Cast<UAS_UltAccelerantAnimThread>( CreateThread( UAS_UltAccelerantAnimThread::StaticClass(), this ) );
		thread.Init( lootData, _numToUse );
	}

	UFUNCTION( BlueprintOverride )
	void OnWeaponScopeSwapRequested( ANCWeapon activeWeapon )
	{
		if ( IsClient() )
			return;

		float32 elapsedSinceLastADSExit	   = TO_SECONDS( GetTimeMilliseconds() ) - lastADSOutTime;
		bool releasedADSLateEnough		   = elapsedSinceLastADSExit < 0.15;
		bool releasedADSTooLongAgoToMatter = elapsedSinceLastADSExit > 0.7;
		if ( !releasedADSLateEnough && !releasedADSTooLongAgoToMatter )
		{
			return;
		}

		if ( isCurrentlyTryingToFire )
		{
			return;
		}

		// Artificially longer to catch firing during scope swap anim. Numbers are fudged.
		int curTimeMS = GetTimeMilliseconds();
		if ( curTimeMS - lastFireInputTimeMS <= 0.25 )
		{
			return;
		}

		TArray<FName> opticMods = Weapons().GetOpticModNamesForWeaponID( MakeWeaponId( activeWeapon ) );

		int numOpticMods = opticMods.Num();
		if ( numOpticMods == 0 )
		{
			return;
		}

		int idx_selectedOptic = 0;
		for ( int i = 0; i < numOpticMods; i++ )
		{
			if ( IsModActiveOnWeapon( activeWeapon, opticMods[i] ) )
			{
				idx_selectedOptic = i;
				break;
			}
		}

		int idx_nextOptic					 = ( idx_selectedOptic + 1 ) % numOpticMods;
		FName nextMod						 = opticMods[idx_nextOptic];
		FWeaponAttachmentData attachmentData = GetAttachmentData( nextMod );
		FGameplayTag attachmentIndex		 = attachmentData.attachmentIndex;

		WeaponLoadouts().ModifyWeaponOptic( this, activeWeapon, attachmentIndex );
		onPlayerScopeSwap.Broadcast( this, activeWeapon );
	}

	UFUNCTION( BlueprintOverride )
	void SetClassID( FGameplayTag classID )
	{
		if ( IsClient() )
			return;

		classManager.SetClass( classID );
	}

	UFUNCTION( BlueprintOverride )
	void NotifyAttachedCameraUpdated()
	{
		if ( IsValid( GetViewmodelWeapon() ) )
		{
			FVector EyeLocation	 = PlayerCameraComponent.GetWorldLocation();
			FRotator EyeRotation = GetAimRotationNoJitter();
			UpdateReticle( EyeLocation, EyeRotation );
		}
	}

	UFUNCTION( BlueprintOverride )
	void GetImpactData( FHitResult result, FNCPlayerImpactData&in dataOut ) const
	{
		bool hardened = GetStatusEffectValue( GameplayTags::StatusEffect_Hardened ) > 0.0;
		if ( GetStatusEffectValue( GameplayTags::StatusEffect_Hardened ) > 0.0 )
		{
			dataOut.ImpactSurface = ( result.BoneName == ImpactConsts::HEAD_BONE ) ? ImpactConsts::HARDENDED_CRIT_SURFACE : ImpactConsts::HARDENDED_SURFACE;
		}
		else if ( asHealthComponent.GetShieldHealth() > 0 )
		{
			dataOut.ImpactSurface			   = ( result.BoneName == ImpactConsts::HEAD_BONE ) ? ImpactConsts::SHIELD_CRIT_SURFACE : ImpactConsts::SHIELD_SURFACE;
			FShieldItemData equippedShieldData = GetEquippedShieldData();
			dataOut.ShieldRarity			   = equippedShieldData.shieldRarity;
		}
		else
		{
			dataOut.ImpactSurface = ( result.BoneName == ImpactConsts::HEAD_BONE ) ? ImpactConsts::FLESH_CRIT_SURFACE : ImpactConsts::FLESH_SURFACE;
		}
	}

	void UpdateReticle( FVector eyeLocation, FRotator cameraAngles )
	{
		ANCWeapon activeWeapon = GetActiveWeapon();
		if ( !IsValid( activeWeapon ) )
			return;

		UNCViewmodelComponent viewmodel = GetViewmodelComponent();

		USkeletalMeshComponent weaponSight = viewmodel.GetWeaponSight();
		weaponSight.SetRenderCustomDepth( true );
		weaponSight.CustomDepthStencilValue = EHighlightStencilValue::STENCIL_TEMP_WEAPON_SIGHT;

		UNCSkeletalMeshComponent vmWeapon = GetViewmodelWeapon();

		FVector eyeForward = activeWeapon.GetWeaponAimDirection( cameraAngles );

		// Show reticle widget if ironsights, hide it if there is a scope.
		// Hack: We don't delete the reticle widget when we're not using it. Because it still runs the UMG that we take the render target from.
		bool isIronsights = IsValid( weaponSight.GetSkeletalMeshAsset() ) ? false : true;

		FVector planeLocation = eyeLocation + ( eyeForward * 10 );
		FVector planeNormal	  = eyeForward;
		FVector intersection  = Math::LinePlaneIntersection( eyeLocation, eyeLocation + ( eyeForward * 100.0f ), planeLocation, planeNormal );

		FVector targetReticlePosition = intersection;

		UNCViewmodelWidgetComponent reticleWidget = viewmodel.ReticleWidgetADS;
		UAS_ReticleWidget dynamicReticle;
		if ( IsValid( reticleWidget ) )
		{
			// Put in a dedicated 'show widget' spot
			reticleWidget.SetTickMode( ETickMode::Enabled );
			reticleWidget.SetVisibility( true );
			reticleWidget.SetManuallyRedraw( false );

			UUserWidget object = reticleWidget.GetUserWidgetObject();
			dynamicReticle	   = Cast<UAS_ReticleWidget>( object );
			if ( IsValid( dynamicReticle ) )
			{
				float dt = Gameplay::GetWorldDeltaSeconds();
				dynamicReticle.ReticleTick( activeWeapon, dt );
			}

			// I have no idea why this is still necessary wtf
			reticleWidget.SetWorldLocation( targetReticlePosition );
		}

		// This is so weird -- can this be disabled elsewhere
		// reticleWidget.GetMaterialInstance().SetScalarParameterValue( n"Opacity", 0 );

		bool isScopeReticle = !isIronsights && IsValid( viewmodel.ScopeMaterial ) && IsValid( dynamicReticle );

		// TEST
		// The viewport we project against is last frame's viewport. Or uses data from last frame.
		// If we want the reticle to look correct, we also have to use last frame's data.
		// In the future, this could happen at a different stage of the frame getting run. Until then, it can be a frame behind.
		FVector locationToUse = targetReticlePosition;

		// Set after first frame
		if ( lastSocketPosSet )
		{
			locationToUse = lastSocketPos;
		}

		ANCPlayerController controller = Client_GetLocalPlayerController();
		FVector2D projectedReticlePos;
		Gameplay::ProjectWorldToScreen( controller, locationToUse, projectedReticlePos, false );
		AAS_HUD hud = GetLocalHUD();
		hud.SetHitmarkerPosition( projectedReticlePos );
		lastSocketPos	 = targetReticlePosition;
		lastSocketPosSet = true;

		// Reticle is drawn on sight glass
		if ( isScopeReticle )
		{
			// Gameplay::ProjectWorldToScreen( controller, locationToUse, projectedReticlePos, false );
			FVector2D vpSize	  = WidgetLayout::GetViewportSize();
			FLinearColor paramVal = FLinearColor( projectedReticlePos.X / vpSize.X, projectedReticlePos.Y / vpSize.Y, 0, 0 );

			viewmodel.ScopeMaterial.SetVectorParameterValue( n"CenterPointUV", paramVal );

			float adsFrac = AdsFraction;

			FVector adSSocketLocation		 = UNCUtils::GetViewmodelSocketLocation( n"ads_positionSocket", PlayerCameraComponent, vmWeapon, cameraAngles, true );
			FVector weapSightEyeViewLocation = UNCUtils::GetViewmodelSocketLocation( n"eyeview", PlayerCameraComponent, weaponSight, cameraAngles, true );

			float distToSocketFromADSLoc = adSSocketLocation.Distance( weapSightEyeViewLocation ); // This is the baseline distance. If EyeLocation.Distance(Intersection) is this distance, we're at baseline.

			// TODO: This doesn't visually scale correctly when FOV contracts in ADS. It is supposed to keep reticle size on screen constant even if gun is getting closer/further from eye.
			float eyeDistToSocket	= eyeLocation.Distance( weapSightEyeViewLocation ) - ( activeWeapon.WeaponConfigData.viewmodelZoomOffset * adsFrac ).Size();
			float inverseScaleRatio = eyeDistToSocket / Math::Max( distToSocketFromADSLoc, 0.001 );
			float quadSize			= 1.0f / inverseScaleRatio;

			// Looks bad when the reticle expands as it gets closer to you when you shoot.
			quadSize = Math::Clamp( quadSize, 0.0f, 1.0f );
			// The viewmodel's FOV changes as we zoom, which in turn changes the on-screen size. In ADS, this won't scale. In hipfire, this will factor in for hipfire FOV "shrinking" how big the gun looks.
			// float viewmodelAdsFovToRegularFovRatio = ActiveWeapon.WeaponConfigData.viewmodelZoomFov / ReticleWidgetADS.DesiredViewmodelFOV;

			// Need to get baseline FOV from settings
			// float currentFovToWorldFovRatio = 100 / GetRendererCamera().FieldOfView;

			// quadSize *= viewmodelAdsFovToRegularFovRatio;
			quadSize *= activeWeapon.WeaponConfigData.reticleWidgetScale;
			// quadSize *= currentFovToWorldFovRatio;
			// quadSize *= 0.5;

			FLinearColor quadParam = FLinearColor( quadSize, quadSize, 0.0, 0.0 );
			viewmodel.ScopeMaterial.SetVectorParameterValue( n"QuadSizeUV", quadParam );
			viewmodel.ScopeMaterial.SetScalarParameterValue( n"Opacity", 1 );
			viewmodel.ScopeMaterial.SetScalarParameterValue( n"Intensity", 8.0 );
		}
	}

	const float FOV_DEFAULT			 = 90.0;
	const float FOV_PLAYER_MAX		 = 110.0;
	const float FOV_MOUNT_MAX		 = 115.0;
	const float FOV_MOUNT_MID		 = 105.0;
	const float FOV_INTERP_SPEED_IN	 = 50.0;
	const float FOV_INTERP_SPEED_OUT = 10.0;

	float lastFPPFovValue = FOV_DEFAULT;
	float lastTPPFovValue = FOV_DEFAULT;
	float lastColorLerp	  = 0;

	int32 cameraSettingsManualFOVControlID = -1;

	FOverrideGoalFOV overrideGoalFOV;
	FOverrideGoalFOV overrideFPPFOVInterpSpeed;

	void UpdateCameraFov( float DeltaSeconds )
	{
		AAS_PlayerEntity player = this;
		if ( !player.IsLocallyControlled() )
			return;

		UNCPlayerMovementComponent ncMovement = Cast<UNCPlayerMovementComponent>( player.MovementComponent );
		AAS_VehicleMount mount				  = Mount::GetPilotedMount( player );

		bool hasEnragePassive		 = player.GetStatusEffectValue( GameplayTags::StatusEffect_Enraged ) >= 1.0;
		bool isOnMount				 = IsValid( mount );
		bool hasActiveSprint		 = isOnMount && mount.motionModel.HasActiveSprint();
		bool hasStrongMoveSpeedBoost = player.GetStatusEffectValue( GameplayTags::StatusEffect_MoveFast ) > 0.2;
		bool hasBoostMod			 = player.GetBoostManager().HasBoost();
		bool isSprinting			 = isOnMount ? mount.IsSprinting() : player.IsSprinting();
		bool isNotStrafing			 = isOnMount ? true : player.GetInputAxisMove().X > 0.8;
		bool shouldEnableSpeedFX	 = isSprinting && isNotStrafing && ( hasStrongMoveSpeedBoost || hasEnragePassive || ( isOnMount && hasActiveSprint ) );
		float minSprintAlpha		 = hasEnragePassive ? 0.65 : 0.0;
		float sprintAlpha			 = Math::Max( minSprintAlpha, isOnMount && hasActiveSprint ? 1.0 : ncMovement.GetSprintAlpha() );
		float speed2D				 = isOnMount ? mount.motionModel.GetSpeed() : player.GetMovementComponentVelocity().Size2D();

		float goalFPPfov = FOV_DEFAULT;
		float goalTPPfov = TPPSettingsComponent.GetSettingCurrentValue( ETPPCameraSetting::FOV ).FloatValue;
		overrideGoalFOV.Broadcast( goalFPPfov );

		if ( shouldEnableSpeedFX || hasBoostMod || hasActiveSprint || hasStrongMoveSpeedBoost )
		{
			float fovMax   = isOnMount ? ( hasBoostMod && hasActiveSprint ) ? FOV_MOUNT_MAX : FOV_MOUNT_MID : FOV_PLAYER_MAX;
			float fovAlpha = ( isOnMount ) ? hasBoostMod || hasActiveSprint ? 1.0 : 0.0 : sprintAlpha;
			if ( hasStrongMoveSpeedBoost )
				fovAlpha = 1.0 * player.GetStatusEffectValue( GameplayTags::StatusEffect_MoveFast ) / 1.0;
			goalFPPfov = Math::GetMappedRangeValueClamped( FVector2D( 0, 1 ), FVector2D( FOV_DEFAULT, fovMax ), fovAlpha );
		}

		// interpolate faster when goal fov is greater ( speeding up )
		float interpSpeedFPP = goalFPPfov > lastFPPFovValue ? FOV_INTERP_SPEED_IN : FOV_INTERP_SPEED_OUT;
		float interpSpeedTPP = goalTPPfov > lastTPPFovValue ? FOV_INTERP_SPEED_IN : FOV_INTERP_SPEED_OUT;
		overrideFPPFOVInterpSpeed.Broadcast( interpSpeedFPP );

		// overide From ScriptAnimNotify ( scripted anims take precedence )
		bool updateTPPCam = false;
		if ( animNotifyTrackingComponent.HasFovData() )
		{
			FScriptAnimNotifyFovData fovData = animNotifyTrackingComponent.GetLatestFovData();
			updateTPPCam					 = true;

			if ( cameraSettingsManualFOVControlID == -1 )
			{
				// Stop the camera settings manager from updating our FOV, we'll handle it until the
				// anim notify is done overriding it.
				FTPPCameraSettings overrideSettings;
				overrideSettings.SetExternallyControlled( ETPPCameraSetting::FOV, true );
				cameraSettingsManualFOVControlID = TPPSettingsComponent.AddOverrideSettings( n"AnimFOVOverride", overrideSettings );
			}

			// FPP
			if ( fovData.ScriptAnimNotifyFOV_FPPLerpIn )
				goalFPPfov = fovData.ScriptAnimNotifyFOV_FPPValue;

			bool stillLerpingOutFPP = GetGameTimeMS() <= fovData.ScriptAnimNotifyFOV_FPPLerpOutTimeMS;
			if ( fovData.ScriptAnimNotifyFOV_FPPLerpIn || stillLerpingOutFPP )
				interpSpeedFPP = fovData.ScriptAnimNotifyFOV_FPPInterp;

			// TPP
			if ( fovData.ScriptAnimNotifyFOV_TPPLerpIn )
				goalTPPfov = fovData.ScriptAnimNotifyFOV_TPPValue;

			bool stillLerpingOutTPP = GetGameTimeMS() <= fovData.ScriptAnimNotifyFOV_TPPLerpOutTimeMS;
			if ( fovData.ScriptAnimNotifyFOV_TPPLerpIn || stillLerpingOutTPP )
				interpSpeedTPP = fovData.ScriptAnimNotifyFOV_TPPInterp;

			// clear stack?
			bool noLerpIns	= !fovData.ScriptAnimNotifyFOV_TPPLerpIn && !fovData.ScriptAnimNotifyFOV_FPPLerpIn;
			bool noLerpOuts = !stillLerpingOutTPP && !stillLerpingOutFPP;
			if ( noLerpIns && noLerpOuts )
				animNotifyTrackingComponent.ClearFovData();
		}
		else
		{
			if ( cameraSettingsManualFOVControlID != -1 )
			{
				TPPSettingsComponent.ClearOverrideSettings( cameraSettingsManualFOVControlID );
				cameraSettingsManualFOVControlID = -1;
			}
		}

		float newFPPFov = Math::FInterpConstantTo( lastFPPFovValue, goalFPPfov, DeltaSeconds, interpSpeedFPP );
		float newTPPFov = Math::FInterpConstantTo( lastTPPFovValue, goalTPPfov, DeltaSeconds, interpSpeedTPP );

		SetScriptFOVOverride( newFPPFov );
		if ( updateTPPCam )
			GetTPPCameraComponent().TPP_Camera.SetFieldOfView( newTPPFov );

		lastFPPFovValue = newFPPFov;
		lastTPPFovValue = newTPPFov;

		const FLinearColor SPEEDLINE_DEFAULT = FLinearColor::White;
		const FLinearColor SPEEDLINE_ACTIVE	 = FLinearColor( 1.5, 1.5, 2.5 );

		float goalColorLerp	   = hasActiveSprint ? 1 : 0;
		float interpSpeedColor = goalColorLerp > lastColorLerp ? FOV_INTERP_SPEED_IN : FOV_INTERP_SPEED_OUT;
		float newColorLerp	   = Math::FInterpConstantTo( lastColorLerp, goalColorLerp, DeltaSeconds, interpSpeedColor );
		lastColorLerp		   = newColorLerp;
		FLinearColor newColor  = Math::Lerp( SPEEDLINE_DEFAULT, SPEEDLINE_ACTIVE, newColorLerp );

		if ( sprintAlpha > 0 && shouldEnableSpeedFX )
		{
			if ( !speedlinesFX.IsVisible() )
			{
				speedlinesFX.SetVisibility( true );
				speedlinesFX.Activate();
			}

			const float effectAlphaMin = 0;
			const float effectAlphaMax = hasBoostMod ? 1.0 : 0.25;
			float effectAlpha		   = Math::GetMappedRangeValueClamped( FVector2D( 0, 1 ), FVector2D( effectAlphaMin, effectAlphaMax ), sprintAlpha );

			newColor.A = effectAlpha;
			speedlinesFX.SetNiagaraVariableLinearColor( "Color", newColor );

			// speedlines draw mostly behind you at high speeds... this brings them out in front
			float xVal = Math::GetMappedRangeValueClamped( FVector2D( 600, 1200 ), FVector2D( 0, 200 ), speed2D );
			speedlinesFX.SetRelativeLocation( FVector( xVal, 0, 0 ) );
		}
		else if ( speedlinesFX.IsVisible() )
		{
			speedlinesFX.SetVisibility( false );
			speedlinesFX.Deactivate();
		}

		// Print( f"sprintAlpha: {sprintAlpha} | FOV: {newFov}", 0.045 );
	}

	void Delayed_ClearInvulnerable( float32 delay )
	{
		Thread( this, n"__Delayed_ClearInvulnerableThread", delay );
	}

	UFUNCTION()
	private void __Delayed_ClearInvulnerableThread( UNCCoroutine co, float32 delay )
	{
		co.Wait( delay );
		SetInvulnerable( false );
	}

	TArray<UMaterialInstanceDynamic> arms_dynamicMats;
	TArray<UMaterialInstanceDynamic> mesh_dynamicMats;

	UFUNCTION()
	void CreateDynamicMaterialInstances()
	{
		arms_dynamicMats.Empty();
		mesh_dynamicMats.Empty();

		USkeletalMeshComponent arms = GetFirstPersonArmsComponent();
		USkeletalMeshComponent mesh = GetPlayerMesh3P();

		for ( int i = 0; i < arms.GetMaterials().Num(); i++ )
		{
			UMaterialInstanceDynamic dynM = arms.CreateDynamicMaterialInstance( i );
			arms_dynamicMats.Add( dynM );
		}

		for ( int i = 0; i < mesh.GetMaterials().Num(); i++ )
		{
			UMaterialInstanceDynamic dynM = mesh.CreateDynamicMaterialInstance( i );
			mesh_dynamicMats.Add( dynM );
		}
	}

	const float ULTIMATE_READY_NAG_TIME = 120.0; // 2 minutes

	UFUNCTION()
	private void OnUltimateCooldownChanged( AAS_PlayerEntity player, ANCWeapon weapon, int startTime, int endTime )
	{
		if ( !ultCooldownComponent.IsCooldownActive() )
		{
			PlayBattleChatter( GameplayTags::Audio_VO_BattleChatter_Ultimate_Ready );
			ultimateReadyNagTimer = System::SetTimer( this, n"UltimateCooldownReadyNag", ULTIMATE_READY_NAG_TIME, true );
		}
		else if ( System::IsTimerActiveHandle( ultimateReadyNagTimer ) )
		{
			System::ClearAndInvalidateTimerHandle( ultimateReadyNagTimer );
		}
	}

	UFUNCTION()
	private void UltimateCooldownReadyNag()
	{
		if ( !ultCooldownComponent.IsCooldownActive() )
			PlayBattleChatter( GameplayTags::Audio_VO_BattleChatter_Ultimate_Ready_Nag );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnMapMarkerVisibilityStatusEffectAddedOrRemoved( ANCPlayerCharacter player )
	{
		// We also want to make sure we refresh map markers when the highlight is refreshed since the two systems are linked
		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			scriptCallbacks.client_OnRefreshAllMapMarkers.Broadcast();
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private bool OnEnemyMarkerVisibilityRefresh( UAS_MapMarkerComponent mapMarker )
	{
		bool result = false;

		// Only non-local players use the custom visibility
		if ( this != Client_GetLocalASPawn() )
		{
			// Keep this visibility in sync with the highlight manager
			int teamId		   = GetTeam();
			bool isHighlighted = highlightManager.GetIsHighlightedForTeam( GetOtherTeam( teamId ) );

			bool hasStatusEffect = false;
			for ( FGameplayTag tag : mapMarkerVisibilityStatusEffects.GameplayTags )
			{
				// Check if any of the status effects are triggered and break at the first one
				if ( GetStatusEffectValue( tag ) > 0 )
				{
					hasStatusEffect = true;
					break;
				}
			}

			result = isHighlighted || hasStatusEffect;
		}

		return result;
	}

	FVoicePackage loadedVoicePack;
	bool voiceLoaded;

	UFUNCTION( BlueprintOverride )
	void OnPlayerSettingsAssetChanged( UPlayerSettingsAsset newPlayerSettings )
	{
		voiceLoaded		= true;
		loadedVoicePack = DialogueUtil::ProcessVoicePackage( newPlayerSettings.CharacterVoiceDataTableAsset );

		ScriptCallbacks().client_onPlayerSettingsChanged.Broadcast( this, newPlayerSettings );
	}

	bool IsVoicePackageLoaded()
	{
		return voiceLoaded;
	}

	const FVoicePackage& GetVoicePackage()
	{
		return loadedVoicePack;
	}

	UFUNCTION()
	void OnLocalPlayerClassChanged( FGameplayTag oldClass, FGameplayTag newClass )
	{
		if ( !IsLocallyControlled() )
		{
			return;
		}

		// Reset class interactions, and wall repair (affected by Jade)
		const USh_WallInteractionSystem wallInteractionSys	 = WallInteractionSystem();
		UAS_WallInteractionListComponent interactionListComp = WallInteractionList();
		interactionListComp.RemovePossibleWallInteraction( wallInteractionSys.GetInteractionForTag( GameplayTags::WallInteractions_JadeRepair ) );

		interactionListComp.RemovePossibleWallInteraction( wallInteractionSys.GetInteractionForTag( GameplayTags::WallInteractions_WallShift ) );
		interactionListComp.RemovePossibleWallInteraction( wallInteractionSys.GetInteractionForTag( GameplayTags::WallInteractions_WallShift_Hint ) );

		interactionListComp.AddPossibleWallInteraction( wallInteractionSys.GetInteractionForTag( GameplayTags::WallInteractions_WallRepair ) );
		interactionListComp.AddPossibleWallInteraction( wallInteractionSys.GetInteractionForTag( GameplayTags::WallInteractions_WallRepair_Hint ) );

		if ( newClass == GameplayTags::Classes_Class_Jade )
		{
			// Jade's interaction replaces wall repair
			interactionListComp.AddPossibleWallInteraction( wallInteractionSys.GetInteractionForTag( GameplayTags::WallInteractions_JadeRepair ) );

			interactionListComp.RemovePossibleWallInteraction( wallInteractionSys.GetInteractionForTag( GameplayTags::WallInteractions_WallRepair ) );
			interactionListComp.RemovePossibleWallInteraction( wallInteractionSys.GetInteractionForTag( GameplayTags::WallInteractions_WallRepair_Hint ) );
		}
		else if ( newClass == GameplayTags::Classes_Class_Skye )
		{
			interactionListComp.AddPossibleWallInteraction( wallInteractionSys.GetInteractionForTag( GameplayTags::WallInteractions_WallShift ) );
			interactionListComp.AddPossibleWallInteraction( wallInteractionSys.GetInteractionForTag( GameplayTags::WallInteractions_WallShift_Hint ) );
		}
	}

	UFUNCTION( BlueprintOverride )
	bool IsTPPCameraEnabled()
	{
		return TPPCameraComponent.IsTPPEnabled();
	}

	UFUNCTION( BlueprintOverride )
	bool ShouldPlayFlinchAnim3P()
	{
		return GetShieldHealth() == 0;
	}

	UFUNCTION()
	private void Server_OnPlayerAssist( ANCPlayerCharacter assistAttacker, ANCPlayerCharacter victim,
								const FDamageInfo&in damageInfo )
	{
		if ( assistAttacker != this )
			return;

		Server_SendPlayerAssistMessage( this, victim.GetPlayerIndex() );
		Print( f"I {this} assisted killing {victim}", 15.f );
	}

	UFUNCTION( BlueprintOverride )
	bool IsPlayingEmote() const
	{
		return emoteManager.net_emoteIndex != -1;
	}
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// End UCLASS
void Cl_InitPlayerEntity( ANC_HUD hud )
{
	UNCRemoteScriptCommands::RegisterServerCommandGlobal( hud, n"ServerCommand_OnCooldownReady", n"SC_OnCooldownReady" );
}

void InitPlayerEntityCallbacks( UNCBaseServerScript serverScript )
{
}

class UNCEquipGrenadeEvent : UNCNetClientToServerEvent
{
	UPROPERTY()
	FNCNetGameplayTag net_grenadeLootTag;

	UFUNCTION( BlueprintOverride )
	void OnEventReceived( ANCNetPlayerController receiver )
	{
		if ( !IsLootIndexValid( net_grenadeLootTag ) )
			return;

		const FLootDataStruct& nextGrenadeData = GetLootDataByIndex( net_grenadeLootTag );
		Print( f"    Loot type is grenade? {nextGrenadeData.lootType == ELootType::Grenade}" );
		if ( nextGrenadeData.lootType != ELootType::Grenade )
			return;

		AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( receiver.GetControlledPawn() );

		int count = 0;
		EWeaponSlot slot;
		if ( IsCooldownBased( nextGrenadeData.index ) )
		{
			count = player.fragCooldownComponent.IsCooldownActive() ? 0 : 1;
			slot  = EWeaponSlot::GrenadeSlot;
		}
		else
		{
			count = player.CountItemsInAllBackpacks( nextGrenadeData.index );
			slot  = EWeaponSlot::CustomBackpackGrenadeSlot;
		}

		if ( count <= 0 )
			return;

		if ( slot != EWeaponSlot::GrenadeSlot )
		{
			ANCWeapon offhandWeapon			 = player.GetWeaponAtSlot( slot );
			UWeaponPrimaryAsset grenadeClass = GetWeaponClassFromScriptDataIndex( nextGrenadeData.referencedItemIndex );
			FName nextGrenadeClassName		 = grenadeClass.GetPrimaryAssetIdName();
			if ( !IsValid( offhandWeapon ) || offhandWeapon.GetWeaponClass() != nextGrenadeClassName )
			{
				FGiveWeaponSettings giveWeaponSettings;
				giveWeaponSettings.ForceEquip = true;
				ANCWeapon newGrenadeWeapon	  = player.GiveWeaponAtSlot( nextGrenadeClassName, WeaponSlot::CustomBackpackGrenadeSlot, giveWeaponSettings );
				newGrenadeWeapon.SetClipAmmo( count );
				return;
			}
		}

		player.TrySwapToWeaponAtSlot( slot );
	}
}

UCLASS()
class UNCUseUltimateAccelEvent : UNCNetClientToServerEvent
{
	UPROPERTY()
	FNCNetGameplayTag net_lootTag;

	UFUNCTION( BlueprintOverride )
	void OnEventReceived( ANCNetPlayerController receiver )
	{
		if ( !IsLootIndexValid( net_lootTag ) )
			return;

		AAS_PlayerEntity player			= Cast<AAS_PlayerEntity>( receiver.GetControlledPawn() );
		const FLootDataStruct& lootData = GetLootDataByIndex( net_lootTag );
		player.TryUseUltimateAccelFromBackpack( lootData );
	}
}

UFUNCTION( NotBlueprintCallable )
void SC_OnCooldownReady( TArray<FString> args )
{
	if ( args.Num() != 2 )
		return;

	int playerEntIndex = args[0].ToInt();
	ScriptAssert( IsValidEntityIndex( playerEntIndex ), "SC_OnCooldownReady: Entity index is invalid" );

	int cooldownIndex = args[1].ToInt();

	AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( GetEntity( playerEntIndex ) );
	if ( IsValid( player ) )
		player.DisplayCooldownReadyMessage( GetGameplayTagForInt( cooldownIndex ) );
}

UFUNCTION( NotBlueprintCallable )
UPlayerSettingsAsset GetPlayerSettingAssetForClass( AAS_PlayerEntity player )
{
	return player.ClassManager().GetClassData().characterSettingsAsset;
}

void Cl_InitTrackWhoKilledMeCommand( ANC_HUD hud )
{
	UNCRemoteScriptCommands::RegisterServerCommandGlobal( hud, n"TrackWhoKilledMe", n"SC_TrackWhoKilledMe" );
}

/*
	Routes information about the person that killed you at time of death
	0 - (float) health percent
	1 - (int) shield level
	2 - (string) shield rarity
	3 - (float) shield percent
	4 - (float) harden percent
*/
UFUNCTION( NotBlueprintCallable )
void SC_TrackWhoKilledMe( TArray<FString> args )
{
	if ( args.Num() != 5 )
		return;

	AAS_PlayerEntity player = Client_GetLocalASPawn();
	if ( IsValid( player ) )
	{
		player.TrackWhoKilledMe( args[0].ToFloat(), args[1].ToInt(), args[2], args[3].ToFloat(), args[4].ToFloat() );
	}
}

UCLASS()
class UAS_UltAccelerantAnimThread : UAS_Thread
{
	AAS_PlayerEntity player;
	FLootDataStruct lootData;
	int numToUse;

	FNCCoroutineSignal signalAnimEnd;
	const FName TAG_INTERRUPT = n"Interrupted";

	void Init( FLootDataStruct _lootData, int _numToUse )
	{
		ScriptAssert( IsServer(), "SERVER ONLY" );

		player	 = Cast<AAS_PlayerEntity>( GetOuter() );
		lootData = _lootData;
		numToUse = _numToUse;

		if ( lootData.lootType != ELootType::UltimateAccelerant )
			return;

		int count = player.CountItemsInAllBackpacks( lootData.index );
		if ( count <= 0 )
			return;

		// Don't use item when Ult is ready (full charge)
		if ( !player.ultCooldownComponent.IsCooldownActive() )
			return;

		if ( player.Passives().HasPassive( GameplayTags::Classes_Passives_BusyScriptedAnim ) )
			return;

		// if no count is specified use up all accelerants to get us to 100%
		if ( numToUse == -1 )
			numToUse = GetNumAccelToUse( count );

		if ( numToUse == 0 )
			return;

		Start();
	}

	int GetNumAccelToUse( int count )
	{
		float32 timeReduction = GameModeDefaults().GamemodeRules_UltimateAccelTimeReduction;
		float32 timeLeft	  = player.ultCooldownComponent.GetCooldownTimeLeft();
		int numAccelToFull	  = Math::CeilToInt( timeLeft / timeReduction );
		int numAccelToUse	  = Math::Min( count, numAccelToFull );

		return numAccelToUse;
	}

	void OnThreadStart( UNCCoroutine co ) override
	{
		Super::OnThreadStart( co );

		bool shouldOneHand = player.IsPlayerRidingMount() || player.IsZiplining();
		UAnimMontage anim  = shouldOneHand ? Abilities().ultimateAccelerantAnimOneHanded : Abilities().ultimateAccelerantAnim;

		FScriptedAnimPlayerSettings settings;
		settings.SetScripted1PDefault( player );
		settings.crouchEnabled = true;
		settings.jumpEnabled   = true;
		settings.allowMounted  = true;

		FScriptedMontage1PResult result = player.PlayNetScriptedMontage1PWithPlayerSettings( settings, anim );

		Anim().animNotifyCallbacks.GetOrMakeDelegate( result.animatedMesh1P, anim, n"PopStone" ).AddUFunction( this, n"OnUltAccelAnimPop" );
		Anim().animNotifyCallbacks.OnAnimEnd( result.animatedMesh1P, anim ).AddUFunction( this, n"OnUltAccelAnimPop" );

		co.EndOn( player, player.OnDeathSignal, TAG_INTERRUPT );
		co.EndOnDestroyed( player, TAG_INTERRUPT );

		co.Wait( this, signalAnimEnd );
	}

	void OnThreadEnd( FNCCoroutineEndParams params ) override
	{
		Super::OnThreadEnd( params );

		if ( params.EndTag == TAG_INTERRUPT )
			return;

		if ( !player.IsAlive() )
			return;

		int count = player.CountItemsInAllBackpacks( lootData.index );
		if ( count <= 0 )
			return;

		// if we don't need to use any because we hit 100% by the time the anim finished... still use one because it will feel like a bug otherwise
		numToUse = Math::Max( GetNumAccelToUse( count ), 1 );
		player.RemoveFromAnyBackpack( MakeBackpackItem( lootData.index, numToUse, -1, 0 ) );

		// Don't reduce anything if our cooldown is done
		if ( !player.ultCooldownComponent.IsCooldownActive() )
			return;

		float32 timeReduction	   = GameModeDefaults().GamemodeRules_UltimateAccelTimeReduction;
		float32 totalTimeReduction = timeReduction * numToUse;
		player.ultCooldownComponent.ReduceCooldown( totalTimeReduction );

		Server_EmitSoundAtLocation( lootData.onPlacedSound, player.GetActorLocation() );
	}

	UFUNCTION()
	private void OnUltAccelAnimPop( USkeletalMeshComponent meshComp, UAnimSequenceBase animation,
							const UAS_AnimNotifyTrackerBase notifyTracker )
	{
		signalAnimEnd.Emit();
	}
}
