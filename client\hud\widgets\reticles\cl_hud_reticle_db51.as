// ( 0, -0.6, 0, 0 ) <---- ironsights height offset for db51

UCLASS(Abstract)
class UAS_Reticle_DB51 : UAS_ReticleWidget
{
	float deployAnimTime = 0.3;
	float deployAnimPlaybackSpeed = 0;
	
	UPROPERTY( Transient, meta = ( BindWidgetAnim ) )
	UWidgetAnimation anim_supercharge_stabilizer_deploy;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset chargeSound_stage1;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset chargeSound_stage2;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset chargeSound_stage3;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset chargeSound_failed;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset chargeSound_complete;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset chargeSound_chargedIdle;

	UPROPERTY( EditDefaultsOnly )
	FScopeMaterialParameters superchargeDisabledParams;

	UPROPERTY( EditDefaultsOnly )
	FScopeMaterialParameters superchargeEnabledParams;

	ANCPlayerCharacter ownerPawn;
	
	UFUNCTION(BlueprintOverride)
	void Construct()
	{
		Super::Construct();
		
		ScriptCallbacks().RegisterSignalCallback( Signals::WEAP_SUPERCHARGE_CHARGE_INTERRUPTED, this, n"Signal_SuperchargeInterrupted" );
	}
	
	void InitializeReticle(ANCWeapon weapon) override
	{
		Super::InitializeReticle(weapon);

		if ( !IsValid( weapon ) )
			return;

		ownerPawn = ownerWeapon.WeaponOwner;

		deployAnimPlaybackSpeed = 1 / deployAnimTime;

		//RegisterReticleAnimation( n"DelayedStabilizer", anim_stabilizerDeploy, EUMGSequencePlayMode::Forward, EUMGSequencePlayMode::Reverse, deployAnimPlaybackSpeed );

		FReticleAnimationData superchargeActiveAnimData = FReticleAnimationData( anim_supercharge_stabilizer_deploy, deployAnimPlaybackSpeed );
		superchargeActiveAnimData.animSound_enable = chargeSound_complete;
		superchargeActiveAnimData.animSound_enabledIdle = chargeSound_chargedIdle;

		RegisterReticleAnimation( WeaponConst::Supercharge::RETICLE_ANIM_KEY, superchargeActiveAnimData );
		PlayReticleAnimationInstant( WeaponConst::Supercharge::RETICLE_ANIM_KEY, IsModActiveOnWeapon( weapon, WeaponConst::Supercharge::MODNAME_STABILIZED ) );

		UAS_Thread_ScopeMaterialAnimation_Supercharge db51SuperchargeAnimThread_Enabled = Cast<UAS_Thread_ScopeMaterialAnimation_Supercharge>( CreateThread( UAS_Thread_ScopeMaterialAnimation_Supercharge::StaticClass(), this ) );
		db51SuperchargeAnimThread_Enabled.chargeSound_stage1 = chargeSound_stage1;
		db51SuperchargeAnimThread_Enabled.chargeSound_stage2 = chargeSound_stage2;
		db51SuperchargeAnimThread_Enabled.chargeSound_stage3 = chargeSound_stage3;
		db51SuperchargeAnimThread_Enabled.chargeSound_failed = chargeSound_failed;
		db51SuperchargeAnimThread_Enabled.aimpointSettings = superchargeEnabledParams;
		
		RegisterScopeMaterialAnimation( WeaponConst::Supercharge::SIGHT_MATERIAL_ANIM_KEY, superchargeEnabledParams, superchargeDisabledParams, db51SuperchargeAnimThread_Enabled, nullptr );
		PlayScopeMaterialAnimation( WeaponConst::Supercharge::SIGHT_MATERIAL_ANIM_KEY, EScopeMaterialAnimationPlayMode::SET_DISABLED_STATE );
	}

	void UpdateStateAnimations() override
	{
		Super::UpdateStateAnimations();
		PlayReticleAnimation( WeaponConst::Supercharge::RETICLE_ANIM_KEY, IsModActiveOnWeapon( ownerWeapon, WeaponConst::Supercharge::MODNAME_STABILIZED ) );
		UpdateSuperchargeState( this, ownerWeapon, ownerPawn );
	}

	void OnLocalPlayerADSChanged(bool isADS) override
	{
		Super::OnLocalPlayerADSChanged(isADS);

		UpdateSuperchargeState( this, ownerWeapon, ownerPawn );
		PlayReticleAnimation( WeaponConst::Supercharge::RETICLE_ANIM_KEY, IsModActiveOnWeapon( ownerWeapon, WeaponConst::Supercharge::MODNAME_STABILIZED ) );
	}

	UFUNCTION()
	private void Signal_SuperchargeInterrupted( FName signalName, UObject signalSource )
	{
		PlayScopeMaterialAnimation( WeaponConst::Supercharge::SIGHT_MATERIAL_ANIM_KEY, EScopeMaterialAnimationPlayMode::SET_DISABLED_STATE );
		UpdateSuperchargeState( this, ownerWeapon, ownerPawn );
	}

	UFUNCTION(BlueprintOverride)
	void Destruct()
	{
		Super::Destruct();
		
		ScriptCallbacks().UnRegisterSignalCallback( Signals::WEAP_SUPERCHARGE_CHARGE_INTERRUPTED, this, n"Signal_SuperchargeInterrupted" );
	}
}