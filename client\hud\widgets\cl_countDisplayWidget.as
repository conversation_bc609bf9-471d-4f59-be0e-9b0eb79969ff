UCLASS( Abstract )
class UAS_CountDisplayWidget : UUserWidgetDefault
{
	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	UListView list;

	UPROPERTY( EditAnywhere )
	int maxCount;

	UPROPERTY( EditAnywhere )
	int count;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		UpdateList();
	}

	void Init( int inCount, int inMaxCount )
	{
		maxCount = inMaxCount;
		count	 = inCount;
		UpdateList();
	}

	void SetCount( int inCount, bool updateList = true )
	{
		ScriptAssert( inCount <= maxCount, f"inCount ({inCount}) cannot exceed maxCount ({maxCount})" );
		count = inCount;

		if ( updateList )
			UpdateList();
	}

	void SetMaxCount( int inMaxCount, bool updateList = true )
	{
		ScriptAssert( count <= inMaxCount, f"count ({count}) cannot exceed maxCount ({inMaxCount})" );
		maxCount = inMaxCount;

		if ( updateList )
			UpdateList();
	}

	void UpdateList()
	{
		TArray<UAS_CountDisplayWidgetObject> objects;
		for ( int i = 0; i < maxCount; i++ )
		{
			UAS_CountDisplayWidgetObject obj = Cast<UAS_CountDisplayWidgetObject>( NewObject( this, UAS_CountDisplayWidgetObject::StaticClass() ) );
			obj.selected					 = ( i < count );
			objects.Add( obj );
		}

		list.SetListItems( objects );
	}
}

UCLASS( Abstract )
class UAS_CountDisplayListEntry : UNCGenericListEntry
{
	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	UWidgetSwitcher widgetSwitcher;

	UFUNCTION( BlueprintOverride )
	void OnDataSet( UObject DataObject )
	{
		UAS_CountDisplayWidgetObject obj = Cast<UAS_CountDisplayWidgetObject>( DataObject );
		widgetSwitcher.SetActiveWidgetIndex( obj.selected ? 1 : 0 );
	}
}

UCLASS()
class UAS_CountDisplayWidgetObject : UObject
{
	bool selected;
}