namespace PriorityMessageRaidTemplates
{
	bool GetTemplateMessageData( EPriorityMessageTemplate template, UCL_PriorityMessageManager_v2 manager, UAS_PriorityMessageData& data )
	{
		bool result = false;

		AAS_GameModeBase_Raidmode_TwoTeams raidMode = TwoTeamModeDefaults();
		if ( IsValid( data ) && IsValid( manager ) && IsValid( raidMode ) )
		{
			// Start assuming that we will find the template
			result = true;

			// Many raid messages rely on the objective data, get that before doing anything
			FRaidBaseObjectiveData objectiveData;
			GetPlayerFacingDataOfCurrentBombPlant( objectiveData );

			switch ( template )
			{
				case EPriorityMessageTemplate::V2_DEFEND_DOME_REPAIR:
					data.theme				 = EPriorityMessageTheme::FRIENDLY_ACTION;
					data.priorityLevel		 = EPriorityMessageLevel::DEFENDER;
					data.header				 = GetLocalizedText( Localization::Raid, f"raid_dome_repairing" );
					data.subheader			 = GetLocalizedText( Localization::Raid, f"raid_enemy_breaker_down" );
					data.autoDissappearDelay = 6.0;
					data.onAppearSound		 = manager.centerDefend;
					// data.onCreate.AddUFunction( manager, n"OnCreate_DomeRepair_Countdown" );
					break;

				case EPriorityMessageTemplate::V2_ATTACK_DOME_REPAIR:
					data.theme		   = EPriorityMessageTheme::ENEMY_ACTION;
					data.priorityLevel = EPriorityMessageLevel::ATTACKER;
					data.header		   = GetLocalizedText( Localization::Raid, f"raid_dome_closing" );
					data.subheader	   = GetLocalizedText( Localization::Raid, f"raid_no_more_respawns" );
					// data.cta				 = GetLocalizedText( Localization::Raid, f"raid_get_inside_the_dome" );
					// data.showCTA			 = false;
					data.autoDissappearDelay = 6.0;
					data.onAppearSound		 = manager.centerAttack;
					// data.onCreate.AddUFunction( manager, n"OnCreate_DomeRepair_Countdown" );
					break;

				case EPriorityMessageTemplate::V2_ATTACK_GENERATOR_DESTROYED:
					data.theme		   = EPriorityMessageTheme::FRIENDLY_ACTION;
					data.priorityLevel = EPriorityMessageLevel::ATTACKER;
					data.onAppearSound = manager.centerAttackerWin;
					break;

				case EPriorityMessageTemplate::V2_DEFEND_GENERATOR_LOST:
					data.theme		   = EPriorityMessageTheme::ENEMY_ACTION;
					data.priorityLevel = EPriorityMessageLevel::DEFENDER;
					data.onAppearSound = manager.centerDefenderLose;
					break;

				case EPriorityMessageTemplate::V2_ATTACK_RAID_STARTED:
					data.theme				 = EPriorityMessageTheme::FRIENDLY_ACTION;
					data.priorityLevel		 = EPriorityMessageLevel::ATTACKER;
					data.header				 = GetLocalizedText( Localization::Raid, f"raid_starting" );
					data.subheader			 = GetLocalizedText( Localization::Raid, f"raid_enemy_base_damage_done", FFormatArgumentValue( raidMode.POINTS_SB_PLANT ) );
					data.onAppearSound		 = manager.centerAttack;
					data.autoDissappearDelay = 6.0f;
					break;

				case EPriorityMessageTemplate::V2_DEFEND_RAID_STARTED:
					data.theme				 = EPriorityMessageTheme::ENEMY_ACTION;
					data.priorityLevel		 = EPriorityMessageLevel::DEFENDER;
					data.header				 = GetLocalizedText( Localization::Raid, f"raid_starting" );
					data.subheader			 = GetLocalizedText( Localization::Raid, f"raid_base_damage_done", FFormatArgumentValue( raidMode.POINTS_SB_PLANT ) );
					data.onAppearSound		 = manager.centerDefend;
					data.announcerAlias		 = GameplayTags::Audio_VO_GameUpdates_TeamObjective_Raid_BeingRaided;
					data.autoDissappearDelay = 6.0f;
					// data.onCreate.AddUFunction( manager, n"OnCreate_UpdateTeleportCTA" );
					break;

				case EPriorityMessageTemplate::V2_ATTACK_DOME_BREACHED:
					data.theme		   = EPriorityMessageTheme::FRIENDLY_ACTION;
					data.priorityLevel = EPriorityMessageLevel::ATTACKER;
					data.header		   = GetLocalizedText( Localization::Raid, f"raid_raid_started_attacker" );
					data.subheader	   = Text::EmptyText;
					data.onAppearSound = manager.centerAttack;
					break;

				case EPriorityMessageTemplate::V2_DEFEND_DOME_BREACHED:
					data.theme		   = EPriorityMessageTheme::ENEMY_ACTION;
					data.priorityLevel = EPriorityMessageLevel::DEFENDER;
					data.header		   = GetLocalizedText( Localization::Raid, f"raid_raid_started_defender" );
					data.subheader	   = Text::EmptyText;
					data.onAppearSound = manager.centerDefend;
					// data.onCreate.AddUFunction( manager, n"OnCreate_UpdateTeleportCTA" );
					break;

				default:
					return false;
			}

			// All raid messages share some of the same traits
			data.widgetClass	= manager.priorityMessageRaidWidgetClass;
			data.placement		= EPriorityMessagePlacement::CENTER;
			data.autoDissappear = true;
		}

		return result;
	}
}