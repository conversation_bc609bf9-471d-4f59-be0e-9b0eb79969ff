UCLASS(Abstract)
class UAS_Reticle_Dynasty : UAS_ReticleWidget
{
	bool isEngaged = false;
	float deployAnimTime = 0.01;
	float deployAnimPlaybackSpeed = 0;
	
	UPROPERTY( Transient, meta = ( BindWidgetAnim ) )
	UWidgetAnimation anim_hotshotDeploy;

	UPROPERTY( Transient, meta = ( BindWidgetAnim ) )
	UWidgetAnimation anim_basebreaker_deploy;
	
	void InitializeReticle(ANCWeapon weapon) override
	{
		Super::InitializeReticle(weapon);

		if ( !IsValid( weapon ) )
			return;

		deployAnimPlaybackSpeed = 1 / deployAnimTime;

		RegisterReticleAnimation( WeaponConst::BaseBreaker::RETICLE_ANIM_KEY, anim_basebreaker_deploy, EUMGSequencePlayMode::Forward, EUMGSequencePlayMode::Reverse, deployAnimPlaybackSpeed );
		PlayReticleAnimation( WeaponConst::BaseBreaker::RETICLE_ANIM_KEY, lookingAtBasebreakerTarget );
	}

	void UpdateStateAnimations() override
	{
		Super::UpdateStateAnimations();
		PlayReticleAnimation( WeaponConst::BaseBreaker::RETICLE_ANIM_KEY, lookingAtBasebreakerTarget );
	}

	void OnAimTargetStateUpdated() override
	{
		Super::OnAimTargetStateUpdated();
		PlayReticleAnimation( WeaponConst::BaseBreaker::RETICLE_ANIM_KEY, lookingAtBasebreakerTarget );
	}
}