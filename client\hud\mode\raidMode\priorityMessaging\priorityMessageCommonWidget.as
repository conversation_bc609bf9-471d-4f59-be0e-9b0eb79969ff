UCLASS( Abstract )
class UAS_PriorityMessageWidget : UAS_PriorityMessageBaseWidget
{
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidgetOptional ) )
	private UImage Icon;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidgetOptional ) )
	private UImage IconBG;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidgetOptional ) )
	private UTextBlock Description;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidgetOptional ) )
	private UTextBlock Direction;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidgetOptional ) )
	private UTextBlock Countdown;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidgetOptional ) )
	private URichTextBlock CTA_Message;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidgetOptional ) )
	private UWidget CTA_MessageOverlay;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidgetOptional ) )
	protected UImage Highlight_BG_OffScreen;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidgetOptional ) )
	protected UImage Highlight_BG_Center;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnimOptional ), Transient )
	private UWidgetAnimation CTA_AppearAnim;
	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnimOptional ), Transient )
	private UWidgetAnimation CTA_DisappearAnim;
	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnimOptional ), Transient )
	private UWidgetAnimation DePrioritizeAnim;
	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnimOptional ), Transient )
	private UWidgetAnimation RePrioritizeAnim;
	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnimOptional ), Transient )
	private UWidgetAnimation ShowCountdownAnim;
	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnimOptional ), Transient )
	private UWidgetAnimation HideCountdownAnim;

	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidgetOptional ) )
	private UImage BG_Center;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidgetOptional ) )
	private UImage BG_OffScreen;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidgetOptional ) )
	private UImage BG_fade;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidgetOptional ) )
	private UImage BG_OffScreen_CTA;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidgetOptional ) )
	private UImage BG_Center_CTA;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidgetOptional ) )
	private UImage BG_fade_CTA;

	private FLinearColor _BG_ColorCTAMessage = PriorityMessageColor::OTHER_COLOR_CTA_MSG;

	// lerping colors
	private FLinearColor OgColor_Main;
	private FLinearColor GoalColor_Main;
	private FLinearColor StartColor_Main;
	private FLinearColor OgColor_CTA;
	private FLinearColor GoalColor_CTA;
	private FLinearColor StartColor_CTA;
	private int LerpColorStartMS;
	private float LerpColorDuration = 0.25;
	private FTimerHandle LerpColorHandle;

	// misc
	private bool bCTAVisible			= false;
	UNCAudioAsset OnAppearSound = nullptr;

	UAS_PriorityMessageWingWidget AddedWing;

	private FLinearColor _BG_ColorMainMessage = PriorityMessageColor::OTHER_COLOR_MAIN_MSG;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		Set_BGColorCTAMessage( _BG_ColorCTAMessage );
		Set_BGColorIcon( PriorityMessageColor::RIGHT_COLOR_ICON_BG );
		Set_ColorIcon( PriorityMessageColor::RIGHT_COLOR_ICON );
	}

	// this is called from inside the BP anim
	UFUNCTION( BlueprintCallable )
	void PlayOnAppearSound()
	{
		if ( IsValid( OnAppearSound ) && PriorityMessage().CanPlayAppearSound( this ) )
		{
			Client_EmitSoundUI( OnAppearSound );
			PriorityMessage().SetLastSoundPlayTime( this );
		}
	}

	UFUNCTION( BlueprintOverride )
	void OnHideStart()
	{
		Super::OnHideStart();

		if ( IsValid( AddedWing ) )
			AddedWing.Hide();

		if ( messageData.clearEdgeOnDisappear )
		{
			ScriptAssert( messageData.placement == EPriorityMessagePlacement::CENTER, "ClearEdgeOnDisappear should only be used with Data.Placement == EPriorityMessagePlacement::CENTER, because it's meant to clear the edge on a center message. Edge messages auto clear themselves" );
			PriorityMessage().ClearActiveEdgeMessages( messageData.priorityLevel );
		}
	}

	void Initialize( UAS_PriorityMessageData data ) override
	{
		Super::Initialize( data );

		FPriorityMessageColorTheme ThemeColors = GetPriorityMessageThemeColors( data.theme );
		Set_BGColorIcon( ThemeColors.IconBG );
		Set_ColorIcon( ThemeColors.IconImage );

		Set_BGColorMainMessage( ThemeColors.MainBG );
		OgColor_Main = Get_BGColorMainMessage();

		Set_BGColorCTAMessage( ThemeColors.CTA_BG );
		OgColor_CTA = Get_BGColorCTAMessage();

		SetLargeText( data.header );
		SetSmallText( data.subheader );
		SetCTA( data.cta );

		if ( IsValid( Icon ) )
			Icon.SetBrushFromTexture( data.iconImage );

		bCTAVisible = data.showCTA;
		if ( IsValid( CTA_MessageOverlay ) )
		{
			if ( bCTAVisible )
				SetWidgetVisibilitySafe( CTA_MessageOverlay, ESlateVisibility::HitTestInvisible );
			else
				SetWidgetVisibilitySafe( CTA_MessageOverlay, ESlateVisibility::Collapsed );
		}

		OnAppearSound = __ChooseOnAppearSound( data );

		if ( data.announcerAlias != GameplayTags::Audio )
			Dialogue().PlayAnnouncerDialogue( data.announcerAlias );
	}

	void Set_BGColorIcon( FLinearColor newColor )
	{
		if ( IsValid( IconBG ) )
			IconBG.SetColorAndOpacity( newColor );
	}

	void Set_ColorIcon( FLinearColor newColor )
	{
		if ( IsValid( Icon ) )
			Icon.SetColorAndOpacity( newColor );
	}

	void Set_BGColorMainMessage( FLinearColor newColor )
	{
		_BG_ColorMainMessage = newColor;

		if ( IsValid( BG_Center ) )
			BG_Center.SetColorAndOpacity( newColor );
		if ( IsValid( BG_OffScreen ) )
			BG_OffScreen.SetColorAndOpacity( newColor );
		if ( IsValid( BG_fade ) )
			BG_fade.SetColorAndOpacity( newColor );
	}

	void Set_BGColorCTAMessage( FLinearColor newColor )
	{
		_BG_ColorCTAMessage = newColor;

		if ( IsValid( Highlight_BG_OffScreen ) )
			Highlight_BG_OffScreen.SetColorAndOpacity( newColor );
		if ( IsValid( Highlight_BG_Center ) )
			Highlight_BG_Center.SetColorAndOpacity( newColor );
		if ( IsValid( BG_OffScreen_CTA ) )
			BG_OffScreen_CTA.SetColorAndOpacity( newColor );
		if ( IsValid( BG_Center_CTA ) )
			BG_Center_CTA.SetColorAndOpacity( newColor );
		if ( IsValid( BG_fade_CTA ) )
			BG_fade_CTA.SetColorAndOpacity( newColor );
	}

	FLinearColor GetCtaBackgroundColor() override
	{
		return Get_BGColorCTAMessage();
	}

	FLinearColor Get_BGColorCTAMessage()
	{
		return _BG_ColorCTAMessage;
	}

	void SetLargeText( FText message )
	{
		if ( IsValid( Description ) )
			Description.SetText( message );
	}

	void SetSmallText( FText message )
	{
		if ( IsValid( Direction ) )
			Direction.SetText( message );
	}

	private UNCAudioAsset __ChooseOnAppearSound( UAS_PriorityMessageData data )
	{
		if ( IsValid( data.onAppearSound ) )
			return data.onAppearSound;

		// defaults
		switch ( data.priorityLevel )
		{
			case EPriorityMessageLevel::DEFENDER:
			case EPriorityMessageLevel::ATTACKER:
			case EPriorityMessageLevel::OTHER_LEFT:
			{
				switch ( data.placement )
				{
					case EPriorityMessagePlacement::CENTER:
						return PriorityMessage().centerDefaultLeft;
					case EPriorityMessagePlacement::EDGE:
						return PriorityMessage().edgeDefaultLeft;
					default:
						ScriptAssert( false, f"ChooseOnAppearSound didn't handle Placement {data.placement}" );
						break;
				}
			}
			break;

			case EPriorityMessageLevel::BASE:
			case EPriorityMessageLevel::POI_SMALL:
			case EPriorityMessageLevel::POI_LARGE:
			case EPriorityMessageLevel::DOOMSDAY:
			case EPriorityMessageLevel::CAREPACKAGE:
			case EPriorityMessageLevel::OTHER_RIGHT:
			{
				switch ( data.placement )
				{
					case EPriorityMessagePlacement::CENTER:
						return PriorityMessage().centerDefaultRight;
					case EPriorityMessagePlacement::EDGE:
						return PriorityMessage().edgeDefaultRight;
					default:
						ScriptAssert( false, f"ChooseOnAppearSound didn't handle Placement {data.placement}" );
						break;
				}
			}
			break;

			case EPriorityMessageLevel::_count:
				break;
		}

		ScriptAssert( false, f"ChooseOnAppearSound didn't handle PriorityLevel {data.priorityLevel}" );
		return nullptr;
	}

	void SetWing( UAS_PriorityMessageWingWidget NewWing )
	{
		AddedWing = NewWing;
	}

	void SetCTA( FText ctaText ) override
	{
		Super::SetCTA( ctaText );
		if ( IsValid( CTA_Message ) )
			CTA_Message.SetText( ctaText );
	}

	void ShowCTA() override
	{
		Super::ShowCTA();
		if ( IsValid( CTA_AppearAnim ) )
			PlayAnimation( CTA_AppearAnim );
	}

	void HideCTA() override
	{
		Super::HideCTA();

		if ( IsValid( CTA_DisappearAnim ) )
			PlayAnimation( CTA_DisappearAnim );
	}

	void OnCountdownTick( int startMs, float32 length ) override
	{
		Super::OnCountdownTick( startMs, length );

		int ElapsedTimeMS	= GetTimeMilliseconds() - startMs;
		float32 ElapsedTime = TO_SECONDS( ElapsedTimeMS );
		float32 TimeLeft	= Math::Max( length - ElapsedTime, 0.0f );

		FNumberFormattingOptions options;
		options.SetMinMaxFractionalDigits( 0 );
		options.SetMinMaxIntegralDigits( 2 );
		options.SetRoundingMode( ERoundingMode::FromZero );

		if ( TimeLeft > 0 )
		{
			float32 SecRemainder = TimeLeft % 60.0;
			int SecLeft			 = Math::FloorToInt( SecRemainder );
			int MSLeft			 = Math::RoundToInt( TO_MILLISECONDS( SecRemainder - float32( SecLeft ) ) * 0.1 );
			if ( MSLeft == 100 )
				MSLeft = 99;

			FTimespan timespan = FTimespan( 0, 0, int( TimeLeft ) );
			FText min		   = FText::AsNumber( timespan.Minutes, options );
			FText sec		   = FText::AsNumber( timespan.Seconds, options );
			FText ms		   = FText::AsNumber( MSLeft, options );
			FText timerText	   = GetLocalizedText( Localization::Utilities, f"standard_timer", FFormatArgumentValue( min ), FFormatArgumentValue( sec ), FFormatArgumentValue( ms ) );

			if ( IsValid( Countdown ) )
				Countdown.SetText( timerText );
		}
		else
		{
			FText min		= FText::AsNumber( 0, options );
			FText sec		= FText::AsNumber( 0, options );
			FText ms		= FText::AsNumber( 0, options );
			FText timerText = GetLocalizedText( Localization::Utilities, f"standard_timer", FFormatArgumentValue( min ), FFormatArgumentValue( sec ), FFormatArgumentValue( ms ) );

			if ( IsValid( Countdown ) )
				Countdown.SetText( timerText );
			messageData.onCountDownStop.Broadcast( this, EPriorityMessageCountdownStopReason::ENDED );
		}
	}

	void ShowCountDown() override
	{
		if ( IsCountdownVisible() )
			return;

		Super::ShowCountDown();
		if ( IsValid( ShowCountdownAnim ) )
		{
			PlayAnimation( ShowCountdownAnim );
		}
	}

	void HideCountdown() override
	{
		if ( !IsCountdownVisible() )
			return;

		Super::HideCountdown();
		if ( IsValid( HideCountdownAnim ) )
		{
			PlayAnimation( HideCountdownAnim );
		}
	}

	void DePrioritize() override
	{
		Super::DePrioritize();

		if ( IsValid( DePrioritizeAnim ) )
		{
			LerpColorDuration = DePrioritizeAnim.GetEndTime();
			PlayAnimation( DePrioritizeAnim );
		}

		// lerp color
		float dimmer	= 0.4;
		StartColor_Main = Get_BGColorMainMessage();
		GoalColor_Main	= OgColor_Main;
		GoalColor_Main.R *= dimmer;
		GoalColor_Main.G *= dimmer;
		GoalColor_Main.B *= dimmer;

		StartColor_CTA = Get_BGColorCTAMessage();
		GoalColor_CTA  = OgColor_CTA;
		GoalColor_CTA.R *= dimmer;
		GoalColor_CTA.G *= dimmer;
		GoalColor_CTA.B *= dimmer;

		LerpColorStartMS = GetTimeMilliseconds();

		System::ClearAndInvalidateTimerHandle( LerpColorHandle );
		__LerpColor();
	}

	void RePrioritize() override
	{
		Super::RePrioritize();

		if ( IsValid( RePrioritizeAnim ) )
		{
			LerpColorDuration = RePrioritizeAnim.GetEndTime();
			PlayAnimation( RePrioritizeAnim );
		}

		// lerp color
		StartColor_Main = Get_BGColorMainMessage();
		GoalColor_Main	= OgColor_Main;

		StartColor_CTA = Get_BGColorCTAMessage();
		GoalColor_CTA  = OgColor_CTA;

		LerpColorStartMS = GetTimeMilliseconds();

		System::ClearAndInvalidateTimerHandle( LerpColorHandle );
		__LerpColor();
	}

	UFUNCTION()
	void __LerpColor()
	{
		float ElapsedTime = TO_SECONDS( GetTimeMilliseconds() - LerpColorStartMS );
		float Alpha		  = Math::Min( ElapsedTime / LerpColorDuration, 1.0 );

		FLinearColor newBGColor = Math::Lerp( StartColor_Main, GoalColor_Main, Alpha );
		Set_BGColorMainMessage( newBGColor );

		FLinearColor newCTAColor = Math::Lerp( StartColor_CTA, GoalColor_CTA, Alpha );
		Set_BGColorCTAMessage( newCTAColor );

		if ( Alpha < 1.0 )
			LerpColorHandle = System::SetTimerForNextTick( this, "__LerpColor" );
	}

	FLinearColor Get_BGColorMainMessage()
	{
		return _BG_ColorMainMessage;
	}
}