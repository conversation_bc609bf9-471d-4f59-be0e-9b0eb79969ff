UCLASS( Abstract )
class UAS_MinimapWidget : UAS_MapWidget
{
	default RotationBehavior = EMapRotationBehavior::FollowPlayer;
	default PositionBehavior = EMapPositionBehavior::FollowPlayer;

	// Used to switch between walking and riding zoom levels
	UPROPERTY( EditInstanceOnly, BlueprintHidden )
	private FFloatInterval walkAndRideZoomLevels;
	default walkAndRideZoomLevels.Min = 0.5f;
	default walkAndRideZoomLevels.Max = 0.7f;

	UPROPERTY( EditInstanceOnly, BlueprintHidden )
	private float inBaseZoomLevel = 0.4f;

	private bool isOnMount = false;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		Super::Construct();

		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			scriptCallbacks.client_onMountStateChanged.AddUFunction( this, n"OnMountStateChanged" );
			OnMountStateChanged( Client_GetLocalASPawn(), nullptr, false );
		}
	}

	void SetWalkingMode()
	{
		LerpToZoomLevel( walkAndRideZoomLevels.Min );
	}

	void SetMountedMode()
	{
		LerpToZoomLevel( walkAndRideZoomLevels.Max );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnMountStateChanged( ANCPlayerCharacter player, AAS_VehicleMount mount, bool isRiding )
	{
		if ( !IsValid( player ) || player != Client_GetLocalASPawn() || IsInBase() )
			return;

		// Cache it for later
		isOnMount = isRiding;

		if ( isOnMount )
		{
			SetMountedMode();
		}
		else
		{
			SetWalkingMode();
		}
	}

	private void OnPlayerEnteredOrExitedMapSwapTrigger( AAS_PlayerEntity player, AAS_BaseSystem base ) override
	{
		Super::OnPlayerEnteredOrExitedMapSwapTrigger( player, base );

		if ( IsInBase() )
		{
			// Zoom in to the lowest level we will go
			LerpToZoomLevel( inBaseZoomLevel );
		}
		else
		{
			// Update the zoom to match their curruent state
			OnMountStateChanged( player, nullptr, player.IsPlayerRidingMount() );
		}
	}
}