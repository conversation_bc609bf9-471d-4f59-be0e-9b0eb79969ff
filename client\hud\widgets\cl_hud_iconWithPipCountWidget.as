UCLASS( Abstract )
class UAS_IconWithPipCountWidget : UUserWidgetDefault
{
	UPROPERTY( NotVisible, BindWidget )
	UImage mainIcon;
	UPROPERTY( NotVisible, BindWidget )
	UImage shadowIcon;
	UPROPERTY( NotVisible, BindWidget )
	UTextBlock textObject;
	UPROPERTY( NotVisible, BindWidget )
	UImage emptyIcon;

	UPROPERTY( NotVisible, BindWidget )
	UImage tab0;
	UPROPERTY( NotVisible, BindWidget )
	UImage tab1;
	UPROPERTY( NotVisible, BindWidget )
	UImage tab2;
	UPROPERTY( NotVisible, BindWidget )
	UImage tab3;

	UPROPERTY()
	TArray<UImage> tabObjects;

	bool isActive;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		tabObjects.Add( tab0 );
		tabObjects.Add( tab1 );
		tabObjects.Add( tab2 );
		tabObjects.Add( tab3 );
	}

	AAS_PlayerEntity GetPlayer()
	{
		return Cast<AAS_PlayerEntity>( Widget_GetPawnOwner( this ) );
	}

	void SetIsActive( bool active )
	{
		isActive = active;

		if ( active )
		{
			SetWidgetVisibilitySafe(mainIcon,  ESlateVisibility::HitTestInvisible );
			SetWidgetVisibilitySafe(shadowIcon,  ESlateVisibility::HitTestInvisible );
			SetWidgetVisibilitySafe(textObject,  ESlateVisibility::HitTestInvisible );
			SetWidgetVisibilitySafe(emptyIcon,  ESlateVisibility::Hidden );
		}
		else
		{
			SetWidgetVisibilitySafe(mainIcon,  ESlateVisibility::Hidden );
			SetWidgetVisibilitySafe(shadowIcon,  ESlateVisibility::Hidden );
			SetWidgetVisibilitySafe(textObject,  ESlateVisibility::Hidden );
			SetWidgetVisibilitySafe(emptyIcon,  ESlateVisibility::HitTestInvisible );
		}
	}

	void UpdatePanelContents( FLootDataStruct lootData, int count )
	{
		bool active = count > 0;

		SetIsActive( active );
		mainIcon.SetBrushFromTexture( lootData.icon );
		shadowIcon.SetBrushFromTexture( lootData.icon );
		textObject.SetText( FText::AsNumber( count, FNumberFormattingOptions() ) );
	}
}