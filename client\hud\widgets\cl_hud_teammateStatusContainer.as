UCLASS( Abstract )
class UAS_TeammateStatusContainer : UUserWidgetDefault
{
	UPROPERTY( NotVisible, BlueprintReadOnly, Meta = ( BindWidget ) )
	UPanelWidget teammateStatusContainerWidget;

	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<UAS_PlayerWidget> teammateStatusWidgetClass;

	TMap<ANCPlayerCharacter, UAS_PlayerWidget> teammateStatusWidgets;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		if ( !GameModeDefaults().UI_ShowTeammates )
		{
			teammateStatusContainerWidget.SetVisibility( ESlateVisibility::Collapsed );
			return;
		}

		ClientCallbacks().OnPlayerChangedTeam.AddUFunction( this, n"Callback_OnPlayerChangedTeam" );
		ClientCallbacks().OnPlayerCreated.AddUFunction( this, n"Callback_OnPlayerCreated" );
		ClientCallbacks().OnPlayerDestroyed.AddUFunction( this, n"Callback_OnPlayerDestroyed" );


		for ( ANCPlayerCharacter player : GetAllPlayers() )
		{
			Callback_OnPlayerCreated( player );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void Callback_OnPlayerChangedTeam( ANCPlayerCharacter Player, int OldTeamNumber, int NewTeamNumber )
	{
		ANCPlayerCharacter MyPawn = Client_GetLocalPawn();

		if ( !IsValid( MyPawn ) )
			return;

		if ( !IsValid( Player ) )
			return;

		bool isPlayerStateLocal = MyPawn == Player;

		if ( isPlayerStateLocal )
		{
			TArray<ANCPlayerCharacter> players = UNCUtils::Client_GetAllPlayers( this );
			for ( ANCPlayerCharacter player : players )
			{
				if ( MyPawn != player )
					Callback_OnPlayerChangedTeam( player, player.GetTeam(), player.GetTeam() );
			}
			return;
		}

		if ( teammateStatusWidgets.Contains( Player ) && ( MyPawn.GetTeam() != NewTeamNumber || NewTeamNumber == -1 || MyPawn.GetTeam() == -1 ) )
		{
			teammateStatusWidgets[Player].RemoveFromParent();
			teammateStatusWidgets.Remove( Player );
		}

		if ( !teammateStatusWidgets.Contains( Player ) && MyPawn.GetTeam() == NewTeamNumber && NewTeamNumber != -1 )
		{
			UAS_PlayerWidget NewWidget = Cast<UAS_PlayerWidget>( WidgetBlueprint::CreateWidget( teammateStatusWidgetClass, GetOwningPlayer() ) );
			teammateStatusContainerWidget.AddChild( NewWidget );
			NewWidget.SetPlayer( Player );

			teammateStatusWidgets.Add( Player, NewWidget );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void Callback_OnPlayerCreated( ANCPlayerCharacter Player )
	{
		Callback_OnPlayerChangedTeam( Player, Player.GetTeam(), Player.GetTeam() );
	}

	UFUNCTION( NotBlueprintCallable )
	void Callback_OnPlayerDestroyed( ANCPlayerCharacter Player, EEndPlayReason EndPlayReason )
	{
		if ( teammateStatusWidgets.Contains( Player ) )
		{
			teammateStatusWidgets[Player].RemoveFromParent();
			teammateStatusWidgets.Remove( Player );
		}
	}
}