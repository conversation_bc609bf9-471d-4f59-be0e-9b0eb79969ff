mixin TOptional<TSubclassOf<UAS_UsePrompt_AddonWidget>> GetUseAddon( AActor self )
{
	TOptional<TSubclassOf<UAS_UsePrompt_AddonWidget>> result;

	{
		UAS_UsableComponent_WithAddon comp = Cast<UAS_UsableComponent_WithAddon>( self.GetComponentByClass( UAS_UsableComponent_WithAddon::StaticClass() ) );
		if ( IsValid( comp ) )
		{
			result.Set( comp.GetUseAddon() );
			return result;
		}
	}
	{
		UAS_ExclusiveUsableComponent_WithAddon comp = Cast<UAS_ExclusiveUsableComponent_WithAddon>( self.GetComponentByClass( UAS_ExclusiveUsableComponent_WithAddon::StaticClass() ) );
		if ( IsValid( comp ) )
		{
			result.Set( comp.GetUseAddon() );
			return result;
		}
	}

	return result;
}

// this class tells the use prompt to create an addon
UCLASS()
class UAS_UsableComponent_WithAddon : UUsableItemComponent
{
	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<UAS_UsePrompt_AddonWidget> registeredAddon;

	TSubclassOf<UAS_UsePrompt_AddonWidget> GetUseAddon()
	{
		return registeredAddon;
	}
}

UCLASS()
class UAS_ExclusiveUsableComponent_WithAddon : UExclusiveUsableItemComponent
{
	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<UAS_UsePrompt_AddonWidget> registeredAddon;

	TSubclassOf<UAS_UsePrompt_AddonWidget> GetUseAddon()
	{
		return registeredAddon;
	}
}

UCLASS( Abstract )
class UAS_UsePrompt_AddonWidget : UUserWidgetDefault
{
	void Init()
	{}
	void OnStartUse()
	{}
	void OnStopUse()
	{}
	FAddonWidgetDisplayOptions Update( UUsableItemComponent useComponent, AActor ownerActor, ANCPlayerCharacter localPlayer )
	{
		return FAddonWidgetDisplayOptions();
	}
}

struct FAddonWidgetDisplayOptions
{
	bool hideTextProgressBar = false;
}

UCLASS( Abstract )
class UAS_UsePrompt_AddonWidget_BombPlant : UAS_UsePrompt_AddonWidget
{
	UPROPERTY( NotVisible, BlueprintHidden, BindWidget )
	private UWidgetSwitcher widgetSwitcher;

	UPROPERTY( NotVisible, BlueprintHidden, BindWidget )
	private UPanelWidget defusePanel;

	UPROPERTY( NotVisible, BlueprintHidden, BindWidget )
	private UAS_ObjectiveMarker objective_marker;

	UPROPERTY( NotVisible, BlueprintHidden, BindWidget )
	private UPanelWidget plantPanel;

	private bool isCenterScreenUseVisible = false;

	UPROPERTY( NotVisible, BlueprintHidden, Transient, Meta = ( BindWidgetAnim ) )
	private UWidgetAnimation anim_showCenterScreenUseInfo;

	void Init() override
	{
		Super::Init();

		widgetSwitcher.SetVisibility( ESlateVisibility::Collapsed );
		PlayAnimation( anim_showCenterScreenUseInfo, 0.9, 1, EUMGSequencePlayMode::Reverse, 3 );
	}

	FAddonWidgetDisplayOptions Update( UUsableItemComponent useComponent, AActor ownerActor, ANCPlayerCharacter localPlayer ) override
	{
		FAddonWidgetDisplayOptions initialData = Super::Update( useComponent, ownerActor, localPlayer );

		int objectiveTeam = ownerActor.GetTeam();
		int localTeam	  = localPlayer.GetTeam();

		if ( IsFriendly( objectiveTeam, localTeam ) )
		{
			widgetSwitcher.SetActiveWidget( defusePanel );
		}
		else
		{
			widgetSwitcher.SetActiveWidget( plantPanel );
		}

		objective_marker.SetOwningObjective( ownerActor );
		initialData.hideTextProgressBar = true;
		isCenterScreenUseVisible		= false;

		return initialData;
	}

	void OnStartUse() override
	{
		Super::OnStartUse();

		if ( !isCenterScreenUseVisible )
		{
			PlayAnimationForward( anim_showCenterScreenUseInfo, 3 );
			isCenterScreenUseVisible = true;
		}
	}

	void OnStopUse() override
	{
		Super::OnStopUse();

		if ( isCenterScreenUseVisible )
		{
			PlayAnimationReverse( anim_showCenterScreenUseInfo, 3 );
			isCenterScreenUseVisible = false;
		}
	}
}