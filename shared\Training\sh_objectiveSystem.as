
UAS_ObjectiveSystem Objectives()
{
	UWorld curWorld = GetCurrentWorld();
	return Cast<UAS_ObjectiveSystem>( UNCGameplaySystemsSubsystem::Get_SharedSystem( curWorld, UAS_ObjectiveSystem::StaticClass() ) );
}

USTRUCT()
struct FTrainingObjectiveDTEntry
{
	UPROPERTY( EditDefaultsOnly )
	FTrainingObjectiveGroupSettings settings;
}

struct FObjectiveMarkerTargetList
{
	TArray< AAS_ObjectiveMarkerTarget > list;
}

struct FTrainingObjectiveMarkerPingData
{
	FVector markerLocation = FVector::ZeroVector;
	ETrainingMarkerType markerIndex = ETrainingMarkerType::INVALID;
	FGameplayTag lootTag = FGameplayTag();
	AActor trackedActor = nullptr;

	FTrainingObjectiveMarkerPingData( FVector inLocation, ETrainingMarkerType inPingType, FGameplayTag inLootTag, AActor inActor )
	{
		markerLocation = inLocation;
		markerIndex = inPingType;
		lootTag = inLootTag;
		trackedActor = inActor;
	}
}

struct FTrainingObjectiveGroupList
{
	TArray<AAS_TrainingObjectiveGroup> list;
}

UCLASS()
class UAS_ObjectiveSystem : UNCGameplaySystem_Shared
{
	UPROPERTY( EditDefaultsOnly )
	UDataTable trainingObjectivesDT;

	UPROPERTY( EditDefaultsOnly )
	TMap< ETrainingMarkerType, UAS_PinnableWidgetSettings > markerIndexToPinSettings;
	
	TMap<int, FTrainingObjectiveGroupSettings> indexToSettings;
	TMap<FName, int> nameToIndex;
	TMap<int, FName> indexToName;
	TMap<FName, int> objectiveSetToTotalNumber;

	TMap<FName, FObjectiveMarkerTargetList> nameToMarkerTarget;
	
	private TArray<FTrainingObjectiveGroupList> currentObjectives;
	default currentObjectives.Reserve( 2 );
	default currentObjectives.SetNum( 2 );

	// Not yet networking category, objective groups just call this when created on the client
	private TArray<AAS_TrainingObjectiveGroup> client_activeObjectives;

	FOnTrainingObjectiveGroupComplete onCurrentObjectiveGroupEndedCallback;

	private AAS_BrushTrigger queuedObjectiveTrigger;
	private FName queuedObjectiveGroupName;
	private FNCCoroutineSignal endDelayedObjectiveThreadSignal;
	private ETrainingObjectiveCategory queuedCategory;

	UFUNCTION(BlueprintOverride)
	void BeginPlay()
	{
		TArray<FTrainingObjectiveDTEntry> entries;
		trainingObjectivesDT.GetAllRows( entries );
		TArray<FName> rowNames = trainingObjectivesDT.GetRowNames();
		int numEntries = entries.Num();
		for ( int i = 0; i < numEntries; i++ )
		{
			indexToSettings.Add( i, entries[ i ].settings );
			nameToIndex.Add( rowNames[ i ], i );
			indexToName.Add( i, rowNames[ i ] );

			const FName objectiveSetName = entries[ i ].settings.objectiveSetName;
			if ( !objectiveSetName.IsNone() )
			{
				objectiveSetToTotalNumber.FindOrAdd( objectiveSetName, 0 )++;
			}
		}

		TArray<AAS_ObjectiveMarkerTarget> allObjectiveMarkerTargets;
		GetAllActorsOfClass( allObjectiveMarkerTargets );
		int numObjectiveMarkerTargets = allObjectiveMarkerTargets.Num();
		for( int i = 0; i < numObjectiveMarkerTargets; i++ )
		{
			const AAS_ObjectiveMarkerTarget target = allObjectiveMarkerTargets[ i ];
			const FName markerName = target.markerName;
			if ( nameToMarkerTarget.Contains( markerName ) )
			{
				nameToMarkerTarget[ markerName ].list.Add(target );
			}
			else
			{
				FObjectiveMarkerTargetList newList;
				newList.list.Add( target );
				nameToMarkerTarget.Add( markerName, newList );
			}
		}

		if ( IsClient() )
		{
			UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"ServerCommand_ObjectiveIncorrectAction", n"SC_OnObjectiveIncorrectAction" );
		}
	}

	// Server only
	AAS_TrainingObjectiveGroup ServerAddObjectiveGroup( FName groupName, ANCPlayerCharacter player, ETrainingObjectiveCategory objectiveCategory )
	{
		FTrainingObjectiveDTEntry entry;
		// TODO: The bool fails, but it sets a struct???
		bool rowMissing = !trainingObjectivesDT.FindRow( groupName, entry );
		if ( rowMissing )
		{
			Warning( f"Warning! Tried to set objective group to {groupName} but it wasn't in the objectiveGroups TMap!");
			return nullptr;
		}

		AAS_TrainingObjectiveGroup instantiatedGroup = Cast< AAS_TrainingObjectiveGroup >( Server_SpawnEntity( AAS_TrainingObjectiveGroup::StaticClass(), player ) );
		ServerSetObjectiveGroup_WithInstantiatedGroup( instantiatedGroup, groupName, player, objectiveCategory );
		return instantiatedGroup;
	}

	// Don't use yet - doesn't have a way for client to know what objectives were used within the group. The purpose of this is to let you make and configure objectives before sending it to this.
	// For example, make an objective group and set data on the contained objectives before initializing it. How will client get those same params, since objectives themselves aren't networked?
	
	void ServerSetObjectiveGroup_WithInstantiatedGroup( AAS_TrainingObjectiveGroup instantiatedGroup, FName groupName, ANCPlayerCharacter player, ETrainingObjectiveCategory objectiveCategory )
	{
		#if EDITOR
			Print(f"Setting objective group: {groupName} category: {objectiveCategory}", 10, FLinearColor::Yellow );
		#endif
		int objectiveCategoryIdx = int( objectiveCategory );

		const bool isHint = objectiveCategory == ETrainingObjectiveCategory::TEMPORARY_HINT;
		bool shouldActivate = isHint || ( !isHint && currentObjectives[ ETrainingObjectiveCategory::TEMPORARY_HINT ].list.IsEmpty() );

		currentObjectives[ objectiveCategoryIdx ].list.Add( instantiatedGroup );
		instantiatedGroup.ServerInitialize( player, groupName );
		if ( shouldActivate )
		{
			instantiatedGroup.ServerSetIsActiveUIObjective();
		}

		// Disable default objective if we're creating a hint -- the hint should stomp the default one
		if ( isHint )
		{
			FTrainingObjectiveGroupList& groupList = currentObjectives[ ETrainingObjectiveCategory::DEFAULT ];
			for( AAS_TrainingObjectiveGroup group : groupList.list )
			{
				group.ServerClearIsActiveUIObjective();
			}
		}

		instantiatedGroup.onObjectiveGroupEnded.AddUFunction( this, n"OnCurrentObjectiveGroupEnded" );
	}

	void ClearObjectiveGroupByName( FName groupName, ETrainingObjectiveCategory objectiveCategory )
	{
		// Is objective of the corresponding class active
		// If yes, kill it
		FTrainingObjectiveDTEntry entry;
		bool rowMissing = !trainingObjectivesDT.FindRow( groupName, entry );
		if ( rowMissing )
		{
			Warning( f"Warning! Tried to set objective group to {groupName} but it wasn't in the objectiveGroups TMap!");
			return;
		}

		int objectiveCategoryIdx = int( objectiveCategory );
		FTrainingObjectiveGroupList& groupList = currentObjectives[ objectiveCategoryIdx ];
		const int numGroupsInCategory = groupList.list.Num();
		for( int i = numGroupsInCategory - 1; i >= 0; i--  )
		{
			AAS_TrainingObjectiveGroup group = groupList.list[ i ];
			if ( group.GetObjectiveName().IsEqual( groupName ) )
			{
				RemoveObjective( group, objectiveCategory );
				break;
			}
		}
	}

	private void RemoveObjective( AAS_TrainingObjectiveGroup inGroup, ETrainingObjectiveCategory category )
	{
		if ( !currentObjectives[ category ].list.Contains( inGroup ) )
		{
			return;
		}

		inGroup.onObjectiveGroupEnded.Unbind( this, n"OnCurrentObjectiveGroupEnded" );

		#if EDITOR
			Print(f"Removing objective group {inGroup.GetObjectiveName()} in category: {category}", 10, FLinearColor::Purple );
		#endif
		inGroup.Destroy();
		currentObjectives[ category ].list.Remove( inGroup );

		// If clearing a hint, re-enable default objectives
		if ( category == ETrainingObjectiveCategory::TEMPORARY_HINT )
		{
			FTrainingObjectiveGroupList& groupList = currentObjectives[ ETrainingObjectiveCategory::DEFAULT ];
			TArray<AAS_TrainingObjectiveGroup>& groups = groupList.list;
			for( AAS_TrainingObjectiveGroup group : groups )
			{
				group.ServerSetIsActiveUIObjective();
			}
		}
	}

	// Pass nullptr for no trigger, pass -1 for no delay. You don't need both.
	void SetObjectiveGroup_OnTouchTriggerOrTimePassed( FName groupName, AAS_BrushTrigger trigger, float timeUntilForceShow, ETrainingObjectiveCategory category )
	{
		// Must provide either trigger or time.
		ScriptAssert( IsValid( trigger ) || timeUntilForceShow >= 0, f"Tried to set an objective to show on trigger or time passed, but neither a trigger nor valid time was given" );
		ClearQueuendObjective();

		queuedObjectiveGroupName = groupName;
		queuedCategory = category;

		if ( IsValid( trigger ) )
		{
			queuedObjectiveTrigger = trigger;
			queuedObjectiveTrigger.onPlayerEntered.AddUFunction( this, n"OnPlayerEnteredQueuedObjectiveTrigger" );
		}

		if ( timeUntilForceShow >= 0 )
		{
			Thread( this, n"DelayedObjectiveThread", timeUntilForceShow );
		}
	}

	private void ClearQueuendObjective()
	{
		if ( IsValid( queuedObjectiveTrigger ) )
		{
			queuedObjectiveTrigger.onPlayerEntered.Unbind( this, n"OnPlayerEnteredQueuedObjectiveTrigger" );
		}
		
		endDelayedObjectiveThreadSignal.Emit();
		queuedObjectiveGroupName = NAME_None;
	}

	UFUNCTION()
	private void OnPlayerEnteredQueuedObjectiveTrigger( AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger )
	{
		ServerAddObjectiveGroup( queuedObjectiveGroupName, asPlayer, queuedCategory );
		ClearQueuendObjective();
	}

	UFUNCTION()
	private void DelayedObjectiveThread( UNCCoroutine co, float delayTime, AAS_PlayerEntity asPlayer )
	{
		co.EndOn( this, endDelayedObjectiveThreadSignal );
		co.Wait( delayTime );

		ServerAddObjectiveGroup( queuedObjectiveGroupName, asPlayer, queuedCategory );
		ClearQueuendObjective();
	}

	UFUNCTION()
	AAS_TrainingObjectiveMarkerActor ServerCreateObjectiveMarker( FTrainingObjectiveMarkerPingData markerData, ANCPlayerCharacter player ) const
	{
		FVector locationToUse = FVector::ZeroVector;
		if ( IsValid( markerData.trackedActor ) )
		{
			const ANCDestructible trackedDestructible = Cast<ANCDestructible>( markerData.trackedActor );
			if ( IsValid( trackedDestructible ) )
			{
				locationToUse = trackedDestructible.GetCenter();
			}
			else
			{
				locationToUse = markerData.trackedActor.GetActorLocation();
			}
		}
		else
		{
			locationToUse = markerData.markerLocation;
		}

		AActor newActor = Server_SpawnEntity( AAS_TrainingObjectiveMarkerActor::StaticClass(), nullptr, locationToUse );
		Print(f"Creating objective marker! {newActor}");
		AAS_TrainingObjectiveMarkerActor newMarker = Cast< AAS_TrainingObjectiveMarkerActor >( newActor );
		newMarker.ServerSetMarkerIndex( markerData.markerIndex );

		return newMarker;
	}

	UFUNCTION()
	private void OnCurrentObjectiveGroupEnded( AAS_TrainingObjectiveGroup group, ETrainingObjectiveEndContext reason )
	{
		onCurrentObjectiveGroupEndedCallback.Broadcast( group, reason );
		
		int numObjectives = currentObjectives.Num();
		for( int i = 0; i < numObjectives; i++ )
		{
			FTrainingObjectiveGroupList& groupsInCategory = currentObjectives[ i ];
			TArray<AAS_TrainingObjectiveGroup>& groups = groupsInCategory.list;
			const int numGroups = groups.Num();
			for( int j = 0; j < numGroups; j++ )
			{
				if ( groups[ j ] == group )
				{
					RemoveObjective( group, ETrainingObjectiveCategory( i ) );
					return;
				}
			}
		}
	}

	UFUNCTION()
	private void SC_OnObjectiveIncorrectAction( TArray<FString> args )
	{
		if ( args.IsEmpty() )
		{
			return;
		}

		int objectiveGroupId = args[ 0 ].ToInt();
		int objectiveIndex = args[ 1 ].ToInt();

		AAS_TrainingObjectiveGroup objectiveGroup = Cast<AAS_TrainingObjectiveGroup>( GetEntity( objectiveGroupId ) );
		if ( !IsValid( objectiveGroup ) )
		{
			return;
		}

		if ( objectiveIndex < 0 || objectiveGroup.objectives.Num() <= objectiveIndex )
		{
			return;
		}

		UAS_TrainingObjective objective = objectiveGroup.objectives[ objectiveIndex ];
		if ( !IsValid( objective ) )
		{
			return;
		}

		objective.client_onObjectiveIncorrectAction.Broadcast( objective );

		if ( objective.incorrectActionDialogue.dialogueTag.IsValid() && !objective.incorrectActionDialoguePlayed )
		{
			objective.incorrectActionDialoguePlayed = true;
			
			TArray<FTrainingDialogue> dialogueData;
			dialogueData.Add( objective.incorrectActionDialogue );
			objectiveGroup.PlayTrainingDialogue( dialogueData, false );
		}
	}

	void ClientRegisterObjective( AAS_TrainingObjectiveGroup newGroup )
	{
		client_activeObjectives.AddUnique( newGroup );
	}

	void ClientUnregisterObjective( AAS_TrainingObjectiveGroup groupToRemove )
	{
		if ( client_activeObjectives.Contains( groupToRemove ) )
		{
			client_activeObjectives.Remove( groupToRemove );
		}
	}

	bool ClientIsObjectiveActive( FName objectiveName )
	{
		for( AAS_TrainingObjectiveGroup group : client_activeObjectives )
		{
			if ( group.GetObjectiveName() == objectiveName )
			{
				return true;
			}
		}

		return false;
	}

	TArray<AAS_TrainingObjectiveGroup> cl_GetAllActiveObjectiveGroups()
	{
		ScriptAssert( IsClient(), "CLIENT ONLY" );
		return client_activeObjectives;
	}

	AAS_TrainingObjectiveGroup cl_GetActiveObjectiveGroupByName( FName objectiveName )
	{
		ScriptAssert( IsClient(), "CLIENT ONLY" );

		for( AAS_TrainingObjectiveGroup group : client_activeObjectives )
		{
			if ( !IsValid( group ) )
			{
				continue;
			}
			
			if ( group.GetObjectiveName() == objectiveName )
			{
				return group;
			}
		}
		return nullptr;
	}

	int GetIndexForGroupName( FName groupName )
	{
		return nameToIndex[ groupName ];
	}

	const FTrainingObjectiveGroupSettings& GetSettingsForIndex( int index )
	{
		return indexToSettings[ index ];
	}

	FName GetSettingsNameForIndex( int index )
	{
		return indexToName[ index ];
	}

	TArray<AAS_TrainingObjectiveGroup>& sv_GetAllActiveObjectiveGroups()
	{
		ScriptAssert( IsServer(), "SERVER ONLY" );
		return currentObjectives[ ETrainingObjectiveCategory::DEFAULT ].list;
	}

	// Category slot always exists even if contents are invalid
	AAS_TrainingObjectiveGroup ServerGetActiveObjective( FName objectiveName, ETrainingObjectiveCategory category )
	{
		ScriptAssert( IsServer(), "SERVER ONLY" );
		FTrainingObjectiveGroupList& groupList = currentObjectives[ category ];
		TArray<AAS_TrainingObjectiveGroup>& groups = groupList.list;
		const int numGroups = groups.Num();
		for( int i = 0; i < numGroups; i++ )
		{
			if ( groups[ i ].GetObjectiveName().IsEqual( objectiveName ) )
			{
				return groups[ i ];
			}
		}

		return nullptr;
	}

	bool ServerIsObjectiveActive( FName objectiveName, ETrainingObjectiveCategory category )
	{
		ScriptAssert( IsServer(), "SERVER ONLY" );
		if ( currentObjectives[ category ].list.IsEmpty() )
		{
			return false;
		}

		FTrainingObjectiveGroupList& groupList = currentObjectives[ category ];
		TArray<AAS_TrainingObjectiveGroup>& groups = groupList.list;
		const int numGroups = groups.Num();
		for( int i = 0; i < numGroups; i++ )
		{
			if ( groups[ i ].GetObjectiveName().IsEqual( objectiveName ) )
			{
				return true;
			}
		}
		return false;
	}

	TArray<AAS_ObjectiveMarkerTarget> GetObjectiveMarkersForName( FName name )
	{
		if ( nameToMarkerTarget.Contains( name ) )
		{
			return nameToMarkerTarget[ name ].list;
		}

		return TArray<AAS_ObjectiveMarkerTarget>();
	}

	TArray<FTrainingObjectiveMarkerPingData> GetObjectiveMarkerDataForName( FName name )
	{
		TArray<FTrainingObjectiveMarkerPingData> objectiveMarkers;
		TArray<AAS_ObjectiveMarkerTarget> markers = GetObjectiveMarkersForName( name );
		if ( !markers.IsEmpty() )
		{
			int numMarkers = markers.Num();
			for( int i = 0; i < numMarkers; i++ )
			{
				const AAS_ObjectiveMarkerTarget marker = markers[ i ];
				if ( !IsValid( marker ) )
				{
					continue;
				}
				objectiveMarkers.Add( FTrainingObjectiveMarkerPingData( marker.GetActorLocation(), marker.markerType, FGameplayTag(), nullptr ) );
			}
		}

		return objectiveMarkers;
	}

	int GetTotalObjectivesInSet( FName set )
	{
		if ( objectiveSetToTotalNumber.Contains( set ) )
		{
			return objectiveSetToTotalNumber[ set ];
		}

		return -1;
	}

	FName GetObjectiveCreateFlag(FName objectiveName)
	{
		return FName( f"ObjectiveCreated_{objectiveName}" );
	}
	
	FName GetObjectiveCompleteFlag(FName objectiveName)
	{
		return FName( f"ObjectiveComplete_{objectiveName}" );
	}
}