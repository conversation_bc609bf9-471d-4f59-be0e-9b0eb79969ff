UCLASS( Abstract )
class UAS_TrainingObjective_Signal : UAS_TrainingObjective
{
	// Complete when signal fires
	UPROPERTY( EditDefaultsOnly )
	FName triggerSignal;
	
	// Complete when flag is set
	UPROPERTY( EditDefaultsOnly )
	FName flagToBeSet;

	void ServerSetPlayer(ANCPlayerCharacter inPlayer) override
	{
		Super::ServerSetPlayer(inPlayer);
		
		AAS_TrainingSession_RaidMode trainingSession;
		if ( !triggerSignal.IsNone() )
		{
			ScriptCallbacks().RegisterSignalCallback( triggerSignal, this, n"OnTriggerSignal" );
		}

		if ( !flagToBeSet.IsNone() )
		{
			ScriptCallbacks().RegisterFlagSafe( flagToBeSet );
			Thread( this, n"FlagWaitThread", flagToBeSet );
		}
	}

	UFUNCTION()
	void OnTriggerSignal( FName signalName, UObject sender )
	{
		ServerMarkAsComplete();
	}

	FNCCoroutineSignal flagWaitEndSignal;
	UFUNCTION()
	void FlagWaitThread( UNCCoroutine co, FName flagName )
	{
		co.EndOn( this, flagWaitEndSignal );

		co.FlagWait( flagToBeSet );
		ServerMarkAsComplete();
	}

	void CleanUp() override
	{
		Super::CleanUp();
		ScriptCallbacks().UnRegisterSignalCallback( triggerSignal, this, n"OnTriggerSignal" );
		flagWaitEndSignal.Emit();
	}
}

UCLASS()
class UAS_TrainingObjective_Location : UAS_TrainingObjective
{
	UPROPERTY( EditDefaultsOnly, Meta=(EditCondition="objectiveMarkerName!=NAME_None", EditConditionHides ) )
	float markerRadius;

	UPROPERTY( EditDefaultsOnly )
	bool mustStayInsideTrigger = false;

	UPROPERTY( EditDefaultsOnly )
	FName brushTriggerTag = NAME_None;

	FVector location;
	ETrainingMarkerType markerType;
	AAS_SphereTriggerActor serverSpawnedTrigger;
	AAS_BrushTrigger brushTrigger;

	void ServerSetPlayer(ANCPlayerCharacter inPlayer) override
	{
		Super::ServerSetPlayer(inPlayer);
		UpdateMarkerData();

		if ( brushTriggerTag != NAME_None )
		{
			ServerSetBrushTriggerTag( brushTriggerTag );
		}
	}

	void UpdateMarkerData()
	{
		if ( objectiveMarkerName != NAME_None )
		{
			TArray<AAS_ObjectiveMarkerTarget> markers = Objectives().GetObjectiveMarkersForName( objectiveMarkerName );
			ScriptAssert( markers.Num() == 1, f"Location objective cannot have more than one objective marker!" );
			if ( markers.IsEmpty() )
			{
				return;
			}

			AAS_ObjectiveMarkerTarget target = markers[ 0 ];
			SetMarkerData( target.GetActorLocation(), markerRadius, target.markerType );
		}
	}

	void SetMarkerData( FVector inLocation, float inRadius, ETrainingMarkerType inMarkerType )
	{
		if ( !IsValid( brushTrigger ) && brushTriggerTag == NAME_None )
		{
			AActor spawnedActor = Server_SpawnEntity( AAS_SphereTriggerActor::StaticClass(), player, inLocation );
			serverSpawnedTrigger = Cast<AAS_SphereTriggerActor>( spawnedActor );
			if ( !IsValid( serverSpawnedTrigger ) )
			{
				return;
			}
			
			serverSpawnedTrigger.TriggerComponent.SetRadius( inRadius );
			serverSpawnedTrigger.TriggerComponent.onPlayerEntered.AddUFunction( this, n"OnPlayerEntered" );
			if ( mustStayInsideTrigger )
			{
				serverSpawnedTrigger.TriggerComponent.onPlayerExited.AddUFunction( this, n"OnPlayerExited" );
			}
		}

		location 	= inLocation;
		markerType 	= inMarkerType;
		
		onObjectiveUpdated.Broadcast( this );
	}

	void ServerSetBrushTriggerTag( FName tagName )
	{
		TArray<AAS_BrushTrigger> brushTriggers;
		GetAllActorsOfClassWithTag( tagName, brushTriggers );

		const int numTriggersFound = brushTriggers.Num();
		if ( numTriggersFound != 1 )
		{
			Warning( f"Warning! Incorrect amount of triggers found for location objective. Found {numTriggersFound}" );
			return;
		}

		brushTrigger = brushTriggers[ 0 ];
		
		brushTrigger.onPlayerEntered.AddUFunction( this, n"OnPlayerEnteredBrushTrigger" );
		if ( mustStayInsideTrigger )
		{
			brushTrigger.onPlayerExited.AddUFunction( this, n"OnPlayerExitedBrushTrigger" );
		}

		UpdateMarkerData();
	}

	UFUNCTION()
	void OnPlayerEnteredBrushTrigger( AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger )
	{
		if ( asPlayer == player )
		{
			ServerMarkAsComplete();
		}
	}

	UFUNCTION()
	void OnPlayerExitedBrushTrigger( AAS_PlayerEntity asPlayer, AAS_BrushTrigger trigger )
	{
		if ( asPlayer == player )
		{
			ServerMarkAsIncomplete();
		}
	}

	UFUNCTION()
	void OnPlayerEntered( AAS_PlayerEntity asPlayer, UAS_SphereTrigger trigger )
	{
		if ( asPlayer == player )
		{
			ServerMarkAsComplete();
		}
	}

	UFUNCTION()
	void OnPlayerExited( AAS_PlayerEntity asPlayer, UAS_SphereTrigger trigger )
	{
		if ( asPlayer == player )
		{
			ServerMarkAsIncomplete();
		}
	}

	void GetObjectiveMarkerLocation( TArray<FTrainingObjectiveMarkerPingData>&out outData ) override
	{
		FTrainingObjectiveMarkerPingData markerPingData;
		markerPingData.markerLocation = location;
		markerPingData.markerIndex = markerType;
		outData.Add( markerPingData );
	}
	// Create a trigger on the location
	// bind to that trigger
	// cleanup

	void CleanUp() override
	{
		Super::CleanUp();
		if ( IsValid( serverSpawnedTrigger ) )
		{
			serverSpawnedTrigger.Destroy();
		}

		if ( IsValid( brushTrigger ) )
		{
			brushTrigger.onPlayerEntered.Unbind( this, n"OnPlayerEnteredBrushTrigger" );
			brushTrigger.onPlayerExited.Unbind( this, n"OnPlayerExitedBrushTrigger" );
		}
	}
}

UCLASS( Abstract )
class UAS_TrainingObjective_GetItems : UAS_TrainingObjective
{
	UPROPERTY( EditDefaultsOnly )
	TArray<FBackpackItemStruct> requiredItems;

	UPROPERTY( EditDefaultsOnly )
	bool checkIfAlreadyHasItems;

	UPROPERTY( EditDefaultsOnly )
	bool itemMustBeAmmoForRaidWeapon;

	// Use this if someone needs to harvest ore to buy something, and you don't want the objective to fail aftr they spend ore on something.
	UPROPERTY( EditDefaultsOnly )
	bool stopTrackingItemsAfterComplete;

	void ServerSetPlayer(ANCPlayerCharacter inPlayer) override
	{
		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( inPlayer );
		asPlayer.backpackComponent.OnContentsChangedDelegate.AddUFunction( this, n"OnBackpackContentsChanged" );
		asPlayer.resourceBackpackComponent.OnContentsChangedDelegate.AddUFunction( this, n"OnBackpackContentsChanged" );
		asPlayer.equippedShieldBackpackComponent.OnContentsChangedDelegate.AddUFunction( this, n"OnBackpackContentsChanged" );
		asPlayer.shieldBreakerBackpackComponent.OnContentsChangedDelegate.AddUFunction( this, n"OnBackpackContentsChanged" );
		asPlayer.ammoBackpackComponent.OnContentsChangedDelegate.AddUFunction( this, n"OnBackpackContentsChanged" );

		if ( checkIfAlreadyHasItems )
		{
			// Awkward timing issue: Server script may not have hit the WaitFlag yet, since this can complete on frame 0
			// No good way to do "wait flag AND do this" so.. here we are.
			Thread( this, n"CheckIfAlreadyHasItemsThread");
		}

		// Get item objectives often used for harvesting. Special case to exclude broken resource nodes from candidate actors
		if ( objectiveMarkerDoFindTarget )
		{
			for( FBackpackItemStruct item : requiredItems )
			{
				if ( !IsLootIndexValid( item.itemIndex ) )
				{
					continue;
				}

				const FLootDataStruct& lootData = GetLootDataByIndex( item.itemIndex );
				if ( lootData.lootType == ELootType::ResourceItem )
				{
					foundTargets_params.evaluateFoundActorDelegate.BindUFunction( this, n"EvaluateFoundActors" );
					break;
				}
			}
		}
		
		// We're modifying found target params, so do this last
		Super::ServerSetPlayer(inPlayer);
	}

	FNCCoroutineSignal endCheckIfAlreadyHasItemsThread;
	UFUNCTION()
	void CheckIfAlreadyHasItemsThread( UNCCoroutine co )
	{
		co.EndOn( this, endCheckIfAlreadyHasItemsThread );
		co.Wait( 0.1 );

		OnBackpackContentsChanged( nullptr );
	}

	UFUNCTION()
	void OnBackpackContentsChanged( UBackpackComponent backpack )
	{
		UAS_BackpackComponent asBackpack = Cast<UAS_BackpackComponent>( backpack );
		if ( IsValid( asBackpack ) && itemMustBeAmmoForRaidWeapon && asBackpack.CanAddTypeToBackpack( ELootType::Ammo ) )
		{
			requiredItems.Empty();
			AAS_PlayerEntity asPlayer = backpack.GetOwnerPlayer();
			if ( IsValid( asPlayer ) )
			{
				ANCWeapon raidWeapon = asPlayer.GetWeaponAtSlot( WeaponSlot::RaidToolsSlot );
				if ( IsValid( raidWeapon ) )
				{
					FNCWeaponPermutationId weaponId = MakeWeaponId( raidWeapon );
					if ( IsLootIndexValidForPrimaryWeapon( weaponId ) )
					{
						FLootDataStruct raidWeaponLootData = GetLootDataForPrimaryWeapon( weaponId );
						FLootDataStruct ammoLootData = GetLootDataForAmmo( raidWeaponLootData.ammoSource );
						// Raid hammer is special case, min to fire is 0
						int ammoCountToBuy = Math::Max( raidWeapon.WeaponConfigData.ammoMinToFire, 1 ) * 2;
						requiredItems.Add( MakeBackpackItem( ammoLootData.index, ammoCountToBuy, 0, 0 ) );
					}
				}
			}
		}

		if ( doProgressBar )
		{
			TArray<float32> progressFracs;
			for ( FBackpackItemStruct item : requiredItems )
			{
				int inBackpack = CountItemsInBackpackAndValidBoxes( player, item.itemIndex, nullptr, CraftingSource::BACKPACK );
				if ( item.itemCount > 0 )
				{
					progressFracs.Add( float32( inBackpack ) / float32( item.itemCount ) );
				}
			}

			float32 finalFrac = 0;
			for( float32 frac : progressFracs )
			{
				finalFrac += frac;
			}

			if ( !requiredItems.IsEmpty() )
			{
				finalFrac /= float32( requiredItems.Num() );
			}

			SetObjectiveProgress( finalFrac, int(finalFrac), requiredItems.Num() );
		}
		else
		{
			if ( DoesPlayerHaveIngredients( player, requiredItems, nullptr, CraftingSource::BACKPACK ) )
			{
				ServerMarkAsComplete();
			}
			else
			{
				ServerMarkAsIncomplete();
			}
		}
			
		if ( IsObjectiveComplete() && stopTrackingItemsAfterComplete )
		{
			DoUnbinds();
		}
	}

	TArray<AAS_ResourceNode> trackedResources;
	void UpdateObjectiveMarkersFromFindTarget() override
	{
		Super::UpdateObjectiveMarkersFromFindTarget();

		for( FTrainingObjectiveMarkerPingData pingData : objectiveMarkers )
		{
			AAS_ResourceNode trackedActorAsResource = Cast<AAS_ResourceNode>( pingData.trackedActor );
			if ( IsValid( trackedActorAsResource ) )
			{
				trackedActorAsResource.sh_OnResourceAmountChanged.AddUFunction( this, n"OnTrackedResourceChanged" );
				trackedResources.Add( trackedActorAsResource );
			}
		}
	}

	UFUNCTION()
	bool EvaluateFoundActors( AActor inActor )
	{
		// Filter out empty resources
		AAS_ResourceNode actorAsResource = Cast<AAS_ResourceNode>( inActor );
		if ( IsValid( actorAsResource ) && actorAsResource.GetResourceAmount() <= 0 )
		{
			return false;
		}

		return true;
	}

	UFUNCTION()
	void OnTrackedResourceChanged( AAS_ResourceNode changedResource )
	{
		// Only update if the resource was destroyed
		if ( changedResource.GetResourceAmount() > 0 )
		{
			return;
		}

		int numObjectiveMarkers = objectiveMarkers.Num();
		for( int i = numObjectiveMarkers - 1; i >= 0; i-- )
		{
			const FTrainingObjectiveMarkerPingData& objectiveMarkerData = objectiveMarkers[ i ];
			AAS_ResourceNode trackedResource = Cast<AAS_ResourceNode>( objectiveMarkerData.trackedActor );
			if ( trackedResource == changedResource )
			{
				objectiveMarkers.RemoveAtSwap( i );
				trackedResources.Remove( changedResource );
				onObjectiveUpdated.Broadcast( this );
				break;
			}
		}
	}

	void GetObjectiveMarkerLocation(TArray<FTrainingObjectiveMarkerPingData>&out outData) override
	{
		// Not great. Makes it so the found objectives are always updated when this is called, so that looping nags update which targets to show.
		if ( objectiveMarkerDoFindTarget )
		{
			UpdateObjectiveMarkersFromFindTarget();
		}

		outData = objectiveMarkers;
	}

	void CleanUp() override
	{
		Super::CleanUp();
		DoUnbinds();

		endCheckIfAlreadyHasItemsThread.Emit();
	}

	void DoUnbinds()
	{
		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( player );
		if ( IsValid( asPlayer ) )
		{
			asPlayer.backpackComponent.OnContentsChangedDelegate.Unbind( this, n"OnBackpackContentsChanged" );
			asPlayer.resourceBackpackComponent.OnContentsChangedDelegate.Unbind( this, n"OnBackpackContentsChanged" );
			asPlayer.equippedShieldBackpackComponent.OnContentsChangedDelegate.Unbind( this, n"OnBackpackContentsChanged" );
			asPlayer.shieldBreakerBackpackComponent.OnContentsChangedDelegate.Unbind( this, n"OnBackpackContentsChanged" );
			asPlayer.ammoBackpackComponent.OnContentsChangedDelegate.Unbind( this, n"OnBackpackContentsChanged" );
		}

		for( AAS_ResourceNode trackedResource : trackedResources )
		{
			if ( !IsValid( trackedResource ) )
			{
				continue;
			}

			trackedResource.sh_OnResourceAmountChanged.Unbind( this, n"OnTrackedResourceChanged" );
		}

		trackedResources.Empty();
	}
}



UCLASS( Abstract )
class UAS_TrainingObjective_GetWeapon : UAS_TrainingObjective
{
	UPROPERTY( EditDefaultsOnly )
	const bool requireSpecificWeapon = true;

	UPROPERTY( EditDefaultsOnly, Meta=(EditCondition="requireSpecificWeapon", EditConditionHides) )
	const FGameplayTag weaponLootTag;

	UPROPERTY( EditDefaultsOnly )
	const bool checkIfPlayerAlreadyHasWeapon;

	void ServerSetPlayer(ANCPlayerCharacter inPlayer) override
	{
		Super::ServerSetPlayer(inPlayer);
		
		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( player );
		ScriptCallbacks().server_onPlayerPickedUpWeapon.AddUFunction( this, n"OnPlayerPickedUpWeapon" );
		if ( requireSpecificWeapon )
		{
			ScriptCallbacks().server_onPlayerDroppedWeapon.AddUFunction( this, n"OnPlaterDroppedWeapon" );
		}

		if ( checkIfPlayerAlreadyHasWeapon )
		{
			ANCWeapon weapon = asPlayer.GetWeaponAtSlot( WeaponSlot::PrimarySlot0 );
			if ( IsValid( weapon ) )
			{
				OnPlayerPickedUpWeapon( asPlayer, MakeBackpackItem( weapon ), WeaponSlot::PrimarySlot0 );
			}

			weapon = asPlayer.GetWeaponAtSlot( WeaponSlot::PrimarySlot1 );
			if ( IsValid( weapon ) )
			{
				OnPlayerPickedUpWeapon( asPlayer, MakeBackpackItem( weapon ), WeaponSlot::PrimarySlot1 );
			}
		}
	}

	UFUNCTION()
	void OnPlayerPickedUpWeapon( ANCPlayerCharacter inPlayer, FBackpackItemStruct item, int slot )
	{
		if ( inPlayer != player )
		{
			return;
		}

		if ( requireSpecificWeapon && item.itemIndex != weaponLootTag )
		{
			return;
		}

		ServerMarkAsComplete();
	}

	UFUNCTION()
	void OnPlaterDroppedWeapon( ANCPlayerCharacter inPlayer, FBackpackItemStruct item, int slot )
	{
		if ( inPlayer != player )
		{
			return;
		}

		if ( item.itemIndex != weaponLootTag )
		{
			return;
		}

		// It's a primary weapon, and you're holding the weapon in your other slot
		if ( slot == WeaponSlot::PrimarySlot0 || slot == WeaponSlot::PrimarySlot1 )
		{
			ANCWeapon weaponInOtherSlot = inPlayer.GetWeaponAtSlot( 1 - slot );
			if ( IsValid( weaponInOtherSlot ) )
			{
				FNCWeaponPermutationId weaponId = MakeWeaponId( weaponInOtherSlot );
				if ( IsLootIndexValidForWeapon( weaponId ) )
				{
					FLootDataStruct weaponLootData = GetLootDataForWeapon( weaponId );
					if ( weaponLootData.index == weaponLootTag )
					{
						return;
					}
				}
			}
		}

		ServerMarkAsIncomplete();
	}

	void CleanUp() override
	{
		Super::CleanUp();
		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			ScriptCallbacks().server_onPlayerPickedUpWeapon.Unbind( this, n"OnPlayerPickedUpWeapon" );
			ScriptCallbacks().server_onPlayerDroppedWeapon.Unbind( this, n"OnPlaterDroppedWeapon" );
		}
	}
}

UCLASS( Abstract )
class UAS_TrainingObjective_InputButtons : UAS_TrainingObjective
{
	// Maximum of 4
	UPROPERTY( EditDefaultsOnly )
	FName inputAction;

	FScriptDelegateHandle inputHandle;
	bool inputComplete;

	void ServerSetPlayer(ANCPlayerCharacter inPlayer) override
	{
		Super::ServerSetPlayer(inPlayer);

		inputHandle = player.AddButtonPressedCallback( inputAction, this, n"OnInputPressed" );
		inputComplete =  false;
	}

	UFUNCTION()
	void OnInputPressed()
	{
		ServerMarkAsComplete();
	}

	void CleanUp() override
	{
		Super::CleanUp();
		if ( IsServer() )
		{
			if ( player.ButtonPressedCallback_DoesExist( inputAction, inputHandle ) )
			{
				player.RemoveButtonPressedCallback( inputAction, inputHandle );
			}
		}
	}
}

event void FOnTargetDummyDamagedIncorrectly( AAS_TargetDummy dummy );

UCLASS( Abstract )
class UAS_TrainingObjective_DamageTargetDummies : UAS_TrainingObjective
{
	UPROPERTY( EditDefaultsOnly )
	int numDummiesToDamage;

	UPROPERTY( EditDefaultsOnly )
	bool requireWeaponToBeScoped;

	UPROPERTY( EditDefaultsOnly )
	bool mustUseGrenades;

	UPROPERTY( EditDefaultsOnly )
	bool mustBeKill;

	UPROPERTY( EditDefaultsOnly, Meta = ( ForceInlineRow, Categories = "Loot.Weapon" ) )
	TArray<FGameplayTag> whitelistedWeapons;

	UPROPERTY( EditDefaultsOnly )
	bool objectiveMarkersOnSurvivingDummies;

	FOnTargetDummyDamagedIncorrectly onDummyDamagedIncorrectly;

	TArray<AAS_TargetDummy> damagedTargetDummies;
	TArray<AAS_TargetDummy> monitoredDummies;

	void ServerSetPlayer(ANCPlayerCharacter inPlayer) override
	{
		foundTargets_params.evaluateFoundActorDelegate.BindUFunction( this, n"EvaluateFoundActors" );
		Super::ServerSetPlayer(inPlayer);
	}

	void SetTargetDummiesToTrack( TArray<AAS_TargetDummy> targetDummies )
	{
		for( AAS_TargetDummy targetDummy : targetDummies )
		{
			targetDummy.onTargetDummyDamaged.AddUFunction( this, n"OnTargetDummyDamaged" );
			monitoredDummies.Add( targetDummy );
		}

		if ( objectiveMarkersOnSurvivingDummies )
		{
			UpdateDummyMarkers();
		}
	}

	void UpdateDummyMarkers()
	{
		objectiveMarkers.Empty();
		UpdateObjectiveMarkersFromFindTarget();

		// If find target failed, try to show any dummy.
		if ( objectiveMarkers.IsEmpty() )
		{
			for( AAS_TargetDummy targetDummy : monitoredDummies )
			{
				if ( !IsValid( targetDummy ) )
				{
					continue;
				}
				
				if ( !damagedTargetDummies.Contains( targetDummy ) )
				{
					// Update ping
					FTrainingObjectiveMarkerPingData newData;
					newData.markerLocation = targetDummy.GetActorLocation();
					newData.markerIndex = ETrainingMarkerType::ENEMY;
					newData.trackedActor = targetDummy;

					objectiveMarkers.Add( newData );

					// Just show one dummy at a time
					break;
				}
			}

			onObjectiveUpdated.Broadcast( this );
		}
	}

	UFUNCTION()
	bool EvaluateFoundActors( AActor actor )
	{
		AAS_TargetDummy dummy = Cast<AAS_TargetDummy>( actor );
		if ( !IsValid( dummy ) )
		{
			return false;
		}

		if ( dummy.IsHidden() )
		{
			return false;

		}

		if ( dummy.isDead )
		{
			return false;
		}

		if ( !monitoredDummies.Contains( dummy ) )
		{
			return false;
		}

		return true;
	}

	UFUNCTION()
	void OnTargetDummyDamaged( AAS_TargetDummy dummy, const FDamageInfo damageInfo )
	{
		if ( !IsValid( dummy ) )
		{
			return;
		}

		if ( mustBeKill )
		{
			if ( dummy.GetHealth() > 0 )
			{
				return;
			}
		}

		if ( requireWeaponToBeScoped )
		{
			bool hasOptic = false;
			ANCProjectileBolt inflictor = Cast<ANCProjectileBolt>( damageInfo.Inflictor );
			if ( IsValid( inflictor ) && IsValid( inflictor.Weapon ) )
			{
				hasOptic = DoesWeaponHaveOpticEquipped( inflictor.Weapon );
			}

			if ( !hasOptic )
			{
				onDummyDamagedIncorrectly.Broadcast( dummy );
				ServerSendObjectiveIncorrectAction();
				return;
			}
		}

		if ( mustUseGrenades )
		{
			ANCGrenade grenade = Cast<ANCGrenade>( damageInfo.Inflictor );
			if ( !IsValid( grenade ) )
			{
				onDummyDamagedIncorrectly.Broadcast( dummy );
				ServerSendObjectiveIncorrectAction();
				return;
			}
		}

		if ( !whitelistedWeapons.IsEmpty() )
		{
			ANCProjectileBolt inflictorBolt = Cast<ANCProjectileBolt>( damageInfo.Inflictor );
			if ( IsValid( inflictorBolt ) )
			{
				ANCWeapon weapon = inflictorBolt.GetWeapon();
				if ( IsValid( weapon ) )
				{
					FNCWeaponPermutationId weaponId = MakeWeaponId( weapon );
					if ( IsLootIndexValidForWeapon( weaponId ) )
					{
						FLootDataStruct weaponLootData = GetLootDataForPrimaryWeapon( weaponId );
						if ( !whitelistedWeapons.Contains( weaponLootData.index ) )
						{
							onDummyDamagedIncorrectly.Broadcast( dummy );
							ServerSendObjectiveIncorrectAction();
							return;
						}
					}
				}
			}
		}

		damagedTargetDummies.AddUnique( dummy );
		monitoredDummies.Shuffle();
		UpdateDummyMarkers();
		
		SetObjectiveProgress( float32( float32( damagedTargetDummies.Num() ) / float32( numDummiesToDamage ) ), damagedTargetDummies.Num(), numDummiesToDamage );
	}

	void CleanUp() override
	{
		Super::CleanUp();

		if ( IsServer() )
		{
			for( AAS_TargetDummy targetDummy : monitoredDummies )
			{
				targetDummy.onTargetDummyDamaged.Unbind( this, n"OnTargetDummyDamaged" );
			}
		}

		onDummyDamagedIncorrectly.Clear();
	}
}

UCLASS( Abstract )
class UAS_TrainingObjective_DoScopeSwap : UAS_TrainingObjective
{
	void ServerSetPlayer(ANCPlayerCharacter inPlayer) override
	{
		Super::ServerSetPlayer(inPlayer);
		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( inPlayer );
		asPlayer.onPlayerScopeSwap.AddUFunction( this, n"OnPlayerScopeSwap" );
	}

	UFUNCTION()
	void OnPlayerScopeSwap( AAS_PlayerEntity asPlayer, ANCWeapon weapon )
	{
		bool hasOptic = DoesWeaponHaveOpticEquipped( weapon );
		if ( hasOptic )
		{
			ServerMarkAsComplete();
		}
		else
		{
			ServerMarkAsIncomplete();
		}
	}
}

event void FOnPlayerDiedIncorrectly();

UCLASS( Abstract )
class UAS_TrainingObjective_Die : UAS_TrainingObjective
{
	UPROPERTY( EditDefaultsOnly )
	bool playerMustHavePurpleShield = true;

	FOnPlayerDiedIncorrectly onPlayerDiedIncorrectly;

	void ServerSetPlayer(ANCPlayerCharacter inPlayer) override
	{
		Super::ServerSetPlayer(inPlayer);
		
		inPlayer.Server_OnPawnDeath.AddUFunction( this, n"OnPlayerDeath" );
	}

	UFUNCTION()
	void OnPlayerDeath( const FDamageInfo&in damageInfo, ANCPlayerCharacter victim )
	{
		if ( victim != player )
		{
			return;
		}

		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( victim );
		if ( playerMustHavePurpleShield && ( !asPlayer.IsActorShieldValid() || !( asPlayer.GetEquippedShieldItem().itemIndex == GameplayTags::Loot_Armor_Level2 ) ) )
		{
			onPlayerDiedIncorrectly.Broadcast();
			ServerSendObjectiveIncorrectAction();
			return;
		}

		ServerMarkAsComplete();
	}
}

UCLASS( Abstract )
class UAS_TrainingObjective_TakeFallDamage : UAS_TrainingObjective
{
	void ServerSetPlayer(ANCPlayerCharacter inPlayer) override
	{
		Super::ServerSetPlayer(inPlayer);
		inPlayer.HealthComponent.BP_OnPostReceivedDamage.AddUFunction( this, n"OnPlayerTookDamage" );
	}

	UFUNCTION()
	void OnPlayerTookDamage( UHealthComponent damagedComponent, const FDamageInfo&in damageInfo )
	{
		if ( damagedComponent.GetOwnerPlayer() != player )
		{
			return;
		}

		if( Bitflags::HasFlag( damageInfo.scriptDamageFlags, EScriptDamageFlags::DF_FALL_DAMAGE ) )
		{
			ServerMarkAsComplete();
		}
	}
}

UCLASS( Abstract )
class UAS_TrainingObjective_ReviveDummy : UAS_TrainingObjective
{
	AAS_TargetDummy_Human trackedDummy;

	void ServerSetPlayer(ANCPlayerCharacter inPlayer) override
	{
		Super::ServerSetPlayer(inPlayer);
	}

	void SetTrackedTargetDummy( AAS_TargetDummy_Human targetDummy )
	{
		targetDummy.onTargetDummyRespawned.AddUFunction( this, n"OnTargetDummyRespawned" );
	}

	UFUNCTION()
	void OnTargetDummyRespawned( AAS_TargetDummy dummy )
	{
		ServerMarkAsComplete();
	}

	void CleanUp() override
	{
		Super::CleanUp();
		if ( IsValid( trackedDummy ) )
		{
			trackedDummy.onTargetDummyRespawned.Unbind( this, n"OnTargetDummyRespawned" );
		}
	}
}

UCLASS( Abstract )
class UAS_TrainingObjective_PurchaseFromVendor : UAS_TrainingObjective
{
	UPROPERTY( EditDefaultsOnly, Meta=(Categories = "Loot" ) )
	TArray<FGameplayTag> itemsToBuy;

	void ServerSetPlayer(ANCPlayerCharacter inPlayer) override
	{
		Super::ServerSetPlayer(inPlayer);
		
		ScriptCallbacks().server_onVendorPurchase.AddUFunction( this, n"OnPlayerPurchasedItemFromVendor" );
	}

	UFUNCTION()
	void OnPlayerPurchasedItemFromVendor( AAS_VendorStoreData vendor, FVendorData storeItem, ANCPlayerCharacter inPlayer, FBackpackItemStruct item )
	{
		if ( inPlayer != player )
		{
			return;
		}

		if ( itemsToBuy.Contains( item.itemIndex ) )
		{
			ServerMarkAsComplete();
			// Update in case the thing you bought changes other objectives' evaluations
			if ( IsObjectiveComplete() )
			{
				onObjectiveUpdated.Broadcast( this );
			}
		}
	}

	void CleanUp() override
	{
		Super::CleanUp();
		if ( IsValid( ScriptCallbacks() ) )
		{
			ScriptCallbacks().server_onVendorPurchase.Unbind( this, n"OnPlayerPurchasedItemFromVendor" );
		}
	}
}

UCLASS( Abstract )
class UAS_TrainingObjective_TalkToVendor : UAS_TrainingObjective
{
	AAS_Vendor targetVendor;

	void ServerSetPlayer(ANCPlayerCharacter inPlayer) override
	{
		Super::ServerSetPlayer(inPlayer);
	}

	void SetTargetVendor( AAS_Vendor vendor )
	{
		if ( IsValid( targetVendor ) )
		{
			targetVendor.onPlayerUsedVendor.Unbind( this, n"OnPlayerUsedVendor" );
		}

		targetVendor = vendor;
		targetVendor.onPlayerUsedVendor.AddUFunction( this, n"OnPlayerUsedVendor" );
		onObjectiveUpdated.Broadcast( this );
	}

	void GetObjectiveMarkerLocation(TArray<FTrainingObjectiveMarkerPingData>&out outData) override
	{
		Super::GetObjectiveMarkerLocation(outData);
		
		if ( IsValid( targetVendor ) )
		{
			FTrainingObjectiveMarkerPingData data;
			data.markerLocation = targetVendor.GetActorLocation();
			data.trackedActor = targetVendor;
			data.markerIndex = ETrainingMarkerType::VENDOR;
			outData.Add( data );
		}
	}

	UFUNCTION()
	void OnPlayerUsedVendor( AAS_PlayerEntity asPlayer, AAS_Vendor vendor )
	{
		if ( asPlayer != player )
		{
			return;
		}

		if ( vendor != targetVendor )
		{
			return;
		}

		ServerMarkAsComplete();
	}

	void CleanUp() override
	{
		Super::CleanUp();
		if ( IsValid( targetVendor ) )
		{
			targetVendor.onPlayerUsedVendor.Unbind( this, n"OnPlayerUsedVendor" );
		}
	}
}

event void FOnDestructibleDestroyedIncorrectlyEvent( ANCDestructible destructible );

UCLASS()
class UAS_TrainingObjective_DestroyWalls : UAS_TrainingObjective
{
	default doProgressBar = true;

	UPROPERTY( EditDefaultsOnly, Meta = ( ForceInlineRow, Categories = "Loot.Weapon" ) )
	TArray< UWeaponPrimaryAsset > whitelistedWeapons;

	TArray< ANCDestructible > destroyedDestructibles;

	UPROPERTY( EditDefaultsOnly )
	FGameplayTag requiredWallCategory;
	default requiredWallCategory = GameplayTags::Destruction_Category_Stone;

	UPROPERTY( EditDefaultsOnly )
	int numWallsToDestroy;
	int numDestroyedWalls = 0;

	UPROPERTY( EditDefaultsOnly )
	bool requireRedmaneLeap;

	float32 leapEndTime = 0;
	bool isLeaping = false;

	void ServerSetPlayer(ANCPlayerCharacter inPlayer) override
	{
		Super::ServerSetPlayer(inPlayer);
		
		if ( whitelistedWeapons.IsEmpty() )
		{
			UNCDestructionManager::Get( GetCurrentWorld() ).OnDestructibleDestroyed_Server.AddUFunction( this, n"OnDestructibleDestroyed" );
		}
		else
		{
			UNCDestructionManager::Get( GetCurrentWorld() ).OnDestructibleDamaged_Server.AddUFunction( this, n"OnDestructibleDamaged" );
		}
		
		if ( requireRedmaneLeap )
		{
			AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( player );
			asPlayer.server_onPlayerStartedRedmaneLeap.AddUFunction( this, n"OnPlayerStartRedmaneLeap" );
			asPlayer.OnLeapEnded.AddUFunction( this, n"OnLeapEnded" );
		}
	}

	UFUNCTION()
	void OnDestructibleDestroyed( FNCDestructibleDestroyedContext_Server destructionContext )
	{
		if ( destructionContext.Attacker != player )
		{
			return;
		}

		// TODO: Does this work on doors/windows?
		if ( requiredWallCategory.IsValid() && destructionContext.Destructible.GetDestructibleCategory().DestructibleCategoryID != requiredWallCategory )
		{
			return;
		}
		
		MarkDestructibleDestroyed( destructionContext.Destructible );
	}

	UFUNCTION()
	void OnDestructibleDamaged( ANCDestructible destructible, const FDamageInfo&in damageInfo )
	{
		bool success = false;
		/* do while false */ while ( true )
		{
			if ( destructible.GetHealth() > 0 )
			{
				break;
			}

			const FName weaponClassName = damageInfo.damageWeaponClassName;
			if ( !IsValidWeaponClassName( weaponClassName ) )
			{
				break;
			}

			const UWeaponPrimaryAsset weaponAsset = GetWeaponClassFromClassName( weaponClassName );
			if ( !whitelistedWeapons.Contains( weaponAsset ) )
			{
				break;
			}

			success = true;

			break;
		}

		if ( success )
		{
			MarkDestructibleDestroyed( destructible );
		}
	}

	UFUNCTION()
	void OnPlayerStartRedmaneLeap( AAS_PlayerEntity leapingPlayer )
	{
		if ( leapingPlayer != player )
		{
			return;
		}

		isLeaping = true;
	}

	UFUNCTION()
	void OnLeapEnded()
	{
		// one second grace period in case of a near-miss
		leapEndTime = TO_SECONDS( player.GetTimeMilliseconds() ) + 1.0;
		isLeaping = false;
	}

	void MarkDestructibleDestroyed( ANCDestructible destructible )
	{
		if ( requireRedmaneLeap )
		{
			float32 curTime = TO_SECONDS( player.GetTimeMilliseconds() );
			float32 remainingTimeToHitTrigger = leapEndTime - curTime;
			bool withinEndLeapGracePeriod = remainingTimeToHitTrigger > 0;
			if ( !withinEndLeapGracePeriod && !isLeaping )
			{
				return;
			}
		}

		ANCDestructible destructibleToMark = GetPrimaryDestructible( destructible );

		bool didAdd = destroyedDestructibles.AddUnique( destructibleToMark );
		if ( didAdd )
		{
			numDestroyedWalls++;
			SetObjectiveProgress( float32( numDestroyedWalls ) / float32( numWallsToDestroy ), numDestroyedWalls, numWallsToDestroy );
		}
	}

	void CleanUp() override
	{
		Super::CleanUp();
		UNCDestructionManager::Get( GetCurrentWorld() ).OnDestructibleDestroyed_Server.Unbind( this, n"OnDestructibleDestroyed" );
		UNCDestructionManager::Get( GetCurrentWorld() ).OnDestructibleDamaged_Server.Unbind( this, n"OnDestructibleDamaged" );
	}
}

UCLASS()
class UAS_TrainingObjective_ReinforceWalls : UAS_TrainingObjective
{
	UPROPERTY( EditDefaultsOnly )
	int numWallsToReinforce = 1;
	
	UPROPERTY( EditDefaultsOnly )
	FName linkedMarkerNames;

	int numWallsUpgraded = 0;
	TArray< AAS_ObjectiveMarkerTarget > markers;
	TArray< ANCDestructible> markerToDestructible;
	TArray<FTrainingObjectiveMarkerPingData> pingData;

	void ServerSetPlayer(ANCPlayerCharacter inPlayer) override
	{
		Super::ServerSetPlayer(inPlayer);
		
		ScriptCallbacks().server_onWallUpgraded.AddUFunction( this, n"OnWallUpgraded" );

		// get linked markers -> destructibles
		// 
		if ( !linkedMarkerNames.IsNone() )
		{
			markers = Objectives().GetObjectiveMarkersForName( linkedMarkerNames );
			for( AAS_ObjectiveMarkerTarget marker : markers )
			{
				pingData.Add( FTrainingObjectiveMarkerPingData( marker.GetActorLocation(), marker.markerType, FGameplayTag(), nullptr ) );
			}
		}
	}

	UFUNCTION()
	void OnWallUpgraded( ANCPlayerCharacter inPlayer, ANCDestructible destructible )
	{
		if ( inPlayer != player )
		{
			return;
		}

		if ( !linkedMarkerNames.IsNone() )
		{
			const float32 MAX_DIST_SQR = 512.f * 512.f;
			// HACKY - Check markers to see if one is in range. Can't manually link since destructibles are often part of a real base instance, and I don't want to change those.
			const int numMarkers = markers.Num();
			for( int i = numMarkers - 1; i >= 0; i-- )
			{
				AAS_ObjectiveMarkerTarget marker = markers[ i ];
				float32 distSqr = float32( destructible.GetCenter().DistSquared2D( marker.GetActorLocation() ) );
				if ( distSqr <= MAX_DIST_SQR )
				{
					markers.RemoveAt( i );
					pingData.RemoveAt( i );
				}
			}
		}


		numWallsUpgraded++;
		if ( doProgressBar )
		{			
			SetObjectiveProgress( float32(numWallsUpgraded) / float32(numWallsToReinforce), numWallsUpgraded, numWallsToReinforce );
		}
		else if ( numWallsUpgraded == numWallsToReinforce )
		{
			ServerMarkAsComplete();
		}
		// In case not progress, want to update if there are pings.
		else
		{
			onObjectiveUpdated.Broadcast( this );
		}
	}

	void GetObjectiveMarkerLocation(TArray<FTrainingObjectiveMarkerPingData>&out outData) override
	{
		if ( !linkedMarkerNames.IsNone() )
		{
			outData = pingData;
		}
		else
		{
			Super::GetObjectiveMarkerLocation(outData);

			// Remove iron walls from the list (hackyyy)
			int numMarkers = outData.Num();
			for( int i = numMarkers - 1; i >= 0; i-- )
			{
				FTrainingObjectiveMarkerPingData data = outData[ i ];
				ANCDestructible trackedDestructible = Cast<ANCDestructible>( data.trackedActor );
				if ( trackedDestructible.GetDestructibleCategory().DestructibleCategoryID == GameplayTags::Destruction_Category_Iron )
				{
					outData.RemoveAt( i );
				}
			}
		}
	}
}

UCLASS()
class UAS_TrainingObjective_GetOnMount : UAS_TrainingObjective
{
	void ServerSetPlayer(ANCPlayerCharacter inPlayer) override
	{
		Super::ServerSetPlayer(inPlayer);

		Thread( this, n"WaitForPlayerToMountThread", player );
	}

	FNCCoroutineSignal endWaitForPlayerToMountThread;
	UFUNCTION()
	void WaitForPlayerToMountThread( UNCCoroutine co, ANCPlayerCharacter inPlayer )
	{
		co.EndOn( this, endWaitForPlayerToMountThread );
		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( inPlayer );
		
		co.Wait( asPlayer, asPlayer.OnMountSignal );
		
		ServerMarkAsComplete();
	}

	void CleanUp() override
	{
		Super::CleanUp();
		endWaitForPlayerToMountThread.Emit();
	}
}

UCLASS()
class UAS_TrainingObjective_StartRaid : UAS_TrainingObjective
{
	void ServerSetPlayer(ANCPlayerCharacter inPlayer) override
	{
		Super::ServerSetPlayer(inPlayer);
		
		ScriptCallbacks().shared_OnRaidStarted.AddUFunction( this, n"OnRaidStarted" );
	}

	UFUNCTION()
	void OnRaidStarted( AAS_RaidEventManager_v2 eventManager )
	{
		ServerMarkAsComplete();
	}

	void CleanUp() override
	{
		Super::CleanUp();
		ScriptCallbacks().shared_OnRaidStarted.Unbind( this, n"OnRaidStarted" );
	}
}

UCLASS()
class UAS_TrainingObjective_RaidEvent : UAS_TrainingObjective
{
	UPROPERTY( EditDefaultsOnly )
	ERaidEventFlag raidEvent;

	void ServerSetPlayer(ANCPlayerCharacter inPlayer) override
	{
		Super::ServerSetPlayer(inPlayer);
		
		ScriptCallbacks().shared_OnRaidEvent.AddUFunction( this, n"OnRaidEvent" );
	}

	UFUNCTION()
	void OnRaidEvent( AAS_RaidEventManager_v2 eventManager, ERaidEventFlag flag )
	{
		if ( flag == raidEvent )
		{
			ServerMarkAsComplete();
		}
	}

	void CleanUp() override
	{
		Super::CleanUp();
		
		ScriptCallbacks().shared_OnRaidEvent.Unbind( this, n"OnRaidEvent" );
	}
}

UCLASS()
class UAS_TrainingObjective_BombExploded : UAS_TrainingObjective
{
	void ServerSetPlayer(ANCPlayerCharacter inPlayer) override
	{
		Super::ServerSetPlayer(inPlayer);
		
		ScriptCallbacks().server_onBombExplode.AddUFunction( this, n"OnBombExploded" );
	}

	UFUNCTION()
	void OnBombExploded( UAS_RaidBombInterfaceComponent interface, int attackerTeam, int defenderTeam )
	{
		ServerMarkAsComplete();
	}

	void CleanUp() override
	{
		Super::CleanUp();
		
		ScriptCallbacks().server_onBombExplode.Unbind( this, n"OnBombExploded" );
	}
}