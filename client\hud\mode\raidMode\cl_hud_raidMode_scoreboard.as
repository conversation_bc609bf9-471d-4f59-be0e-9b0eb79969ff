UCLASS( Abstract )
class UAS_RaidMode_ScoreboardWidget : UAS_ScoreboardWidget
{
	UPROPERTY( EditDefaultsOnly )
	bool displayTeamLevel = true;

	UPROPERTY( EditDefaultsOnly )
	int colummnCount = 3;

	UFUNCTION( BlueprintOverride )
	void AddTeamWidget( int teamNum )
	{
		Super::AddTeamWidget( teamNum );
		UAS_RaidMode_ScoreboardWidgetTeam widget = Cast<UAS_RaidMode_ScoreboardWidgetTeam>( teamWidgets[teamNum] );
		widget.displayTeamLevel					 = displayTeamLevel;
	}

	void SetRowAndColumn( UAS_ScoreboardWidgetTeam widget, int order )
	{
		int row	   = order % colummnCount;
		int column = order / colummnCount;

		UGridSlot slot = Cast<UGridSlot>( widget.Slot );
		slot.SetColumn( column );
		slot.SetRow( row );

		FMargin padding = FMargin( 5, 5 );
		slot.SetPadding( padding );
	}

	void UpdateTeams() override
	{
		Super::UpdateTeams();

		int numTeams = GetNumTeams();

		// sort teams by score
		TArray<UAS_ScoreboardWidgetTeam> sortedWidgets;
		int ii = 0;
		for ( UAS_ScoreboardWidgetTeam elem : teamWidgets )
		{
			if ( ii < numTeams )
				sortedWidgets.Add( elem );
			else
				SetWidgetVisibilitySafe( elem, ESlateVisibility::Collapsed );

			ii++;
		}

		for ( int i = 0; i < sortedWidgets.Num() - 1; i++ )
		{
			for ( int j = i + 1; j < sortedWidgets.Num(); j++ )
			{
				UAS_ScoreboardWidgetTeam widget_i = sortedWidgets[i];
				UAS_ScoreboardWidgetTeam widget_j = sortedWidgets[j];

				FString teamName_i = UNCUtils::GetTeamName( this, widget_i.GetTeam() );
				FString teamName_j = UNCUtils::GetTeamName( this, widget_j.GetTeam() );

				int score_i = GetTeamLevel( widget_i.GetTeam() );
				int score_j = GetTeamLevel( widget_j.GetTeam() );

				if ( ( score_i < score_j ) )
				{
					sortedWidgets[i] = widget_j;
					sortedWidgets[j] = widget_i;
				}
				else if ( teamName_i > teamName_j )
				{
					sortedWidgets[i] = widget_j;
					sortedWidgets[j] = widget_i;
				}
			}
		}

		int count = 0;
		for ( UAS_ScoreboardWidgetTeam widget : sortedWidgets )
		{
			SetRowAndColumn( widget, count++ );

			int team = widget.GetTeam();
			bool hideTeam = GetPlayersOfTeam( team ).IsEmpty() || TeamIsEliminated( team );
			SetWidgetVisibilitySafe( widget, hideTeam ? ESlateVisibility::Collapsed : ESlateVisibility::HitTestInvisible );
		}
	}
}

UCLASS( Abstract )
class UAS_RaidMode_ScoreboardWidgetTeam : UAS_ScoreboardWidgetTeam
{
	UPROPERTY( NotEditable, BindWidget )
	UTextBlock teamNameText;

	UPROPERTY( NotEditable, BindWidget )
	UTextBlock teamScoreText;

	UPROPERTY( NotEditable, BindWidget )
	UImage teamColorBar;

	UPROPERTY( EditAnywhere )
	bool displayTeamLevel = true;

	void SetTeam( int team ) override
	{
		Super::SetTeam( team );

		if ( displayTeamLevel )
			teamScore = GetTeamScore( team );

		FTeamPresentationData teamPresentationData = GetTeamPresentationDataForIndex( team );
		// TODO @jmccarty: Are we shipping team names as colors???
		teamNameText.SetText( GetLocalizedText( Localization::RaidMode, "team_name", FFormatArgumentValue( FText::FromName( teamPresentationData.teamName ) ) ) );

		// teamNameText.SetColorAndOpacity( teamPresentationData.teamColor );
		teamColorBar.SetColorAndOpacity( teamPresentationData.teamColor );

		teamScoreText.SetText( FText::AsNumber( teamScore, GetDefaultNumberFormattingOptionsWithGrouping() ) );
	}
}