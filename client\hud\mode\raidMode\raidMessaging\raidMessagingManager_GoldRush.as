const float TOP_PAD_MARGIN_RAID		= 150;
const float TOP_PAD_MARGIN_NON_RAID = 90;

UCLASS( Abstract )
class UAS_RaidMessagingManager_GoldRush : UAS_RaidMessagingManager
{
	UPROPERTY( EditDefaultsOnly )
	UMaterialInterface shieldBreakerMaterial;

	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<UAS_GoldRushTopWidget> topWidgetClass;

	UFUNCTION( BlueprintOverride )
	void Initialize()
	{
		Super::Initialize();

		raidMessagingHud.SetShieldBreakerIndicatorMaterial( shieldBreakerMaterial );

		UAS_GoldRushTopWidget topHud = Cast<UAS_GoldRushTopWidget>( WidgetBlueprint::CreateWidget( topWidgetClass, GetLocalPlayerController() ) );
		raidMessagingHud.gameModeExtrasOverlay.AddChild( topHud );

		GetGameStateEntity_GoldRush().net_walletsLocked.OnReplicated().AddUFunction( this, n"OnWalletLockedChanged" );
		OnWalletLockedChanged( GetGameStateEntity_GoldRush().net_walletsLocked, GetGameStateEntity_GoldRush().net_walletsLocked );
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"NotifyCraftingSoon", n"SC_NotifyCraftingSoon" );
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"TriggerRaidReport", n"SC_TriggerRaidReport" );
	}

	UFUNCTION()
	private void OnWalletLockedChanged( bool oldValue, bool newValue )
	{
		raidMessagingHud.gameModeExtrasOverlay.SetVisibility( newValue ? ESlateVisibility::Hidden : ESlateVisibility::HitTestInvisible );
	}

	UFUNCTION()
	void SC_NotifyCraftingSoon( TArray<FString> args )
	{
		Dialogue().PlayAnnouncerDialogue( GameplayTags::Audio_VO_GameUpdates_GoldRush_Shieldbreaker_Soon );

		UAS_PriorityMessageData data = GetTemplateMessageData( EPriorityMessageTemplate::SHIELDBREAKER_CRAFTING );
		data.header					 = GetLocalizedText( Localization::GoldRush, "shield_breaker_crafting_soon" );

		UCL_PriorityMessageManager_v2 manager = PriorityMessage();
		if ( IsValid( manager ) )
		{
			manager.AddMessage( data );
		}

		SetSubheaderText( data.header );
		StartCountdownTimer_Custom( GetGameTimeMS(), args[0].ToInt(), true );
	}

	UFUNCTION()
	void SC_TriggerRaidReport( TArray<FString> args )
	{
		if ( args.Num() != 4 )
			return;

		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( IsValid( player ) )
		{
			// Save off the values from the server command
			int goldStolen	 = args[0].ToInt();
			int attackerTeam = args[1].ToInt();
			// int defenderTeam	  = args[2].ToInt();
			// int eventManagerIndex = args[3].ToInt();

			// Get info about our local player
			int teamId		  = player.GetTeam();
			bool wasAttacking = attackerTeam == teamId;

			// Use the score earned and compare it to the points awarded by a shieldbreaker plant to see if the local player won the encounter
			bool localPlayerWon = ( goldStolen > 0 && wasAttacking ) || ( goldStolen == 0 && !wasAttacking );

			EPriorityMessageTemplate template;
			if ( localPlayerWon )
			{
				// If the player had a successful raid, use the win template
				template = wasAttacking ? EPriorityMessageTemplate::V2_ATTACK_WIN : EPriorityMessageTemplate::V2_DEFEND_WIN;
			}
			else
			{
				// If the player lost, use the lose template
				template = wasAttacking ? EPriorityMessageTemplate::V2_ATTACK_LOSE : EPriorityMessageTemplate::V2_DEFEND_LOSE;
			}

			UAS_PriorityMessageData data = GetTemplateMessageData( template );
			if ( IsValid( data ) )
			{
				// Set the header and calculate the number of raid coins to show
				data.header	   = GetLocalizedText( Localization::Raid, "raid_ended" );
				data.subheader = GetLocalizedText( Localization::GoldRush, "gold_stolen", FFormatArgumentValue( goldStolen ) );

				// When the player wins, we want to play some audio on the message
				if ( wasAttacking && localPlayerWon )
				{
					data.onAppearSound = raidSuccessAudio;
				}
				else if ( !wasAttacking && localPlayerWon )
				{
					data.onAppearSound = defendSuccessAudio;
				}

				if ( goldStolen > 20 )
				{
					if ( wasAttacking )
						Dialogue().PlayAnnouncerDialogue( GameplayTags::Audio_VO_GameUpdates_Raid_AttackSuccess_You );
					else
						Dialogue().PlayAnnouncerDialogue( GameplayTags::Audio_VO_GameUpdates_Raid_DefenseFailure_Enemy );
				}
				else
				{
					if ( wasAttacking )
						Dialogue().PlayAnnouncerDialogue( GameplayTags::Audio_VO_GameUpdates_Raid_AttackFailure_You );
					else
						Dialogue().PlayAnnouncerDialogue( GameplayTags::Audio_VO_GameUpdates_Raid_DefenseSuccess_Enemy );
				}

				AddPriorityMessage( data );
			}
		}
	}

	void InitializeRaidMessaging() override final
	{
		if ( !IsValid( localPlayer ) )
			return;

		int playerTeam = localPlayer.GetTeam();
		int otherTeam  = GetOtherTeam( playerTeam );

		// "Health" is the number of points the other team has subtracted from the score limit, so we track the opposite team
		int scoreLimit = GoldRushDefaults().ScoreLimit;
		SetScoreLimits( scoreLimit );
		SetTrackedTeams( playerTeam, otherTeam );
	}

	void OnGamePhaseOrTimeChanged() override
	{
		Super::OnGamePhaseOrTimeChanged();
		StartCountdownTimer( isCountdownAudioQueued );
	}

	private void OnRaidObjectiveStateChanged( int team, int oldValue, int newValue ) override final
	{
		Super::OnRaidObjectiveStateChanged( team, oldValue, newValue );

		UpdateObjectivePriorityMessage();
		UpdateObjectiveText();
		CheckForCriticalAlert();

		EObjectiveState newState = EObjectiveState( newValue );
		if ( teamObjectiveAudio.Contains( newState ) )
		{
			// Play any cues associated with the event changes from the hud
			Client_EmitSoundUI( teamObjectiveAudio[newState] );
		}

		if ( EObjectiveState( oldValue ) == EObjectiveState::PostRaid )
		{
			UCL_PriorityMessageManager_v2 messageManager = PriorityMessage();
			if ( IsValid( messageManager ) )
			{
				messageManager.ClearActiveCenterMessages( EPriorityMessageLevel::BASE );
				messageManager.ClearActiveCenterMessages( EPriorityMessageLevel::ATTACKER );
				messageManager.ClearActiveCenterMessages( EPriorityMessageLevel::DEFENDER );
			}
		}
	}

	private	void UpdateObjectiveText()
	{
		if ( !IsValid( localPlayer ) )
			return;

		AAS_TeamStateManager_RaidMode teamState = GetTeamStateManager_RaidMode( localPlayer.GetTeam() );
		if ( IsValid( teamState ) )
		{
			EObjectiveState objectiveState = teamState.GetRaidObjectiveState();

			// Store out data based on the states
			bool isShieldBreakerInWorld = false;
			bool isShieldBreakerHeld	= false;
			FString subheaderId;

			switch ( objectiveState )
			{
				case EObjectiveState::DefensePhase:
					StartCountdownTimer( false );
					subheaderId = "secure_your_base_state_subheader";
					break;
				case EObjectiveState::ShieldBreakerCrafting:
					subheaderId = "shieldbreaker_crafting_state_header";
					StartShieldbreakerCountdown();
					break;
				case EObjectiveState::PostRaid:
					subheaderId = "raid_ended";
					ShowOrHideShieldbreakerIndicator( false );
					break;
				case EObjectiveState::PickupShieldBreaker:
				case EObjectiveState::InterceptShieldBreaker:
					isShieldBreakerInWorld = true;
					ClearCountdownTimer();
					subheaderId = "pickup_shieldbreaker_state_subheader";
					break;
				case EObjectiveState::Hidden:
					ShowOrHideShieldbreakerIndicator( false );
					StartCountdownTimer( false );
					SetSubheaderText( FText() );
					return;
				case EObjectiveState::ExploreAndGearUp:
				case EObjectiveState::Raiding:
				default:
					ShowOrHideShieldbreakerIndicator( false );
					StartCountdownTimer( true );
					SetSubheaderText( GetLocalizedText( Localization::GoldRush, "gold_rush_subheader" ) );
					return; // Anything we don't care about can just be ignored
			}

			// Update the subheader text based on the game state
			SetSubheaderText( GetLocalizedText( Localization::GameState, subheaderId ) );

			// Show/hide the shieldbreaker indicator if it is active at all
			ShowOrHideShieldbreakerIndicator( isShieldBreakerHeld || isShieldBreakerInWorld );
		}
	}

	UFUNCTION()
	void StartShieldbreakerCountdown()
	{
		AAS_TeamStateManager_RaidMode teamState = GetTeamStateManager_RaidMode( localPlayer.GetTeam() );
		if ( !IsValid( teamState ) )
			return;

		EObjectiveState objectiveState = teamState.GetRaidObjectiveState();

		if ( objectiveState != EObjectiveState::ShieldBreakerCrafting )
			return;

		AAS_SBCrafter crafter = GetCurrentCrafter();
		if ( !IsValid( crafter ) )
		{
			System::SetTimer( this, n"StartShieldbreakerCountdown", 0.1, false );
			return;
		}

		StartCountdownTimer_Custom( GetGameTimeMS(), crafter.net_SBCrafterStateEndTime, true );
	}

	AAS_SBCrafter GetCurrentCrafter() const
	{
		TArray<AAS_SBCrafter> crafters;
		GetAllActorsOfClass( crafters );
		for ( AAS_SBCrafter crafter : crafters )
		{
			if ( crafter.GetSBCrafterState() == ESBCrafterState::HIDDEN )
				continue;

			return crafter;
		}

		return nullptr;
	}

	void OnRaidStartedOrEnded() override
	{
		Super::OnRaidStartedOrEnded();
		UCL_PriorityMessageManager_v2 messageManager = PriorityMessage();
		if ( !IsValid( messageManager ) )
			return;

		messageManager.ClearActiveCenterMessages( EPriorityMessageLevel::BASE );
	}

	private void OnRaidStarted( AAS_RaidEventManager_v2 eventManager ) override final
	{
		Super::OnRaidStarted( eventManager );
		OnRaidStartedOrEnded();
		ShowOrHideObjectiveMarkers( false );
		ShowOrHideShieldbreakerTracker( false );
		ShowOrHideRaidingTeamRaidState( true );
		ShowOrHideRaidingTeamRespawns( true );
		ShowOrHideShieldbreakerIndicator( false );
		if ( eventManager.GetAttackerTeam() == Client_GetLocalPawn().GetTeam() )
			SetSubheaderText( GetLocalizedText( Localization::Raid, "raid_raiding" ) );
		else
			SetSubheaderText( GetLocalizedText( Localization::Raid, "raid_defending" ) );
		SetHeaderText( Text::EmptyText );
	}

	void OnRaidEvent( AAS_RaidEventManager_v2 eventManager, ERaidEventFlag flag ) override
	{
		Super::OnRaidEvent( eventManager, flag );

		if ( flag == ERaidEventFlag::ATTACKER_RAID_AUTOENDING || flag == ERaidEventFlag::DEFENDER_RAID_AUTOENDING )
		{
			StartCountdownTimer( true );
			SetSubheaderText( GetLocalizedText( Localization::Raid, "raid_auto_ending" ) );

			{
				EPriorityMessageTemplate template = flag == ERaidEventFlag::ATTACKER_RAID_AUTOENDING ? EPriorityMessageTemplate::V2_ATTACK_DOME_REPAIR : EPriorityMessageTemplate::V2_DEFEND_DOME_REPAIR;
				UAS_PriorityMessageData data	  = GetTemplateMessageData( template );
				if ( IsValid( data ) )
				{
					data.header			= GetLocalizedText( Localization::Raid, "raid_breaker_down" );
					data.subheader		= GetLocalizedText( Localization::Raid, "raid_breaker_down_sub" );
					data.autoDissappear = false;
					AddPriorityMessage( data );
				}
			}
		}
	}

	private void OnRaidEnded( AAS_RaidEventManager_v2 eventManager ) override final
	{
		Super::OnRaidEnded( eventManager );

		// When a raid ends we want to avoid any auto updates and hold the raid ended messaging for a bit
		SetSubheaderText( GetLocalizedText( Localization::Raid, "raid_ended" ) );
		SetHeaderText( Text::EmptyText );
		OnRaidStartedOrEnded();

		CheckForActivePlant();
		CheckForCriticalAlert();
		ShowOrHideRaidingTeamRaidState( false );
		ShowOrHideRaidingTeamRespawns( false );
	}

	private void UpdateObjectivePriorityMessage()
	{
		if ( !IsValid( localPlayer ) )
			return;

		AAS_TeamStateManager_RaidMode teamState = GetTeamStateManager_RaidMode( localPlayer.GetTeam() );
		if ( IsValid( teamState ) )
		{
			EPriorityMessageTemplate priorityMessage = EPriorityMessageTemplate::_count;

			EObjectiveState objectiveState = teamState.GetRaidObjectiveState();
			switch ( objectiveState )
			{
				case EObjectiveState::DefensePhase:
					priorityMessage = EPriorityMessageTemplate::PREPARE_BASE_CENTER;
					break;
				case EObjectiveState::PickupShieldBreaker:
				case EObjectiveState::InterceptShieldBreaker:
					priorityMessage = EPriorityMessageTemplate::SHIELDBREAKER_CRAFTED;
					break;
				case EObjectiveState::ShieldBreakerCrafting:
					priorityMessage = EPriorityMessageTemplate::SHIELDBREAKER_CRAFTING;
					break;
				case EObjectiveState::ExploreAndGearUp:
				case EObjectiveState::Raiding:
				default:
					break;
			}

			if ( priorityMessage != EPriorityMessageTemplate::_count )
			{
				// Send out any valid messages
				AddTemplateMessage( priorityMessage );
			}
			else
			{
				UCL_PriorityMessageManager_v2 manager = PriorityMessage();
				if ( IsValid( manager ) )
				{
					manager.ClearActiveMessages( EPriorityMessageLevel::BASE );
				}
			}
		}
	}
}