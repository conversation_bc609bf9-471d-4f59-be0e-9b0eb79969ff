UCLASS( Abstract )
class UAS_PriorityMessageContainer : UNC_DisplayWidget
{
	const int PRIORITY_MESSAGE_MAX_OTHER_RIGHT_MSGS = 3;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	UOverlay priorityMessageContainerCenter;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	UVerticalBox priorityMessageContainerLeft;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	UVerticalBox priorityMessageContainerRight;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	UOverlay priorityMessageContainerWings;

	UPROPERTY( NotVisible, BlueprintHidden, Transient, Meta = ( BindWidgetAnim ) )
	private UWidgetAnimation raidStartedAnimation;
	
	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		// what I want to do in here is set a callback that updates whether the widget is on screen or not.
		// But I cannot seem to do that from inside this widget... which is annoying. I tried OnVisibilityChanged
		// but that only updates if this specific widget's visibility changes... not whether it's actually on screen.
		// instead I handle every instance that uses this widget which makes this widget far less powerful and prone to user error.
		// UAS_BuildingHUDWidget::On_VisibilityChanged

		// OnScreenChanged.AddUFunction( this, n"OnScreenChangedCallback" );
		ClientCallbacks().OnTeamScoreChanged.AddUFunction( this, n"OnTeamScoreChanged" );

		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			// TODO @jmccarty: Changes these to be called externally via events instead of being bound to raid context events
			scriptCallbacks.shared_OnRaidStarted.AddUFunction( this, n"OnRaidStarted" );
			scriptCallbacks.shared_OnRaidEnded.AddUFunction( this, n"OnRaidEnded" );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnTeamScoreChanged( int teamId, int newScore )
	{
		AAS_PlayerEntity localPlayer = Client_GetLocalASPawn();
		if ( !IsValid( localPlayer ) )
			return;

		if ( localPlayer.GetTeam() == teamId && newScore == 0 )
		{
			Hide();
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnRaidStarted( AAS_RaidEventManager_v2 eventManager )
	{
		PlayAnimationForward( raidStartedAnimation );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnRaidEnded( AAS_RaidEventManager_v2 eventManager )
	{
		PlayAnimationReverse( raidStartedAnimation, 2.0f );
	}
}
