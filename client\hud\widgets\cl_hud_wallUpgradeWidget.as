UCLASS( Abstract )
class UAS_PinnableWidget_WallUpgrade : UAS_PinnableWidget
{
	UPROPERTY( BindWidget )
	UAS_KeybindWidget actionKeybind;

	UPROPERTY( BindWidget )
	UAS_KeybindWidget altActionKeybind;

	UPROPERTY( BindWidget )
	UImage primaryIcon;

	UPROPERTY( BindWidget )
	UImage alternateIcon;

	UPROPERTY( BindWidget )
	UOverlay altUseWidgetPanel;

	UPROPERTY( BindWidget )
	UOverlay mainPanelOverlay;

	void SetPrimaryText( FText newText )
	{
		actionKeybind.SetActionText( newText );
		if ( newText.IsEmpty() )
		{
			SetWidgetVisibilitySafe( mainPanelOverlay, ESlateVisibility::Collapsed );
		}
		else
		{
			SetWidgetVisibilitySafe( mainPanelOverlay, ESlateVisibility::SelfHitTestInvisible );
		}
	}

	void SetPrimaryIcon( UObject newIcon )
	{
		if ( !IsValid( newIcon ) )
		{
			SetWidgetVisibilitySafe( primaryIcon, ESlateVisibility::Collapsed );
			return;
		}

		primaryIcon.SetBrushResourceObject( newIcon );
		SetWidgetVisibilitySafe( primaryIcon, ESlateVisibility::SelfHitTestInvisible );
	}

	void SetAltText( FText newText )
	{
		altActionKeybind.SetActionText( newText );
		if ( newText.IsEmpty() )
		{
			SetWidgetVisibilitySafe( altUseWidgetPanel, ESlateVisibility::Collapsed );
		}
		else
		{
			SetWidgetVisibilitySafe( altUseWidgetPanel, ESlateVisibility::SelfHitTestInvisible );
		}
	}

	void SetAlternateIcon( UObject newIcon )
	{
		if ( !IsValid( newIcon ) )
		{
			SetWidgetVisibilitySafe( alternateIcon, ESlateVisibility::Collapsed );
			return;
		}

		alternateIcon.SetBrushResourceObject( newIcon );
		SetWidgetVisibilitySafe( alternateIcon, ESlateVisibility::SelfHitTestInvisible );
	}
}