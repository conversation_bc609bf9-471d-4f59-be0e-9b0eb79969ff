UCLASS( Abstract )
class UAS_PriorityMessageWingWidget : UAS_PriorityMessageBaseWidget
{
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	private UImage BG_OffScreenL;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	private UImage BG_OffScreenR;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	private UImage BG_CenterL;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	private UImage BG_CenterR;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	private UImage BG_fadeL;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	private UImage BG_fadeR;

	private FLinearColor _BG_ColorMainMessage = PriorityMessageColor::OTHER_COLOR_MAIN_MSG;

	void Init( EPriorityMessageTheme Theme, UPanelWidget Container )
	{
		FPriorityMessageColorTheme ThemeColors = GetPriorityMessageThemeColors( Theme );
		Set_BGColorMainMessage( ThemeColors.MainBG );

		Container.AddChild( this );

		UOverlaySlot MySlot = Cast<UOverlaySlot>( this.Slot );

		MySlot.SetHorizontalAlignment( EHorizontalAlignment::HAlign_Fill );
		MySlot.SetVerticalAlignment( EVerticalAlignment::VAlign_Fill );

		Show();
	}

	void Set_BGColorMainMessage( FLinearColor newColor )
	{
		_BG_ColorMainMessage = newColor;

		BG_OffScreenL.SetColorAndOpacity( newColor );
		BG_OffScreenR.SetColorAndOpacity( newColor );
		BG_CenterL.SetColorAndOpacity( newColor );
		BG_CenterR.SetColorAndOpacity( newColor );
		BG_fadeL.SetColorAndOpacity( newColor );
		BG_fadeR.SetColorAndOpacity( newColor );
	}
}