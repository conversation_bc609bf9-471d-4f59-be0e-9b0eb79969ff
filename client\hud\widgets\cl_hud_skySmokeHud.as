UCLASS( Abstract )
class UAS_SkySmokeHudWidget : UNC_DisplayWidget
{
	AAS_SkySmokeBirdVehicle birdVehicle;

	UPROPERTY(NotEditable, BindWidget)
	UAS_SkySmokeTimer timer;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage ammoCounter;

	float32 duration;
	private UMaterialInstanceDynamic ammoCounterMaterial;

	UFUNCTION(BlueprintOverride)
	void Construct()
	{
		ammoCounterMaterial = ammoCounter.GetDynamicMaterial();
	}

	void InitVehicle( AAS_SkySmokeBirdVehicle inBird, float32 inDuration )
	{
		duration = inDuration;
		birdVehicle = inBird;
		birdVehicle.net_birdViewEndTime.OnReplicated().AddUFunction( this, n"OnEndTimeChanged" );
		birdVehicle.net_birdViewShotsLeft.OnReplicated().AddUFunction( this, n"OnShotsLeftChanged" );
		OnEndTimeChanged( 0, birdVehicle.net_birdViewEndTime );
		OnShotsLeftChanged( 0, birdVehicle.net_birdViewShotsLeft );
	}

	

	UFUNCTION()
	private void OnShotsLeftChanged( int oldValue, int newValue )
	{
		if ( IsValid( ammoCounterMaterial ) )
		{
			ammoCounterMaterial.SetScalarParameterValue( n"CurrentAmmo", newValue );
			ammoCounterMaterial.SetScalarParameterValue( n"MaxAmmo", SkySmokeConsts::BIRD_MAX_SHOTS );
		}
	}

	UFUNCTION(BlueprintOverride)
	void OnShowStart()
	{
		timer.SetVisibility( ESlateVisibility::Visible );
	}

	UFUNCTION()
	private void OnEndTimeChanged( int oldValue, int newValue )
	{
		
	}

	UFUNCTION(BlueprintOverride)
	void Tick(FGeometry MyGeometry, float InDeltaTime)
	{
		if ( !IsValid( birdVehicle ) )
		{
			timer.SetPercent( 0.0f );
			return;
		}

		int32 birdViewEndTime = birdVehicle.net_birdViewEndTime;

		int startTime = birdViewEndTime - TO_MILLISECONDS(duration);
		float fill = Math::GetMappedRangeValueClamped( FVector2D(startTime, birdViewEndTime), FVector2D(1.0f,0.0f), GetTimeMilliseconds() );
		timer.SetPercent( fill );
	}
}








UCLASS( Abstract )
class UAS_SkySmokeTimer : UNC_DisplayWidget
{
	UPROPERTY( NotVisible, BlueprintHidden, BindWidget )
	private UImage progress_left;

	UPROPERTY( NotVisible, BlueprintHidden, BindWidget )
	private UImage progress_right;

	private UMaterialInstanceDynamic progressMaterial_l;
	private UMaterialInstanceDynamic progressMaterial_r;

	UFUNCTION(BlueprintOverride)
	void Construct()
	{
		progressMaterial_l = CreateDynamicMaterialFromImageBrush( progress_left );
		if ( IsValid( progressMaterial_l ) )
		{
			progress_left.SetBrushFromMaterial( progressMaterial_l );
		}
		progressMaterial_r = CreateDynamicMaterialFromImageBrush( progress_right );
		if ( IsValid( progressMaterial_r ) )
		{
			progress_right.SetBrushFromMaterial( progressMaterial_r );
		}
		SetPercent( 0.0f );
	}

	void SetPercent( float percent )
	{
		if ( IsValid( progressMaterial_l ) )
		{
			float currentPercent = progressMaterial_l.GetScalarParameterValue( MaterialParameter::LERP_ALPHA );
			if ( !Math::IsNearlyEqual( currentPercent, percent ) )
			{
				progressMaterial_l.SetScalarParameterValue( MaterialParameter::LERP_ALPHA, percent );
			}
		}
		if ( IsValid( progressMaterial_r ) )
		{
			float currentPercent = progressMaterial_r.GetScalarParameterValue( MaterialParameter::LERP_ALPHA );
			if ( !Math::IsNearlyEqual( currentPercent, percent ) )
			{
				progressMaterial_r.SetScalarParameterValue( MaterialParameter::LERP_ALPHA, percent );
			}
		}
	}
}