UCLASS(Abstract)
class UAS_ReticleWidget_CorsairCharge : UAS_ReticleWidget
{
	UPROPERTY( Meta=(BindWidget) )
	UOverlay squareReticleContainer;

	UPROPERTY( Meta=(BindWidget) )
	USpacer squareReticleSpacer;

	UPROPERTY( Meta=(BindWidget) )
	UOverlay precisionReticleContainer;

	UPROPERTY( Transient, meta = ( BindWidgetAnim ) )
	UWidgetAnimation anim_precisionReticleFadeIn;

	UPROPERTY( Transient, meta = ( BindWidgetAnim ) )
	UWidgetAnimation anim_passiveCharge;
	
	UPROPERTY()
	const int spacerStartSize = 32;

	UPROPERTY()
	const int spacerEndSize = 48;

	UPROPERTY()
	const float rotateStartSpeed = 0;

	UPROPERTY()
	const float rotateEndSpeed = 1080.0;

	UPROPERTY()
	const float rotateSpeedRampExponent = 2.0;

	UPROPERTY()
	const float maxDecelerateSpin = 1080;

	UPROPERTY()
	const float precisionReticleFadeInFrac = 0.5;

	float displayFrac = 0.0;

	float oldChargeFrac = 0;
	bool wasDecelerating = false;

	float targetDecelerationAngle = 0;
	float cumulativeSpin = 0;

	float startingRotation = 0;

	UAS_WeaponContext_Corsair wpContext;
	
	void InitializeReticle(ANCWeapon weapon) override
	{
		Super::InitializeReticle(weapon);

		if ( !IsValid( weapon ) )
			return;

		ScriptCallbacks().RegisterSignalCallback( Signals::WEAPON_CONTEXT_WORKAROUND_CORSAIR, this, n"Signal_CorsairContextWorkaround" );

		startingRotation = squareReticleContainer.RenderTransformAngle;
	}

	UFUNCTION(NotBlueprintCallable)
	void Signal_CorsairContextWorkaround( FName signalName, UObject signalSource )
	{
		wpContext = Cast<UAS_WeaponContext_Corsair>( signalSource );
	}

	void ScriptReticleTick(ANCWeapon currentWeapon, float32 dt) override
	{
		Super::ScriptReticleTick(currentWeapon, dt);

		if ( GetVisibility() == ESlateVisibility::Hidden )
			return;
		
		float chargeFrac = currentWeapon.GetChargeFraction();
		bool isDecelerating = false;
		float remainingChargeTime = 0;
		if ( chargeFrac > 0 )
		{
			// Display frac finishes slightly earlier than charge frac
			float adjustedChargeFrac = Math::Min( chargeFrac / 0.95, 1.0f );
			// Going to the 1.5th power, rather than squaring, helps the lower values (sub 0.5) be more visible
			displayFrac = Math::Pow( adjustedChargeFrac, 1.5 );
			isDecelerating = chargeFrac < oldChargeFrac;

			if ( isDecelerating )
			{
				remainingChargeTime = currentWeapon.GetChargeTime() * chargeFrac;
			}
		}

		// Hold display frac at 1.0 while shooting
		else if ( currentWeapon.GetWeaponState() == EWeaponState::PrimaryAttack )
		{
			displayFrac = 1.0f;
		}

		// Interpolate display frac to 0, even though charge frac is already 0.
		else if ( chargeFrac == 0 && displayFrac > 0 )
		{
			isDecelerating = true;
			remainingChargeTime = currentWeapon.GetWeaponData().timeBetweenBursts * displayFrac;
			// Recede so that it's 0 before next shot is ready, to prevent snapping
			float recedeTime = currentWeapon.GetWeaponData().timeBetweenBursts;
			float recedeRate = 1 / recedeTime;
			displayFrac = Math::Max( 0, displayFrac - ( recedeRate * dt ) );
		}

		else
		{
			ResetReticle();
		}

		float curAngle = squareReticleContainer.GetRenderTransformAngle();

		if ( isDecelerating && cumulativeSpin > 0 )
		{
			if ( !wasDecelerating )
			{
				float angleIntervalToEndOn = 90 + startingRotation;
				targetDecelerationAngle = cumulativeSpin;
				float endOnRightAngleAdjustment = ( curAngle + targetDecelerationAngle ) % ( angleIntervalToEndOn );
				endOnRightAngleAdjustment = endOnRightAngleAdjustment > (angleIntervalToEndOn * 0.5) ? angleIntervalToEndOn - endOnRightAngleAdjustment : endOnRightAngleAdjustment * -1;
				targetDecelerationAngle = curAngle + endOnRightAngleAdjustment + targetDecelerationAngle;
			}

			float newAngle = Math::LerpStable( curAngle, targetDecelerationAngle, 1.0 - displayFrac );
			squareReticleContainer.SetRenderTransformAngle( newAngle );
			cumulativeSpin += newAngle - curAngle;
		}
		else
		{
			float spinRate = Math::GetMappedRangeValueClamped( FVector2D( 0, 1 ), FVector2D( rotateStartSpeed, rotateEndSpeed ), Math::Pow( displayFrac, rotateSpeedRampExponent ) );
			cumulativeSpin += spinRate * dt;
			squareReticleContainer.SetRenderTransformAngle( curAngle + cumulativeSpin );
		}

		// Rotate box based on display frac
		float spacerSize = Math::GetMappedRangeValueClamped( FVector2D( 0, 1 ), FVector2D( spacerStartSize, spacerEndSize ), displayFrac );
		squareReticleSpacer.SetSize( FVector2D( spacerSize, spacerSize ) );

		oldChargeFrac = chargeFrac;
		wasDecelerating = isDecelerating;

		// Get a smooth float val that's chasing the stair-stepped charge index frac
		// use that smooth fraction to index into the animation


		//Print(f"WP context valid? {IsValid( wpContext)}");

		// Weapon context is erratically becoming invalid. Early out here until it's fixed.
		return;

		// commenting this out to prevent warning
		// 
		// if ( IsValid( wpContext ) )
		// {
		// 	interpTarget = wpContext.GetCurrentChargeIndexFrac( currentWeapon );
		// 	interpVal = Math::FInterpTo( interpVal, interpTarget, dt, 1.5 );
		// 	PlayAnimation( anim_passiveCharge, interpVal, 1, EUMGSequencePlayMode::Forward, 0, false );
		// 	//Print(f"Passive charge anim frac: {interpVal}");

		// 	if ( !precisionReticleFadedIn && interpTarget >= precisionReticleFadeInFrac )
		// 	{
		// 		// Fade in
		// 		precisionReticleFadedIn = true;
		// 		float32 startAtTime = GetAnimationCurrentTime( anim_precisionReticleFadeIn );
		// 		PlayAnimation( anim_precisionReticleFadeIn, startAtTime, 1, EUMGSequencePlayMode::Forward );
		// 	}
		// 	else if ( precisionReticleFadedIn && interpTarget < precisionReticleFadeInFrac )
		// 	{
		// 		// Fade out
		// 		precisionReticleFadedIn = false;
		// 		float32 startAtTime = GetAnimationCurrentTime( anim_precisionReticleFadeIn );
		// 		PlayAnimation( anim_precisionReticleFadeIn, startAtTime, 1, EUMGSequencePlayMode::Reverse );
		// 	}
		// }

	}

	float interpTarget = 0;
	float interpVal = 0;
	bool precisionReticleFadedIn = false;

	void ResetReticle()
	{
		squareReticleContainer.SetRenderTransformAngle( startingRotation );
		squareReticleSpacer.SetSize( FVector2D( spacerStartSize, spacerStartSize ) );
		oldChargeFrac = 0;
		cumulativeSpin = 0;
		precisionReticleContainer.SetRenderOpacity( 0 );
	}
}