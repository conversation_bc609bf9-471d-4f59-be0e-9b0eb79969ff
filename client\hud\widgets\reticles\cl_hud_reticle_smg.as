
UCLASS(Abstract)
class UAS_Reticle_SMG : UAS_ReticleWidget
{
	bool isEngaged = false;
	float deployAnimTime = 0.3;
	float deployAnimPlaybackSpeed = 0;
	
	UPROPERTY( Transient, meta = ( BindWidgetAnim ) )
	UWidgetAnimation anim_stabilizerDeploy;
	
	void InitializeReticle(ANCWeapon weapon) override
	{
		Super::InitializeReticle(weapon);

		if ( !IsValid( weapon ) )
			return;

		deployAnimPlaybackSpeed = 1 / deployAnimTime;

		ScriptCallbacks().RegisterSignalCallback( Signals::WEAPON_UPDATE_RETICLE_CUSTOM, this, n"Signal_WeaponStabilityChanged" );
	}

	UFUNCTION(NotBlueprintCallable)
	void Signal_WeaponStabilityChanged( FName signalName, UObject signalSource )
	{
		UAS_WeaponContext_SMG wpContext = Cast<UAS_WeaponContext_SMG>( signalSource );
		if ( !IsValid( wpContext ) )
			return;

		if ( !ownerWeapon.WeaponOwner.IsADS() )
		{
			ReleaseStabilizer();
		}
		else// if ( wpContext.curMitigationFrac > 0 )
		{
			EngageStabilizer();
			//System::SetTimer( this, n"DelayedReleaseStabilizer", 0.25, false );
		}
	}

	UFUNCTION()
	void DelayedReleaseStabilizer()
	{
		ReleaseStabilizer();
	}

	void ReleaseStabilizer()
	{
		PlayDeployStabilizerAnim( false );
		System::ClearTimer( this, f"DelayedReleaseStabilizer" );
	}

	void EngageStabilizer()
	{
		PlayDeployStabilizerAnim( true );
	}

	void PlayDeployStabilizerAnim( bool shouldEngage )
	{
		if ( isEngaged == shouldEngage )
			return;
		
		float startTime = 0;
		EUMGSequencePlayMode playMode = shouldEngage ? EUMGSequencePlayMode::Forward : EUMGSequencePlayMode::Reverse;
		if ( IsAnimationPlaying( anim_stabilizerDeploy ) )
		{
			startTime = GetAnimationCurrentTime( anim_stabilizerDeploy );
		}

		PlayAnimation( anim_stabilizerDeploy, startTime, 1, playMode, deployAnimPlaybackSpeed );
		isEngaged = shouldEngage;
	}

	UFUNCTION(BlueprintOverride)
	void Destruct()
	{
		Super::Destruct();
		ScriptCallbacks().UnRegisterSignalCallback( Signals::WEAPON_UPDATE_RETICLE_CUSTOM, this, n"Signal_WeaponStabilityChanged" );
	}
}