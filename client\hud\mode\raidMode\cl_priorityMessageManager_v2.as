enum EPriorityMessagePlacement
{
	CENTER,
	EDGE,

	_count
}

UCL_PriorityMessageManager_v2
PriorityMessage()
{
	UCL_PriorityMessageManager_v2 result;

	result = Cast<UCL_PriorityMessageManager_v2>( GetClientSystem( UCL_PriorityMessageManager_v2::StaticClass() ) );

	return result;
}

void Cl_InitPriorityMessages( ANC_HUD hud )
{
	UNCRemoteScriptCommands::RegisterServerCommandGlobal( hud, n"ServerCommand_AddTemplateMessage", n"SC_AddTemplateMessage" );
}

UFUNCTION( NotBlueprintCallable )
void SC_AddTemplateMessage( TArray<FString> args )
{
	if ( args.Num() < 1 )
		return;

	EPriorityMessageTemplate messageTemplate = EPriorityMessageTemplate( args[0].ToInt() );
	AddTemplateMessage( messageTemplate );
}

UCLASS( Abstract )
class UCL_PriorityMessageManager_v2 : UNCGameplaySystem_Client
{
	UPROPERTY( EditDefaultsOnly, BlueprintHidden, Category = "Widget Classes" )
	TSubclassOf<UAS_PriorityMessageBaseWidget> priorityWidgetRaidReportClass;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden, Category = "Widget Classes" )
	TSubclassOf<UAS_PriorityMessageBaseWidget> priorityMessageRaidWidgetClass;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden, Category = "Widget Classes" )
	TSubclassOf<UAS_PriorityMessageContainer> priorityMessageContainerClass;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden, Category = "Audio Data" )
	UNCAudioAsset centerDefaultLeft;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden, Category = "Audio Data" )
	UNCAudioAsset centerDefaultRight;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden, Category = "Audio Data" )
	UNCAudioAsset edgeDefaultLeft;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden, Category = "Audio Data" )
	UNCAudioAsset edgeDefaultRight;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden, Category = "Audio Data" )
	UNCAudioAsset centerDefend;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden, Category = "Audio Data" )
	UNCAudioAsset centerAttack;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden, Category = "Audio Data" )
	UNCAudioAsset centerDefenderWin;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden, Category = "Audio Data" )
	UNCAudioAsset centerDefenderLose;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden, Category = "Audio Data" )
	UNCAudioAsset centerAttackerWin;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden, Category = "Audio Data" )
	UNCAudioAsset centerAttackerLose;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden, Category = "Audio Data" )
	UNCAudioAsset edgeTeamWipe;

	UAS_PriorityMessageContainer priorityMessageContainer;

	FNCCoroutineSignal onMessageCreatedSignal;
	FNCCoroutineSignal onMessageClearedSignal;

	private TMap<EPML_DefendIdx, FRaidMessageMap_v2> defendMessageMap;
	private TMap<EPML_AttackIdx, FRaidMessageMap_v2> attackMessageMap;
	private TMap<EPriorityMessageLevel, int> savedCombinedRaidFlags; // to test against to see what's new
	private bool __DevTest = false;
	private AAS_HUD hud;
	private int lastSoundPlayTimeMS;

	private const float COUNTDOWN_DELAY_TIMER = 0.3;
	private const float RAID_ENERGY_GAPSIZE = 0.04;
	private const float PRIORITY_MESSAGE_DEFAULT_UPDATE_TICK = 0.5;
	private const float PRIORITY_MESSAGE_SHOULD_TELEPORT_DIST = 20000;
	private const float PRIORITY_MESSAGE_ATTACK_BREACHER_DIST = 6500;
	private const int PRIORITY_MESSAGE_MAX_OTHER_RIGHT_MSGS = 3;
	private const float PRIORITY_MESSAGE_AUDIO_NOPLAY_BUFFER = 0.5;

	UFUNCTION( BlueprintOverride )
	void Initialize()
	{
		if ( GetCvarBool("ScriptDebug.NewRaidMessaging") )
		{
			return;
		}

		hud = Cast<AAS_HUD>( HUD );

		savedCombinedRaidFlags.Add( EPriorityMessageLevel::DEFENDER, 0 );
		savedCombinedRaidFlags.Add( EPriorityMessageLevel::ATTACKER, 0 );

		/************************************************************\

			** THE ORDER MATTERS **
			- Each bucket is an ordered list of flag priority.
			- As soon as a flag is true, the template is picked.
			- using EPriorityListTemplate::_count does not create
			  a ERaidEventFlag to test for that template type ( types
			  are edge and center )
			- last position is always default, so doesn't matter
			  what the map key is. ( EDGE TEMPLATES ONLY )

		\************************************************************/

		////////////////////////////
		//	DEFEND PRIMARY
		FRaidMessageMap_v2 mapDefenderPrimary;
		if ( !GameModeDefaults().GameModeRules_CustomRaidEndMessaging )
		{
			mapDefenderPrimary.Add( ERaidEventFlag::DEFENDER_WIN, EPriorityListTemplate::_count, EPriorityMessageTemplate::V2_DEFEND_WIN );
			mapDefenderPrimary.Add( ERaidEventFlag::DEFENDER_LOSE, EPriorityListTemplate::_count, EPriorityMessageTemplate::V2_DEFEND_LOSE );
		}

		mapDefenderPrimary.Add( ERaidEventFlag::DEFENDER_RAID_STARTED, EPriorityListTemplate::DEFENDER_STOP_RAID, EPriorityMessageTemplate::V2_DEFEND_RAID_STARTED ); // last position is always default, doesn't matter what the map key is
		defendMessageMap.Add( EPML_DefendIdx::PRIMARY, mapDefenderPrimary );

		////////////////////////////
		//	DEFEND BREACHER
		FRaidMessageMap_v2 mapDefendBreacher;
		mapDefendBreacher.Add( ERaidEventFlag::DEFENDER_DOME_BREACHED, EPriorityListTemplate::DEFENDER_RAID_ENERGY, EPriorityMessageTemplate::V2_DEFEND_DOME_BREACHED );
		mapDefendBreacher.Add( ERaidEventFlag::DEFENDER_RAID_STARTED, EPriorityListTemplate::EMPTY ); // last position is always default, doesn't matter what the map key is
		defendMessageMap.Add( EPML_DefendIdx::BREACHER, mapDefendBreacher );

		////////////////////////////
		//	DEFEND REVEAL
		FRaidMessageMap_v2 mapDefenderReveal;
		mapDefenderReveal.Add( ERaidEventFlag::DEFENDER_ENEMY_REVEALING, EPriorityListTemplate::DEFENDER_ENEMY_REVEAL );
		mapDefenderReveal.Add( ERaidEventFlag::DEFENDER_RAID_STARTED, EPriorityListTemplate::DEFENDER_DOME_REPAIR_EMPTY ); // last position is always default, doesn't matter what the map key is
		defendMessageMap.Add( EPML_DefendIdx::REVEAL, mapDefenderReveal );

		////////////////////////////
		//	DEFEND ENEMIES
		FRaidMessageMap_v2 mapDefendEnemies;
		mapDefendEnemies.Add( ERaidEventFlag::DEFENDER_RAID_STARTED, EPriorityListTemplate::DEFENDER_ENEMIES_IN_BASE ); // last position is always default, doesn't matter what the map key is
		defendMessageMap.Add( EPML_DefendIdx::ENEMIES, mapDefendEnemies );

		////////////////////////////
		//	UPDATE RESPAWN LIVES
		FRaidMessageMap_v2 mapDefenderRespawns;
		mapDefenderRespawns.Add( ERaidEventFlag::DEFENDER_RAID_STARTED, EPriorityListTemplate::DEFENDER_RESPAWNS_LEFT ); // last position is always default, doesn't matter what the map key is
		defendMessageMap.Add( EPML_DefendIdx::RESPAWN, mapDefenderRespawns );

		FRaidMessageMap_v2 mapAttackerRespawns;
		mapAttackerRespawns.Add( ERaidEventFlag::ATTACKER_RAID_STARTED, EPriorityListTemplate::ATTACKER_RESPAWNS_LEFT );
		attackMessageMap.Add( EPML_AttackIdx::RESPAWN, mapAttackerRespawns );

		////////////////////////////
		//	ATTACK PRIMARY
		FRaidMessageMap_v2 mapAttackPrimary;
		if ( !GameModeDefaults().GameModeRules_CustomRaidEndMessaging )
		{
			mapAttackPrimary.Add( ERaidEventFlag::ATTACKER_WIN, EPriorityListTemplate::_count, EPriorityMessageTemplate::V2_ATTACK_WIN );
			mapAttackPrimary.Add( ERaidEventFlag::ATTACKER_LOSE, EPriorityListTemplate::_count, EPriorityMessageTemplate::V2_ATTACK_LOSE );
		}
		mapAttackPrimary.Add( ERaidEventFlag::ATTACKER_RAID_STARTED, EPriorityListTemplate::ATTACKER_EMPTY, EPriorityMessageTemplate::V2_ATTACK_RAID_STARTED ); // last position is always default, doesn't matter what the map key is
		attackMessageMap.Add( EPML_AttackIdx::PRIMARY, mapAttackPrimary );

		////////////////////////////
		//	ATTACK BREACHER
		FRaidMessageMap_v2 mapAttackBreacher;
		mapAttackBreacher.Add( ERaidEventFlag::ATTACKER_BREACHER_PENETRATED, EPriorityListTemplate::ATTACKER_RAID_ENERGY, EPriorityMessageTemplate::V2_ATTACK_DOME_BREACHED );
		mapAttackBreacher.Add( ERaidEventFlag::ATTACKER_RAID_STARTED, EPriorityListTemplate::EMPTY ); // last position is always default, doesn't matter what the map key is
		attackMessageMap.Add( EPML_AttackIdx::BREACHER, mapAttackBreacher );

		////////////////////////////
		//	ATTACKER REVEAL
		FRaidMessageMap_v2 mapAttackerReveal;
		mapAttackerReveal.Add( ERaidEventFlag::ATTACKER_ENEMY_IS_REVEALED, EPriorityListTemplate::ATTACKER_ENEMY_IS_REVEALED );
		mapAttackerReveal.Add( ERaidEventFlag::ATTACKER_ENEMY_REVEALING, EPriorityListTemplate::ATTACKER_ENEMY_REVEAL );
		mapAttackerReveal.Add( ERaidEventFlag::ATTACKER_RAID_STARTED, EPriorityListTemplate::ATTACKER_DOME_REPAIR_EMPTY ); // last position is always default, doesn't matter what the map key is
		attackMessageMap.Add( EPML_AttackIdx::REVEAL, mapAttackerReveal );

		CreateContainer();
	}

	void CreateContainer()
	{
		ScriptAssert( priorityMessageContainerClass != nullptr, "priorityMessageContainerClass is null" );
		priorityMessageContainer = Cast<UAS_PriorityMessageContainer>( WidgetBlueprint::CreateWidget( priorityMessageContainerClass, HUD.OwningPlayerController ) );
		priorityMessageContainer.AddToViewport( GameConst::ZORDER_OVER_HUD );
	}

	/****************************************************************\

	██████   █████  ██ ██████      ███████ ██    ██ ███████ ███    ██ ████████
	██   ██ ██   ██ ██ ██   ██     ██      ██    ██ ██      ████   ██    ██
	██████  ███████ ██ ██   ██     █████   ██    ██ █████   ██ ██  ██    ██
	██   ██ ██   ██ ██ ██   ██     ██       ██  ██  ██      ██  ██ ██    ██
	██   ██ ██   ██ ██ ██████      ███████   ████   ███████ ██   ████    ██

	\****************************************************************/
	void UpdateRaidMessaging( int raidFlags )
	{
		// CENTER MESSAGES
		UpdateRaidMessagingPerCenterPriority( raidFlags, EPriorityMessageLevel::ATTACKER );
		UpdateRaidMessagingPerCenterPriority( raidFlags, EPriorityMessageLevel::DEFENDER );

		Signal( Signals::UPDATE_RAID_MESSAGING );
	}

	private bool InActiveRaidByPriorityLevel( EPriorityMessageLevel priorityLevel )
	{
		bool result = false;

		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( IsValid( player ) )
		{
			int team = player.GetTeam();

			switch ( priorityLevel )
			{
				case EPriorityMessageLevel::DEFENDER:
					result = IsTeamBeingRaided( team );
					break;
				case EPriorityMessageLevel::ATTACKER:
					result = IsTeamRaiding( team );
					break;
				default:
					ScriptAssert( false, f"InActiveRaidByPriorityLevel didn't handle priority level: {priorityLevel}" );
					result = false;
			}
		}

		return result;
	}

	private TMap<int, UAS_PriorityMessageListData> GetListEntriesFromPriorityLevel( EPriorityMessageLevel priorityLevel, int raidFlags )
	{
		TMap<int, UAS_PriorityMessageListData> listEntries;
		switch ( priorityLevel )
		{
			case EPriorityMessageLevel::DEFENDER:
			{
				for ( int i = 0; i < int( EPML_DefendIdx::_count ); i++ )
				{
					EPriorityListTemplate template	 = GetHighestPriorityTemplateForListIndex( raidFlags, defendMessageMap[EPML_DefendIdx( i )] );
					UAS_PriorityMessageListData data = GetTemplateMessageListData( template );
					if ( IsValid( data ) )
					{
						listEntries.Add( i, data );
					}
				}
			}
			break;

			case EPriorityMessageLevel::ATTACKER:
			{
				for ( int i = 0; i < int( EPML_AttackIdx::_count ); i++ )
				{
					EPriorityListTemplate template	 = GetHighestPriorityTemplateForListIndex( raidFlags, attackMessageMap[EPML_AttackIdx( i )] );
					UAS_PriorityMessageListData data = GetTemplateMessageListData( template );
					if ( IsValid( data ) )
					{
						listEntries.Add( i, data );
					}
				}
			}
			break;

			default:
				ScriptAssert( false, f"GetListEntriesFromPriorityLevel didn't handle priority level: {priorityLevel}" );
				break;
		}

		return listEntries;
	}

	private EPriorityListTemplate GetHighestPriorityTemplateForListIndex( int raidFlags, FRaidMessageMap_v2 templateMap )
	{
		EPriorityListTemplate result = EPriorityListTemplate::EMPTY;

		int index = 0;
		for ( TMapConstIterator<ERaidEventFlag, EPriorityListTemplate> map : templateMap.edgeTemplateMap )
		{
			ERaidEventFlag eventFlag		   = map.GetKey();
			EPriorityListTemplate listTemplate = map.GetValue();
			index++;

			// last index, eventFlag is irrelivant... this is the default
			if ( index == templateMap.edgeTemplateMap.Num() || Bitflags::HasEnumFlag( raidFlags, eventFlag ) )
			{
				result = listTemplate;
			}
		}

		ScriptAssert( result != EPriorityListTemplate::EMPTY, "GetHighestPriorityTemplateForListIndex got to unreachable code: unexpected results may follow" );
		return result;
	}

	private void UpdateRaidMessagingPerCenterPriority( int raidFlags, EPriorityMessageLevel priorityLevel )
	{
		int newAddedFlags					  = GetNewAddedFlagsFromPriorityLevel( priorityLevel, raidFlags );
		savedCombinedRaidFlags[priorityLevel] = raidFlags;

		EPriorityMessageTemplate centerTemplate = GetHighestPriorityCenterTemplateFromPriorityLevel( priorityLevel, newAddedFlags );

		if ( centerTemplate != EPriorityMessageTemplate::_count )
		{
			AddTemplateMessage( centerTemplate );
		}
	}

	private EPriorityMessageTemplate GetHighestPriorityCenterTemplateFromPriorityLevel( EPriorityMessageLevel priorityLevel, int raidFlags )
	{
		switch ( priorityLevel )
		{
			case EPriorityMessageLevel::DEFENDER:
			{
				for ( int i = 0; i < int( EPML_DefendIdx::_count ); i++ )
				{
					EPriorityMessageTemplate template = GetHighestPriorityCenterTemplateFromMessageMap( raidFlags, defendMessageMap[EPML_DefendIdx( i )] );
					if ( template != EPriorityMessageTemplate::_count )
						return template;
				}
			}
			break;

			case EPriorityMessageLevel::ATTACKER:
			{
				for ( int i = 0; i < int( EPML_AttackIdx::_count ); i++ )
				{
					EPriorityMessageTemplate template = GetHighestPriorityCenterTemplateFromMessageMap( raidFlags, attackMessageMap[EPML_AttackIdx( i )] );
					if ( template != EPriorityMessageTemplate::_count )
						return template;
				}
			}
			break;

			default:
				ScriptAssert( false, f"GetHighestPriorityCenterTemplateFromRaidFlags didn't handle priority level: {priorityLevel}" );
				break;
		}

		return EPriorityMessageTemplate::_count;
	}

	private EPriorityMessageTemplate GetHighestPriorityCenterTemplateFromMessageMap( int raidFlags, FRaidMessageMap_v2 messageMap )
	{
		EPriorityMessageTemplate result = EPriorityMessageTemplate::_count;

		for ( TMapConstIterator<ERaidEventFlag, EPriorityMessageTemplate> map : messageMap.centerTemplateMap )
		{
			ERaidEventFlag eventFlag				= map.GetKey();
			EPriorityMessageTemplate centerTemplate = map.GetValue();

			if ( Bitflags::HasEnumFlag( raidFlags, eventFlag ) )
			{
				result = centerTemplate;
				break;
			}
		}

		return result;
	}

	/****************************************************************\

	 █████  ██      ████████ ███████ ██████  ███    ██  █████  ████████ ███████ ███████
	██   ██ ██         ██    ██      ██   ██ ████   ██ ██   ██    ██    ██      ██
	███████ ██         ██    █████   ██████  ██ ██  ██ ███████    ██    █████   ███████
	██   ██ ██         ██    ██      ██   ██ ██  ██ ██ ██   ██    ██    ██           ██
	██   ██ ███████    ██    ███████ ██   ██ ██   ████ ██   ██    ██    ███████ ███████

	\****************************************************************/
	UFUNCTION( NotBlueprintCallable )
	void AlternateEval_BreacherPlantedDefend( UAS_PriorityMessageListData listData, int raidFlags, bool& eval )
	{
		eval = false;

		// is a breacher planted?
		if ( !Bitflags::HasEnumFlag( raidFlags, ERaidEventFlag::DEFENDER_BREACHER_PLANTED ) )
			return;

		//	if there are no bombs, and the dome is not broken, do a special priority message
		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( IsValid( player ) )
		{
			int defenderTeam		= player.GetTeam();
			AAS_RaidDomeShield dome = GetRaidDomeShieldForTeam( defenderTeam );
			if ( IsValid( dome ) )
			{
				bool domeBroken = IsValid( dome ) && dome.IsBroken();
				bool bombInPlay = GetAllRaidBombsForDefenders( defenderTeam ).Num() > 0 || GetAllObjectsHavingBombPlantedOnDefendingTeam( defenderTeam ).Num() > 0;

				eval = !bombInPlay && !domeBroken;
			}
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void AlternateEval_BreacherPlantedAttack( UAS_PriorityMessageListData listData, int raidFlags, bool& eval )
	{
		eval = false;

		// no significant primary message
		EPriorityListTemplate primaryTemplate = GetHighestPriorityTemplateForListIndex( raidFlags, attackMessageMap[EPML_AttackIdx::PRIMARY] );
		if ( CanLocalPlayerSeekAnyVault() || primaryTemplate != EPriorityListTemplate::ATTACKER_EMPTY )
			return;

		eval = true;
	}

	UFUNCTION( NotBlueprintCallable )
	void AlternateEval_AttackEmpty( UAS_PriorityMessageListData listData, int raidFlags, bool& eval )
	{
		// no significant primary message
		EPriorityListTemplate primaryTemplate = GetHighestPriorityTemplateForListIndex( raidFlags, attackMessageMap[EPML_AttackIdx::PRIMARY] );
		if ( primaryTemplate != EPriorityListTemplate::ATTACKER_EMPTY )
		{
			eval = false;
			return;
		}

		bool canSeekVault = CanLocalPlayerSeekAnyVault();

		// no significant breacher message
		EPriorityListTemplate breacherTemplate = GetHighestPriorityTemplateForListIndex( raidFlags, attackMessageMap[EPML_AttackIdx::BREACHER] );
		if ( breacherTemplate != EPriorityListTemplate::EMPTY && !canSeekVault )
		{
			eval = false;
			return;
		}

		eval = true;
	}

	UFUNCTION( NotBlueprintCallable )
	void PriorityEval_AttackBreacherDefault( UAS_PriorityMessageListData listData, int raidFlags, bool& eval )
	{
		// no significant primary message and seek the vault... then any breacher message is primary
		EPriorityListTemplate primaryTemplate = GetHighestPriorityTemplateForListIndex( raidFlags, attackMessageMap[EPML_AttackIdx::PRIMARY] );
		if ( primaryTemplate == EPriorityListTemplate::ATTACKER_EMPTY && !CanLocalPlayerSeekAnyVault() )
		{
			eval = true;
			return;
		}

		bool higherPriorityBreach  = false;
		bool higherPriorityPrimary = Bitflags::HasEnumFlag( raidFlags, ERaidEventFlag::ATTACKER_BOMB_BEING_DEFUSED );

		eval = higherPriorityBreach && !higherPriorityPrimary;
	}

	UFUNCTION( NotBlueprintCallable )
	void AlternateEval_DomeRepair_Defender( UAS_PriorityMessageListData listData, int raidFlags, bool& eval )
	{
		AAS_RaidDomeShield dome = GetRaidDomeShieldByPriorityLevel( EPriorityMessageLevel::DEFENDER );
		eval					= IsValid( dome ) && dome.IsRepairing();
	}

	UFUNCTION( NotBlueprintCallable )
	void AlternateEval_DomeRepair_Attacker( UAS_PriorityMessageListData listData, int raidFlags, bool& eval )
	{
		AAS_RaidDomeShield dome = GetRaidDomeShieldByPriorityLevel( EPriorityMessageLevel::ATTACKER );
		eval					= IsValid( dome ) && dome.IsRepairing();
	}

	/****************************************************************\

	 █████  ██████  ██████      ███    ███ ███████ ███████ ███████  █████   ██████  ███████
	██   ██ ██   ██ ██   ██     ████  ████ ██      ██      ██      ██   ██ ██       ██
	███████ ██   ██ ██   ██     ██ ████ ██ █████   ███████ ███████ ███████ ██   ███ █████
	██   ██ ██   ██ ██   ██     ██  ██  ██ ██           ██      ██ ██   ██ ██    ██ ██
	██   ██ ██████  ██████      ██      ██ ███████ ███████ ███████ ██   ██  ██████  ███████

	\****************************************************************/
	private UAS_PriorityMessageBaseWidget AddOrUpdateMessage( UAS_PriorityMessageData data )
	{
		UAS_PriorityMessageBaseWidget message;

		if ( IsValid( data ) )
		{
			TArray<UAS_PriorityMessageBaseWidget> oldMessages = GetActiveMatchingEdgeMessages( data.messageTemplate );
			ScriptAssert( oldMessages.Num() <= 1, "AddOrUpdateMessage found more than 1 matching edge message" );

			if ( oldMessages.Num() == 0 )
				return AddMessage( data );

			message								   = oldMessages[0];
			UAS_PriorityMessageWidget_v2 messagev2 = Cast<UAS_PriorityMessageWidget_v2>( message );
			if ( IsValid( messagev2 ) )
			{
				for ( TMapIterator<int, UAS_PriorityMessageListData> listDataEntry : data.v2_ListEntries )
				{
					UAS_PriorityMessageListData listDataItem = listDataEntry.GetValue();
					if ( IsValid( listDataItem ) )
					{
						bool setPriority = listDataItem.SetPriority;
						int index		 = int( listDataEntry.GetKey() );
						messagev2.UpdateListItemAtIndex( index, listDataItem, setPriority );
					}
				}

				message = messagev2;
			}
		}

		return message;
	}

	/****************************************************************\

	██    ██ ████████ ██ ██      ██ ████████ ██    ██
	██    ██    ██    ██ ██      ██    ██     ██  ██
	██    ██    ██    ██ ██      ██    ██      ████
	██    ██    ██    ██ ██      ██    ██       ██
	 ██████     ██    ██ ███████ ██    ██       ██

	\****************************************************************/
	private TArray<AAS_RaidEventManager_v2> GetAllRaidEventManagersFromPriorityLevel_v2( EPriorityMessageLevel priorityLevel )
	{
		TArray<AAS_RaidEventManager_v2> results;

		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( IsValid( player ) )
		{
			switch ( priorityLevel )
			{
				case EPriorityMessageLevel::DEFENDER:
					results = GetAllDefendingRaidEventManagersForTeam_v2( player.GetTeam() );
					break;
				case EPriorityMessageLevel::ATTACKER:
					results = GetAllAttackingRaidEventManagersForTeam_v2( player.GetTeam() );
					break;
				default:
					break;
			}
		}

		return results;
	}

	private TArray<AAS_RaidBomb> GetAllBombsFromPriorityLevel( EPriorityMessageLevel priorityLevel )
	{
		TArray<AAS_RaidBomb> results;

		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( IsValid( player ) )
		{
			switch ( priorityLevel )
			{
				case EPriorityMessageLevel::DEFENDER:
					results = GetAllRaidBombsForDefenders( player.GetTeam() );
					break;
				case EPriorityMessageLevel::ATTACKER:
					results = GetAllRaidBombsForAttackers( player.GetTeam() );
					break;
				default:
					break;
			}
		}

		return results;
	}

	private TArray<UAS_RaidBombInterfaceComponent> GetAllObjectsBeingBombPlantedFromPriorityLevel( EPriorityMessageLevel priorityLevel )
	{
		TArray<UAS_RaidBombInterfaceComponent> results;

		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( IsValid( player ) )
		{
			switch ( priorityLevel )
			{
				case EPriorityMessageLevel::DEFENDER:
					results = GetAllObjectsHavingBombPlantedOnDefendingTeam( player.GetTeam() );
					break;
				case EPriorityMessageLevel::ATTACKER:
					results = GetAllObjectsBeingBombPlantedByAttackingTeam( player.GetTeam() );
					break;
				default:
					break;
			}
		}

		return results;
	}

	private TArray<AAS_ShieldBreaker> GetAllBreachersFromPriorityLevel( EPriorityMessageLevel priorityLevel )
	{
		TArray<AAS_ShieldBreaker> results;

		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( IsValid( player ) )
		{
			switch ( priorityLevel )
			{
				case EPriorityMessageLevel::DEFENDER:
					results = GetAllBreachersTargetingDefendingTeam( player.GetTeam() );
					break;
				case EPriorityMessageLevel::ATTACKER:
				{
					AAS_ShieldBreaker breacher = GetBreacherForTeam( player.GetTeam() );
					if ( IsValid( breacher ) )
					{
						results.Add( breacher );
					}
					break;
				}
				default:
					break;
			}
		}

		return results;
	}

	private int GetNewAddedFlagsFromPriorityLevel( EPriorityMessageLevel priorityLevel, int raidFlags )
	{
		return savedCombinedRaidFlags.Contains( priorityLevel ) ? Bitflags::GetOnlyAddedFlags( savedCombinedRaidFlags[priorityLevel], raidFlags ) : 0;
	}

	bool CanLocalPlayerSeekAnyVault()
	{
		bool result = false;

		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( IsValid( player ) )
		{
			TArray<AAS_RaidEventManager_v2> allManagers = GetAllAttackingRaidEventManagersForTeam_v2( player.GetTeam() );
			for ( AAS_RaidEventManager_v2 manager : allManagers )
			{
				if ( IsValid( manager ) )
				{
					AAS_RaidDomeShield dome = GetRaidDomeShieldForTeam( manager.GetDefenderTeam() );
					if ( IsValid( dome ) && CanLocalPlayerSeekVaultForDome( dome ) )
					{
						result = true;
						break;
					}
				}
			}
		}

		return result;
	}

	bool CanLocalPlayerSeekVaultForDome( AAS_RaidDomeShield dome )
	{
		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( !IsValid( player ) )
			return false;

		// in the dome?
		if ( Distance( player.GetActorLocation(), dome.GetActorLocation() ) < dome.GetWorldPlayerDetectionTriggerRadius() )
			return true;

		// has a breacher?
		AAS_ShieldBreaker breacher = GetBreacherForTeam( player.GetTeam() );
		if ( IsValid( breacher ) )
			return true;

		// has a breach hole made by our team in range
		if ( dome.HasBreachLocationByAttackingTeamInRange( player ) )
			return true;

		// has any spawn points in dome
		if ( dome.GetAllAttackerRespawnBeaconsInsideDome().Num() > 0 )
			return true;

		return false;
	}

	private bool CanLocalPlayerFixVault()
	{
		return false;
	}

	/****************************************************************\

	 ██████  █████  ██      ██      ██████   █████   ██████ ██   ██ ███████
	██      ██   ██ ██      ██      ██   ██ ██   ██ ██      ██  ██  ██
	██      ███████ ██      ██      ██████  ███████ ██      █████   ███████
	██      ██   ██ ██      ██      ██   ██ ██   ██ ██      ██  ██       ██
	 ██████ ██   ██ ███████ ███████ ██████  ██   ██  ██████ ██   ██ ███████

	\****************************************************************/

	// TODO @jmccarty: I stopped cleaning up at this point 3/26

	UFUNCTION( NotBlueprintCallable )
	void OnCreate_BombCountdown_v2( UAS_PriorityMessageListItem newListItem )
	{
		EPriorityMessageLevel priorityLevel = newListItem.parentMessage.messageData.priorityLevel;
		TArray<AAS_RaidBomb> allBombs		= GetAllBombsFromPriorityLevel( priorityLevel );
		AAS_RaidBomb bomb					= GetHighestPriorityBombByPlantTime( allBombs );
		if ( !IsValid( bomb ) )
		{
			AAS_PlayerEntity player = Client_GetLocalASPawn();
			allBombs				= GetAllRaidBombsFor3rdPartyTeam( player.GetTeam() );
			bomb					= GetHighestPriorityBombByPlantTime( allBombs );
			if ( !IsValid( bomb ) )
				return;
		}

		float delay = 0; // COUNTDOWN_DELAY_TIMER;
		if ( bomb.IsBeingDefused() )
			newListItem.StopCountdown();
		else
			newListItem.StartCountdown_ByEndTime( bomb.GetExplodeTimeMS(), delay );
	}

	UFUNCTION( NotBlueprintCallable )
	void OnCreate_BombDefusing_v2( UAS_PriorityMessageListItem newListItem )
	{
		EPriorityMessageLevel priorityLevel = newListItem.parentMessage.messageData.priorityLevel;
		TArray<AAS_RaidBomb> allBombs		= GetAllBombsFromPriorityLevel( priorityLevel );
		AAS_RaidBomb bomb					= GetHighestPriorityBombByDefuseTime( allBombs );
		if ( !IsValid( bomb ) )
		{
			// try 3rd party
			OnCreate_BombDefusing3rdParty_v2( newListItem );
			return;
		}

		newListItem.StartCountdown_ByDuration( bomb.GetDefuseStartTimeMS(), bomb.GetDefuseTime(), 0 );
	}

	UFUNCTION( NotBlueprintCallable )
	void OnCreateProgressBar_BombDefusing( UAS_PriorityMessageListItem newListItem )
	{
		EPriorityMessageLevel priorityLevel = newListItem.parentMessage.messageData.priorityLevel;
		TArray<AAS_RaidBomb> allBombs		= GetAllBombsFromPriorityLevel( priorityLevel );
		AAS_RaidBomb bomb					= GetHighestPriorityBombByDefuseTime( allBombs );
		if ( !IsValid( bomb ) )
		{
			// try 3rd party
			OnCreateProgressBar_BombDefusing3rdParty( newListItem );
			return;
		}

		newListItem.StartProgressbar_ByDuration( bomb.GetDefuseStartTimeMS(), bomb.GetDefuseTime() );
	}

	UFUNCTION( NotBlueprintCallable )
	void OnCreate_BombPlanting_v2( UAS_PriorityMessageListItem newListItem )
	{
		EPriorityMessageLevel priorityLevel				 = newListItem.parentMessage.messageData.priorityLevel;
		TArray<UAS_RaidBombInterfaceComponent> allVaults = GetAllObjectsBeingBombPlantedFromPriorityLevel( priorityLevel );
		UAS_RaidBombInterfaceComponent bombInterface	 = GetHighestPriorityVaultByPlantingTime( allVaults );
		if ( !IsValid( bombInterface ) )
			return;

		newListItem.StartCountdown_ByDuration( bombInterface.GetBombPlantStartTimeMS(), bombInterface.BOMB_PLANT_TIME, COUNTDOWN_DELAY_TIMER );
	}

	UFUNCTION( NotBlueprintCallable )
	void OnCreate_BombCountdown3rdParty_v2( UAS_PriorityMessageListItem newListItem )
	{
		AAS_PlayerEntity player		  = Client_GetLocalASPawn();
		TArray<AAS_RaidBomb> allBombs = GetAllRaidBombsFor3rdPartyTeam( player.GetTeam() );
		AAS_RaidBomb bomb			  = GetHighestPriorityBombByPlantTime( allBombs );
		if ( !IsValid( bomb ) )
			return;

		if ( bomb.IsBeingDefused() )
			newListItem.StopCountdown();
		else
			newListItem.StartCountdown_ByEndTime( bomb.GetExplodeTimeMS(), COUNTDOWN_DELAY_TIMER );
	}

	UFUNCTION( NotBlueprintCallable )
	void OnCreate_BombDefusing3rdParty_v2( UAS_PriorityMessageListItem newListItem )
	{
		AAS_PlayerEntity player		  = Client_GetLocalASPawn();
		TArray<AAS_RaidBomb> allBombs = GetAllRaidBombsFor3rdPartyTeam( player.GetTeam() );
		AAS_RaidBomb bomb			  = GetHighestPriorityBombByDefuseTime( allBombs );
		if ( !IsValid( bomb ) )
			return;

		newListItem.StartCountdown_ByDuration( bomb.GetDefuseStartTimeMS(), bomb.GetDefuseTime(), COUNTDOWN_DELAY_TIMER );
	}

	UFUNCTION( NotBlueprintCallable )
	void OnCreateProgressBar_BombDefusing3rdParty( UAS_PriorityMessageListItem newListItem )
	{
		AAS_PlayerEntity player		  = Client_GetLocalASPawn();
		TArray<AAS_RaidBomb> allBombs = GetAllRaidBombsFor3rdPartyTeam( player.GetTeam() );
		AAS_RaidBomb bomb			  = GetHighestPriorityBombByDefuseTime( allBombs );
		if ( !IsValid( bomb ) )
			return;

		newListItem.StartProgressbar_ByDuration( bomb.GetDefuseStartTimeMS(), bomb.GetDefuseTime() );
	}

	UFUNCTION( NotBlueprintCallable )
	void OnCreate_BreacherCountdown_v2( UAS_PriorityMessageListItem newListItem )
	{
		EPriorityMessageLevel priorityLevel	   = newListItem.parentMessage.messageData.priorityLevel;
		TArray<AAS_ShieldBreaker> allBreachers = GetAllBreachersFromPriorityLevel( priorityLevel );
		AAS_ShieldBreaker breacher			   = GetHighestPriorityBreacherByPlantTime( allBreachers );
		if ( !IsValid( breacher ) )
			return;

		newListItem.StartCountdown_ByDuration( breacher.GetPlantTimeMS(), breacher.GetBreachTime(), COUNTDOWN_DELAY_TIMER );
	}

	UFUNCTION( NotBlueprintCallable )
	void OnCreate_DefenderRaidEnergy( UAS_PriorityMessageListItem newListItem )
	{
		EPriorityMessageLevel priorityLevel	   = newListItem.parentMessage.messageData.priorityLevel;
		TArray<AAS_ShieldBreaker> allBreachers = GetAllBreachersFromPriorityLevel( priorityLevel );
		AAS_ShieldBreaker breacher			   = GetHighestPriorityBreacherByRaidEnergy( allBreachers );
		if ( !IsValid( breacher ) )
			return;

		Thread( this, n"__UpdateRaidEnergy", newListItem, breacher, true );
	}

	UFUNCTION( NotBlueprintCallable )
	void OnCreate_AttackerRaidEnergy( UAS_PriorityMessageListItem newListItem )
	{
		AAS_PlayerEntity player	   = Client_GetLocalASPawn();
		AAS_ShieldBreaker breacher = GetBreacherForTeam( player.GetTeam() );

		if ( !IsValid( breacher ) )
			return;

		if ( GetCvarBool( f"ScriptDebug.RaidMsg_BreacherEnergySegment" ) )
		{
			int segments = GetTeamAttackerMaxLives( player.GetTeam() );
			newListItem.SetProgressBarSegments( segments, RAID_ENERGY_GAPSIZE );
		}
		Thread( this, n"__UpdateRaidEnergy", newListItem, breacher, false );
	}

	UFUNCTION( NotBlueprintCallable )
	private void __UpdateRaidEnergy( UNCCoroutine co, UAS_PriorityMessageListItem listItem, AAS_ShieldBreaker breacher, bool isDefender )
	{
		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( !IsValid( player ) )
			return;

		co.EndOn( this, listItem.onStopSignal );

		float32 duration = breacher.GetBreacherEnergyTotalDuration();
		int startTimeMS	 = __getRaidEnergyBarStartMS( player, breacher );
		listItem.StartProgressbar_ByDuration( startTimeMS, duration );

		FText respawnText = GetLocalizedText( Localization::Raid, f"raid_attacker_respawns" );
		listItem.SetListMessage( respawnText );

		float32 baseTime = ( GameModeDefaults().RespawnRules_RaidRespawn_AttackerLives * GameModeDefaults().RespawnRules_ShieldBreakerLifetimeSeconds_PerLife ) + GameModeDefaults().RespawnRules_ShieldBreakerLifetimeSeconds_Base;

		while ( true )
		{
			if ( !IsValid( breacher ) )
				break;

			if ( !IsValid( listItem ) )
				return;

			if ( !isDefender )
			{
				FNumberFormattingOptions options;
				options.SetMinMaxFractionalDigits( 1 );

				float32 percent		   = ( breacher.GetBreacherEnergyTimeLeft() / baseTime ) * 100;
				FText percentTxt	   = FText::AsNumber( percent, options );
				FText txtBreakerEnergy = GetLocalizedText( Localization::Raid, f"raid_breaker_energy_percent", FFormatArgumentValue( percentTxt ) );

				listItem.SetListMessage( txtBreakerEnergy );
			}

			int barStartTimeMS = __getRaidEnergyBarStartMS( player, breacher );
			listItem.UpdateProgressBarInfo( barStartTimeMS, duration );

			co.AddWait( Signals::UPDATE_RAID_MESSAGING );
			co.AddWait( 0.1 ); // GetDefaultUpdateTickDelay() );
			co.AwaitAny();
		}

		listItem.Hide();
	}

	private int __getRaidEnergyBarStartMS( AAS_PlayerEntity player, AAS_ShieldBreaker breacher )
	{
		// the padding is to get the timer to line up with the gaps so when the lives switch over we're not sitting in a gap for long
		float totalDuration = breacher.GetBreacherEnergyTotalDuration();
		int segments		= GetTeamAttackerMaxLives( player.GetTeam() );
		float gapFrac		= RAID_ENERGY_GAPSIZE / segments;
		int paddingMS		= TO_MILLISECONDS( float32( totalDuration * ( gapFrac * 0.5 ) ) );

		return breacher.net_breacherCountdownStartTime - breacher.GetUsedLivesTimeMS() + paddingMS;
	}

	UFUNCTION( NotBlueprintCallable )
	void OnCreate_UpdateCanFixBase_v2( UAS_PriorityMessageListItem newListItem )
	{
		Thread( this, n"__UpdateCanFixBase_v2", newListItem );
	}

	UFUNCTION( NotBlueprintCallable )
	private void __UpdateCanFixBase_v2( UNCCoroutine co, UAS_PriorityMessageListItem listItem )
	{
		co.EndOn( this, listItem.onStopSignal );

		while ( true )
		{
			if ( !IsValid( listItem ) )
				return;

			UAS_PriorityMessageListData updateData = GetTemplateMessageListData( EPriorityListTemplate::DEFENDER_STOP_RAID );
			listItem.UpdateListItem( updateData );

			co.AddWait( Signals::UPDATE_RAID_MESSAGING );
			co.AddWait( GetDefaultUpdateTickDelay() );
			co.AwaitAny();
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void OnCreate_UpdateTeleport_v2( UAS_PriorityMessageListItem newListItem )
	{
		Thread( this, n"__UpdateTeleport_v2", newListItem );
	}

	UFUNCTION( NotBlueprintCallable )
	private void __UpdateTeleport_v2( UNCCoroutine co, UAS_PriorityMessageListItem listItem )
	{
		co.EndOn( this, listItem.onStopSignal );

		while ( true )
		{
			if ( !IsValid( listItem ) )
				return;

			if ( ShouldLocalPlayerTeleportHome() )
				listItem.Appear();
			else
				listItem.Disappear();

			co.AddWait( Signals::UPDATE_RAID_MESSAGING );
			co.AddWait( GetDefaultUpdateTickDelay() );
			co.AwaitAny();
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void OnCreate_UpdateNumBreachers_v2( UAS_PriorityMessageListItem newListItem )
	{
		Thread( this, n"__UpdateNumBreachers_v2", newListItem );
	}

	UFUNCTION( NotBlueprintCallable )
	private void __UpdateNumBreachers_v2( UNCCoroutine co, UAS_PriorityMessageListItem listItem )
	{
		if ( IsDevTest() )
			return;

		co.EndOn( this, listItem.onStopSignal );

		while ( true )
		{
			if ( !IsValid( listItem ) )
				return;

			EPriorityMessageLevel priorityLevel	   = listItem.parentMessage.messageData.priorityLevel;
			TArray<AAS_ShieldBreaker> allBreachers = GetAllBreachersFromPriorityLevel( priorityLevel );
			UAS_PriorityMessageListData updateData = listItem.listItemData;

			updateData.ListItemTxt = GetLocalizedText( Localization::Raid, f"raid_num_breakers", FFormatArgumentValue( allBreachers.Num() ) );

			if ( allBreachers.Num() > 0 )
				listItem.UpdateListItem( updateData );
			else if ( !listItem.IsHidden() )
			{
				float duration = listItem.Disappear();

				co.Wait( duration );

				listItem.SetListMessage( updateData.ListItemTxt );
			}

			co.AddWait( Signals::UPDATE_RAID_MESSAGING );
			co.AddWait( GetDefaultUpdateTickDelay() );
			co.AwaitAny();
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void OnCreate_UpdateNumEnemies_v2( UAS_PriorityMessageListItem newListItem )
	{
		Thread( this, n"__UpdateNumEnemies_v2", newListItem );
	}

	UFUNCTION( NotBlueprintCallable )
	private void __UpdateNumEnemies_v2( UNCCoroutine co, UAS_PriorityMessageListItem listItem )
	{
		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( !IsValid( player ) )
			return;

		AAS_RaidDomeShield dome = GetRaidDomeShieldForTeam( player.GetTeam() );

		co.EndOn( this, listItem.onStopSignal );

		while ( true )
		{
			if ( !IsValid( dome ) )
				return;

			if ( !IsValid( listItem ) )
				return;

			int numEnemies						   = dome.net_DomeContainsEnemies;
			int numEnemySpawns					   = dome.GetAllAttackerRespawnBeaconsInsideDome().Num();
			UAS_PriorityMessageListData updateData = listItem.listItemData;

			bool shouldShowData = true;
			if ( numEnemies > 0 )
				updateData.ListItemTxt = GetLocalizedText( Localization::Raid, f"raid_enemies_in_base", FFormatArgumentValue( numEnemies ) );
			else if ( numEnemySpawns > 0 )
				updateData.ListItemTxt = GetLocalizedText( Localization::Raid, f"raid_enemy_spawn_point_present", FFormatArgumentValue( numEnemies ) );
			else
				shouldShowData = false;

			if ( shouldShowData )
				listItem.UpdateListItem( updateData );
			else if ( !listItem.IsHidden() )
			{
				float duration = listItem.Disappear();
				co.Wait( duration );
				listItem.SetListMessage( updateData.ListItemTxt );
			}

			co.AddWait( Signals::UPDATE_RAID_MESSAGING );
			co.AddWait( GetDefaultUpdateTickDelay() );
			co.AwaitAny();
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void OnCreate_UpdateDefenderLives_v2( UAS_PriorityMessageListItem newListItem )
	{
	}

	UFUNCTION( NotBlueprintCallable )
	void OnCreate_UpdateAttackerLives_v2( UAS_PriorityMessageListItem newListItem )
	{
		if ( GetCvarBool( f"ScriptDebug.RaidMsg_NoBreacherLives" ) )
			newListItem.Hide();
		if ( !GetCvarBool( f"ScriptDebug.RaidMsg_NoBreacherLives" ) )
			Thread( this, n"__UpdateRaidLivesDefaultTemplate_v2", newListItem, false );
	}

	UFUNCTION( NotBlueprintCallable )
	private void __UpdateRaidLivesDefaultTemplate_v2( UNCCoroutine co, UAS_PriorityMessageListItem listItem, bool isDefender )
	{
		AAS_PlayerEntity player = Client_GetLocalASPawn();
		int playerTeam			= player.GetTeam();

		co.EndOn( this, listItem.onStopSignal );

		while ( true )
		{
			if ( !IsValid( listItem ) )
				return;

			UAS_PriorityMessageListData updateData = listItem.listItemData;

			int numRespawns;
			bool shouldUpdate	   = true;
			numRespawns			   = GetTeamAttackerLives( playerTeam );
			updateData.ListItemTxt = GetLocalizedText( Localization::Raid, f"raid_attacker_respawns_left", FFormatArgumentValue( numRespawns ) );

			AAS_ShieldBreaker breacher = GetBreacherForTeam( playerTeam );
			if ( !isDefender && !IsValid( breacher ) )
				updateData.ListItemTxt = GetLocalizedText( Localization::Raid, f"raid_no_shield_breaker" );

			if ( shouldUpdate )
				listItem.UpdateListItem( updateData );

			co.AddWait( Signals::UPDATE_RAID_MESSAGING );
			co.AddWait( GetDefaultUpdateTickDelay() );
			co.AwaitAny();
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnCreate_UpdateAttackerDefaultTemplate_v2( UAS_PriorityMessageListItem newListItem )
	{
		Thread( this, n"__UpdateAttackerDefaultTemplate_v2", newListItem );
	}

	UFUNCTION( NotBlueprintCallable )
	private void __UpdateAttackerDefaultTemplate_v2( UNCCoroutine co, UAS_PriorityMessageListItem listItem )
	{
		co.EndOn( this, listItem.onStopSignal );

		FText findVaultText = GetLocalizedText( Localization::Raid, f"raid_find_the_vault" );
		FText lockedOutText = GetLocalizedText( Localization::Raid, f"raid_locked_out" );
		FText plantBombText = GetLocalizedText( Localization::Raid, f"raid_plant_the_bomb" );

		UAS_PriorityMessageListData defaultData = GetTemplateMessageListData( listItem.listItemData.ListTemplate );

		bool textEqualToDefault = Text::EqualEqual_IgnoreCase_TextText( listItem.listItemData.ListItemTxt, defaultData.ListItemTxt );
		if ( textEqualToDefault )
			listItem.listItemData.ListItemTxt = lockedOutText;
	}

	UFUNCTION( NotBlueprintCallable )
	void OnCreate_DomeRepair( UAS_PriorityMessageListItem newListItem )
	{
		EPriorityMessageLevel priorityLevel = newListItem.parentMessage.messageData.priorityLevel;
		bool isDefender						= priorityLevel == EPriorityMessageLevel::DEFENDER;

		EPriorityMessageTemplate template = isDefender ? EPriorityMessageTemplate::V2_DEFEND_DOME_REPAIR : EPriorityMessageTemplate::V2_ATTACK_DOME_REPAIR;
		AddTemplateMessage( template );

		AAS_RaidDomeShield dome = GetRaidDomeShieldByPriorityLevel( priorityLevel );
		if ( IsValid( dome ) )
			newListItem.StartCountdown_ByDuration( GetGameTimeMS(), dome.GetCurrentRepairTime(), COUNTDOWN_DELAY_TIMER );
	}

	UFUNCTION( NotBlueprintCallable )
	void OnCreate_RevealEnemy( UAS_PriorityMessageListItem newListItem )
	{
		AAS_PlayerEntity player				= Client_GetLocalASPawn();
		EPriorityMessageLevel priorityLevel = newListItem.parentMessage.messageData.priorityLevel;
		bool isDefender						= priorityLevel == EPriorityMessageLevel::DEFENDER;

		if ( !IsValid( player ) || !IsValid( newListItem ) )
			return;

		AAS_RaidDomeShield dome = GetRaidDomeShieldByPriorityLevel( priorityLevel );
		if ( !IsValid( dome ) || dome.IsBroken() )
			return;

		bool hasEnemies = isDefender ? dome.net_DomeContainsEnemies > 0 : dome.playerDetectionRadius.playersInside.Contains( player );
		if ( hasEnemies )
		{
			newListItem.Appear();
			newListItem.StartCountdown_ByDuration( GetGameTimeMS(), GameConst::RAID_MODE_REVEAL_ENEMIES_COUNTDOWN, COUNTDOWN_DELAY_TIMER );

			EPriorityMessageTemplate template = isDefender ? EPriorityMessageTemplate::V2_DEFEND_ENEMY_REVEAL : EPriorityMessageTemplate::V2_ATTACK_ENEMY_REVEAL;
			AddTemplateMessage( template );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void OnEnd_RevealEnemy_Countdown( UAS_PriorityMessageListItem listItem, EPriorityMessageCountdownStopReason Reason )
	{
		listItem.HideCountdown();
	}

	UFUNCTION( NotBlueprintCallable )
	void OnStop_RevealEnemy( UAS_PriorityMessageListItem listItem )
	{
		listItem.HideCountdown();
	}

	// This is super gross but we have sequential timers and need them to line up exactly
	private int lastCountdownSeconds = GameConst::INDEX_NONE;
	private UAS_PriorityMessageBaseWidget gameStartingWidget;
	private int gameStartingEndTimeMS = 0;
	UFUNCTION( NotBlueprintCallable )
	private void OnGameStartingThread()
	{
		if ( !IsValid( gameStartingWidget ) )
			return;

		int timeRemainingMs = gameStartingEndTimeMS - GetGameTimeMS();
		if ( timeRemainingMs > 0 )
		{
			int countdownSeconds = Math::CeilToInt( TO_SECONDS( timeRemainingMs ) );
			if ( countdownSeconds != lastCountdownSeconds )
			{
				FText formattedTime = Text::AsTimespan_Timespan( FTimespan::FromMilliseconds( timeRemainingMs ) );
				FText subheader		= GetLocalizedText( Localization::GameState, "game_starting_message_subheader", FFormatArgumentValue( formattedTime ) );
				gameStartingWidget.SetSubheader( subheader );
				lastCountdownSeconds = countdownSeconds;
			}

			System::SetTimerForNextTick( this, "OnGameStartingThread" );
		}
	}

	float GetDefaultUpdateTickDelay()
	{
		float delay = TO_SECONDS( GetGameTimeMS() ) % PRIORITY_MESSAGE_DEFAULT_UPDATE_TICK;

		if ( delay == 0 )
		{
			delay = PRIORITY_MESSAGE_DEFAULT_UPDATE_TICK;
		}

		return delay;
	}

	////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
	// Ported from UCL_PriorityMessageManager

	void SetLastSoundPlayTime( UAS_PriorityMessageBaseWidget Message )
	{
		// only care about center messages to create a buffer for edge messages... edge messages can stack audio wise
		if ( Message.messageData.placement != EPriorityMessagePlacement::CENTER )
			return;

		lastSoundPlayTimeMS = Message.GetTimeMilliseconds();
	}

	bool CanPlayAppearSound( UAS_PriorityMessageBaseWidget Message )
	{
		// center messages ALWAYS play no matter the buffer
		if ( Message.messageData.placement == EPriorityMessagePlacement::CENTER )
			return true;

		float ElapsedTime = TO_SECONDS( Message.GetTimeMilliseconds() - lastSoundPlayTimeMS );
		return ElapsedTime >= PRIORITY_MESSAGE_AUDIO_NOPLAY_BUFFER;
	}

	protected void EnableDevTest()
	{
		__DevTest = true;
	}

	protected bool IsDevTest()
	{
		return __DevTest;
	}

	/****************************************************************\

	 ██████ ██      ███████  █████  ███    ██     ██    ██ ██████
	██      ██      ██      ██   ██ ████   ██     ██    ██ ██   ██
	██      ██      █████   ███████ ██ ██  ██     ██    ██ ██████
	██      ██      ██      ██   ██ ██  ██ ██     ██    ██ ██
	 ██████ ███████ ███████ ██   ██ ██   ████      ██████  ██

	\****************************************************************/

	UFUNCTION()
	protected void CleanupMessage_Delayed( UNCCoroutine co, float32 delay, UWidget widget )
	{
		co.Wait( delay );
		CleanupMessage( widget );
	}

	UFUNCTION()
	protected void CleanupMessage( UWidget Widget )
	{
		UAS_PriorityMessageBaseWidget OldWidget = Cast<UAS_PriorityMessageBaseWidget>( Widget );
		if ( !IsValid( OldWidget ) )
			return;

		// it's already disappearing meaning something else called CleanupMessage
		if ( OldWidget.IsMessageDisappearing() )
			return;

		UAS_PriorityMessageData Data = OldWidget.messageData;
		if ( !IsValid( Data ) )
			return;

		if ( Data.autoDissappear )
		{
			OldWidget.Hide();
			onMessageClearedSignal.Emit();
		}

		if ( !ShouldDoFollowupMessage( Data ) )
			return;

		DoFollowupMessage( Data );
	}

	bool ShouldDoFollowupMessage( UAS_PriorityMessageData Data )
	{
		// no follow up
		if ( Data.followUpTemplate == EPriorityMessageTemplate::_count && !IsValid( Data.followUpData ) )
			return false;

		// this data had an instant follow up... Do Not Repeat the followup
		if ( ( Data.instantFollowup ) )
			return false;

		UAS_PriorityMessageData FollowupData = GetFollowupData( Data );
		if ( !IsValid( FollowupData ) )
			return false;

		// if the followup is going to an edge, check some stuff... but if it's center - we're good to go
		if ( FollowupData.placement == EPriorityMessagePlacement::CENTER )
			return true;

		// no current edge message at that priority -> all clear
		TArray<UAS_PriorityMessageBaseWidget> AllEdgeMessages = GetActiveEdgeMessages( FollowupData.priorityLevel );
		if ( AllEdgeMessages.Num() == 0 )
			return true;

		// follow up msg already on edge
		for ( UAS_PriorityMessageBaseWidget EdgeMessage : AllEdgeMessages )
		{
			if ( EdgeMessage.messageData.messageTemplate == EPriorityMessageTemplate::CUSTOM )
				continue;
			if ( EdgeMessage.messageData.messageTemplate == Data.followUpTemplate )
				return false;
			if ( IsValid( Data.followUpData ) && EdgeMessage.messageData == Data.followUpData )
				return false;
			if ( Data.priorityStackValue < EdgeMessage.messageData.priorityStackValue )
				return false;
		}
		return true;
	}

	protected void DoFollowupMessage( UAS_PriorityMessageData Data )
	{
		UAS_PriorityMessageData FollowupData = GetFollowupData( Data );
		if ( IsValid( FollowupData ) )
			AddMessage( FollowupData );
	}

	UAS_PriorityMessageData GetFollowupData( UAS_PriorityMessageData Data )
	{
		if ( Data.followUpTemplate != EPriorityMessageTemplate::_count )
		{
			ScriptAssert( !IsValid( Data.followUpData ), "UAS_PriorityMessageData has both a .FollowUpData and .FollowUpTemplate. The system can't know which one you want... only pick one" );
			return GetTemplateMessageData( Data.followUpTemplate );
		}
		else if ( IsValid( Data.followUpData ) )
		{
			return Data.followUpData;
		}

		return nullptr;
	}

	/****************************************************************\

	 █████  ██████  ██████      ███    ███ ███████ ███████ ███████  █████   ██████  ███████
	██   ██ ██   ██ ██   ██     ████  ████ ██      ██      ██      ██   ██ ██       ██
	███████ ██   ██ ██   ██     ██ ████ ██ █████   ███████ ███████ ███████ ██   ███ █████
	██   ██ ██   ██ ██   ██     ██  ██  ██ ██           ██      ██ ██   ██ ██    ██ ██
	██   ██ ██████  ██████      ██      ██ ███████ ███████ ███████ ██   ██  ██████  ███████

	\****************************************************************/
	UAS_PriorityMessageBaseWidget AddTemplateMessage( EPriorityMessageTemplate template )
	{
		UAS_PriorityMessageData data = GetTemplateMessageData( template );
		return AddMessage( data );
	}

	UAS_PriorityMessageBaseWidget AddMessage( UAS_PriorityMessageData data )
	{
		if ( !IsValid( hud ) || !IsValid( hud.mainHUDWidget ) || !IsValid( data ) )
			return nullptr;

		UAS_PriorityMessageBaseWidget newMessage = Cast<UAS_PriorityMessageBaseWidget>( WidgetBlueprint::CreateWidget( data.widgetClass, hud.mainHUDWidget.GetOwningPlayer() ) );
		if ( IsValid( newMessage ) )
		{
			newMessage.Initialize( data );

			if ( data.clearEdgeOnAppear )
			{
				ScriptAssert( data.placement == EPriorityMessagePlacement::CENTER, "ClearEdgeOnAppear should only be used with Data.Placement == EPriorityMessagePlacement::CENTER, because it's meant to clear the edge on a center message. Edge messages auto clear themselves" );
				ClearActiveEdgeMessages( data.priorityLevel );
			}

			if ( data.instantEdgeReplace || data.instantFollowup )
			{
				bool shouldReplaceEdge = __ShouldReplaceEdgeMessage( data );
				ScriptAssert( IsValid( GetFollowupData( data ) ), "InstantFollowup set to true, but no followup message set up. Either leave InstantFollowup false, or setup followup data" );

				if ( data.instantFollowup || shouldReplaceEdge )
					DoFollowupMessage( data );
			}

			data.onCreate.Broadcast( newMessage );

			UPanelWidget Container = GetPriorityMessageChildContainer( data );
			__UpdateExistingPriorityMessagesInContainer( Container, data );

			Container.AddChild( newMessage );
			onMessageCreatedSignal.Emit();

			__UpdateEdgePriorities();

			Thread( this, n"CleanupMessage_Delayed", data.autoDissappearDelay, newMessage );

			// V2 messages

			if ( IsValid( newMessage ) )
			{
				UAS_PriorityMessageWidget_v2 v2Message = Cast<UAS_PriorityMessageWidget_v2>( newMessage );
				if ( IsValid( v2Message ) )
				{
					for ( TMapIterator<int, UAS_PriorityMessageListData> listDataEntry : data.v2_ListEntries )
					{
						UAS_PriorityMessageListData listDataItem = listDataEntry.GetValue();
						bool SetPriority						 = v2Message.GetListItemWidgets().Num() == 0 ? true : listDataItem.SetPriority;
						v2Message.AddListItem( listDataItem, SetPriority );
					}

					newMessage = v2Message;
				}
			}

			return newMessage;
		}

		return newMessage;
	}

	protected bool __ShouldReplaceEdgeMessage( UAS_PriorityMessageData Data )
	{
		if ( !Data.instantEdgeReplace )
			return false;

		UAS_PriorityMessageData FollowupData = GetFollowupData( Data );
		ScriptAssert( IsValid( FollowupData ), "InstantEdgeReplace set to true, but no followup message set up. Either leave InstantEdgeReplace false, or setup followup data" );
		ScriptAssert( FollowupData.placement == EPriorityMessagePlacement::EDGE, "InstantEdgeReplace set to true, but Followup's Placement is not set to EPriorityMessagePlacement::EDGE. Either leave InstantEdgeReplace false or set Followup's Placement to EPriorityMessagePlacement::EDGE" );

		return GetActiveEdgeMessages( FollowupData.priorityLevel ).Num() > 0;
	}

	protected void __UpdateExistingPriorityMessagesInContainer( UPanelWidget Container, UAS_PriorityMessageData Data )
	{
		int MaxNum = GetPriorityMessageMaxCount( Data );
		ScriptAssert( MaxNum > 0, "GetPriorityMessageMaxCount returned 0 but there must be at least a max of 1" );

		TArray<UAS_PriorityMessageBaseWidget> ActiveMessages;
		switch ( Data.placement )
		{
			case EPriorityMessagePlacement::CENTER:
				ActiveMessages = GetAllActiveCenterMessages();
				break;

			case EPriorityMessagePlacement::EDGE:
				ActiveMessages = GetActiveEdgeMessages( Data.priorityLevel );
				break;

			default:
				ScriptAssert( false, f"Didn't account for Active Messages of Placement: {Data.placement}" );
				break;
		}

		const int AddNum = 1;
		int NumToClear	 = Math::Max( ActiveMessages.Num() - ( MaxNum - AddNum ), 0 );

		int Index = 0;
		while ( NumToClear > 0 )
		{
			UAS_PriorityMessageBaseWidget OldMessage = ActiveMessages[Index];

			/*	defensive check for https://wildlight.atlassian.net/browse/NC1-14273. I didn't bother to
				track down the root of how this bug happened. It's the first time in 2 years and there
				is a good chance this whole system goes away when UI team refactors the messaging, so I
				figured I'd not waste time tracking down an impossible to repro bug. 	*/
			if ( OldMessage != nullptr )
			{
				EPriorityMessageLevel oldPriority					= OldMessage.messageData.priorityLevel;
				EPriorityMessageLevel newPriority					= Data.priorityLevel;
				TSubclassOf<UAS_PriorityMessageBaseWidget> oldClass = OldMessage.Class;
				TSubclassOf<UAS_PriorityMessageBaseWidget> newClass = Data.widgetClass;
				// don't CleanupMessage if we're quickly updating the center stack  with the same priority
				if ( !( oldPriority == newPriority && oldClass == newClass ) )
					CleanupMessage( OldMessage );

				OldMessage.Hide();
			}

			Index++;
			NumToClear--;
		}
	}

	protected void __UpdateEdgePriorities()
	{
		TArray<UWidget> AllChildren = priorityMessageContainer.priorityMessageContainerLeft.GetAllChildren();

		int CurrPriority = 100; // just any large number... 0 is highest priority
		for ( int i = 0; i < AllChildren.Num(); i++ )
		{
			UPanelWidget Container = Cast<UPanelWidget>( AllChildren[i] );
			ScriptAssert( IsValid( Container ), f"PriorityMessageContainer.PriorityMessageContainerLeft doesn't have a container for index: {i}" );

			TArray<UWidget> AllOldWidgets = Container.GetAllChildren();
			for ( UWidget OldWidget : AllOldWidgets )
			{
				UAS_PriorityMessageBaseWidget OldMessage = Cast<UAS_PriorityMessageBaseWidget>( OldWidget );
				if ( !IsValid( OldMessage ) )
					continue;
				if ( OldMessage.IsMessageDisappearing() )
					continue;

				if ( CurrPriority > i )
					CurrPriority = i;

				if ( CurrPriority != i && OldMessage.MessageHasPriority() )
					OldMessage.DePrioritize();
				else if ( CurrPriority == i && !OldMessage.MessageHasPriority() )
					OldMessage.RePrioritize();
			}
		}
	}

	/****************************************************************\

	██    ██ ████████ ██ ██      ██ ████████ ██    ██
	██    ██    ██    ██ ██      ██    ██     ██  ██
	██    ██    ██    ██ ██      ██    ██      ████
	██    ██    ██    ██ ██      ██    ██       ██
	 ██████     ██    ██ ███████ ██    ██       ██

	\****************************************************************/
	TArray<UAS_PriorityMessageBaseWidget> GetActiveEdgeMessages( EPriorityMessageLevel PriorityLevel )
	{
		UPanelWidget Container = GetPriorityMessageEdgeContainer( PriorityLevel );

		TArray<UWidget> AllChildren = Container.GetAllChildren();
		TArray<UAS_PriorityMessageBaseWidget> Results;
		for ( UWidget OldWidget : AllChildren )
		{
			UAS_PriorityMessageBaseWidget OldMessage = Cast<UAS_PriorityMessageBaseWidget>( OldWidget );
			if ( !IsValid( OldMessage ) )
				continue;
			if ( OldMessage.IsMessageDisappearing() )
				continue;

			Results.Add( OldMessage );
		}

		return Results;
	}

	TArray<UAS_PriorityMessageBaseWidget> GetActiveCenterMessages( EPriorityMessageLevel PriorityLevel )
	{
		TArray<UAS_PriorityMessageBaseWidget> Results;

		if ( !IsValid(priorityMessageContainer) )
			return Results;

		UPanelWidget Container = GetPriorityMessageCenterContainer();

		TArray<UWidget> AllChildren = Container.GetAllChildren();
		for ( UWidget OldWidget : AllChildren )
		{
			UAS_PriorityMessageBaseWidget OldMessage = Cast<UAS_PriorityMessageBaseWidget>( OldWidget );
			if ( !IsValid( OldMessage ) )
				continue;
			if ( OldMessage.IsMessageDisappearing() )
				continue;
			if ( OldMessage.messageData.priorityLevel != PriorityLevel )
				continue;

			Results.Add( OldMessage );
		}

		return Results;
	}

	TArray<UAS_PriorityMessageBaseWidget> GetAllActiveCenterMessages()
	{
		UPanelWidget Container = GetPriorityMessageCenterContainer();

		TArray<UWidget> AllChildren = Container.GetAllChildren();
		TArray<UAS_PriorityMessageBaseWidget> Results;
		for ( UWidget OldWidget : AllChildren )
		{
			UAS_PriorityMessageBaseWidget OldMessage = Cast<UAS_PriorityMessageBaseWidget>( OldWidget );
			if ( !IsValid( OldMessage ) )
				continue;
			if ( OldMessage.IsMessageDisappearing() )
				continue;

			Results.Add( OldMessage );
		}

		return Results;
	}

	TArray<UAS_PriorityMessageBaseWidget> GetActiveMessages( EPriorityMessageLevel PriorityLevel )
	{
		TArray<UAS_PriorityMessageBaseWidget> AllOldMessages = GetActiveCenterMessages( PriorityLevel );
		AllOldMessages.Append( GetActiveEdgeMessages( PriorityLevel ) );

		return AllOldMessages;
	}

	TArray<UAS_PriorityMessageBaseWidget> GetActiveMatchingEdgeMessages( EPriorityMessageTemplate Template )
	{
		TArray<UAS_PriorityMessageBaseWidget> results;

		UAS_PriorityMessageData oldData = GetTemplateMessageData( Template );
		if ( IsValid( oldData ) )
		{
			TArray<UAS_PriorityMessageBaseWidget> allOldMessages = GetActiveEdgeMessages( oldData.priorityLevel );
			for ( UAS_PriorityMessageBaseWidget oldMessage : allOldMessages )
			{
				if ( oldMessage.messageData.messageTemplate == Template )
				{
					results.Add( oldMessage );
				}
			}
		}

		return results;
	}

	TArray<UAS_PriorityMessageBaseWidget> GetActiveMatchingCenterMessages( EPriorityMessageTemplate Template )
	{
		TArray<UAS_PriorityMessageBaseWidget> results;

		UAS_PriorityMessageData oldData = GetTemplateMessageData( Template );
		if ( IsValid( oldData ) )
		{
			TArray<UAS_PriorityMessageBaseWidget> allOldMessages = GetActiveCenterMessages( oldData.priorityLevel );
			for ( UAS_PriorityMessageBaseWidget OldMessage : allOldMessages )
			{
				if ( OldMessage.messageData.messageTemplate == Template )
				{
					results.Add( OldMessage );
				}
			}
		}

		return results;
	}

	TArray<UAS_PriorityMessageBaseWidget> GetActiveMatchingMessages( EPriorityMessageTemplate Template )
	{
		UAS_PriorityMessageData oldData = GetTemplateMessageData( Template );
		if ( IsValid( oldData ) )
		{
			switch ( oldData.placement )
			{
				case EPriorityMessagePlacement::CENTER:
					return GetActiveMatchingCenterMessages( Template );

				case EPriorityMessagePlacement::EDGE:
					return GetActiveMatchingEdgeMessages( Template );

				default:
					break;
			}
		}

		ScriptAssert( false, f"GetActiveMessageFromTemplate didn't have a case for {oldData.placement}" );
		TArray<UAS_PriorityMessageBaseWidget> Empty;
		return Empty;
	}

	UFUNCTION()
	void ClearActiveEdgeMessages( EPriorityMessageLevel PriorityLevel )
	{
		TArray<UAS_PriorityMessageBaseWidget> AllOldMessages = GetActiveEdgeMessages( PriorityLevel );
		for ( UAS_PriorityMessageBaseWidget OldMessage : AllOldMessages )
			OldMessage.Hide();

		__UpdateEdgePriorities();
	}

	UFUNCTION()
	void ClearActiveCenterMessages( EPriorityMessageLevel PriorityLevel )
	{
		TArray<UAS_PriorityMessageBaseWidget> AllOldMessages = GetActiveCenterMessages( PriorityLevel );
		for ( UAS_PriorityMessageBaseWidget OldMessage : AllOldMessages )
			OldMessage.Hide();
	}

	UFUNCTION()
	void ClearActiveMessages( EPriorityMessageLevel PriorityLevel )
	{
		ClearActiveCenterMessages( PriorityLevel );
		ClearActiveEdgeMessages( PriorityLevel );
	}

	UFUNCTION()
	void ClearActiveMatchingEdgeMessages( EPriorityMessageTemplate Template )
	{
		TArray<UAS_PriorityMessageBaseWidget> AllOldMessages = GetActiveMatchingEdgeMessages( Template );
		for ( UAS_PriorityMessageBaseWidget OldMessage : AllOldMessages )
			OldMessage.Hide();

		__UpdateEdgePriorities();
	}

	UFUNCTION()
	void ClearActiveMatchingCenterMessages( EPriorityMessageTemplate Template )
	{
		TArray<UAS_PriorityMessageBaseWidget> AllOldMessages = GetActiveMatchingCenterMessages( Template );
		for ( UAS_PriorityMessageBaseWidget OldMessage : AllOldMessages )
			OldMessage.Hide();
	}

	UFUNCTION()
	void ClearActiveMatchingMessages( EPriorityMessageTemplate Template )
	{
		UAS_PriorityMessageData oldData = GetTemplateMessageData( Template );
		if ( IsValid( oldData ) )
		{
			switch ( oldData.placement )
			{
				case EPriorityMessagePlacement::CENTER:
					ClearActiveMatchingCenterMessages( Template );
					break;

				case EPriorityMessagePlacement::EDGE:
					ClearActiveMatchingEdgeMessages( Template );
					break;

				default:
					break;
			}
		}

		ScriptAssert( false, f"ClearActiveMessageFromTemplate didn't have a case for {oldData.placement}" );
	}

	UAS_PriorityMessageContainer GetPriorityMessageMainContainer()
	{
		return priorityMessageContainer;
	}

	UPanelWidget GetPriorityMessageChildContainer( UAS_PriorityMessageData Data )
	{
		UPanelWidget Container;
		if ( Data.placement == EPriorityMessagePlacement::CENTER )
			Container = GetPriorityMessageCenterContainer();
		else
			Container = GetPriorityMessageEdgeContainer( Data.priorityLevel );

		return Container;
	}

	UPanelWidget GetPriorityMessageCenterContainer()
	{
		ScriptAssert( IsValid( priorityMessageContainer.priorityMessageContainerCenter ), f"PriorityMessageContainer.PriorityMessageContainerCenter is not valid or is not a UPanelWidget" );
		return priorityMessageContainer.priorityMessageContainerCenter;
	}

	UPanelWidget GetPriorityMessageEdgeContainer( EPriorityMessageLevel PriorityLevel )
	{
		int Index = GetPriorityMessageEdgeIndexFromPriorityLevel( PriorityLevel );
		UPanelWidget Container;
		switch ( PriorityLevel )
		{
			case EPriorityMessageLevel::DEFENDER:
			case EPriorityMessageLevel::ATTACKER:
			case EPriorityMessageLevel::OTHER_LEFT:
				Container = Cast<UPanelWidget>( priorityMessageContainer.priorityMessageContainerLeft.GetChildAt( Index ) );
				break;

			case EPriorityMessageLevel::BASE:
			case EPriorityMessageLevel::POI_SMALL:
			case EPriorityMessageLevel::POI_LARGE:
			case EPriorityMessageLevel::DOOMSDAY:
			case EPriorityMessageLevel::CAREPACKAGE:
			case EPriorityMessageLevel::OTHER_RIGHT:
				Container = Cast<UPanelWidget>( priorityMessageContainer.priorityMessageContainerRight.GetChildAt( Index ) );
				break;

			default:
				break;
		}

		ScriptAssert( IsValid( Container ), f"PriorityMessageContainer.PriorityMessageContainer * EDGE doesn't have a container for index: {Index}" );
		return Container;
	}

	int GetPriorityMessageMaxCount( UAS_PriorityMessageData Data )
	{
		if ( Data.placement == EPriorityMessagePlacement::CENTER )
			return 1;

		switch ( Data.priorityLevel )
		{
			case EPriorityMessageLevel::OTHER_RIGHT:
				return PRIORITY_MESSAGE_MAX_OTHER_RIGHT_MSGS;

			default:
				break;
		}

		return 1;
	}

	int GetPriorityMessageEdgeIndexFromPriorityLevel( EPriorityMessageLevel PriorityLevel )
	{
		// harded coded values referencing index of panels inside of PriorityMessageContainers added to WBP_MainHudWidget.uasset
		switch ( PriorityLevel )
		{
			case EPriorityMessageLevel::DEFENDER:
				return 0;
			case EPriorityMessageLevel::ATTACKER:
				return 1;
			case EPriorityMessageLevel::OTHER_LEFT:
				return 2;

			case EPriorityMessageLevel::BASE:
				return 0;
			case EPriorityMessageLevel::DOOMSDAY:
				return 1;
			case EPriorityMessageLevel::POI_SMALL:
				return 2;
			case EPriorityMessageLevel::POI_LARGE:
				return 3;
			case EPriorityMessageLevel::CAREPACKAGE:
				return 4;
			case EPriorityMessageLevel::OTHER_RIGHT:
				return 5;

			default:
				break;
		}

		ScriptAssert( false, f"PriorityLevel {PriorityLevel} doesn't have a valid edge index" );
		return -1;
	}

	bool ShouldLocalPlayerTeleportHome()
	{
		AAS_PlayerEntity Player = Client_GetLocalASPawn();
		if ( !IsValid( Player ) )
			return false;

		if ( !IsAlive( Player ) )
			return false;

		FVector PlayerPos		= Player.GetActorLocation();
		AAS_RaidDomeShield dome = GetRaidDomeShieldForTeam( Player.GetTeam() );
		if ( !IsValid( dome ) )
			return false;

		float dist = Distance( dome.GetActorLocation(), PlayerPos );

		return dist > PRIORITY_MESSAGE_SHOULD_TELEPORT_DIST;
	}

	AAS_RaidDomeShield GetRaidDomeShieldByPriorityLevel( EPriorityMessageLevel priorityLevel )
	{
		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( !IsValid( player ) )
			return nullptr;

		bool isDefender = priorityLevel == EPriorityMessageLevel::DEFENDER;
		AAS_RaidDomeShield dome;
		if ( isDefender )
			dome = GetRaidDomeShieldForTeam( player.GetTeam() );
		else
		{
			TArray<AAS_RaidEventManager_v2> allManagers = GetAllAttackingRaidEventManagersForTeam_v2( player.GetTeam() );
			for ( AAS_RaidEventManager_v2 manager : allManagers )
				dome = GetRaidDomeShieldForTeam( manager.GetDefenderTeam() );
		}

		return dome;
	}

	/****************************************************************\

	 ██████  █████  ██      ██      ██████   █████   ██████ ██   ██ ███████
	██      ██   ██ ██      ██      ██   ██ ██   ██ ██      ██  ██  ██
	██      ███████ ██      ██      ██████  ███████ ██      █████   ███████
	██      ██   ██ ██      ██      ██   ██ ██   ██ ██      ██  ██       ██
	 ██████ ██   ██ ███████ ███████ ██████  ██   ██  ██████ ██   ██ ███████

	\****************************************************************/
	UFUNCTION()
	void OnCreate_UpdateTeleportCTA( UAS_PriorityMessageBaseWidget NewMessage )
	{
		Thread( this, n"__UpdateTeleportCTA", NewMessage );
	}

	UFUNCTION()
	protected void __UpdateTeleportCTA( UNCCoroutine co, UWidget OldWidget )
	{
		while ( true )
		{
			UAS_PriorityMessageBaseWidget NewMessage = Cast<UAS_PriorityMessageBaseWidget>( OldWidget );
			if ( !IsValid( NewMessage ) )
				return;

			if ( NewMessage.IsMessageDisappearing() )
				return;

			if ( ShouldLocalPlayerTeleportHome() )
			{
				FText teleportHomeTxt = GetLocalizedText( Localization::Raid, f"raid_teleport_home" );
				NewMessage.SetCTA( teleportHomeTxt );
				NewMessage.ShowCTA();
			}
			else
				NewMessage.HideCTA();

			co.Wait( PRIORITY_MESSAGE_DEFAULT_UPDATE_TICK );
		}
	}

	UFUNCTION()
	void OnCreate_GamePhaseOrRaidScheduler_Countdown( UAS_PriorityMessageBaseWidget NewMessage )
	{
		if ( IsDevTest() )
			NewMessage.StartCountdown_ByDuration( NewMessage.GetTimeMilliseconds(), 30 );
		else if ( NewMessage.messageData.countdownDuration > 0 )
			NewMessage.StartCountdown_ByDuration( NewMessage.GetTimeMilliseconds(), NewMessage.messageData.countdownDuration );
		else
		{
			int endTime = NewMessage.messageData.countdownUsesRaidScheduler ? int( GetRaidModeNetworking().net_nextEventTime ) : GetNextGamePhaseTime();
			NewMessage.StartCountdown_ByEndTime( endTime );
		}
	}

	UFUNCTION()
	void OnEnd_GamePhase_Countdown( UAS_PriorityMessageBaseWidget NewMessage, EPriorityMessageCountdownStopReason Reason )
	{
	}

	UFUNCTION()
	void OnCreate_EnemyReveal_Countdown( UAS_PriorityMessageBaseWidget NewMessage )
	{
		NewMessage.StartCountdown_ByDuration( GetGameTimeMS(), GameConst::RAID_MODE_REVEAL_ENEMIES_COUNTDOWN );
	}

	UFUNCTION()
	void OnCreate_DomeRepair_Countdown( UAS_PriorityMessageBaseWidget NewMessage )
	{
		EPriorityMessageLevel priorityLevel = NewMessage.messageData.priorityLevel;
		AAS_RaidDomeShield dome				= GetRaidDomeShieldByPriorityLevel( priorityLevel );
		bool isAttacker						= priorityLevel == EPriorityMessageLevel::ATTACKER;

		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( isAttacker && IsValid( player ) )
		{
			// in the dome?
			if ( Distance( player.GetActorLocation(), dome.GetActorLocation() ) <= dome.GetWorldPlayerDetectionTriggerRadius() )
			{
				FText breakerDownTxt = GetLocalizedText( Localization::Raid, f"raid_breaker_down" );
				NewMessage.SetHeader( breakerDownTxt );
				return;
			}
			else
				NewMessage.ShowCTA();
		}

		NewMessage.StartCountdown_ByDuration( GetGameTimeMS(), dome.GetCurrentRepairTime() );
	}

	UFUNCTION()
	void OnCreate_AttackerLose( UAS_PriorityMessageBaseWidget NewMessage )
	{
		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( !IsValid( player ) )
			return;

		AAS_RaidDomeShield dome;
		TArray<AAS_RaidEventManager_v2> allManagers = GetAllAttackingRaidEventManagersForTeam_v2( player.GetTeam(), ERaidEventActive::ACTIVE_OR_INACTIVE );
		for ( AAS_RaidEventManager_v2 manager : allManagers )
		{
			dome = GetRaidDomeShieldForTeam( manager.GetDefenderTeam() );
			if ( IsValid( dome ) && dome.IsDomeDestroyed() )
			{
				FText lostToRivalTxt = GetLocalizedText( Localization::Raid, f"raid_lost_to_rival_team" );
				NewMessage.SetSubheader( lostToRivalTxt );
				return;
			}
		}
	}

	////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
}