namespace PriorityMessageGameStateTemplates
{
	bool GetTemplateMessageData( EPriorityMessageTemplate template, UCL_PriorityMessageManager_v2 manager, UAS_PriorityMessageData& data )
	{
		bool result = false;

		if ( IsValid( data ) && IsValid( manager ) )
		{
			// Start assuming that we will find the template
			result = true;

			switch ( template )
			{
				case EPriorityMessageTemplate::PREPARE_BASE_CENTER:
					data.theme			= EPriorityMessageTheme::NEUTRAL_ACTION;
					data.header			= GetLocalizedText( Localization::GameState, f"secure_your_base_message_header" );
					data.subheader		= GetLocalizedText( Localization::GameState, f"secure_your_base_message_subheader" );
					data.autoDissappear = true;
					break;

				case EPriorityMessageTemplate::SHIELDBREAKER_CRAFTING:
					data.theme			= EPriorityMessageTheme::NEUTRAL_SHIELDBREAKER_ACTION;
					data.header			= GetLocalizedText( Localization::GameState, f"shieldbreaker_crafting_message_header" );
					data.subheader		= GetLocalizedText( Localization::GameState, f"shieldbreaker_crafting_message_subheader" );
					data.autoDissappear = true;
					break;

				case EPriorityMessageTemplate::SHIELDBREAKER_CRAFTED:
					data.theme			= EPriorityMessageTheme::NEUTRAL_SHIELDBREAKER_ACTION;
					data.header			= GetLocalizedText( Localization::GameState, f"pickup_shieldbreaker_message_header" );
					data.subheader		= GetLocalizedText( Localization::GameState, f"pickup_shieldbreaker_message_subheader" );
					data.autoDissappear = true;
					break;

				case EPriorityMessageTemplate::SHIELDBREAKER_PICKED_UP:
					data.theme			= EPriorityMessageTheme::FRIENDLY_SHIELDBREAKER_ACTION;
					data.header			= GetLocalizedText( Localization::GameState, f"raid_base_message_header" );
					data.subheader		= GetLocalizedText( Localization::GameState, f"raid_base_message_subheader" );
					data.autoDissappear = true;
					break;

				case EPriorityMessageTemplate::SHIELDBREAKER_PICKED_UP_ENEMY:
					data.theme			= EPriorityMessageTheme::ENEMY_SHIELDBREAKER_ACTION;
					data.header			= GetLocalizedText( Localization::GameState, f"intercept_shieldbreaker_message_header" );
					data.subheader		= GetLocalizedText( Localization::GameState, f"intercept_shieldbreaker_message_subheader" );
					data.autoDissappear = true;
					break;

				case EPriorityMessageTemplate::EXPLORE_PHASE:
					data.theme			= EPriorityMessageTheme::NEUTRAL_ACTION;
					data.header			= GetLocalizedText( Localization::GameState, f"explore_gather_message_header" );
					data.subheader		= GetLocalizedText( Localization::GameState, f"explore_gather_message_subheader" );
					data.autoDissappear = true;
					break;

				case EPriorityMessageTemplate::GAME_STARTING:
					data.theme	   = EPriorityMessageTheme::NEUTRAL_ACTION;
					data.header	   = GetLocalizedText( Localization::GameState, f"game_starting_message_header" );
					data.subheader = GetLocalizedText( Localization::GameState, f"game_starting_message_subheader" );
					data.autoDissappear = false;
					break;

				default:
					return false;
			}

			// All game state messages share some of the same traits
			data.widgetClass		 = manager.priorityMessageRaidWidgetClass;
			data.autoDissappearDelay = 4.0f;
			data.priorityLevel		 = EPriorityMessageLevel::BASE;
		}

		return result;
	}
}