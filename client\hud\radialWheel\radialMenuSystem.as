UAS_RadialMenuSystem GetRadialMenuSystem()
{
	UAS_RadialMenuSystem result = Cast<UAS_RadialMenuSystem>( UNCGameplaySystemsSubsystem::Get_ClientSystem( GetCurrentWorld(), UAS_RadialMenuSystem::StaticClass() ) );
	return result;
}

UCLASS( Abstract )
class UAS_RadialMenuSystem : UNCGameplaySystem_Client
{
	UPROPERTY()
	private TMap<FName, TSubclassOf<UAS_RadialMenuData>> radialWheelObjectData;
	private TMap<FName, UAS_RadialMenuData> _radialWheelObjectData;

	UPROPERTY()
	private TSubclassOf<UAS_RadialMenu> radialMenuClass;

	private UAS_DisplayWidgetManager radialManager;

	UFUNCTION( BlueprintOverride )
	void Initialize()
	{
		AAS_HUD hud = Cast<AAS_HUD>( HUD );
		if ( IsValid( hud ) )
		{
			radialManager = hud.radialManager;
		}

		for ( auto elem : radialWheelObjectData )
		{
			UAS_RadialMenuData obj = Cast<UAS_RadialMenuData>( NewObject( this, elem.Value ) );
			_radialWheelObjectData.Add( elem.Key, obj );
		}
	}

	TOptional<UAS_RadialMenuData> GetRadialWheelObjectData( FName name )
	{
		TOptional<UAS_RadialMenuData> result;

		if ( _radialWheelObjectData.Contains( name ) )
			result.Set( _radialWheelObjectData[name] );

		return result;
	}

	UAS_RadialMenu TryToOpenRadialMenu( FName name )
	{
		// Just to be safe, validate the menu name and that we are in the part of the game where wheels can be used
		if ( !ValidateRadialMenuName( name ) || GetGamePhase() < GamePhase::PREMATCH )
			return nullptr;

		if ( radialManager.HasCurrentDisplayWidget() )
		{
			// Before opening the new one, clear any previous menus
			radialManager.ClearDisplayWidget();
		}

		UAS_RadialMenu radialMenu = Cast<UAS_RadialMenu>( radialManager.CreateDisplayWidget( radialMenuClass ) );
		TArray<UAS_RadialMenuListItemData> outData;
		bool success			 = false;
		bool handleMouseNatively = true;

		if ( IsValid( radialMenu ) )
		{
			UAS_RadialMenuData data = Cast<UAS_RadialMenuData>( _radialWheelObjectData[name] );

			if ( _radialWheelObjectData.Contains( name ) )
			{
				handleMouseNatively = data.handleMouseNatively;
				success				= data.Populate( outData );
			}

			// If everything has been found, set the data
			if ( success )
			{
				// TODO @jmccarty: Refactor this
				for ( UAS_RadialMenuListItemData rdata : outData )
				{
					rdata.menuData	= data.radialData;
					rdata.menuIndex = name;
				}

				radialMenu.SetData( data.radialData, outData );
				radialMenu.SetItemOffset( data.radialMenuAngleOffset );
				radialMenu.ShouldHandleMouseNatively = handleMouseNatively;

				// As soon as the new menu is created, show it
				radialManager.ShowCurrentDisplayWidget();
			}
		}

		return radialMenu;
	}

	bool TryToActivateRadialMenu( FName name )
	{
		bool result = false;

		if ( !ValidateRadialMenuName( name ) )
			return result;

		radialManager.ActivateCurrentDisplayWidget( UAS_RadialMenu::StaticClass() );

		return result; // Set result = true?????
	}

	private bool ValidateRadialMenuName( FName name )
	{
		return IsValid( radialManager ) && !name.IsNone() && _radialWheelObjectData.Contains( name );
	}

	bool TryActivateCurrentDisplayWidget()
	{
		if ( !IsValid( radialManager ) )
			return false;

		radialManager.ActivateCurrentDisplayWidget( UAS_RadialMenu::StaticClass() );

		return true;
	}
}