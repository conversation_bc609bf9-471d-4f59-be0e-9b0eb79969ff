UCLASS( Abstract )
class UAS_PriorityMessageRaidWidget : UAS_PriorityMessageBaseWidget
{
	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UCommonRichTextBlock header;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UCommonRichTextBlock subheader;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage background;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	UImage flare;

	private UMaterialInstanceDynamic backgroundMaterial;

	private const FName MESSAGE_STATE_PARAMETER = n"MessageState";
	private const FName COMPLEX_STROKE_PARAMETER = n"UseComplexStroke";
	private const FName SHOW_CHEVRONS_PARAMETER = n"ShowChevrons";

	void Initialize( UAS_PriorityMessageData data ) override
	{
		Super::Initialize( data );

		if ( IsValid( data ) )
		{
			backgroundMaterial = background.GetDynamicMaterial();
			if ( IsValid( backgroundMaterial ) )
			{
				bool useComplexStroke = false;
				bool showChevrons	  = false;
				FName mpcColorName;

				switch ( data.theme )
				{
					case EPriorityMessageTheme::ENEMY_SHIELDBREAKER_ACTION:
						useComplexStroke = true;
						fallthrough;
					case EPriorityMessageTheme::ENEMY_ACTION:
						mpcColorName = CommonUiColorMpcNames::ENEMY_HIGHLIGHT;
						break;
					case EPriorityMessageTheme::FRIENDLY_SHIELDBREAKER_ACTION:
						useComplexStroke = true;
						fallthrough;
					case EPriorityMessageTheme::FRIENDLY_ACTION:
						mpcColorName = CommonUiColorMpcNames::FRIENDLY_HIGHLIGHT;
						break;
					case EPriorityMessageTheme::NEUTRAL_SHIELDBREAKER_ACTION:
						useComplexStroke = true;
						mpcColorName	 = CommonUiColorMpcNames::GOLD;
						break;
					case EPriorityMessageTheme::NEUTRAL_ACTION:
						mpcColorName = CommonUiColorMpcNames::WHITE;
						break;
					case EPriorityMessageTheme::DEFENDER_WIN:
						fallthrough;
					case EPriorityMessageTheme::ATTACKER_WIN:
						mpcColorName = CommonUiColorMpcNames::POSITIVE;
						break;
					case EPriorityMessageTheme::DEFENDER_LOSE:
						fallthrough;
					case EPriorityMessageTheme::ATTACKER_LOSE:
						mpcColorName = CommonUiColorMpcNames::NEGATIVE;
						break;
					case EPriorityMessageTheme::OVERTIME_ACTION:
						showChevrons = true;
						mpcColorName = CommonUiColorMpcNames::OVERTIME;
						break;
					default:
						break;
				}

				backgroundMaterial.SetScalarParameterValue( COMPLEX_STROKE_PARAMETER, MaterialParameter::GetTrueFalseFloat( useComplexStroke ) );
				backgroundMaterial.SetVectorParameterValue( MaterialParameter::ACCENT_COLOR, GetCommonUiMpcColor( mpcColorName ) );
				backgroundMaterial.SetScalarParameterValue( SHOW_CHEVRONS_PARAMETER, MaterialParameter::GetTrueFalseFloat( showChevrons ) );
				background.SetBrushFromMaterial( backgroundMaterial );
			}

			SetHeader( data.header );
			SetSubheader( data.subheader );
		}
	}

	UFUNCTION( BlueprintOverride )
	void Tick( FGeometry MyGeometry, float InDeltaTime )
	{
		if ( !IsValid( messageData ) )
			return;

		if ( !messageData.endTimeMs.IsSet() )
			return;

		int timeRemainingMs = messageData.endTimeMs.GetValue() - GetGameTimeMS();
		FText formattedTime = Text::AsTimespan_Timespan( FTimespan::FromMilliseconds( timeRemainingMs ) );
		SetSubheader( FText::Format( messageData.subheader, formattedTime ) );
	}

	void SetHeader( FText headerText ) override
	{
		Super::SetHeader( headerText );
		header.SetText( headerText );
	}

	void SetSubheader( FText subheaderText ) override
	{
		Super::SetSubheader( subheaderText );
		subheader.SetText( subheaderText );
	}
}