
UAS_RaidMessagingTrainingHud GetTrainingRaidHud()
{
	UAS_RaidMessagingManager grm = GetRaidMessagingManager();
	if( !IsValid(grm) )
		return nullptr;

	UAS_RaidMessagingTrainingHud result = Cast<UAS_RaidMessagingTrainingHud>(grm.GetRaidHud());
	return result;
}

UCLASS( Abstract )
class UAS_RaidMessagingTrainingHud : UAS_RaidMessagingHud
{
	UPROPERTY( NotVisible, BindWidget )
	private UCommonTextBlock TrainingProgressTitle;

	UPROPERTY( NotVisible, BindWidget )
	private UProgressBar TrainingProgress;

	UPROPERTY( NotVisible, Transient, meta=(BindWidgetAnim))
	private UWidgetAnimation DisableScores;

	UPROPERTY( NotVisible, Transient, meta=(BindWidgetAnim))
	private UWidgetAnimation HighlightEnemyScore;
	
	UPROPERTY( NotVisible, Transient, meta=(BindWidgetAnim))
	private UWidgetAnimation ResetEnemyScore;


	private float32 goalProgressFrac = 0;
	private float32 currProgressFrac = 0;
	private int tickTime;
	private FTimerHandle tickHandle;

	private float32 INTERP_SPEED 	= 0.25;

	UFUNCTION(BlueprintOverride)
	void Construct()
	{
		Super::Construct();
		TrainingProgress.SetPercent(0);
	}

	void SetTrainingProgressTitle(FText text)
	{
		TrainingProgressTitle.SetText(text);
	}

	void SetTrainingProgressBarSafe( float32 progressFrac )
	{
		if ( progressFrac < goalProgressFrac )
			return;
		
		SetTrainingProgressBar( progressFrac );
	}

	void SetTrainingProgressBar( float32 progressFrac )
	{
		ScriptAssert( progressFrac >= goalProgressFrac, "Progress can only increase, not decrease" );

		tickTime 			= GetGameTimeMS();
		goalProgressFrac 	= progressFrac;
		
		System::ClearAndInvalidateTimerHandle(tickHandle);
		tickHandle = System::SetTimerForNextTick( this, "_AnimateProgressBar" );
	}

	UFUNCTION( NotBlueprintCallable )
	private void _AnimateProgressBar()
	{
		float32 deltaTime 	= TO_SECONDS( GetGameTimeMS() - tickTime );
		tickTime 			= GetGameTimeMS();

		currProgressFrac = Math::Min( Math::FInterpConstantTo( currProgressFrac, goalProgressFrac, deltaTime, INTERP_SPEED ), goalProgressFrac );
		TrainingProgress.SetPercent(currProgressFrac);

		if ( currProgressFrac <goalProgressFrac )
			tickHandle = System::SetTimerForNextTick( this, "_AnimateProgressBar" );
	}

	bool scoreDisabled = false;
	void DisableScoreBoard()
	{
		if ( scoreDisabled )
			return;
		scoreDisabled = true;

		float32 startTime = GetAnimationCurrentTime(DisableScores);
		PlayAnimation( DisableScores, startTime );
	}

	void EnableScoreBoard()
	{
		if ( !scoreDisabled )
			return;
		scoreDisabled = false;

		float32 startTime = GetAnimationCurrentTime(DisableScores);
		PlayAnimation( DisableScores, startTime, 1, EUMGSequencePlayMode::Reverse );
	}

	void HighlightEnemyScoreBoardWidget()
	{
		Thread( this, n"_HighlightEnemyScoreboardWidgetThread" );
	}

	UFUNCTION()
	void _HighlightEnemyScoreboardWidgetThread(UNCCoroutine co)
	{				
		PlayAnimation(HighlightEnemyScore);

		FakeUpdateEnemyHealthBar(65, 65, 100);

		co.Wait( 2.0 );
		int pendingScore	 	= GetTeamScore( enemyTeamHealth.trackedTeamId );
		int score 				= 100;
		int trackedScoreLimit 	= 100;
		FakeUpdateEnemyHealthBar(pendingScore, score, trackedScoreLimit);

		co.Wait( 2.5 );
		enemyTeamHealth.UpdateBar();	//reset back to real
		//FakeUpdateEnemyHealthBar(65, 65, 100);
				
		PlayAnimation(ResetEnemyScore);
	}

	void FakeUpdateEnemyHealthBar(int pendingScore, int score, int trackedScoreLimit)
	{
		float pendingPercent	= trackedScoreLimit > 0 ? Math::Max( float( pendingScore ) / trackedScoreLimit, 0 ) : 0.0f;
		float actualPercent	 	= trackedScoreLimit > 0 ? Math::Max( float( score ) / trackedScoreLimit, 0 ) : 0.0f;

		if ( IsValid( enemyTeamHealth.healthMaterial ) )
		{
			if ( score <= pendingScore || pendingScore < 0 )
			{
				enemyTeamHealth.healthMaterial.SetScalarParameterValue( MaterialParameter::LERP_ALPHA, actualPercent );
				enemyTeamHealth.healthMaterial.SetScalarParameterValue( MaterialParameter::DAMAGE_TAIL_LERP_ALPHA, pendingPercent );
				enemyTeamHealth.healthMaterial.SetScalarParameterValue( MaterialParameter::ALLOW_DAMAGE_TAIL_PARAMETER, MaterialParameter::GetTrueFalseFloat( score != pendingScore || pendingScore < 0 ) );
			}
			else
			{
				enemyTeamHealth.healthMaterial.SetScalarParameterValue( MaterialParameter::LERP_ALPHA, pendingPercent );
				enemyTeamHealth.healthMaterial.SetScalarParameterValue( MaterialParameter::ALLOW_DAMAGE_TAIL_PARAMETER, MaterialParameter::GetTrueFalseFloat( true ) );
				enemyTeamHealth.healthMaterial.SetScalarParameterValue( MaterialParameter::DAMAGE_TAIL_LERP_ALPHA, actualPercent );
			}
		}
	}


}