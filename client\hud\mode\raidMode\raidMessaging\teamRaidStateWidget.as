UCLASS( Abstract )
class UAS_TeamRaidState : UNC_DisplayWidget
{
	default StateOnConstruction = EDisplayConstructVisibility::Hidden;
	default HideVisibility		= ESlateVisibility::Collapsed;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage progress;

	UPROPERTY( EditInstanceOnly, BlueprintHidden )
	private bool trackEnemyTeam = false;

	UPROPERTY( EditInstanceOnly, BlueprintHidden )
	private UMaterialInterface teamMaterial;

	UPROPERTY( EditInstanceOnly, BlueprintHidden )
	private float lowThreshold = 0.4f;

	private int trackedTeamId = GameConst::INDEX_NONE;
	private UMaterialInstanceDynamic raidStateMaterial;

	private const int ENEMY_TEAM_COLUMN = 0;
	private const int LOCAL_TEAM_COLUMN = 2;
	private const EHorizontalAlignment RIGHT_ALIGNED = EHorizontalAlignment::HAlign_Right;
	private const EHorizontalAlignment LEFT_ALIGNED = EHorizontalAlignment::HAlign_Left;
	private const FName RAID_STATE_PARAMETER = n"RaidState";
	private const int IS_RAIDING = -1;

	UFUNCTION( BlueprintOverride )
	void PreConstruct( bool isDesignTime )
	{
		if ( IsValid( teamMaterial ) )
		{
			progress.SetBrushFromMaterial( teamMaterial );
		}
	}

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		raidStateMaterial = CreateDynamicMaterialFromImageBrush( progress );
		if ( IsValid( raidStateMaterial ) )
		{
			progress.SetBrushFromMaterial( raidStateMaterial );
		}
	}

	void SetTrackedTeamId( int teamId )
	{
		if ( trackedTeamId != teamId )
		{
			trackedTeamId = teamId;
		}
	}

	UFUNCTION( BlueprintOverride )
	void OnShowStart()
	{
		// If we aren't raiding, we don't need to do anything
		bool isRaiding = IsTeamRaiding( trackedTeamId );
		if ( isRaiding )
		{
			if ( IsValid( raidStateMaterial ) )
			{
				// Flip the state on the raid state material to show the correct indicator
				raidStateMaterial.SetScalarParameterValue( RAID_STATE_PARAMETER, IS_RAIDING );
			}

			UpdateTeamProgress();
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void UpdateTeamProgress()
	{
		if ( IsTeamRaiding( trackedTeamId ) )
		{
			AAS_ShieldBreaker breaker		 = GetBreacherForTeam( trackedTeamId );
			AAS_RaidDomeShield otherTeamDome = GetRaidDomeShieldForTeam( GetOtherTeam( trackedTeamId ) );

			if ( IsValid( breaker ) )
			{
				// Calculate the teams raid brogress by using the cooldown timer and modifying it by the amount of lives left
				float totalDuration	  = breaker.GetBreacherEnergyTotalDuration();
				int startTimeMs		  = breaker.net_breacherCountdownStartTime - breaker.GetUsedLivesTimeMS();
				float32 elapsedTimeMS = GetTimeMilliseconds() - startTimeMs;
				float32 elapsedTime	  = TO_SECONDS( int( elapsedTimeMS ) );
				float timeLeft		  = Math::Max( totalDuration - elapsedTime, 0.0f );
				float newProgress	  = Math::GetMappedRangeValueClamped( FVector2D( 0, totalDuration ), FVector2D( 0, 1 ), timeLeft );

				if ( IsValid( raidStateMaterial ) )
				{
					float lastProgress = raidStateMaterial.GetScalarParameterValue( MaterialParameter::LERP_ALPHA );
					if ( !Math::IsNearlyEqual( lastProgress, newProgress ) )
					{
						raidStateMaterial.SetScalarParameterValue( MaterialParameter::LERP_ALPHA, newProgress );
					}
				}
			}

			System::SetTimerForNextTick( this, "UpdateTeamProgress" );
		}
	}
}