UCLASS( Abstract )
class UAS_MaximapWidget : UAS_MapWidget
{
	default zoomDuration	 = 0.5f;
	default RotationBehavior = EMapRotationBehavior::NorthUp;
	default PositionBehavior = EMapPositionBehavior::MaintainInput;
	default mapSize			 = FVector2D( 1192.0f, 670.0f );

	UPROPERTY( EditInstanceOnly, BlueprintHidden )
	private FFloatInterval zoomLevels;
	default zoomLevels.Min = 0.3f;
	default zoomLevels.Max = 2.0f;

	private FVector2D offset;
	private FVector2D startingPosition;
	private bool mapHasFocus = false;
	private FTimerHandle controllerZoomHandle;
	private bool listenForLeftTrigger = false;
	private bool listenForRightTrigger = false;
	private TArray<AAS_LocationTrigger> cachedLocationTriggers;
	private TOptional<FVector2D> optDragResult;

	UPROPERTY()
	FMaxiMapInfo MaxiMapInfo;
	
	UFUNCTION(BlueprintOverride)
	void Construct()
	{
		Super::Construct();
		MaxiMapInfo = UNCUtils::GetMaximapInfoForLevel(GetOwningPlayer());
	}

	UFUNCTION( BlueprintOverride )
	void OnShowStart()
	{
		AAS_BaseHUD hud = GetLocalBaseHUD();
		if ( IsValid( hud ) )
		{
			// TODO @jmccarty: Replace this with enhanced input
			hud.OnKeyDown.AddUFunction( this, n"OnKeyDownEvent" );
		}

		APlayerController pc = GetOwningPlayer();
		if ( IsValid( pc ) && GetCurrentInputType() != ECommonInputType::MouseAndKeyboard )
		{
			// On first open, snap the mouse to the center of the screen
			pc.SetMouseLocation( int( WidgetLayout::GetViewportSize().X ) / 2, int( WidgetLayout::GetViewportSize().Y ) / 2 );
		}

		// Ensure the right map is shown because the player may have switched last time the maximap was opened
		ShowCurrentMap();

		// When the map first opens, centering on the player doesn't work so I am going to do this for now
		System::SetTimerForNextTick( this, "OnCenterMapDelayed" );
	}

	UFUNCTION( BlueprintOverride )
	void OnShowEnd()
	{
		ShowHideLocationTriggers( true );
	}

	UFUNCTION( BlueprintOverride )
	void OnHideStart()
	{
		AAS_BaseHUD hud = GetLocalBaseHUD();
		if ( IsValid( hud ) )
		{
			// TODO @jmccarty: Replace this with enhanced input
			hud.OnKeyDown.Unbind( this, n"OnKeyDownEvent" );
		}

		UNCMapMarkerWidget mapMarker = GetMapMarkerWidgetForActor( Client_GetLocalASPawn() );
		if ( IsValid( mapMarker ) )
		{
			mapMarker.SetMaterialScalarValue( n"MaximapOpened", MaterialParameter::GetTrueFalseFloat( false ) );
		}
	}

	UFUNCTION( BlueprintOverride )
	void OnHideEnd()
	{
		ShowHideLocationTriggers( false );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnKeyDownEvent( FKey key )
	{
		if ( key == EKeys::MiddleMouseButton || key == EKeys::Gamepad_RightShoulder )
		{
/*				-- Disabled until we can work out the math
			UCl_PingManager pingManager = ClPingManager();
			if ( IsValid( pingManager ) )
			{
				FVector2D mapPos   = GetMousePositionRelativeToWidget( this );
				FVector2D worldPos = GetWorldPositionFromNormalizedPos( mins, maxs, mapPos );
				pingManager.SendMapPingAtLocation( EPlayerPingType::LOCATION, worldPos );
			}
*/			
		}
		else if ( key == EKeys::PageDown )
		{
			float currentNorth = GetRotation() * 360.0f;
			currentNorth += 1.0f;
			SetNorthAngle(currentNorth);
			LogMapInfo();
		}
		else if ( key == EKeys::Delete )
		{
			float currentNorth = GetRotation() * 360.0f;
			currentNorth -= 1.0f;
			SetNorthAngle(currentNorth);
			LogMapInfo();
		}
		else if ( key == EKeys::Home )
		{
			float cZoom = GetZoom();
			cZoom += 0.02;
			SetZoom(cZoom);
			LogMapInfo();
		}
		else if ( key == EKeys::End )
		{
			float cZoom = GetZoom();
			cZoom -= 0.02;
			SetZoom(cZoom);
			LogMapInfo();
		}
		else if ( key == EKeys::Up)
		{
			MaxiMapInfo.MaximapCenterOffset.Y -= 1000;
			FVector2D newCenter = FVector2D(MaxiMapInfo.MaximapCenterOffset.X, MaxiMapInfo.MaximapCenterOffset.Y);
			RecenterMap(newCenter);
			LogMapInfo();
		}
		else if ( key == EKeys::Down)
		{
			MaxiMapInfo.MaximapCenterOffset.Y += 1000;
			FVector2D newCenter = FVector2D(MaxiMapInfo.MaximapCenterOffset.X, MaxiMapInfo.MaximapCenterOffset.Y);
			RecenterMap(newCenter);
			LogMapInfo();
		}
		else if ( key == EKeys::Left)
		{
			MaxiMapInfo.MaximapCenterOffset.X -= 1000;
			FVector2D newCenter = FVector2D(MaxiMapInfo.MaximapCenterOffset.X, MaxiMapInfo.MaximapCenterOffset.Y);
			RecenterMap(newCenter);
			LogMapInfo();
		}
		else if ( key == EKeys::Right)
		{
			MaxiMapInfo.MaximapCenterOffset.X += 1000;
			FVector2D newCenter = FVector2D(MaxiMapInfo.MaximapCenterOffset.X, MaxiMapInfo.MaximapCenterOffset.Y);
			RecenterMap(newCenter);
			LogMapInfo();
		}

		else if ( key == EKeys::M || key == EKeys::Gamepad_Special_Left )
		{
			// This is gross, see the comment in UAS_HUD_MaximapInputManager
			System::SetTimerForNextTick( this, "ProcessMapCloseOnNextTick" );
		}
	}
	UFUNCTION( NotBlueprintCallable )
	void LogMapInfo()
	{
		FVector mapCenter = MaxiMapInfo.MaximapCenterOffset;
		float northAngle = GetNorthAngle();
		float zoom = GetZoom();
		Log(f"North Angle = {northAngle}  Zoom = {zoom}  Offset = {mapCenter}");
	}
	UFUNCTION( NotBlueprintCallable )
	void ProcessMapCloseOnNextTick()
	{
		UNCUIManager uiManager = GetASUIManager();
		if ( IsValid( uiManager ) )
		{
			uiManager.CloseIfActive( UAS_MaximapMenu::StaticClass() );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void ZoomIn()
	{
		float currentZoom = GetCurrentUserZoom();
		LerpToZoomLevel( Math::Clamp( currentZoom - 0.02f, zoomLevels.Min, zoomLevels.Max ) );
		controllerZoomHandle = System::SetTimerForNextTick( this, "ZoomIn" );
	}

	UFUNCTION( NotBlueprintCallable )
	private void ZoomOut()
	{
		float currentZoom = GetCurrentUserZoom();
		LerpToZoomLevel( Math::Clamp( currentZoom + 0.02f, zoomLevels.Min, zoomLevels.Max ) );
		controllerZoomHandle = System::SetTimerForNextTick( this, "ZoomOut" );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnCenterMapDelayed()
	{
		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( IsValid( player ) )
		{
			// Center the map on the player to start
			RecenterMap( player.GetActorLocation().ToVector2D() );

			UNCMapMarkerWidget mapMarker = GetMapMarkerWidgetForActor( player );
			if ( IsValid( mapMarker ) )
			{
				mapMarker.SetMaterialScalarValue( n"MaximapOpened", MaterialParameter::GetTrueFalseFloat( true ) );
			}
		}

		SetZoom(MaxiMapInfo.MaximapZoom > 0.0f ? MaxiMapInfo.MaximapZoom : 1.0f);
		SetNorthAngle(MaxiMapInfo.MaximapNorthAngle);

		if (!MaxiMapInfo.MaximapCenterOffset.IsNearlyZero())
		{
			RecenterMap(FVector2D(MaxiMapInfo.MaximapCenterOffset.X,MaxiMapInfo.MaximapCenterOffset.Y));
		}
	}

	UFUNCTION( BlueprintOverride )
	void OnMapMarkerCreated( UNCMapMarkerWidget Widget, AActor actor ) final
	{
		Super::OnMapMarkerCreated( Widget, actor );

		AAS_LocationTrigger asLocation = Cast<AAS_LocationTrigger>( actor );
		if ( IsValid( asLocation ) && !cachedLocationTriggers.Contains( asLocation ) )
		{
			// We want to cache all of the location marker actors so we can turn them on/off easily
			cachedLocationTriggers.Add( asLocation );
		}
	}

	private void ShowHideLocationTriggers( bool show )
	{
		for ( AAS_LocationTrigger trigger : cachedLocationTriggers )
		{
			if ( IsValid( trigger ) )
			{
				if ( show )
				{
					trigger.mapMarkerComponent.ShowMarkers();
				}
				else
				{
					trigger.mapMarkerComponent.HideMarkers();
				}
			}
		}
	}

	private void ShowCurrentMap()
	{
		SwapToWorldMap();
	}

	void SwapToBaseMap() override
	{
		SwapToWorldMap();
	}

	void SwapToWorldMap() override
	{
		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( !IsValid( player ) || !baseMapActive )
			return;

		// Reset the map back to the world
		GetMinsAndMaxsOfWorld( mins, maxs );

		SetMap( MaxiMapInfo.Maximap.Get(), mins, maxs, MaxiMapInfo.MaximapNorthAngle);
		SetZoom(MaxiMapInfo.MaximapZoom);
		baseMapActive = false;

		// Show the base markers but hide the objectives when in world
		ShowHideAllBaseMarkers( true );
		ShowHideAllBaseVendorMarkers( false );
		ShowHideAllObjectiveMarkers( false );
	}
}