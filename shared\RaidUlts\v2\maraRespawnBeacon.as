UCLASS()
class AAS_MaraRespawnZone : AAS_BaseRespawnBeaconWithLives
{
	UPROPERTY( DefaultComponent )
	UDecalComponent decal;
	UPROPERTY( DefaultComponent )
	UNiagaraComponent fx;
	UPROPERTY( DefaultComponent )
	UNiagaraComponent spawnFX;
	UPROPERTY( DefaultComponent )
	UNiagaraComponent interactFX;
	UPROPERTY( DefaultComponent )
	USkeletalMeshComponent tendrils;

	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<AAS_ClientSideRespawnCamera> respawnCameraClass;
	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<AAS_MaraRespawnPreview> previewClass;
	UPROPERTY( EditDefaultsOnly )
	float timeAlive;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset deploy1P;
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset deploy3P;
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset sustain1p;
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset sustain3p;
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset dismiss1p;
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset dismiss3p;
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset useComponentSoundAsset;

	UPROPERTY( EditDefaultsOnly )
	UAnimMontage spawnAnim;
	UPROPERTY( EditDefaultsOnly )
	UAnimMontage idleAnim;
	UPROPERTY( EditDefaultsOnly )
	UAnimMontage destroyAnim;

	UPROPERTY( EditDefaultsOnly )
	UAnimMontage destroy1pAnim;
	UPROPERTY( EditDefaultsOnly )
	UAnimMontage destroy3pAnim;

	UPROPERTY( EditDefaultsOnly )
	UNiagaraSystem spawnInstantFX;
	UPROPERTY( EditDefaultsOnly )
	UMaterialInstance enemyTendrilMat;

	AAS_PlayerEntity ownerPlayer;
	TArray<AAS_MaraRespawnPreview> existingPreviews;
	TArray<ASoundActor> sustainActors;
	ASoundActor sustainUseSound;
	UMaterialInstanceDynamic dynDecalMat;

	UPROPERTY()
	FNCNetBool net_isBeingDestroyed;

	UPROPERTY()
	FNCNetBool net_isPlayerSpawning;

	UPROPERTY( EditDefaultsOnly )
	float32 fadeTime;
	default fadeTime = 2.0;

	UPROPERTY( DefaultComponent )
	UUsableItemComponent useComponent;
	default useComponent.HasExtendedUseAction = true;

	UPROPERTY( DefaultComponent )
	UAS_EntityHighlightComponent highlightManager;
	default highlightManager.pinWidgetName = n"PinnableWidget_Detection_MaraRespawnZone";

	default beaconType = ERespawnBeaconType::ENDLESS_NIGHT;
	FNCCoroutineSignal onDestroyedSignal;

	UFUNCTION( BlueprintOverride )
	void ServerBeginPlay()
	{
		ownerPlayer			= GetOwnerPlayer();
		AAS_BaseSystem base = GetBaseForTeam( ownerPlayer.GetTeam() );
		if ( IsValid( base ) )
		{
			this.baseOwnershipComp.SetTeam( base.net_teamID );
			this.OnBaseInitialized( base.net_teamID );
		}

		Super::ServerBeginPlay();

		Server_EmitSoundAtLocation_1P3P( deploy1P, deploy3P, this.GetActorLocation(), GetOwnerPlayer() );
		sustainActors = Server_EmitSoundOnEntity_1P3P_ReturnEntities( sustain1p, sustain3p, this, GetOwnerPlayer() );

		useComponent.OnUsed_Extended.AddUFunction( this, n"OnUsed_Extended" );
		useComponent.OnExtendedUseStarted.AddUFunction( this, n"OnExtendedUseStarted" );
		useComponent.OnExtendedUseEnded.AddUFunction( this, n"OnExtendedUseEnded" );

		Thread( this, n"Thread_DelayedDestroyZone" );

		this.SV_ChangeProtectedRespawnStatus( true );

		// only call this after everything else is initialized
		ScriptCallbacks().server_OnMaraRespawnZoneCreated.Broadcast( this );

		AAS_TeamStateManager_RaidMode atkTeamManager = GetTeamStateManager_RaidMode( ownerPlayer.GetTeam() );
		if ( atkTeamManager.IsRespawnLocked() )
			atkTeamManager.UnlockOnlyPendingRespawns( this );
	}

	UFUNCTION( BlueprintOverride )
	void ClientBeginPlay()
	{
		Super::ClientBeginPlay();
		net_isBeingDestroyed.OnReplicated().AddUFunction( this, n"OnIsBeingDestroyedChanged" );
		net_isPlayerSpawning.OnReplicated().AddUFunction( this, n"OnIsPlayerSpawningChanged" );

		bool isFriendly = IsFriendly( GetOwnerPlayer().GetTeam(), Client_GetLocalASPawn().GetTeam() );

		fx.SetFloatParameter( n"TeamColorFloat", isFriendly ? 0 : 1 );
		spawnFX.SetFloatParameter( n"TeamColorFloat", isFriendly ? 0 : 1 );

		UNiagaraComponent instantFX = Client_SpawnEffectAtLocation_OneShot( spawnInstantFX, GetActorLocation(), GetActorRotation() );
		instantFX.SetFloatParameter( n"TeamColorFloat", isFriendly ? 0 : 1 );

		dynDecalMat = decal.CreateDynamicMaterialInstance();
		dynDecalMat.SetScalarParameterValue( n"TeamColorFloat", isFriendly ? 0 : 1 );

		if ( isFriendly )
			interactFX.SetVisibility( false );
		if ( !isFriendly )
			tendrils.SetMaterial( 0, enemyTendrilMat );

		tendrils.PlayAnimation( spawnAnim, false );
		System::SetTimer( this, n"SwapToIdleAnim", spawnAnim.GetPlayLength(), false );

		useComponent.GetExtendedUseHintDelegate.BindUFunction( this, n"UseHint_RespawnBeacon" );

		Thread( this, n"Thread_ActivateDecal" );

		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			scriptCallbacks.client_OnMaraSpawnBeaconPlaced.Broadcast( this );
		}
	}

	UFUNCTION()
	void Thread_ActivateDecal( UNCCoroutine co )
	{
		float32 timeToFadeIn_float = 0.5;
		int timeToFadeIn		   = TO_MILLISECONDS( timeToFadeIn_float );
		int startTime			   = GetGameTimeMS();
		int endTime				   = startTime + timeToFadeIn;
		while ( GetGameTimeMS() < endTime )
		{
			int currTime = GetGameTimeMS();
			float perc	 = float( currTime - startTime ) / float( timeToFadeIn );
			dynDecalMat.SetScalarParameterValue( n"activate", perc );
			co.Wait( 0.05 );
		}
	}

	UFUNCTION()
	void SwapToIdleAnim()
	{
		tendrils.PlayAnimation( idleAnim, true );
	}

	void SharedBeginPlay() override
	{
		Super::SharedBeginPlay();

		useComponent.GetUsabilityStateDelegate.BindUFunction( this, n"GetUsabilityState" );
		useComponent.GetUseDurationDelegate.BindUFunction( this, n"GetUseDuration" );
	}

	AAS_ShieldBreaker breacher;
	void SetBreacher( AAS_ShieldBreaker inBreacher )
	{
		breacher = inBreacher;
		breacher.OnEndPlay.AddUFunction( this, n"OnBreacherEndPlay" );
	}

	UFUNCTION()
	private void OnBreacherEndPlay( AActor Actor, EEndPlayReason EndPlayReason )
	{
		// DestroyZone();
	}

	UFUNCTION()
	private void Thread_DelayedDestroyZone( UNCCoroutine co )
	{
		co.Wait( timeAlive );
		DestroyZone();
	}

	UFUNCTION()
	private void Thread_FinishDestroyZone( UNCCoroutine co )
	{
		// delay destruction until all players are done respawning
		if ( net_isPlayerSpawning )
		{
			while ( existingPreviews.Num() > 0 )
				co.Wait( this, onPlayerRespawned );
		}

		net_isBeingDestroyed.SetNetValue( true );

		for ( auto s : sustainActors )
			s.Destroy();
		Server_EmitSoundAtLocation_1P3P( dismiss1p, dismiss3p, GetActorLocation(), ownerPlayer );

		co.Wait( fadeTime );

		onDestroyedSignal.Emit();
		Destroy();
	}

	UFUNCTION()
	void DestroyZone()
	{
		Thread( this, n"Thread_FinishDestroyZone" );
	}

	UFUNCTION()
	void OnIsBeingDestroyedChanged( bool old, bool new )
	{
		if ( !new )
			return;

		Thread( this, n"Thread_FadeOut" );
		tendrils.PlayAnimation( destroyAnim, false );
		interactFX.Deactivate();
	}

	UFUNCTION()
	FInputUsabilityStates GetUsabilityState( const UUsableItemComponent Component, const ANCPlayerCharacter playerUser )
	{
		FInputUsabilityStates usabilityStates;
		if ( playerUser.GetTeam() == baseOwnershipComp.GetTeam() )
		{
			return usabilityStates;
		}
		if ( net_isBeingDestroyed )
		{
			return usabilityStates;
		}
		usabilityStates.NormalExtendedInput = EUsabilityState::USABLE;
		return usabilityStates;
	}

	bool CanSpawnPlayer() override
	{
		return Super::CanSpawnPlayer() && ( !net_isBeingDestroyed );
	}

	UFUNCTION()
	void OnIsPlayerSpawningChanged( bool old, bool new )
	{
		if ( !IsValid( dynDecalMat ) )
			return;

		dynDecalMat.SetScalarParameterValue( n"PortalActive", new ? 1 : 0 );
	}

	void BeaconRanOutOfLives() override
	{
		DestroyZone();
	}

	UFUNCTION()
	void Thread_FadeOut( UNCCoroutine co )
	{
		fx.Deactivate();
		if ( !IsValid( dynDecalMat ) )
			dynDecalMat = decal.CreateDynamicMaterialInstance();

		int startTime = GetGameTimeMS();
		int endTime	  = GetGameTimeMS() + TO_MILLISECONDS( float32( fadeTime ) );
		while ( GetGameTimeMS() < endTime )
		{
			float fade = 1.0f - ( float( GetGameTimeMS() - startTime ) / float( endTime - startTime ) );
			dynDecalMat.SetScalarParameterValue( n"OpacityMultiply", fade );
			dynDecalMat.SetScalarParameterValue( n"activate", fade );
			co.Wait( 0.025 );
		}
	}

	UFUNCTION( BlueprintOverride )
	void EndPlay( EEndPlayReason EndPlayReason )
	{
		Super::EndPlay( EndPlayReason );

		if ( IsServer() )
		{
			for ( auto p : existingPreviews )
			{
				if ( IsValid( p ) )
				{
					p.ownerZone = nullptr;
					p.Destroy();
				}
			}

			if ( IsValid( sustainUseSound ) )
				sustainUseSound.Destroy();
		}
		else
		{
			UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
			if ( IsValid( scriptCallbacks ) )
			{
				scriptCallbacks.client_OnMaraSpawnBeaconDestroyed.Broadcast( this );
			}
		}
	}

	void PlayerRespawnRequested( AAS_PlayerEntity player ) override
	{
		AAS_MaraRespawnPreview p = Cast<AAS_MaraRespawnPreview>( Server_SpawnEntity( previewClass, player, GetActorLocation() + ( FVector::UpVector * 25 ), FRotator() ) );
		p.SV_Initialize( this );
		existingPreviews.Add( p );

		net_isPlayerSpawning.SetNetValue( true );

		Super::PlayerRespawnRequested( player );
	}

	FNCCoroutineSignal onPlayerRespawned;

	void PlayerRespawned( AAS_MaraRespawnPreview p )
	{
		if ( existingPreviews.Contains( p ) )
			existingPreviews.Remove( p );

		if ( existingPreviews.Num() <= 0 )
			net_isPlayerSpawning.SetNetValue( false );

		onPlayerRespawned.Emit();
	}

	UFUNCTION()
	float32 GetUseDuration( const UUsableItemComponent Component, const ANCPlayerCharacter playerUser,
							bool isNormalUseInput )
	{
		if ( playerUser.GetTeam() == baseOwnershipComp.GetTeam() )
			return 0.0;
		return 3.0;
	}

	UFUNCTION()
	FText UseHint_RespawnBeacon( const UUsableItemComponent Component, const ANCPlayerCharacter playerUser, const EUsabilityState UsabilityState )
	{
		return GetLocalizedText( Localization::Character_Mara, f"mara_beacon_purge" );
	}

	UFUNCTION( NotBlueprintCallable )
	void OnExtendedUseStarted( UUsableItemComponent component, ANCPlayerCharacter playerUser, bool isNormalUseInput )
	{
		if ( IsServer() )
		{
			if ( !IsValid( sustainUseSound ) )
				sustainUseSound = Server_EmitSoundAtLocation_ReturnEntity( useComponentSoundAsset, GetActorLocation() );

			FScriptedAnimPlayerSettings playerSettings;
			playerSettings.WeaponsEnabled	 = false;
			playerSettings.TurnSlowIntensity = 1.0;
			playerSettings.MoveSlowIntensity = 0.5;

			AAS_PlayerEntity asUser = Cast<AAS_PlayerEntity>( playerUser );
			asUser.PlayNetScriptedMontage1P3PWithPlayerSettings( playerSettings, destroy1pAnim, destroy3pAnim );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void OnExtendedUseEnded( UUsableItemComponent usedComponent, ANCPlayerCharacter playerUser, bool isNormalUse, EUseInterruptedReason interruptReason )
	{
		if ( IsValid( sustainUseSound ) )
			sustainUseSound.Destroy();

		if ( IsServer() )
		{
			AAS_PlayerEntity asUser = Cast<AAS_PlayerEntity>( playerUser );
			asUser.StopScriptedMontage();
		}
	}

	UFUNCTION()
	void OnUsed_Extended( UUsableItemComponent component, ANCPlayerCharacter playerUser )
	{
		if ( IsServer() )
		{
			useComponent.DisableUsable();
			DestroyZone();
		}
	}
}

UCLASS()
class AAS_MaraRespawnPreview : ANCDefaultActor
{
	UPROPERTY( DefaultComponent, RootComponent )
	USceneComponent root;
	UPROPERTY( DefaultComponent )
	UNiagaraComponent fx;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset sustain1p;
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset sustain3p;
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset spawn1p;
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset spawn3p;

	UPROPERTY( EditDefaultsOnly )
	UNiagaraSystem spawnFX;

	UPROPERTY( EditDefaultsOnly )
	float timeAlive = 30.0f;

	AAS_MaraRespawnZone ownerZone;
	AAS_PlayerEntity ownerPlayer;
	FNCCoroutineSignal spawnSignal;
	int moveSilentlyStatusHandle;
	bool hasSetupPresentation = false;

	FScriptDelegateHandle fireInputHandle;
	TArray<ASoundActor> sustainActors;

	UFUNCTION( BlueprintOverride )
	void BeginPlay()
	{
		ownerPlayer = Cast<AAS_PlayerEntity>( GetOwnerPlayer() );
		if ( IsServer() && IsValid( ownerPlayer ) )
		{
			SetNCNetOwnerId( ownerPlayer.GetEntityId() );
			System::SetTimer( this, n"DestroyAfterTime", timeAlive, false );
		}

		if ( IsClient() )
		{
			ClientCallbacks().OnPawnRespawned.AddUFunction( this, n"SetupPresentation" );
		}
		else
		{
			SetupPresentation( ownerPlayer );
		}
	}

	UFUNCTION( BlueprintOverride )
	void EndPlay( EEndPlayReason EndPlayReason )
	{
		if ( IsValid( ownerZone ) )
			ownerZone.PlayerRespawned( this );

		if ( IsServer() )
		{
			ownerPlayer.ClearStatusEffect( moveSilentlyStatusHandle );
		}

		TeardownPresentation();
	}

	UFUNCTION()
	void DestroyAfterTime()
	{
		Destroy();
	}

	UFUNCTION()
	void OnFirePressed( ANCPlayerCharacter owner )
	{
		Destroy();
	}

	UFUNCTION()
	void SV_Initialize( AAS_MaraRespawnZone zone )
	{
		ownerZone = zone;
		Thread( this, n"Thread_DestroyOnPlayerLeaving" );
	}

	UFUNCTION()
	private void Thread_DestroyOnPlayerLeaving( UNCCoroutine co )
	{
		if ( !IsValid( ownerZone ) || !IsValid( ownerPlayer ) )
			return;

		co.EndOnDestroyed( ownerZone );
		co.EndOnDestroyed( ownerPlayer );

		if ( IsServer() )
		{
			moveSilentlyStatusHandle = ownerPlayer.AddStatusEffect( GameplayTags::StatusEffect_MoveSilently, 1.0f, 0.f, 0.f, 0.f );
		}

		const float32 WAIT_TIME = 0.05;

		while ( true )
		{
			float distanceToCenter = Distance2D( GetActorLocation(), ownerZone.GetActorLocation() );
			if ( distanceToCenter >= ownerZone.decal.DecalSize.X )
			{
				Destroy();
				return;
			}
			co.Wait( WAIT_TIME );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void SetupPresentation( ANCPlayerCharacter pawn )
	{
		if ( !IsValid( ownerPlayer ) )
			return;
		if ( Cast<AAS_PlayerEntity>( pawn ) != ownerPlayer )
			return;
		if ( hasSetupPresentation )
			return; // safety to avoid duplicate setup calls

		if ( IsServer() )
		{
			ownerPlayer.SetViewRotation( GetActorRotation() );
			ownerPlayer.ScriptHolsterWeapon();
			ownerPlayer.SetInvulnerable( true );
			ownerPlayer.Hide();

			AttachToActor( ownerPlayer, NAME_None, EAttachmentRule::SnapToTarget );

			fireInputHandle = ownerPlayer.AddButtonPressedCallback( n"Fire", this, n"OnFirePressed" );
			sustainActors	= Server_EmitSoundOnEntity_1P3P_ReturnEntities( sustain1p, sustain3p, this, ownerPlayer );
		}

		if ( IsClient() )
		{
			fx.SetColorParameter( n"Color", HardenConst::HARDEN_COLOR );

			if ( ownerPlayer.IsLocallyControlled() )
			{
				ownerPlayer.GetTPPCameraComponent().SetAllowZoon( false );
				ownerPlayer.GetTPPCameraComponent().AddTPPRequest( n"MaraRespawn" );

				AAS_HUD localHud = GetLocalHUD();
				if ( IsValid( localHud ) )
					localHud.mainHUDWidget.hudHintOverrideDelegate.BindUFunction( this, n"HudHintOverride" );
			}
			else if ( !IsFriendly( ownerPlayer.GetTeam(), Client_GetLocalASPawn().GetTeam() ) )
			{
				// hide FX for enemy players
				fx.Deactivate();
			}
		}

		hasSetupPresentation = true;
	}

	UFUNCTION()
	FHUDHintData HudHintOverride()
	{
		return FHUDHintData( GetLocalizedText( Localization::Character_Mara, "mara_beacon_spawn" ), n"Fire" );
	}

	void TeardownPresentation()
	{
		if ( !IsValid( ownerPlayer ) )
			return;

		if ( IsServer() )
		{
			ownerPlayer.Delayed_ClearInvulnerable( 2.0 );
			ownerPlayer.ScriptDeployWeapon();
			ownerPlayer.EquipLastPrimaryWeapon();
			ownerPlayer.Show();
			ownerPlayer.SetMovementComponentVelocity( FVector( 0, 0, 0 ) );

			DetachFromActor( EDetachmentRule::KeepWorld, EDetachmentRule::KeepWorld, EDetachmentRule::KeepWorld );

			for ( auto s : sustainActors )
				s.Destroy();
			Server_EmitSoundAtLocation_1P3P( spawn1p, spawn3p, ownerPlayer.GetActorLocation(), ownerPlayer );

			ownerPlayer.RemoveButtonPressedCallback( n"Fire", fireInputHandle );

			Server_SpawnEffectAtLocation_OneShot( spawnFX, ownerPlayer.GetFootPosition(), FRotator() );
		}

		if ( IsClient() )
		{
			if ( ownerPlayer.IsLocallyControlled() )
			{
				ownerPlayer.GetTPPCameraComponent().SetAllowZoon( true );
				ownerPlayer.GetTPPCameraComponent().ClearTPPRequest( n"MaraRespawn" );

				AAS_HUD localHud = GetLocalHUD();
				if ( IsValid( localHud ) && localHud.mainHUDWidget.hudHintOverrideDelegate.IsBound() )
					localHud.mainHUDWidget.hudHintOverrideDelegate.Clear();
			}
		}
	}
}