void HornetGetUIBarFillFracs( ANCWeapon weapon, float32& leftFillFrac, float32& rightFillFrac )
{
}

UCLASS( Abstract )
class UAS_Reticle_Hornet : UAS_Reticle_HipfireCross
{
	UPROPERTY( NotVisible, BindWidget )
	UImage ammoBarL;

	UPROPERTY( NotVisible, BindWidget )
	UImage ammoBarR;

	// TODO dbocek: find a way to send this on client on fire from weapon rather than tick
	void ScriptReticleTick( ANCWeapon currentWeapon, float32 dt ) override
	{
		Super::ScriptReticleTick( currentWeapon, dt );

		ANCPlayerCharacter player = currentWeapon.GetWeaponOwner();
		if ( !IsValid( player ) )
			return;

		EWeaponState curWeaponState = currentWeapon.GetWeaponState();
		isReloading					= curWeaponState == EWeaponState::Reloading && !currentWeapon.IsReadyToFire();
		bool isSprinting			= player.IsSprinting();
		if ( isReloading || isSprinting )
		{
			ammoBarL.SetVisibility( ESlateVisibility::Hidden );
			ammoBarR.SetVisibility( ESlateVisibility::Hidden );
			return;
		}
		else
		{
			if ( ammoBarL.Visibility == ESlateVisibility::Hidden )
				ammoBarL.SetVisibility( ESlateVisibility::SelfHitTestInvisible );
			if ( ammoBarR.Visibility == ESlateVisibility::Hidden )
				ammoBarR.SetVisibility( ESlateVisibility::SelfHitTestInvisible );
		}

		float32 leftFillFrac;
		float32 rightFillFrac;
		const float32 halfMaxAmmo = currentWeapon.GetClipAmmoMax() / 2.f;
		const float32 currentAmmo = currentWeapon.GetClipAmmo();

		if ( currentAmmo >= halfMaxAmmo )
		{
			leftFillFrac  = 1.f;
			rightFillFrac = ( currentAmmo - halfMaxAmmo ) / halfMaxAmmo;
		}
		else
		{
			leftFillFrac  = currentAmmo / halfMaxAmmo;
			rightFillFrac = 0.f;
		}

		ammoBarL.GetDynamicMaterial().SetScalarParameterValue( n"FillFrac", leftFillFrac );
		ammoBarR.GetDynamicMaterial().SetScalarParameterValue( n"FillFrac", rightFillFrac );
	}
}

// TODO dbocek: this should be its own widget instead of gross copy paste here
UCLASS( Abstract )
class UAS_Reticle_HornetSight : UAS_Reticle_PaladinSMG
{
	UPROPERTY( NotVisible, BindWidget )
	UImage ammoBarL;

	UPROPERTY( NotVisible, BindWidget )
	UImage ammoBarR;

	// TODO dbocek: find a way to send this on client on fire from weapon rather than tick
	void ScriptReticleTick( ANCWeapon currentWeapon, float32 dt ) override
	{
		Super::ScriptReticleTick( currentWeapon, dt );

		ANCPlayerCharacter player = currentWeapon.GetWeaponOwner();
		if ( !IsValid( player ) )
			return;

		EWeaponState curWeaponState = currentWeapon.GetWeaponState();
		isReloading					= curWeaponState == EWeaponState::Reloading && !currentWeapon.IsReadyToFire();
		bool isSprinting			= player.IsSprinting();
		if ( isReloading || isSprinting )
		{
			ammoBarL.SetVisibility( ESlateVisibility::Hidden );
			ammoBarR.SetVisibility( ESlateVisibility::Hidden );
			return;
		}
		else
		{
			if ( ammoBarL.Visibility == ESlateVisibility::Hidden )
				ammoBarL.SetVisibility( ESlateVisibility::SelfHitTestInvisible );
			if ( ammoBarR.Visibility == ESlateVisibility::Hidden )
				ammoBarR.SetVisibility( ESlateVisibility::SelfHitTestInvisible );
		}

		float32 leftFillFrac;
		float32 rightFillFrac;
		const float32 halfMaxAmmo = currentWeapon.GetClipAmmoMax() / 2.f;
		const float32 currentAmmo = currentWeapon.GetClipAmmo();

		if ( currentAmmo >= halfMaxAmmo )
		{
			leftFillFrac  = 1.f;
			rightFillFrac = ( currentAmmo - halfMaxAmmo ) / halfMaxAmmo;
		}
		else
		{
			leftFillFrac  = currentAmmo / halfMaxAmmo;
			rightFillFrac = 0.f;
		}

		ammoBarL.GetDynamicMaterial().SetScalarParameterValue( n"FillFrac", leftFillFrac );
		ammoBarR.GetDynamicMaterial().SetScalarParameterValue( n"FillFrac", rightFillFrac );
	}
}