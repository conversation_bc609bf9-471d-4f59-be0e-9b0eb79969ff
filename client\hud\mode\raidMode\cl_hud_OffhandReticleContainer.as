UCLASS(Abstract)
class UAS_OffhandReticleContainer : UUserWidgetDefault
{
	const int PRIORITY_MESSAGE_MAX_OTHER_RIGHT_MSGS		= 3;

	UPROPERTY(BlueprintReadOnly, NotEditable, Meta=(BindWidget))
    UOverlay OffhandReticleContainer;

	UAS_ReticleWidget offhandReticle;	
	bool containerVisible;
	
	UFUNCTION(BlueprintOverride)
	void Construct()
	{	
		ScriptCallbacks().localClient_OnSetOffhandMeleeReticle.AddUFunction( this, n"OnSetOffhandMeleeReticle" );

		System::SetTimer( this, n"FakeClientTick", 0.016, true );
		
		containerVisible = false;
		SetRenderOpacity(0);
	}

	UFUNCTION()
	void OnSetOffhandMeleeReticle(TSubclassOf<UNCReticleWidget> reticleWidgetClass)
	{
		AAS_HUD HUD = GetLocalHUD();
		UAS_ReticleWidgetMeleeWeapon meleeReticle = Cast<UAS_ReticleWidgetMeleeWeapon>(WidgetBlueprint::CreateWidget( reticleWidgetClass, HUD.mainHUDWidget.GetOwningPlayer() ) );

		ScriptAssert( IsValid(meleeReticle), "Offhand melee Reticle didn't cast to UAS_ReticleWidgetMeleeWeapon" );
		
		ClMeleeReticle().SetMasterMeleeReticle(meleeReticle);
		OffhandReticleContainer.AddChild(meleeReticle);

		if ( IsValid(offhandReticle) )
			offhandReticle.RemoveFromParent();
		offhandReticle = meleeReticle;
	}

	UFUNCTION()
	private void FakeClientTick()
	{
		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( !IsValid(player ) )
			return;
		
		ANCMeleeWeapon meleeWeapon = Cast<ANCMeleeWeapon>(player.GetWeaponAtSlot( WeaponSlot::MeleeSlot ));
		if ( !IsValid(meleeWeapon ) )
			return;

		if ( !IsValid(offhandReticle) )
			return;
				
		float dt = Gameplay::GetWorldDeltaSeconds();
		offhandReticle.ReticleTick( meleeWeapon, dt );
		
		bool show = ClMeleeReticle().ShouldShowOffhandContainer( player, meleeWeapon );
		if ( show != containerVisible )
		{
			containerVisible = show;
			SetRenderOpacity(containerVisible ? 1 : 0);
		}
	}
}
