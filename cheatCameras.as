UCLASS()
class AAS_FreecamTest : AAS_CineCamTest
{
	FVector currentVelocity;
	FRotator currentTurnVelocity;

	UFUNCTION( BlueprintOverride )
	void Tick( float DeltaSeconds )
	{
		Super::Tick( DeltaSeconds );

		float decellerationFrictionMove = GetCvarFloat( "CustomCamera.DecellerationFriction_Move" );
		float decellerationFrictionTurn = GetCvarFloat( "CustomCamera.DecellerationFriction_Turn" );

		float zInput = 0;

		float turnSpeed		  = GetCvarFloat( "CustomCamera.TurnSpeed" );
		float speed			  = GetCvarFloat( "CustomCamera.MoveSpeed" );
		float Z_CHANGE_AMOUNT = GetCvarFloat( "CustomCamera.ZSpeed" ) / 100.0;

		zInput += z_up_pressed ? Z_CHANGE_AMOUNT : 0;
		zInput += z_down_pressed ? -Z_CHANGE_AMOUNT : 0;

		FVector moveInput	= Client_GetLocalPawn().GetInputAxisMove();
		FVector rotateInput = Client_GetLocalPawn().GetInputAxisTurn();
		float scale			= scaleDown_pressed ? 0.25 : 1.0;

		FVector fwd = GetActorForwardVector();
		FVector rgt = GetActorRightVector();

		FRotator currentRotation = GetActorRotation();

		FRotator turnDelta = FRotator( rotateInput.Y, rotateInput.X, 0 );

		FRotator newRotation = currentRotation + ( turnDelta * turnSpeed * DeltaSeconds );
		newRotation.Pitch	 = Math::Clamp( newRotation.Pitch, -89, 89 );
		SetActorRotation( newRotation );

		FVector moveDelta = ( fwd * moveInput.X ) + ( rgt * moveInput.Y );

		if ( GetCvarBool( "CustomCamera.LockMovementZ" ) )
		{
			moveDelta.Z = 0;
		}

		if ( decellerationFrictionMove <= 0 )
		{
			SetActorLocation( GetActorLocation() + ( moveDelta * speed * DeltaSeconds * scale ) );
		}
		else
		{
			currentVelocity += ( moveDelta * speed * DeltaSeconds );
			FVector moveSpeedDirection = currentVelocity;
			moveSpeedDirection.Normalize();
			float currentSpeed = currentVelocity.Size();
			currentSpeed	   = Math::Min( currentSpeed, speed );
			currentVelocity	   = ( moveSpeedDirection * currentSpeed );

			SetActorLocation( GetActorLocation() + ( currentVelocity * DeltaSeconds * scale ) );

			float speedReduction = ( 1.0 - moveInput.Size() );

			if ( speedReduction > 0 )
			{
				float friction = decellerationFrictionMove / 100.0;
				currentSpeed -= Math::Min( currentSpeed, Math::Max( currentSpeed * ( speedReduction * friction ), speedReduction * 1 ) );
				currentVelocity = ( moveSpeedDirection * currentSpeed );
			}
		}

		SetActorLocation( GetActorLocation() + FVector( 0, 0, zInput * speed * DeltaSeconds * scale ) );
	}
}

UCLASS()
class AAS_SnorricamTest : AAS_CineCamTest
{
	UFUNCTION( BlueprintOverride )
	void Tick( float DeltaSeconds )
	{
		Super::Tick( DeltaSeconds );

		float32 zInput			= 0;
		float turnSpeed			= GetCvarFloat( "CustomCamera.TurnSpeed" );
		float speed				= GetCvarFloat( "CustomCamera.MoveSpeed" );
		float32 Z_CHANGE_AMOUNT = GetCvarFloat( "CustomCamera.ZSpeed" ) / 100.0;

		zInput += z_up_pressed ? Z_CHANGE_AMOUNT : 0;
		zInput += z_down_pressed ? -Z_CHANGE_AMOUNT : 0;

		FVector moveInput	= Client_GetLocalPawn().GetInputAxisMove();
		FVector rotateInput = Client_GetLocalPawn().GetInputAxisTurn();
		float scale			= scaleDown_pressed ? 0.25 : 1.0;

		FRotator newRotation = GetActorRotation();
		newRotation += FRotator( scale * turnSpeed * rotateInput.Y * DeltaSeconds, scale * turnSpeed * rotateInput.X * DeltaSeconds, 0 );
		newRotation.Pitch = Math::Clamp( newRotation.Pitch, -89, 89 );
		SetActorRotation( newRotation );
		CameraComponent.SetRelativeLocation( CameraComponent.GetRelativeLocation() + FVector( moveInput.X * speed * DeltaSeconds * scale, 0, 0 ) );
		SetActorRelativeLocation( GetActorRelativeLocation() + FVector( 0, 0, zInput * speed * DeltaSeconds * scale ) );

		zInput = 0;
	}
}

class AAS_CineCamTest : ACameraActor
{
	default CameraComponent.ConstraintAspectRatio = false;

	bool scaleUp_pressed;
	bool scaleDown_pressed;
	bool rollRight_pressed;
	bool rollLeft_pressed;
	bool focalDistanceUp_pressed;
	bool focalDistanceDown_pressed;
	bool focusUp_pressed;
	bool focusDown_pressed;
	bool apertureUp_pressed;
	bool apertureDown_pressed;
	bool z_up_pressed;
	bool z_down_pressed;

	ANCPlayerController controller;

	UFUNCTION( BlueprintOverride )
	void BeginPlay()
	{
		controller				= Client_GetLocalPlayerController();
		UNCInputComponent input = controller.GetNCInputComponent();
		input.BindAction( n"Camera_AutoSetFocus", EInputEvent::IE_Pressed, FInputActionHandlerDynamicSignature( this, n"OnFocusHerePressed" ) );
		input.BindAction( n"Camera_SnapZoom", EInputEvent::IE_Pressed, FInputActionHandlerDynamicSignature( this, n"OnSnapZoomPressed" ) );
		input.BindAction( n"Camera_SnapZoom", EInputEvent::IE_Released, FInputActionHandlerDynamicSignature( this, n"OnSnapZoomReleased" ) );

		input.BindAction( n"Camera_ScaleUp", EInputEvent::IE_Pressed, FInputActionHandlerDynamicSignature( this, n"Camera_ScaleUp_Pressed" ) );
		input.BindAction( n"Camera_ScaleUp", EInputEvent::IE_Released, FInputActionHandlerDynamicSignature( this, n"Camera_ScaleUp_Released" ) );
		input.BindAction( n"Camera_ScaleDown", EInputEvent::IE_Pressed, FInputActionHandlerDynamicSignature( this, n"Camera_ScaleDown_Pressed" ) );
		input.BindAction( n"Camera_ScaleDown", EInputEvent::IE_Released, FInputActionHandlerDynamicSignature( this, n"Camera_ScaleDown_Released" ) );
		input.BindAction( n"Camera_FocalDistanceUp", EInputEvent::IE_Pressed, FInputActionHandlerDynamicSignature( this, n"Camera_FocalDistanceUp_Pressed" ) );
		input.BindAction( n"Camera_FocalDistanceUp", EInputEvent::IE_Released, FInputActionHandlerDynamicSignature( this, n"Camera_FocalDistanceUp_Released" ) );
		input.BindAction( n"Camera_FocalDistanceDown", EInputEvent::IE_Pressed, FInputActionHandlerDynamicSignature( this, n"Camera_FocalDistanceDown_Pressed" ) );
		input.BindAction( n"Camera_FocalDistanceDown", EInputEvent::IE_Released, FInputActionHandlerDynamicSignature( this, n"Camera_FocalDistanceDown_Released" ) );
		input.BindAction( n"Camera_ApertureUp", EInputEvent::IE_Pressed, FInputActionHandlerDynamicSignature( this, n"Camera_ApertureUp_Pressed" ) );
		input.BindAction( n"Camera_ApertureUp", EInputEvent::IE_Released, FInputActionHandlerDynamicSignature( this, n"Camera_ApertureUp_Released" ) );
		input.BindAction( n"Camera_ApertureDown", EInputEvent::IE_Pressed, FInputActionHandlerDynamicSignature( this, n"Camera_ApertureDown_Pressed" ) );
		input.BindAction( n"Camera_ApertureDown", EInputEvent::IE_Released, FInputActionHandlerDynamicSignature( this, n"Camera_ApertureDown_Released" ) );
		input.BindAction( n"Camera_FocusUp", EInputEvent::IE_Pressed, FInputActionHandlerDynamicSignature( this, n"Camera_FocusUp_Pressed" ) );
		input.BindAction( n"Camera_FocusUp", EInputEvent::IE_Released, FInputActionHandlerDynamicSignature( this, n"Camera_FocusUp_Released" ) );
		input.BindAction( n"Camera_FocusDown", EInputEvent::IE_Pressed, FInputActionHandlerDynamicSignature( this, n"Camera_FocusDown_Pressed" ) );
		input.BindAction( n"Camera_FocusDown", EInputEvent::IE_Released, FInputActionHandlerDynamicSignature( this, n"Camera_FocusDown_Released" ) );
		input.BindAction( n"Camera_RollLeft", EInputEvent::IE_Pressed, FInputActionHandlerDynamicSignature( this, n"Camera_RollLeft_Pressed" ) );
		input.BindAction( n"Camera_RollLeft", EInputEvent::IE_Released, FInputActionHandlerDynamicSignature( this, n"Camera_RollLeft_Released" ) );
		input.BindAction( n"Camera_RollRight", EInputEvent::IE_Pressed, FInputActionHandlerDynamicSignature( this, n"Camera_RollRight_Pressed" ) );
		input.BindAction( n"Camera_RollRight", EInputEvent::IE_Released, FInputActionHandlerDynamicSignature( this, n"Camera_RollRight_Released" ) );
		input.BindAction( n"Camera_Z_Up", EInputEvent::IE_Pressed, FInputActionHandlerDynamicSignature( this, n"Camera_Z_Up_Pressed" ) );
		input.BindAction( n"Camera_Z_Up", EInputEvent::IE_Released, FInputActionHandlerDynamicSignature( this, n"Camera_Z_Up_Released" ) );
		input.BindAction( n"Camera_Z_Down", EInputEvent::IE_Pressed, FInputActionHandlerDynamicSignature( this, n"Camera_Z_Down_Pressed" ) );
		input.BindAction( n"Camera_Z_Down", EInputEvent::IE_Released, FInputActionHandlerDynamicSignature( this, n"Camera_Z_Down_Released" ) );

		CameraComponent.PostProcessSettings.bOverride_NCDepthofFieldViewmodelOnly = true;
		CameraComponent.PostProcessSettings.NCDepthofFieldViewmodelOnly			  = false;
	}

	UFUNCTION()
	private void OnFocusHerePressed( FKey Key )
	{
		FHitResult trace = LineTraceSingle( CameraComponent.GetWorldLocation(), CameraComponent.GetWorldLocation() + ( CameraComponent.GetWorldRotation().ForwardVector * 100000 ), ETraceTypeQuery::WeaponFine, true, TArray<AActor>(), true );
		if ( trace.bBlockingHit )
		{
			float focusDistance														= ( trace.ImpactPoint - trace.TraceStart ).Size();
			CameraComponent.PostProcessSettings.bOverride_DepthOfFieldFocalDistance = true;
			CameraComponent.PostProcessSettings.DepthOfFieldFocalDistance			= focusDistance;
			DrawDebugSphere( CameraComponent.GetWorldLocation() + ( CameraComponent.GetWorldRotation().ForwardVector * focusDistance ), 5, 0.5 );
		}
	}

	bool isSnapZooming;
	float snapZoomStartFov;
	FNCCoroutineSignal snapZoomEndSignal;

	UFUNCTION()
	private void OnSnapZoomPressed( FKey Key )
	{
		if (isSnapZooming)
			return;

		Thread( this, n"SnapZoomThreadStart" );
	}

	UFUNCTION()
	private void OnSnapZoomReleased( FKey Key )
	{
		snapZoomEndSignal.Emit();
	}

	UFUNCTION()
	void SnapZoomThreadStart( UNCCoroutine co )
	{
		isSnapZooming = true;
		snapZoomEndSignal.Emit();
		co.EndOn( this, snapZoomEndSignal );
		co.OnCoroutineEnd.AddUFunction( this, n"OnSnapZoomStart_End" );
		
		snapZoomStartFov = CameraComponent.FieldOfView;
		float snapZoomTargetFov = GetCvarFloat( "CustomCamera.SnapZoomTargetFOV" );

		float startTime = Gameplay::GetTimeSeconds();
		float duration = TO_SECONDS( GetCvarInt( "CustomCamera.SnapZoomLerpDurationMS" ) );

		while ( true )
		{
			float alpha = (Gameplay::GetTimeSeconds() - startTime) / duration;
			alpha = Math::Clamp(alpha,0,1);
			CameraComponent.FieldOfView = Math::Lerp( snapZoomStartFov, snapZoomTargetFov, alpha );
			Print( f"FieldOfView {CameraComponent.FieldOfView} ... {snapZoomStartFov} --> {snapZoomTargetFov} ... {alpha}" );
			co.Wait( 0.001 );
		}
	}

	UFUNCTION()
	private void OnSnapZoomStart_End(FNCCoroutineEndParams endParams)
	{
		Thread( this, n"SnapZoomThreadEnd" );
	}

	UFUNCTION()
	void SnapZoomThreadEnd( UNCCoroutine co )
	{
		co.OnCoroutineEnd.AddUFunction( this, n"OnSnapZoomEnd" );
	
		float snapZoomTargetFov = snapZoomStartFov;
		float startFov = CameraComponent.FieldOfView;

		float startTime = Gameplay::GetTimeSeconds();
		float duration = TO_SECONDS( GetCvarInt( "CustomCamera.SnapZoomLerpDurationMS" ) );

		while ( true )
		{
			float alpha = (Gameplay::GetTimeSeconds() - startTime) / duration;
			alpha = Math::Clamp(alpha,0,1);
			CameraComponent.FieldOfView = Math::Lerp( startFov, snapZoomTargetFov, alpha );
			Print( f"SnapZoomThreadEnd :::: FieldOfView {CameraComponent.FieldOfView} ... {startFov} --> {snapZoomTargetFov} ... {alpha}" );
			if ( alpha >= 1 )
				return;
			co.Wait( 0.001 );
		}
	}

	UFUNCTION()
	private void OnSnapZoomEnd(FNCCoroutineEndParams endParams)
	{
		isSnapZooming = false;
	}

	UFUNCTION()
	private void Camera_ScaleUp_Pressed( FKey Key )
	{
		scaleUp_pressed = true;
	}

	UFUNCTION()
	private void Camera_ScaleUp_Released( FKey Key )
	{
		scaleUp_pressed = false;
	}

	UFUNCTION()
	private void Camera_ScaleDown_Pressed( FKey Key )
	{
		scaleDown_pressed = true;
	}

	UFUNCTION()
	private void Camera_ScaleDown_Released( FKey Key )
	{
		scaleDown_pressed = false;
	}

	UFUNCTION()
	private void Camera_FocalDistanceUp_Pressed( FKey Key )
	{
		focalDistanceUp_pressed = true;
	}

	UFUNCTION()
	private void Camera_FocalDistanceUp_Released( FKey Key )
	{
		focalDistanceUp_pressed = false;
	}

	UFUNCTION()
	private void Camera_FocalDistanceDown_Pressed( FKey Key )
	{
		focalDistanceDown_pressed = true;
	}

	UFUNCTION()
	private void Camera_FocalDistanceDown_Released( FKey Key )
	{
		focalDistanceDown_pressed = false;
	}

	UFUNCTION()
	private void Camera_ApertureUp_Pressed( FKey Key )
	{
		apertureUp_pressed = true;
	}

	UFUNCTION()
	private void Camera_ApertureUp_Released( FKey Key )
	{
		apertureUp_pressed = false;
	}

	UFUNCTION()
	private void Camera_ApertureDown_Pressed( FKey Key )
	{
		apertureDown_pressed = true;
	}

	UFUNCTION()
	private void Camera_ApertureDown_Released( FKey Key )
	{
		apertureDown_pressed = false;
	}

	UFUNCTION()
	private void Camera_FocusUp_Pressed( FKey Key )
	{
		focusUp_pressed = true;
	}

	UFUNCTION()
	private void Camera_FocusUp_Released( FKey Key )
	{
		focusUp_pressed = false;
	}

	UFUNCTION()
	private void Camera_FocusDown_Pressed( FKey Key )
	{
		focusDown_pressed = true;
	}

	UFUNCTION()
	private void Camera_FocusDown_Released( FKey Key )
	{
		focusDown_pressed = false;
	}

	UFUNCTION()
	private void Camera_RollLeft_Pressed( FKey Key )
	{
		rollLeft_pressed = true;
	}

	UFUNCTION()
	private void Camera_RollLeft_Released( FKey Key )
	{
		rollLeft_pressed = false;
	}

	UFUNCTION()
	private void Camera_RollRight_Pressed( FKey Key )
	{
		rollRight_pressed = true;
	}

	UFUNCTION()
	private void Camera_RollRight_Released( FKey Key )
	{
		rollRight_pressed = false;
	}

	UFUNCTION()
	private void Camera_Z_Up_Pressed( FKey Key )
	{
		z_up_pressed = true;
	}

	UFUNCTION()
	private void Camera_Z_Up_Released( FKey Key )
	{
		z_up_pressed = false;
	}

	UFUNCTION()
	private void Camera_Z_Down_Pressed( FKey Key )
	{
		z_down_pressed = true;
	}

	UFUNCTION()
	private void Camera_Z_Down_Released( FKey Key )
	{
		z_down_pressed = false;
	}

	UFUNCTION( BlueprintOverride )
	void Tick( float DeltaSeconds )
	{
		float ROLL_CHANGE_AMOUNT = GetCvarFloat( "CustomCamera.RollSpeed" );
		float rollDelta			 = 0;

		float FOCUS_CHANGE_AMOUNT = 10;
		float focusDelta		  = 0;

		float FOCAL_DISTANCE_CHANGE_AMOUNT = 5;
		float focalDistanceDelta		   = 0;

		float APERTURE_CHANGE_AMOUNT = 0.2;
		float apertureDelta			 = 0;

		focalDistanceDelta += focalDistanceUp_pressed ? -FOCAL_DISTANCE_CHANGE_AMOUNT : 0;
		focalDistanceDelta += focalDistanceDown_pressed ? FOCAL_DISTANCE_CHANGE_AMOUNT : 0;

		apertureDelta += apertureUp_pressed ? APERTURE_CHANGE_AMOUNT : 0;
		apertureDelta += apertureDown_pressed ? -APERTURE_CHANGE_AMOUNT : 0;

		focusDelta += focusUp_pressed ? FOCUS_CHANGE_AMOUNT : 0;
		focusDelta += focusDown_pressed ? -FOCUS_CHANGE_AMOUNT : 0;

		rollDelta += rollLeft_pressed ? -ROLL_CHANGE_AMOUNT : 0;
		rollDelta += rollRight_pressed ? ROLL_CHANGE_AMOUNT : 0;

		float scale = scaleDown_pressed ? 0.25 : 1.0;
		scale		= scaleUp_pressed ? 50 : scale;

		float rollScale = scaleDown_pressed ? 0.25 : 1.0;
		rollScale		= scaleUp_pressed ? 3.0 : rollScale;

		if ( focusDelta != 0 )
		{
			float focusDistance = Math::Clamp( CameraComponent.PostProcessSettings.DepthOfFieldFocalDistance + ( DeltaSeconds * focusDelta * scale ), 0, 100000 );

			CameraComponent.PostProcessSettings.bOverride_DepthOfFieldFocalDistance = true;
			CameraComponent.PostProcessSettings.DepthOfFieldFocalDistance			= focusDistance;

			DrawDebugSphere( CameraComponent.GetWorldLocation() + ( CameraComponent.GetWorldRotation().ForwardVector * focusDistance ), 5 );
		}

		if ( isSnapZooming )
		{
		}
		else if ( focalDistanceDelta != 0 )
		{
			float currentFocal			= CameraComponent.FieldOfView;
			currentFocal				= Math::Clamp( currentFocal + ( focalDistanceDelta * DeltaSeconds * scale ), 0, 200 );
			CameraComponent.FieldOfView = currentFocal;
			snapZoomStartFov			= currentFocal;
		}

		if ( apertureDelta != 0 )
		{
			float currentAperture											   = CameraComponent.PostProcessSettings.DepthOfFieldFstop;
			currentAperture													   = Math::Clamp( currentAperture + ( apertureDelta * DeltaSeconds * scale ), 0, 32 );
			CameraComponent.PostProcessSettings.bOverride_DepthOfFieldFstop	   = true;
			CameraComponent.PostProcessSettings.DepthOfFieldFstop			   = currentAperture;
			CameraComponent.PostProcessSettings.bOverride_DepthOfFieldMinFstop = true;
			CameraComponent.PostProcessSettings.DepthOfFieldMinFstop		   = 0;
			Print( f"{currentAperture}" );
		}

		float roll = CameraComponent.GetRelativeRotation().Roll;
		CameraComponent.SetRelativeRotation( FRotator( 0, 0, roll + ( DeltaSeconds * rollDelta * rollScale ) ) );
	}
}