enum ETrainingObjectiveEndContext
{
	COMPLETED,
	INCOMPLETE,
	CLIENT_COMPLETE_AND_REMOVE,
}

USTRUCT()
struct FTrainingDialogue
{
	// Use this for nags
	UPROPERTY()
	float delay = 0.0;

	// Use this to space out consecutive lines
	UPROPERTY()
	float startDelay = 0.0;

	UPROPERTY()
	float endDelay = 0.0;

	UPROPERTY()
	bool playOnComplete = false;
	
	UPROPERTY( meta=( Categories="Audio.VO" ) )
	FGameplayTag dialogueTag;

	UPROPERTY()
	bool isAnnouncerDialogue;
}

USTRUCT()
struct FTrainingObjectiveGroupSettings
{
	UPROPERTY()
	bool areObjectivesOrdered = false;

	UPROPERTY()
	TArray< TSubclassOf< UAS_TrainingObjective > > objectiveClasses;

	UPROPERTY()
	FText titleText;

	UPROPERTY()
	FText descriptionText;

	UPROPERTY()
	FName objectiveSetName;

	UPROPERTY()
	FString titleTextLocKey;

	UPROPERTY(meta=(EditCondition="!objectiveSetName.IsNone()", EditConditionHides))
	int objectiveSetNumber;

	UPROPERTY()
	UTexture binkTexture;

	UPROPERTY()
	FName objectiveMarkerName;

	// By default (false), hint objective markers will stomp default objective markers.
	// Set this if you want the objective marker, if a default objective, to hide when it becomes inactive.
	UPROPERTY()
	bool hideObjectiveMarkerIfNotActive = false;

	UPROPERTY( EditDefaultsOnly )
	TArray<FTrainingDialogue> dialogueData;

	UPROPERTY()
	bool hasHUDHint = false;

	// Changes whether or not it's a low prio hint. Default hints will stomp low prio hints.
	UPROPERTY()
	bool hintIsLowPrio = false;

	UPROPERTY( Meta = ( EditCondition="hasHUDHint", EditConditionHides) )
	FHUDHintData hintData;
}

event void FOnTrainingObjectiveGroupComplete( AAS_TrainingObjectiveGroup group, ETrainingObjectiveEndContext reason );
event void FOnTrainingObjectivesUpdated( AAS_TrainingObjectiveGroup group );
event void FOnTrainingObjectiveActiveChanged( AAS_TrainingObjectiveGroup group, bool isActive );

USTRUCT()
struct FNetTrainingObjectiveStatus
{
	UPROPERTY()
	FNCNetBool net_isComplete;

	UPROPERTY()
	FNCNetFloat net_progress;

	UPROPERTY()
	FNCNetInt net_progressVal1(-1);
	UPROPERTY()
	FNCNetInt net_progressVal2(-1);
}

UCLASS()
class AAS_TrainingObjectiveGroup : ANCDefaultActor
{
	// Used by the client to find settings
	UPROPERTY( NotVisible )
	FNCNetInt net_settingsIndex;
	default net_settingsIndex.SetNetValue( -1 );

	UPROPERTY( NotVisible )
	TArray< FNetTrainingObjectiveStatus > net_objectiveStatusList;
	
	UPROPERTY( NotVisible )
	FNCNetInt net_objectiveStatusChanged;

	// Used to juggle between temp hit objectives and default objectives.
	UPROPERTY( NotVisible )
	FNCNetBool net_isActiveUIObjective;
	default net_isActiveUIObjective.SetNetValue( false );

	UPROPERTY( NotVisible )
	FNCNetInt net_furthestCompletedObjectiveIndex;
	default net_furthestCompletedObjectiveIndex.SetNetValue( -1 );
	
	// This is set using net_settingsIndex
	FTrainingObjectiveGroupSettings settings;

	private FName objectiveName;

	FOnTrainingObjectiveGroupComplete onObjectiveGroupEnded;
	FOnTrainingObjectiveActiveChanged onObjectiveActiveChanged;
	TArray< UAS_TrainingObjective > objectives;

	ANCPlayerCharacter player;
	private bool hasGroupCompleted = false;
	private bool hasInitializedObjectives = false;

	TArray< AAS_TrainingObjectiveMarkerActor > serverActiveMarkers;
	bool overrideObjectiveMarkerSet = false;
	
	bool isTrainingHintActive = true;
	TOptional<FHUDHintData> lastSetObjectiveHint;
	bool lastSetObjectiveHintWasLowPrio = false;

	// Functionally server begin play
	void ServerInitialize( ANCPlayerCharacter inPlayer, FName inObjectiveName )
	{
		player = inPlayer;
		objectiveName = inObjectiveName;

		net_settingsIndex.SetNetValue( Objectives().GetIndexForGroupName( inObjectiveName ) );
		settings = Objectives().GetSettingsForIndex( net_settingsIndex );

		if ( settings.objectiveMarkerName != NAME_None )
		{
			ServerSetOverrideObjectiveMarker( Objectives().GetObjectiveMarkerDataForName( settings.objectiveMarkerName ) );
		}

		SharedBeginPlay();
		InitializeObjectives();

		for( UAS_TrainingObjective objective : objectives )
		{
			objective.ServerSetPlayer( inPlayer );
		}

		SetFlagCreate();
	}

	UFUNCTION(BlueprintOverride)
	void ClientBeginPlay()
	{
		SharedBeginPlay();

		net_objectiveStatusChanged.OnReplicated().AddUFunction( this, n"OnObjectiveStatusChanged" );
		net_settingsIndex.OnReplicated().AddUFunction( this, n"OnSettingsIndexChanged" );
		net_isActiveUIObjective.OnReplicated().AddUFunction( this, n"OnIsActiveChanged" );
		net_furthestCompletedObjectiveIndex.OnReplicated().AddUFunction( this, n"OnFurthestCompletedObjectiveIndexChanged" );
		OnSettingsIndexChanged( -1, net_settingsIndex );
		OnObjectiveStatusChanged( -1, net_objectiveStatusChanged );
		OnFurthestCompletedObjectiveIndexChanged( -1, net_furthestCompletedObjectiveIndex );
		OnIsActiveChanged( false, net_isActiveUIObjective );

		Objectives().ClientRegisterObjective( this );		

		// Dialogue -- FOR NOW, just play off the rip.
		// TODO: Solve if this becomes inactive b/c of a hint or something.
		//		 How to resume lines? Or if there's a queue of lines, how to handle that?
		PlayTrainingDialogue( settings.dialogueData, false );

		SetFlagCreate();
	}

	void SharedBeginPlay()
	{
		onObjectiveGroupEnded.AddUFunction( this, n"OnThisObjectiveEnded" );
	}

	void InitializeObjectives()
	{
		// Messy - Client calls this twice. Sometimes in PIE I wasn't getting the OnChanged event to fire, so I do it manually (which then causes duplicate calls)
		if ( hasInitializedObjectives )
		{
			return;
		}
		hasInitializedObjectives = true;

		for( TSubclassOf< UAS_TrainingObjective > objectiveClass : settings.objectiveClasses )
		{
			UAS_TrainingObjective newObjective = Cast<UAS_TrainingObjective>( NewObject( this, objectiveClass ) );
			objectives.Add( newObjective );
			newObjective.objectiveGroup = this;

			if ( IsServer() )
			{
				FNetTrainingObjectiveStatus objectiveStatus;
				net_objectiveStatusList.Add( objectiveStatus );
				newObjective.onObjectiveEnded.AddUFunction( this, n"OnServerObjectiveEnded" );
				newObjective.onObjectiveUpdated.AddUFunction( this, n"OnServerObjectiveUpdated" );
			}
		}

		if ( IsServer() )
		{
			net_objectiveStatusChanged.SetNetValue( net_objectiveStatusChanged + 1 );
		}
	}

	FName GetObjectiveName() const
	{
		return objectiveName;
	}

	void ServerSetIsActiveUIObjective()
	{
		net_isActiveUIObjective.SetNetValue( true );
		onObjectiveActiveChanged.Broadcast( this, true );
		ServerUpdateObjectiveMarker();
	}

	void ServerClearIsActiveUIObjective()
	{
		net_isActiveUIObjective.SetNetValue( false );
		onObjectiveActiveChanged.Broadcast( this, false );
		
		// Only call if hiding. Otherwise it'll try to make an objective marker that'll maybe stomp another one.
		if ( settings.hideObjectiveMarkerIfNotActive )
		{
			ServerUpdateObjectiveMarker();
		}
	}

	UFUNCTION()
	private void OnServerObjectiveEnded( UAS_TrainingObjective objective, ETrainingObjectiveEndContext reason )
	{
		ServerUpdateObjectives();
	}

	UFUNCTION()
	private void OnServerObjectiveUpdated( UAS_TrainingObjective objective )
	{
		ServerUpdateObjectives();
	}

	void ServerUpdateObjectives()
	{
		if ( hasGroupCompleted )
		{
			return;
		}

		bool isGroupComplete = true;

		const int numObjectives = objectives.Num();
		bool areObjectivesCompleteSoFar = true;
		bool didSomethingChange = false;
		for( int i = 0; i < numObjectives; i++ )
		{
			UAS_TrainingObjective objective = objectives[ i ];

			bool curObjectiveComplete = objective.IsObjectiveComplete();
			if ( !objective.IsObjectiveComplete() || ( settings.areObjectivesOrdered && !areObjectivesCompleteSoFar ) )
			{
				areObjectivesCompleteSoFar = false;
				didSomethingChange = didSomethingChange || net_objectiveStatusList[ i ].net_isComplete;
				net_objectiveStatusList[ i ].net_isComplete.SetNetValue( false );
				if ( i <= net_furthestCompletedObjectiveIndex )
				{
					net_furthestCompletedObjectiveIndex.SetNetValue( i - 1 );
				}
			}
			else if ( curObjectiveComplete )
			{
				didSomethingChange = didSomethingChange || !net_objectiveStatusList[ i ].net_isComplete;
				net_objectiveStatusList[ i ].net_isComplete.SetNetValue( true );
				net_furthestCompletedObjectiveIndex.SetNetValue( i );
			}
			
			if ( objective.GetObjectiveProgress() != net_objectiveStatusList[ i ].net_progress )
			{
				didSomethingChange = true;
				net_objectiveStatusList[ i ].net_progress.SetNetValue( objective.GetObjectiveProgress() );
				net_objectiveStatusList[ i ].net_progressVal1.SetNetValue( objective.GetObjectiveProgressTextVal1() );
				net_objectiveStatusList[ i ].net_progressVal2.SetNetValue( objective.GetObjectiveProgressTextVal2() );
			}
		}

		isGroupComplete = areObjectivesCompleteSoFar;
		if ( didSomethingChange )
		{
			net_objectiveStatusChanged.SetNetValue( net_objectiveStatusChanged + 1 );
		}

		if ( isGroupComplete != hasGroupCompleted )
		{
			hasGroupCompleted = isGroupComplete;
			onObjectiveGroupEnded.Broadcast( this, ETrainingObjectiveEndContext::COMPLETED );
			
			onObjectiveActiveChanged.Broadcast( this, false );
		}

		ServerUpdateObjectiveMarker();
	}

	void ServerSetOverrideObjectiveMarker( TArray<FTrainingObjectiveMarkerPingData> pingData )
	{
		overrideObjectiveMarkerSet = true;
		ServerClearActiveObjectiveMarker( true );
		
		CreateObjectivePingsFromArray( pingData );
	}

	void ServerClearOverrideObjectiveMarker()
	{
		overrideObjectiveMarkerSet = false;
		ServerClearActiveObjectiveMarker( true );
	}

	void ServerUpdateObjectiveMarker()
	{
		// Only run this for markers from objectives, if the group has objectives, skip this
		if ( overrideObjectiveMarkerSet )
		{
			return;
		}

		bool shouldClearObjective = hasGroupCompleted;
		if ( settings.hideObjectiveMarkerIfNotActive && !net_isActiveUIObjective )
		{
			shouldClearObjective = true;
		}

		const int numObjectives = objectives.Num();
		if ( numObjectives > 0 )
		{
			const int activeObjectiveIndex = Math::Min( net_furthestCompletedObjectiveIndex + 1, numObjectives - 1 );
			UAS_TrainingObjective activeObjective = objectives[ activeObjectiveIndex ];
			TArray<FTrainingObjectiveMarkerPingData> objectiveMarkerData;
			if ( !shouldClearObjective )
			{
				activeObjective.GetObjectiveMarkerLocation( objectiveMarkerData );
			}

			bool areNewPingsDifferent = false;
			while ( true ) /* do while false */ 
			{
				if ( activeObjective != currentMarkerObjective )
				{
					areNewPingsDifferent = true;
					break;
				}

				const int numPingsToCreate = objectiveMarkerData.Num();
				const int numCurrentPings = serverActiveMarkers.Num();
				if ( numCurrentPings != numPingsToCreate )
				{
					areNewPingsDifferent = true;
					break;
				}

				// If new pings are the same, don't make new ones.
				bool isMatch = true;
				for( int i = 0; i < numPingsToCreate; i++ )
				{
					const AAS_TrainingObjectiveMarkerActor markerObject = serverActiveMarkers[ i ];
					const FTrainingObjectiveMarkerPingData& markerData = objectiveMarkerData[ i ];
					if ( !IsValid( markerObject ) )
					{
						isMatch = false;
						break;
					}

					const FVector newMarkerLocation = IsValid( markerData.trackedActor ) ? markerData.trackedActor.GetActorLocation() : markerData.markerLocation;
					if ( markerObject.GetActorLocation() != newMarkerLocation )
					{
						isMatch = false;
						break;
					}
					if ( markerObject.GetMarkerIndex() != markerData.markerIndex )
					{
						isMatch = false;
						break;
					}
				}

				if ( !isMatch )
				{
					areNewPingsDifferent = true;
					break;
				}

				break;
			}

			if ( areNewPingsDifferent )
			{
				//ServerClearActiveObjectiveMarker();

				//if ( !objectiveMarkerData.IsEmpty() )
				if ( activeObjective != currentMarkerObjective )
				{
					Thread( this, n"RunObjectiveMarkersFromArrayThread", objectiveMarkerData, activeObjective );
				}
				else
				{
					updateObjectiveMarkerThread.Emit();
				}
			}

		}
	}

	UAS_TrainingObjective currentMarkerObjective = nullptr;
	FNCCoroutineSignal endObjectiveMarkerThread;
	FNCCoroutineSignal updateObjectiveMarkerThread;
	UFUNCTION()
	private void RunObjectiveMarkersFromArrayThread( UNCCoroutine co, TArray<FTrainingObjectiveMarkerPingData> objectivePingsToCreate, UAS_TrainingObjective activeObjective )
	{
		endObjectiveMarkerThread.Emit();
		co.EndOn( this, endObjectiveMarkerThread );
		co.OnCoroutineEnd.AddUFunction( this, n"CreateObjectivePingsFromArrayThreadEnd" );
		currentMarkerObjective = activeObjective;

		// Nag markers are updated every time the nag fires
		TArray<FTrainingObjectiveMarkerPingData> nagMarkers = objectivePingsToCreate;

		// Initial delay
		co.Wait( activeObjective.objectiveDelay );

		float32 waitTime = 0.f;

		while ( true )
		{
			activeObjective.GetObjectiveMarkerLocation( nagMarkers );
			ServerClearActiveObjectiveMarker( false );

			CreateObjectivePingsFromArray( nagMarkers );
			
			// Looping nags can update when manually updated, or when looping time happens
			if ( activeObjective.loopingNag )
			{
				waitTime = nagMarkers.IsEmpty() ? 1.f : activeObjective.loopingNagInterval;
				co.AddWait( waitTime );
			}

			// Wait to be updated
			co.AddWait( this, updateObjectiveMarkerThread );

			co.AwaitAny();
			nagMarkers.Empty();
		}
	}

	UFUNCTION()
	void CreateObjectivePingsFromArrayThreadEnd( FNCCoroutineEndParams params )
	{
		currentMarkerObjective = nullptr;
	}

	private void CreateObjectivePingsFromArray( TArray<FTrainingObjectiveMarkerPingData> objectivePingsToCreate )
	{
		const UAS_ObjectiveSystem objectivesSys = Objectives();
		const int numPingsToCreate = objectivePingsToCreate.Num();
		for( int i = 0; i < numPingsToCreate; i++ )
		{
			const FTrainingObjectiveMarkerPingData& markerData = objectivePingsToCreate[ i ];
			serverActiveMarkers.Add( objectivesSys.ServerCreateObjectiveMarker( markerData, player ) );
		}
	}

	private void ServerClearActiveObjectiveMarker( bool cancelThread )
	{
		if ( !serverActiveMarkers.IsEmpty() )
		{
			int numPingsToDestroy = serverActiveMarkers.Num();
			for( int i = 0; i < numPingsToDestroy; i++ )
			{
				if ( IsValid( serverActiveMarkers[ i ] ) )
				{
					serverActiveMarkers[ i ].Destroy();
				}
			}

			serverActiveMarkers.Empty();
		}
		
		if ( cancelThread )
		{
			endObjectiveMarkerThread.Emit();
		}
	}

	void MarkObjectiveGroupAsComplete()
	{
		for ( int i = 0; i < this.objectives.Num(); i++ )
		{
			if ( !IsValid(this.objectives[i]))
				continue;
			if ( this.objectives[i].IsObjectiveComplete() )
				continue;
			this.objectives[i].ServerMarkAsComplete();
		}	
	}

	UFUNCTION()
	void OnObjectiveStatusChanged( int oldValue, int newValue )
	{
		// Update from objective status TArray
		bool isGroupComplete = true;

		const int numObjectives = net_objectiveStatusList.Num();
		for( int i = 0; i < numObjectives; i++ )
		{
			UAS_TrainingObjective objective = objectives[ i ];
			FNetTrainingObjectiveStatus newStatus = net_objectiveStatusList[ i ];
			if ( newStatus.net_isComplete )
			{
				if ( i == numObjectives - 1 )
				{
					objective.ClientMarkAsCompleteAndRemove();
				}
				else
				{
					objective.ClientMarkAsComplete();
				}

				if ( objective.ShouldPlayDialogue( true ) )
				{
					PlayTrainingDialogue( objective.objectiveDialogue, true );
					objective.hasObjectiveSuccessDialoguePlayed = true;
				}
			}
			else
			{
				objective.ClientMarkAsIncomplete();
				isGroupComplete = false;
			}

			if ( objective.doProgressBar )
			{
				objective.SetObjectiveProgress( newStatus.net_progress, newStatus.net_progressVal1, newStatus.net_progressVal2 );				
			}
		}
		
		if ( isGroupComplete != hasGroupCompleted )
		{
			hasGroupCompleted = isGroupComplete;
			onObjectiveGroupEnded.Broadcast( this, ETrainingObjectiveEndContext::COMPLETED );
			
			if ( IsClient() )
			{
				PlayTrainingDialogue( settings.dialogueData, true );
			}
			
			onObjectiveActiveChanged.Broadcast( this, false );
		}
	}

	UFUNCTION()
	void OnSettingsIndexChanged( int oldValue, int newValue )
	{
		if ( newValue < 0 )
		{
			return;
		}

		settings = Objectives().GetSettingsForIndex( newValue );
		objectiveName = Objectives().GetSettingsNameForIndex( newValue );
		InitializeObjectives();
	}

	UFUNCTION()
	void OnIsActiveChanged( bool oldValue, bool newValue )
	{
		UAS_ClientScript_RaidMode_Training clientMode = ClientTrainingMode();
		if ( !IsValid( clientMode ) )
		{
			//Warning( f"Warning! Objective {this} active state changed, but training messaging hud was not valid!");
			return;
		}

		UAS_Training_MessagingHud messagingHud = clientMode.trainingMessagingHud;
		if ( !IsValid( messagingHud ) )
		{
			return;
		}

		if ( newValue )
		{
			clientMode.trainingMessagingHud.SetNewObjectiveGroup( this );
			// Clear HUD hint
			if ( settings.hasHUDHint )
			{
				lastSetObjectiveHint = settings.hintData;
				lastSetObjectiveHintWasLowPrio = settings.hintIsLowPrio;
			}
		}
		else
		{
			// If this is a default objective, a hint may be active. In that case, we don't want to clear it.
			clientMode.trainingMessagingHud.ClearObjectiveGroupIfMatching( this, ETrainingObjectiveEndContext::INCOMPLETE );
		}

		onObjectiveActiveChanged.Broadcast( this, newValue );
		UpdateHUDHint();
	}

	UFUNCTION()
	void OnFurthestCompletedObjectiveIndexChanged( int oldValue, int newValue )
	{
		if ( !settings.areObjectivesOrdered && objectives.Num() > 1 )
		{
			return;
		}

		const int numObjectives = objectives.Num();
		// This makes it so the furthest non-hidden objective will be set as active, by extending the furthest active index.
		int hiddenObjectiveFurthestActiveExtension = 0;
		bool canDoHiddenObjectiveExtension = true;

		for( int i = 0; i < numObjectives; i++ )
		{
			// The next objective to complete is the one marked as active. If furthest complete is 1 / 2, then the active "one you're working on" is 2 / 2
			// Hidden objectives (used for chaining location objectives 99.9% of the time) complicate this a bit.
			//		We want to show the next NON hidden objective as the active objective.

			const bool isActive = i == ( newValue + 1 + hiddenObjectiveFurthestActiveExtension );
			UAS_TrainingObjective curObjective = objectives[ i ];
			curObjective.ClientSetIsActiveOrderedObjective( isActive );
			// Allow for further objectives to be active as long as we're iterating over hidden objectives
			// Don't start building hidden extension if this objective is complete
			if ( !curObjective.IsObjectiveComplete() && curObjective.hiddenObjective && canDoHiddenObjectiveExtension )
			{
				hiddenObjectiveFurthestActiveExtension++;
			}
			// Once we hit a non-hidden objective, stop the extension
			else if ( hiddenObjectiveFurthestActiveExtension > 0 )
			{
				canDoHiddenObjectiveExtension = false;
			}

			if ( isActive && !curObjective.ShouldPlayDialogue( false ) )
			{
				PlayTrainingDialogue( curObjective.objectiveDialogue, false );
				curObjective.hasObjectiveDialoguePlayed = true;
			}

			// if objective group has a hud hint, disregard hud hints from any objectives
			if ( !settings.hasHUDHint )
			{
				if ( isActive && curObjective.hasHUDHint )
				{
					lastSetObjectiveHint = curObjective.hintData;
					lastSetObjectiveHintWasLowPrio = curObjective.hintIsLowPrio;
				}
				// Old objective had a hint, new one doesn't
				else if ( isActive && !curObjective.hasHUDHint && lastSetObjectiveHint.IsSet() )
				{
					shouldClearHUDHint = true;
				}
			}
		}

		UpdateHUDHint();
	}

	bool shouldClearHUDHint = false;
	UFUNCTION()
	void UpdateHUDHint()
	{
		if ( lastSetObjectiveHint.IsSet() )
		{
			UAS_MainHUDWidget mainHUDWidget;
			AAS_HUD localHud = GetLocalHUD();
			if ( IsValid( localHud ) )
			{
				mainHUDWidget = localHud.mainHUDWidget;
			}

			if ( !IsValid( mainHUDWidget ) )
			{
				return;
			}

			FHUDHintData hintData = lastSetObjectiveHint.GetValue();
			// Setting hint: We have hint data, group is active, and don't want to clear hint
			if ( net_isActiveUIObjective && !shouldClearHUDHint)
			{
				if ( lastSetObjectiveHintWasLowPrio )
				{
					mainHUDWidget.SetLowPrioScriptedHint( hintData, -1 );
				}
				else
				{
					mainHUDWidget.SetScriptedHint( hintData, -1 );
				}
				isTrainingHintActive = true;
			}
			// Clearing hint: Hint is still active, but group is not active anymore. Or we want to clear hint (regardless of whether visible or not)
			else if ( isTrainingHintActive || shouldClearHUDHint )
			{
				if ( lastSetObjectiveHintWasLowPrio )
				{
					mainHUDWidget.ClearLowPrioScriptedHint( hintData );
				}
				else
				{
					mainHUDWidget.ClearScriptedHint( hintData );
				}
				isTrainingHintActive = false;

				// Clearing hint data
				if ( shouldClearHUDHint )
				{
					lastSetObjectiveHint.Reset();
					shouldClearHUDHint = false;
				}
			}
		}
	}

	void PlayTrainingDialogue( TArray<FTrainingDialogue> dialogueToPlay, bool playSuccessDialogue )
	{
		if ( dialogueToPlay.Num() > 0 )
		{
			Thread(this, n"DialogueThread", dialogueToPlay, playSuccessDialogue );
		}
	}

	//  This thread does NOT cancel other threads, currently multiple can go at once
	UFUNCTION()
	void DialogueThread( UNCCoroutine co, TArray<FTrainingDialogue> dialogueToPlay, bool playSuccessDialogue )
	{		
		ANCPlayerCharacter localPlayer = Client_GetLocalPawn();
		co.EndOnDestroyed( localPlayer );
		
		UAS_DialogueSystem dialogueSys = Dialogue();

		// TODO: Gigahack to get session on client
		TArray<AAS_TrainingSession_RaidMode> sessions;
		GetAllActorsOfClass( sessions );
		AAS_Vendor speakingVendor = sessions[ 0 ].GetBestVendorToSpeak();
		
		for( FTrainingDialogue dialogueData : dialogueToPlay )
		{
			// Only play success when instructed to, otherwise default only. Skip mismatches.
			if ( playSuccessDialogue != dialogueData.playOnComplete )
			{
				continue;
			}

			if ( dialogueData.delay > 0 )
			{
				ScriptAssert( !playSuccessDialogue, f"Cannot use dialogue delay when playing on complete. Line: {dialogueData.dialogueTag} Objective: {objectiveName}" );
			}
			co.Wait( dialogueData.delay );
			if ( dialogueData.isAnnouncerDialogue )
			{
				dialogueSys.PlayTrainingAnnouncerDialogue( dialogueData.dialogueTag, dialogueData.startDelay, dialogueData.endDelay );
			}
			else
			{
				dialogueSys.PlayAlwaysLoadedDialogue( speakingVendor, GameplayTags::Audio_VO_Trader, dialogueData.dialogueTag, dialogueData.startDelay, dialogueData.endDelay );
			}
		}
	}

	UFUNCTION()
	private void OnThisObjectiveEnded( AAS_TrainingObjectiveGroup group, ETrainingObjectiveEndContext reason )
	{
		if ( IsClient() && IsValid( Objectives() ) )
		{
			Objectives().ClientUnregisterObjective( this );
		}

		Signal( objectiveName );
		SetFlagComplete();
	}

	UFUNCTION(BlueprintOverride)
	void EndPlay(EEndPlayReason EndPlayReason)
	{
		// Superstition, not sure if UObjects naturally do this.
		if ( !hasGroupCompleted )
		{
			onObjectiveGroupEnded.Broadcast( this, ETrainingObjectiveEndContext::INCOMPLETE );
			onObjectiveActiveChanged.Broadcast( this, false );
		}

		if ( isTrainingHintActive )
		{
			shouldClearHUDHint = true;
			UpdateHUDHint();
		}
		ServerClearActiveObjectiveMarker( true );
		
		onObjectiveActiveChanged.Clear();
		onObjectiveGroupEnded.Clear();

		for( UAS_TrainingObjective objective : objectives )
		{
			objective.CleanUp();
		}

		objectives.Empty();
	}
	
	void SetFlagCreate()
	{
		FName FLAG_CREATE = Objectives().GetObjectiveCreateFlag(objectiveName);
		ScriptCallbacks().RegisterFlagSafe( FLAG_CREATE );
		ScriptCallbacks().FlagSet( FLAG_CREATE );		
	}

	void SetFlagComplete()
	{
		FName FLAG_COMPLETE = Objectives().GetObjectiveCompleteFlag(objectiveName);
		ScriptCallbacks().RegisterFlagSafe( FLAG_COMPLETE );
		ScriptCallbacks().FlagSet( FLAG_COMPLETE );		
	}
}

event void FOnTrainingObjectiveComplete( UAS_TrainingObjective objective, ETrainingObjectiveEndContext reason );
event void FOnTrainingObjectiveUpdated( UAS_TrainingObjective objective );

UCLASS( Abstract )
class UAS_TrainingObjective : UObject
{
	UPROPERTY( EditDefaultsOnly )
	const FText description;

	// Should the UI show a progress bar corresponding to completion %. Make sure the objective angelscript hooks into this.
	UPROPERTY( EditDefaultsOnly )
	const bool doProgressBar;

	// If true, this won't show up in the UI, though it will still be considered an objective.
	// Example: Location objective that just shows up as a marker, which gates the current group from ending.
	UPROPERTY( EditDefaultsOnly )
	const bool hiddenObjective;

	UPROPERTY( EditDefaultsOnly )
	const bool hasHUDHint;

	UPROPERTY( EditDefaultsOnly, Meta = ( EditCondition="hasHUDHint", EditConditionHides ) )
	const FHUDHintData hintData;

	UPROPERTY( EditDefaultsOnly, Meta = ( EditCondition="hasHUDHint", EditConditionHides )  )
	const bool hintIsLowPrio;

	UPROPERTY( EditDefaultsOnly )
	FName objectiveMarkerName;

	UPROPERTY( EditDefaultsOnly )
	bool objectiveMarkerDoFindTarget = false;

	// Updates when SetObjectiveProgress is called
	UPROPERTY( EditDefaultsOnly, meta=( EditCondition="objectiveMarkerDoFindTarget", EditConditionHides ) )
	bool foundTargets_updateWhenProgressChanged = false;

	// Updates found target whenever objective markers are requested. Most expensive.
	UPROPERTY( EditDefaultsOnly, meta=( EditCondition="objectiveMarkerDoFindTarget", EditConditionHides ) )
	bool foundTargets_updateAlways = false;

	// Max num objective markers to show from the list of found targets.
	UPROPERTY( EditDefaultsOnly, meta=( EditCondition="objectiveMarkerDoFindTarget", EditConditionHides ) )
	int foundTargets_maxNumMarkersToShow = 1;

	UPROPERTY( EditDefaultsOnly, meta=( EditCondition="objectiveMarkerDoFindTarget", EditConditionHides ) )
	ETrainingMarkerType foundTargets_markerType = ETrainingMarkerType::INVALID;

	/*
		We always exclude the player as an actor to ignore
		Player is used for start point and direction
	*/
	UPROPERTY( EditDefaultsOnly, meta=( EditCondition="objectiveMarkerDoFindTarget", EditConditionHides ) )
	FNCFindTargetParameters foundTargets_params;

	UPROPERTY( EditDefaultsOnly )
	float objectiveDelay = 0;

	UPROPERTY( EditDefaultsOnly )
	bool loopingNag = false;

	UPROPERTY( EditDefaultsOnly, meta=( EditCondition="loopingNag", EditConditionHides ) )
	float32 loopingNagInterval = 15;

	bool incorrectActionDialoguePlayed = false;

	UPROPERTY( EditDefaultsOnly )
	FTrainingDialogue incorrectActionDialogue;

	UPROPERTY( EditDefaultsOnly )
	TArray<FTrainingDialogue> objectiveDialogue;

	// Currently objective dialogue just plays once, when the objective becomes active
	bool hasObjectiveDialoguePlayed = false;
	bool hasObjectiveSuccessDialoguePlayed = false;

	ANCPlayerCharacter player;

	AAS_TrainingObjectiveGroup objectiveGroup;
	FOnTrainingObjectiveComplete client_onObjectiveEnded;
	FOnTrainingObjectiveComplete onObjectiveEnded;
	FOnTrainingObjectiveUpdated onObjectiveUpdated;
	FOnTrainingObjectiveUpdated client_onObjectiveIncorrectAction;
	FOnTrainingObjectiveUpdated client_onObjectiveCorrectAction;
	private bool objectiveCompleted = false;
	private float32 objectiveProgress = 0;
	private int objectiveProgressTextVal1 = -1;
	private int objectiveProgressTextVal2 = -1;
	private bool isActiveOrderedObjective = false;

	TArray<FTrainingObjectiveMarkerPingData> objectiveMarkers;

	// Server only
	void ServerSetPlayer( ANCPlayerCharacter inPlayer )
	{
		player = inPlayer;

		if ( objectiveMarkerName != NAME_None )
		{
			UpdateObjectiveMarkersFromName();
		}
		else if ( objectiveMarkerDoFindTarget )
		{
			UpdateObjectiveMarkersFromFindTarget();
		}
	}

	void UpdateObjectiveMarkersFromName()
	{
		objectiveMarkers = Objectives().GetObjectiveMarkerDataForName( objectiveMarkerName );
	}

	// TODO: How handle exclude dead dummies, exclude broken resources... can server access params and set callbacks?
	// 	TODO: We DO need to do this.
	// TODO: Do we need to run a callback when the tracked actor dies or something? Ewww that's gonna be harddddd
	void UpdateObjectiveMarkersFromFindTarget()
	{
		if ( foundTargets_params.targetClassesToSearchFor.IsEmpty() )
		{
			return;
		}

		objectiveMarkers.Empty();

		FRotator eyeRotation;
		FVector eyeLocation;
		player.GetActorEyesViewPoint( eyeLocation, eyeRotation );

		TArray<UClass> classesToSearchFor;
		classesToSearchFor.Add( AAS_TargetDummy::StaticClass() );

		TArray<AActor> actorsToIgnore;
		actorsToIgnore.Add( player );
		
		foundTargets_params.startLocation = eyeLocation;
		foundTargets_params.forward = eyeRotation.GetForwardVector();
		foundTargets_params.ownerTeam = player.GetTeam();
		foundTargets_params.actorsToIgnore = actorsToIgnore;

		TArray<FNCFoundTargetData> foundTargets = GetTargetsInRangeWithFOV( foundTargets_params, UAS_GetTargetsInRangeFilterBase_Training::StaticClass() );
		int numMarkers = 0;
		for( FNCFoundTargetData foundTargetData : foundTargets )
		{
			if ( !IsValid( foundTargetData.actor ) )
			{
				continue;
			}

			// Update ping
			FTrainingObjectiveMarkerPingData newData;
			newData.markerIndex = foundTargets_markerType;
			newData.trackedActor = foundTargetData.actor;
			objectiveMarkers.Add( newData );

			numMarkers++;
			if ( numMarkers >= foundTargets_maxNumMarkersToShow )
			{
				break;
			}
		}
	}

	// Server: Objective itself sets this. Client: Set by group, according to what server said.
	void ServerMarkAsComplete() final
	{
		if ( objectiveCompleted || !IsServer() )
		{
			return;
		}

		objectiveCompleted = true;

		onObjectiveEnded.Broadcast( this, ETrainingObjectiveEndContext::COMPLETED );
	}

	// Server: Objective itself sets this. Client: Set by group, according to what server said.
	void ServerMarkAsIncomplete() final
	{
		if ( !objectiveCompleted || !IsServer() )
		{
			return;
		}

		objectiveCompleted = false;

		onObjectiveEnded.Broadcast( this, ETrainingObjectiveEndContext::INCOMPLETE );
	}

	void ClientMarkAsComplete() final
	{
		if ( objectiveCompleted || !IsClient() )
		{
			return;
		}

		Log(f"Training Obj: Marking {this} for complete!");
		client_onObjectiveEnded.Broadcast( this, ETrainingObjectiveEndContext::COMPLETED );
		objectiveCompleted = true;
	}

	// Called for objectives whose completion also marks completion of the objective.
	void ClientMarkAsCompleteAndRemove() final
	{
		if ( objectiveCompleted || !IsClient() )
		{
			return;
		}

		Log(f"Training Obj: Marking {this} for complete and remove!");
		client_onObjectiveEnded.Broadcast( this, ETrainingObjectiveEndContext::CLIENT_COMPLETE_AND_REMOVE );
		objectiveCompleted = true;
	}

	void ClientMarkAsIncomplete() final
	{
		if ( !objectiveCompleted || !IsClient() )
		{
			return;
		}

		Log(f"Training Obj: Marking {this} for incomplete!");
		client_onObjectiveEnded.Broadcast( this, ETrainingObjectiveEndContext::INCOMPLETE );
		objectiveCompleted = false;
	}

	void SetObjectiveProgress( float32 newProgress, int optionalProgressTextVal1 = -1, int optionalProgressTextVal2 = -1 )
	{
		bool didSomethingChange = newProgress != objectiveProgress;
		objectiveProgress = float32( Math::Clamp( newProgress, 0, 1 ) );
		objectiveProgressTextVal1 = optionalProgressTextVal1;
		objectiveProgressTextVal2 = optionalProgressTextVal2;
		if ( IsServer() )
		{
			if ( objectiveProgress == 1 )
			{
				ServerMarkAsComplete();
			}
			else if ( objectiveProgress < 1 )
			{
				if ( foundTargets_updateWhenProgressChanged )
				{
					UpdateObjectiveMarkersFromFindTarget();
				}

				ServerMarkAsIncomplete();
			}
		}
		if ( didSomethingChange )
		{
			onObjectiveUpdated.Broadcast( this );
			client_onObjectiveCorrectAction.Broadcast( this );
		}
	}

	void ServerSendObjectiveIncorrectAction()
	{
		UNCRemoteScriptCommands::SendServerCommand( player, f"ServerCommand_ObjectiveIncorrectAction {objectiveGroup.EntityId.RawValue} {objectiveGroup.objectives.FindIndex( this )}" );
	}

	float32 GetObjectiveProgress() const
	{
		return objectiveProgress;
	}

	int GetObjectiveProgressTextVal1() const
	{
		return objectiveProgressTextVal1;
	}

	int GetObjectiveProgressTextVal2() const
	{
		return objectiveProgressTextVal2;
	}

	FText GetObjectiveProgressText()
	{
		if ( objectiveProgressTextVal1 < 0 || objectiveProgressTextVal2 <= 0 )
			return FText();

		FText progressText = GetLocalizedText( Localization::Utilities, f"var_0_slash_var_1", FFormatArgumentValue( objectiveProgressTextVal1 ), FFormatArgumentValue( objectiveProgressTextVal2 ) );
		return progressText;
	}

	bool IsObjectiveComplete() final
	{
		return objectiveCompleted;
	}

	void ClientSetIsActiveOrderedObjective( bool inIsActiveOrderedObjective ) final
	{
		const bool didSomethingChange = inIsActiveOrderedObjective != isActiveOrderedObjective;
		isActiveOrderedObjective = inIsActiveOrderedObjective;

		if ( didSomethingChange)
		{
			onObjectiveUpdated.Broadcast( this );
		}
	}

	// Used to grey this objective out if it's not the current objective to complete in an ordered list of objectives.
	bool ClientGetIsActiveOrderedObjective()
	{
		return isActiveOrderedObjective;
	}

	// Objective group calls this on server to get the objective location. Happens when this becomes the active objective.
	void GetObjectiveMarkerLocation( TArray<FTrainingObjectiveMarkerPingData>&out outData )
	{
		if ( foundTargets_updateAlways && objectiveMarkerDoFindTarget )
		{
			UpdateObjectiveMarkersFromFindTarget();
		}
		
		outData = objectiveMarkers;
	}

	bool ShouldPlayDialogue( bool successDialogue )
	{
		bool canPlay = successDialogue ? !hasObjectiveSuccessDialoguePlayed : !hasObjectiveDialoguePlayed;
		return canPlay && !objectiveDialogue.IsEmpty();
	}

	void CleanUp()
	{
		// Superstition, not sure if UObjects naturally do this.
		onObjectiveEnded.Clear();
	}
}

// SERVER VERSION.. need a client version too
// Not part of the class so that there's not a dependency if this needs to get re-used later on
bool GetTrainingSessionForObjectiveGroup( AAS_TrainingObjectiveGroup group, ANCPlayerCharacter player, AAS_TrainingSession_RaidMode&out outSession )
{
	UAS_ServerScript_Mode_RaidMode_Training trainingMode = Cast< UAS_ServerScript_Mode_RaidMode_Training>( GetServerScript() );
	
	if ( !IsValid( trainingMode ) )
	{
		return false;
	}

	outSession = trainingMode.GetSessionForPlayer( player );

	return IsValid( outSession );
}