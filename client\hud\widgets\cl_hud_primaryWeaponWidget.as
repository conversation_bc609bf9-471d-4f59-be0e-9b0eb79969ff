UCLASS( Abstract )
class UAS_PrimaryWeaponWidget : UNC_DisplayWidget
{
	UPROPERTY( NotVisible, meta = ( BindWidgetOptional ) )
	UAS_WeaponCardWidget primarySlot;

	UPROPERTY( NotVisible, meta = ( BindWidgetOptional ) )
	UAS_WeaponCardWidget secondarySlot;

	UPROPERTY( NotVisible, meta = ( BindWidgetOptional ) )
	UAS_WeaponCardWidget raidToolSlot;

	UPROPERTY( NotVisible, meta = ( BindWidgetOptional ) )
	UAS_EquippedWeaponCardWidget equippedSlot;

	UPROPERTY( NotVisible, Transient, Meta = ( BindWidgetAnim ) )
	UWidgetAnimation anim_modUpgradeAvailable;

	UPROPERTY( NotVisible, Transient, Meta = ( BindWidgetAnim ) )
	UWidgetAnimation anim_modUpgradeNag;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
	}

	void SetWeapons( ANCWeapon equipped, ANCWeapon stowed )
	{
		ANCPlayerCharacter localPlayer = Client_GetLocalPawn();
		if ( IsValid( localPlayer ) )
		{
			if ( IsValid( primarySlot ) )
			{
				primarySlot.SetWeapon( equipped );
			}

			if ( IsValid( secondarySlot ) )
			{
				secondarySlot.SetWeapon( stowed );
			}

			if ( IsValid( raidToolSlot ) )
			{
				raidToolSlot.SetWeapon( localPlayer.GetWeaponAtSlot( EWeaponSlot::RaidToolsSlot ) );
			}

			if ( IsValid( equippedSlot ) )
			{
				equippedSlot.SetWeapon( localPlayer.GetActiveWeapon() );
			}
		}

		InvalidateLayoutAndVolatility();
	}
}