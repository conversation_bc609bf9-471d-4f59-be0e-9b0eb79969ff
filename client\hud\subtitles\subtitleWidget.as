UCLASS( Abstract )
class UAS_Subtitle : UNC_DisplayWidget
{
	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UCommonRichTextBlock subtitleText;

	void Init( FSubtitleData data )
	{
		FName colorId;

		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( data.actor );
		if ( IsValid( asPlayer ) )
		{
			colorId = GetUIColorIdForPlayer( asPlayer );
		}
		else
		{
			colorId = SafeColors::PLAYER_INDICATOR_ANNOUNCER;
		}

		subtitleText.SetText( GetLocalizedText(
			Localization::HUDMainWidget,
			"subtitle_format_speaker",
			FFormatArgumentValue( FText::FromName( colorId ) ),
			FFormatArgumentValue( data.speaker ),
			FFormatArgumentValue( data.caption ) ) );
	}

	UFUNCTION( BlueprintOverride )
	void OnHideEnd()
	{
		// Remove ourselves from the array when we are done hiding
		RemoveFromParent();
	}
}