struct FBuildingDummyAttachData
{
	UPROPERTY()
	FTransform attachTransform;

	UPROPERTY()
	AAS_BuildingDummy attachObject;

	UPROPERTY()
	bool isValidAttach = false;
}

struct FBuildingDummySetupData
{
	UPROPERTY()
	TMap<TSubclassOf<AActor>, FTransform> rootObjects;
}

UCLASS()
class AAS_BuildingDummy : AActor
{
	UPROPERTY( DefaultComponent, RootComponent )
	USceneComponent root;

	TSubclassOf<AActor> baseBlueprint;
	TArray<AAS_BuildingDummy> dummies;
	TArray<UActorComponent> components;

	bool isVisible;
	AAS_BuildingDummy lastBestAttachObject;

	UFUNCTION( BlueprintOverride )
	void BeginPlay()
	{
		OnEndPlay.AddUFunction( this, n"ClearDummy" );
	}

	void SetupDummyFromData( FBuildingDummySetupData data, UMaterialInstance material )
	{
		for ( auto rootData : data.rootObjects )
		{
			AAS_BuildingDummy dummy = Cast<AAS_BuildingDummy>( SpawnActor( AAS_BuildingDummy::StaticClass() ) );
			dummy.SetupDummy( rootData.Key );

			dummy.SetMaterial( material );
			dummy.AttachToActor( this );
			dummy.SetActorTransform( rootData.Value );

			dummies.Add( dummy );
		}
	}

	void SetupDummy( TSubclassOf<AActor> blueprint )
	{
		baseBlueprint = blueprint;

		if ( blueprint == nullptr )
			return;

		TArray<UActorComponent> defaultComponents = GetComponentsFromActorClass( blueprint.Get(), UActorComponent::StaticClass() );

		for ( UActorComponent component : defaultComponents )
		{
			if ( component.IsA( UChildActorComponent::StaticClass() ) )
			{
				UChildActorComponent childActorComponent = Cast<UChildActorComponent>( component );
				TSubclassOf<AActor> childActorClass		 = childActorComponent.ChildActorClass;

				FHitResult emptyHit;
				AAS_BuildingDummy nestedDummy = Cast<AAS_BuildingDummy>( SpawnActor( AAS_BuildingDummy::StaticClass() ) );
				nestedDummy.SetActorTransform( childActorComponent.GetRelativeTransform() );
				nestedDummy.AttachToActor( this, NAME_None, EAttachmentRule::KeepRelative, EAttachmentRule::KeepRelative, EAttachmentRule::KeepRelative, false );
				nestedDummy.SetupDummy( childActorClass );

				dummies.Add( nestedDummy );
			}
			else if ( component.IsA( UStaticMeshComponent::StaticClass() ) )
			{
				if ( component.IsA( UAS_StructureUpgradeVisual::StaticClass() ) )
				{
					UAS_StructureUpgradeVisual upgradeVisual = Cast<UAS_StructureUpgradeVisual>( component );
					if ( upgradeVisual.levelForVisual != 1 )
						continue; // ignore upgrade visuals for future levels
				}
				UStaticMeshComponent meshComponent = Cast<UStaticMeshComponent>( component );
				UStaticMeshComponent dummyMesh	   = Cast<UStaticMeshComponent>( CreateComponent( UStaticMeshComponent::StaticClass() ) );
				dummyMesh.SetStaticMesh( meshComponent.StaticMesh );
				dummyMesh.SetCollisionEnabled( ECollisionEnabled::NoCollision );
				dummyMesh.SetRelativeTransform( meshComponent.GetRelativeTransform() );

				components.Add( dummyMesh );
			}
			else if ( component.IsA( USkeletalMeshComponent::StaticClass() ) )
			{
				USkeletalMeshComponent meshComponent = Cast<USkeletalMeshComponent>( component );
				USkeletalMeshComponent dummyMesh	 = Cast<USkeletalMeshComponent>( CreateComponent( USkeletalMeshComponent::StaticClass() ) );
				dummyMesh.SetSkeletalMeshAsset( meshComponent.GetSkeletalMeshAsset() );
				dummyMesh.SetCollisionEnabled( ECollisionEnabled::NoCollision );
				dummyMesh.SetRelativeTransform( meshComponent.GetRelativeTransform() );

				components.Add( dummyMesh );
			}
		}
	}

	UFUNCTION( BlueprintCallable )
	void ClearDummy( AActor Pawn, EEndPlayReason Reason )
	{
		for ( UObject dummy : dummies )
		{
			if ( dummy.IsA( AActor::StaticClass() ) )
			{
				AActor dummyActor = Cast<AActor>( dummy );
				dummyActor.Destroy();
			}
		}
	}

	UFUNCTION( BlueprintCallable )
	void SetMaterial( UMaterialInterface material )
	{
		for ( UActorComponent component : components )
		{
			if ( component.IsA( UStaticMeshComponent::StaticClass() ) )
			{
				UStaticMeshComponent staticMesh = Cast<UStaticMeshComponent>( component );
				for ( int i = 0; i < staticMesh.GetNumMaterials(); i++ )
					staticMesh.SetMaterial( i, material );
			}
		}

		for ( AAS_BuildingDummy dummy : dummies )
		{
			if ( IsValid( dummy ) )
				dummy.SetMaterial( material );
		}
	}

	UFUNCTION( BlueprintCallable )
	void SetVisibility( bool newVisible )
	{
		isVisible = newVisible;

		for ( UActorComponent component : components )
		{
			if ( component.IsA( UStaticMeshComponent::StaticClass() ) )
			{
				UStaticMeshComponent staticMesh = Cast<UStaticMeshComponent>( component );
				staticMesh.SetVisibility( isVisible, true );
			}
		}

		for ( AAS_BuildingDummy dummy : dummies )
		{
			if ( IsValid( dummy ) )
				dummy.SetVisibility( isVisible );
		}
	}

	UFUNCTION( BlueprintCallable )
	void UpdateBestAttachObjectVisibility( AAS_BuildingDummy bestSnapObjectOnDummy )
	{
		if ( lastBestAttachObject != bestSnapObjectOnDummy )
		{
			if ( IsValid( bestSnapObjectOnDummy ) )
				bestSnapObjectOnDummy.SetVisibility( true );
		}

		if ( IsValid( bestSnapObjectOnDummy ) )
			bestSnapObjectOnDummy.SetVisibility( false );

		lastBestAttachObject = bestSnapObjectOnDummy;
	}
}