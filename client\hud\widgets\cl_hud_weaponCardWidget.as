UCLASS( Abstract )
class UAS_WeaponCardWidget : UNC_DisplayWidget
{
	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	protected UImage icon;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	protected UImage background;

	protected ANCWeapon equippedWeapon;
	protected TOptional<FWeaponData> optWeaponData;
	protected TOptional<FBackpackItemStruct> optWeaponItemData;
	protected TOptional<FLootDataStruct> optWeaponLootData;

	UFUNCTION( BlueprintOverride )
	protected void Construct()
	{
		AAS_HUD raidHUD = GetLocalHUD();
		if ( IsValid( raidHUD ) )
		{
			raidHUD.onLocalPlayerWeaponModsChanged.AddUFunction( this, n"OnLocalPlayerWeaponModsChanged" );
		}

		ClientCallbacks().OnLocalWeaponClipAmmoChanged.AddUFunction( this, n"OnLocalWeaponClipAmmoChanged" );
		ClientCallbacks().OnLocalWeaponStockpileAmmoChanged.AddUFunction( this, n"OnLocalWeaponStockpileAmmoChanged" );

		AAS_PlayerEntity localPlayer = Client_GetLocalASPawn();
		if ( IsValid( localPlayer ) && IsValid( localPlayer.ammoBackpackComponent ) )
		{
			localPlayer.ammoBackpackComponent.OnContentsChangedDelegate.AddUFunction( this, n"OnBackpackContentsChanged" );
		}
	}

	void SetWeapon( ANCWeapon newWeapon )
	{
		equippedWeapon = WeaponIsValidForSlot( newWeapon ) ? newWeapon : nullptr;
		bool equippedWeaponIsValid = IsValid( equippedWeapon );
		
		optWeaponLootData.Reset();
		optWeaponItemData.Reset();
		optWeaponData.Reset();

		if ( equippedWeaponIsValid )
		{
			if ( IsLootIndexValidForWeapon( equippedWeapon ) )
			{
				optWeaponLootData = GetLootDataForWeapon( equippedWeapon );
				optWeaponItemData = MakeBackpackItem( equippedWeapon );
			}
			optWeaponData = equippedWeapon.GetWeaponData();
		}

		EmptyWeaponCard( !equippedWeaponIsValid );

		Update();
	}

	const bool WeaponIsValidForSlot( ANCWeapon newWeapon )
	{
		// Not all weapons passed in are valid to be shown in this UI element, we want to filter out melee weapons
		return IsValid( newWeapon ) && ( newWeapon.WeaponSlot == WeaponSlot::PrimarySlot0 || newWeapon.WeaponSlot == WeaponSlot::PrimarySlot1 || newWeapon.WeaponSlot == WeaponSlot::RaidToolsSlot || newWeapon.WeaponSlot == WeaponSlot::AbilityWeaponSwapSlot );
	}

	// TODO @Davis: This will be wrong if your weapon has a mod equipped. This weapon data is gathered from the weapon DT, not a live weapon. Mod data needs to be applied to this.
	void SetWeaponData( FWeaponData inWeaponData, FBackpackItemStruct inItemData )
	{
		equippedWeapon = nullptr;
		optWeaponData.Set( inWeaponData );
		optWeaponItemData.Set( inItemData );
		optWeaponLootData.Set( GetLootDataByIndex( optWeaponItemData.Value.itemIndex ) );

		Update();
	}

	TOptional<FBackpackItemStruct> GetEquippedWeaponItemData()
	{
		return optWeaponItemData;
	}

	void ClearWeaponData()
	{
		if ( !optWeaponData.IsSet() && !optWeaponItemData.IsSet() && !IsValid( equippedWeapon ) )
			return;

		// Empty the weapon card if the weapon data is cleared
		EmptyWeaponCard( true );

		Update();
	}

	protected void Update()
	{
		UpdateIcon();
		UpdateRarity();
		InvalidateLayoutAndVolatility();
	}

	protected bool isPrimaryWeapon()
	{
		bool success = false;

		if ( IsValid( equippedWeapon ) )
		{
			success = equippedWeapon.WeaponSlot == WeaponSlot::PrimarySlot0 ||
					  equippedWeapon.WeaponSlot == WeaponSlot::PrimarySlot1;
		}

		return success;
	}

	protected bool IsRaidTool()
	{
		bool success = false;

		if ( IsValid( equippedWeapon ) )
		{
			success = equippedWeapon.WeaponSlot == WeaponSlot::RaidToolsSlot;
		}

		return success;
	}

	protected int GetModsBitfield()
	{
		int modsBitfield = -1;

		if ( IsValid( equippedWeapon ) )
		{
			modsBitfield = equippedWeapon.GetActiveModsBitField();
		}
		else if ( optWeaponItemData.IsSet() )
		{
			modsBitfield = optWeaponItemData.Value.customInt2;
		}

		return modsBitfield;
	}

	UFUNCTION( NotBlueprintCallable )
	protected void OnLocalPlayerWeaponModsChanged( ANCWeapon changedWeapon )
	{
		if ( changedWeapon != equippedWeapon )
			return;

		Update();
	}

	protected void UpdateIcon()
	{
		if ( optWeaponData.IsSet() )
		{
			icon.SetBrushFromTexture( optWeaponData.Value.Icon );
		}
	}

	protected void UpdateRarity()
	{
		FLinearColor rarityColor = GetRarityColor( GameplayTags::Loot_Rarity_Common );

		UMaterialInstanceDynamic backgroundDynMat = background.GetDynamicMaterial();

		if ( optWeaponLootData.IsSet() )
		{
			const FLootDataStruct& weaponData = GetLootDataByIndex( optWeaponLootData.Value.index );
			rarityColor = GetRarityColor( weaponData.rarity );
			
			int weaponLevel = GetLootRarityLevel( optWeaponLootData.GetValue() );
			// Don't ask me why, I can't do an int cast for this >:(
			int isNotCommon = weaponLevel > 0 ? 1 : 0;
			backgroundDynMat.SetScalarParameterValue( n"ShowTexture", isNotCommon );
			backgroundDynMat.SetScalarParameterValue( n"RadialGradientOpacity", float( isNotCommon ) * 0.75 );
		}

		backgroundDynMat.SetVectorParameterValue( n"RarityColor", rarityColor );
	}

	UFUNCTION( NotBlueprintCallable )
	protected void OnLocalWeaponClipAmmoChanged( ANCWeapon weapon, int oldValue, int newValue )
	{
		// Intentionally empty
	}

	UFUNCTION( NotBlueprintCallable )
	protected void OnLocalWeaponStockpileAmmoChanged( ANCWeapon weapon, int oldValue, int newValue )
	{
		// Intentionally empty
	}

	UFUNCTION( NotBlueprintCallable )
	protected void OnBackpackContentsChanged( UBackpackComponent newBackpack )
	{
		// Intentionally empty
	}

	protected void EmptyWeaponCard( bool isEmpty )
	{
		SetWidgetVisibilitySafe( icon, isEmpty ? ESlateVisibility::Hidden : ESlateVisibility::SelfHitTestInvisible );
		SetWidgetVisibilitySafe( background, isEmpty ? ESlateVisibility::Hidden : ESlateVisibility::SelfHitTestInvisible );

		if ( isEmpty )
		{
			equippedWeapon = nullptr;
			optWeaponData.Reset();
			optWeaponItemData.Reset();
			optWeaponLootData.Reset();

			UpdateRarity();
		}
	}
}