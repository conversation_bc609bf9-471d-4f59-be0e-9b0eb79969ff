UCL_GlobalParameterSystem GetGlobalParameters()
{
	UCL_GlobalParameterSystem widgetManager = Cast<UCL_GlobalParameterSystem>( UNCGameplaySystemsSubsystem::Get_ClientSystem( GetCurrentWorld(), UCL_GlobalParameterSystem::StaticClass() ) );
	return widgetManager;
}

UCLASS( Abstract )
class UCL_GlobalParameterSystem : UNCGameplaySystem_Client
{
	// adding this here so it gets added to builds
	UPROPERTY( EditDefaultsOnly )
	UNiagaraParameterCollection globalNiagaraParameters;

	UPROPERTY( EditDefaultsOnly )
	UMaterialParameterCollection globalPlayerParameters;

	UPROPERTY( EditDefaultsOnly )
	UMaterialParameterCollection interactionParameters;

	UPROPERTY( EditDefaultsOnly )
	UMaterialParameterCollection weaponParameters;

	UPROPERTY( EditDefaultsOnly )
	UMaterialParameterCollection screenDamageIndicator;

	UPROPERTY( EditDefaultsOnly )
	UMaterialParameterCollection gameplayParameters;

	UPROPERTY( EditDefaultsOnly )
	UMaterialParameterCollection hightlightParameters;

	UPROPERTY( EditDefaultsOnly )
	UMaterialParameterCollection commonUiColors;

	AAS_PlayerEntity localPlayer;

	private const FName ADS_PARAMETER = n"playerADS";
	private const FName FOV_PARAMETER = n"playerFOV";

	UFUNCTION( BlueprintOverride )
	void Initialize()
	{
		localPlayer = Cast<AAS_PlayerEntity>( ClientCallbacks().LocalPlayer );
		Thread( this, n"UpdateThread" );
	}

	UFUNCTION()
	void UpdateThread( UNCCoroutine co )
	{
		if ( !IsValid( localPlayer ) )
			return;

		co.EndOnDestroyed( localPlayer );

		while ( true )
		{
			float lastAds	 = Material::GetScalarParameterValue( globalPlayerParameters, ADS_PARAMETER );
			float currentAds = localPlayer.GetAdsFraction();
			if ( !Math::IsNearlyEqual( lastAds, currentAds ) )
			{
				Material::SetScalarParameterValue( globalPlayerParameters, ADS_PARAMETER, currentAds );
			}

			float lastFov		 = Material::GetScalarParameterValue( globalPlayerParameters, FOV_PARAMETER );
			float worldCameraFOV = localPlayer.GetPlayerCameraComponent().FieldOfView;
			float currentFov	 = Math::Min( worldCameraFOV / 90.0, 1.0 );
			if ( !Math::IsNearlyEqual( lastFov, currentFov ) )
			{
				Material::SetScalarParameterValue( globalPlayerParameters, FOV_PARAMETER, currentFov );
			}

			co.Wait( 0.01f );
		}
	}
}