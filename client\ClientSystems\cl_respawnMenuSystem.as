UAS_RespawnMenuSystem RespawnMenuSystem()
{
	return Cast<UAS_RespawnMenuSystem>( UNCGameplaySystemsSubsystem::Get_ClientSystem( GetCurrentWorld(), UAS_RespawnMenuSystem::StaticClass() ) );
}

// TODO: Does this need to be a shared system? Can it just be server?
UCLASS()
class UAS_RespawnMenuSystem : UNCGameplaySystem_Client
{
	// Set respawn style (networked)
	// Set respawn location (networked)

	private float deathRecapDuration = 5.0;
	private bool skipDeathRecap = false;
	private bool doesInputChangeRespawnMenuTabs = true;
	private bool doesDeathRecapAutoClose = true;

	bool GetShouldSkipDeathRecap() const
	{
		return skipDeathRecap;
	}

	void SetShouldSkipDeathRecap()
	{
		skipDeathRecap = true;
	}

	void ClearShouldSkipDeathRecap()
	{
		skipDeathRecap = false;
	}

	void SetCanInputChangeRespawnMenuTabs()
	{
		doesInputChangeRespawnMenuTabs = true;
	}

	void ClearCanInputChangeRespawnMenuTabs()
	{
		doesInputChangeRespawnMenuTabs = false;
	}

	bool CanInputChangeRespawnMenuTabs() const
	{
		return doesInputChangeRespawnMenuTabs;
	}

	bool GetDoesDeathRecapAutoClose() const
	{
		return doesDeathRecapAutoClose;
	}

	void SetDoesDeathRecapAutoClose()
	{
		doesDeathRecapAutoClose = true;
	}

	void ClearDoesDeathRecapAutoClose()
	{
		doesDeathRecapAutoClose = false;
	}

	float GetDeathRecapDuration() const
	{
		return deathRecapDuration;
	}

	void SetDeathRecapDuration( float newDuration )
	{
		deathRecapDuration = newDuration;
	}
}