UCLASS( Abstract )
class UAS_WeaponContext_RaidUlt_LionRoar : UAS_RaidUltWeaponContext_Base
{
	default cooldownTime = 540;

	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<AAS_LionRoar> lionRoarClass;

	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<AAS_LionRoarCapsuleCollision> capsuleClass;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset activateSound_1P;
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset activateSound_3P;
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset idleSound_1P;
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset idleSound_3P;
	UPROPERTY( EditDefaultsOnly )
	UMaterialInterface highlightMaterial;

	TArray<ASoundActor> idleSoundActors;

	bool active = false;
	ANCWeapon savedWeapon;

	UFUNCTION( BlueprintOverride )
	void CodeCallback_OnWeaponActivate( ANCWeapon weapon )
	{
		Super::CodeCallback_OnWeaponActivate(weapon);

		savedWeapon					 = weapon;

		Activate();
	}

	UFUNCTION( BlueprintOverride )
	void CodeCallback_OnWeaponDeactivate( ANCWeapon weapon )
	{
		Super::CodeCallback_OnWeaponDeactivate( weapon );
		Deactivate();
	}

	protected void Activate()
	{
		if ( active )
			return;

		if ( !IsValid( savedWeapon ) )
			return;

		active = true;

		OnActivate();
	}

	protected void Deactivate()
	{
		if ( !active )
			return;

		active = false;

		OnDeactivate();
	}

	UAS_LionRoarClientThread roarThread;

	void OnActivate()
	{
		ANCWeapon weapon = savedWeapon;
		weapon.PlayWeaponSound( activateSound_1P, activateSound_3P );
		AAS_PlayerEntity asPawn = Cast<AAS_PlayerEntity>( weapon.GetWeaponOwner() );

		weapon.AddMod( n"EarlyCancel" );

		if ( IsServer() )
		{
			idleSoundActors = Server_EmitSoundOnEntity_1P3P_ReturnEntities( idleSound_1P, idleSound_3P, asPawn, asPawn );
		}

		if ( IsClient() && asPawn.IsLocallyControlled() )
		{
			roarThread = Cast<UAS_LionRoarClientThread>( CreateThread( UAS_LionRoarClientThread::StaticClass(), weapon ) );
			roarThread.Init( this, weapon.GetWeaponOwner() );
			asPawn.overrideGoalFOV.AddUFunction( this, n"InhaleOverrideGoalFOV" );
			CreateScreenEffect( asPawn );
		}
	}

	void OnDeactivate()
	{
		AAS_PlayerEntity asPawn = Cast<AAS_PlayerEntity>( savedWeapon.GetWeaponOwner() );

		for ( auto a : idleSoundActors )
			a.Destroy();
		idleSoundActors.Empty();

		if ( roarThread != nullptr && roarThread.IsRunning() )
		{
			roarThread.Cancel();
			roarThread = nullptr;
		}

		if ( IsClient() && asPawn.IsLocallyControlled() )
		{
			asPawn.overrideGoalFOV.Unbind( this, n"InhaleOverrideGoalFOV" );
			asPawn.overrideGoalFOV.Unbind( this, n"ExhaleOverrideGoalFOV" );
			TeardownScreenEffect( asPawn );
		}
	}

	FWeaponPrimaryAttackInfo cachedAttackInfo;
	UFUNCTION( BlueprintOverride, Meta = (NoSuperCall) )
	void CodeCallback_OnWeaponPrimaryAttack( ANCWeapon weapon, FWeaponPrimaryAttackInfo attackInfo,
											 FWeaponPrimaryAttackReturnParams& info )
	{
		//Super::CodeCallback_OnWeaponPrimaryAttack( weapon, attackInfo, info );
		cachedAttackInfo = attackInfo;
		System::SetTimer( this, n"DelayedRoar", 0.45, false );

		ANCPlayerCharacter owner = savedWeapon.GetWeaponOwner();
		if ( savedWeapon.HasMod( n"EarlyCancel" ) )
			savedWeapon.RemoveMod( n"EarlyCancel" );

		if ( IsServer() )
		{
			ConsumeRaidUlt( weapon );
			//owner.AddStatusEffect( GameplayTags::StatusEffect_TurnSlow, 0.8, 1.0, 0.0, 0.5 );
			owner.AddStatusEffect( GameplayTags::StatusEffect_MoveSlow, 0.5, 0.5, 0.0, 0.5 );
		}
	}

	UFUNCTION( BlueprintOverride )
	bool CodeCallback_CanFireWeapon( ANCWeapon weapon )
	{
		return Super::CodeCallback_CanFireWeapon( weapon );
	}

	UFUNCTION() // todo: this is hacky until we get a proper code fix for delayed primary attack
	void DelayedRoar()
	{
		AAS_PlayerEntity asPawn = Cast<AAS_PlayerEntity>( savedWeapon.GetWeaponOwner() );
		if ( IsServer() )
		{
			// DrawDebugLine( attackInfo.eyePosition, attackInfo.eyePosition + (attackInfo.shotDirection * 5000), 30 );

			ANCPlayerCharacter owner = savedWeapon.GetWeaponOwner();
			AAS_LionRoar roar		 = Spawn( owner, cachedAttackInfo.eyePosition, cachedAttackInfo.shotDirection.Rotation() );
			roar.SetOwnerWeapon( savedWeapon );

			AAS_ShieldBreaker breacher = GetBreacherForTeam( owner.GetTeam() );
			if ( IsValid( breacher ) )
			{
				roar.SetBreacher( breacher );
			}
		}

		if ( IsClient() && asPawn.IsLocallyControlled() )
		{
			asPawn.PlayScreenShake( 15, 6, 1.0, 1.0, 0.0, 0.5 );
			TeardownScreenEffect( asPawn );

			asPawn.overrideGoalFOV.Unbind( this, n"InhaleOverrideGoalFOV" );
			asPawn.overrideGoalFOV.AddUFunction( this, n"ExhaleOverrideGoalFOV" );
			System::SetTimer( this, n"UnbindExhale", 0.5, false );
		}

		for ( auto a : idleSoundActors )
			a.Destroy();
		idleSoundActors.Empty();
	}

	AAS_LionRoar Spawn( ANCPlayerCharacter playerOwner, FVector position, FRotator rotation )
	{
		return Cast<AAS_LionRoar>( Server_SpawnEntity( lionRoarClass, playerOwner, position, rotation ) );
	}

	UFUNCTION()
	void InhaleOverrideGoalFOV( float& goal )
	{
		goal = goal + 20;
	}

	UFUNCTION()
	void ExhaleOverrideGoalFOV( float& goal )
	{
		goal = goal - 15;
	}

	UFUNCTION()
	void UnbindExhale()
	{
		AAS_PlayerEntity asPawn = Cast<AAS_PlayerEntity>( savedWeapon.GetWeaponOwner() );
		asPawn.overrideGoalFOV.Unbind( this, n"ExhaleOverrideGoalFOV" );
	}

	int handle = -1;

	UFUNCTION()
	void CreateScreenEffect( ANCPlayerCharacter p )
	{
		if ( !p.IsLocallyControlled() )
			return;
		TeardownScreenEffect( p );
		UAS_ScreenEffect_Redmane screenEffect = GetScreenEffect_Redmane();
		if ( !IsValid( screenEffect ) )
			return;

		FScreenStatusIndicatorData newStatusIndicatorData;
		newStatusIndicatorData.fullscreenAmount		   = 0.5;
		newStatusIndicatorData.fullscreenDepth		   = 0.2;
		newStatusIndicatorData.fullscreenGlowIntensity = 0.9;

		newStatusIndicatorData.baseColor			 = FLinearColor( 0.55, 0.00, 0.00 );
		newStatusIndicatorData.glowColor			 = FLinearColor::White;
		newStatusIndicatorData.modulateTextureAmount = 0.4f;

		newStatusIndicatorData.modulateScaleX = 2.0f;
		newStatusIndicatorData.modulateScaleY = 0.192f;
		newStatusIndicatorData.modulateSpeedX = 0;
		newStatusIndicatorData.modulateSpeedY = -0.12;

		handle = screenEffect.RegisterStatusIndicator( newStatusIndicatorData );
	}

	UFUNCTION()
	void TeardownScreenEffect( ANCPlayerCharacter p )
	{
		UAS_ScreenEffect_Redmane screenEffect = GetScreenEffect_Redmane();
		if ( !IsValid( screenEffect ) )
			return;
		if ( !screenEffect.IsHandleValid( handle ) )
			return;

		FScreenStatusIndicatorData& registeredData = screenEffect.GetStatusIndicatorData( handle );
		float curTime							   = GetGameTimeMS();
		float endTime							   = curTime + TO_MILLISECONDS( 1.5f );
		registeredData.LerpDepth( 0.0, curTime, endTime, true );
	}
}

UCLASS()
class UAS_LionRoarClientThread : UAS_Thread
{
	ANCPlayerCharacter playerOwner;
	UAS_WeaponContext_RaidUlt_LionRoar weaponContext;
	AAS_LionRoarCapsuleCollision capsule;

	void Init( UAS_WeaponContext_RaidUlt_LionRoar inWeaponContext, ANCPlayerCharacter _playerOwner )
	{
		weaponContext = inWeaponContext;
		playerOwner	  = _playerOwner;
		Start();
	}

	TArray<AActor> targets;

	void OnThreadStart( UNCCoroutine co ) override
	{
		Super::OnThreadStart( co );

		capsule = Cast<AAS_LionRoarCapsuleCollision>( SpawnActor( weaponContext.capsuleClass, playerOwner.GetPawnViewLocation(), playerOwner.GetViewRotation() ) );
		capsule.decalMaterial.SetDecalMaterial( weaponContext.highlightMaterial );

		while ( true )
		{
			capsule.SetActorLocation( playerOwner.GetPawnViewLocation() );
			capsule.SetActorRotation( playerOwner.GetViewRotation() );

			ClearVisualEffects();

			targets = capsule.GetTargetActors( playerOwner );

			for ( AActor target : targets )
			{
				if ( !IsValid( target ) )
					continue;

				// if you are not a code destructible you are a script object
				if ( !target.Class.IsChildOf( ANCDestructible::StaticClass() ) )
				{
					ApplyVisualEffects( target );
				}
			}

			co.Wait( 0.016 );
		}
	}

	void ApplyVisualEffects( const AActor actor )
	{
		TArray<USceneComponent> comps;
		actor.GetComponentsByClass( USceneComponent::StaticClass(), comps );
		for ( USceneComponent component : comps )
		{
			if ( component.IsA( UAS_StructureUpgradeVisual::StaticClass() ) )
			{
				UAS_StructureUpgradeVisual upgradeVisual = Cast<UAS_StructureUpgradeVisual>( component );
				if ( upgradeVisual.levelForVisual != 1 )
					continue; // ignore upgrade visuals for future levels
			}
			UStaticMeshComponent meshComponent = Cast<UStaticMeshComponent>( component );
			if ( IsValid( meshComponent ) )
			{
				meshComponent.NCSetReceivesNCSpecialDecals( true );
			}
		}
		sceneComps.Append( comps );
	}

	TArray<USceneComponent> sceneComps;

	void ClearVisualEffects()
	{
		for ( USceneComponent component : sceneComps )
		{
			if ( !IsValid( component ) )
				continue;

			if ( component.IsA( UAS_StructureUpgradeVisual::StaticClass() ) )
			{
				UAS_StructureUpgradeVisual upgradeVisual = Cast<UAS_StructureUpgradeVisual>( component );
				if ( upgradeVisual.levelForVisual != 1 )
					continue; // ignore upgrade visuals for future levels
			}
			UStaticMeshComponent meshComponent = Cast<UStaticMeshComponent>( component );
			if ( IsValid( meshComponent ) )
			{
				meshComponent.NCSetReceivesNCSpecialDecals( false );
			}
		}
		sceneComps.Empty();
	}

	void OnThreadEnd( FNCCoroutineEndParams params ) override
	{
		Super::OnThreadEnd( params );
		capsule.Destroy();
	}
}