bool HasChargeMod( ANCWeapon weapon )
{
	TArray<FName> activeMods;
	weapon.GetActiveMods( activeMods );
	return activeMods.Contains( n"Charges" );
}





UCLASS()
class UAS_WeaponContext_AutomatedDrone : UAS_WeaponContext_TacticalAbility
{
	default cooldownTime = GameConst::AUTOMATED_DRONE_CHARGE_COOLDOWN_TIME;
	default battleChatterLine = GameplayTags::Audio_VO_BattleChatter_UsingAbility_Tactical;

	ANCWeapon cachedOwnerWeapon;

	UFUNCTION(BlueprintOverride)
	void CodeCallback_OnWeaponDataSet(ANCWeapon weapon)
	{
		Super::CodeCallback_OnWeaponDataSet(weapon);
		
		if ( IsServer() )
			ScriptCallbacks().shared_onAutomatedDroneDestroyed.AddUFunction( this, n"OnDroneDestroyed" );
	}

	UFUNCTION( BlueprintOverride )
	void CodeCallback_OnWeaponActivate( ANCWeapon weapon )
	{
		cachedOwnerWeapon = weapon;
	}

	UFUNCTION( BlueprintOverride, meta = ( NoSuperCall ) )
	void CodeCallback_OnWeaponPrimaryAttack( ANCWeapon weapon, FWeaponPrimaryAttackInfo attackInfo,
											 FWeaponPrimaryAttackReturnParams& returnInfo )
	{
		ScriptCallbacks().shared_onAutomatedDroneThrown.Broadcast( cachedOwnerWeapon.WeaponOwner );

		if ( !IsServer() )
			return;

		AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( weapon.GetWeaponOwner() );
		if ( !HasChargeMod( weapon ) )
		{
			if ( IsValid( player ) )
			{
				ANCWeapon automatedDroneWeapon = player.GetWeaponAtSlot( WeaponSlot::TacticalSlot );
				FVector spawnLoc = player.GetEyeLocation() + ( player.GetActorForwardVector() * 1200 );
				if ( IsValid( automatedDroneWeapon ) )
					SpawnAutomatedDrone( spawnLoc, player, automatedDroneWeapon );
			}
		}
		else
		{
			AAS_AutomatedDrone drone = GetAutomatedDroneFromPlayer( player );
			if ( IsValid( drone ) )
			{
				drone.ReadySurveyCharge();

				if ( drone.remainingSurveyCharges == 0 )
					Thread( this, n"Thread_TransitionFromChargeWeapon", cachedOwnerWeapon, true, false );
				else
					player.tacticalCooldownComponent.StartCooldown( player.GetWeaponAtSlot( WeaponSlot::TacticalSlot ), GameConst::AUTOMATED_DRONE_CHARGE_COOLDOWN_TIME );
			}
		}
	}

	UFUNCTION()
	void OnDroneDestroyed( AAS_AutomatedDrone drone, AAS_PlayerEntity owner )
	{
		if ( !IsValid( cachedOwnerWeapon ) )
			return;

		AAS_PlayerEntity playerOwner = Cast<AAS_PlayerEntity>( cachedOwnerWeapon.GetWeaponOwner() );
		if ( !IsValid( playerOwner ) )
			return;

		if ( owner != playerOwner )
			return;

		if ( !HasChargeMod( cachedOwnerWeapon ) )
			return;

		Thread( this, n"Thread_TransitionFromChargeWeapon", cachedOwnerWeapon, false, false );
	}

	UFUNCTION()
	void Thread_TransitionFromChargeWeapon( UNCCoroutine co, ANCWeapon weapon, bool shouldWait, bool shouldHalveCooldown )
	{
		if ( shouldWait )
			co.Wait( 2.0 );

		if ( !IsValid( weapon ) )
			return;
		AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( weapon.GetWeaponOwner() );
		if ( !IsValid( player ) )
			return;

		weapon.SetClipAmmo( 0 );
		float32 cooldown = GameConst::AUTOMATED_DRONE_COOLDOWN_TIME;
		if ( shouldHalveCooldown )
			cooldown = cooldown / 2.0f;
		player.tacticalCooldownComponent.StartCooldown( player.GetWeaponAtSlot( WeaponSlot::TacticalSlot ), cooldown );
		weapon.RemoveMod( n"Charges" );
	}
}