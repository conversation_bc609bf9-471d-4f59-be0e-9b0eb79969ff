UCLASS( Abstract )
class UAS_InputContextWidget : UUserWidgetDefault
{
	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_KeybindWidget keybind;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UTextBlock text;

	UFUNCTION()
	void SetInputContext( FName inputAction, FText context, bool isHold = false )
	{
		keybind.SetInputAction( inputAction );
		keybind.SetHoldButton( isHold );
		text.SetText( context );
	}
}