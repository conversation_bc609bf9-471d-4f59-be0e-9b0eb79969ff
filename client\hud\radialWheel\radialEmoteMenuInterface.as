UCLASS( Abstract )
class UAS_RadialEmoteMenuData : UAS_RadialMenuData
{
	UFUNCTION( BlueprintOverride )
	bool Populate( TArray<UAS_RadialMenuListItemData>& results )
	{
		FGameplayTag characterTag	= Client_GetLocalASPawn().classManager.GetClassIndex();
		UAS_EmoteSystem emoteSystem = EmoteSystem();
		int numOptions				= EmoteSystem().GetMaxEmoteSlots();
		results.Empty( numOptions );

		for ( int i = 0; i < numOptions; i++ )
		{
			UAS_EmoteWheelItemData item	   = UAS_EmoteWheelItemData();
			const UNCMTXRuntimeEmote emote = EmoteSystem().GetEmote( Client_GetLocalASPawn(), characterTag, i );
			item.emoteIndex				   = i;
			item.isMountEmote			   = false;
			item.isAvailable			   = true;

			if ( IsValid( emote ) )
			{
				const UNCMTXRuntimeEmote e = emote;
				FNC_RadialListItemDefinition definition;
				definition.Icon				   = FSlateBrush();
				definition.Icon.ResourceObject = e.Icon;
				definition.Label			   = e.DisplayName;
				item.SetDefinition( definition );
			}

			results.Add( item );
		}

		return !results.IsEmpty();
	}

	UFUNCTION( BlueprintOverride )
	bool SelectItem( UAS_RadialMenuListItemData data )
	{
		FGameplayTag characterTag		 = Client_GetLocalASPawn().classManager.GetClassIndex();
		UAS_EmoteWheelItemData emoteData = Cast<UAS_EmoteWheelItemData>( data );
		bool result						 = false;
		const UNCMTXRuntimeEmote emote	 = EmoteSystem().GetEmote( Client_GetLocalASPawn(), characterTag, emoteData.emoteIndex );
		if ( !IsValid( emote ) )
			return result;

		result							 = true;
		UNCEmoteRequest emoteEvent = Cast<UNCEmoteRequest>( NewObject( GetCurrentWorld(), UNCEmoteRequest::StaticClass() ) );
		emoteEvent.net_isMountEmote.SetNetValue( false );
		emoteEvent.net_requestedEmote.SetNetValue( emoteData.emoteIndex );
		emoteEvent.SendToServer();
		return result;
	}
}

UCLASS( Abstract )
class UAS_RadialMountEmoteMenuData : UAS_RadialMenuData
{
	UFUNCTION( BlueprintOverride )
	bool Populate( TArray<UAS_RadialMenuListItemData>& results )
	{
		// Empty the results but ensure we reserve space for the teleport types given
		FGameplayTag mountTag		= Client_GetLocalASPawn().GetEquippedMountTag();
		UAS_EmoteSystem emoteSystem = EmoteSystem();
		int numOptions				= emoteSystem.GetMaxMountEmoteSlots();
		results.Empty( numOptions );

		for ( int i = 0; i < numOptions; i++ )
		{
			UAS_EmoteWheelItemData item	   = UAS_EmoteWheelItemData();
			item.emoteIndex				   = i;
			item.isMountEmote			   = true;
			item.isAvailable			   = true;
			const UNCMTXRuntimeEmote emote = emoteSystem.GetMountEmote( Client_GetLocalASPawn(), mountTag, i );
			FNC_RadialListItemDefinition definition;
			definition.Icon				   = FSlateBrush();
			definition.Icon.ResourceObject = emote.Icon;
			definition.Label			   = emote.DisplayName;
			item.SetDefinition( definition );
			results.Add( item );
		}

		return !results.IsEmpty();
	}

	UFUNCTION( BlueprintOverride )
	bool SelectItem( UAS_RadialMenuListItemData data )
	{
		UAS_EmoteWheelItemData emoteData = Cast<UAS_EmoteWheelItemData>( data );
		bool result						 = false;
		FGameplayTag mountTag			 = Client_GetLocalASPawn().GetEquippedMountTag();
		const UNCMTXRuntimeEmote emote	 = EmoteSystem().GetMountEmote( Client_GetLocalASPawn(), mountTag, emoteData.emoteIndex );
		if ( !IsValid( emote ) )
			return result;

		result							 = true;
		UNCEmoteRequest emoteEvent = Cast<UNCEmoteRequest>( NewObject( GetCurrentWorld(), UNCEmoteRequest::StaticClass() ) );
		emoteEvent.net_isMountEmote.SetNetValue( true );
		emoteEvent.net_requestedEmote.SetNetValue( emoteData.emoteIndex );
		emoteEvent.SendToServer();
		return result;
	}
}

UCLASS( Abstract )
class UAS_RadialCombinedMountAndEmoteMenuData : UAS_RadialMenuData
{
	int TOTAL_SLOTS		  = 10;
	int NUM_REGULAR_SLOTS = 5;

	default radialMenuAngleOffset = -1 * ( 360 / TOTAL_SLOTS ) * ( TOTAL_SLOTS / NUM_REGULAR_SLOTS );

	//
	int PlayerSlotToRadialSlot( int playerSlot )
	{
		// 2 - 3 - 1 - 4 - 0
		int offset = Math::CeilToInt( playerSlot * 0.5f );
		int middle = ( NUM_REGULAR_SLOTS / 2 );

		if ( playerSlot % 2 == 0 )
			return middle - offset;
		else
			return middle + offset;
	}

	int RadialSlotToPlayerSlot( int radialSlot )
	{
		int last = NUM_REGULAR_SLOTS - 1;
		int i	 = radialSlot;
		int mid	 = NUM_REGULAR_SLOTS / 2;

		if ( i <= mid )
			return last - ( 2 * i );
		else
			return ( ( i - mid ) / 2 ) + 1;
	}

	UFUNCTION( BlueprintOverride )
	bool Populate( TArray<UAS_RadialMenuListItemData>& results )
	{
		results.Empty();
		FGameplayTag characterTag	= Client_GetLocalASPawn().classManager.GetClassIndex();
		FGameplayTag mountTag		= Client_GetLocalASPawn().GetEquippedMountTag();
		UAS_EmoteSystem emoteSystem = EmoteSystem();
		int numMountOptions			= emoteSystem.GetMaxMountEmoteSlots();
		int numOptions				= 0;
		numOptions					= emoteSystem.GetMaxEmoteSlots();

		for ( int i = 0; i < NUM_REGULAR_SLOTS; i++ )
		{
			UAS_EmoteWheelItemData item = UAS_EmoteWheelItemData();
			int translatedSlot			= RadialSlotToPlayerSlot( i );

			if ( translatedSlot < numOptions )
			{
				item.emoteIndex				   = translatedSlot;
				item.isMountEmote			   = false;
				item.isAvailable			   = true;
				const UNCMTXRuntimeEmote emote = EmoteSystem().GetEmote( Client_GetLocalASPawn(), characterTag, item.emoteIndex );
				FNC_RadialListItemDefinition definition;
				definition.Icon = FSlateBrush();
				if ( IsValid( emote ) )
				{
					const UNCMTXRuntimeEmote e	   = emote;
					definition.Icon.ResourceObject = e.Icon;
					definition.Label			   = e.DisplayName;
				}
				item.SetDefinition( definition );
			}
			else
			{
				item.isAvailable = false;
			}

			results.Add( item );
		}

		for ( int i = NUM_REGULAR_SLOTS; i < TOTAL_SLOTS; i++ )
		{
			UAS_EmoteWheelItemData item = UAS_EmoteWheelItemData();
			int translatedSlot			= RadialSlotToPlayerSlot( i - NUM_REGULAR_SLOTS );

			if ( translatedSlot < numMountOptions )
			{
				int emoteIndex				   = translatedSlot;
				item.emoteIndex				   = emoteIndex;
				item.isMountEmote			   = true;
				item.isAvailable			   = true;
				const UNCMTXRuntimeEmote emote = emoteSystem.GetMountEmote( Client_GetLocalASPawn(), mountTag, emoteIndex );
				FNC_RadialListItemDefinition definition;
				if ( IsValid( emote ) )
				{
					definition.Icon				   = FSlateBrush();
					definition.Icon.ResourceObject = emote.Icon;
					definition.Label			   = emote.DisplayName;
				}
				item.SetDefinition( definition );
			}
			else
			{
				item.isAvailable = false;
			}
			results.Add( item );
		}

		return !results.IsEmpty();
	}

	UFUNCTION( BlueprintOverride )
	bool SelectItem( UAS_RadialMenuListItemData data )
	{
		UAS_EmoteWheelItemData emoteData = Cast<UAS_EmoteWheelItemData>( data );

		if ( !emoteData.isAvailable )
			return false;

		FGameplayTag characterTag = Client_GetLocalASPawn().classManager.GetClassIndex();
		const UNCMTXRuntimeEmote emote;
		if ( emoteData.isMountEmote )
		{
			FGameplayTag mountTag = Client_GetLocalASPawn().GetEquippedMountTag();
			emote				  = EmoteSystem().GetMountEmote( Client_GetLocalASPawn(), mountTag, emoteData.emoteIndex );
		}
		else
		{
			emote = EmoteSystem().GetEmote( Client_GetLocalASPawn(), characterTag, emoteData.emoteIndex );
		}

		bool result = false;
		if ( !IsValid( emote ) )
			return result;

		result							 = true;
		UNCEmoteRequest emoteEvent = Cast<UNCEmoteRequest>( NewObject( GetCurrentWorld(), UNCEmoteRequest::StaticClass() ) );
		emoteEvent.net_isMountEmote.SetNetValue( emoteData.isMountEmote );
		emoteEvent.net_requestedEmote.SetNetValue( emoteData.emoteIndex );
		emoteEvent.SendToServer();
		return result;
	}
}