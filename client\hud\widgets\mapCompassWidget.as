UCLASS( Abstract )
class UAS_MapCompassWidget : UNC_DisplayWidget
{
	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage compass;

	private FVector northForward;
	private FVector eastForward;
	private UMaterialInstanceDynamic compassMaterial;

	// The value in pixels where the N for north is centered in the source asset
	private const float NORTH_OFFSET = 19;
	private const float CARDINAL_RANGE = 20.0f;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		compassMaterial = CreateDynamicMaterialFromImageBrush( compass );
		if ( IsValid( compassMaterial ) )
		{
			compass.SetBrushFromMaterial( compassMaterial );
		}

		GetCardinalDirectionsFromSettings( northForward, eastForward );
	}

	UFUNCTION( BlueprintOverride )
	void Tick( FGeometry MyGeometry, float InDeltaTime )
	{
		FVector cameraLocation;
		FRotator cameraRotation;

		APlayerController playerController = GetOwningPlayer();
		if ( IsValid( playerController ) && IsValid( compassMaterial ) )
		{
			GetCardinalDirectionsFromSettings( northForward, eastForward );
			playerController.GetPlayerViewPoint( cameraLocation, cameraRotation );

			FVector flatCameraForward = cameraRotation.ForwardVector;
			flatCameraForward.Z		  = 0;
			flatCameraForward.Normalize();

			float degreesOffNorth = DotToAngles( flatCameraForward.DotProduct( northForward ) );
			if ( flatCameraForward.DotProduct( eastForward ) < 0 )
			{
				degreesOffNorth = 360 - degreesOffNorth;
			}

			// The offset isn't quite half, use the value from the asset
			float width				  = compass.Brush.ImageSize.ToVector2D().X;
			float offsetFrac		  = ( degreesOffNorth / 360 );
			float offsetFromNorth	  = offsetFrac * width;

			// Use this if we want to go back to snapping
			{
				float lockValue			 = 0.0f;
				bool showCenterLetter	 = GetLockedCardinalDirection( offsetFromNorth, lockValue );
				float newCompassPosition = NORTH_OFFSET - ( showCenterLetter ? lockValue : offsetFromNorth );
				compassMaterial.SetScalarParameterValue( MaterialParameter::SHOW_SCALAR, MaterialParameter::GetTrueFalseFloat( showCenterLetter ) );
				compassMaterial.SetScalarParameterValue( MaterialParameter::LERP_ALPHA, newCompassPosition / width );
			}

			// Use this if we want to go back to simple cardinal directions
			{
				// FString cardinalDirection = GetCardinalDirection( offsetFromNorth );

				// if ( cardinalDirection != lastCardinalDirection )
				// {
				// 	lastCardinalDirection = cardinalDirection;
				// 	cardinalText.SetText( Localization::GetUnlocalizedTextFromString( cardinalDirection ) );
				// }

				// compassMaterial.SetScalarParameterValue( MaterialParameter::LERP_ALPHA, newCompassPosition / width );
			}

			// Use this if we want to go back to simple masking
			{
				// float lock = 0.0f;
				// FString cardinalDirection;
				// bool scalar = GetLockedCardinalDirection( offsetFromNorth, lock, cardinalDirection );

				// if ( cardinalDirection != lastCardinalDirection )
				// {
				// 	// lastCardinalDirection = cardinalDirection;
				// 	// cardinalText.SetText( Localization::GetUnlocalizedTextFromString( cardinalDirection ) );
				// }

				// compassMaterial.SetScalarParameterValue( MaterialParameter::SHOW_SCALAR, MaterialParameter::GetTrueFalseFloat( scalar ) );
				// compassMaterial.SetScalarParameterValue( MaterialParameter::LERP_ALPHA, newCompassPosition / width );
			}
		}
	}

	private bool GetLockedCardinalDirection( float offsetFromNorth, float& outLockedResult ) const
	{
		bool result = false;

		// The compass is broken down into four segments
		int numDirections	 = 4;
		float cardinalMetric = compass.Brush.ImageSize.ToVector2D().X / numDirections;

		for ( int i = 0; i < numDirections; i++ )
		{
			// The segment size is used to check ranges 0, 77, 154, 231
			float cardinalOffset = cardinalMetric * i;

			// Given a range degree value, see if we should lock to a specific cardinal direction
			if ( Math::IsNearlyEqual( cardinalOffset, offsetFromNorth, CARDINAL_RANGE ) )
			{
				outLockedResult = cardinalOffset;
				result			= true;
				break;
			}
		}

		return result;
	}
}