UCLASS( Abstract )
class UAS_LightningStrikeHudWidget : UNC_DisplayWidget
{
	default StateOnConstruction = EDisplayConstructVisibility::Shown;

	private UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	UAS_LightningStrikeTimer timer;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage ammoCounter;

	private UMaterialInstanceDynamic ammoCounterMaterial;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		ammoCounterMaterial = ammoCounter.GetDynamicMaterial();
		ClientCallbacks().OnLocalWeaponClipAmmoChanged.AddUFunction( this, n"OnLocalWeaponClipAmmoChanged" );
		TimerTick();
	}

	UFUNCTION( NotBlueprintCallable )
	protected void OnLocalWeaponClipAmmoChanged( ANCWeapon weapon, int oldValue, int newValue )
	{
		if ( !IsValid( weapon ) || weapon.GetWeaponClass() != n"Weap_RaidUlt_LightningStrike" )
			return;

		if ( IsValid( ammoCounterMaterial ) )
		{
			ammoCounterMaterial.SetScalarParameterValue( n"CurrentAmmo", weapon.GetClipAmmo() );
			ammoCounterMaterial.SetScalarParameterValue( n"MaxAmmo", weapon.GetClipAmmoMax() );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	protected void TimerTick()
	{
		AAS_LightningStrike m = GetMyStrikeManager();
		if ( IsValid( m ) )
		{
			int currentTime	   = GetTimeMilliseconds();
			int abilityEndTime = m.abilityEndTime;
			float timeLeftPerc = float( abilityEndTime - currentTime ) / float( m.abilityTotalTime );
			timer.SetPercent( timeLeftPerc );
		}
		System::SetTimerForNextTick( this, f"TimerTick" );
	}


	AAS_LightningStrike myStrikeManager;

	UFUNCTION( NotBlueprintCallable )
	AAS_LightningStrike GetMyStrikeManager()
	{
		if ( !IsValid( myStrikeManager ) )
			myStrikeManager = GetStrikeManagerForPlayer( Client_GetLocalASPawn() );
		return myStrikeManager;
	}
}