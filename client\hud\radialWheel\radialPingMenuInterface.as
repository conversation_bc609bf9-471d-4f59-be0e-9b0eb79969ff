UCLASS(Abstract)
class UAS_RadialPingMenuData : UAS_RadialMenuData
{
	UPROPERTY( EditDefaultsOnly )
	TArray<EPlayerPingType> pingTypes;

	default handleMouseNatively = false;
	
	TOptional<FPingMenuData> optionalData;

	UFUNCTION(BlueprintOverride)
	bool Populate( TArray<UAS_RadialMenuListItemData>& results )
	{
		UCl_PingManager pingManager = ClPingManager();
		if ( !IsValid( pingManager ) )
			return false;

		// Empty the results but ensure we reserve space for the ping types given
		results.Empty( pingTypes.Num() );

		for ( EPlayerPingType pingType : pingTypes )
		{
			UAS_RadialPingData item = UAS_RadialPingData();
			if ( IsValid( item ) )
			{
				item.pingType = pingType;

				FPlayerPingData pingOptionData = pingManager.GetPingTypeData( pingType );

				FNC_RadialListItemDefinition definition;
				definition.Icon				   = FSlateBrush();
				definition.Icon.ResourceObject = pingOptionData.widgetSettings.pingIcon;
				if ( pingOptionData.widgetSettings.usePlayerColorForFrame )
				{
					definition.Icon.TintColor = GetUIColorForPlayer( pingOptionData.widgetSettings.ownerPlayer );
				}
				else if ( pingType == EPlayerPingType::BASE_HERE )
				{
					AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( Client_GetLocalASPawn() );
					if ( IsValid( player ) )
					{
						definition.Icon.TintColor = GetTeamPresentationDataForIndex( player.GetTeam() ).teamColor;
					}
					else
					{
						FName colorId 				= CommonUiColorMpcNames::GetColorIdFromEnum( pingOptionData.widgetSettings.iconColor );
						definition.Icon.TintColor 	= GetCommonUiMpcColor( colorId );
					}
				}
				else
				{
					FName colorId 				= CommonUiColorMpcNames::GetColorIdFromEnum( pingOptionData.widgetSettings.iconColor );
					definition.Icon.TintColor 	= GetCommonUiMpcColor( colorId );
				}

				definition.Label = pingOptionData.displayName;
				item.SetDefinition( definition );

				results.Add( item );
			}
		}
		
		return !results.IsEmpty();
	}

	UFUNCTION( BlueprintOverride )
	bool SelectItem( UAS_RadialMenuListItemData data )
	{
		UAS_RadialPingData pingItemData = Cast<UAS_RadialPingData>(data);
		if ( !optionalData.IsSet() )
			return false;

		bool result = false;

		if ( IsValid( pingItemData ) )
		{
			UCl_PingManager pingManager = ClPingManager();
			if ( IsValid( pingManager ) )
			{
				// We don't care about the extra int or parent int if we are pinging from a wheel
				pingManager.SendPingAtLocation( pingItemData.pingType, optionalData.GetValue().location, nullptr );
				result = true;
			}
		}

		optionalData.Reset();

		return result;
	}
}