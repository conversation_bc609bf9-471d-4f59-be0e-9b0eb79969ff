namespace LightningStrikeConst
{
    const float32 ABILITY_DURATION = 15.0;
    const int NUM_SPEARS = 5;
}


UCLASS(Abstract)
class AAS_LightningStrike : ANCDefaultActor
{
    UPROPERTY( DefaultComponent )
    USceneComponent root;

	UPROPERTY( DefaultComponent )
	UNCNetMovementComponent movementComponent;
    
    UPROPERTY( EditDefaultsOnly )
	TSubclassOf<AAS_LightningStrikeSpear> spearClass;

    UPROPERTY( EditDefaultsOnly )
	UNiagaraSystem aoeArcEffect;

	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<UAS_LightningStrikeHudWidget> widgetClass;

    //AUDIO
    UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset flightActivateSound1P;
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset flightActivateSound3P;
    UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset flightDeactivateSound1P;
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset flightDeactivateSound3P;
    UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset fireSound_1P;
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset fireSound_3P;
    UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset jetwashSound_3P;

    //FX
    UPROPERTY(EditDefaultsOnly)
	UNiagaraSystem bodyIdleFX;
	UPROPERTY(EditDefaultsOnly)
	UImpactTablePrimaryAsset jetWashImpactFX; 


    AAS_PlayerEntity cachedOwner;
    ANCWeapon cachedWeapon;
    TArray<AAS_LightningStrikeSpear> spears;
    TArray<AFXActor> bodyIdleFXActors;
    TArray<ASoundActor> sustainSounds;

    UPROPERTY()
    FNCNetInt abilityEndTime;
    UPROPERTY()
    FNCNetInt abilityTotalTime;

	UAS_LightningStrikeHudWidget widget;

    UFUNCTION(BlueprintOverride)
    void ServerBeginPlay()
    {
        SharedBeginPlay();

        //damage mitigation
        FPreReceievedDamageDelegateBP preDamage;
        preDamage.BindUFunction( this, n"OnLightningStrikePreDamage" );
        cachedOwner.asHealthComponent.AddScriptPreDamageEvent( n"LightningStrike", preDamage );
        cachedOwner.healthRegenComponent.StartRegen();

        //idle FX
        if ( IsValid( bodyIdleFX ) )
        {
            AFXActor friendlyBodyIdleActor = Server_SpawnEffectOnEntity_Looping( bodyIdleFX, cachedOwner );
            friendlyBodyIdleActor.SetSendFlags( cachedOwner, ESendEventFlags::SEND_TO_FRIENDLIES );
            bodyIdleFXActors.Add( friendlyBodyIdleActor );

            AFXActor enemyBodyIdleActor = Server_SpawnEffectOnEntity_Looping( bodyIdleFX, cachedOwner );
            enemyBodyIdleActor.SetSendFlags( cachedOwner, ESendEventFlags::SEND_TO_ENEMIES );
            enemyBodyIdleActor.SetVariableLinearColor( n"Color", LightningRodConst::EnemyColor );
            bodyIdleFXActors.Add( enemyBodyIdleActor );
        }

        //sounds
        if ( IsValid( flightActivateSound1P ) && IsValid( flightActivateSound3P ) )
            Server_EmitSoundOnEntity_1P3P( flightActivateSound1P, flightActivateSound3P, cachedOwner, cachedOwner );
    }

    UFUNCTION(BlueprintOverride)
    void ClientBeginPlay()
    {
        SharedBeginPlay();

        if ( cachedOwner.IsLocallyControlled() )
        {
            if ( IsValid( GetLocalHUD() ) )
                GetLocalHUD().PushHideAlivePanel( n"LightningStrike" );
            widget = Cast<UAS_LightningStrikeHudWidget>( WidgetBlueprint::CreateWidget( widgetClass, Client_GetLocalPlayerController() ) );
            widget.AddToViewport();
        }
    }

    UFUNCTION()
    void Init( ANCWeapon weapon )
    {
        cachedWeapon = weapon;

        if ( IsServer() )
        {
            //Ability timeout
            FWeaponData weaponData = weapon.GetWeaponData();
            float32 deployTime = weaponData.raiseTime;
            float32 totalTime = LightningStrikeConst::ABILITY_DURATION + deployTime;
            System::SetTimer( this, n"EndAbilityAfterTime", totalTime, false );

            abilityEndTime.SetNetValue( GetGameTimeMS() + TO_MILLISECONDS( totalTime ) );
            abilityTotalTime.SetNetValue( TO_MILLISECONDS( totalTime ) );

            CreateSpears();
            Thread( this, n"ChargeSpears", cachedOwner, deployTime );
        }
    }

    UFUNCTION()
    void SharedBeginPlay()
    {
        cachedOwner = GetOwnerPlayer();
		UAS_LightningStrike_FlightThread thread = Cast<UAS_LightningStrike_FlightThread>( CreateThread( UAS_LightningStrike_FlightThread::StaticClass(), this ) );
		thread.Init( cachedOwner, this, jetWashImpactFX, jetwashSound_3P );
    }

    UFUNCTION()
    void CreateSpears()
    {
        // create spears
        float32 offsetZ = 75;
        float32 offsetY = 150;
        TArray<float32> angles;
        float32 angleBetween = 180.0f / LightningStrikeConst::NUM_SPEARS;
        for ( int i = 0; i < LightningStrikeConst::NUM_SPEARS; i++ )
        {
            angles.Add( angleBetween * i );
        }

        USkeletalMeshComponent mesh = cachedOwner.GetSkeletalMeshComponent();

        for ( int i = 0; i < angles.Num(); i++ )
        {
            float32 angle	  = Math::DegreesToRadians( angles[i] );
            FVector posOffset = FVector();
            posOffset.Z		  = Math::Sin( angle ) * offsetZ;
            posOffset.Y		  = Math::Cos( angle ) * offsetY;
            posOffset.X		  = 35;

            FTransform relativeTransform;
            relativeTransform.Location = posOffset;
            relativeTransform.Rotation = FRotator::ZeroRotator.Quaternion();

            FTransform baseTransform;
            baseTransform.Location = FVector::ZeroVector;
            baseTransform.Rotation = ( mesh.GetBoneTransform( n"Weapon" ).Rotator() + FRotator( 0, 0, -90 ) ).Quaternion();

            FTransform resultTransform = RelativeTransformToWorldTransform( relativeTransform, baseTransform );

            AAS_LightningStrikeSpear spear = Cast<AAS_LightningStrikeSpear>( Server_SpawnEntity( spearClass, cachedOwner, mesh.GetBoneTransform( n"Weapon" ).Location + resultTransform.Location + ( FVector::UpVector * 100 ), mesh.GetBoneTransform( n"Weapon" ).Rotator() + FRotator( 0, 0, -90 ) ) );
            spear.SetWeaponOwner( cachedWeapon );
            spear.AttachToComponent( mesh, n"Weapon", EAttachmentRule::KeepWorld );
            spears.Add( spear );
        }
    }

    UFUNCTION()
	void ChargeSpears( UNCCoroutine co, ANCPlayerCharacter player, float deployTime )
	{
		co.Wait( deployTime );

		for ( int i = 0; i < spears.Num(); i++ )
		{
			spears[i].net_isCharged.SetNetValue( true );
			co.Wait( 0.25 );
		}

        Thread( this, n"Thread_StrikeEffect" );
	}

    UFUNCTION()
    void FireSpear()
    {
        if ( spears.Num() > 0 )
        {
            AAS_LightningStrikeSpear spear = spears[spears.Num() - 1];
            spears.RemoveAt( spears.Num() - 1 );
            spear.DetachFromActor( EDetachmentRule::KeepWorld, EDetachmentRule::KeepWorld, EDetachmentRule::KeepWorld );
            spear.Destroy();
        }
    }




    UFUNCTION()
	void OnLightningStrikePreDamage( FDamageInfo& dmgInfo )
	{
        if ( Bitflags::HasFlag( dmgInfo.damageFlags, EDamageFlags::DF_FELL_OUT_OF_WORLD ) )
			return;
		Print( f"Mitigating damage" );
		dmgInfo.damage = dmgInfo.damage * 0.65;
	}




    UFUNCTION()
    void EndAbilityAfterTime()
    {
        if ( IsValid( cachedOwner ) )
            cachedOwner.TryEquipLastPrimary();
    }

    UFUNCTION(BlueprintOverride)
    void EndPlay(EEndPlayReason EndPlayReason)
    {
        if ( IsServer() )
        {
            //removing damage mitigation
			if ( IsValid( cachedOwner ) )
				cachedOwner.asHealthComponent.RemoveScriptPreDamageEvent( n"LightningStrike" );

            //delete spears
            for ( AAS_LightningStrikeSpear spear : spears )
            {
                if ( IsValid( spear ) )
				    spear.Destroy();
            }

            //delete idle FX
            for( auto a : bodyIdleFXActors )
			{
				if ( IsValid( a ) )
					a.Destroy();
			}

            //destroy audio
            for( auto s : sustainSounds )
            {
                if ( IsValid( s ) )
                    s.Destroy();
            }

            if ( IsValid( flightDeactivateSound1P ) && IsValid( flightDeactivateSound3P ) )
                Server_EmitSoundAtLocation_1P3P( flightDeactivateSound1P, flightDeactivateSound3P, cachedOwner.GetActorLocation(), cachedOwner );
        }

		if ( IsClient() )
        {
            if ( IsValid( cachedOwner )  && cachedOwner.IsLocallyControlled() )
            {
				if ( IsValid( GetLocalHUD() ) )
					GetLocalHUD().PopHideAlivePanel( n"LightningStrike" );
                if ( IsValid( widget ) )
                {
                    widget.RemoveFromParent();
                    widget = nullptr;
                }
            }
        }        
    }











    UFUNCTION()
	void Thread_StrikeEffect( UNCCoroutine co )
	{
        co.EndOn( cachedOwner, cachedOwner.OnDeathSignal );

		int defaultHitCheck = 10;
		int didHitCounter = defaultHitCheck;

		while ( true )
		{
			bool didHit = false;
			FRotator randomRotation = FRotator( Math::RandRange( -90, -45 ), Math::RandRange( -180, 180 ), Math::RandRange( -180, 180 ) );
			FVector orientationVector = randomRotation.Vector();
			orientationVector.Normalize();
			FVector loc = cachedOwner.GetActorLocation() + ( orientationVector * 5000 );

            //DrawDebugLine( cachedOwner.GetActorLocation(), loc, 5.0, FLinearColor::LucBlue, 3.0, false );

			TArray<AActor> ignoreActors; ignoreActors.Add( cachedOwner );
			FHitResult hit = LineTraceSingle( cachedOwner.GetActorLocation(), loc, ETraceTypeQuery::WeaponFine, false, ignoreActors, true );
			if ( hit.bBlockingHit && hit.Distance > 200 )
			{
				loc = hit.ImpactPoint;
				didHit = true;
			}

			if ( IsValid( aoeArcEffect ) && didHit )
			{
				FRotator rot = orientationVector.ToOrientationRotator();
				FNiagaraVariablePackage varPackage;
				varPackage.AddVariableFloat( n"Beam Width Scale", 2.0 );
				varPackage.AddVariableVec3( n"Beam Start", GetActorLocation() );
				varPackage.AddVariableVec3( n"Beam End", loc );
				Server_SpawnEffectAtLocation_OneShot_SetNiagaraVariables_WithFilter( aoeArcEffect, cachedOwner.GetActorLocation(), rot, varPackage, GetOwnerPlayer(), UNCNetEventFilterFriendlyIgnoreSender::StaticClass() );
				varPackage.AddVariableColor( n"Color", LightningRodConst::EnemyColor );

				Server_SpawnEffectAtLocation_OneShot_SetNiagaraVariables_WithFilter( aoeArcEffect, cachedOwner.GetActorLocation(), rot, varPackage, GetOwnerPlayer(), UNCNetEventFilterEnemy::StaticClass() );
			}

			if ( didHit || didHitCounter <= 0 )
			{
				co.Wait( Math::RandRange( 0.5f, 1.0f ) );
				didHitCounter = defaultHitCheck;
			}
			else
				didHitCounter = didHitCounter - 1; //didn't hit, let's do another check
		}
	}
}







AAS_LightningStrike GetStrikeManagerForPlayer( AAS_PlayerEntity player )
{
    TArray<AActor> managers;
    GetAllActorsOfClass( AAS_LightningStrike::StaticClass(), managers );

    for( auto m : managers )
    {
        AAS_LightningStrike s = Cast<AAS_LightningStrike>( m );
        if ( IsValid( s ) && s.GetOwnerPlayer() == player )
            return s;
    }

    return nullptr;
}