UAS_RaidMessagingManager GetRaidMessagingManager()
{
	UAS_RaidMessagingManager result = Cast<UAS_RaidMessagingManager>( UNCGameplaySystemsSubsystem::Get_ClientSystem( GetCurrentWorld(), UAS_RaidMessagingManager::StaticClass() ) );
	return result;
}

UCLASS( Abstract )
class UAS_RaidMessagingManager : UAS_GameplaySystem_Client
{
	UPROPERTY( EditDefaultsOnly )
	protected TSubclassOf<UNCScreenWidget> raidMessagingHudClass;

	UPROPERTY( EditDefaultsOnly, Category = "Audio" )
	protected TMap<EObjectiveState, UNCAudioAsset> teamObjectiveAudio;

	UPROPERTY( EditDefaultsOnly, Category = "Audio" )
	protected TMap<EShotClockState, UNCAudioAsset> shotClockAudio;

	UPROPERTY( EditDefaultsOnly, Category = "Audio" )
	protected UNCAudioAsset raidSuccessAudio;

	UPROPERTY( EditDefaultsOnly, Category = "Audio" )
	protected UNCAudioAsset defendSuccessAudio;

	UPROPERTY( EditDefaultsOnly, Category = "Audio" )
	UNCAudioAsset fiveSecondsLeftAudio;

	UPROPERTY( EditDefaultsOnly, Category = "Audio" )
	protected UNCAudioAsset teamWipeSuccess;

	UPROPERTY( EditDefaultsOnly, Category = "Audio" )
	protected UNCAudioAsset teamWipeFail;

	UPROPERTY( EditDefaultsOnly, Category = "Audio" )
	protected UNCAudioAsset teamWipeTimesUp;

	bool isCountdownAudioQueued = false;

	protected UAS_RaidMessagingHud raidMessagingHud;
	protected FTimerHandle countdownTimerHandle;
	protected TOptional<int> countdownStartMs;
	protected TOptional<int> countdownDurationMs;
	protected int lastCountdownSeconds = GameConst::INDEX_NONE;

	////////// Shared interfaces \\\\\\\\\\
	// -------------------------------------------------------------------------------------

	UFUNCTION( BlueprintOverride )
	void Initialize()
	{
		Super::Initialize();

		raidMessagingHud = Cast<UAS_RaidMessagingHud>( WidgetBlueprint::CreateWidget( raidMessagingHudClass, Client_GetLocalPlayerController() ) );
		raidMessagingHud.AddToViewport( GameConst::ZORDER_RAID_HUD );
		raidMessagingHud.Show();

		if ( GetCvarBool( "ScriptDebug.NewRaidMessaging" ) )
			GetLocalHUD().SetSplashMessageContainer( raidMessagingHud.splashMessageContainer, FOnAnnouncementSplashAdded( this, n"OnCenterAnnouncementAdded" ) );

		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			scriptCallbacks.client_onRaidObjectiveStateChanged.AddUFunction( this, n"OnRaidObjectiveStateChanged" );
			scriptCallbacks.client_onNextPhaseTimeChanged.AddUFunction( this, n"OnNextPhaseTimeChanged" );
			scriptCallbacks.client_onShieldBreakerStatusChanged.AddUFunction( this, n"OnShieldBreakerStatusChanged" );
			scriptCallbacks.client_OnShotClockStateChanged.AddUFunction( this, n"OnShotClockStateChanged" );
			scriptCallbacks.shared_OnRaidStarted.AddUFunction( this, n"OnRaidStarted" );
			scriptCallbacks.shared_OnRaidEnded.AddUFunction( this, n"OnRaidEnded" );

			if ( GetCvarBool( "ScriptDebug.NewRaidMessaging" ) )
			{
				scriptCallbacks.shared_OnRaidStateChanged.AddUFunction( this, n"OnRaidStateChanged" );
			}
		}

		UCL_ScriptCallbacks clientCallbacks = ClientCallbacks();
		if ( IsValid( clientCallbacks ) )
		{
			clientCallbacks.OnGamePhaseChanged.AddUFunction( this, n"OnGamePhaseChanged" );
			clientCallbacks.OnTeamScoreChanged.AddUFunction( this, n"OnTeamScoreChanged" );
		}

		AAS_GameState_RaidModeTwoTeam raidGameState = GetGameStateEntity_RaidModeTwoTeam();
		if ( IsValid( raidGameState ) )
		{
			raidGameState.net_overtime.OnReplicated().AddUFunction( this, n"OnOvertimeChanged" );
			OnOvertimeChanged( !raidGameState.net_overtime, raidGameState.net_overtime );
		}

		if ( IsValid( localPlayer ) )
		{
			int playerTeam								   = localPlayer.GetTeam();
			AAS_TeamStateManager_RaidMode teamStateManager = GetTeamStateManager_RaidMode( playerTeam );
			if ( IsValid( teamStateManager ) )
			{
				OnRaidObjectiveStateChanged( playerTeam, GameConst::INDEX_NONE, teamStateManager.GetRaidObjectiveState() );
			}
		}

		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"TransitionToPlaying", n"SC_TransitionToPlaying" );
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"ServerCommandCarePackageIncoming", n"SC_CarePackageIncoming" );
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"ServerCommandCarePackageLanded", n"SC_CarePackageLanded" );
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"ServerCommandCarePackageOpened", n"SC_CarePackageOpened" );
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"ServerCommand_DisplayTeamWipeToast", n"SC_DisplayTeamWipeToast" );

		if ( GetCvarBool( "ScriptDebug.NewRaidMessaging" ) )
			UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"TriggerTemplatedMessage", n"SC_TriggerTemplatedMessage" );

		// After opening the raid messaging hud, let each raid messaging manager handle initializing it
		InitializeRaidMessaging();
	}

	UFUNCTION()
	private void OnCenterAnnouncementAdded( UAS_CenterAnnouncementWidget widget )
	{
		splashMessageCleanupSignal.Emit();
		GetLocalHUD().ClearCurrentSplashMessage();
		currentPinnedMessage = nullptr;

		USizeBoxSlot slot = Cast<USizeBoxSlot>( widget.Slot );
		slot.SetHorizontalAlignment( EHorizontalAlignment::HAlign_Center );
		slot.SetVerticalAlignment( EVerticalAlignment::VAlign_Center );
		slot.SetPadding( FMargin( 0, 0, 0, 350 ) );
	}

	UFUNCTION()
	protected void OnRaidStateChanged( AAS_RaidEventManager_v2 eventManager, int oldState,
							 int newState )
	{
		UAS_PriorityMessageData data = GetPinnedMessageForRaidState( eventManager, newState );

		if ( IsValid( data ) )
			SetPinnedMessage( data );
		else
			ClearPinnedMessage();
	}

	UAS_PriorityMessageData GetPinnedMessageForRaidState( AAS_RaidEventManager_v2 eventManager, int raidState )
	{
		if ( Bitflags::HasEnumFlag( raidState, ERaidState::RAID_ENDED ) || !Bitflags::HasEnumFlag( raidState, ERaidState::RAID_STARTED ) )
			return nullptr;

		if ( Bitflags::HasEnumFlag( raidState, ERaidState::RAID_AUTOENDING ) )
		{
			EPriorityMessageTemplate template = localPlayer.GetTeam() == eventManager.GetAttackerTeam() ? EPriorityMessageTemplate::V2_ATTACK_DOME_REPAIR : EPriorityMessageTemplate::V2_DEFEND_DOME_REPAIR;
			UAS_PriorityMessageData data	  = GetTemplateMessageData( template );
			if ( IsValid( data ) )
			{
				data.header			= GetLocalizedText( Localization::Raid, "raid_breaker_down" );
				data.subheader		= GetLocalizedText( Localization::Raid, "raid_breaker_down_sub" );
				data.autoDissappear = false;
			}

			return data;
		}

		return nullptr;
	}

	private TArray<FName> raidMessagingHudHideRequests;

	void AddRaidMessagingHudHideRequest( FName requestId )
	{
		ScriptAssert( !raidMessagingHudHideRequests.Contains( requestId ), f"AddRaidMessagingHudHideRequest -- already registered request {requestId}" );

		raidMessagingHudHideRequests.Add( requestId );

		raidMessagingHud.ShowOrHide( raidMessagingHudHideRequests.Num() == 0, true );
	}

	bool HasRaidMessagingHudHideRequest( FName requestId )
	{
		return raidMessagingHudHideRequests.Contains( requestId );
	}

	void RemoveRaidMessagingHudHideRequest( FName requestId )
	{
		ScriptAssert( raidMessagingHudHideRequests.Contains( requestId ), f"RemoveRaidMessagingHudHideRequest -- request {requestId} not found" );

		raidMessagingHudHideRequests.Remove( requestId );

		raidMessagingHud.ShowOrHide( raidMessagingHudHideRequests.Num() == 0, true );
	}

	protected void InitializeRaidMessaging()
	{
	}

	protected void SetHeaderText( FText headerText, bool animateToText = false )
	{
		if ( IsValid( raidMessagingHud ) )
		{
			raidMessagingHud.SetHeaderText( headerText, animateToText );
		}
	}

	protected void SetHeaderTextMs( int ms )
	{
		FText formattedTime = Text::AsTimespan_Timespan( FTimespan::FromMilliseconds( Math::Max( 0, ms ) ) );
		SetHeaderText( formattedTime );
	}

	protected void SetSubheaderText( FText subheaderText, bool animateToText = true )
	{
		if ( IsValid( raidMessagingHud ) )
		{
			raidMessagingHud.SetSubheaderText( subheaderText, animateToText );
		}
	}

	protected void CheckForCriticalAlert()
	{
		if ( IsValid( raidMessagingHud ) )
		{
			raidMessagingHud.CheckForCriticalAlert();
		}
	}

	protected void CheckForActivePlant()
	{
		if ( IsValid( raidMessagingHud ) )
		{
			raidMessagingHud.CheckForActivePlant();
		}
	}

	protected void ShowOrHideShieldbreakerTracker( bool show )
	{
		if ( IsValid( raidMessagingHud ) )
		{
			raidMessagingHud.ShowOrHideShieldbreakerTracker( show );
		}
	}

	protected void ShowOrHideObjectiveMarkers( bool show )
	{
		if ( IsValid( raidMessagingHud ) )
		{
			raidMessagingHud.ShowOrHideObjectiveMarkers( show );
		}
	}

	protected void SetTrackedTeams( int playerTeam, int enemyTeam )
	{
		if ( IsValid( raidMessagingHud ) )
		{
			raidMessagingHud.SetTrackedTeams( playerTeam, enemyTeam );
		}
	}

	protected void SetScoreLimits( int scoreLimit )
	{
		if ( IsValid( raidMessagingHud ) )
		{
			raidMessagingHud.SetScoreLimits( scoreLimit );
		}
	}

	protected void ShowOrHideRaidingTeamRaidState( bool show )
	{
		if ( IsValid( raidMessagingHud ) )
		{
			raidMessagingHud.ShowOrHideRaidingTeamRaidState( show );
		}
	}

	protected void ShowOrHideRaidingTeamRespawns( bool show )
	{
		if ( IsValid( raidMessagingHud ) )
		{
			raidMessagingHud.ShowOrHideRaidingTeamRespawns( show );
		}
	}

	protected void ShowOrHideCharacterPortraits( bool show )
	{
		if ( IsValid( raidMessagingHud ) )
		{
			raidMessagingHud.ShowOrHideCharacterPortraits( show );
		}
	}

	protected void ShowOrHideShieldbreakerIndicator( bool show )
	{
		if ( IsValid( raidMessagingHud ) )
		{
			raidMessagingHud.ShowOrHideShieldbreakerIndicator( show );
		}
	}

	protected void StartCountdownTimer( bool playCountdownAudio )
	{
		System::ClearAndInvalidateTimerHandle( tickingAudioTimerHandle );
		int gameTimeMs		= GetGameTimeMS();
		countdownStartMs	= gameTimeMs;
		countdownDurationMs = GetNextGamePhaseTime() - gameTimeMs;
		TickCountdown();

		isCountdownAudioQueued = playCountdownAudio;
		if ( playCountdownAudio )
		{
			StartCountdownTickingAudio();
		}
	}

	protected void StartCountdownTimer_Custom( int startTimeMS, int endTimeMS, bool playCountdownAudio )
	{
		System::ClearAndInvalidateTimerHandle( tickingAudioTimerHandle );
		countdownStartMs	= startTimeMS;
		countdownDurationMs = endTimeMS - startTimeMS;
		TickCountdown();

		isCountdownAudioQueued = playCountdownAudio;
		if ( playCountdownAudio )
		{
			StartCountdownTickingAudio();
		}
	}

	FTimerHandle tickingAudioTimerHandle;

	void StartCountdownTickingAudio()
	{
		if ( !countdownStartMs.IsSet() || !countdownDurationMs.IsSet() )
			return;

		int endTime = countdownStartMs.GetValue() + countdownDurationMs.GetValue();
		if ( endTime <= GetTimeMilliseconds() )
			return;

		int delayMS		 = endTime - GetTimeMilliseconds() - TO_MILLISECONDS( 5.0 );
		float32 delaySec = TO_SECONDS( delayMS );
		if ( delaySec > 0 )
			tickingAudioTimerHandle = System::SetTimer( this, n"OnFiveSecondsLeft", delaySec, false );
		else if ( delaySec < -5 )
		{
			// maybe later
		}
	}

	UFUNCTION()
	private void OnFiveSecondsLeft()
	{
		// OnSecondsLeft( 5 );
		Client_EmitSoundUI( fiveSecondsLeftAudio );
	}

	UFUNCTION( NotBlueprintCallable )
	protected void TickCountdown()
	{
		if ( !countdownStartMs.IsSet() || !countdownDurationMs.IsSet() )
			return;

		int startMs	   = countdownStartMs.GetValue();
		int durationMs = countdownDurationMs.GetValue();

		int timeElapsedMs = GetGameTimeMS() - startMs;
		int countdownMs	  = Math::Max( durationMs - timeElapsedMs, 0 );
		if ( countdownMs > 0 )
		{
			int countdownSeconds = Math::CeilToInt( TO_SECONDS( countdownMs ) );
			if ( countdownSeconds != lastCountdownSeconds )
			{
				// Only update text every major second
				lastCountdownSeconds = countdownSeconds;
				SetHeaderTextMs( countdownMs );
			}

			countdownTimerHandle = System::SetTimerForNextTick( this, "TickCountdown" );
		}
		else
		{
			ClearCountdownTimer();
		}
	}

	protected void ClearCountdownTimer()
	{
		countdownStartMs.Reset();
		countdownDurationMs.Reset();
		System::ClearAndInvalidateTimerHandle( countdownTimerHandle );
		System::ClearAndInvalidateTimerHandle( tickingAudioTimerHandle );
		isCountdownAudioQueued = false;
	}

	// -------------------------------------------------------------------------------------

	////////// Callbacks \\\\\\\\\\
	// -------------------------------------------------------------------------------------

	UFUNCTION( NotBlueprintCallable )
	protected void OnNextPhaseTimeChanged( int oldValue, int newValue )
	{
		// To use this callback for a game mode, override in the game modes raid messaging manager
		OnGamePhaseOrTimeChanged();
	}

	UFUNCTION( NotBlueprintCallable )
	protected void OnGamePhaseChanged( int OldState, int NewState )
	{
		// To use this callback for a game mode, override in the game modes raid messaging manager
		OnGamePhaseOrTimeChanged();
	}

	UFUNCTION( NotBlueprintCallable )
	protected void OnRaidObjectiveStateChanged( int team, int oldValue, int newValue )
	{
		// To use this callback for a game mode, override in the game modes raid messaging manager
		OnGamePhaseOrTimeChanged();
	}

	UFUNCTION( NotBlueprintCallable )
	protected void OnShieldBreakerStatusChanged( AAS_PlayerEntity player )
	{
		// To use this callback for a game mode, override in the game modes raid messaging manager
	}

	UFUNCTION( NotBlueprintCallable )
	protected void OnShotClockStateChanged( EShotClockState oldValue, EShotClockState newValue )
	{
		// To use this callback for a game mode, override in the game modes raid messaging manager
	}

	UFUNCTION( NotBlueprintCallable )
	protected void OnOvertimeChanged( bool oldValue, bool newValue )
	{
		// To use this callback for a game mode, override in the game modes raid messaging manager
	}

	UFUNCTION( NotBlueprintCallable )
	protected void OnTeamScoreChanged( int teamId, int newValue )
	{
		// To use this callback for a game mode, override in the game modes raid messaging manager
	}

	UFUNCTION( NotBlueprintCallable )
	protected void OnRaidEvent( AAS_RaidEventManager_v2 eventManager, ERaidEventFlag flag )
	{
		// To use this callback for a game mode, override in the game modes raid messaging manager
	}

	UFUNCTION( NotBlueprintCallable )
	protected void OnRaidEventCleared( AAS_RaidEventManager_v2 eventManager, ERaidEventFlag flag )
	{
		// To use this callback for a game mode, override in the game modes raid messaging manager
	}

	UFUNCTION( NotBlueprintCallable )
	protected void OnRaidStarted( AAS_RaidEventManager_v2 eventManager )
	{
		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			if ( !GetCvarBool( "ScriptDebug.NewRaidMessaging" ) )
			{
				// Bind the raid events when the raid starts
				scriptCallbacks.shared_OnRaidEvent.AddUFunction( this, n"OnRaidEvent" );
				scriptCallbacks.shared_OnRaidEventCleared.AddUFunction( this, n"OnRaidEventCleared" );
			}
		}

		// To use this callback for a game mode, override in the game modes raid messaging manager
		OnRaidStartedOrEnded();
	}

	UFUNCTION( NotBlueprintCallable )
	protected void OnRaidEnded( AAS_RaidEventManager_v2 eventManager )
	{
		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			if ( !GetCvarBool( "ScriptDebug.NewRaidMessaging" ) )
			{
				// Unbind the raid events when the raid ends
				scriptCallbacks.shared_OnRaidEvent.Unbind( this, n"OnRaidEvent" );
				scriptCallbacks.shared_OnRaidEventCleared.Unbind( this, n"OnRaidEventCleared" );
			}
		}

		// To use this callback for a game mode, override in the game modes raid messaging manager
		OnRaidStartedOrEnded();
	}

	UFUNCTION( NotBlueprintCallable )
	void SC_TransitionToPlaying( TArray<FString> args )
	{
		// To use this callback for a game mode, override in the game modes raid messaging manager
	}

	UFUNCTION( NotBlueprintCallable )
	protected void SC_CarePackageIncoming( TArray<FString> args )
	{
		// To use this callback for a game mode, override in the game modes raid messaging manager
	}

	UFUNCTION( NotBlueprintCallable )
	protected void SC_CarePackageLanded( TArray<FString> args )
	{
		// To use this callback for a game mode, override in the game modes raid messaging manager
	}

	UFUNCTION( NotBlueprintCallable )
	protected void SC_CarePackageOpened( TArray<FString> args )
	{
		// To use this callback for a game mode, override in the game modes raid messaging manager
	}

	UFUNCTION( NotBlueprintCallable )
	void SC_TriggerTemplatedMessage( TArray<FString> args )
	{
		int index = args[0].ToInt();
		AddSplashMessage( GetTemplateMessageData( EPriorityMessageTemplate( index ) ) );
	}

	UFUNCTION( NotBlueprintCallable )
	void SC_DisplayTeamWipeToast( TArray<FString> args )
	{
		ScriptAssert( args.Num() == 1, f"wrong number of arguments. looking for 1 got {args.Num()}" );

		int plantingTeam = args[0].ToInt();
		DisplayTeamWipeToast( plantingTeam );
	}

	protected void OnGamePhaseOrTimeChanged()
	{
		// To use this callback for a game mode, override in the game modes raid messaging manager
	}

	protected void OnRaidStartedOrEnded()
	{
		// To use this callback for a game mode, override in the game modes raid messaging manager
		ClearPinnedMessage();
	}
	// -------------------------------------------------------------------------------------

	protected void CleanUpRaidMessaging()
	{
		if ( IsValid( raidMessagingHud ) )
		{
			raidMessagingHud.RemoveFromParent();
		}
	}

	UAS_RaidMessagingHud GetRaidHud()
	{
		return raidMessagingHud;
	}

	void DisplayTeamWipeToast( int plantingTeam )
	{
		if ( plantingTeam == GameConst::INDEX_NONE )
		{
			// If the team isn't valid, we just play the times up event
			Client_EmitSoundUI( teamWipeTimesUp );
			return;
		}

		AAS_PlayerEntity player = localPlayer;
		bool sameTeam			= IsValid( player ) && plantingTeam == player.GetTeam();

		FLinearColor themeColor;
		UNCAudioAsset sound;
		FText message;
		FText description;

		Client_EmitSoundUI( teamWipeFail );

		if ( sameTeam )
		{
			bool found	= GetSafeColor( n"gold", themeColor );
			message		= GetLocalizedText( Localization::RaidMode, "sudden_death_enemy_team_wipe" );
			description = GetLocalizedText( Localization::RaidMode, "shot_clock_expired_team_wipe_autoplant_attacker" );
			sound		= teamWipeSuccess;
		}
		else
		{
			bool found	= GetSafeColor( n"system_red", themeColor );
			message		= GetLocalizedText( Localization::RaidMode, "sudden_death_your_team_wipe" );
			description = GetLocalizedText( Localization::RaidMode, "shot_clock_expired_team_wipe_autoplant" );
			sound		= teamWipeFail;
		}

		localHUD.DisplayAnnouncementSplash( message, description, themeColor, 0.0, 0.0, sound );
	}

	FNCCoroutineSignal splashMessageCleanupSignal;

	UAS_PriorityMessageData currentPinnedMessage;
	UAS_PriorityMessageData currentSplashMessage;

	// NEW RAID MESSAGING (WIP)
	void AddSplashMessage( UAS_PriorityMessageData data )
	{
		currentSplashMessage = data;

		UAS_RaidMessagingHud raidHud			 = GetRaidHud();
		UAS_PriorityMessageBaseWidget newMessage = Cast<UAS_PriorityMessageBaseWidget>( WidgetBlueprint::CreateWidget( data.widgetClass, raidHud.GetOwningPlayer() ) );
		newMessage.Initialize( data );
		GetLocalHUD().AddSplashMessage( newMessage );

		ScriptAssert( data.autoDissappearDelay > 0, f"{data.Class.ToString()} autoDisappearDelay needs to be > 0" );
		Thread( this, n"CleanupSplashMessage_Delayed", data.autoDissappearDelay );
	}

	void SetPinnedMessage( UAS_PriorityMessageData data )
	{
		currentPinnedMessage = data;
		if ( IsValid( currentSplashMessage ) )
			return;

		UAS_RaidMessagingHud raidHud			 = GetRaidHud();
		UAS_PriorityMessageBaseWidget newMessage = Cast<UAS_PriorityMessageBaseWidget>( WidgetBlueprint::CreateWidget( data.widgetClass, raidHud.GetOwningPlayer() ) );
		newMessage.Initialize( data );
		GetLocalHUD().AddSplashMessage( newMessage );
	}

	void ClearPinnedMessage()
	{
		if ( IsValid( currentPinnedMessage ) && !IsValid( currentSplashMessage ) )
		{
			GetLocalHUD().ClearCurrentSplashMessage();
		}

		currentPinnedMessage = nullptr;
	}

	UFUNCTION()
	protected void CleanupSplashMessage_Delayed( UNCCoroutine co, float32 delay )
	{
		splashMessageCleanupSignal.Emit();
		co.EndOn( this, splashMessageCleanupSignal );

		co.Wait( delay );

		GetLocalHUD().ClearCurrentSplashMessage();
		currentSplashMessage = nullptr;

		if ( currentPinnedMessage != nullptr )
		{
			SetPinnedMessage( currentPinnedMessage );
		}
	}
}