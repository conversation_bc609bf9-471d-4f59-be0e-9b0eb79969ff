UCLASS( Abstract )
class UAS_ObituaryWidget : UAS_QueueWidget
{
	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		UAS_ObituarySystem obituarySystem = GetObituarySystem();
		if ( IsValid( obituarySystem ) )
		{
			obituarySystem.RegisterQueueWidget( this );
		}
	}

	bool GetMatchingBackpackItem( FGameplayTag itemId, FBackpackItemStruct& outBackpackItem, int& outId )
	{
		bool result = false;

		for ( TMapIterator<int, UAS_QueueItemData> iter : idToQueueItemData )
		{
			UAS_ObituaryItemData trackedObituaryData = Cast<UAS_ObituaryItemData>( iter.GetValue() );
			if ( IsValid( trackedObituaryData ) )
			{
				TOptional<FBackpackItemStruct> trackedBackpackItem = trackedObituaryData.GetOptionalBackpackItem();
				if ( trackedBackpackItem.IsSet() && trackedBackpackItem.Value.itemIndex == itemId )
				{
					outBackpackItem = trackedBackpackItem.GetValue();
					outId			= iter.GetKey();
					result			= true;
				}
			}
		}

		return result;
	}
}