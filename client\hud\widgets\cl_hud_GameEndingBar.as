enum EGameEndingBarState
{
	INVALID,
	PLAYING,
	NAG,
	COUNTDOWN,
}

class UAS_GameEndingBar : UUserWidgetDefault
{		
	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	private UTextBlock message;
	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	private UTextBlock timer;
	
	UPROPERTY( BlueprintReadOnly, NotEditable, Transient, Meta = ( BindWidgetAnim ) )
	private UWidgetAnimation appearAnim;
	UPROPERTY( BlueprintReadOnly, NotEditable, Transient, Meta = ( BindWidgetAnim ) )
	private UWidgetAnimation disappearAnim;

	bool isOn;
	EGameEndingBarState barState = EGameEndingBarState::INVALID;
	FNCCoroutineSignal endSignalTimer;
	
	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		Init();
		
		ClientCallbacks().OnGamePhaseChanged.AddUFunction( this, n"OnGamePhaseOrTimeChanged" );
		ScriptCallbacks().client_onNextPhaseTimeChanged.AddUFunction( this, n"OnGamePhaseOrTimeChanged" );	
		OnGamePhaseOrTimeChanged( GameConst::INDEX_NONE, GetGamePhase() );
	}

	UFUNCTION()
	// **** Do NOT use the passed in values because there is more than 1 netvar bound to this func that gives 2 different meanings of int
	private void OnGamePhaseOrTimeChanged( int InvalidValue1, int InvalidValue2 )
	{		
		Thread( this, n"EvaluateCountdown", GetGamePhase() );
	}

	UFUNCTION()
	private void EvaluateCountdown(UNCCoroutine co, int newState )
	{
		endSignalTimer.Emit();
		
		if ( newState != GamePhase::PLAYING )
		{
			Disappear();				
			return;
		}

		co.EndOn( this, endSignalTimer );

		float timeLeft	   	= GetTimeLeft();
		float timeNag 		= GameModeDefaults().GameModeRules_TimeLimitNagSeconds;
		float timeCountdown = GameModeDefaults().GameModeRules_TimeLimitCountDownSeconds;

		if ( timeLeft > timeNag )
		{
			float delay = timeLeft - timeNag;

			co.Wait( delay );
			Thread( this, n"EvaluateCountdown", GetGamePhase() ); //re-evaluate
			return;	
		}
		else if ( timeLeft > timeCountdown && timeLeft <= timeNag )
		{
			Thread( this, n"DisplayNag" );

			float delay = timeLeft - timeCountdown;
			
			co.Wait( delay );
			Thread( this, n"EvaluateCountdown", GetGamePhase() ); //re-evaluate
			return;
		}
		else if ( timeLeft <= timeCountdown )
		{
			Thread( this, n"DisplayCountdown" );
			return;
		}			
	}
	
	UFUNCTION()
	private void DisplayNag(UNCCoroutine co )
	{
		co.EndOn( this, endSignalTimer );

		if ( !HudValid() )
		{
			co.Wait(0.5 );
			Thread( this, n"EvaluateCountdown", GetGamePhase() ); //re-evaluate
			return;
		}

		if ( int(barState) < int(EGameEndingBarState::NAG) )
		{
			barState = EGameEndingBarState::NAG ;
			Appear();
		}
		
		message.SetText( GetLocalizedText( Localization::Raid, "raid_game_ending_colon") );
		
		//this function could get called later than it should, (timescaling during dev for example) so get an accurate start time
		float32 timeLeft	   		= GetTimeLeft();
		float32 timeNag 			= GameModeDefaults().GameModeRules_TimeLimitNagSeconds;
		int startTimeMS 			= GetPlayingTimeMS() - TO_MILLISECONDS( timeLeft - timeNag );

		float elapsedTime 			= TO_SECONDS( GetGameTimeMS() - startTimeMS );
		const float32 HOLD_TIME 	= 5.0;
		const float32 BUFFER_TIME 	= 1.0;

		while(elapsedTime < (HOLD_TIME+BUFFER_TIME) )
		{
			int timeLeftMS = TO_MILLISECONDS(GetTimeLeft());
			timer.SetText( GetFormattedCountdownTime( timeLeftMS ) );

			elapsedTime = TO_SECONDS( GetGameTimeMS() - startTimeMS );
			if ( elapsedTime >= HOLD_TIME )
				break;
			
			co.Wait( 0.01 );
		}

		Disappear();	

		//this is really only necessary when timescaling... but it does help during dev
		co.Wait(1.0);
		Thread( this, n"EvaluateCountdown", GetGamePhase() ); //re-evaluate	
	}

	UFUNCTION()
	private void DisplayCountdown(UNCCoroutine co )
	{
		co.EndOn( this, endSignalTimer );

		if ( !HudValid() )
		{
			co.Wait(0.02);
			Thread( this, n"EvaluateCountdown", GetGamePhase() ); //re-evaluate
			return;
		}

		if ( int(barState) < int(EGameEndingBarState::COUNTDOWN) )
		{
			barState = EGameEndingBarState::COUNTDOWN;
			Appear();
		}
		
		message.SetText( GetLocalizedText( Localization::Raid, "raid_game_ending_colon") );
		
		while(true)
		{
			int timeLeftMS = TO_MILLISECONDS(GetTimeLeft());
			timer.SetText( GetFormattedCountdownTime( timeLeftMS ) );
			co.Wait( 0.01 );
		}
	}

	private void Init()
	{
		message.SetText( FText() );
		timer.SetText(FText() );
		
		isOn = false;
		SetWidgetVisibilitySafe(this,  ESlateVisibility::Collapsed );		
		PlayAnimation(disappearAnim, 0.95, 1, EUMGSequencePlayMode::Forward, 10 );
	}

	private void Appear()
	{
		if ( isOn )
			return;
		isOn = true;

		if ( this.GetVisibility() == ESlateVisibility::Collapsed )
			SetWidgetVisibilitySafe(this,  ESlateVisibility::HitTestInvisible );	

		PlayAnimation(appearAnim);
	}

	private void Disappear()
	{
		if ( !isOn )
			return;
		isOn = false;
		
		PlayAnimation(disappearAnim);
	}

	private float32 GetTimeLeft()
	{
		float32 timeLeft 	= GetGameTimeMS() - GetNextGamePhaseTime();
		
		return timeLeft;
	}

	private bool HudValid()
	{
		AAS_HUD hud = Cast<AAS_HUD>( GetHUD() );
		return IsValid(hud) && IsValid(hud.mainHUDWidget);
	}
}