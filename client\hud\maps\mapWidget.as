UCLASS( Abstract )
class UAS_MapWidget : UNCMapWidget
{
	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private USizeBox mapBox;

	UPROPERTY( EditInstanceOnly, BlueprintHidden )
	protected float zoomDuration = 10.0f;

	UPROPERTY( EditInstanceOnly, BlueprintHidden )
	protected FVector2D mapSize = FVector2D( 184.0f, 184.0f );

	protected FVector2D mins = FVector2D( MAX_flt, MAX_flt );
	protected FVector2D maxs = FVector2D( -MAX_flt, -MAX_flt );
	protected bool baseMapActive = false;

	private TMap<AAS_PingObject, UNCMapMarkerWidget> pingMap;
	private TOptional<float> optUserZoomTarget;
	private const FName SEQUENCE_INDEX_PARAMETER = n"SequenceIndex";
	private bool showObjectiveMarkerTimerText = false;
	private AAS_BaseSystem currentBase;
	private TArray<AAS_BaseSystem> allBases;
	private TArray<AAS_BaseSubObjective> allSubobjectives;
	private TArray<AAS_BaseVault> allVaults;
	private TMap<AAS_BaseSystem, FBaseVendors> allBaseVendors;

	UFUNCTION( BlueprintOverride )
	void PreConstruct( bool IsDesignTime )
	{
		mapBox.SetHeightOverride( mapSize.Y );
		mapBox.SetWidthOverride( mapSize.X );
	}

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			scriptCallbacks.client_OnPlayerEnteredMapSwapTrigger.AddUFunction( this, n"OnPlayerEnteredMapSwapTrigger" );
			scriptCallbacks.client_OnPlayerExitedMapSwapTrigger.AddUFunction( this, n"OnPlayerExitedMapSwapTrigger" );
			scriptCallbacks.client_onShieldBreakerStatusChanged.AddUFunction( this, n"OnShieldbreakerStatusChanged" );
			scriptCallbacks.client_OnObjectiveMarkerPlantInProgress.AddUFunction( this, n"OnObjectiveMarkerPlantInProgress" );
			scriptCallbacks.client_OnObjectiveMarkerBombPlanted.AddUFunction( this, n"OnObjectiveMarkerBombPlanted" );
			scriptCallbacks.client_OnObjectiveMarkerDefuseInProgress.AddUFunction( this, n"OnObjectiveMarkerDefuseInProgress" );
			scriptCallbacks.client_OnObjectiveMarkerCountdown.AddUFunction( this, n"OnObjectiveMarkerCountdown" );
			scriptCallbacks.client_OnObjectiveMarkerBombDefused.AddUFunction( this, n"OnObjectiveMarkerBombDefused" );
			scriptCallbacks.client_OnObjectiveMarkerPlantStopped.AddUFunction( this, n"OnObjectiveMarkerPlantStopped" );
			scriptCallbacks.client_OnObjectiveMarkerParameterChanged.AddUFunction( this, n"OnObjectiveMarkerParameterChanged" );
		}

		// On construct use the world mins and maxs
		GetMinsAndMaxsOfWorld( mins, maxs );
		TSoftObjectPtr<UTexture2D> map = UNCUtils::GetMaximapForLevel( GetOwningPlayer() );
		SetMap( map.Get(), mins, maxs );
	}

	void OnMapOpened()
	{
		// Intentionally empty
	}

	void OnMapClosed()
	{
		// Intentionally empty
	}

	protected void LerpToZoomLevel( float targetZoom )
	{
		optUserZoomTarget = targetZoom;
		OnLerpToZoomLevelStart();
	}

	protected void GetMinsAndMaxsOfWorld( FVector2D& outMins, FVector2D& outMaxs )
	{
		// TODO: This should be part of generating the map. Or at least manually pulling in this data
		// There is absolutely no need to do this at runtime.
		TArray<AActor> mapCorners;
		// PERF: Requesting all actors of class is very expensive. We need to do better
		Gameplay::GetAllActorsOfClassWithTag( AActor::StaticClass(), n"MinimapMarker", mapCorners );

		if ( mapCorners.Num() >= 2 )
		{
			for ( AActor corner : mapCorners )
			{
				FVector loc = corner.GetActorLocation();
				if ( loc.X < outMins.X )
					outMins.X = loc.X;
				if ( loc.X > outMaxs.X )
					outMaxs.X = loc.X;

				if ( loc.Y < outMins.Y )
					outMins.Y = loc.Y;
				if ( loc.Y > outMaxs.Y )
					outMaxs.Y = loc.Y;
			}
		}
	}

	protected void GetMinsAndMaxsOfBase( AAS_BaseSystem base, FVector2D& outMins, FVector2D& outMaxs )
	{
		if ( !IsValid( base ) )
			return;

		AAS_RaidDomeShield dome = base.domeShield;
		if ( IsValid( dome ) )
		{
			// For the minimap, we recalculate the mins and maxes using the base location and radius of the map trigger
			FVector north;
			FVector east;
			GetCardinalDirectionsFromSettings( north, east );

			// Use the domes location, trigger radius, and the worlds north and east to find the bounds of a base in relation to the world
			FVector baseLocation  = dome.GetActorLocation();
			float baseRadius	  = dome.mapSwapDetectionTrigger.GetBoundsRadius();
			FVector forwardVector = north;
			FVector reverseVector = forwardVector * -1;
			FVector rightVector	  = east;

			FVector cornerBR = baseLocation + ( ( reverseVector + rightVector ) * baseRadius );
			FVector cornerFL = baseLocation + ( ( forwardVector - rightVector ) * baseRadius );
			outMaxs			 = cornerBR.ToVector2D();
			outMins			 = cornerFL.ToVector2D();
		}
	}

	protected void OnPlayerEnteredOrExitedMapSwapTrigger( AAS_PlayerEntity player, AAS_BaseSystem base )
	{
		if ( currentBase != nullptr )
		{
			SwapToBaseMap();
		}
		else
		{
			SwapToWorldMap();
		}
	}

	protected void OnLerpToZoomLevelStart()
	{
		// Intentionally empty
	}

	protected void OnLerpToZoomLevelEnd()
	{
		// Intentionally empty
	}

	protected float GetCurrentUserZoom()
	{
		return optUserZoomTarget.IsSet() ? optUserZoomTarget.GetValue() : GetZoom();
	}

	UFUNCTION( BlueprintOverride )
	protected void Tick( FGeometry myGeometry, float inDeltaTime )
	{
		if ( optUserZoomTarget.IsSet() )
		{
			// If the player has zoomed, interp to that value
			float zoom	 = GetZoom();
			float target = optUserZoomTarget.GetValue();
			SetZoom( Math::FInterpTo( zoom, target, inDeltaTime, 10.0f ) );

			if ( Math::IsNearlyEqual( zoom, target ) )
			{
				// Reset the value when the goal was hit
				optUserZoomTarget.Reset();
				SetZoom( target );
				OnLerpToZoomLevelEnd();
			}
		}
	}

	UFUNCTION( BlueprintOverride )
	protected void OnMapMarkerCreated( UNCMapMarkerWidget widget, AActor actor )
	{
		if ( !IsValid( widget ) || !IsValid( actor ) )
			return;

		// Start by resetting the text, only location volumes use it
		widget.SetText( Text::EmptyText );

		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( actor );
		if ( IsValid( asPlayer ) )
		{
			bool isLocalPlayer		 = asPlayer == Client_GetLocalASPawn();
			FLinearColor playerColor = GetUIColorForPlayer( asPlayer );
			widget.SetMaterialVectorValue( MaterialParameter::FILL_COLOR, playerColor );
			widget.SetMaterialScalarValue( MaterialParameter::SHOW_SCALAR, MaterialParameter::GetTrueFalseFloat( isLocalPlayer ) );
		}

		AAS_CarePackageMarker asDrop = Cast<AAS_CarePackageMarker>( actor );
		if ( IsValid( asDrop ) )
		{
			UAS_MapMarkerThread dropThread = Cast<UAS_MapMarkerThread>( CreateThread( UAS_MapMarkerThread::StaticClass(), this ) );
			if ( IsValid( dropThread ) )
			{
				dropThread.Initialize( GameConst::CARE_PACKAGE_WARNING_TIME, widget );
			}
		}

		AAS_PingObject asPing = Cast<AAS_PingObject>( actor );
		if ( IsValid( asPing ) )
		{
			pingMap.Add( asPing, widget );
			asPing.onPingUpdated.AddUFunction( this, n"OnPingObjectUpdated" );
			OnPingObjectUpdated( asPing );
		}

		AAS_BaseSubObjective asSubobjective = Cast<AAS_BaseSubObjective>( actor );
		if ( IsValid( asSubobjective ) )
		{
			widget.SetText( asSubobjective.playerFacingData.name );

			if ( !allSubobjectives.Contains( asSubobjective ) )
			{
				// The first time a subobjective is created, cache it for later
				allSubobjectives.Add( asSubobjective );
			}
		}

		AAS_BaseVault asVault = Cast<AAS_BaseVault>( actor );
		if ( IsValid( asVault ) )
		{
			widget.SetText( Localization::GetUnlocalizedTextFromString( Glyphs::VAULT ) );

			if ( !allVaults.Contains( asVault ) )
			{
				// The first time a vault is created, cache it for later
				allVaults.Add( asVault );
			}
		}

		AAS_LocationTrigger asLocation = Cast<AAS_LocationTrigger>( actor );
		if ( IsValid( asLocation ) )
		{
			widget.SetText( asLocation.GetLocationName() );
		}

		AAS_BaseSystem asBase = Cast<AAS_BaseSystem>( actor );
		if ( IsValid( asBase ) )
		{
			FBaseDataStruct baseData;
			if ( asBase.GetBaseData( baseData ) )
			{
				widget.SetMaterialStreamedTextureValue( n"BaseMap", baseData.baseMap );
			}

			if ( !allBases.Contains( asBase ) )
			{
				// The first time a base is created, cache it for later
				allBases.Add( asBase );
			}

			if ( !allBaseVendors.Contains( asBase ) && IsValid( asBase.domeShield ) )
			{
				// We also want to add each bases vendors into a map
				TArray<AActor> vendors = GetAllActorsInRange2D( AAS_Vendor::StaticClass(), asBase.GetActorLocation(), asBase.domeShield.GetMapSwapDetectionTriggerRadius() );
				allBaseVendors.Add( asBase, FBaseVendors( vendors ) );
			}
		}
	}

	protected void SwapToBaseMap()
	{
		if ( !IsValid( currentBase ) || baseMapActive )
			return;

		TSoftObjectPtr<UTexture2D> map;
		float angleInDegrees = 0.0f;

		FBaseDataStruct baseData;
		if ( currentBase.GetBaseData( baseData ) )
		{
			// Swap the map texture to whatever matches the base the player is in
			GetMinsAndMaxsOfBase( currentBase, mins, maxs );

			FVector northDirection;
			FVector eastDirection;
			GetCardinalDirectionsFromSettings( northDirection, eastDirection );

			// We care about the rotation of our base in relation to the worlds "north"
			FVector forwardVector = currentBase.GetActorForwardVector();
			float angleInRadians  = northDirection.AngularDistance( forwardVector );
			angleInDegrees		  = Math::RadiansToDegrees( angleInRadians );

			if ( forwardVector.DotProduct( eastDirection ) >= 0 )
			{
				angleInDegrees = 360 - angleInDegrees;
			}

			map = baseData.baseMap;
		}

		SetMap( map, mins, maxs, angleInDegrees );
		baseMapActive = true;

		// Show the objectives but hide the base markers when in the base
		ShowHideAllBaseMarkers( false );
		ShowHideAllBaseVendorMarkers( true );
		ShowHideAllObjectiveMarkers( true );
	}

	protected void SwapToWorldMap()
	{
		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( !IsValid( player ) || !baseMapActive )
			return;

		// Reset the map back to the world
		GetMinsAndMaxsOfWorld( mins, maxs );
		TSoftObjectPtr<UTexture2D> map = UNCUtils::GetMaximapForLevel( player );

		SetMap( map, mins, maxs, 0.0f );
		baseMapActive = false;

		// Show the base markers but hide the objectives when in world
		ShowHideAllBaseMarkers( true );
		ShowHideAllBaseVendorMarkers( false );
		ShowHideAllObjectiveMarkers( false );
	}

	protected bool IsInBase() const
	{
		return IsValid( currentBase );
	}

	protected void OnZoomLevelChanged()
	{
		// Intentionally empty
	}

	protected void ShowHideAllBaseMarkers( bool show )
	{
		for ( AAS_BaseSystem base : allBases )
		{
			if ( IsValid( base ) )
			{
				ShowHideMapMarkerComponent( show, base.GetMapMarkerComponent() );
			}
		}
	}

	protected void ShowHideAllObjectiveMarkers( bool show )
	{
		// First do the subobjectives
		for ( AAS_BaseSubObjective subobjective : allSubobjectives )
		{
			if ( IsValid( subobjective ) )
			{
				ShowHideMapMarkerComponent( show, subobjective.GetMapMarkerComponent() );
			}
		}

		// Next do all of the vaults
		for ( AAS_BaseVault vault : allVaults )
		{
			if ( IsValid( vault ) )
			{
				ShowHideMapMarkerComponent( show, vault.GetMapMarkerComponent() );
			}
		}
	}

	protected void ShowHideAllBaseVendorMarkers( bool show )
	{
		for ( TMapIterator<AAS_BaseSystem, FBaseVendors> iter : allBaseVendors )
		{
			for ( AAS_Vendor vendor : iter.Value.baseVendors )
			{
				if ( IsValid( vendor ) )
				{
					ShowHideMapMarkerComponent( show, vendor.GetMapMarkerComponent() );
				}
			}
		}
	}

	private void ShowHideMapMarkerComponent( bool show, UAS_MapMarkerComponent mapMarker )
	{
		if ( IsValid( mapMarker ) )
		{
			if ( show )
			{
				mapMarker.ShowMarkers();
			}
			else
			{
				mapMarker.HideMarkers();
			}
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnPingObjectUpdated( AAS_PingObject ping )
	{
		if ( !IsValid( ping ) || !pingMap.Contains( ping ) )
			return;

		UNCMapMarkerWidget mapMarker = pingMap[ping];
		if ( IsValid( mapMarker ) )
		{
			UCl_PingManager pingManager = ClPingManager();
			if ( IsValid( pingManager ) )
			{
				UAS_PingWidgetSettings pingSettings = pingManager.GetPingWidgetSettings( ping.GetPingType() );
				if ( IsValid( pingSettings ) && IsValid( pingSettings.ownerPlayer ) )
				{
					FLinearColor playerColor = GetUIColorForPlayer( pingSettings.ownerPlayer );
					mapMarker.SetMaterialVectorValue( MaterialParameter::FILL_COLOR, playerColor );
				}
			}
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnPlayerEnteredMapSwapTrigger( AAS_PlayerEntity player, AAS_BaseSystem base )
	{
		currentBase = base;
		OnPlayerEnteredOrExitedMapSwapTrigger( player, base );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnPlayerExitedMapSwapTrigger( AAS_PlayerEntity player, AAS_BaseSystem base )
	{
		currentBase = nullptr;
		OnPlayerEnteredOrExitedMapSwapTrigger( player, base );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnShieldbreakerStatusChanged( AAS_PlayerEntity player )
	{
		AAS_PlayerEntity localPlayer = Client_GetLocalASPawn();
		if ( !IsValid( player ) || !IsValid( localPlayer ) )
			return;

		EAffinity affinity = UNCUtils::GetRelationshipBetweenTeams( player.GetTeam(), localPlayer.GetTeam() );
		if ( affinity != EAffinity::Enemy )
		{
			UNCMapMarkerWidget mapMarker = GetMapMarkerWidgetForActor( player );
			if ( IsValid( mapMarker ) )
			{
				mapMarker.SetMaterialScalarValue( n"HasShieldbreaker", MaterialParameter::GetTrueFalseFloat( PlayerHasShieldBreaker( player ) ) );
			}
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnObjectiveMarkerPlantInProgress( AActor actor )
	{
		SetObjectiveMarkerState( actor, true );
		showObjectiveMarkerTimerText = false;
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnObjectiveMarkerBombPlanted( AActor actor )
	{
		SetObjectiveMarkerState( actor, false );
		showObjectiveMarkerTimerText = true;
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnObjectiveMarkerDefuseInProgress( AActor actor )
	{
		SetObjectiveMarkerState( actor, true );
		showObjectiveMarkerTimerText = false;
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnObjectiveMarkerCountdown( AActor actor, float value )
	{
		if ( showObjectiveMarkerTimerText )
		{
			UNCMapMarkerWidget mapMarker = GetMapMarkerWidgetForActor( actor );
			if ( IsValid( mapMarker ) )
			{
				FText timerText = FText::AsNumber( value, GetDefaultNumberFormattingOptionsWithGrouping() );
				mapMarker.SetText( timerText );
			}
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnObjectiveMarkerBombDefused( AActor actor )
	{
		showObjectiveMarkerTimerText = false;
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnObjectiveMarkerPlantStopped( AActor actor )
	{
		showObjectiveMarkerTimerText = false;
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnObjectiveMarkerParameterChanged( AActor actor, FName parameter, float value )
	{
		UNCMapMarkerWidget mapMarker = GetMapMarkerWidgetForActor( actor );
		if ( IsValid( mapMarker ) )
		{
			mapMarker.SetMaterialScalarValue( parameter, value );
		}
	}

	private FText GetObjectiveMarkerName( AActor actor )
	{
		FText result;

		AAS_BaseSubObjective asSubobjective = Cast<AAS_BaseSubObjective>( actor );
		if ( IsValid( asSubobjective ) )
		{
			result = asSubobjective.playerFacingData.name;
		}

		AAS_BaseVault asVault = Cast<AAS_BaseVault>( actor );
		if ( IsValid( asVault ) )
		{
			result = Localization::GetUnlocalizedTextFromString( Glyphs::VAULT );
		}

		return result;
	}

	private void SetObjectiveMarkerState( AActor actor, bool plantingOrDefusing )
	{
		UNCMapMarkerWidget mapMarker = GetMapMarkerWidgetForActor( actor );
		if ( IsValid( mapMarker ) )
		{
			// mapMarker.Text.SetStyle( isPlanting || isDefusing ? plantingOrDefusingTextStyle : countdownTextStyle );
			mapMarker.SetText( GetObjectiveMarkerName( actor ) );

			// We can't play an animation here, so just set the scale explicitly
			mapMarker.Icon.SetRenderScale( FVector2D::UnitVector * ( plantingOrDefusing ? 1.0f : 1.4f ) );
		}
	}
}