namespace PriorityMessageMiscRaidTemplates
{
	bool GetTemplateMessageData( EPriorityMessageTemplate template, UCL_PriorityMessageManager_v2 manager, UAS_PriorityMessageData& data )
	{
		bool result = false;

		if ( IsValid( data ) && IsValid( manager ) )
		{
			// Start assuming that we will find the template
			result = true;

			// All misc raid messages share some of the same traits

			switch ( template )
			{
				case EPriorityMessageTemplate::V2_DEFEND_ENEMY_REVEAL:
					data.theme			= EPriorityMessageTheme::DEFENDER_STOPPING;
					data.placement		= EPriorityMessagePlacement::CENTER;
					data.priorityLevel	= EPriorityMessageLevel::DEFENDER;
					data.header			= GetLocalizedText( Localization::Raid, f"raid_enemies_revealed_in" );
					data.subheader		= Text::EmptyText;
					data.autoDissappear = true;
					data.onAppearSound	= manager.centerDefend;
					data.onCreate.AddUFunction( manager, n"OnCreate_EnemyReveal_Countdown" );
					break;

				case EPriorityMessageTemplate::V2_ATTACK_ENEMY_REVEAL:
					data.theme			= EPriorityMessageTheme::ATTACKER_INTERRUPTED;
					data.placement		= EPriorityMessagePlacement::CENTER;
					data.priorityLevel	= EPriorityMessageLevel::ATTACKER;
					data.header			= GetLocalizedText( Localization::Raid, f"raid_position_revealed_in" );
					data.subheader		= Text::EmptyText;
					data.autoDissappear = true;
					data.onAppearSound	= manager.centerAttack;
					data.onCreate.AddUFunction( manager, n"OnCreate_EnemyReveal_Countdown" );
					break;

				case EPriorityMessageTemplate::OVERTIME:
					data.widgetClass	= manager.priorityMessageRaidWidgetClass;
					data.theme			= EPriorityMessageTheme::OVERTIME_ACTION;
					data.placement		= EPriorityMessagePlacement::CENTER;
					data.priorityLevel	= EPriorityMessageLevel::ATTACKER;
					data.header			= GetLocalizedText( Localization::Raid, f"raid_overtime_start" );
					data.subheader		= GetLocalizedText( Localization::Raid, f"raid_respawns_locked" );
					data.autoDissappear = false;
					data.autoDissappearDelay = 4.0f;
					data.onAppearSound	= manager.centerDefend;
					break;

				case EPriorityMessageTemplate::OVERTIME_LIGHTWEIGHT:
					data.widgetClass	= manager.priorityMessageRaidWidgetClass;
					data.theme			= EPriorityMessageTheme::OVERTIME_ACTION;
					data.placement		= EPriorityMessagePlacement::CENTER;
					data.priorityLevel	= EPriorityMessageLevel::ATTACKER;
					data.header			= GetLocalizedText( Localization::Raid, f"raid_overtime" );
					data.autoDissappear = false;
					data.autoDissappearDelay = 4.0f;
					data.onAppearSound	= manager.centerDefend;
					break;

				default:
					return false;
			}
		}

		return result;
	}
}