

void SpawnAutomatedDrone( FVector spawnlocation, ANCPlayerCharacter player, ANCWeapon weapon )
{
	AAS_AutomatedDrone drone = Cast<AAS_AutomatedDrone>( Server_SpawnEntity( Abilities().automatedDroneGlobals.droneClass, player, spawnlocation, player.GetActorRotation() ) );
}

AAS_AutomatedDrone GetAutomatedDroneFromPlayer( ANCPlayerCharacter player )
{
	TArray<AActor> drones;
	GetAllActorsOfClass( AAS_AutomatedDrone::StaticClass(), drones );
	for ( auto actor : drones )
	{
		AAS_AutomatedDrone drone = Cast<AAS_AutomatedDrone>( actor );
		if ( drone.GetOwnerPlayer() == player )
			return drone;
	}
	return nullptr;
}

UENUM()
enum EDroneSurveyState
{
	IDLE	  = 0,
	WARNING	  = 1,
	SURVEYING = 2,
	_count
}

namespace AutomatedDroneConst

{
	const FLinearColor BIRD_FRIENDLY_COLOR = FLinearColor( 0, 0.97, 1.0 );
	const FLinearColor BIRD_ENEMY_COLOR	   = FLinearColor( 1, 0.27, 0 );
}

UCLASS( Abstract )
class AAS_AutomatedDrone : ANCDefaultActor
{
	UPROPERTY( DefaultComponent, RootComponent )
	USceneComponent Root;

	UPROPERTY( DefaultComponent )
	UAS_SphereTrigger sphere;
	default sphere.SetRadius( GameConst::AUTOMATED_DRONE_RADIUS );

	AAS_AutomatedDroneBird bird;
	UPROPERTY()
	FNCNetEntityHandle birdHandle;

	ASoundActor flyingSound;
	AAS_DroneSurveyZone surveyZone;

	AAS_PlayerEntity cachedOwnerPlayer;
	int team;
	TMap<AAS_PlayerEntity, int> playersInRadiusForStatus;
	TMap<AAS_PlayerEntity, FName> playersInRadiusForFootsteps;
	TMap<AAS_PlayerEntity, int> playersDetectedBySurvey;

	UPROPERTY()
	FNCNetInt remainingSurveyCharges( 3, 0, 3 );

	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<UAS_DroneStatusWidget> largeStatusWidgetClass;
	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<UAS_DroneStatusWidget> smallStatusWidgetClass;
	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<AAS_AutomatedDroneBird> birdClass;

	UFUNCTION( BlueprintOverride )
	void ServerBeginPlay()
	{
		AAS_PlayerEntity ownerPlayer = GetOwnerPlayer();
		team = ownerPlayer.GetTeam();
		ownerPlayer.Server_OnPawnDeath.AddUFunction( this, n"OnOwnerDeath" );

		ReadySurveyCharge();
		Launch();

		sphere.onPlayerEntered.AddUFunction( this, n"OnPlayerEnteredArea" );
		sphere.onPlayerExited.AddUFunction( this, n"OnPlayerExitedArea" );

		SharedBeginPlay();
	}

	UFUNCTION( BlueprintOverride )
	void ClientBeginPlay()
	{
		if ( GetOwnerPlayer().IsLocallyControlled() )
		{
			// System::SetTimer( this, n"Client_CheckPlayersInRadius", 0.25, true );
			remainingSurveyCharges.OnReplicated().AddUFunction( this, n"OnRemainingChargesChanged" );
			ScriptCallbacks().localClient_onWeaponCooldownChanged.AddUFunction( this, n"OnWeaponCooldownChanged" );

			AAS_HUD myHUD = GetLocalHUD();
			if ( IsValid( myHUD ) )
			{
				int startTime = GetGameTimeMS();
				int endTime	  = GetGameTimeMS() + int( Abilities().automatedDroneGlobals.droneActiveTime * 1000 );

				// TODO @jmccarty: Chat with Mark about this
				// myHUD.mainHUDWidget.UpdateWeaponActiveTime( startTime, endTime );
			}
		}

		SharedBeginPlay();
	}

	UFUNCTION()
	void SharedBeginPlay()
	{
		cachedOwnerPlayer = GetOwnerPlayer();
		ScriptCallbacks().shared_onAutomatedDroneCreated.Broadcast( this, GetOwnerPlayer() );
	}

	UFUNCTION()
	void OnWeaponCooldownChanged( ANCWeapon w, int startTime, int endTime )
	{
		AAS_PlayerEntity player = GetOwnerPlayer();
		ANCWeapon thisWeapon	= player.GetWeaponAtSlot( WeaponSlot::TacticalSlot );
		if ( w != thisWeapon )
			return;

		AAS_HUD myHUD = GetLocalHUD();
		if ( IsValid( myHUD ) )
		{
			if ( endTime > 0 )
				myHUD.mainHUDWidget.RemoveInputContext( n"SpecialWeapon1" );
			else
				myHUD.mainHUDWidget.AddInputContext( n"SpecialWeapon1", GetLocalizedText( Localization::Character_Condor, "tactical_use" ) );
		}
	}

	UFUNCTION()
	void Launch()
	{
		FVector launchLocation = GetActorLocation() + FVector( 0, 0, Abilities().automatedDroneGlobals.droneLaunchHeight );

		if ( IsValid( Abilities().automatedDroneGlobals.drone_flyingSound ) )
			flyingSound = Server_EmitSoundOnEntity_ReturnEntity( Abilities().automatedDroneGlobals.drone_flyingSound, this );
		if ( IsValid( birdClass ) )
		{
			bird = Cast<AAS_AutomatedDroneBird>( Server_SpawnEntity( birdClass, GetOwnerPlayer(), launchLocation ) );
			birdHandle.SetNetValue( bird );
		}

		if ( IsValid( Abilities().automatedDroneGlobals.drone_launchSound1P ) && IsValid( Abilities().automatedDroneGlobals.drone_launchSound3P ) )
			Server_EmitSoundAtLocation_1P3P( Abilities().automatedDroneGlobals.drone_launchSound1P, Abilities().automatedDroneGlobals.drone_launchSound3P, GetActorLocation(), GetOwnerPlayer() );
		/*if ( IsValid( Abilities().automatedDroneGlobals.drone_launchFX ) )
			Server_SpawnEffectAtLocation_OneShot( Abilities().automatedDroneGlobals.drone_launchFX, GetActorLocation() );*/

		System::SetTimer( this, n"DestroyDroneAfterTime", Abilities().automatedDroneGlobals.droneActiveTime, false );
	}

	/*UFUNCTION()
	void Client_CheckPlayersInRadius()
	{
		TArray<AAS_PlayerEntity> playersInRadius = GetAllPlayersInRange( GetActorLocation(), GameConst::AUTOMATED_DRONE_RADIUS );

		TArray<AAS_PlayerEntity> playersToUntrack;
		for( auto data : playersInRadiusForFootsteps )
		{
			AAS_PlayerEntity player = data.Key;
			if ( !playersInRadius.Contains( player ) )
				playersToUntrack.Add( player );
		}

		for( auto player : playersToUntrack )
		{
			Footsteps().UntrackPlayer( playersInRadiusForFootsteps[player] );
			playersInRadiusForFootsteps.Remove( player );
		}

		for( AAS_PlayerEntity player : playersInRadius )
		{
			if ( !playersInRadiusForFootsteps.Contains( player ) )
			{
				FFootstepTrackingData trackingData = Footsteps().TrackPlayer( player, GetGameTimeMS(), Abilities().automatedDroneGlobals.footstepDecayTime );
				playersInRadiusForFootsteps.Add( player, trackingData.tag );
			}
		}
	}*/

	UFUNCTION( NotBlueprintCallable )
	void OnPlayerEnteredArea( AAS_PlayerEntity player, UAS_SphereTrigger trigger )
	{
		if ( player.GetTeam() == GetOwnerPlayer().GetTeam() )
			return;

		if ( IsServer() )
			TrackPlayer( player );
	}

	UFUNCTION( NotBlueprintCallable )
	void OnPlayerExitedArea( AAS_PlayerEntity player, UAS_SphereTrigger trigger )
	{
		if ( !IsServer() )
			return;
		if ( !IsValid( player ) )
			return;

		AAS_PlayerEntity owner = GetOwnerPlayer();

		if ( playersInRadiusForStatus.Contains( player ) )
			UntrackPlayer( player, playersInRadiusForStatus[player] );
		if ( playersInRadiusForFootsteps.Contains( player ) )
			playersInRadiusForFootsteps.Remove( player );

		if ( playersDetectedBySurvey.Contains( player ) )
		{
			ClearPlayerDetection( player, playersDetectedBySurvey[player] );
			playersDetectedBySurvey.Remove( player );
		}

		if ( player == owner )
			DestroyDroneAfterTime();
	}

	/////////////////// HEALTH /////////////////////

	UFUNCTION( NotBlueprintCallable )
	void OnOwnerDeath( const FDamageInfo&in DamageInfo, ANCPlayerCharacter Victim )
	{
		DestroyDrone();
	}

	UFUNCTION()
	void DestroyDroneAfterTime()
	{
		if ( !IsValid( surveyZone ) )
		{
			DestroyDrone();
			return;
		}

		// if drone is currently surveying, let's wait for it to finish
		Thread( this, n"CR_WaitForSurveyFinish" );
	}

	UFUNCTION()
	void CR_WaitForSurveyFinish( UNCCoroutine co )
	{
		co.Wait( this, surveyCompleteSignal );
		DestroyDrone();
	}

	UFUNCTION()
	void DestroyDrone()
	{
		remainingSurveyCharges.SetNetValue( 0 );

		if ( IsValid( flyingSound ) )
			flyingSound.Destroy();
		if ( IsValid( bird ) )
			bird.MarkForDeletion();
		if ( IsValid( Abilities().automatedDroneGlobals.drone_deathSound ) )
			Server_EmitSoundAtLocation( Abilities().automatedDroneGlobals.drone_deathSound, GetActorLocation() );

		if ( IsValid( surveyZone ) )
			surveyZone.Destroy();

		if ( !IsActorBeingDestroyed() || GetBugReproNum() == 9049 ) // this can get called when the actor is already being destroyed - so don't double destroy!
			Destroy();
	}

	UFUNCTION( BlueprintOverride )
	void EndPlay( EEndPlayReason EndPlayReason )
	{
		ScriptCallbacks().shared_onAutomatedDroneDestroyed.Broadcast( this, cachedOwnerPlayer );

		if ( IsClient() && IsValid( Client_GetLocalASPawn() ) && Client_GetLocalASPawn().IsLocallyControlled() )
		{
			for ( auto player : playersInRadiusForFootsteps )
				Footsteps().UntrackPlayer( player.Value );
			playersInRadiusForFootsteps.Empty();

			AAS_HUD myHUD = GetLocalHUD();
			if ( IsValid( myHUD ) )
				myHUD.mainHUDWidget.RemoveInputContext( n"SpecialWeapon1" );
		}

		/*if ( IsServer() )
		{
			AAS_PlayerEntity ownerPlayer = GetOwnerPlayer();
			if ( IsValid( ownerPlayer ) )
			{
				Server_SendGenericMessage( ownerPlayer, Localization::Character_Condor, "condor_offline", EHUDMessageStyle::SMALL_CENTER );
			}
		}*/
	}

	///////////////// MOVEMENT SURVEY //////////////////////////

	UFUNCTION()
	void ReadySurveyCharge()
	{
		if ( !IsServer() )
			return;
		if ( remainingSurveyCharges <= 0 )
			return;

		remainingSurveyCharges.SetNetValue( remainingSurveyCharges - 1 );

		if ( IsValid( surveyZone ) )
			surveyZone.Destroy();
		surveyZone = Cast<AAS_DroneSurveyZone>( Server_SpawnEntity( AAS_DroneSurveyZone::StaticClass(), GetOwnerPlayer(), GetActorLocation() ) );
		surveyZone.SetDroneReady( Abilities().automatedDroneGlobals.droneSurveyWarning );

		System::SetTimer( this, n"FireSurveyCharge", Abilities().automatedDroneGlobals.droneSurveyWarning, false );
	}

	UFUNCTION()
	void FireSurveyCharge()
	{
		if ( IsValid( surveyZone ) )
			surveyZone.SetDroneSurveying( Abilities().automatedDroneGlobals.droneSurveyTime );
		System::SetTimer( this, n"ResetSurveyCharge", Abilities().automatedDroneGlobals.droneSurveyTime, false );
		Thread( this, n"CR_Survey" );
	}

	FNCCoroutineSignal resetSurveySignal;
	FNCCoroutineSignal surveyCompleteSignal;

	UFUNCTION()
	void CR_Survey( UNCCoroutine co )
	{
		co.EndOn( this, resetSurveySignal );
		co.Wait( 0.15 );

		bool bcPlayed = false;
		int detected  = 0;

		int startTime = GetGameTimeMS();

		while ( true )
		{
			for ( auto data : playersInRadiusForStatus )
			{
				AAS_PlayerEntity player = data.Key;
				if ( playersDetectedBySurvey.Contains( player ) )
					continue;

				float playerVelocity = player.GetMovementComponentVelocity().Size();
				if ( playerVelocity > 0.1 )
				{
					detected++;
					DetectPlayer( player );
				}
			}

			int elapsedTime = GetGameTimeMS() - startTime;

			if ( !bcPlayed && elapsedTime > TO_MILLISECONDS( 1.0 ) )
			{
				bcPlayed = true;
				if ( detected == 0 )
					GetOwnerPlayer().PlayBattleChatter( GameplayTags::Audio_VO_BattleChatter_Condor_TacticalNoneDetected );
				else
					GetOwnerPlayer().PlayBattleChatter( GameplayTags::Audio_VO_BattleChatter_Condor_TacticalDetected );
			}

			co.Wait( 0.1 );
		}
	}

	UFUNCTION()
	void ResetSurveyCharge()
	{
		resetSurveySignal.Emit();

		for ( auto player : playersDetectedBySurvey )
			ClearPlayerDetection( player.Key, player.Value );
		playersDetectedBySurvey.Empty();

		if ( IsValid( surveyZone ) )
		{
			surveyZone.Destroy();
			surveyZone = nullptr;
		}

		if ( remainingSurveyCharges <= 0 )
		{
			DestroyDrone();
			return;
		}

		surveyCompleteSignal.Emit();
	}

	UFUNCTION()
	void OnRemainingChargesChanged( int old, int new )
	{
		AAS_HUD myHUD = GetLocalHUD();
		if ( IsValid( myHUD ) )
		{
			myHUD.mainHUDWidget.RemoveInputContext( n"SpecialWeapon1" );
		}
	}

	UFUNCTION()
	void TrackPlayer( AAS_PlayerEntity player )
	{
		int statusID = player.AddStatusEffect( GameplayTags::StatusEffect_Drone_Tracked, 1.0, MAX_flt, 0.0, 0.0 );
		playersInRadiusForStatus.Add( player, statusID );
	}

	UFUNCTION()
	void UntrackPlayer( AAS_PlayerEntity player, int statusID )
	{
		player.ClearStatusEffect( playersInRadiusForStatus[player] );
		playersInRadiusForStatus.Remove( player );
	}

	TMap<AAS_PlayerEntity, AAS_PingObject> playerToPing;

	UFUNCTION()
	void DetectPlayer( AAS_PlayerEntity player )
	{
		float32 timeActive = System::GetTimerRemainingTime( this, f"ResetSurveyCharge" );
		int detectedStatus = player.AddStatusEffect( GameplayTags::StatusEffect_Drone_Detected, 1.0, timeActive, 0.0, 0 );
		playersDetectedBySurvey.Add( player, detectedStatus );
		player.highlightManager.AddIsHighlightedForTeam( team );

		if ( IsValid( surveyZone ) )
			surveyZone.hasDetectedPlayer.SetNetValue( true );

		PlayerStatsManager().IncrementCharacterStatForPlayer( GetOwnerPlayer(), GameplayTags::Classes_Class_Condor, GameplayTags::Progression_PlayerStats_Condor_EnemiesScanned );
	}

	UFUNCTION()
	void ClearPlayerDetection( AAS_PlayerEntity player, int statusID )
	{
		player.ClearStatusEffect( statusID );
		player.highlightManager.RemoveIsHighlightedForTeam( team );
	}
}

UCLASS()
class AAS_AutomatedDroneBird : ANCDefaultActor
{
	UPROPERTY( DefaultComponent, RootComponent )
	USceneComponent root;
	UPROPERTY( DefaultComponent )
	USkeletalMeshComponent birdMesh;
	UPROPERTY( DefaultComponent )
	UNiagaraComponent trackFX;
	UPROPERTY( DefaultComponent )
	UNCNetMovementComponent netMovementComponent;
	default bAutoRegisterServerFixedTick = true;

	UPROPERTY( EditDefaultsOnly )
	UNiagaraSystem appearFX;
	UPROPERTY( EditDefaultsOnly )
	UNiagaraSystem disappearFX;

	TArray<UMaterialInstanceDynamic> mats;

	float radiusGrowthPerSecond = 8;
	float radiansPerSecond		= 0.45;

	FVector initialLocation;
	float currentRadians = 0.0;
	float currentRadius	 = 2500.0;

	const float32 BIRD_LAUNCH_TIME = 2.0f;

	UPROPERTY()
	FNCNetBool isBeingDeleted;

	UFUNCTION( BlueprintOverride )
	void ServerBeginPlay()
	{
		initialLocation = GetActorLocation();
		currentRadians	= Math::RandRange( 0.0f, PI );
		SetActorLocation( initialLocation + ( FVector( Math::Cos( currentRadians ), Math::Sin( currentRadians ), 0 ) * currentRadius ) );

		if ( !IsValid( GetOwnerPlayer() ) )
			return;
		ANCWeapon tacWep = Cast<ANCWeapon>( GetOwnerPlayer().GetWeaponAtSlot( EWeaponSlot::TacticalSlot ) );
		if ( IsValid( tacWep ) )
			Thread( this, n"Thread_TransitionToChargeWeapon", tacWep );
	}

	UFUNCTION( BlueprintOverride )
	void ClientBeginPlay()
	{
		trackFX.AttachTo( birdMesh, n"fx_body_socket", EAttachLocation::SnapToTarget );
		bool isFriendly = IsFriendly( GetOwnerPlayer().GetTeam(), Client_GetLocalASPawn().GetTeam() );
		trackFX.SetColorParameter( n"Team Color", isFriendly ? FLinearColor( 0, 0.97, 1.0 ) : FLinearColor( 1, 0.27, 0 ) );

		TArray<UMaterialInterface> m = birdMesh.GetMaterials();
		for ( int i = 0; i < m.Num(); i++ )
		{
			UMaterialInstanceDynamic dynMat = birdMesh.CreateDynamicMaterialInstance( i );
			if ( IsValid( dynMat ) )
				mats.Add( dynMat );
		}
		SetBirdMatValues( 1.0 );
		isBeingDeleted.OnReplicated().AddUFunction( this, n"OnMarkedForDeletion" );
	}

	UFUNCTION()
	void Client_BeginAppear()
	{
		Thread( this, n"Thread_Appear" );
	}

	UFUNCTION()
	void Thread_TransitionToChargeWeapon( UNCCoroutine co, ANCWeapon weapon )
	{
		co.Wait( 1.0 );

		if ( !IsValid( weapon ) )
			return;
		AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( weapon.GetWeaponOwner() );
		if ( !IsValid( player ) )
			return;

		weapon.AddMod( n"Charges" );
		player.tacticalCooldownComponent.StartCooldown( weapon, GameConst::AUTOMATED_DRONE_CHARGE_COOLDOWN_TIME );
	}


	UFUNCTION(BlueprintOverride)
	void FixedTick(const FNCFixedTickContext& context)
	{
		float DeltaSeconds = context.DeltaSeconds;
		currentRadians	= currentRadians + ( radiansPerSecond * DeltaSeconds );
		currentRadius	= currentRadius + ( radiusGrowthPerSecond * DeltaSeconds );
		FVector nextLoc = initialLocation + ( FVector( Math::Cos( currentRadians ), Math::Sin( currentRadians ), 0 ) * currentRadius );

		FVector rightVector = initialLocation - nextLoc;
		rightVector.Normalize();
		FVector forward = rightVector.CrossProduct( FVector::UpVector );
		FRotator rotation = forward.ToOrientationRotator();

		SetActorLocationAndRotation( nextLoc, rotation, false );
	}


	UFUNCTION()
	void MarkForDeletion()
	{
		isBeingDeleted.SetNetValue( true );
		System::SetTimer( this, n"DestroyAfterTime", BIRD_LAUNCH_TIME + 0.5, false );
	}

	UFUNCTION()
	void OnMarkedForDeletion( bool old, bool new )
	{
		Thread( this, n"Thread_Disappear" );
	}

	UFUNCTION()
	void DestroyAfterTime()
	{
		Destroy();
	}

	UFUNCTION()
	void Thread_Appear( UNCCoroutine co )
	{
		if ( !IsValid( GetOwnerPlayer() ) )
			return;
		if ( !IsValid( Client_GetLocalASPawn() ) )
			return;

		bool isFriendly = IsFriendly( GetOwnerPlayer().GetTeam(), Client_GetLocalASPawn().GetTeam() );

		UNiagaraComponent c1 = Client_SpawnEffectOnEntity_OneShot( appearFX, this );
		c1.SetColorParameter( n"Team Color", isFriendly ? AutomatedDroneConst::BIRD_FRIENDLY_COLOR : AutomatedDroneConst::BIRD_ENEMY_COLOR );
		trackFX.Activate( true );

		int startTime = GetGameTimeMS();
		int endTime	  = startTime + ( TO_MILLISECONDS( 1.0 ) );
		while ( GetGameTimeMS() <= endTime )
		{
			int timeSinceStart = Math::Clamp( GetGameTimeMS() - startTime, 0, MAX_int32 );
			float perc		   = float( timeSinceStart ) / float( endTime - startTime );
			float inv		   = 1 - perc;
			SetBirdMatValues( inv );
			co.Wait( 0.05 );
		}

		SetBirdMatValues( 0 );
		UNiagaraComponent c2 = Client_SpawnEffectOnEntity_OneShot( appearFX, this );
		c2.SetColorParameter( n"Team Color", isFriendly ? AutomatedDroneConst::BIRD_FRIENDLY_COLOR : AutomatedDroneConst::BIRD_ENEMY_COLOR );
	}

	UFUNCTION()
	void Thread_Disappear( UNCCoroutine co )
	{
		if ( !IsValid( GetOwnerPlayer() ) )
			return;
		if ( !IsValid( Client_GetLocalASPawn() ) )
			return;

		// Print( f"Disaappering" );
		bool isFriendly;
		if ( IsValid( GetOwnerPlayer() ) )
			isFriendly = IsFriendly( GetOwnerPlayer().GetTeam(), Client_GetLocalASPawn().GetTeam() );

		UNiagaraComponent c1 = Client_SpawnEffectOnEntity_OneShot( disappearFX, this );
		c1.SetColorParameter( n"Team Color", isFriendly ? AutomatedDroneConst::BIRD_FRIENDLY_COLOR : AutomatedDroneConst::BIRD_ENEMY_COLOR );
		trackFX.Deactivate();

		int startTime = GetGameTimeMS();
		int endTime	  = startTime + ( TO_MILLISECONDS( 1.0 ) );
		while ( GetGameTimeMS() <= endTime )
		{
			int timeSinceStart = Math::Clamp( GetGameTimeMS() - startTime, 0, MAX_int32 );
			float perc		   = float( timeSinceStart ) / float( endTime - startTime );
			SetBirdMatValues( perc );
			co.Wait( 0.05 );
		}

		SetBirdMatValues( 1.0 );
		// Print( f"Disaappered" );
	}

	UFUNCTION()
	void SetBirdMatValues( float v )
	{
		if ( !IsValid( GetOwnerPlayer() ) )
			return;
		if ( !IsValid( Client_GetLocalASPawn() ) )
			return;

		bool isFriendly = IsFriendly( GetOwnerPlayer().GetTeam(), Client_GetLocalASPawn().GetTeam() );
		for ( int i = 0; i < mats.Num(); i++ )
		{
			UMaterialInstanceDynamic dynMat = mats[i];
			dynMat.SetScalarParameterValue( n"Dissolve", v );
			dynMat.SetScalarParameterValue( n"Emissive", v );
			dynMat.SetVectorParameterValue( n"Team Color", isFriendly ? AutomatedDroneConst::BIRD_FRIENDLY_COLOR : AutomatedDroneConst::BIRD_ENEMY_COLOR );

			// Print( f"Setting bird values to {v}" );
		}
	}
}