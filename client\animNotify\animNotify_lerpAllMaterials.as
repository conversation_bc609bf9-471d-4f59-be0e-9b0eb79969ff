UCLASS() // naming class UAnimNotify_* will populate the editor list with just the words before the *
class UAnimNotify_LerpAllMaterials : UAS_ScriptAnimNotifyBase
{
	UPROPERTY()
	FName parameterName;

	UPROPERTY()
	float32 startValue;

	UPROPERTY()
	float32 endValue;

	UPROPERTY()
	float32 duration;

	// customize defaults
	default enableServer		   = false;
	default animNotifyTrackerClass = UAS_AnimNotifyTracker_LerpAllMaterials::StaticClass();
}

UCLASS()
class UAS_AnimNotifyTracker_LerpAllMaterials : UAS_AnimNotifyTrackerBase
{
	UAS_LerpAllMaterialInstances_Thread thread;

	void OnNotify( USkeletalMeshComponent meshComp, UAnimSequenceBase animation,
				   const UAS_ScriptAnimNotifyBase animNotify ) override
	{
		Super::OnNotify( meshComp, animation, animNotify );

		thread = Cast<UAS_LerpAllMaterialInstances_Thread>( CreateThread( UAS_LerpAllMaterialInstances_Thread::StaticClass(), meshComp ) );
		thread.Init( Cast<UAnimNotify_LerpAllMaterials>( animNotify ), meshComp );
	}

	void OnCleanup( USkeletalMeshComponent meshComp, UAnimSequenceBase animation,
					const UAS_ScriptAnimNotifyBase animNotify, EScriptAnimCleanupReason cleanupReason ) override
	{
		if ( IsValid( thread ) )
			thread.Cancel();
	}
}

UCLASS()
class UAS_LerpAllMaterialInstances_Thread : UAS_Thread
{
	UPROPERTY( EditDefaultsOnly )
	FName parameterName;

	UPROPERTY( EditDefaultsOnly )
	float32 startValue;

	UPROPERTY( EditDefaultsOnly )
	float32 endValue;

	UPROPERTY( EditDefaultsOnly )
	float32 duration;

	USkeletalMeshComponent meshComp;

	void Init( const UAnimNotify_LerpAllMaterials animNotify, USkeletalMeshComponent inMeshComp )
	{
		parameterName = animNotify.parameterName;
		startValue	  = animNotify.startValue;
		endValue	  = animNotify.endValue;
		duration	  = animNotify.duration;
		meshComp	  = inMeshComp;
		Start();
	}

	void OnThreadStart( UNCCoroutine co ) override
	{
		Super::OnThreadStart( co );

		co.EndOnDestroyed( meshComp.GetOwner() );

		float startTime = System::GetGameTimeInSeconds();
		float endTime	= startTime + duration;

		TArray<UMaterialInstanceDynamic> materials;
		for ( int i = 0; i < meshComp.GetNumMaterials(); i++ )
		{
			UMaterialInstanceDynamic material = meshComp.CreateDynamicMaterialInstance( i );
			materials.Add( material );

			if ( !IsValid( material ) )
			{
				ScriptError_Silent_WithBug( f"material {i} {meshComp.GetMaterial(i)} on {meshComp.SkeletalMeshAsset} is trying to use LerpMaterialInstance but it doesn't have a dynamic material", "pyeoung", "" );
				return;
			}
		}

		while ( IsValid( meshComp ) && System::GetGameTimeInSeconds() < endTime )
		{
			float value = Math::GetMappedRangeValueClamped( FVector2D( startTime, endTime ), FVector2D( startValue, endValue ), System::GetGameTimeInSeconds() );
			for ( UMaterialInstanceDynamic material : materials )
				material.SetScalarParameterValue( parameterName, value );
			co.Wait( 0.01 );
		}

		for ( UMaterialInstanceDynamic material : materials )
			material.SetScalarParameterValue( parameterName, endValue );
	}
}
////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////