enum EPriorityMessageTemplate
{
	PREPARE_BASE_CENTER,
	
	TEAM_ELIMINATED_EDGE,

	SHIELDBREAKER_CRAFTING,
	SHIELDBREAKER_CRAFTED,

	CUSTOM,

	V2_ATTACK_DOME_BREACHED,
	V2_DEFEND_DOME_BREACHED,
	V2_ATTAC<PERSON>_RAID_STARTED,
	V2_DEFEND_RAID_STARTED,
	V2_DEFEND_GENERATOR_LOST,
	V2_DEFEND_WIN,
	V2_DEFEND_LOSE,
	V2_ATTACK_WIN,
	V2_ATTACK_LOSE,
	V2_ATTACK_GENERATOR_DESTROYED,

	V2_ATTACK_DOME_REPAIR,
	V2_DEFEND_DOME_REPAIR,
	V2_DEFEND_ENEMY_REVEAL,
	V2_ATTACK_ENEMY_REVEAL,

	OVERTIME,
	OVERTIME_LIGHTWEIGHT,

	SHIELDBREAKER_PICKED_UP,
	SHIELDBREAKER_PICKED_UP_ENEMY,
	SHIE<PERSON><PERSON><PERSON>KER_DROPPED,
	EXPLORE_PHASE,
	GAME_STARTING,

	_count
}

enum EPriorityListTemplate
{
	DEFENDER_TELEPORT_HOME,
	DEFENDER_ENEMIES_IN_BASE,
	DEFENDER_BREACHER_PLANTED,
	DEFENDER_BREACHER_PLANTED_PRIORITY,
	DEFENDER_STOP_RAID,
	DEFENDER_FIX_BASE,
	DEFENDER_BOMB_BEING_PLANTED,
	DEFENDER_BOMB_BEING_PLANTED_GENERATOR,
	DEFENDER_BOMB_PLANTED,
	DEFENDER_BOMB_PLANTED_GENERATOR,
	DEFENDER_BOMB_DEFUSING,
	DEFENDER_BOMB_DEFUSING_GENERATOR,
	// DEFENDER_WIN,
	// DEFENDER_LOSE,
	DEFENDER_RESPAWNS_LEFT,
	DEFENDER_RAID_ENERGY,

	ATTACKER_EMPTY,
	ATTACKER_BREACHER_DEFEND,
	ATTACKER_BREACHER_PLANTED,
	ATTACKER_BREACHER_PLANTED_ALT,
	ATTACKER_DEFAULT_RAID_START,
	ATTACKER_BOMB_PLANTING,
	ATTACKER_BOMB_PLANTING_GENERATOR,
	ATTACKER_BOMB_PLANTED,
	ATTACKER_BOMB_PLANTED_GENERATOR,
	ATTACKER_BOMB_BEING_DEFUSED,
	ATTACKER_BOMB_BEING_DEFUSED_GENERATOR,
	// ATTACKER_WIN,
	// ATTACKER_LOSE,
	ATTACKER_RESPAWNS_LEFT,
	ATTACKER_RAID_ENERGY,

	DEFENDER_DOME_REPAIR_EMPTY,
	ATTACKER_DOME_REPAIR_EMPTY,
	DEFENDER_DOME_REPAIR,
	ATTACKER_DOME_REPAIR,
	DEFENDER_ENEMY_REVEAL,
	ATTACKER_ENEMY_REVEAL,
	ATTACKER_ENEMY_IS_REVEALED,

	CUSTOM,
	EMPTY,

	_count
}

enum EPML_DefendIdx
{
	PRIMARY,
	RESPAWN,
	BREACHER,
	REVEAL,
	ENEMIES,
	_count,
}

enum EPML_AttackIdx
{
	PRIMARY,
	RESPAWN,
	BREACHER,
	REVEAL,
	_count,
}