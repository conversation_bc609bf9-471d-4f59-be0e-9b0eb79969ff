UCLASS( Abstract )
class UAS_Training_MessagingHud : UNCScreenWidget
{
	UPROPERTY( NotVisible, BindWidget)
	private UAS_MissionObjectivePanelWidget objectivePanel;

	UPROPERTY( NotVisible, BindWidget)
	private UAS_Training_TitleCardWidget titleCard;

	private TArray<AAS_TrainingObjectiveGroup> activeObjectiveGroups;
	TMap<UAS_TrainingObjective,UAS_MissionObjectiveListObject> objectiveMap;

	UFUNCTION(BlueprintOverride)
	void Construct()
	{
		
	}

	void SetNewObjectiveGroup( AAS_TrainingObjectiveGroup newGroup )
	{
		if ( activeObjectiveGroups.Contains( newGroup ) )
		{
			return;
		}

		activeObjectiveGroups.Add( newGroup );
		newGroup.onObjectiveGroupEnded.AddUFunction( this, n"OnCurrentObjectiveGroupEnded" );
		// Handle objective getting set active on same frame it's killed (onObjectiveGroupEnded may not fire)
		newGroup.OnEndPlay.AddUFunction( this, n"OnCurrentObjectiveGroupEndPlay" );
		
		SetObjectiveWidget_v2( newGroup );
	}
	
	void ClearObjectiveGroupIfMatching( AAS_TrainingObjectiveGroup groupToMatch, ETrainingObjectiveEndContext context )
	{
		if ( !activeObjectiveGroups.Contains( groupToMatch ) )
		{
			return;
		}

		groupToMatch.onObjectiveGroupEnded.Unbind( this, n"OnCurrentObjectiveGroupEnded" );
		groupToMatch.OnEndPlay.Unbind( this, n"OnCurrentObjectiveGroupEndPlay" );

		activeObjectiveGroups.Remove( groupToMatch );
		ClearObjectiveWidget_v2( groupToMatch );
	}

	// TODO: Later on, differentiate interrupted versus success
	UFUNCTION()
	void OnCurrentObjectiveGroupEnded( AAS_TrainingObjectiveGroup group, ETrainingObjectiveEndContext context )
	{
		ClearObjectiveGroupIfMatching( group, context );
	}

	UFUNCTION()
	void OnCurrentObjectiveGroupEndPlay( AActor actor, EEndPlayReason reason )
	{
		ClearObjectiveGroupIfMatching( Cast<AAS_TrainingObjectiveGroup>( actor ), ETrainingObjectiveEndContext::INCOMPLETE );
	}

	void ShowTitle()
	{
		titleCard.PlayShow();
	}

	void HideTitle()
	{
		titleCard.PlayHide();
	}


	//////////////////////////////////////////////////////////////////////////////////////////
	//	I would normally create a mission objective manager class that interfaces between 
	//	client/server but davis's logic is already so intertwined with his level, that I 
	//	just piggy back on it here to manage the objective UI. I'll add a mission objective
	// 	manager later... when I do adventures - Mo

	void SetObjectiveWidget_v2( AAS_TrainingObjectiveGroup newGroup )
	{
		objectivePanel.ClearObjectiveHint();

		if ( !newGroup.settings.descriptionText.IsEmpty() )
			objectivePanel.AddObjectiveHint( newGroup.settings.descriptionText );

		for( UAS_TrainingObjective objective : newGroup.objectives )
		{
			if ( objective.hiddenObjective )
				continue;

			bool isActive = objective.ClientGetIsActiveOrderedObjective();
			UAS_MissionObjectiveListObject objectiveListObject;		
			if ( objective.doProgressBar )
				objectiveListObject = objectivePanel.AddProgressbarObjective( objective.description, isActive );
			else
				objectiveListObject = objectivePanel.AddCheckboxObjective( objective.description, isActive );
			
			objectiveMap.Add(objective,objectiveListObject);

			// in case objective is already complete by the time it's set
			if ( objective.IsObjectiveComplete())	
				objectiveListObject.SetObjectiveCompleted();
			
			Update(objective);

			objective.client_onObjectiveEnded.AddUFunction( this, n"OnTrackedObjectiveEnded" );
			objective.onObjectiveUpdated.AddUFunction( this, n"OnTrackedObjectiveUpdated" );
			objective.client_onObjectiveIncorrectAction.AddUFunction( this, n"OnTrackedObjectiveIncorrectAction" );
			objective.client_onObjectiveCorrectAction.AddUFunction( this, n"OnTrackedObjectiveCorrectAction" );
		}		
	}

	private void Update(UAS_TrainingObjective objective)
	{
		if ( !IsValid( objective ) )
			return;

		if ( !objectiveMap.Contains(objective) )
			return;

		UAS_MissionObjectiveListObject objectiveListObject = objectiveMap[objective];
		if ( !IsValid(objectiveListObject) )
			return;

		objectiveListObject.UpdateObjectiveTextSilent(objective.description);
		objectiveListObject.UpdateObjectiveProgressSilent( objective.GetObjectiveProgress(), objective.GetObjectiveProgressText() );		
		//todo, for single digit progress... we might want to also set the progress text
		
		const bool isActive = objective.ClientGetIsActiveOrderedObjective();
		
		if ( isActive && !objectiveListObject.GetIsActive() && objectiveListObject.GetIsCompleted() )
			objectiveListObject.ClearObjectiveCompleted();

		if ( isActive )
			objectiveListObject.SetActive();
		else
			objectiveListObject.SetInactive();
	}

	UFUNCTION()
	private void OnTrackedObjectiveEnded( UAS_TrainingObjective objective, ETrainingObjectiveEndContext reason )
	{
		Update(objective);

		if ( !IsValid( objective ) )
			return;

		if ( !objectiveMap.Contains(objective) )
			return;

		UAS_MissionObjectiveListObject objectiveListObject = objectiveMap[objective];
		if ( !IsValid(objectiveListObject) )
			return;

		if ( objectiveListObject.GetIsMarkedForDeleteOrRemove() )
			return;

		if ( reason == ETrainingObjectiveEndContext::COMPLETED )
			objectiveListObject.SetObjectiveCompleted();
		else if ( reason == ETrainingObjectiveEndContext::CLIENT_COMPLETE_AND_REMOVE )
			objectiveListObject.SetObjectiveCompletedAndRemove();
	}
	
	UFUNCTION()
	private void OnTrackedObjectiveUpdated( UAS_TrainingObjective objective )
	{
		Update(objective);
	}

	UFUNCTION()
	private void OnTrackedObjectiveIncorrectAction( UAS_TrainingObjective objective )
	{
		const bool isActive = objective.ClientGetIsActiveOrderedObjective();
		if ( !isActive )
			return;
		
		if ( !IsValid( objective ) )
			return;

		if ( !objectiveMap.Contains(objective) )
			return;

		UAS_MissionObjectiveListObject objectiveListObject = objectiveMap[objective];
		if ( !IsValid(objectiveListObject) )
			return;

		objectiveListObject.NagObjectiveIncorrect();
	}

	UFUNCTION()
	private void OnTrackedObjectiveCorrectAction( UAS_TrainingObjective objective )
	{
		const bool isActive = objective.ClientGetIsActiveOrderedObjective();
		if ( !isActive )
			return;
		
		if ( !IsValid( objective ) )
			return;

		if ( !objectiveMap.Contains(objective) )
			return;

		UAS_MissionObjectiveListObject objectiveListObject = objectiveMap[objective];
		if ( !IsValid(objectiveListObject) )
			return;

		objectiveListObject.NagObjective();
	}

	void ClearObjectiveWidget_v2(AAS_TrainingObjectiveGroup oldGroup)
	{
		objectivePanel.ClearObjectiveHint();

		TArray<UAS_TrainingObjective> removalList;

		for( UAS_TrainingObjective objective : oldGroup.objectives )
		{
			if ( !IsValid( objective ) )
				continue;

			if ( !objectiveMap.Contains(objective) )
				continue;
			
			objective.client_onObjectiveEnded.Unbind( this, n"OnTrackedObjectiveEnded" );
			objective.onObjectiveUpdated.Unbind( this, n"OnTrackedObjectiveUpdated" );
			objective.client_onObjectiveIncorrectAction.Unbind( this, n"OnTrackedObjectiveIncorrectAction" );
			objective.client_onObjectiveCorrectAction.Unbind( this, n"OnTrackedObjectiveCorrectAction" );

			//remove from map after we finish iterating
			removalList.Add(objective);

			UAS_MissionObjectiveListObject objectiveListObject = objectiveMap[objective];
			if ( IsValid(objectiveListObject) )
			{
				// Not complete, or completed but not marked for removal
				if ( !objectiveListObject.GetIsCompleted() || !objectiveListObject.GetIsMarkedForDeleteOrRemove() )
				{
					Log(f"Training Msg: Marking {objectiveListObject.GetObjectiveText()} for removal! Is completed? {objectiveListObject.GetIsCompleted()}");
					objectiveListObject.RemoveObjective();
				}
			}
		}

		for ( UAS_TrainingObjective objective : removalList )
			objectiveMap.Remove(objective);
	}
}