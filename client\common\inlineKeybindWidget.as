UCLASS( Abstract )
class UAS_InlineKeybind : UAS_BaseKeybind
{
	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UCommonRichTextBlock keybind;

	UPROPERTY( EditInstanceOnly, BlueprintHidden )
	private FText actionText;

	void SetActionText( FText newActionText )
	{
		if ( !actionText.IdenticalTo( newActionText ) )
		{
			actionText = newActionText;
			Update();
		}
	}

	void OnUpdate( FText glyph ) override
	{
		Super::OnUpdate( glyph );
		keybind.SetText( FText::Format( actionText, glyph ) );
	}
}