
UCLASS()
class UNCAutomatedDroneEvent : UNCNetClientToServerEvent
{
	UPROPERTY()
	FNCNetVector net_firePos;

	UPROPERTY()
	FNCNetRotator net_fireRotation;

	UFUNCTION(BlueprintOverride)
	void OnEventReceived(ANCNetPlayerController receiver)
	{
		AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( receiver.GetControlledPawn() );
		FVector loc = net_firePos;
		FRotator rot = net_fireRotation;
		SendWispToDrone( player, loc, rot );
	}
}

USTRUCT()
struct FDroneSurveyZonePlayerData
{
	UPROPERTY()
	FNCNetPlayerHandle playerHandle;
}

UCLASS()
class AAS_DroneSurveyZone : ANCDefaultActor
{
	UPROPERTY( DefaultComponent, RootComponent )
	USceneComponent root;

	UPROPERTY( DefaultComponent )
	UAS_SphereTrigger sphere;
	default sphere.SetRadius( GameConst::AUTOMATED_DRONE_RADIUS );

	UPROPERTY()
	TArray<FDroneSurveyZonePlayerData> playersInZone;

	UPROPERTY()
	FNCNetInt zoneState( 0, 0, EDroneSurveyState::_count );

	UPROPERTY()
	FNCNetInt stateEndTime;
	UPROPERTY()
	FNCNetBool hasDetectedPlayer;

	int warningSoundID;
	int surveySoundID;

	UAS_DroneStatusWidget statusWidget;
	FScriptDelegateHandle detectedStatusEffect;
	bool hasStatusEffectBeenSet = false;

	//NCNet specific vars
	bool cachedlocalPlayerInZone = false;
	int cachedZoneState = EDroneSurveyState::IDLE;
	bool cachedHasDetectedPlayers = false;

	UFUNCTION( BlueprintOverride )
	void ServerBeginPlay()
	{
		sphere.onPlayerEntered.AddUFunction( this, n"OnPlayerEnteredTrigger" );
		sphere.onPlayerExited.AddUFunction( this, n"OnPlayerExitedTrigger" );
	}

	UFUNCTION( BlueprintOverride )
	void ClientBeginPlay()
	{
		NCNetComponent.OnPostReplication.AddUFunction( this, n"OnPostReplication" );
	}

	UFUNCTION()
	void OnPostReplication( const FNCNetReplicationChanges&in changes )
	{
		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if( !IsValid( player ) )
			return;

		bool zoneChanged = false;
		bool hasDetecedPlayersChanged = false;

		bool newLocalPlayerInZone = IsPlayerInZone( player );
		if( newLocalPlayerInZone != cachedlocalPlayerInZone )
		{
			cachedlocalPlayerInZone = newLocalPlayerInZone;
			if( cachedlocalPlayerInZone )
			{
				SetupClientAssets();
				zoneChanged = true;
			}
			else
			{
				ResetClientAssets();
			}
		}

		if( cachedZoneState != zoneState )
		{
			cachedZoneState = zoneState;
			zoneChanged = true;
		}

		if ( cachedHasDetectedPlayers != hasDetectedPlayer )
		{
			cachedHasDetectedPlayers = hasDetectedPlayer;
			hasDetecedPlayersChanged = true;
		}

		if( zoneChanged )
			OnZoneStateChanged( 0, zoneState );
		if ( hasDetecedPlayersChanged && IsValid( statusWidget ) )
			statusWidget.EnemiesDetectedAlert();

		if ( IsValid( statusWidget ) )
			statusWidget.stateEndTime = stateEndTime;
	}

	UFUNCTION()
	void OnPawnDied( ANCPlayerCharacter player )
	{
		ResetClientAssets();
	}

	UFUNCTION()
	void SetDroneReady( float timeToFire )
	{
		zoneState.SetNetValue( int( EDroneSurveyState::WARNING ) );
		stateEndTime.SetNetValue( GetGameTimeMS() + TO_MILLISECONDS( float32( timeToFire ) ) );

		if ( IsValid( Abilities().automatedDroneGlobals.drone_surveyStartSound1P ) && IsValid( Abilities().automatedDroneGlobals.drone_surveyStartSound3P ) )
			Server_EmitSoundAtLocation_1P3P( Abilities().automatedDroneGlobals.drone_surveyStartSound1P, Abilities().automatedDroneGlobals.drone_surveyStartSound3P, GetActorLocation(), GetOwnerPlayer() );
	}

	UFUNCTION()
	void SetDroneSurveying( float timeToReset )
	{
		zoneState.SetNetValue( int( EDroneSurveyState::SURVEYING ) );
		stateEndTime.SetNetValue( GetGameTimeMS() + TO_MILLISECONDS( float32( timeToReset ) ) );
	}

	UFUNCTION()
	void OnPlayerEnteredTrigger( AAS_PlayerEntity player, UAS_SphereTrigger trigger )
	{
		if ( IsServer() )
		{
			AddPlayerToZone(player);
		}
	}

	UFUNCTION()
	void OnPlayerExitedTrigger( AAS_PlayerEntity player, UAS_SphereTrigger trigger )
	{
		if ( IsServer() )
		{
			RemovePlayerFromZone(player);
		}
	}

	void AddPlayerToZone(AAS_PlayerEntity player)
	{
		for( FDroneSurveyZonePlayerData data : playersInZone )
		{
			if(data.playerHandle.GetPlayer() == player)
			{
				return;
			}
		}

		FDroneSurveyZonePlayerData data;
		data.playerHandle.SetNetValue(player);
		playersInZone.Add(data);
	}

	void RemovePlayerFromZone(AAS_PlayerEntity player)
	{
		FDroneSurveyZonePlayerData data;
		for( int i = playersInZone.Num() - 1; i >= 0; i-- )
		{
			if(playersInZone[i].playerHandle.GetPlayer() == player)
			{
				playersInZone.RemoveAt(i);
				return;
			}
		}
	}

	bool IsPlayerInZone(AAS_PlayerEntity player)
	{
		for( FDroneSurveyZonePlayerData data : playersInZone )
		{
			if(data.playerHandle.GetPlayer() == player)
			{
				return true;
			}
		}

		return false;
	}

	UFUNCTION()
	void OnZoneStateChanged( int old, int new )
	{
		if ( !IsValid( statusWidget ) )
			return;

		bool isFriendly = UNCUtils::GetRelationshipBetweenTeams( GetOwnerPlayer().GetTeam(), Client_GetLocalASPawn().GetTeam() ) == EAffinity::Friendly;

		if ( new == int( EDroneSurveyState::IDLE ) )
			statusWidget.ResetDroneStatus();
		if ( new == int( EDroneSurveyState::WARNING ) )
		{
			statusWidget.SetDroneToReady();
			if ( warningSoundID > -1 )
			{
				Client_StopSound( warningSoundID );
				warningSoundID = -1;
			}
			if ( isFriendly && IsValid( Abilities().automatedDroneGlobals.drone_surveyWarningSound_Friendly ) )
				warningSoundID = Client_EmitSoundUI( Abilities().automatedDroneGlobals.drone_surveyWarningSound_Friendly ).EventID;
			if ( !isFriendly && IsValid( Abilities().automatedDroneGlobals.drone_surveyWarningSound_Enemy ) )
				warningSoundID = Client_EmitSoundUI( Abilities().automatedDroneGlobals.drone_surveyWarningSound_Enemy ).EventID;
		}
		if ( new == int( EDroneSurveyState::SURVEYING ) )
		{
			statusWidget.SetDroneToSurveying();
			if ( warningSoundID > -1 )
			{
				Client_StopSound( warningSoundID );
				warningSoundID = -1;
			}
			if ( surveySoundID > -1 )
			{
				Client_StopSound( surveySoundID );
				surveySoundID = -1;
			}
			if ( isFriendly && IsValid( Abilities().automatedDroneGlobals.drone_surveyActiveSound_Friendly ) )
				surveySoundID = Client_EmitSoundUI( Abilities().automatedDroneGlobals.drone_surveyActiveSound_Friendly ).EventID;
			if ( !isFriendly && IsValid( Abilities().automatedDroneGlobals.drone_surveyActiveSound_Enemy ) )
				surveySoundID = Client_EmitSoundUI( Abilities().automatedDroneGlobals.drone_surveyActiveSound_Enemy ).EventID;
		}
	}

	UFUNCTION()
	void OnStateEndTimeChanged( int old, int new )
	{
		if ( !IsValid( statusWidget ) )
			return;

		statusWidget.stateEndTime = new;
	}

	UFUNCTION( BlueprintOverride )
	void EndPlay( EEndPlayReason EndPlayReason )
	{
		ResetClientAssets();
	}

	UFUNCTION()
	void OnPlayerDetected( ANCPlayerCharacter p )
	{
		if ( IsValid( statusWidget ) )
			statusWidget.MovementAlert();
	}

	void SetupClientAssets()
	{
		ClientCallbacks().OnPawnDied.AddUFunction( this, n"OnPawnDied" );

		detectedStatusEffect   = Client_GetLocalASPawn().AddStatusEffectHasBegunCallback( GameplayTags::StatusEffect_Drone_Detected, this, n"OnPlayerDetected" );
		hasStatusEffectBeenSet = true;

		AAS_AutomatedDrone myDrone = GetAutomatedDroneFromPlayer( GetOwnerPlayer() );
		if ( !IsValid( myDrone ) )
			return;

		if ( Client_GetLocalASPawn().GetTeam() != GetOwnerPlayer().GetTeam() )
			statusWidget = Cast<UAS_DroneStatusWidget>( WidgetBlueprint::CreateWidget( myDrone.largeStatusWidgetClass, Client_GetLocalPlayerController() ) );
		else if ( Client_GetLocalASPawn() == GetOwnerPlayer() )
			statusWidget = Cast<UAS_DroneStatusWidget>( WidgetBlueprint::CreateWidget( myDrone.smallStatusWidgetClass, Client_GetLocalPlayerController() ) );

		if ( !IsValid( statusWidget ) )
			return;
		if ( !IsValid( GetLocalHUD() ) )
			return;

		statusWidget.SetDrone( this );
		GetLocalHUD().mainHUDWidget.alivePanel.AddChild( statusWidget );

		if ( Client_GetLocalASPawn().GetTeam() != GetOwnerPlayer().GetTeam() )
		{
			FAnchors anchors;
			anchors.Minimum = FVector2D( 0.5, 0.3 );
			anchors.Maximum = FVector2D( 0.5, 0.0 );

			UCanvasPanelSlot slot = Cast<UCanvasPanelSlot>( statusWidget.Slot );
			slot.SetAlignment( FVector2D( 0.5, 0.0 ) );
			slot.SetAnchors( anchors );
		}
		else if ( Client_GetLocalASPawn() == GetOwnerPlayer() )
		{
			FAnchors anchors;
			anchors.Minimum = FVector2D( 0.5, 1.0 );
			anchors.Maximum = FVector2D( 0.5, 1.0 );

			UCanvasPanelSlot slot = Cast<UCanvasPanelSlot>( statusWidget.Slot );
			slot.SetAlignment( FVector2D( 0.5, 1.0 ) );
			slot.SetAnchors( anchors );
		}

		if ( Client_GetLocalASPawn().GetStatusEffectValue( GameplayTags::StatusEffect_Drone_Detected ) > 0.0 )
			statusWidget.MovementAlert();
	}

	void ResetClientAssets()
	{
		if ( !IsClient() )
			return;
		if ( !IsValid( Client_GetLocalASPawn() ) )
			return;
		if ( !Client_GetLocalASPawn().IsLocallyControlled() )
			return;
		if ( !IsValid( GetLocalHUD() ) )
			return;

		if ( warningSoundID > -1 )
		{
			Client_StopSound( warningSoundID );
			warningSoundID = -1;
		}
		if ( surveySoundID > -1 )
		{
			Client_StopSound( surveySoundID );
			surveySoundID = -1;
		}

		if ( hasStatusEffectBeenSet )
			Client_GetLocalASPawn().RemoveStatusEffectHasBegunCallback( GameplayTags::StatusEffect_Drone_Detected, detectedStatusEffect );

		if ( !IsValid( statusWidget ) )
			return;

		statusWidget.ResetDroneStatus();
	}
}

UCLASS()
class UAnimNotify_SpawnBird : UAS_ScriptAnimNotifyBase
{
	UPROPERTY()
	FName boneName;

	UFUNCTION( BlueprintOverride )
	bool Notify( USkeletalMeshComponent MeshComp, UAnimSequenceBase Animation,
				 FAnimNotifyEventReference EventReference ) const
	{
		if ( !IsClient() )
			return false;

		FTransform t = MeshComp.GetBoneTransform( n"mount_spine_01" );
		FVector loc	 = t.Location;
		FRotator rot = t.Rotator();

		if ( IsValid( Client_GetLocalPlayerController() ) )
		{
			UNCAutomatedDroneEvent automatedDroneEvent = Cast<UNCAutomatedDroneEvent>( NewObject( GetCurrentWorld(), UNCAutomatedDroneEvent::StaticClass() ) );
			automatedDroneEvent.net_firePos.SetNetValue(loc);
			automatedDroneEvent.net_fireRotation.SetNetValue(rot);
			automatedDroneEvent.SendToServer();
		}

		return false;
	}
}

UCLASS( Abstract )
class UAS_DroneStatusWidget : UNC_DisplayWidget
{
	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UImage timer;
	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UTextBlock countdown;

	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidgetOptional ) )
	UCanvasPanel positionRevealedLayer;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidgetOptional ) )
	UCanvasPanel timerLayer;

	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidgetOptional ) )
	UImage frame;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidgetOptional ) )
	UImage gradient;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UTextBlock statusText;

	UPROPERTY()
	EDroneSurveyState state;
	UPROPERTY()
	int stateEndTime;

	AAS_DroneSurveyZone myZone;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		if ( IsValid( positionRevealedLayer ) )
			SetWidgetVisibilitySafe( positionRevealedLayer, ESlateVisibility::Hidden );
		SetWidgetVisibilitySafe( this, ESlateVisibility::Hidden );
		SetWidgetVisibilitySafe( countdown, ESlateVisibility::Hidden );
	}

	FNCCoroutineSignal droneStatusReset;

	UFUNCTION()
	void SetDrone( AAS_DroneSurveyZone zone )
	{
		myZone = zone;
		bool isFriendly = UNCUtils::GetRelationshipBetweenTeams( myZone.GetOwnerPlayer().GetTeam(), Client_GetLocalASPawn().GetTeam() ) == EAffinity::Friendly;
		timer.GetDynamicMaterial().SetScalarParameterValue( n"AffinityState", isFriendly ? 2 : 0 );

		if ( isFriendly )
		{
			if ( IsValid( frame ) )
			{
				frame.GetDynamicMaterial().SetVectorParameterValue( n"StartColor", FLinearColor(0.0f, 0.77, 0.804 , 0.0f ) );
				frame.GetDynamicMaterial().SetVectorParameterValue( n"EndColor", FLinearColor(0.0f, 0.77, 0.804 ) );
			}

			if ( IsValid( gradient ) )
				gradient.SetBrushTintColor( FSlateColor( FLinearColor( 0.0f, 0.77, 0.804  )) );
		}
	}

	UFUNCTION()
	void SetDroneToReady()
	{
		state = EDroneSurveyState::WARNING;

		statusText.SetText( GetLocalizedText( Localization::Character_Condor, "tac_scan_ready" ) );

		float32 secondsLeft = TO_SECONDS( stateEndTime - GetGameTimeMS() );
		if ( secondsLeft < 0 )
			secondsLeft = Abilities().automatedDroneGlobals.droneSurveyWarning; // this is dumb but seconds left might not be set yet

		FNumberFormattingOptions options;
		options.SetAlwaysSign( false );
		options.SetMinMaxFractionalDigits( 0 );
		options.SetRoundingMode( ERoundingMode::FromZero );
		countdown.SetText( FText::AsNumber( secondsLeft, options ) );

		SetWidgetVisibilitySafe( countdown, ESlateVisibility::Visible );
		timer.GetDynamicMaterial().SetScalarParameterValue( n"IconAlpha", 0.0f );

		SetWidgetVisibilitySafe( this, ESlateVisibility::SelfHitTestInvisible );
		Thread( this, n"CR_DroneUpdate" );
	}

	UFUNCTION()
	void SetDroneToSurveying()
	{
		state = EDroneSurveyState::SURVEYING;

		statusText.SetText( GetLocalizedText( Localization::Character_Condor, "tac_scan_surveying" ) );

		SetWidgetVisibilitySafe( countdown, ESlateVisibility::Hidden );
		timer.GetDynamicMaterial().SetScalarParameterValue( n"IconAlpha", 1.0f );
		timer.GetDynamicMaterial().SetScalarParameterValue( n"LerpAlpha", 1.0 );

		SetWidgetVisibilitySafe( this, ESlateVisibility::SelfHitTestInvisible );
		Thread( this, n"CR_DroneUpdate" );
	}

	UFUNCTION()
	void CR_DroneUpdate( UNCCoroutine co )
	{
		droneStatusReset.Emit();
		co.EndOn( this, droneStatusReset );

		while ( true )
		{
			float32 totalTime = 0.1;
			switch ( state )
			{
				case EDroneSurveyState::WARNING:
					totalTime = Abilities().automatedDroneGlobals.droneSurveyWarning;
					break;
				case EDroneSurveyState::SURVEYING:
					totalTime = Abilities().automatedDroneGlobals.droneSurveyTime;
					break;
				default:
					break;
			}

			float32 frac = TO_SECONDS( stateEndTime - GetGameTimeMS() ) / totalTime;
			if ( state == EDroneSurveyState::WARNING )
				frac = 1.0 - frac;
			timer.GetDynamicMaterial().SetScalarParameterValue( n"LerpAlpha", frac );

			float32 secondsLeft = TO_SECONDS( stateEndTime - GetGameTimeMS() );
			if ( state == EDroneSurveyState::WARNING && secondsLeft >= 0 )
			{
				FNumberFormattingOptions options;
				options.SetAlwaysSign( false );
				options.SetMinMaxFractionalDigits( 0 );
				options.SetRoundingMode( ERoundingMode::FromZero );
				countdown.SetText( FText::AsNumber( secondsLeft, options ) );
			}

			co.Wait( 0.1 );
		}
	}

	UFUNCTION()
	void MovementAlert()
	{
		if ( IsValid( positionRevealedLayer ) )
			SetWidgetVisibilitySafe( positionRevealedLayer, ESlateVisibility::Visible );
		if ( IsValid( timerLayer ) )
			SetWidgetVisibilitySafe( timerLayer, ESlateVisibility::Hidden );
	}

	UFUNCTION()
	void EnemiesDetectedAlert()
	{
		bool isFriendly = UNCUtils::GetRelationshipBetweenTeams( myZone.GetOwnerPlayer().GetTeam(), Client_GetLocalASPawn().GetTeam() ) == EAffinity::Friendly;
		if ( !isFriendly )
			return;

		statusText.SetText( GetLocalizedText( Localization::Character_Condor, "tac_scan_enemies_found" ) );
		if ( IsValid( frame ) )
		{
			frame.GetDynamicMaterial().SetVectorParameterValue( n"StartColor", FLinearColor( 0.65f, 0.0185, 0.0185, 0.0f ) );
			frame.GetDynamicMaterial().SetVectorParameterValue( n"EndColor", FLinearColor( 0.65f, 0.0185, 0.0185 ) );
		}

		if ( IsValid( gradient ) )
			gradient.SetBrushTintColor( FSlateColor( FLinearColor( 0.65f, 0.0185, 0.0185 ) ) );

		timer.GetDynamicMaterial().SetScalarParameterValue( n"AffinityState", 0 );
	}

	UFUNCTION()
	void ResetDroneStatus()
	{
		droneStatusReset.Emit();
		RemoveFromParent();
	}
}