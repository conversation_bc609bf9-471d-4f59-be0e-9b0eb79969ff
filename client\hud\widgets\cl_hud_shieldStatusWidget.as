UCLASS(Abstract)
class UAS_ShieldStatusWidget : UUserWidgetDefault
{
    UPROPERTY( BlueprintReadOnly, Meta=(BindWidget) )
    UImage shieldOutline;

    UPROPERTY( BlueprintReadOnly, Meta=(BindWidget) )
    UImage shieldShadow;

    UPROPERTY( BlueprintReadOnly, Meta=(BindWidget) )
    UProgressBar shieldFillBar;
    
    UPROPERTY( BlueprintReadOnly, Meta=(BindWidget) )
    UAS_BackpackGridWidget backpackGrid_shieldButton;

    void SetShieldInfo( FShieldItemData newData )
    {        
        FSlateBrush newBrush = GetNewBrushThatIsCopyOf( shieldOutline.Brush );
        newBrush.ResourceObject = newData.inventoryIcon_outline;
        newBrush.TintColor = newData.GetShieldColor();
        shieldOutline.SetBrush( newBrush );

        newBrush = GetNewBrushThatIsCopyOf( shieldOutline.Brush );
        newBrush.ResourceObject = newData.inventoryIcon_outline;
        shieldShadow.SetBrush( newBrush );

        float fillAlpha = shieldFillBar.GetFillColorAndOpacity().A;
        FLinearColor newFillColor = newData.GetShieldColor();
        newFillColor.A = fillAlpha;
        shieldFillBar.SetFillColorAndOpacity( newFillColor );

        newBrush = GetNewBrushThatIsCopyOf( shieldFillBar.WidgetStyle.FillImage );
        newBrush.ResourceObject = newData.inventoryIcon_fill;
        shieldFillBar.WidgetStyle.FillImage = newBrush;

    }
}