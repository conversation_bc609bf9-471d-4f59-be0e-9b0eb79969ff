const FLinearColor CLAW_NEAR_TARGET_COLOR = FLinearColor( 0.16, 0.66, 0.32 );
const FLinearColor CLAW_FAR_TARGET_COLOR  = FLinearColor( 0.00, 0.40, 1.00 );

namespace ClawConsts
{
	const float movementTimeScale = 0.75f;

	const float32 CLAW_MELEE_RADIUS	  = 30.0f;
	const float32 CLAW_MELEE_DISTANCE = 160.0f;
	const float32 CLAW_MELEE_FOV	  = 80.0f;

	// Meters per second speed that we should lerp towards our melee lunge target
	const float meleeLungeMovementSpeedMetersPerSecond = 75.0f;

	// Amount of velocity to apply when bouncing off of an iron wall
	const float meleeBounceOffIronVelocity = 600.0f;
	// Amount of upwards bias to put into the bounce
	const float meleeBounceVerticalBias = 1.0f;

	// Amount of velocity to apply when bouncing off of an iron wall
	const float rangedBounceOffIronVelocity = 600.0f;
	// Amount of upwards bias to put into the bounce
	const float rangedBounceVerticalBias = 1.0f;

	// Amount of time to wait before switching from melee to leap
	const int leapActivationTimerMS = 300;
	// Amount of time to leap before we allow mantling and deflection off of surfaces
	const int leapEnableMantleAndDeflectionTimeMS = 250;
	// Failsafe timer, if leap goes for longer than this we break out of leap
	const int leapFailsafeTimerMS = 5000;

	const float CLAW_WALL_DAMAGE   = 1125.0f;
	const float CLAW_PLAYER_DAMAGE = 100.0f;
}

UENUM()
enum EClawTargetMode
{
	NONE,
	MELEE,
	RANGED
}

UCLASS()
class UAS_WeaponContext_Claw_New : UAS_WeaponContext_TacticalAbility
{
	default cooldownTime = 40.0f;

	AAS_PlayerEntity myPlayer;
	ANCWeapon cachedWeapon;

	TArray<ASoundActor> sustainActors;

	FTimerHandle meleeLungeTimer;

	bool clientLeapPredictionEnabled = false;
	bool serverLeapEnabled			 = false;
	FTimerHandle leapActivationTimerHandle;
	FTimerHandle leapEnableMantleAndDeflectTimer;
	FTimerHandle leapFailsafeTimer;
	int leapHitTargetCount = 0;
	TArray<AActor> leapDamagedActors;
	FVector lastLungeDir = FVector::ZeroVector;
	float lastLeapAttackAnimPlayTime = 0.0f;

	UFUNCTION( BlueprintOverride )
	void CodeCallback_OnWeaponDataSet( ANCWeapon weapon )
	{
		Super::CodeCallback_OnWeaponDataSet( weapon );

		cachedWeapon = weapon;
		myPlayer	 = Cast<AAS_PlayerEntity>( cachedWeapon.GetWeaponOwner() );
	}

	UFUNCTION( BlueprintOverride )
	void CodeCallback_OnWeaponActivate( ANCWeapon weapon )
	{
		// Shared code run predictively on client and on server

		clientLeapPredictionEnabled = false;
		serverLeapEnabled			= false;
		leapActivationTimerHandle	= System::SetTimer( this, n"HandleLeapActivationTimerExpired", TO_SECONDS( ClawConsts::leapActivationTimerMS ), false );

		if ( cachedWeapon.HasMod( n"RangedLeap" ) )
			cachedWeapon.RemoveMod( n"RangedLeap" );
		weapon.AddMod( n"EarlyCancel" );
	}

	UFUNCTION( BlueprintOverride )
	void CodeCallback_OnWeaponDeactivate( ANCWeapon weapon )
	{
		// Shared code run predictively on client and on server
		System::ClearAndInvalidateTimerHandle( leapActivationTimerHandle );
		System::ClearAndInvalidateTimerHandle( leapFailsafeTimer );
		System::ClearAndInvalidateTimerHandle( leapEnableMantleAndDeflectTimer );
		System::ClearAndInvalidateTimerHandle( meleeLungeTimer );

		if ( !IsServer() )
			return;

		// Server-only

		AAS_WeaponClawTargetManager myTargetManager = GetMyTargetManager();
		if ( IsValid(myTargetManager) )
			myTargetManager.StartTargeting(EClawTargetMode::MELEE);

		for ( auto sActor : sustainActors )
		{
			if ( IsValid( sActor ) )
				sActor.Destroy();
		}
	}

	UFUNCTION( BlueprintOverride )
	void CodeCallback_OnWeaponTossRelease( ANCWeapon weapon, FWeaponPrimaryAttackInfo attackInfo,
										   FWeaponPrimaryAttackReturnParams& returnInfo )
	{
		Super::CodeCallback_OnWeaponTossRelease( weapon, attackInfo, returnInfo );

		if ( IsValid( cachedWeapon ) )
		{
			if ( cachedWeapon.HasMod( n"EarlyCancel" ) )
				cachedWeapon.RemoveMod( n"EarlyCancel" );
		}

		if ( IsClient() )
		{
			if ( clientLeapPredictionEnabled )
			{
				ClientStartLeap();
			}
		}
		else // IsServer()
		{
			if ( IsValid( weapon.GetWeaponOwner() ) )
			{
				AAS_PlayerEntity asPawn = Cast<AAS_PlayerEntity>( weapon.GetWeaponOwner() );
				if ( IsValid( asPawn ) )
				{
					if ( asPawn.IsPlayerRidingMount() )
						Mount::Dismount( asPawn );
				}
			}

			AAS_WeaponClawTargetManager myTargetManager = GetMyTargetManager();
			if ( !IsValid( myTargetManager ) )
				return;

			EClawTargetMode currentTargetMode = EClawTargetMode::MELEE;
			bool hasMeleeTarget				  = myTargetManager.HasMeleeTargets();
			if ( !hasMeleeTarget && serverLeapEnabled )
				currentTargetMode = EClawTargetMode::RANGED;

			// clear ranged targetting timer handle if not in ranged mode by now
			if ( System::IsValidTimerHandle( leapActivationTimerHandle ) && currentTargetMode != EClawTargetMode::RANGED )
				System::ClearAndInvalidateTimerHandle( leapActivationTimerHandle );

			if ( currentTargetMode == EClawTargetMode::MELEE )
			{
				ServerStartMeleeLunge();
			}
			else
			{
				ServerStartLeap();
			}
		}
	}

	UFUNCTION()
	void ServerStartMeleeLunge()
	{
		AAS_WeaponClawTargetManager myTargetManager = GetMyTargetManager();
		if ( !IsValid( myTargetManager ) )
			return;

		TArray<AActor> targetsToAttack;
		if ( myTargetManager.GetActiveTargetMode() == EClawTargetMode::MELEE )
			targetsToAttack.Append( myTargetManager.GetCachedMeleeTargets() );

		cachedWeapon.PlayNetMontage( EAnimMontageActivity::ACTM_FIRE );

		myPlayer.GetPlayerMesh3P().PlayNetMontage( EAnimMontageActivity::ACTM3P_CUSTOM_2 );

		Server_EmitSoundOnEntity_1P3P( Abilities().clawNewGlobals.clawStrike1P, Abilities().clawNewGlobals.clawStrike3P, myPlayer, myPlayer );
		if ( targetsToAttack.Num() == 0 )
		{
			// player has whiffed melee
			Server_EmitSoundOnEntity_WithSendFlags( Abilities().clawNewGlobals.clawMiss1Pv3P, myPlayer, myPlayer, ESendEventFlags::SEND_TO_OWNER );
			Server_EmitSoundOnEntity_WithSendFlags( Abilities().clawNewGlobals.clawMiss3Pv3P, myPlayer, myPlayer, ESendEventFlags::SEND_TO_FRIENDLIES | ESendEventFlags::SEND_TO_ENEMIES );
			cachedWeapon.SetClipAmmo( cachedWeapon.GetClipAmmo() + 1 );
			System::SetTimer( this, n"SwapToLastWeapon", 0.5, false );
			return;
		}

		SetupPlayerForLunge();

		// Put Redmane into a blank movement state, this will let StartLerpMovement move him through
		// the air instead of having to walk to his target
		myPlayer.EnableCustomScriptMovement();
		myPlayer.SetMovementComponentVelocity( FVector( 0, 0, 0 ) );
		myPlayer.ServerDisableMovement();
		myPlayer.DisableWallScrambleUntilLanded();

		// Lerp towards the target
		const FVector lerpStartLocation = myPlayer.GetActorLocation();
		// Offset hit location down by our eye height.
		// The target manager uses eye location but we are moving the actor capsule, this offset makes us meet the target
		float eyeHeight					 = myPlayer.GetEyeHeightFromCenterForCurrentCrouchState();
		const FVector lerpOffset		 = FVector( 0, 0, eyeHeight );
		const FVector lerpTargetLocation = targetsToAttack[0].GetActorLocation() - lerpOffset;
		const float lerpDistM			 = Distance( lerpTargetLocation, lerpStartLocation ) / 100.0f;
		const float lerpTimeSeconds		 = lerpDistM / ClawConsts::meleeLungeMovementSpeedMetersPerSecond;
		FPlayerLerpMovementOverlayParams lerpParams;
		lerpParams.StartLocation   = lerpStartLocation;
		lerpParams.TargetLocation  = lerpTargetLocation;
		lerpParams.DurationSeconds = lerpTimeSeconds;
		lerpParams.EasingFunc	   = EEasingFunc::SinusoidalInOut;
		//search for moving player targets
		for( auto t : targetsToAttack )
		{
			if ( t.IsA( AAS_PlayerEntity::StaticClass() ) || t.IsA( AAS_Dev_MovingTarget::StaticClass() ) )
			{
				lerpParams.TargetComponent = t.RootComponent;
				break;
			}
		}
		myPlayer.StartLerpMovementOverlay( lerpParams );

		lastLungeDir = ( lerpTargetLocation - lerpStartLocation ).GetSafeNormal();

		cachedTargets		  = targetsToAttack;
		cachedLungeTargetMode = EClawTargetMode::MELEE;

		meleeLungeTimer = System::SetTimer( this, n"PlayerMeleeLungeComplete", lerpTimeSeconds, false );

		// NOTE -- joe - This is overwriting the animation started above, is this intentional?
		myPlayer.GetPlayerMesh3P().PlayNetMontage( EAnimMontageActivity::ACTM3P_CUSTOM_1 );
	}

	UFUNCTION()
	void ClientStartLeap()
	{
		// Puts the local client into the leap movement mode predictively

		FPlayerLeapParams leapParams = GetLeapParams();

		FOnLeapEvent callback;
		callback.BindUFunction( this, n"HandleLeapEvent" );
		myPlayer.SetLeapEventHandler( callback );

		myPlayer.OnLeapEnded.AddUFunction( this, n"HandleLeapEnded" );
		myPlayer.StartLeapMovement( leapParams );
		myPlayer.DisableWallScrambleUntilLanded();

		leapDamagedActors.Empty();
		leapHitTargetCount = 0;
		alternatingLeapCounter = 0;
		lastLeapAttackAnimPlayTime = 0.0f;

		leapEnableMantleAndDeflectTimer = System::SetTimer( this, n"HandleLeapEnableMantleAndDeflectionTimer", TO_SECONDS( ClawConsts::leapEnableMantleAndDeflectionTimeMS ), false );
	}

	UFUNCTION()
	void ServerStartLeap()
	{
		AAS_WeaponClawTargetManager myTargetManager = GetMyTargetManager();
		if ( !IsValid( myTargetManager ) )
			return;

		Server_EmitSoundOnEntity_1P3P( Abilities().clawNewGlobals.clawLeap1P, Abilities().clawNewGlobals.clawLeap3P, myPlayer, myPlayer );

		SetupPlayerForLunge();

		leapDamagedActors.Empty();
		leapHitTargetCount = 0;
		alternatingLeapCounter = 0;
		lastLeapAttackAnimPlayTime = 0.0f;

		leapFailsafeTimer = System::SetTimer( this, n"HandleLeapFailsafeTimer", TO_SECONDS( ClawConsts::leapFailsafeTimerMS ), false );

		FOnLeapEvent callback;
		callback.BindUFunction( this, n"HandleLeapEvent" );
		myPlayer.SetLeapEventHandler( callback );
		myPlayer.OnLeapEnded.AddUFunction( this, n"HandleLeapEnded" );
		FPlayerLeapParams leapParams = GetLeapParams();
		myPlayer.StartLeapMovement( leapParams );
		myPlayer.DisableWallScrambleUntilLanded();
		leapEnableMantleAndDeflectTimer = System::SetTimer( this, n"HandleLeapEnableMantleAndDeflectionTimer", TO_SECONDS( ClawConsts::leapEnableMantleAndDeflectionTimeMS ), false );

		cachedLungeTargetMode = EClawTargetMode::RANGED;

		myPlayer.GetPlayerMesh3P().PlayNetMontage( EAnimMontageActivity::ACTM3P_CUSTOM_1 );
		myPlayer.server_onPlayerStartedRedmaneLeap.Broadcast( myPlayer );
	}

	UFUNCTION()
	FPlayerLeapParams GetLeapParams()
	{
		FPlayerLeapParams leapParams;
		FProjectileArcParams arcParams = NCProjectile::CalcArcParamsForLaunchAngle( cachedWeapon.WeaponConfigData.ProjectileData, cachedWeapon.WeaponFireAngles );
		leapParams.InitialVelocity	   = ( cachedWeapon.WeaponFireAngles.Vector() * arcParams.InitialSpeed );
		leapParams.GravityScale		   = arcParams.GravityScale;
		leapParams.bAllowMantle		   = false;
		leapParams.bMaintainVelocity   = true;
		leapParams.bCrouch			   = true;
		return leapParams;
	}

	UFUNCTION()
	void HandleLeapActivationTimerExpired()
	{
		// This returns true if the player is still holding down the attack button for this weapon
		if ( cachedWeapon.GetPrimaryAttackRequested() )
		{
			if ( IsClient() )
			{
				clientLeapPredictionEnabled = true;
			}
			else // IsServer()
			{
				serverLeapEnabled = true;

				Server_EmitSoundOnEntity_1P3P( Abilities().clawNewGlobals.clawActivate1P, Abilities().clawNewGlobals.clawActivate3P, myPlayer, myPlayer );
				sustainActors								= Server_EmitSoundOnEntity_1P3P_ReturnEntities( Abilities().clawNewGlobals.clawSustain1P, Abilities().clawNewGlobals.clawSustain3P, myPlayer, myPlayer );
				AAS_WeaponClawTargetManager myTargetManager = GetMyTargetManager();
				if ( IsValid( myTargetManager ) )
					myTargetManager.StartTargeting(EClawTargetMode::RANGED);
			}

			cachedWeapon.AddMod( n"RangedLeap" );
		}
	}

	UFUNCTION()
	void HandleLeapEnableMantleAndDeflectionTimer()
	{
		myPlayer.SetLeapAllowMantle( true );
		myPlayer.SetLeapMaintainVelocity( false );
	}

	UFUNCTION()
	void HandleLeapEvent( const FPlayerLeapEvent&in hitObstacle, FPlayerLeapEventResult& hitResponse )
	{
		hitResponse.bContinueLeap = ( hitObstacle.Event != EPlayerLeapEventType::LandedOnGround );

		bool clientFakeLeapAttack = false;
		if ( IsValid( hitObstacle.Hit.Actor ) && !leapDamagedActors.Contains( hitObstacle.Hit.Actor ) )
		{
			AAS_WeaponClawTargetManager myTargetManager = GetMyTargetManager();
			if ( IsValid( myTargetManager ) )
			{
				if ( myTargetManager.IsValidTargetType( hitObstacle.Hit.Actor ) )
				{
					if ( IsServer() )
					{
						TArray<AActor> targetActors;
						targetActors.Add( hitObstacle.Hit.Actor );
						Thread( this, n"AttackTargets", targetActors, hitObstacle.Hit );
					}
					else 
					{
						if (!leapDamagedActors.Contains(hitObstacle.Hit.Actor))
						{
							clientFakeLeapAttack = true;
							leapDamagedActors.Add(hitObstacle.Hit.Actor);
						}
					}

					leapHitTargetCount++;
					hitResponse.bMaintainVelocity = true;
				}
			}
		}

		if (clientFakeLeapAttack)
		{
			leapHitTargetCount++;
			if ( alternatingLeapCounter == 0 )
				cachedWeapon.PlayNetMontage( EAnimMontageActivity::ACTM_CUSTOM_1 );
			else if ( !IsEven( alternatingLeapCounter ) )
				cachedWeapon.PlayNetMontage( EAnimMontageActivity::ACTM_CUSTOM_2 );
			else if ( IsEven( alternatingLeapCounter )  )
				cachedWeapon.PlayNetMontage( EAnimMontageActivity::ACTM_CUSTOM_3 );
			alternatingLeapCounter++;
			lastLeapAttackAnimPlayTime = TO_SECONDS(myPlayer.GetGameTimeMS());
		}

	}

	UFUNCTION()
	void HandleLeapEnded()
	{
		System::ClearAndInvalidateTimerHandle( leapEnableMantleAndDeflectTimer );
		System::ClearAndInvalidateTimerHandle( leapFailsafeTimer );

		PlayerLeapComplete();
	}

	UFUNCTION()
	void HandleLeapFailsafeTimer()
	{
		// This will activate HandleLeapEnded() if we haven't already exited the state
		myPlayer.EndLeapMovement();
	}

	UFUNCTION()
	AAS_WeaponClawTargetManager GetMyTargetManager()
	{
		auto passive = Passive_Enraged();
		if (IsValid(passive))
		{
			return passive.GetTargetManager(myPlayer);
		}
		return nullptr;
	}

	TArray<AActor> cachedTargets;
	bool isLunging = false;
	EClawTargetMode cachedLungeTargetMode;

	UFUNCTION()
	void PlayLungeImpactEffects()
	{
		FHitResult hit = HackGetImpact( myPlayer.GetActorLocation() + ( FVector::UpVector * 50 ), myPlayer.GetActorLocation() - ( FVector::UpVector * 200 ) );
		if ( IsValid( hit.GetPhysMaterial() ) )
		{
			Server_PlayImpactTableAtPoint_WithSendFlags( myPlayer.GetEntityComponent(),
														 Abilities().clawNewGlobals.landingImpactTable,
														 hit.ImpactPoint,
														 hit.ImpactNormal.Rotation(),
														 hit.GetPhysMaterial(),
														 myPlayer,
														 ESendEventFlags::SEND_TO_FRIENDLIES | ESendEventFlags::SEND_TO_ENEMIES,
														 EPlayerPOV::_3P );
			Server_PlayImpactTableAtPoint_WithSendFlags( myPlayer.GetEntityComponent(),
														 Abilities().clawNewGlobals.landingImpactTable,
														 hit.ImpactPoint,
														 hit.ImpactNormal.Rotation(),
														 hit.GetPhysMaterial(),
														 myPlayer,
														 ESendEventFlags::SEND_TO_OWNER,
														 EPlayerPOV::_1P );
		}
	}

	UFUNCTION()
	void PlayerMeleeLungeComplete()
	{
		Thread( this, n"AttackTargets", cachedTargets, FHitResult() );

		System::SetTimer( this, n"SwapToLastWeapon", 0.3, false );

		if ( !IsValid( myPlayer ) )
			return;

		PlayLungeImpactEffects();

		myPlayer.GetPlayerMesh3P().PlayNetMontage( EAnimMontageActivity::ACTM3P_CUSTOM_2 );
	}

	UFUNCTION()
	void PlayerLeapComplete()
	{
		System::SetTimer( this, n"SwapToLastWeapon", 0.3, false );

		if ( !IsValid( myPlayer ) )
			return;

		// We might have landed in front of some targets that are within melee range, blow 'em up
		if (IsServer())
		{
			TArray<AActor> leapEndtargets;
			AAS_WeaponClawTargetManager myTargetManager = GetMyTargetManager();
			if ( IsValid( myTargetManager ) )
				leapEndtargets = myTargetManager.FindTargets(EClawTargetMode::RANGED, myPlayer.GetActorLocation());

			Thread( this, n"AttackTargets", leapEndtargets, FHitResult());

			PlayLungeImpactEffects();

			myPlayer.GetPlayerMesh3P().PlayNetMontage( EAnimMontageActivity::ACTM3P_CUSTOM_2 );
		}

		// Likely temp script -- Play the lead attack animation if we didn't hit anything on the way
		if ( leapHitTargetCount == 0 )
		{
			cachedWeapon.PlayNetMontage(EAnimMontageActivity::ACTM_CUSTOM_1);
			lastLeapAttackAnimPlayTime = TO_SECONDS(myPlayer.GetGameTimeMS());
		}
	}


	int alternatingLeapCounter = 0;

	UFUNCTION()
	void AttackTargets( UNCCoroutine co, TArray<AActor> targets, FHitResult optionalHitResult )
	{
		co.EndOn( myPlayer, myPlayer.OnDeathSignal );

		TArray<AActor> hitTargets;

		if ( cachedLungeTargetMode == EClawTargetMode::MELEE )
			co.Wait( 0.1 ); // timing to damage for animation

		TArray<ANCPlayerCharacter> playerTargets;
		for ( auto target : targets )
		{
			if ( !IsValid( target ) )
				continue;

			// Don't damage the same target twice if it was duplicated in the targets list
			if ( hitTargets.Contains( target ) )
				continue;

			// Leap should never damage the same actor twice
			if ( cachedLungeTargetMode == EClawTargetMode::RANGED && leapDamagedActors.Contains( target ) )
				continue;

			hitTargets.Add( target );
			if ( cachedLungeTargetMode == EClawTargetMode::RANGED )
				leapDamagedActors.Add( target );

			DamageTarget( target );

			if ( target.IsA( AAS_PlayerEntity::StaticClass() ) )
				playerTargets.Add( Cast<ANCPlayerCharacter>( target ) );
		}

		if ( hitTargets.Num() > 0 )
		{
			if ( cachedLungeTargetMode == EClawTargetMode::RANGED )
			{
				if ( alternatingLeapCounter == 0 )
					cachedWeapon.PlayNetMontage( EAnimMontageActivity::ACTM_CUSTOM_1 );
				else if ( !IsEven( alternatingLeapCounter ) )
					cachedWeapon.PlayNetMontage( EAnimMontageActivity::ACTM_CUSTOM_2 );
				else if ( IsEven( alternatingLeapCounter )  )
					cachedWeapon.PlayNetMontage( EAnimMontageActivity::ACTM_CUSTOM_3 );
				lastLeapAttackAnimPlayTime = TO_SECONDS(myPlayer.GetGameTimeMS());
			}

			Server_EmitSoundOnEntity_1P3P( Abilities().clawNewGlobals.clawStrike1P, Abilities().clawNewGlobals.clawStrike3P, myPlayer, myPlayer );

			// audio for target hit
			bool didBreakThroughTarget		= true;
			bool didFailToBreakDestructible = false;
			for ( auto t : hitTargets )
			{
				if ( !IsValid( t ) )
					continue;

				UHealthComponent healthComponent = Cast<UHealthComponent>( t.GetComponentByClass( UHealthComponent::StaticClass() ) );
				if ( IsValid( healthComponent ) && healthComponent.GetHealth() > 0.0 )
				{
					didBreakThroughTarget = false;

					if ( t.IsA( ANCDestructible::StaticClass() ) || t.IsA( AAS_ScriptDoorBreakableObject::StaticClass() ) )
					{
						didFailToBreakDestructible = true;
					}
				}
			}

			if ( didBreakThroughTarget )
				myPlayer.PlayBattleChatter( GameplayTags::Audio_VO_BattleChatter_UsingAbility_Tactical );

			// todo: add battle chatter line for if the thing we hit needs an extra hit
			if ( didFailToBreakDestructible )
			{
				if ( cachedLungeTargetMode == EClawTargetMode::MELEE )
				{
					// Bounce in the opposite direction that we lerped
					FVector bounceDir		= lastLungeDir * -1.f;
					FVector biasedBounceDir = ( bounceDir + FVector::UpVector * ClawConsts::meleeBounceVerticalBias ).GetSafeNormal();

					myPlayer.StopLerpMovementOverlay();
					myPlayer.BouncePlayer( biasedBounceDir * ClawConsts::meleeBounceOffIronVelocity, 0 );
				}
				else
				{
					// Bounce opposite our current velocity
					FVector bounceDir		= (myPlayer.GetMovementComponentVelocity() * -1.f).GetSafeNormal();
					if (optionalHitResult.bBlockingHit)
					{
						bounceDir = (optionalHitResult.ImpactNormal + bounceDir).GetSafeNormal();
					}
					FVector biasedBounceDir = ( bounceDir + FVector::UpVector * ClawConsts::rangedBounceVerticalBias ).GetSafeNormal();

					myPlayer.BouncePlayer( biasedBounceDir * ClawConsts::rangedBounceOffIronVelocity, 0.25 );
				}
			}

			Server_EmitSoundOnEntity_WithSendFlags( Abilities().clawNewGlobals.clawImpact1pv3p, myPlayer, myPlayer, ESendEventFlags::SEND_TO_OWNER );
			Server_EmitSoundOnEntity_WithSendFlags( Abilities().clawNewGlobals.clawImpact3pv3p, myPlayer, myPlayer, ESendEventFlags::SEND_TO_FRIENDLIES | ESendEventFlags::SEND_TO_ENEMIES );
			for ( auto target : playerTargets )
				Server_EmitSoundOnEntity_WithSendFlags( Abilities().clawNewGlobals.clawImpact3pv1p, target, target, ESendEventFlags::SEND_TO_OWNER );

			alternatingLeapCounter++;
		}
	}

	UFUNCTION()
	void DamageTarget( AActor t )
	{
		if ( !IsServer() )
			return;

		if ( !IsValid( t ) )
			return;

		// Fake impact info for logic that needs it
		FVector start = cachedWeapon.GetWeaponOwner().GetPawnViewLocation();
		FVector cameraForward = cachedWeapon.GetWeaponOwner().GetEyeForward().GetSafeNormal();
		FVector end = start + cameraForward * ClawConsts::CLAW_MELEE_DISTANCE;
		FHitResult hit = HackGetImpact( start, end );
		if ( !IsValid( hit.GetPhysMaterial() ) )
		{
			// Backup sphere trace if forward trace doesn't hit
			TArray<AActor> ignoreActors;
			ignoreActors.Add( myPlayer );

			FVector actorCenter, actorExtents;
			t.GetActorBounds( true, actorCenter, actorExtents, false );
			actorCenter.Z = Math::Min(start.Z, actorCenter.Z);
			FVector newEnd = ( actorCenter + end ) / 2;

			hit = SphereTraceSingle(start, newEnd, ClawConsts::CLAW_MELEE_RADIUS * 1.2f, ETraceTypeQuery::WeaponFine, true, ignoreActors, true,
				EDrawDebugTrace::ForDuration, FLinearColor::Teal, FLinearColor::Red, 10 );
		}

		if ( IsValid( cachedWeapon ) && IsValid( hit.GetPhysMaterial() ) )
		{
			Server_PlayImpactTableAtPoint_WithSendFlags( cachedWeapon.GetEntityComponent(),
														 cachedWeapon.GetImpactTable(),
														 hit.ImpactPoint,
														 hit.ImpactNormal.Rotation(),
														 hit.GetPhysMaterial(),
														 myPlayer,
														 ESendEventFlags::SEND_TO_FRIENDLIES | ESendEventFlags::SEND_TO_ENEMIES,
														 EPlayerPOV::_3P );
			Server_PlayImpactTableAtPoint_WithSendFlags( cachedWeapon.GetEntityComponent(),
														 cachedWeapon.GetImpactTable(),
														 hit.ImpactPoint,
														 hit.ImpactNormal.Rotation(),
														 hit.GetPhysMaterial(),
														 myPlayer,
														 ESendEventFlags::SEND_TO_OWNER,
														 EPlayerPOV::_1P );
		}

		FDamageInfo info;
		info.attacker			   = myPlayer;
		info.traceHitResult		   = hit;
		info.damageFlags		   = EDamageFlags::DF_NONE;
		info.damageSourceLocation  = start;
		info.scriptDamageFlags	   = EScriptDamageFlags::DF_KNOCKBACK | EScriptDamageFlags::DF_RESOURCE;
		info.damageWeaponClassName = cachedWeapon.GetWeaponClass();

		UHealthComponent healthComponent	  = Cast<UHealthComponent>( t.GetComponentByClass( UHealthComponent::StaticClass() ) );
		AAS_ResourceNode resourceNodeToDamage = Cast<AAS_ResourceNode>( t );
		if ( IsValid( healthComponent ) )
			healthComponent.ReceiveDamage( info, IncomingDamage( ClawConsts::CLAW_PLAYER_DAMAGE, ClawConsts::CLAW_WALL_DAMAGE ) );
	}

	FHitResult HackGetImpact( FVector Start, FVector End )
	{
		FVector Dir = End - Start;
		Dir.Normalize();
		FVector cEnd = End + ( Dir * 10 );

		TArray<AActor> IgnoreActors;
		IgnoreActors.Add( myPlayer );

		FHitResult outHit = LineTraceSingle( Start, cEnd, ETraceTypeQuery::WeaponFine, true, IgnoreActors, true );
		return outHit;
	}

	UFUNCTION()
	void SwapToLastWeapon()
	{
		TeardownPlayerFromLunge();
		if ( cachedLungeTargetMode == EClawTargetMode::RANGED )
			cachedWeapon.PlayNetMontage( EAnimMontageActivity::ACTM_CUSTOM_2 );
		myPlayer.EquipLastPrimaryWeapon();
	}

	UFUNCTION()
	void OnPawnDied( const FDamageInfo&in damageInfo, ANCPlayerCharacter victim )
	{
		if ( !IsValid( myPlayer ) )
			return;

		TeardownPlayerFromLunge();
	}

	UFUNCTION()
	void SetupPlayerForLunge()
	{
		if ( isLunging )
			return;

		isLunging = true;

		myPlayer.Server_OnPawnDeath.AddUFunction( this, n"OnPawnDied" );

		myPlayer.ServerDisableWeaponSlotsByType( EWeaponDisableSlotType::DisableSlotType_PrimaryWeapons, n"Debug0" );
		myPlayer.ServerDisableWeaponSlotsByType( EWeaponDisableSlotType::DisableSlotType_MeleeWeapons, n"Debug1" );
		myPlayer.ServerDisableWeaponSlotsByType( EWeaponDisableSlotType::DisableSlotType_SpecialWeapons, n"Debug2" );
		myPlayer.ServerDisableWeaponSlotsByType( EWeaponDisableSlotType::DisableSlotType_MainWeapons, n"Debug3" );
		myPlayer.ServerDisableWeaponSlotsByType( EWeaponDisableSlotType::DisableSlotType_OffhandWeapons, n"Debug4" );
	}

	UFUNCTION()
	void TeardownPlayerFromLunge()
	{
		if ( !isLunging )
			return;

		isLunging = false;
		alternatingLeapCounter = 0;

		System::ClearAndInvalidateTimerHandle( meleeLungeTimer );

		myPlayer.SetMovementMode( EMovementMode::MOVE_Falling );
		myPlayer.ServerEnableMovement();
		myPlayer.OnLeapEnded.Unbind( this, n"HandleLeapEnded" );
		myPlayer.Server_OnPawnDeath.Unbind( this, n"OnPawnDied" );

		myPlayer.ServerEnableWeaponSlotsByType( EWeaponDisableSlotType::DisableSlotType_PrimaryWeapons, n"Debug0" );
		myPlayer.ServerEnableWeaponSlotsByType( EWeaponDisableSlotType::DisableSlotType_MeleeWeapons, n"Debug1" );
		myPlayer.ServerEnableWeaponSlotsByType( EWeaponDisableSlotType::DisableSlotType_SpecialWeapons, n"Debug2" );
		myPlayer.ServerEnableWeaponSlotsByType( EWeaponDisableSlotType::DisableSlotType_MainWeapons, n"Debug3" );
		myPlayer.ServerEnableWeaponSlotsByType( EWeaponDisableSlotType::DisableSlotType_OffhandWeapons, n"Debug4" );
	}

	// todo: hookup impact tables

	// stretch:
	// todo: ping OT about showing/hiding ranged arc
}

UCLASS()
class AAS_WeaponClawTargetManager : ANCDefaultActor
{
	UPROPERTY( DefaultComponent, RootComponent )
	USceneComponent root;

	UPROPERTY()
	FNCNetBool hasMeleeTarget;
	UUserWidget meleeTargetWidget;

	UAS_Claw_TargettingThread meleeTargetThread;
	UAS_Claw_TargettingThread rangedTargetThread;

	TArray<UClass> targetClassesToSearchFor;
	default targetClassesToSearchFor.Add( AAS_ScriptDoorObject::StaticClass() );
	default targetClassesToSearchFor.Add( AAS_PlayerEntity::StaticClass() );
	default targetClassesToSearchFor.Add( ANCResourceBase::StaticClass() );
	default targetClassesToSearchFor.Add( AAS_BreakableObject::StaticClass() );
	default targetClassesToSearchFor.Add( AAS_DestructibleItem::StaticClass() );
	default targetClassesToSearchFor.Add( ANCDestructible::StaticClass() );
	default targetClassesToSearchFor.Add( ANCDestructibleProp::StaticClass() );
	default targetClassesToSearchFor.Add( AAS_Dev_MovingTarget::StaticClass() );

	private EClawTargetMode activeTargetingMode;

	UFUNCTION( BlueprintOverride )
	void ClientBeginPlay()
	{
		OnHasShortRangeTargetChanged( false, false );
		hasMeleeTarget.OnReplicated().AddUFunction( this, n"OnHasShortRangeTargetChanged" );

		if (Client_GetLocalPawn() == GetOwnerPlayer())
		{
			auto passive = Passive_Enraged();
			check(passive != nullptr);
			passive.Client_RegisterTargetManager(GetOwnerPlayer(), this);

			ScriptCallbacks().onPlayerWeaponSlotEnabledChanged.AddUFunction( this, n"OnAnyWeaponSlotEnabledStateChanged" );
		}
	}


	void StartTargeting(EClawTargetMode targetMode)
	{
		if (activeTargetingMode != targetMode)
		{
			if (activeTargetingMode == EClawTargetMode::MELEE)
				StopMeleeTargeting();
			else if (activeTargetingMode == EClawTargetMode::RANGED)
				StopRangedTargeting();

			if (targetMode == EClawTargetMode::MELEE)
				SetupMeleeTargetThread(GetOwnerPlayer());
			else if (targetMode == EClawTargetMode::RANGED)
				SetupRangedTargetThread(GetOwnerPlayer());

			activeTargetingMode = targetMode;
		}
	}

	void StopTargeting()
	{
		StartTargeting(EClawTargetMode::NONE);
	}


	EClawTargetMode GetActiveTargetMode() const
	{
		return activeTargetingMode;
	}

	bool HasMeleeTargets()
	{
		return activeTargetingMode == EClawTargetMode::MELEE && meleeTargetThread.cachedTargets.Num() > 0;
	}

	TArray<AActor> GetCachedMeleeTargets() const
	{
		if (activeTargetingMode == EClawTargetMode::MELEE)
		{
			return meleeTargetThread.cachedTargets;
		}

		return TArray<AActor>();
	}


	TArray<AActor> FindTargets( EClawTargetMode targetMode, const FVector& searchLocation )
	{
		// NOTE -- The intent of this function is to let the calling claw behavior get an immediate list of
		// targets at any time, currently just used to find some extra things to smash after a leap. Ideally
		// this wouldn't have to dig into an active thread, or need an active thread at all. To do that, the
		// targeting logic should get pulled out to a place where both this and the threads can use it.

		if (targetMode == activeTargetingMode && targetMode != EClawTargetMode::NONE)
		{
			UAS_Claw_TargettingThread thread = targetMode == EClawTargetMode::MELEE ? meleeTargetThread : rangedTargetThread;
			return thread.TargetTracking(targetMode, searchLocation, true );
		}

		return TArray<AActor>();
	}



	private void StopMeleeTargeting()
	{
		if ( IsValid( meleeTargetThread ) && meleeTargetThread.IsRunning() )
			meleeTargetThread.Cancel();

		hasMeleeTarget.SetNetValue(false);
	}

	private void StopRangedTargeting()
	{
		if ( IsValid( rangedTargetThread ) && rangedTargetThread.IsRunning() )
			rangedTargetThread.Cancel();
	}

	private void SetupMeleeTargetThread( AAS_PlayerEntity p )
	{
		if ( IsValid( meleeTargetThread ) && meleeTargetThread.IsRunning() )
			meleeTargetThread.Cancel();
		meleeTargetThread = Cast<UAS_Claw_TargettingThread>( CreateThread( UAS_Claw_TargettingThread::StaticClass(), this ) );
		meleeTargetThread.Init( p, this, targetClassesToSearchFor, EClawTargetMode::MELEE, true );
	}

	private void SetupRangedTargetThread( AAS_PlayerEntity p )
	{
		if ( IsValid( rangedTargetThread ) && rangedTargetThread.IsRunning() )
			rangedTargetThread.Cancel();
		rangedTargetThread = Cast<UAS_Claw_TargettingThread>( CreateThread( UAS_Claw_TargettingThread::StaticClass(), this ) );
		rangedTargetThread.Init( p, this, targetClassesToSearchFor, EClawTargetMode::RANGED );
	}

	UFUNCTION()
	void SetHasMeleeTarget( bool t )
	{
		hasMeleeTarget.SetNetValue( t );
	}

	UFUNCTION()
	bool IsValidTargetType( AActor target )
	{
		if ( !IsValid( target ) )
		{
			return false;
		}

		for ( UClass validClass : targetClassesToSearchFor )
		{
			if ( target.IsA( validClass ) )
			{
				return true;
			}
		}

		return false;
	}

	bool hasShortRangeMeleeTarget = false;
	UFUNCTION()
	void OnHasShortRangeTargetChanged( bool old, bool new )
	{
		if ( GetOwnerPlayerIndex() != Client_GetLocalPawn().GetOwnerPlayerIndex() )
			return;

		hasShortRangeMeleeTarget = new;

		UpdateShortRangeTargetHint();
	}

	UFUNCTION()
	void OnAnyWeaponSlotEnabledStateChanged( AAS_PlayerEntity asPlayer, int weaponSlot, int oldValue, int newvalue )
	{
		if ( weaponSlot != WeaponSlot::TacticalSlot )
		{
			return;
		}

		UpdateShortRangeTargetHint();
	}

	UFUNCTION()
	void UpdateShortRangeTargetHint()
	{
		ANCPlayerCharacter localPawn = Client_GetLocalPawn();
		if ( !IsValid( localPawn ) )
		{
			return;
		}

		bool shouldShow = hasShortRangeMeleeTarget && localPawn.WeaponSlotIsEnabled( WeaponSlot::TacticalSlot );

		if ( shouldShow && IsValid( GetLocalHUD() ) )
		{
			meleeTargetWidget = WidgetBlueprint::CreateWidget( Abilities().clawGlobals.shortClawSwipeWidgetClass, GetLocalHUD().GetOwningPlayerController() );
			GetLocalHUD().mainHUDWidget.alivePanel.AddChild( meleeTargetWidget );

			meleeTargetWidget.SetPadding( FMargin( 0, 0, 150, 0 ) );

			FAnchors anchors;
            anchors.Minimum = FVector2D( 0.5, 0.5 );
            anchors.Maximum = FVector2D( 0.5, 0.5 );

			UCanvasPanelSlot slot = Cast<UCanvasPanelSlot>( meleeTargetWidget.Slot );
            slot.SetAlignment( FVector2D( 1.0, 0.5 ) );
            slot.SetAnchors( anchors );
		}
		else if ( IsValid( meleeTargetWidget ) )
		{
			if ( IsValid( meleeTargetWidget ) )
				meleeTargetWidget.RemoveFromParent();
		}
	}

	UFUNCTION( BlueprintOverride )
	void EndPlay( EEndPlayReason EndPlayReason )
	{
		if ( IsValid( meleeTargetWidget ) )
			meleeTargetWidget.RemoveFromParent();
	}
}

UCLASS(Abstract)
class UAS_ClawSwipeHintWidget : UUserWidget
{
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UImage swipe;

	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UImage swipeShadow;
	
	UFUNCTION(BlueprintOverride)
	void Construct()
	{
		FLinearColor swipeColor; FLinearColor swipeShadowColor;
		GetSafeColor( n"white", swipeColor );
		GetSafeColor( n"redmane_theme", swipeShadowColor );

		swipe.SetBrushTintColor( FSlateColor( swipeColor ) );
		swipeShadow.SetBrushTintColor( FSlateColor( swipeShadowColor ) );
	}
}

UCLASS()
class UAS_Claw_TargettingThread : UAS_Thread
{
	AAS_PlayerEntity myPlayer;
	AAS_WeaponClawTargetManager myTargetManager;
	TArray<UClass> targetClasses;
	EClawTargetMode targetMode;

	bool isCharacterSpawnSetup;

	void Init( AAS_PlayerEntity p, AAS_WeaponClawTargetManager t, TArray<UClass> tC, EClawTargetMode tM, bool iCS = false )
	{
		myPlayer			  = p;
		myTargetManager		  = t;
		targetClasses		  = tC;
		targetMode			  = tM;
		isCharacterSpawnSetup = iCS;
		Start();
	}

	FHitResult cachedMeleeHit;
	TArray<AActor> cachedTargets;

	void OnThreadStart( UNCCoroutine co ) override
	{
		Super::OnThreadStart( co );

		// this is annoying but we need it to wait a bit while the player initializes
		while ( !IsValid( myPlayer ) && !IsValid( myTargetManager ) )
			co.Wait( 0.5 );

		co.EndOnDestroyed( myPlayer );
		co.EndOnDestroyed( myTargetManager );

		while ( true )
		{
			co.Wait( 0.1 );

			cachedMeleeHit = FHitResult();
			cachedTargets.Empty();

			TArray<AActor> foundTargets;
			switch ( targetMode )
			{
				case EClawTargetMode::MELEE:
				{
					if ( IsValid( myTargetManager ) && IsValid( myTargetManager.rangedTargetThread ) && myTargetManager.rangedTargetThread.IsRunning() )
					{
						// Print( f"Skipping melee targetting" );
						continue;
					}
					foundTargets = TargetTracking( EClawTargetMode::MELEE, myPlayer.GetPawnViewLocation() );
					break;
				}
				case EClawTargetMode::RANGED:
				{
					FArcIndicatorData data = myPlayer.GetActiveWeapon().SimulateProjectilePath();
					if ( data.Path.Num() != 0 )
					{
						int bestIndex = data.Path.Num() - 1 - ( data.Path.Num() / 5 );
						foundTargets  = TargetTracking( EClawTargetMode::RANGED, data.Path[bestIndex] + FVector( 0, 0, myPlayer.CapsuleComponent.CapsuleHalfHeight ) );
					}
					break;
				}

				default:
					break;
			}
			cachedTargets.Append( foundTargets );
		}
	}

	void OnThreadEnd( FNCCoroutineEndParams params ) override
	{
		Super::OnThreadEnd( params );
	}

	TArray<AActor> TargetTracking( EClawTargetMode mode, FVector start, bool shouldFindBackupTargets = false )
	{
		TArray<AActor> foundTargets;
		if ( !IsValid( myTargetManager ) )
			return foundTargets;

		AActor foundTarget;
		ANCMeleeWeapon meleeWeapon = Cast<ANCMeleeWeapon>( myPlayer.GetWeaponAtSlot( WeaponSlot::MeleeSlot ) );
		if ( IsValid( meleeWeapon ) )
		{
			FMeleeReticleTargetData targetData = CodeMeleeUtil::GetMeleeReticleTargetData( meleeWeapon );
			if ( IsValid( targetData.target ) )
			{
				AActor potentialTarget = targetData.target;
				for ( auto c : targetClasses )
				{
					if ( !potentialTarget.IsA( c ) )
						continue;
					UHealthComponent healthComp = potentialTarget.GetHealthComponent();
					if ( !IsValid( healthComp ) )
						continue;
					if ( healthComp.Health <= 0 )
						continue;

					bool isFriendly		 = false;
					bool shouldCheckTeam = potentialTarget.CanHaveTeam() && potentialTarget.IsA( AAS_PlayerEntity::StaticClass() );

					if ( shouldCheckTeam && !GetCvarBool( f"ScriptDebug.DebugRaid" ) )
						isFriendly = potentialTarget.GetTeam() == myPlayer.GetTeam();
					if ( !isFriendly )
					{
						foundTarget = potentialTarget;
						break;
					}
				}
			}
		}

		ANCWeapon testWeapon = myPlayer.GetWeaponAtSlot( EWeaponSlot::TacticalSlot );
		int ammoRemaining	 = 0;
		if ( IsValid( testWeapon ) )
			ammoRemaining = testWeapon.GetClipAmmo();

		bool isValidTarget = IsValid( foundTarget );

		bool busyMelee = myPlayer.Passives().HasPassive( GameplayTags::Classes_Passives_BusyMeleeClash ) ||
						 myPlayer.Passives().HasPassive( GameplayTags::Classes_Passives_BusyMeleeExecution );

		if ( busyMelee )
			myTargetManager.SetHasMeleeTarget( false );
		else if ( myPlayer.IsADS() )
			myTargetManager.SetHasMeleeTarget( false );
		else if ( ammoRemaining > 0 && mode == EClawTargetMode::MELEE )
			myTargetManager.SetHasMeleeTarget( isValidTarget );
		else
			myTargetManager.SetHasMeleeTarget( false );
		if ( !isValidTarget )
			return foundTargets;

		if ( shouldFindBackupTargets )
		{
			foundTargets = TryFindTargets( myPlayer, targetClasses, start, mode == EClawTargetMode::MELEE ? ClawConsts::CLAW_MELEE_FOV : ClawConsts::CLAW_MELEE_FOV * 2 );
			/*for( auto target : foundTargets )
				DrawDebugSphere( target.GetActorLocation(), 10, 0.1, mode == EClawTargetMode::MELEE ? FLinearColor::Green : FLinearColor::LucBlue, 1.0, true );*/
		}

		if ( !foundTargets.Contains( foundTarget ) )
			foundTargets.Add( foundTarget );

		return foundTargets;
	}
}

UFUNCTION()
TArray<AActor> TryFindTargets( AAS_PlayerEntity myPlayer, TArray<UClass> targetClasses, FVector startLocation, float32 FOV )
{
	TArray<AActor> ignoreActors;
	ignoreActors.Add( myPlayer );

	FVector staggeredStart = startLocation - ( myPlayer.GetActorForwardVector() * 100 );

	FNCFindTargetParameters params;
	params					  = MakeFindTargetParameters( staggeredStart,
										  myPlayer.GetActorForwardVector(),
										  ClawConsts::CLAW_MELEE_DISTANCE * 3.0,
										  FOV,
										  targetClasses,
										  ignoreActors,
										  ENCTargetSortStyle::Angle );
	params.canReturnResources = true;
	params.ownerTeam		  = myPlayer.GetTeam();
	params.onlyTargetEnemies  = false;

	TArray<FNCFoundTargetData> foundTargets = GetTargetsInRangeWithFOV( params );
	TArray<AActor> validTargets;
	for ( FNCFoundTargetData data : foundTargets )
	{
		if ( data.actor.CanHaveTeam() && !data.actor.GetClass().IsChildOf( ANCDestructible::StaticClass() ) )
		{
			if ( data.actor.GetTeam() == myPlayer.GetTeam() )
				continue;
		}

		if ( data.actor.GetClass().IsChildOf( AAS_PlayerEntity::StaticClass() ) )
		{
			AAS_PlayerEntity fPlayer = Cast<AAS_PlayerEntity>( data.actor );
			if ( fPlayer.GetTeam() == myPlayer.GetTeam() )
				continue; // don't target friendlies
			validTargets.Add( data.actor );
		}
		else if ( data.actor.GetClass().IsChildOf( ANCDestructible::StaticClass() ) )
		{
			ANCDestructible d = Cast<ANCDestructible>( data.actor );
			if ( d.GetTeamID() != myPlayer.GetTeam() || d.GetDestructibleCategory().DestructibleCategoryID.MatchesTag( GameplayTags::Destruction_Category_Ice ) )
				validTargets.Add( data.actor );
		}

		else
			validTargets.Add( data.actor );
	}

	return validTargets;
}
