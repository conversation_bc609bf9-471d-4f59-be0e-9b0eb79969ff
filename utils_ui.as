void SetWidgetVisibilitySafe( UWidget widget, ESlateVisibility visibility )
{
	if ( !IsValid( widget ) )
	{
		Log( f"SetWidgetVisibilitySafe failed to set visibility to {visibility}, widget was null." );
		return;
	}

	UNC_DisplayWidget displayWidget = Cast<UNC_DisplayWidget>( widget );

	if ( displayWidget != nullptr )
	{
		if ( visibility == ESlateVisibility::Visible || visibility == ESlateVisibility::HitTestInvisible || visibility == ESlateVisibility::SelfHitTestInvisible )
		{
			displayWidget.Show();
		}
		else if ( visibility == ESlateVisibility::Collapsed || visibility == ESlateVisibility::Hidden )
		{
			displayWidget.Hide();
		}
		else
		{
			Log( f"SetWidgetVisibilitySafe received unknown visibility for display widget" );
			widget.SetVisibility( visibility );
		}
	}
	else
	{
		widget.SetVisibility( visibility );
	}
}

AHUD GetHUD()
{
	AHUD result = nullptr;

	if ( !IsServer() )
	{
		UCL_ScriptCallbacks clientCallbacks = ClientCallbacks();
		if ( IsValid( clientCallbacks ) )
		{
			ANCPlayerController localController = clientCallbacks.LocalClientController;
			if ( IsValid( localController ) )
				result = localController.GetHUD();
		}
	}

	return result;
}

AAS_HUD GetLocalHUD()
{
	return Cast<AAS_HUD>( GetHUD() );
}

AAS_BaseHUD GetLocalBaseHUD()
{
	return Cast<AAS_BaseHUD>( GetHUD() );
}

UAS_UIManager GetASUIManager()
{
	UAS_UIManager result;

	AAS_BaseHUD hud = GetLocalBaseHUD();
	if ( IsValid( hud ) )
	{
		result = hud.GetASUIManager();
	}

	return result;
}

UAS_MenuWidget OpenMenu( FName menuName, bool closeOtherMenus = false, bool onlyOpenIfNoOtherMenusAreOpen = false, bool showPreviousMenu = false )
{
	UAS_MenuWidget menu;

	UAS_UIManager uiManager = GetASUIManager();
	if ( IsValid( uiManager ) )
	{
		menu = uiManager.OpenMenu( menuName, closeOtherMenus, onlyOpenIfNoOtherMenusAreOpen, showPreviousMenu );
	}

	return menu;
}

UNCScreenWidget OpenScreenClass( TSubclassOf<UNCScreenWidget> screenClass, bool closeOtherMenus = false, bool onlyOpenIfNoOtherMenusAreOpen = false, bool showPreviousMenu = false )
{
	UNCScreenWidget screen;

	UAS_UIManager uiManager = GetASUIManager();
	if ( IsValid( uiManager ) )
	{
		screen = uiManager.OpenScreenClass( screenClass, closeOtherMenus, onlyOpenIfNoOtherMenusAreOpen, showPreviousMenu );
	}

	return screen;
}

bool TryToQuit( UObject object, FName functionName, bool closeOtherMenus = false, bool onlyOpenIfNoOtherMenusAreOpen = false, bool showPreviousMenu = false )
{
	if ( !IsValid( object ) || functionName.IsNone() )
		return false;

	// TODO @jmccarty: These FTexts need to be built using a localization system
	// TODO @jmccarty: We should look at creating popup data assets that can be referenced in instances where we want to show the same popup
	// data, for example quit will always be quit but we are currently calling this pop up from three different places
	FYesNoPopupData quitPopup;
	quitPopup.titleText				  = GetLocalizedText( Localization::SystemMessages, "quit_confirmation_message" );
	quitPopup.bodyText				  = Text::EmptyText;
	quitPopup.noText				  = GetLocalizedText( Localization::SystemMessages, "menu_no" );
	quitPopup.yesText				  = GetLocalizedText( Localization::SystemMessages, "menu_yes" );
	quitPopup.executeNoOnNavigateBack = false;
	quitPopup.onYes.BindUFunction( object, functionName );

	UAS_GenericYesNoPopup popup = Cast<UAS_GenericYesNoPopup>( OpenMenu( n"GenericYesNoPopup", closeOtherMenus, onlyOpenIfNoOtherMenusAreOpen, showPreviousMenu ) );
	if ( IsValid( popup ) )
	{
		popup.Init( quitPopup );
		return true;
	}

	return false;
}

// TODO @jmccarty: Investigating NC1-8688
UAS_ClassSystem GetClassSystem( UObject source = nullptr )
{
	UAS_ClassSystem result = Classes();

	if ( !IsValid( result ) )
	{
		FString logString = "Tried to get UAS_ClassSystem but failed.";

		if ( IsValid( source ) )
		{
			logString = f"{source.Name} tried to get UAS_ClassSystem but failed.";
		}

		Print( f"NC1-8688 - {logString}" );
	}

	return result;
}

void QuitGame( APlayerController playerController, bool savePersistence = false )
{
	if ( savePersistence )
	{
		SaveClientPersistence();
	}

	UNCUIConnectionManager connectionManager = GetConnectionManager();
	if ( IsValid( connectionManager ) )
	{
		connectionManager.Disconnect();
	}

	System::QuitGame( playerController, EQuitPreference::Quit, false );
}

UNCUserWidget CreateAndAddWidgetToViewport( TSubclassOf<UNCUserWidget> widgetClass, int zOrder = 0 )
{
	UNCUserWidget result;

	if ( IsValid( widgetClass ) )
	{
		result = Cast<UNCUserWidget>( WidgetBlueprint::CreateWidget( widgetClass, Client_GetLocalPlayerController() ) );
		if ( IsValid( result ) )
		{
			result.AddToViewport( zOrder );
		}
	}

	return result;
}

FLinearColor GetRarityColor( FGameplayTag rarity )
{
	FLinearColor outResult;

	const UAS_HudGlobals hudGlobals = GetHUDGlobals();
	if ( IsValid( hudGlobals ) )
	{
		FName colorId = NAME_None;

		if ( rarity == GameplayTags::Loot_Rarity_Uncommon )
		{
			colorId = GameConst::RARITY_UNCOMMON;
		}
		else if ( rarity == GameplayTags::Loot_Rarity_Rare )
		{
			colorId = GameConst::RARITY_RARE;
		}
		else if ( rarity == GameplayTags::Loot_Rarity_Extraordinary )
		{
			colorId = GameConst::RARITY_EXTRAORDINARY;
		}
		else if ( rarity == GameplayTags::Loot_Rarity_Artifact )
		{
			colorId = GameConst::RARITY_ARTIFACT;
		}
		else if ( rarity == GameplayTags::Loot_Rarity_Common || rarity == GameplayTags::Loot_Rarity_Basic )
		{
			colorId = GameConst::RARITY_COMMON;
		}

		hudGlobals.GetSafeColor( colorId, outResult );
	}

	return outResult;
}

FLinearColor GetRarityHighlightColor( FGameplayTag rarity )
{
	FLinearColor outResult;

	const UAS_HudGlobals hudGlobals = GetHUDGlobals();
	if ( IsValid( hudGlobals ) )
	{
		FName colorId = NAME_None;

		if ( rarity == GameplayTags::Loot_Rarity_Uncommon )
		{
			colorId = GameConst::RARITY_UNCOMMON_HIGHLIGHT;
		}
		else if ( rarity == GameplayTags::Loot_Rarity_Rare )
		{
			colorId = GameConst::RARITY_RARE_HIGHLIGHT;
		}
		else if ( rarity == GameplayTags::Loot_Rarity_Extraordinary )
		{
			colorId = GameConst::RARITY_EXTRAORDINARY_HIGHLIGHT;
		}
		else if ( rarity == GameplayTags::Loot_Rarity_Artifact )
		{
			colorId = GameConst::RARITY_ARTIFACT_HIGHLIGHT;
		}
		else if ( rarity == GameplayTags::Loot_Rarity_Common || rarity == GameplayTags::Loot_Rarity_Basic )
		{
			colorId = GameConst::RARITY_COMMON_HIGHLIGHT;
		}

		hudGlobals.GetSafeColor( colorId, outResult );
	}

	return outResult;
}

FLinearColor GetRarityShieldBreakColor( FGameplayTag rarity )
{
	FLinearColor outResult;

	const UAS_HudGlobals hudGlobals = GetHUDGlobals();
	if ( IsValid( hudGlobals ) )
	{
		FName colorId = NAME_None;

		if ( rarity == GameplayTags::Loot_Rarity_Common || rarity == GameplayTags::Loot_Rarity_Basic )
		{
			colorId = GameConst::RARITY_COMMON_SHIELDBREAK;
		}
		else if ( rarity == GameplayTags::Loot_Rarity_Uncommon )
		{
			colorId = GameConst::RARITY_UNCOMMON_SHIELDBREAK;
		}
		else if ( rarity == GameplayTags::Loot_Rarity_Rare )
		{
			colorId = GameConst::RARITY_RARE_SHIELDBREAK;
		}
		else if ( rarity == GameplayTags::Loot_Rarity_Extraordinary )
		{
			colorId = GameConst::RARITY_EXTRAORDINARY_SHIELDBREAK;
		}

		hudGlobals.GetSafeColor( colorId, outResult );
	}

	return outResult;
}

FText GetRarityText( FGameplayTag rarity )
{
	FName rarityId;

	if ( rarity == GameplayTags::Loot_Rarity_Uncommon )
	{
		rarityId = GameConst::RARITY_UNCOMMON;
	}
	else if ( rarity == GameplayTags::Loot_Rarity_Rare )
	{
		rarityId = GameConst::RARITY_RARE;
	}
	else if ( rarity == GameplayTags::Loot_Rarity_Extraordinary )
	{
		rarityId = GameConst::RARITY_EXTRAORDINARY;
	}
	else if ( rarity == GameplayTags::Loot_Rarity_Common || rarity == GameplayTags::Loot_Rarity_Basic )
	{
		rarityId = GameConst::RARITY_COMMON;
	}

	return FText::FromStringTable( Localization::Core, rarityId.ToString() );
}

FText GetLootTypeText( ELootType lootType )
{
	FName locId;

	switch ( lootType )
	{
		case ELootType::PrimaryWeapon:
		{
			locId = GameConst::LOC_LOOTTYPE_PRIMARYWEAPON;
			break;
		}
		case ELootType::Ammo:
		{
			locId = GameConst::LOC_LOOTTYPE_AMMO;
			break;
		}
		case ELootType::PlaceableItem:
		{
			locId = GameConst::LOC_LOOTTYPE_PLACEABLEITEM;
			break;
		}
		case ELootType::ClassPlaceableItem:
		{
			locId = GameConst::LOC_LOOTTYPE_CLASSPLACEABLEITEM;
			break;
		}
		case ELootType::GenericInventoryItem:
		{
			locId = GameConst::LOC_LOOTTYPE_GENERICINVENTORYITEM;
			break;
		}
		case ELootType::Grenade:
		{
			locId = GameConst::LOC_LOOTTYPE_GRENADE;
			break;
		}
		case ELootType::HealItem:
		{
			locId = GameConst::LOC_LOOTTYPE_HEALITEM;
			break;
		}
		case ELootType::PlayerShield:
		{
			locId = GameConst::LOC_LOOTTYPE_PLAYERSHIELD;
			break;
		}
		case ELootType::ResourceItem:
		{
			locId = GameConst::LOC_LOOTTYPE_RESOURCEITEM;
			break;
		}
		case ELootType::ShieldBreacher:
		{
			locId = GameConst::LOC_LOOTTYPE_SHIELDBREACHER;
			break;
		}
			// TODO dbocek -- loc equipment

		default:
		{
			break;
		}
	}

	return FText::FromStringTable( Localization::Core, locId.ToString() );
}

FText GetUpradedWeaponText( FLootDataStruct weaponLootData, FGameplayTag attachmentTag )
{
	FText locEntry						 = GameConst::FTEXT_EMPTY;
	FWeaponAttachmentData attachmentData = GetAttachmentData( attachmentTag );
	if ( attachmentData.weaponDescOverride.Contains( weaponLootData.referencedItemIndex ) )
	{
		locEntry = attachmentData.weaponDescOverride[weaponLootData.referencedItemIndex];
	}
	else
	{
		locEntry = attachmentData.desc;
	}

	return locEntry;
}

FText GetWeaponAttachmentDescText( FLootDataStruct weaponLootData )
{
	FText outText								   = GameConst::FTEXT_EMPTY;
	TOptional<FWeaponAttachmentData> optAttachData = TryGetNamedAttachmentData( weaponLootData );

	if ( optAttachData.IsSet() )
	{
		FGameplayTag attachTag			 = optAttachData.GetValue().attachmentIndex;
		FWeaponAttachmentData attachData = GetAttachmentData( attachTag );
		if ( attachData.weaponDescOverride.Contains( weaponLootData.referencedItemIndex ) )
		{
			outText = attachData.weaponDescOverride[weaponLootData.referencedItemIndex];
		}
		else
		{
			outText = attachData.desc;
		}
	}

	return outText;
}

TOptional<FWeaponAttachmentData> TryGetNamedAttachmentData( FLootDataStruct weaponLootData )
{
	TOptional<FWeaponAttachmentData> outData;
	FGameplayTag attachTagToUse;
	for ( FGameplayTag attachTag : weaponLootData.weaponMods )
	{
		if ( attachTag == WeaponConst::LEVEL2_MOD || attachTag == WeaponConst::LEVEL3_MOD )
			continue;

		attachTagToUse = attachTag;
		break;
	}

	if ( IsValidAttachmentIndex( attachTagToUse ) )
	{
		outData = GetAttachmentData( attachTagToUse );
	}

	return outData;
}

TOptional<FWeaponAttachmentData> TryGetNamedAttachmentData( UWeaponPrimaryAsset weaponClass, int modBitfield )
{
	return TryGetNamedAttachmentData( GetLootDataForWeapon( MakeWeaponId( weaponClass, modBitfield ) ) );
}

TOptional<FWeaponAttachmentData> TryGetNamedAttachmentData( const FNCWeaponPermutationId weaponId )
{
	return TryGetNamedAttachmentData( GetLootDataForWeapon( weaponId ) );
}

UObject GetWeaponAttachmentIcon( FLootDataStruct weaponLootData )
{
	TOptional<FWeaponAttachmentData> optAttachData = TryGetNamedAttachmentData( weaponLootData );
	if ( optAttachData.IsSet() )
	{
		return optAttachData.Value.icon;
	}
	return nullptr;
}

// TODO @jmccarty: Deprecate this when EEquipmentRarity is deprecated
FGameplayTag ConvertRarity( EEquipmentRarity rarity )
{
	FGameplayTag result;

	if ( rarity == EEquipmentRarity::Common )
	{
		result = GameplayTags::Loot_Rarity_Common;
	}
	else if ( rarity == EEquipmentRarity::Uncommon )
	{
		result = GameplayTags::Loot_Rarity_Uncommon;
	}
	else if ( rarity == EEquipmentRarity::Rare )
	{
		result = GameplayTags::Loot_Rarity_Rare;
	}
	else if ( rarity == EEquipmentRarity::Legendary )
	{
		result = GameplayTags::Loot_Rarity_Extraordinary;
	}

	return result;
}

bool GetMtxCategoryName( EMTXAssetType assetType, FText& outResult, bool plural = true )
{
	UAS_CollectionNavigator collectionNavigator = GetCollectionNavigator();
	if ( IsValid( collectionNavigator ) )
	{
		outResult = collectionNavigator.GetCategoryName( assetType, plural );
	}

	return !outResult.IsEmpty();
}

// JoinTextWithSpace is not localization friendly, if you are concatinating two player facing pieces of text use a unique string table entry
FText JoinTextWithSpace( FText textA, FText textB )
{
	TArray<FText> args;
	args.Add( textA );
	args.Add( textB );
	return FText::Join( Localization::SPACE_DELIMITER, args );
}

FText GetMtxCategoryAndItemNameText( EMTXAssetType assetType, FGameplayTag rarityId )
{
	FText result;
	FText outCategory;

	if ( GetMtxCategoryName( assetType, outCategory, false ) )
	{
		result = GetLocalizedText( Localization::Core, "rarity_format", FFormatArgumentValue( GetRarityText( rarityId ) ), FFormatArgumentValue( outCategory ) );
	}

	return result;
}

FText GetKeybindWithText( FName inputAction, FText displayText )
{
	FText result;

	UCL_ScriptCallbacks clientCallbacks = ClientCallbacks();
	if ( IsValid( clientCallbacks ) )
	{
		ANCPlayerController localController	 = clientCallbacks.LocalClientController;
		UCommonInputSubsystem inputSubsystem = UCommonInputSubsystem::Get( localController.LocalPlayer );
		if ( IsValid( inputSubsystem ) )
		{
			bool isUsingController = inputSubsystem.CurrentInputType == ECommonInputType::Gamepad;

			UInputSettings inputSettings = UInputSettings::GetInputSettings();
			if ( IsValid( inputSettings ) )
			{
				TArray<FInputActionKeyMapping> actionMappings = inputSettings.GetActionMappings();
				for ( int i = 0; i < actionMappings.Num(); i++ )
				{
					FInputActionKeyMapping action = actionMappings[i];
					if ( action.ActionName == inputAction && ( ( isUsingController && action.Key.IsGamepadKey() ) || ( !isUsingController && !action.Key.IsGamepadKey() ) ) )
					{
						UAS_GlyphSystem glyphSystem = GetGlyphSystem();
						if ( IsValid( glyphSystem ) )
						{
							FString inputGlyph = glyphSystem.GetGlyph( action.Key );
							if ( !inputGlyph.IsEmpty() )
							{
								// Loc note, this is a fine use case of FromString since glyphs aren't localized
								result = JoinTextWithSpace( Localization::GetUnlocalizedTextFromString( inputGlyph ), displayText );
								break;
							}
						}
					}
				}
			}
		}
	}

	return result;
}

// TODO @jmccarty @robin: Do this with a better sort method in code
void SortMtxItems( TArray<UNCUIMTXItem>& items, ESortMode sortMode = ESortMode::OWNED_ASCENDING )
{
	int n = items.Num();
	for ( int i = 0; i < n - 1; ++i )
	{
		for ( int j = 0; j < n - i - 1; ++j )
		{
			bool sortResult	   = false;
			UNCUIMTXItem itemA = items[j];
			UNCUIMTXItem itemB = items[j + 1];

			switch ( sortMode )
			{
				case ESortMode::RARITY_ASCENDING:
					sortResult = CompareRarity( itemA, itemB, true );
					break;
				case ESortMode::RARITY_DESCENDING:
					sortResult = CompareRarity( itemA, itemB, false );
					break;
				case ESortMode::ALPHABETICAL_ASCENDING:
					sortResult = CompareDisplayNames( itemA, itemB, true );
					break;
				case ESortMode::ALPHABETICAL_DESCENDING:
					sortResult = CompareDisplayNames( itemA, itemB, false );
					break;
				case ESortMode::OWNED_ASCENDING:
					sortResult = CompareIsOwned( itemA, itemB, true );
					break;
				case ESortMode::OWNED_DESCENDING:
					sortResult = CompareIsOwned( itemA, itemB, false );
					break;
				default:
					break;
			}

			if ( sortResult )
			{
				items.Swap( j, j + 1 );
			}
		}
	}
}

bool CompareDisplayNames( UNCUIMTXItem itemA, UNCUIMTXItem itemB, bool isAscending )
{
	ScriptAssert( IsValid( itemA ) && IsValid( itemB ), f"CompareDisplayNames tried to compare two items where one was invalid" );
	return isAscending ?
			   TextCompare( itemA.DisplayName, itemB.DisplayName ) > 0 :
			   TextCompare( itemA.DisplayName, itemB.DisplayName ) < 0;
}

int TextCompare( FText textA, FText textB )
{
	return textA.ToString().Compare( textB.ToString() );
}

bool CompareRarity( UNCUIMTXItem itemA, UNCUIMTXItem itemB, bool isAscending )
{
	ScriptAssert( IsValid( itemA ) && IsValid( itemB ), f"CompareRarity tried to compare two items where one was invalid" );
	return isAscending ?
			   ( itemA.Rarity > itemB.Rarity ) :
			   ( itemA.Rarity < itemB.Rarity );
}

bool CompareIsOwned( UNCUIMTXItem itemA, UNCUIMTXItem itemB, bool isAscending )
{
	ScriptAssert( IsValid( itemA ) && IsValid( itemB ), f"CompareIsOwned tried to compare two items where one was invalid" );

	bool aOwned = IsMtxItemOwned( itemA );
	bool bOwned = IsMtxItemOwned( itemB );
	return isAscending ?
			   ( !aOwned && bOwned ) :
			   ( aOwned && !bOwned );
}

FText GetSortModeText( ESortMode sortMode )
{
	FString key;

	switch ( sortMode )
	{
		case ESortMode::RARITY_ASCENDING:
			key = f"sort_mode_rarity_ascending";
			break;
		case ESortMode::RARITY_DESCENDING:
			key = f"sort_mode_rarity_descending";
			break;
		case ESortMode::ALPHABETICAL_ASCENDING:
			key = f"sort_mode_alphabetical_ascending";
			break;
		case ESortMode::ALPHABETICAL_DESCENDING:
			key = f"sort_mode_alphabetical_descending";
			break;
		case ESortMode::OWNED_ASCENDING:
			key = f"sort_mode_owned_ascending";
			break;
		case ESortMode::OWNED_DESCENDING:
			key = f"sort_mode_owned_descending";
			break;
		default:
			break;
	}

	return GetLocalizedText( Localization::Core, key );
}

void PrintGlyphNotFoundDebug( FString message )
{
	if ( GetCvarBool( f"UIDebug.LogInvalidGlyphs" ) )
	{
		Print( message );
	}
}

void PrintFrontEndCameraDebug( FString message )
{
	if ( GetCvarBool( f"UIDebug.LogFrontEndCameraDebug" ) )
	{
		Print( message );
	}
}

bool GetSafeColor( FName name, FLinearColor& outColor )
{
	bool result = false;

	const UAS_HudGlobals hudGlobals = GetHUDGlobals();
	if ( IsValid( hudGlobals ) )
	{
		result = hudGlobals.GetSafeColor( name, outColor );
	}

	return result;
}

UMaterialInstanceDynamic CreateDynamicMaterialFromImageBrush( UImage image )
{
	UMaterialInstanceDynamic result;

	if ( IsValid( image ) )
	{
		UMaterialInterface sourceMaterial = Cast<UMaterialInterface>( image.Brush.ResourceObject );
		if ( IsValid( sourceMaterial ) )
		{
			result = Material::CreateDynamicMaterialInstance( sourceMaterial );
		}
	}

	return result;
}

void SetUniqueScalarParameter( UMaterialInstanceDynamic material, FName parameter, float value )
{
	if ( IsValid( material ) && !Math::IsNearlyEqual( material.GetScalarParameterValue( parameter ), value ) )
	{
		material.SetScalarParameterValue( parameter, value );
	}
}

void SetUniqueVectorParameter( UMaterialInstanceDynamic material, FName parameter, FLinearColor value )
{
	if ( IsValid( material ) && !material.GetVectorParameterValue( parameter ).Equals( value ) )
	{
		material.SetVectorParameterValue( parameter, value );
	}
}

void OpenGenericPopup( UObject object, FText titleText, FText bodyText, FText yesText, FText noText, FName yesFunctionName, FName noFunctionName, bool closeOtherMenus = false, bool onlyOpenIfNoOtherMenusAreOpen = false, bool showPreviousMenu = false )
{
	FYesNoPopupData quitPopup;
	quitPopup.titleText				  = titleText;
	quitPopup.bodyText				  = bodyText;
	quitPopup.yesText				  = yesText;
	quitPopup.noText				  = noText;
	quitPopup.executeNoOnNavigateBack = false;

	if ( !yesFunctionName.IsNone() )
	{
		quitPopup.onYes.BindUFunction( object, yesFunctionName );
	}

	if ( !noFunctionName.IsNone() )
	{
		quitPopup.onYes.BindUFunction( object, noFunctionName );
	}

	UAS_GenericYesNoPopup popup = Cast<UAS_GenericYesNoPopup>( OpenMenu( n"GenericYesNoPopup", closeOtherMenus, onlyOpenIfNoOtherMenusAreOpen, showPreviousMenu ) );
	if ( IsValid( popup ) )
	{
		popup.Init( quitPopup );
	}
}

/* Returns screen position of WorldPos in the form of a X, Y transform offset. Bounds are X: [-1920/2, 1920/2] Y: [-1080/2, 1080/2]
	Can be plugged directly into CanvasSlot SetPosition calls
*/
bool GetScreenPos( FVector WorldPos, UNCUserWidget widget, FVector2D& outScreenPos )
{
	if ( !IsValid( widget ) )
		return false;

	ANCPlayerController controller = Cast<ANCPlayerController>( widget.GetOwningPlayer() );
	if ( !IsValid( controller ) )
		return false;

	FVector2D screenRes;
	if ( !GetScreenRes( widget, screenRes ) )
		return false;

	FVector2D screenPos;
	if ( !Gameplay::ProjectWorldToScreen( controller, WorldPos, screenPos, false ) )
		return false;

	float screenWidthX	= screenRes.X * 0.5;
	float screenWidthY	= screenRes.Y * 0.5;
	float screenOffsetX = screenPos.X - screenWidthX;
	float screenOffsetY = screenPos.Y - screenWidthY;
	float resultWidthX	= 1920 * 0.5;
	float resultWidthY	= 1080 * 0.5;
	float XMap			= Math::GetMappedRangeValueClamped( FVector2D( -screenWidthX, screenWidthX ), FVector2D( -resultWidthX, resultWidthX ), screenOffsetX );
	float YMap			= Math::GetMappedRangeValueClamped( FVector2D( -screenWidthY, screenWidthY ), FVector2D( -resultWidthY, resultWidthY ), screenOffsetY );

	outScreenPos = FVector2D( XMap, YMap );
	return true;
}

// Returns screen resolution for player owner of widget
bool GetScreenRes( UNCUserWidget widget, FVector2D& outScreenRes )
{
	if ( !IsValid( widget ) )
		return false;

	ANCPlayerController pc = Cast<ANCPlayerController>( widget.GetOwningPlayer() );
	if ( !IsValid( pc ) )
		return false;

	return GetScreenRes( pc, outScreenRes );
}

bool GetScreenRes( ANCPlayerController pc, FVector2D& outScreenRes )
{
	if ( !IsValid( pc ) )
		return false;

	int X = 0;
	int Y = 0;
	pc.GetViewportSize( X, Y );

	outScreenRes = FVector2D( X, Y );
	return true;
}

FVector2D MapViewportPositionToWidgetPosition( FVector2D pos, UWidget widget )
{
	FVector2D result = FVector2D::ZeroVector;

	if ( IsValid( widget ) )
	{
		// Get the viewport size and widget size
		FVector2D vSize = WidgetLayout::GetViewportSize();
		FVector2D wSize = widget.GetPaintSpaceGeometry().AbsoluteSize;

		// Get the viewport scale to calculate based on dpi scaling
		float vScale = WidgetLayout::GetViewportScale();

		// Get the ratio
		FVector2D ratio = !vSize.Equals( FVector2D::ZeroVector ) ? wSize / vSize : FVector2D::ZeroVector;

		// Map the point
		result = vScale > 0.0f ? ( pos * ratio ) / vScale : FVector2D::ZeroVector;
	}

	return result;
}

// Does the same as the previous method, but uses widget geometry
// Maps the viewport position to the position within the provided geometry.
FVector2D MapViewportPositionToWidgetPositionWithGeometry( FVector2D screenPosition, FGeometry widgetGeometry )
{
	FVector2D viewportSize	   = WidgetLayout::GetViewportSize();
	FVector2D relativePosition = screenPosition / viewportSize;

	FVector2D widgetSize  = widgetGeometry.GetLocalSize();
	FVector2D newPosition = relativePosition * widgetSize;

	return newPosition;
}

bool ShouldPinRespawnBeaconButton( UAS_RespawnBeaconButton button )
{
	return button.Class.IsChildOf( UAS_RespawnBeaconButton_Mara::StaticClass() );
}

FPinRequestOptions GetRespawnMenuDefaultScreenPinOptions( UAS_RespawnBeaconButton button = nullptr, FVector2D buttonScreenPos = FVector2D::ZeroVector )
{
	FPinRequestOptions options;
	options.stickToEdge			= IsValid( button ) ? !button.Class.IsChildOf( UAS_RespawnBeaconButton_Mara::StaticClass() ) : true;
	options.useScreenPercentage = true;

	FVector2D screenRes = WidgetLayout::GetViewportSize();

	// the right margin is for the vendor tab... the vendor tab has a little bump in it near the center...
	// all this complicated math is to push the margin out even more near the bump
	float buttonYPerc = buttonScreenPos.Y / screenRes.Y; // get button y loc
	buttonYPerc -= 0.5;									 // center it so it ranges from -0.5 to 0.5 ( instead of 0-1 )
	buttonYPerc += 0.05;								 // except the bump isn't exactly center screen... its a little higher on the Y
	buttonYPerc				 = Math::Abs( buttonYPerc ); // now we can abs it to get an unsigned value from edge to center
	const float32 BUMP_START = 0.1;						 // hard coded value - percentage of screen the bump starts angling
	const float32 BUMP_END	 = 0.065;					 // hard coded value - percentage of screen the bump flattens back out
	const float32 BUMP_ADD	 = 0.07;					 // hard coded value - percentage of screen the bump sticks out
	float marginXBumpAdd	 = Math::GetMappedRangeValueClamped( FVector2D( BUMP_START, BUMP_END ), FVector2D( 0, BUMP_ADD ), buttonYPerc );

	float basePixelSize		 = screenRes.X > screenRes.Y ? screenRes.Y : screenRes.X;
	float32 marginXRightPerc = GetCvarFloat( "SiegeTower.ZiplineMarginXRight" ) + float32( marginXBumpAdd );
	float xPixelSize		 = basePixelSize * marginXRightPerc;
	float marginXRight		 = xPixelSize / screenRes.X;
	float marginXLeft		 = 0.05;
	options.xScreenMargin	 = FVector2D( marginXLeft, marginXRight );

	float32 marginYTop	  = GetCvarFloat( "SiegeTower.ZiplineMarginYTop" );
	options.yScreenMargin = FVector2D( marginYTop, 0 );

	return options;
}

FText GetGameplayTagAsRichTextId( FGameplayTag tag )
{
	// Gameplay tags to rich text ids should always ALWAYS be lowercase
	return Localization::GetUnlocalizedTextFromString( tag.ToString().ToLower() );
}

FText GetGameplayTagAsRichTextIdColorHighlight( FGameplayTag tag )
{
	FString concat = f"{GetGameplayTagAsRichTextId( tag ).ToString()}.highlight";

	// Gameplay tags to rich text ids should always ALWAYS be lowercase
	return Localization::GetUnlocalizedTextFromString( concat.ToLower() );
}

mixin FVector2D ToVector2D( FVector self )
{
	return FVector2D( self.X, self.Y );
}

bool IsGamepadInput( APlayerController controller )
{
	return UNCUtils::Client_IsGamepadInput( controller );
}

void GetForecastedShieldAndHealth( const UHealthComponent healthComponent, float32 damageAmount, float32&out healthFrac, float32&out shieldFrac )
{
	// TODO: Once NC1-18943 is fixed, you don't need to do this anymore.
	const float32 maxShieldHealth	 = healthComponent.GetMaxShieldHealth();
	const float32 shieldHealth		 = healthComponent.GetShieldHealth();
	const float32 totalMaxHealth	 = maxShieldHealth + healthComponent.GetMaxHealth();
	const float32 totalCurrentHealth = shieldHealth + healthComponent.GetHealth() - damageAmount;
	const float32 totalHealthFrac	 = totalMaxHealth <= 0 ? 0 : totalCurrentHealth / totalMaxHealth;

	const float32 totalHealthFrac_shieldPortion = maxShieldHealth <= 0 ? 0 : maxShieldHealth / totalMaxHealth;
	if ( totalHealthFrac_shieldPortion > 0 )
	{
		shieldFrac = Math::Max( ( totalHealthFrac_shieldPortion - ( 1.f - totalHealthFrac ) ) / totalHealthFrac_shieldPortion, 0.f );
	}
	healthFrac = Math::Min( totalHealthFrac / ( 1.f - totalHealthFrac_shieldPortion ), 1.f );
}

mixin void PlayAnimationForwardOrReverse( UUserWidget widget, bool forward, UWidgetAnimation animation, float playbackSpeed = 1.0f, bool resoreState = false )
{
	if ( !IsValid( animation ) )
		return;

	if ( forward )
	{
		widget.PlayAnimationForward( animation, playbackSpeed, resoreState );
	}
	else
	{
		widget.PlayAnimationReverse( animation, playbackSpeed, resoreState );
	}
}

FLinearColor GetCommonUiMpcColor( FName colorId )
{
	if ( colorId == CommonUiColorMpcNames::PURE_WHITE )
		return FLinearColor::White;

	FLinearColor result = FLinearColor::DPink;

	UCL_GlobalParameterSystem globalParameters = GetGlobalParameters();
	if ( IsValid( globalParameters ) && IsValid( globalParameters.commonUiColors ) )
	{
		result = Material::GetVectorParameterValue( globalParameters.commonUiColors, colorId );
	}

	return result;
}

// Used for countdown in character select and base select phases.
// Pass in countdown time left plus frame deltatime and it will play audio at every second and once for the last 5
void PlayCountdownAudio( float timeLeft, float deltaTime )
{
	const int FINAL_COUNTDOWN_TIME	 = 4;
	const int NORMAL_COUNTDOWN_START = 5;

	if ( timeLeft > NORMAL_COUNTDOWN_START )
		return;

	int previousTimeFloor = Math::FloorToInt( timeLeft + deltaTime );
	int currentTimeFloor  = Math::FloorToInt( timeLeft );

	// Play single countdown every second from 10 - 6
	// At 5 left, play final five countdown and do not play anything else again

	// We did not cross a whole second, ie 2.1 -> 1.9
	if ( previousTimeFloor <= currentTimeFloor )
		return;

	if ( previousTimeFloor < FINAL_COUNTDOWN_TIME )
		return;

	if ( previousTimeFloor == FINAL_COUNTDOWN_TIME )
		Client_EmitSoundUI( Audio().ui_countdown_final_five );
	else
		Client_EmitSoundUI( Audio().ui_countdown_normal_single );
}

bool DoesCurrentContextMatchAny( FGameplayTagContainer matchingContainer )
{
	bool result = false;

	UAS_FrontEndNavigator frontEndNavigator = GetFrontEndNavigator();
	if ( IsValid( frontEndNavigator ) )
	{
		FGameplayTagContainer container = frontEndNavigator.GetCurrentContexts();
		result							= container.HasAny( matchingContainer );
	}

	return result;
}

FText GetCurrentMapName()
{
	FText result;

	UNCPackageConfigAsset configAsset = UNCPackageConfigEngineSubsystem::GetPackageConfig();
	if ( IsValid( configAsset ) )
	{
		AAS_GameModeBase gameMode = GameModeDefaults();
		if ( IsValid( gameMode ) )
		{
			for ( FMapListStruct map : configAsset.MapsList )
			{
				if ( map.MapFile.GetAssetName() == GetCurrentWorld().GetName() )
				{
					result = map.MapName;
				}
			}
		}
	}

	if ( result.IsEmpty() )
	{
		// If we are unable to find the map name using the map list, call this a "custom map"
		result = GetLocalizedText( Localization::MapsAndModes, "custom_map" );
	}

	return result;
}

FModeListStruct GetCurrentModeData()
{
	UNCPackageConfigAsset configAsset = UNCPackageConfigEngineSubsystem::GetPackageConfig();
	if ( IsValid( configAsset ) )
	{
		AAS_GameModeBase gameMode = GameModeDefaults();
		if ( IsValid( gameMode ) )
		{
			for ( FModeListStruct mode : configAsset.ModesList )
			{
				if ( mode.GameMode.ResolveClass() == gameMode.Class )
				{
					return mode;
				}
			}
		}
	}
	FModeListStruct temp;
	temp.ModeId = GameplayTags::Dev_Invalid;
	return temp;
}

TOptional<FModeListStruct> GetModeData( FGameplayTag modeIndex )
{
	TOptional<FModeListStruct> result;

	UNCPackageConfigAsset configAsset = UNCPackageConfigEngineSubsystem::GetPackageConfig();
	if ( IsValid( configAsset ) )
	{
		for ( FModeListStruct mode : configAsset.ModesList )
		{
			if ( mode.ModeId == modeIndex )
			{
				result = mode;
			}
		}
	}

	return result;
}

FText GetCurrentModeName()
{
	FText result;

	FModeListStruct mode = GetCurrentModeData();
	result				 = mode.ModeName;

	if ( result.IsEmpty() )
	{
		// If we are unable to find the mode name using the mode list, call this a "custom mode"
		result = GetLocalizedText( Localization::MapsAndModes, "custom_mode" );
	}

	return result;
}

FGameplayTag GetCurrentModeTag()
{
	FGameplayTag result = GetCurrentModeData().ModeId;
	return result;
}

void SetFrontEndCamera( APlayerController controller, FGameplayTag cameraId )
{
	UAS_FrontEndCameraManager frontEndCameraManager = GetFrontEndCameraManager();
	if ( IsValid( frontEndCameraManager ) )
	{
		ACameraActor outCamera;
		if ( frontEndCameraManager.GetCameraById( cameraId, outCamera ) )
		{
			frontEndCameraManager.TryToSetViewTarget( controller, outCamera );
		}
	}
}

void ChangeContextCameraAndSpawnItemOfType( FGameplayTag screenContext, UNCUIMTXItem item )
{
	if ( !IsValid( item ) )
		return;

	UAS_FrontEndItemManager itemManager = GetFrontEndItemManager();
	if ( IsValid( itemManager ) )
	{
		ChangeContextAndCamera( screenContext );
		itemManager.RemoveSpawnedItems();

		FGameplayTag itemTypeTag = GetItemTypeTag( item );
		itemManager.TryToSpawnItemOfType( itemTypeTag, screenContext );
	}
}

void ChangeContextAndCamera( FGameplayTag screenContext )
{
	UAS_FrontEndNavigator frontEndNavigator = GetFrontEndNavigator();
	if ( IsValid( frontEndNavigator ) )
	{
		frontEndNavigator.TryToChangeSubMenuContext( screenContext );
	}

	SetFrontEndCamera( Client_GetLocalPlayerController(), screenContext );
}

void EnableOrDisableCursorInteractionManager( bool isEnabled )
{
	UAS_CursorInteractionManager cursorInteractionManager = GetCursorInteractionManager();
	if ( IsValid( cursorInteractionManager ) )
	{
		cursorInteractionManager.TryToEnableCursorInteraction( isEnabled );
	}
}