UCLASS( Abstract, Config = Game, DefaultConfig )
class UAS_GameInstance : UNCGameInstance
{
	default WatermarkID			= GetWatermarkID();
	default DebugLaunchSettings = GetDefaultDebugLaunchSettings();

	bool hasProcessedInitialMessages = false;

	UFUNCTION( BlueprintOverride )
	void Init()
	{
		UNCOnlineSubsystem::SetBuildVersion( GetMatchmakingVersion().ToString() );
	}

	UFUNCTION( BlueprintOverride )
	void Server_LaunchSettingsReceived()
	{
		const int numPlayers = GetMaxPlayers();
		FString command		 = f"BotsAdd {numPlayers - 1} examplebot";
		System::ExecuteConsoleCommand( command );

		ServerCallbacks().OnGamePhaseChanged.AddUFunction( this, n"OnDebugLauncherGamePhaseChanged" );
	}

	UFUNCTION()
	void OnDebugLauncherGamePhaseChanged( int OldState, int NewState )
	{
		if ( NewState == GamePhase::PREMATCH )
		{
			SelectCharactersForSpecifiedPlayers( 0, DebugLaunchSettings.CharactersToSelectForTeamZero );
			SelectCharactersForSpecifiedPlayers( 1, DebugLaunchSettings.CharactersToSelectForTeamOne );
		}
		else if ( NewState == GamePhase::WINNER_DETERMINED || NewState == GamePhase::WAITING_FOR_PLAYERS )
		{
			return;
		}
		else // CHARACTER_SELECT, PLAYING, BASE_SELECT
		{
			if ( NewState == GamePhase::BASE_SELECT )
			{
				SelectBaseForSpecifiedTeam( 0, DebugLaunchSettings.BaseToSelectForTeamZero );
				SelectBaseForSpecifiedTeam( 1, DebugLaunchSettings.BaseToSelectForTeamOne );
			}

			UAS_ServerScript_Mode mode = GetServerScript();
			mode.SkipCurrentPhase();
		}
	}

	void SelectCharactersForSpecifiedPlayers( int TeamIdx, const TArray<FGameplayTag>& DesiredClasses )
	{
		TArray<ANCPlayerCharacter> Players = GetPlayersOfTeam( TeamIdx );
		ScriptAssert( Players.Num() == DesiredClasses.Num(), "Mismatch between specified characters and the players this mode expects" );
		for ( int i = 0; i < DesiredClasses.Num(); ++i )
		{
			AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( Players[i] );
			if ( IsValid( player ) )
			{
				player.classManager.SetClass( DesiredClasses[i] );
			}
		}
	}

	void SelectBaseForSpecifiedTeam( int TeamIdx, FGameplayTag DesiredBase )
	{
		Bases().SetBaseSelectionOverride( TeamIdx, DesiredBase );
	}

	// good for when you want to turn things on and off while debugging and don't want to accidentally check in something that you might forget
	FConsoleVariable CVar_DebugTempDebug( "ScriptDebug.TempDebug", 0 );
	//////////////////////////////////////////////////////
	// C O N S O L E    V A R S    -    B U G S
	//////////////////////////////////////////////////////
	FConsoleVariable CVar_BugReproNum( "BugReproNum", -1 );

	//////////////////////////////////////////////////////
	// C O N S O L E    V A R S
	//////////////////////////////////////////////////////

#if EDITOR
	FConsoleVariable CVar_EnableShotClock( "ScriptDebug.EnableShotClock", 0 );
#else
	FConsoleVariable CVar_EnableShotClock( "ScriptDebug.EnableShotClock", 1 );
#endif
	FConsoleVariable Cvar_UnlimitedBreacherEnergy( "ScriptDebug.UnlimitedBreacherEnergy", 0 );

	FConsoleVariable CVar_Crafting( "ScriptDebug.Crafting", 0 );
	FConsoleVariable CVar_Bleedout( "ScriptDebug.Bleedout", 0 );
	FConsoleVariable CVar_ResourceRegen( "ScriptDebug.ResourceRegen", 0 );
	FConsoleVariable CVar_ResourceDisableWeaponDamage( "ScriptDebug.ResourceDisableWeaponDamage", 0 );
	FConsoleVariable CVar_ResourceMeleeBonus( "ScriptDebug.MeleeBonus", 0 );
	FConsoleVariable CVar_Teammates( "ScriptDebug.SameTeam", 0 );
	FConsoleVariable CVar_SkipRespawnMenu( "ScriptDebug.SkipRespawnMenu", 0 );
	FConsoleVariable CVar_RaidDebug( "ScriptDebug.DebugRaid", 0 );

	FConsoleVariable CVar_MinutesForFullDay( "ScriptDebug.MinutesForFullDay", -1.0 );
#if EDITOR
	FConsoleVariable CVar_SequentialJoin( "ScriptDebug.SequentialJoin", 0 );
#else
	FConsoleVariable CVar_SequentialJoin( "ScriptDebug.SequentialJoin", 0 );
#endif
	FConsoleVariable CVar_FillEmptyFirst( "ScriptDebug.FillEmptyTeamsFirst", 0 );
	FConsoleVariable CVar_LootZoneResetClosesDoors( "ScriptDebug.LootZoneResetClosesDoors", 1 );

#if EDITOR
	FConsoleVariable CVar_SkipWaitingForPlayers( "ScriptDebug.SkipWaitingForPlayers", 1 );
#else
	FConsoleVariable CVar_SkipWaitingForPlayers( "ScriptDebug.SkipWaitingForPlayers", 0 );
#endif

#if EDITOR
	FConsoleVariable CVar_SkipCharacterSelect( "ScriptDebug.SkipCharacterSelect", 1 );
#else
	FConsoleVariable CVar_SkipCharacterSelect( "ScriptDebug.SkipCharacterSelect", 0 );
#endif

#if EDITOR
	FConsoleVariable CVar_SkipBaseSelect( "ScriptDebug.SkipBaseSelect", 1 );
#else
	FConsoleVariable CVar_SkipBaseSelect( "ScriptDebug.SkipBaseSelect", 0 );
#endif

#if EDITOR
	FConsoleVariable CVar_SkipMatchIntro( "ScriptDebug.SkipMatchIntro", 1 );
#else
	FConsoleVariable CVar_SkipMatchIntro( "ScriptDebug.SkipMatchIntro", 0 );
#endif

#if EDITOR
	FConsoleVariable CVar_EverythingUnlocked( "ScriptDebug.EverythingUnlocked", 1 );
#else
	FConsoleVariable CVar_EverythingUnlocked( "ScriptDebug.EverythingUnlocked", 0 );
#endif

#if EDITOR
	FConsoleVariable Cvar_EnableMatchEnding( "ScriptDebug.EnableMatchEnding", 0 );
#else
	FConsoleVariable Cvar_EnableMatchEnding( "ScriptDebug.EnableMatchEnding", 1 );
#endif

#if EDITOR
	FConsoleVariable Cvar_AllowJoinInProgress( "ScriptDebug.AllowJoinInProgress", 1 );
#else
	FConsoleVariable Cvar_AllowJoinInProgress( "ScriptDebug.AllowJoinInProgress", 0 );
#endif

#if EDITOR
	FConsoleVariable Cvar_InstantRespawn( "ScriptDebug.InstantRespawn", 1 );
#else
	FConsoleVariable Cvar_InstantRespawn( "ScriptDebug.InstantRespawn", 0 );
#endif
	FConsoleVariable Cvar_DebugInteractiveWidget( "ScriptDebug.DebugInteractiveWidget", 0 );
	FConsoleVariable Cvar_EnableSubtitles( "ScriptDebug.EnableSubtitles", 1 );
	FConsoleVariable Cvar_EnableSubtitlesDebug( "ScriptDebug.EnableSubtitlesDebug", 0 );

	FConsoleVariable Cvar_EnableDomeShieldCrumble( "ScriptDebug.EnableDomeShieldCrumble", 1 );
	FConsoleVariable Cvar_EnableDomeShieldFX( "ScriptDebug.EnableDomeShieldFX", 1 );
	FConsoleVariable Cvar_DebugSpawnMount( "ScriptDebug.DebugSpawnMount", 0 );
	FConsoleVariable Cvar_DebugHammerSlam( "ScriptDebug.DebugHammerSlam", 0 );

	FConsoleVariable Cvar_DestructibleResources( "ScriptDebug.DestructibleResources", 1 );

	FConsoleVariable Cvar_EnableRaidRespawnSelection( "ScriptDebug.EnableRaidRespawnSelection", 1 );

	FConsoleVariable Cvar_RespawnRuleBeaconEnemyBlockRange( "RespawnRules.BeaconEnemyBlockRange", 2000 );

	FConsoleVariable Cvar_EnableAmmoHammer( "ScriptDebug.EnableAmmoHammer", 1 );
	FConsoleVariable Cvar_RaidMsg_NoBreacherLives( "ScriptDebug.RaidMsg_NoBreacherLives", 0 );
	FConsoleVariable Cvar_RaidMsg_BreacherEnergySegment( "ScriptDebug.RaidMsg_BreacherEnergySegment", 1 );
	FConsoleVariable Cvar_EnableMusic( "ScriptDebug.EnableMusic", 1 );
	FConsoleVariable Cvar_EnableMusicInTestMaps( "ScriptDebug.EnableMusicInTestMaps", 0 );
	FConsoleVariable Cvar_DebugMusic( "ScriptDebug.DebugMusic", 0 );
	FConsoleVariable Cvar_EnableBreachPlantPoints( "ScriptDebug.EnableBreachPlantPoints", 1 );
	FConsoleVariable Cvar_DebugTreeFall( "ScriptDebug.DebugTreeFall", 0 );
	FConsoleVariable Cvar_DisableDomeColorUpdates( "ScriptDebug.DisableDomeColorUpdates", 1 );
	FConsoleVariable Cvar_BreakerSplashAnimRate( "ScriptDebug.BreakerSplashAnimRate", 100 );
	FConsoleVariable Cvar_BreakerSplashNagDelay( "ScriptDebug.BreakerSplashNagDelay", 60 );
	FConsoleVariable Cvar_EnableCinematicPrototype( "ScriptDebug.EnableCinematicPrototype", 0 );
	FConsoleVariable Cvar_DebugSpeed( "ScriptDebug.DebugSpeed", 0 );
	FConsoleVariable Cvar_DebugZSpeed( "ScriptDebug.DebugZSpeed", 0 );
	FConsoleVariable Cvar_ShowMountStatsInMenu( "ScriptDebug.ShowMountStatsInMenu", 1 );
	FConsoleVariable Cvar_DebugCondorTactical( "ScriptDebug.TestCondorTactical", 0 );
	FConsoleVariable Cvar_SyncAnimDebug( "ScriptDebug.SyncAnimDebug", 0 );
	FConsoleVariable Cvar_PlayerStatsSubmissionTimeout( "Progression.PlayerStatsSubmissionTimeout", 90 );
	FConsoleVariable Cvar_DebugScriptAnimNotify( "ScriptDebug.DebugScriptAnimNotify", 0 );
	FConsoleVariable Cvar_DebugLogAnimTrackerNotifies( "ScriptDebug.LogAnimTrackerNotifies", 0 );
	FConsoleVariable Cvar_DebugShowZiplineSpawnBounds( "ScriptDebug.ShowZiplineSpawnBounds", 0 );
	FConsoleVariable Cvar_DebugEnableTeleportBugs( "ScriptDebug.EnableTeleportBugs", 1 );
	FConsoleVariable Cvar_DebugLockTPPCamDist( "ScriptDebug.LockTPPCamDist", 0 );
	FConsoleVariable Cvar_DebugVendor( "ScriptDebug.DebugVendor", 0 );
	FConsoleVariable Cvar_DebugVendorActivity( "ScriptDebug.DebugVendorActivity", 0 );
	FConsoleVariable Cvar_DebugDisableNPCVO( "ScriptDebug.DisableNPCVO", 0 );
	FConsoleVariable Cvar_DebugDrawMeleeAimAssist( "ScriptDebug.DrawMeleeAimAssist", 0 );
	FConsoleVariable Cvar_LootAllowDevOnly( "ScriptDebug.LootAllowDevOnly", 1 );
	FConsoleVariable Cvar_DebugTrainingBlimp( "ScriptDebug.DebugTrainingBlimp", 0 );

	//////////////////////////////////////////////////////
	// PLAYER MELEE
	FConsoleVariable Cvar_PlayerMeleeTrackedMeleeLungeTime( "PlayerMelee.TrackedMeleeLungeTime", 0.5 );
	FConsoleVariable Cvar_PlayerMeleeDisableMeleeInputAfterTrackedLungeImpactTime( "PlayerMelee.DisableMeleeInputAfterTrackedLungeImpactTime", 0.5 );
	FConsoleVariable Cvar_PlayerMeleeTargetMeleeAttackKnockbackDist( "PlayerMelee.TargetMeleeAttackKnockbackDist", 1750.0 );
	FConsoleVariable Cvar_PlayerMeleeAttackerMeleeAttackKnockbackDist( "PlayerMelee.AttackerMeleeAttackKnockbackDist", 750.0 );
	FConsoleVariable Cvar_PlayerMeleeClashAxesForceApartDist( "PlayerMelee.ClashAxesForceApartDist", 1500.0 );
	FConsoleVariable Cvar_PlayerMeleeEnableSyncedExecutions( "PlayerMelee.EnableSyncedExecutions", 0 );
	FConsoleVariable Cvar_PlayerMeleeEnableSyncedClashes( "PlayerMelee.EnableSyncedClashes", 0 );

	//////////////////////////////////////////////////////
	// SIEGE TOWER
	FConsoleVariable Cvar_SiegeTowerDebugPOV( "SiegeTower.DebugPOV", 0 );
	FConsoleVariable Cvar_SiegeTowerDebugTowerAnim( "SiegeTower.DebugTowerAnim", 0 );
	FConsoleVariable Cvar_SiegeTowerDebugTowerCollision( "SiegeTower.DebugTowerCollision", 0 );
	FConsoleVariable Cvar_SiegeTowerCollisionPushStrength( "SiegeTower.CollisionPushStrength", 350 );

	FConsoleVariable Cvar_SiegeTowerZiplineEnableHalfClamp( "SiegeTower.ZiplineEnableHalfClamp", 1 );
	FConsoleVariable Cvar_SiegeTowerZiplineHalfClampOffset( "SiegeTower.ZiplineHalfClampOffset", 2800 );

	FConsoleVariable Cvar_SiegeTowerZiplineEnableDomeClamp( "SiegeTower.ZiplineEnableDomeClamp", 1 );
	FConsoleVariable Cvar_SiegeTowerZiplineDomeClampRadius( "SiegeTower.ZiplineDomeClampRadius", -1400.0 );

	FConsoleVariable Cvar_SiegeTowerZiplineEnableFocusClamp( "SiegeTower.ZiplineEnableFocusClamp", 1 );
	FConsoleVariable Cvar_SiegeTowerZiplineFocusClampRadius( "SiegeTower.ZiplineFocusClampRadius", 4600.0 );
	FConsoleVariable Cvar_SiegeTowerZiplineFocusClampOffset( "SiegeTower.ZiplineFocusClampOffset", -3000.0 );

	FConsoleVariable Cvar_SiegeTowerZiplineEnableScreenClamp( "SiegeTower.ZiplineEnableScreenClamp", 1 );
	FConsoleVariable Cvar_SiegeTowerZiplineScreenClampX( "SiegeTower.ZiplineScreenClampX", 0.75 );
	FConsoleVariable Cvar_SiegeTowerZiplineScreenClampY( "SiegeTower.ZiplineScreenClampY", 0.49 );
	FConsoleVariable Cvar_SiegeTowerZiplineScreenOffsetY( "SiegeTower.ZiplineScreenOffsetY", -0.05 );
	FConsoleVariable Cvar_SiegeTowerZiplineEdgeAlphaClamp( "SiegeTower.ZiplineEdgeAlphaClamp", 0.2 );

	FConsoleVariable Cvar_SiegeTowerZiplineEnableOtherFocusClamp( "SiegeTower.ZiplineEnableOtherFocusClamp", 1 );
	FConsoleVariable Cvar_SiegeTowerZiplineOtherFocusClampRadiusInner( "SiegeTower.ZiplineOtherFocusClampRadiusInner", 0.085 );
	FConsoleVariable Cvar_SiegeTowerZiplineOtherFocusClampRadiusOuter( "SiegeTower.ZiplineOtherFocusClampRadiusOuter", 0.1 );
	FConsoleVariable Cvar_SiegeTowerZiplineOtherFocusClampOffsetY( "SiegeTower.ZiplineOtherFocusClampOffsetY", 0.027 );
	FConsoleVariable Cvar_SiegeTowerZiplineMarginXRight( "SiegeTower.ZiplineMarginXRight", 0.39 );
	FConsoleVariable Cvar_SiegeTowerZiplineMarginYTop( "SiegeTower.ZiplineMarginYTop", 0.2 );

	FConsoleVariable Cvar_SiegeTowerZiplineEnableBoxClamp( "SiegeTower.ZiplineEnableBoxClamp", 1 );
	FConsoleVariable Cvar_SiegeTowerZiplineBoxClampX( "SiegeTower.ZiplineBoxClampX", 0.075 );
	FConsoleVariable Cvar_SiegeTowerZiplineBoxClampY( "SiegeTower.ZiplineBoxClampY", 0.2 );
	FConsoleVariable Cvar_SiegeTowerZiplineBoxOffsetY( "SiegeTower.ZiplineBoxOffsetY", -0.05 );
	FConsoleVariable Cvar_SiegeTowerZiplineCursorValidDeltaTolerance( "SiegeTower.ZiplineCursorValidDeltaTolerance", 500.0 );

	FConsoleVariable Cvar_SiegeTowerDebugZiplineSpawn( "SiegeTower.DebugZiplineSpawn", 0 );
	FConsoleVariable Cvar_SiegeTowerZiplineScreenFade( "SiegeTower.ZiplineScreenFade", 0 );
	FConsoleVariable Cvar_ZiplineShield( "ScriptDebug.EnableZiplineShield", 1 );

	FConsoleVariable Cvar_SiegeTowerTaubeTower( "SiegeTower.TaubeTower", 0 );

	//////////////////////////////////////////////////////
	// MOUNT
	FConsoleVariable Cvar_VehicleMountFreeLookReturn( "VehicleMount.FreeLookReturn", 0 );
	FConsoleVariable Cvar_VehicleMountEnableTankControls( "VehicleMount.EnableTankControls", 0 );
	FConsoleVariable Cvar_VehicleMountLockTankControlsInFreelook( "VehicleMount.LockTankControlsInFreelook", 1 );
	FConsoleVariable Cvar_VehicleMountTankControlsAutoPitch( "VehicleMount.TankControlsAutoPitch", 1 );
	FConsoleVariable Cvar_VehicleMountDebugSpawn( "VehicleMount.DebugSpawn", 0 );
	FConsoleVariable Cvar_VehicleMountEnableEject( "VehicleMount.EnableEject", 0 );
	FConsoleVariable Cvar_VehicleMountEnableSlideOffMount( "VehicleMount.EnableSlideOffMount", 0 );
	FConsoleVariable Cvar_VehicleMountEnableKickOffMountInAir( "VehicleMount.EnableKickOffMountInAir", 1 );
	FConsoleVariable Cvar_VehicleMountEnableActiveSprint( "VehicleMount.EnableActiveSprint", 1 );
	FConsoleVariable Cvar_VehicleMountEnableScreenshake( "VehicleMount.EnableScreenshake", 0 );
	FConsoleVariable Cvar_VehicleMountInputHold( "VehicleMount.InputHold", 0 );
	FConsoleVariable Cvar_VehicleMountInputDblTap( "VehicleMount.InputDblTap", 1 );
	FConsoleVariable Cvar_VehicleMountEnableADSSlowdown( "VehicleMount.EnableADSSlowdown", 1 );
	FConsoleVariable Cvar_VehicleMountEnableShootingExitsActiveSprint( "VehicleMount.EnableShootingExitsActiveSprint", 1 );

	FConsoleVariable Cvar_VehicleMountDebugAcceleration( "VehicleMount.DebugAcceleration", 0 );
	FConsoleVariable Cvar_VehicleMountDebugSpeed( "VehicleMount.DebugSpeed", 0 );
	FConsoleVariable Cvar_VehicleMountDebugTurnRadius( "VehicleMount.DebugTurnRadius", 0 );
	FConsoleVariable Cvar_VehicleMountDebugDrift( "VehicleMount.DebugDrift", 0 );
	FConsoleVariable Cvar_VehicleMountDebugGoalDir( "VehicleMount.DebugGoalDir", 0 );
	FConsoleVariable Cvar_VehicleMountDebugLookAnims( "VehicleMount.DebugLookAnims", 0 );
	FConsoleVariable Cvar_VehicleMountDebugSlide( "VehicleMount.DebugSlide", 0 );
	FConsoleVariable Cvar_VehicleMountDebugJump( "VehicleMount.DebugJump", 0 );
	FConsoleVariable Cvar_VehicleMountDebugLean( "VehicleMount.DebugLean", 0 );
	FConsoleVariable Cvar_VehicleMountDebugKillMount( "VehicleMount.DebugKillMount", 0 );
	FConsoleVariable Cvar_VehicleMountDebugForceDismount( "VehicleMount.DebugForceDismount", 0 );
	FConsoleVariable Cvar_VehicleMountDebugTrickHints( "VehicleMount.DebugTrickHints", 0 );
	FConsoleVariable Cvar_VehicleMountDebugWindSFX( "VehicleMount.DebugWindSFX", 0 );

	FConsoleVariable Cvar_WallInteractionSystemEnabled( "ScriptDebug.WallInteractionSystemEnabled", 1 );
	FConsoleVariable Cvar_PinnedWidgetEnableUpdateZorderHack( "PinnedWidget.EnableUpdateZorderHack", 0 );
	FConsoleVariable Cvar_PinnedWidgetEnableDebug( "PinnedWidget.EnableDebug", 0 );
	FConsoleVariable Cvar_PinnedWidgetEnableEllipseClamp( "PinnedWidget.EnableEllipseClamp", 1 );

	FConsoleVariable Cvar_PingEnableDebug( "Ping.EnableDebug", 0 );
	FConsoleVariable Cvar_PingMinisizeEllipseRadiusX( "Ping.MinisizeEllipseRadiusX", 425 );
	FConsoleVariable Cvar_PingMinisizeEllipseRadiusY( "Ping.MinisizeEllipseRadiusY", 200 );

	// @jmccarty: Placeholder for player color mode settings
	FConsoleVariable Cvar_ColorMode( "ScriptDebug.ColorMode", 0 );

	// @jmccarty: Show fake data on the death recap in addition to real data
	FConsoleVariable Cvar_UseFakeDeathRecapData( "DeathRecap.UseFakeData", 0 );

	// @jmccarty: Use a cvar to simulate buying an event from the war table
	FConsoleVariable Cvar_TestEventPurchased( "Progression.TestEventPurchased", 0 );

	// @jmccarty: Use a cvar to simulate gaining and spending grind currency
	FConsoleVariable Cvar_GrindCurrencyAmount( "Progression.GrindCurrencyAmount", 0 );

	// @jmccarty allows for visualization of bounds and pivot of front end items
	FConsoleVariable Cvar_FrontEndItemDebugEnabled( "FrontEndItem.DebugEnabled", 0 );

	// @jmccarty allows logging for debugging front end animations
	FConsoleVariable Cvar_UIDebugLogAnimDebug( "UIDebug.LogAnimDebug", 0 );

	// @jmccarty allows logging for glyphs that cannot be found
	FConsoleVariable Cvar_UIDebugLogInvalidGlyphs( "UIDebug.LogInvalidGlyphs", 0 );

	// @jmccarty allows logging for front end cameras
	FConsoleVariable Cvar_UIDebugLogFrontEndCameraDebug( "UIDebug.LogFrontEndCameraDebug", 0 );

	// @jmccarty allows logging for front end cameras
	FConsoleVariable Cvar_CharacterSelectDurationOverride( "ScriptDebug.CharacterSelectDurationOverride", -1 );

	//// AUDIO ////
	// @ckowalski: AUDIO ECHO Threat System Enabled/Disabled
	FConsoleVariable Cvar_ECHOThreatSystemEnabled( "ScriptDebug.ECHOThreatSystemEnabled", 1 );
	FConsoleVariable Cvar_ECHOThreatDebugEnabled( "ScriptDebug.ECHOThreatDebugEnabled", 0 );
	FConsoleVariable Cvar_ECHOThreatVerticalContextEnabled( "ScriptDebug.ECHOThreatVerticalContextEnabled", 1 );
	// @ckowalski: Prototype Destructible Wall Visual Helper Enabled/Disabled
	FConsoleVariable Cvar_DestructibleWallHelperEnabled( "ScriptDebug.DestructibleWallHelperEnabled", 0 );

// @jmccarty allows the use of demo, WIP, non-shipping characters
#if EDITOR
	FConsoleVariable Cvar_CharacterShippingMode( "ScriptDebug.CharacterShippingMode", EShippingMode::WORK_IN_PROGRESS );
#else
	FConsoleVariable Cvar_CharacterShippingMode( "ScriptDebug.CharacterShippingMode", EShippingMode::SHIPPING );
#endif

	// allows you to trigger your own prox mines
	FConsoleVariable Cvar_DebugProxMine( "ScriptDebug.DebugProxMine", 0 );

	// forces shipping bases in dev maps
	FConsoleVariable Cvar_ForceNonDevBases( "ScriptDebug.ForceNonDevBases", 0 );

	// @jmccarty Allows us to tweak the amount of time between repetative pings
	FConsoleVariable Cvar_PingCooldown( "ScriptDebug.PingCooldown", 0.3f );

	// @jmccarty Allows us to turn on/off erroring when an invalid gameplay tag is found
	FConsoleVariable Cvar_CharacterSelectErrorOnMissingGameplayTag( "ScriptDebug.CharacterSelectErrorOnMissingGameplayTag", 0 );

	// @dbocek -- to revert defensive fix while trying to repro NC14765
	FConsoleVariable CVar_DisableDefensiveFixRocketAudioCrash( "ScriptDebug.DisableDefensiveFixRocketAudioCrash", 0, "No longer will check for valid SingleFire sounds on weapons that call Server_WeaponPlayFireSoundsSingle script function" );

	// @jmccarty Enables debug weapon serials
	FConsoleVariable Cvar_ShowDebugWeaponSerial( "UI.Debug.ShowDebugWeaponSerial", 1 );

	// @jmccarty Shows the vault pinned widget all the time, it no longer needs to be discovered
	FConsoleVariable Cvar_JadeDemonToggleShield( "ScriptDebug.JadeDemonToggleShield", 0 );

	// Toggle logging dialogue system warnings
	FConsoleVariable Cvar_ShowDialogueErrors( "ScriptDebug.ShowDialogueErrors", 0 );

	// @jmccarty Allows us to switch between PlayStation and Xbox glyphs
	FConsoleVariable Cvar_SimulatePlayStationDevice( "UI.SimulatePlayStationDevice", 0 );

	FConsoleVariable CVar_EnableDesaturation( f"ScriptDebug.EnableDesaturation", 1 );
	FConsoleVariable CVar_EnableDamageArrow( f"ScriptDebug.EnableDamageArrow", 0 );
	FConsoleVariable CVar_EnableRadialDamageIndicator( f"ScriptDebug.EnableRadialDamageIndicator", 1 );

	FConsoleVariable CVar_DebugTraining( f"ScriptDebug.DebugTraining", 0 );
	FConsoleVariable CVar_ShowDevBasesInTraining( f"ScriptDebug.ShowDevBasesInTraining", 0 );
	FConsoleVariable CVar_Training_OnlyEnemyBaseIsDevBase( f"ScriptDebug.OnlyEnemyBaseIsDevBase", 0 );
	FConsoleVariable CVar_Training_OnlyFriendlyBaseIsDevBase( f"ScriptDebug.OnlyFriendlyBaseIsDevBase", 0 );
	FConsoleVariable CVar_Training_TitleCard( f"ScriptDebug.TrainingTitleCard", 1 );
	FConsoleVariable CVar_TrainingNewObj( f"ScriptDebug.TrainingNewObj", 1 );

	FConsoleVariable CVar_AntiPeekEnabled( f"ScriptDebug.AntiPeekEnabled", 1 );

#if EDITOR
	FConsoleVariable CVar_AutoEndOnEmptyTeam( f"ScriptDebug.AutoEndOnEmptyTeam", 0 );
#else
	FConsoleVariable CVar_AutoEndOnEmptyTeam( f"ScriptDebug.AutoEndOnEmptyTeam", 1 );
#endif

#if EDITOR
	FConsoleVariable CVar_WaitingForPlayersForever( f"ScriptDebug.WaitingForPlayersForever", 1 );
#else
	FConsoleVariable CVar_WaitingForPlayersForever( f"ScriptDebug.WaitingForPlayersForever", 0 );
#endif

#if EDITOR
	FConsoleVariable CVar_EnableOutOfBounds( f"ScriptDebug.EnableOutOfBounds", 0 );
#else
	FConsoleVariable CVar_EnableOutOfBounds( f"ScriptDebug.EnableOutOfBounds", 1 );
#endif

	FConsoleVariable CVar_ShowZiplinePredictionStatus( f"ScriptDebug.ShowZiplinePredictionStatus", 0 );

	FConsoleVariable CVar_EmotesUseMTXBackend( f"ScriptDebug.EmotesUseMTXBackend", 1 );

	FConsoleVariable CVar_DisableBaseLoading( f"ScriptDebug.DisableBaseLoading", 0 );

	FConsoleVariable CVar_TimestampWeaponStateChanges( f"ScriptDebug.TimestampWeaponStateChanges", 0 );

	FConsoleVariable CVar_NewRaidMessaging( f"ScriptDebug.NewRaidMessaging", 0 );

	FConsoleVariable CVar_Camera_MoveSpeed( f"CustomCamera.MoveSpeed", 600 );
	FConsoleVariable CVar_Camera_TurnSpeed( f"CustomCamera.TurnSpeed", 20 );
	FConsoleVariable CVar_Camera_RollSpeed( f"CustomCamera.RollSpeed", 20 );
	FConsoleVariable CVar_Camera_ZSpeed( f"CustomCamera.ZSpeed", 100.0 );
	FConsoleVariable CVar_Camera_DecellerationFriction_Move( f"CustomCamera.DecellerationFriction_Move", 0 );
	FConsoleVariable CVar_Camera_DecellerationFriction_Turn( f"CustomCamera.DecellerationFriction_Turn", 0 );
	FConsoleVariable CVar_Camera_LockMovementZ( f"CustomCamera.LockMovementZ", 0 );
	FConsoleVariable CVar_Camera_SnapZoomTargetFOV( f"CustomCamera.SnapZoomTargetFOV", 20 );
	FConsoleVariable CVar_Camera_SnapZoomLerpDuration( f"CustomCamera.SnapZoomLerpDurationMS", 100 );
}