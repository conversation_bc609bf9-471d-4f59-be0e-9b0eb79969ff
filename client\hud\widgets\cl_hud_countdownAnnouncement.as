UCLASS( Abstract )
class UAS_CountdownAnnouncementWidget : UUserWidgetDefault
{
	UPROPERTY( NotEditable, BindWidget )
	UImage radial;

	UPROPERTY( NotEditable, BindWidget )
	UTextBlock titleText;

	UPROPERTY( NotEditable, BindWidget )
	UTextBlock timerText;

	UPROPERTY( NotEditable, Meta = ( BindWidgetAnim ), Transient )
	UWidgetAnimation appear;

	private int endTime = -1;

	void Start( FText text, int _endTime )
	{
		PlayAnimation( appear );
		titleText.SetText( text );
		endTime = _endTime;
	}

	UFUNCTION( BlueprintOverride )
	void Tick( FGeometry MyGeometry, float InDeltaTime )
	{
		if ( endTime < 0 )
			return;

		int currentTime = GetTimeMilliseconds();
		int timeLeft	= endTime - currentTime;

		if ( timeLeft <= -TO_SECONDS( 1 ) )
			RemoveFromParent();

		if ( timeLeft <= 0 )
			return;

		float timeLeftSeconds			= TO_SECONDS( timeLeft );
		float frac						= Math::Fmod( timeLeftSeconds, 1.0 );
		FNumberFormattingOptions format = GetNumberFormattingOptions( ERoundingMode::FromZero );
		format.SetMinMaxFractionalDigits( 0 );
		timerText.SetText( FText::AsNumber( timeLeftSeconds, format ) );
		radial.GetDynamicMaterial().SetScalarParameterValue( n"fill", frac );
	}
}