UCLASS( Abstract )
class UAS_ControllerBinding : UUserWidgetDefault
{
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UTextBlock Command1;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UTextBlock Command2;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UTextBlock Command3;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UTextBlock glyph1;
	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UTextBlock glyph2;
	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UTextBlock glyph3;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		ClearBinding();
	}

	void ClearBinding()
	{
		SetBinding( Text::EmptyText, Text::EmptyText );
	}

	void SetBinding( FText commandText1, FText keyText1, FText commandText2 = Text::EmptyText, FText keyText2 = Text::EmptyText, FText commandText3 = Text::EmptyText, FText keyText3 = Text::EmptyText )
	{
		Command1.SetText( commandText1 );
		Command2.SetText( commandText2 );
		Command3.SetText( commandText3 );

		glyph1.SetText( keyText1 );
		glyph2.SetText( keyText2 );
		glyph3.SetText( keyText3 );

		SetWidgetVisibilitySafe( Command1, Text::TextIsEmpty( commandText1 ) ? ESlateVisibility::Collapsed : ESlateVisibility::HitTestInvisible );
		SetWidgetVisibilitySafe( Command2, Text::TextIsEmpty( commandText2 ) ? ESlateVisibility::Collapsed : ESlateVisibility::HitTestInvisible );
		SetWidgetVisibilitySafe( Command3, Text::TextIsEmpty( commandText3 ) ? ESlateVisibility::Collapsed : ESlateVisibility::HitTestInvisible );
		SetWidgetVisibilitySafe( glyph1, Text::TextIsEmpty( keyText1 ) ? ESlateVisibility::Collapsed : ESlateVisibility::HitTestInvisible );
		SetWidgetVisibilitySafe( glyph2, Text::TextIsEmpty( keyText2 ) ? ESlateVisibility::Collapsed : ESlateVisibility::HitTestInvisible );
		SetWidgetVisibilitySafe( glyph3, Text::TextIsEmpty( keyText3 ) ? ESlateVisibility::Collapsed : ESlateVisibility::HitTestInvisible );
	}
}

UCLASS( Abstract )
class UAS_ControlSchemeWidget : UUserWidgetDefault
{
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UWidget BindingSelect;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UWidget BindingStart;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UWidget BindingLS;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UWidget BindingLB;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UWidget BindingLT;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UWidget BindingRS;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UWidget BindingRB;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UWidget BindingRT;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UWidget BindingA;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UWidget BindingB;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UWidget BindingX;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UWidget BindingY;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UWidget BindingUp;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UWidget BindingDown;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UWidget BindingLeft;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UWidget BindingRight;

	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UAS_ControllerBinding KeyBindingSelect;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UAS_ControllerBinding KeyBindingStart;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UAS_ControllerBinding KeyBindingLS;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UAS_ControllerBinding KeyBindingLB;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UAS_ControllerBinding KeyBindingLT;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UAS_ControllerBinding KeyBindingRS;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UAS_ControllerBinding KeyBindingRB;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UAS_ControllerBinding KeyBindingRT;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UAS_ControllerBinding KeyBindingA;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UAS_ControllerBinding KeyBindingB;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UAS_ControllerBinding KeyBindingX;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UAS_ControllerBinding KeyBindingY;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UAS_ControllerBinding KeyBindingUp;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UAS_ControllerBinding KeyBindingDown;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UAS_ControllerBinding KeyBindingLeft;
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UAS_ControllerBinding KeyBindingRight;

	TMap<UAS_ControllerBinding, UWidget> BindingMap;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		BindingMap.Add( KeyBindingSelect, BindingSelect );
		BindingMap.Add( KeyBindingStart, BindingStart );
		BindingMap.Add( KeyBindingLS, BindingLS );
		BindingMap.Add( KeyBindingLB, BindingLB );
		BindingMap.Add( KeyBindingLT, BindingLT );
		BindingMap.Add( KeyBindingRS, BindingRS );
		BindingMap.Add( KeyBindingRB, BindingRB );
		BindingMap.Add( KeyBindingRT, BindingRT );
		BindingMap.Add( KeyBindingA, BindingA );
		BindingMap.Add( KeyBindingB, BindingB );
		BindingMap.Add( KeyBindingX, BindingX );
		BindingMap.Add( KeyBindingY, BindingY );
		BindingMap.Add( KeyBindingUp, BindingUp );
		BindingMap.Add( KeyBindingDown, BindingDown );
		BindingMap.Add( KeyBindingLeft, BindingLeft );
		BindingMap.Add( KeyBindingRight, BindingRight );

		ClearAllBindings();

		APlayerController controller = GetOwningPlayer();
		if ( IsValid( controller ) )
		{
			UCommonInputSubsystem inputSubsystem = UCommonInputSubsystem::Get( controller.GetLocalPlayer() );
			if ( IsValid( inputSubsystem ) )
				inputSubsystem.OnInputMethodChanged.AddUFunction( this, n"OnInputMethodChanged" );
		}

		// TODO @jmccarty @robin, this is a hack to ensure hud globals are ready
		WaitForHudGlobalsAndInputSystem();
	}

	UFUNCTION( NotBlueprintCallable )
	void WaitForHudGlobalsAndInputSystem()
	{
		const UAS_HudGlobals hudGlobals		 = GetHUDGlobals();
		UAS_InputSystem inputSystem			 = InputSystem();
		APlayerController controller		 = GetOwningPlayer();
		UCommonInputSubsystem inputSubsystem = IsValid( controller ) ? UCommonInputSubsystem::Get( controller.GetLocalPlayer() ) : nullptr;

		if ( !IsValid( hudGlobals ) || !IsValid( inputSystem ) || !IsValid( inputSubsystem ) )
		{
			System::SetTimer( this, n"WaitForHudGlobalsAndInputSystem", 0.1f, false );
			return;
		}

		UpdateAllDefaultBindings( inputSubsystem.GetCurrentInputType() );
	}

	UFUNCTION()
	private void OnInputMethodChanged( ECommonInputType bNewInputType )
	{
		UpdateAllDefaultBindings( bNewInputType );
	}

	void ClearAllBindings()
	{
		for ( auto Elem : BindingMap )
			ClearBinding( Elem.GetKey() );
	}

	void ClearBinding( UAS_ControllerBinding Widget )
	{
		SetBinding( Widget, Text::EmptyText, Text::EmptyText );
	}

	void SetBinding( UAS_ControllerBinding Widget, FText commandText1, FText keyText1, FText commandText2 = Text::EmptyText, FText keyText2 = Text::EmptyText, FText commandText3 = Text::EmptyText, FText keyText3 = Text::EmptyText )
	{
		SetWidgetVisibilitySafe( BindingMap[Widget], Text::TextIsEmpty( commandText1 ) ? ESlateVisibility::Hidden : ESlateVisibility::HitTestInvisible );
		Widget.SetBinding( commandText1, keyText1, commandText2, keyText2, commandText3, keyText3 );
	}

	void UpdateAllDefaultBindings( ECommonInputType inputType )
	{
		UAS_InputSystem inputSystem = InputSystem();
		if ( !IsValid( inputSystem ) )
		{
			Log( f"Attempted to update control scheme widget, but the Input System but it hasn't been created yet." );
			return;
		}

		ClearAllBindings();

		SetShoulderStickBindings( inputType );
		SetFaceButtonBindings( inputType );
		SetDPadBindings( inputType );
		SetMiscBindings( inputType );
	}

	void SetShoulderStickBindings( ECommonInputType inputType )
	{
		FText txtTac = GetLocalizedText( Localization::KeyBindActions, "tactical" );
		FText keyTac = GetKeybindGlyph( inputType, n"SpecialWeapon1" );
		SetBinding( KeyBindingLB, txtTac, keyTac );

		FText txtAxe   = GetLocalizedText( Localization::KeyBindActions, "melee" );
		FText keyAxe   = GetKeybindGlyph( inputType, n"MeleeWeapon" );
		FText txtThrow = GetLocalizedText( Localization::KeyBindActions, "melee_hold" );
		FText keyThrow = GetKeybindGlyph( inputType, n"MeleeWeapon" );
		SetBinding( KeyBindingRS, txtAxe, keyAxe, txtThrow, keyThrow );

		FText txtAim = GetLocalizedText( Localization::KeyBindActions, "aim" );
		FText keyAim = GetKeybindGlyph( inputType, n"Aim" );
		SetBinding( KeyBindingLT, txtAim, keyAim );

		FText txtFire = GetLocalizedText( Localization::KeyBindActions, "fire" );
		FText keyFire = GetKeybindGlyph( inputType, n"Fire" );
		SetBinding( KeyBindingRT, txtFire, keyFire );

		FText txtPing = GetLocalizedText( Localization::KeyBindActions, "ping_wheel" );
		FText keyPing = GetKeybindGlyph( inputType, n"Ping" );
		SetBinding( KeyBindingRB, txtPing, keyPing );
	}

	void SetFaceButtonBindings( ECommonInputType inputType )
	{
		bool isGamepad = inputType == ECommonInputType::Gamepad;

		FText txtJump  = GetLocalizedText( Localization::KeyBindActions, "jump" );
		FText keyJump  = GetKeybindGlyph( inputType, n"Jump" );
		FText txtMount = isGamepad ? GetLocalizedText( Localization::KeyBindActions, "mount_dbl_tap" ) : GetLocalizedText( Localization::KeyBindActions, "mount" );
		FText keyMount = GetKeybindGlyph( inputType, n"Jump", n"Mount" );
		FText txtEject = GetLocalizedText( Localization::KeyBindActions, "mount_eject" );
		FText keyEject = GetKeybindGlyph( inputType, n"Jump" );
		SetBinding( KeyBindingA, txtJump, keyJump, txtMount, keyMount, txtEject, keyEject );

		FText txtCrouch = GetLocalizedText( Localization::KeyBindActions, "crouch_slide" );
		FText keyCrouch = GetKeybindGlyph( inputType, n"Crouch" );
		SetBinding( KeyBindingB, txtCrouch, keyCrouch );

		FText txtReload = GetLocalizedText( Localization::KeyBindActions, "reload" );
		FText keyReload = GetKeybindGlyph( inputType, n"UseReload", n"Reload" );
		FText txtMod	= GetLocalizedText( Localization::KeyBindActions, "weapon_mod" );
		FText keyMod	= GetKeybindGlyph( inputType, n"UseReload", n"reload" );
		FText txtUse	= GetLocalizedText( Localization::KeyBindActions, "use" );
		FText keyUse	= GetKeybindGlyph( inputType, n"UseReload", n"Use" );
		SetBinding( KeyBindingX, txtReload, keyReload, txtMod, keyMod, txtUse, keyUse );

		FText txtSwitch = GetLocalizedText( Localization::KeyBindActions, "switch_weapon" );
		FText keySwitch = GetKeybindGlyph( inputType, n"AltUseSwitchWeapon", n"SwitchWeapon" );
		FText txtRaid	= isGamepad ? GetLocalizedText( Localization::KeyBindActions, "raid_tool_hold" ) : GetLocalizedText( Localization::KeyBindActions, "raid_tool" );
		FText keyRaid	= GetKeybindGlyph( inputType, n"CycleGrenade" );
		FText txtDismnt = GetLocalizedText( Localization::KeyBindActions, "dismount" );
		FText keyDismnt = GetKeybindGlyph( inputType, n"AltUseSwitchWeapon", n"Mount" );
		SetBinding( KeyBindingY, txtSwitch, keySwitch, txtRaid, keyRaid, txtDismnt, keyDismnt );
	}

	void SetDPadBindings( ECommonInputType inputType )
	{
		FText txtCraft = GetLocalizedText( Localization::KeyBindActions, "craft_wheel" );
		FText keyCraft = GetKeybindGlyph( inputType, n"Craft" );
		SetBinding( KeyBindingUp, txtCraft, keyCraft );

		FText txtHeal = GetLocalizedText( Localization::KeyBindActions, "heal" );
		FText keyHeal = GetKeybindGlyph( inputType, n"Heal" );
		SetBinding( KeyBindingDown, txtHeal, keyHeal );

		FText txtGren = GetLocalizedText( Localization::KeyBindActions, "grenade" );
		FText keyGren = GetKeybindGlyph( inputType, n"SpecialWeapon0" );
		SetBinding( KeyBindingRight, txtGren, keyGren );

		FText txtTele = GetLocalizedText( Localization::KeyBindActions, "teleport_wheel" );
		FText keyTele = GetKeybindGlyph( inputType, GameConst::TELEPORT_HOME_ACTION );
		SetBinding( KeyBindingLeft, txtTele, keyTele );
	}

	void SetMiscBindings( ECommonInputType inputType )
	{
		bool isGamepad = inputType == ECommonInputType::Gamepad;

		FText txtMap = GetLocalizedText( Localization::KeyBindActions, "map" );
		FText keyMap = GetKeybindGlyph( inputType, n"TrackerToggle", n"Maximap" );
		SetBinding( KeyBindingSelect, txtMap, keyMap );

		FText txtInv  = GetLocalizedText( Localization::KeyBindActions, "inventory" );
		FText keyInv  = GetKeybindGlyph( inputType, n"InGameMenu", n"Inventory" );
		FText txtMenu = isGamepad ? GetLocalizedText( Localization::KeyBindActions, "main_menu_hold" ) : GetLocalizedText( Localization::KeyBindActions, "main_menu" );
		FText keyMenu = GetKeybindGlyph( inputType, n"InGameMenu" );
		SetBinding( KeyBindingStart, txtInv, keyInv, txtMenu, keyMenu );
	}
}