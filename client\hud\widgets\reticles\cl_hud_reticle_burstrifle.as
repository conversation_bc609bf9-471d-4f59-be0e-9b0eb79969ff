UCLASS(Abstract)
class UAS_Reticle_BurstRifle : UAS_ReticleWidget
{
	bool isEngaged = false;
	float deployAnimTime = 0.3;
	float deployAnimPlaybackSpeed = 0;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset chargeSound_stage1;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset chargeSound_stage2;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset chargeSound_stage3;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset chargeSound_failed;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset chargeSound_complete;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset chargeSound_chargedIdle;
	
	UPROPERTY( NotVisible, Transient, meta = ( BindWidgetAnim ) )
	UWidgetAnimation anim_supercharge_stabilizer_deploy;

	UPROPERTY( EditDefaultsOnly )
	FScopeMaterialParameters superchargeDisabledParams;

	UPROPERTY( EditDefaultsOnly )
	FScopeMaterialParameters superchargeEnabledParams;

	ANCPlayerCharacter renderPawn;

	UFUNCTION(BlueprintOverride)
	void Construct()
	{
		Super::Construct();
		ScriptCallbacks().RegisterSignalCallback( Signals::WEAP_SUPERCHARGE_CHARGE_INTERRUPTED, this, n"Signal_SuperchargeInterrupted" );
	}
	
	void InitializeReticle(ANCWeapon weapon) override
	{
		Super::InitializeReticle(weapon);

		if ( !IsValid( weapon ) )
			return;

		deployAnimPlaybackSpeed = 1 / deployAnimTime;

		FReticleAnimationData superchargeActiveAnimData = FReticleAnimationData( anim_supercharge_stabilizer_deploy, deployAnimPlaybackSpeed );
		superchargeActiveAnimData.animSound_enable = chargeSound_complete;
		superchargeActiveAnimData.animSound_enabledIdle = chargeSound_chargedIdle;

		RegisterReticleAnimation( WeaponConst::Supercharge::RETICLE_ANIM_KEY, superchargeActiveAnimData );
		PlayReticleAnimationInstant( WeaponConst::Supercharge::RETICLE_ANIM_KEY, IsModActiveOnWeapon( weapon, WeaponConst::Supercharge::MODNAME_STABILIZED ) );

		PlayReticleAnimationInstant( WeaponConst::Optics::MODNAME_MARKSMAN, IsModActiveOnWeapon( ownerWeapon, GetModNameForAttachmentIndex( GameplayTags::Weapons_Attachments_Optics_Marksman ) ) );

		renderPawn = ownerWeapon.WeaponOwner;

		// TODO: Instantiate super charge anim thread class, populate, then use for registration
		UAS_Thread_ScopeMaterialAnimation_Supercharge burstRifleSuperchargeAnimThread_Enabled = Cast<UAS_Thread_ScopeMaterialAnimation_Supercharge>( CreateThread( UAS_Thread_ScopeMaterialAnimation_Supercharge::StaticClass(), this ) );
		burstRifleSuperchargeAnimThread_Enabled.chargeSound_stage1 = chargeSound_stage1;
		burstRifleSuperchargeAnimThread_Enabled.chargeSound_stage2 = chargeSound_stage2;
		burstRifleSuperchargeAnimThread_Enabled.chargeSound_stage3 = chargeSound_stage3;
		burstRifleSuperchargeAnimThread_Enabled.chargeSound_failed = chargeSound_failed;
		burstRifleSuperchargeAnimThread_Enabled.aimpointSettings = superchargeEnabledParams;
		
		RegisterScopeMaterialAnimation( WeaponConst::Supercharge::SIGHT_MATERIAL_ANIM_KEY, superchargeEnabledParams, superchargeDisabledParams, burstRifleSuperchargeAnimThread_Enabled, nullptr );
		PlayScopeMaterialAnimation( WeaponConst::Supercharge::SIGHT_MATERIAL_ANIM_KEY, EScopeMaterialAnimationPlayMode::SET_DISABLED_STATE );
	}
	void UpdateStateAnimations() override
	{
		Super::UpdateStateAnimations();

		PlayReticleAnimation( WeaponConst::Supercharge::RETICLE_ANIM_KEY, IsModActiveOnWeapon( ownerWeapon, WeaponConst::Supercharge::MODNAME_STABILIZED ) );

		UpdateSuperchargeState( this, ownerWeapon, renderPawn );
	}

	void OnLocalPlayerADSChanged(bool isADS) override
	{
		Super::OnLocalPlayerADSChanged(isADS);

		PlayReticleAnimation( WeaponConst::Supercharge::RETICLE_ANIM_KEY, IsModActiveOnWeapon( ownerWeapon, WeaponConst::Supercharge::MODNAME_STABILIZED ) );
		UpdateSuperchargeState( this, ownerWeapon, renderPawn );
	}

	UFUNCTION()
	private void Signal_SuperchargeInterrupted( FName signalName, UObject signalSource )
	{
		PlayScopeMaterialAnimation( WeaponConst::Supercharge::SIGHT_MATERIAL_ANIM_KEY, EScopeMaterialAnimationPlayMode::SET_DISABLED_STATE );
		UpdateSuperchargeState( this, ownerWeapon, renderPawn );
	}

	UFUNCTION(BlueprintOverride)
	void Destruct()
	{
		Super::Destruct();
		ScriptCallbacks().UnRegisterSignalCallback( Signals::WEAP_SUPERCHARGE_CHARGE_INTERRUPTED, this, n"Signal_SuperchargeInterrupted" );
	}
}