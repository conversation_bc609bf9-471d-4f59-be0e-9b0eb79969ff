delegate FHUDHintData FOverrideHUDHint();

UCLASS( Abstract )
class UAS_MainHUDWidget : UNC_DisplayWidget
{
	UPROPERTY( NotEditable )
	FText playerName;

	////////////////// Bind widgets //////////////////
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidgetOptional ) )
	UAS_PlayerWidget localPlayerStatus;

	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	UAS_TeammateStatusContainer teammateStatusContainerWidget;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UAS_PrimaryWeaponWidget weaponStatusWidget;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UOverlay baseRaidShieldOverlay;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UProgressBar baseRaidShieldWidget;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UHorizontalBox baseRaidShieldGeneratorsBox;

	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<UUserWidget> shieldGeneratorHUDClass;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UPanelWidget alivePanel;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UAS_TeammateDisconnectedWidget teammateDisconnectedMessage;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UAS_KillMessage killMessage;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UAS_KillMessage assistMessage;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UAS_SmallHUDMessage smallCenterMessage;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UTextBlock axePerfectMessage;

	UPROPERTY( BlueprintReadOnly, NotEditable, Transient, Meta = ( BindWidgetAnim ) )
	UWidgetAnimation axePerfectAnim;
	UPROPERTY( BlueprintReadOnly, NotEditable, Transient, Meta = ( BindWidgetAnim ) )
	UWidgetAnimation axeBadAnim;
	UPROPERTY( BlueprintReadOnly, NotEditable, Transient, Meta = ( BindWidgetAnim ) )
	UWidgetAnimation hideHud;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UHorizontalBox inputContextBox;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UAS_HUDPromptManager hudPromptManager;
	FOverrideHUDHint hudHintOverrideDelegate;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UPanelWidget outOfBaseDetectionPanel;

	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<UAS_InputContextWidget> inputContextWidgetClass;

	UPROPERTY( NotEditable )
	TMap<FName, UAS_InputContextWidget> activeInputContexts;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UNCDeferredWidget subtitleManagerDeferredWidget;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UNCDeferredWidget enemiesInBaseDeferredWidget;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_AbilityStatusWidget_v2 tacticalAbility;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_AbilityStatusWidget_v2 grenadeAbility;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_AbilityStatusWidget_v2 healAbility;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_AbilityStatusWidget_v2 mountAbility;

	////////////////////////////////////////////////////////////////////////////////

	UPROPERTY( NotEditable, BlueprintReadOnly )
	AAS_HUD myHUD;

	UPROPERTY( NotEditable, BlueprintReadOnly )
	float timeLeft;

	int playerCount;

	protected AAS_PlayerEntity localASPlayer;

	FNCDeferredScriptAction updateHintTextAction;

	private FHUDHintData scriptedHint;
	private int scriptedHintEndTime;
	private FTimerHandle scriptedHintTimerHandle;

	private FHUDHintData lowPrioScriptedHint;
	private bool lowPrioScriptedHintIsEndless;
	private int lowPrioScriptedHintEndTime;
	private FTimerHandle lowPrioScriptedHintTimerHandle;

	private bool trainingDisableScopeSwapHint = false;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		myHUD = Cast<AAS_HUD>( GetOwningPlayer().GetHUD() );
		ScriptAssert( IsValid( myHUD ), "HUD not valid" );

		ClientCallbacks().OnPlayerChangedTeam.AddUFunction( this, n"Callback_OnPlayerChangedTeam" );
		ClientCallbacks().OnPlayerCreated.AddUFunction( this, n"Callback_OnPlayerCreated" );
		ClientCallbacks().OnPlayerDestroyed.AddUFunction( this, n"Callback_OnPlayerStateDestroyed" );
		ClientCallbacks().OnLocalWeaponClipAmmoChanged.AddUFunction( this, n"OnLocalWeaponClipAmmoChanged" );
		ClientCallbacks().OnLocalWeaponStockpileAmmoChanged.AddUFunction( this, n"OnLocalWeaponStockAmmoChanged" );
		ClientCallbacks().OnPawnHealthChanged.AddUFunction( this, n"OnPawnHealthChanged" );

		ClientCallbacks().OnGamePhaseChanged.AddUFunction( this, n"OnGamePhaseChanged" );
		OnGamePhaseChanged( -1, GetGamePhase() );

		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();

		if ( IsValid( scriptCallbacks ) )
		{
			scriptCallbacks.RegisterSignalCallback( Signals::WEAP_LOCAL_PLAYER_ADS_CHANGED, this, n"Signal_LocalADSChangedDelayed" );
			scriptCallbacks.RegisterSignalCallback( Signals::WEAP_LOCAL_PLAYER_MODS_CHANGED, this, n"Signal_LocalModsChanged" );
			scriptCallbacks.client_onTrainingWeaponSlotEnabledChanged.AddUFunction( this, n"OnTrainingWeaponSlotEnabledChanged" );
		}

		TArray<ANCPlayerCharacter> allPS = UNCUtils::Client_GetAllPlayers( GetOwningPlayer() );
		for ( ANCPlayerCharacter PS : allPS )
		{
			Callback_OnPlayerCreated( PS );
		}

		localASPlayer = Client_GetLocalASPawn();
		playerName	  = localASPlayer.GetPlayerNameAsText();
		localASPlayer.healsBackpackComponent.OnContentsChangedDelegate.AddUFunction( this, n"OnAmmoBackpackContentsChanged" );

		if ( IsValid( localASPlayer ) )
		{
			SetActiveWeapon( EViewmodelArm::MainHand, nullptr, localASPlayer.GetActiveWeaponInHand( EViewmodelArm::MainHand ) );
			SetActiveWeapon( EViewmodelArm::AltHand, nullptr, localASPlayer.GetActiveWeaponInHand( EViewmodelArm::AltHand ) );

			if ( IsValid( ClientTrainingMode() ) )
			{
				localASPlayer.mountData.net_mountEnabled.OnReplicated().AddUFunction( this, n"OnMountEnabledChanged" );
			}
		}

		updateHintTextAction.BindUFunction( this, n"UpdateHintTextDeferred" );

		if ( IsValid( localPlayerStatus ) )
			localPlayerStatus.SetPlayer( localASPlayer );
		FHealthChangedInfo healthInfo;
		healthInfo.pawn		 = localASPlayer;
		healthInfo.newHealth = localASPlayer.GetHealth();
		OnPawnHealthChanged( healthInfo );

		ClientCallbacks().OnPlayerSpectateCameraChanged.AddUFunction( this, n"OnSpectateEntChanged" );

		UpdateAlivePanel();

		Thread( this, n"Hack_TrackActiveWeaponState" );

		subtitleManagerDeferredWidget.Show();

		if ( IsValid( scriptCallbacks ) )
		{
			scriptCallbacks.shared_OnDomeEnemyCountChanged.AddUFunction( this, n"OnDomeEnemyCountChanged" );
		}
	}

	UFUNCTION()
	void OnWeaponSlotsDisabledChanged( int old, int new )
	{

	}

	UFUNCTION()
	private void OnAmmoBackpackContentsChanged( UBackpackComponent backpack )
	{
		UpdateHintText();
	}

	void UpdateAlivePanel()
	{
		if ( !IsAlive( localASPlayer ) || !myHUD.ShouldAliveHudBeVisible() || GetGamePhase() >= GamePhase::WINNER_DETERMINED )
			SetWidgetVisibilitySafe( alivePanel, ESlateVisibility::Hidden );
		else
			SetWidgetVisibilitySafe( alivePanel, ESlateVisibility::HitTestInvisible );
	}

	UFUNCTION()
	private void OnSpectateEntChanged( ANCPlayerCharacter player )
	{
		UpdateAlivePanel();

		// Turned off since this is handled in the raid respawn menu
		// Should probably be a game mode setting or something
		//
		// ESlateVisibility oldVis = spectatePanel.GetVisibility();

		// ANCPlayerController controller = Cast<ANCPlayerController>(GetOwningPlayer());
		// AActor spectateEnt = IsValid( controller.GetClientOverrideSpectateActor() ) ? controller.GetClientOverrideSpectateActor() : player.GetSpectateEntity();

		// if ( ShouldShowSpectatePanel( spectateEnt ) )
		// {
		// 	SetWidgetVisibilitySafe(spectatePanel,  ESlateVisibility::HitTestInvisible );
		// }
		// else
		// {
		// 	SetWidgetVisibilitySafe(spectatePanel,  ESlateVisibility::Collapsed );
		// }

		// Mount, granade, and ability hud icons are updated on inventory changed
		// However, weapon data asset id is not replicated to client on inventory changed, so these icons cannot get correct icons from weapon data asset
		// We need to icons once again after weapon got correct data asset
		UpdateOffhandWeapons();
	}

	bool ShouldShowSpectatePanel( AActor currentSpectateActor )
	{
		if ( !IsValid( currentSpectateActor ) )
			return false;

		if ( currentSpectateActor.Class.IsChildOf( ANCPlayerCharacter::StaticClass() ) )
			return true;

		ANCDefaultActor castedActor = Cast<ANCDefaultActor>( currentSpectateActor );
		if ( IsValid( castedActor ) )
		{
			return IsValid( castedActor.GetOwnerPlayer() );
		}

		return false;
	}

	private int nextAllowWeaponHintTextTime;

	void ClearWeaponHintTextPause()
	{
		PauseWeaponHintText( 0 );
	}

	void PauseWeaponHintText( float32 forDuration )
	{
		int newTime = GetTimeMilliseconds() + TO_MILLISECONDS( forDuration );
		if ( newTime <= nextAllowWeaponHintTextTime )
		{
			return;
		}

		nextAllowWeaponHintTextTime = newTime;
		UpdateHintText();
		System::SetTimer( this, n"UpdateHintTextTimer", forDuration + 0.1, false );
	}

	private int weaponHintHoldTimeStart = -1;
	void SetWeaponHintTextInputStartTime( int startTimeMS )
	{
		weaponHintHoldTimeStart = startTimeMS;
		UpdateHintTextProgressBar();

		// Show ads hint widget if you press cycle button early
		if ( startTimeMS > 0 )
		{
			ClearWeaponHintTextPause();
		}
	}

	void ResetWeaponHintTextInputStartTime()
	{
		SetWeaponHintTextInputStartTime( -1 );
		UpdateHintText();
	}

	const float32 CYCLE_SCOPE_HOLD_DURATION = 0.2f;
	void UpdateHintTextProgressBar()
	{
		if ( weaponHintHoldTimeStart <= 0 )
		{
			hudPromptManager.SetHintProgressFrac( 0 );
			return;
		}

		float32 frac = TO_SECONDS( GetTimeMilliseconds() - weaponHintHoldTimeStart ) / CYCLE_SCOPE_HOLD_DURATION;
		hudPromptManager.SetHintProgressFrac( frac );
	}

	UFUNCTION()
	void UpdateHintTextTimer()
	{
		UpdateHintText();
	}

	void UpdateHintText()
	{
		updateHintTextAction.ExecuteOnceLater( ENCDeferredScriptActionExecutionTime::UpdateWidgetsBeforeHUD );
	}

	// Negative duration = last forever
	void SetScriptedHint( FHUDHintData data, float32 duration = 5.0 )
	{
		scriptedHint		= data;
		if ( duration > 0 )
		{
			scriptedHintEndTime = GetGameTimeMS() + TO_MILLISECONDS( duration );
		}
		else
		{
			scriptedHintEndTime = MAX_int32;
		}
		UpdateHintText();

		if ( System::TimerExistsHandle( scriptedHintTimerHandle ) )
		{
			System::ClearAndInvalidateTimerHandle( scriptedHintTimerHandle );
		}

		if ( duration > 0 )
		{
			scriptedHintTimerHandle = System::SetTimer( this, n"UpdateHintTextTimer", duration + 0.2, false );
		}
	}

	void ForceClearScriptedHint()
	{
		scriptedHint		= FHUDHintData();
		scriptedHintEndTime = 0;
		UpdateHintText();

		if ( System::TimerExistsHandle( scriptedHintTimerHandle ) )
		{
			System::ClearAndInvalidateTimerHandle( scriptedHintTimerHandle );
		}
	}

	void ClearScriptedHint( FHUDHintData data )
	{
		if ( data.hintString.IdenticalTo( scriptedHint.hintString ) )
		{
			ForceClearScriptedHint();
		}
	}

	// Default scripted hints will override these. Negative duration = last forever
	void SetLowPrioScriptedHint( FHUDHintData data, float32 duration = 5.0 )
	{
		if ( System::TimerExistsHandle( lowPrioScriptedHintTimerHandle ) )
		{
			System::ClearAndInvalidateTimerHandle( lowPrioScriptedHintTimerHandle );
		}

		lowPrioScriptedHint			= data;
		if ( duration > 0 )
		{
			lowPrioScriptedHintEndTime = GetGameTimeMS() + TO_MILLISECONDS( duration );
			lowPrioScriptedHintTimerHandle = System::SetTimer( this, n"UpdateHintTextTimer", duration + 0.2, false );
			lowPrioScriptedHintIsEndless = false;
		}
		else
		{
			lowPrioScriptedHintIsEndless = true;
			lowPrioScriptedHintEndTime = MAX_int32;
		}

		UpdateHintText();
		
	}

	void ForceClearLowPrioScriptedHint()
	{
		lowPrioScriptedHint		= FHUDHintData();
		lowPrioScriptedHintEndTime = 0;
		UpdateHintText();

		if ( System::TimerExistsHandle( lowPrioScriptedHintTimerHandle ) )
		{
			System::ClearAndInvalidateTimerHandle( lowPrioScriptedHintTimerHandle );
		}
	}

	void ClearLowPrioScriptedHint( FHUDHintData data )
	{
		if ( data.hintString.IdenticalTo( lowPrioScriptedHint.hintString ) )
		{
			ForceClearLowPrioScriptedHint();
		}
	}


	UFUNCTION()
	private void UpdateHintTextDeferred()
	{
		if ( hudHintOverrideDelegate.IsBound() )
		{
			hudPromptManager.SetHint( hudHintOverrideDelegate.Execute() );
			return;
		}

		if ( GetGameTimeMS() < scriptedHintEndTime )
		{
			hudPromptManager.SetHint( scriptedHint );
			return;
		}
		else
		{
			hudPromptManager.ClearHint();
		}

		if ( localASPlayer.GetIsDowned() )
		{
			hudPromptManager.ClearHint();
			return;
		}

		int healCount = localASPlayer.CountItemsOfTypeInAllBackpacks( ELootType::HealItem );
		if ( !GameModeDefaults().GamemodeRules_ShieldRegenEnabled )
		{
			if ( localASPlayer.GetMaxShieldHealth() > 0 && localASPlayer.GetShieldHealth() < 25 && healCount > 0 )
			{
				hudPromptManager.SetHint( FHUDHintData( GetLocalizedText( Localization::HUDMainWidget, f"hint_heal" ), n"Heal" ) );
				return;
			}
		}

		ANCWeapon lastPrimary  = localASPlayer.GetCurrentOrLastActivePrimaryWeapon();
		ANCWeapon activeWeapon = localASPlayer.GetActiveWeapon();
		if ( IsValid( lastPrimary ) && activeWeapon == lastPrimary )
		{
			int clipAmmo  = lastPrimary.GetClipAmmo();
			int maxAmmo	  = lastPrimary.GetClipAmmoMax();
			int stockpile = lastPrimary.GetStockpileAmmo();
			if ( clipAmmo < float32( maxAmmo ) * 0.5 || stockpile == 0 )
			{
				if ( stockpile == 0 )
				{
					if ( clipAmmo == 0 )
					{
						FHUDHintData hintData = FHUDHintData( GetLocalizedText( Localization::HUDMainWidget, f"hint_no_ammo" ) );
						hintData.hintStyle	  = EHintStyle::CRITICAL_WARN;
						hudPromptManager.SetHint( hintData );
						return;
					}
					else
					{
						hudPromptManager.SetHint( FHUDHintData( GetLocalizedText( Localization::HUDMainWidget, f"hint_low_ammo" ) ) );
						return;
					}
				}
				else if ( lastPrimary.GetWeaponState() != EWeaponState::Reloading )
				{
					UTexture2D hintTexture;
					FHUDHintData hintData = FHUDHintData( GetLocalizedText( Localization::HUDMainWidget, f"hint_reload" ), n"UseReload", n"Reload" );
					if ( localASPlayer.GetStatusEffectValue( GameplayTags::StatusEffect_Enraged ) >= 1.0 )
					{
						UAS_PassiveDataStruct data = Classes().GetClassData( GameplayTags::Classes_Class_Redmane ).passive;
						if ( IsValid( data ) )
						{
							hintTexture = data.icon;
							hintData.icon		  = hintTexture;
							hintData.hintStyle	  = EHintStyle::ICON;
						}
					}
					hudPromptManager.SetHint( hintData );
					return;
				}
			}
		}

		if ( !GameModeDefaults().GamemodeRules_ShieldRegenEnabled )
		{
			if ( localASPlayer.GetMaxShieldHealth() > 0 && localASPlayer.GetMaxShieldHealth() - localASPlayer.GetShieldHealth() > 25 && healCount > 0 )
			{
				hudPromptManager.SetHint( FHUDHintData( GetLocalizedText( Localization::HUDMainWidget, f"hint_heal" ), n"Heal" ) );
				return;
			}
		}

		// Scope swap
		if ( localASPlayer.IsADS() && IsValid( activeWeapon ) && !trainingDisableScopeSwapHint )
		{
			/* do while false*/ while ( true )
			{
				FName weaponClass = activeWeapon.GetWeaponClass();
				if ( !IsValidWeaponClassName( weaponClass ) )
				{
					break;
				}

				if ( activeWeapon.WeaponIsSwappingScopes() )
				{
					break;
				}

				TArray<FName> opticMods = Weapons().GetOpticModNamesForWeaponID( MakeWeaponId( activeWeapon ) );
				int numOpticMods		= opticMods.Num();
				if ( numOpticMods <= 1 )
				{
					break;
				}

				int idx_selectedOptic = 0;
				for ( int i = 0; i < numOpticMods; i++ )
				{
					if ( IsModActiveOnWeapon( activeWeapon, opticMods[i] ) )
					{
						idx_selectedOptic = i;
						break;
					}
				}

				if ( activeWeapon.GetWeaponState() != EWeaponState::Idle )
				{
					break;
				}

				if ( GetTimeMilliseconds() < nextAllowWeaponHintTextTime )
				{
					break;
				}

				int idx_nextOptic					 = ( idx_selectedOptic + 1 ) % numOpticMods;
				FName nextMod						 = opticMods[idx_nextOptic];
				FWeaponAttachmentData attachmentData = GetAttachmentData( nextMod );

				FHUDHintData hintData = FHUDHintData( attachmentData.name, n"CycleScope" );
				hintData.hintStyle	  = EHintStyle::SCOPE_SWAP;
				hudPromptManager.SetHint( hintData );
				return;
			}
		}

		if ( shouldShowLoadoutHint && GameModeDefaults().GamemodeRules_LoadoutHintEnabled )
		{
			hudPromptManager.SetHint( FHUDHintData( GetLocalizedText( Localization::HUDMainWidget, f"hint_changeloadout" ), n"ToggleLoadoutMenu" ) );
			return;
		}
		

		if ( GetGameTimeMS() < lowPrioScriptedHintEndTime )
		{
			hudPromptManager.SetHint( lowPrioScriptedHint );
			return;
		}

		hudPromptManager.ClearHint();
	}

	UFUNCTION( NotBlueprintCallable )
	void OnPawnHealthChanged( FHealthChangedInfo healthInfo )
	{
		if ( healthInfo.pawn == localASPlayer )
		{
			UpdateHintText();

			if ( ( healthInfo.oldHealth <= 0 && healthInfo.newHealth > 0 ) || ( healthInfo.oldHealth > 0 && healthInfo.newHealth <= 0 ) )
				UpdateAlivePanel();
		}
	}

	UFUNCTION( BlueprintEvent )
	protected void OnGamePhaseChanged( int OldState, int NewState )
	{
		UpdateHintText();
	}

	UFUNCTION()
	private void OnLocalWeaponClipAmmoChanged( ANCWeapon Weapon, int OldValue, int NewValue )
	{
		UpdateHintText();
	}

	UFUNCTION()
	private void OnLocalWeaponStockAmmoChanged( ANCWeapon Weapon, int OldValue, int NewValue )
	{
		UpdateHintText();
	}

	UFUNCTION()
	private void Signal_LocalADSChangedDelayed( FName signalName, UObject signalSource )
	{
		PauseWeaponHintText( WeaponConst::CycleScope::HINT_DELAY_INITIAL );
	}

	UFUNCTION()
	private void Signal_LocalModsChanged( FName signalName, UObject signalSource )
	{
		UAS_BaseWeaponScriptContext weaponContext = Cast<UAS_BaseWeaponScriptContext>( signalSource );
		if ( !IsValid( weaponContext ) )
		{
			return;
		}

		ANCWeapon weapon = weaponContext.GetOwnerWeapon();
		if ( !IsValid( weapon ) )
		{
			return;
		}

		FName weaponClassName = weapon.GetWeaponClass();
		if ( !IsValidWeaponClassName( weaponClassName ) )
		{
			return;
		}

		int changedModsBitfield = weaponContext.GetChangedModsBitfield();

		bool showAfterDelay = false;
		int availableOptics = Weapons().GetAvailableOpticBitfieldForWeaponID( MakeWeaponId( weapon ) );
		if ( ( availableOptics & changedModsBitfield ) > 0 )
		{
			showAfterDelay = true;
		}

		if ( !showAfterDelay )
		{
			return;
		}

		PauseWeaponHintText( WeaponConst::CycleScope::HINT_DELAY_AFTER_SWAP );
	}

	UFUNCTION( NotBlueprintCallable )
	void OnPossessedPawnChanged( APawn OldPawn, APawn NewPawn )
	{
		AAS_PlayerEntity asOld = Cast<AAS_PlayerEntity>( OldPawn );
		AAS_PlayerEntity asNew = Cast<AAS_PlayerEntity>( NewPawn );

		if ( IsValid( asOld ) )
		{
			asOld.Client_OnActiveWeaponChanged.UnbindObject( this );
			asOld.client_onAnyBackpackChanged.UnbindObject( this );
			asOld.healsBackpackComponent.OnContentsChangedDelegate.UnbindObject( this );
			asOld.Client_OnWeaponInventoryChanged.UnbindObject( this );
			asOld.Client_OnMountInventoryChanged.UnbindObject( this );
		}

		weaponStatusWidget.SetWeapons( nullptr, nullptr );

		if ( IsValid( asNew ) )
		{
			asNew.Client_OnActiveWeaponChanged.AddUFunction( this, n"SetActiveWeapon" );

			asNew.Client_OnWeaponInventoryChanged.AddUFunction( this, n"UpdateOffhandWeapons" );
			asNew.Client_OnMountInventoryChanged.AddUFunction( this, n"UpdateOffhandWeapons" );
			UpdateOffhandWeapons();
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void SetActiveWeapon( EViewmodelArm vmArm, ANCWeapon OldWeapon, ANCWeapon NewWeapon )
	{
		if ( vmArm != EViewmodelArm::MainHand )
			return;

		ANCPlayerCharacter localPlayer = localASPlayer;
		if ( IsValid( localPlayer ) )
		{
			weaponStatusWidget.SetWeapons( localPlayer.GetWeaponAtSlot( WeaponSlot::PrimarySlot0 ), localPlayer.GetWeaponAtSlot( WeaponSlot::PrimarySlot1 ) );
		}

		UpdateHintText();
	}

	EWeaponState hack_currentWeaponState = EWeaponState::EWeaponState_MAX;

	UFUNCTION()
	void Hack_TrackActiveWeaponState( UNCCoroutine co )
	{
		ANCPlayerCharacter player = localASPlayer;
		co.EndOnDestroyed( localASPlayer );

		while ( true )
		{
			ANCWeapon weapon = player.GetActiveWeapon();
			if ( IsValid( weapon ) )
			{
				EWeaponState state = weapon.GetWeaponState();
				if ( state != hack_currentWeaponState )
				{
					hack_currentWeaponState = state;

					if ( state != EWeaponState::Idle )
						PauseWeaponHintText( 1.5 );
					else
						UpdateHintText();
				}
			}
			co.Wait( 0.1 );
		}
	}

	bool IsWeaponInPrimarySlot( ANCWeapon weapon )
	{
		if ( weapon.GetWeaponSlot() == WeaponSlot::PrimarySlot0 )
			return true;
		if ( weapon.GetWeaponSlot() == WeaponSlot::PrimarySlot1 )
			return true;
		return false;
	}

	UFUNCTION( NotBlueprintCallable )
	void UpdateOffhandWeapons()
	{
		ANCPlayerCharacter localPlayer = localASPlayer;

		if ( IsValid( localPlayer ) )
		{
			weaponStatusWidget.SetWeapons( localPlayer.GetWeaponAtSlot( WeaponSlot::PrimarySlot0 ), localPlayer.GetWeaponAtSlot( WeaponSlot::PrimarySlot1 ) );

			{
				FGameplayTag tag = localPlayer.GetEquippedMountTag();
				if ( IsValidMountTag( tag ) && localPlayer.IsMountEnabled() )
				{
					// With mounts, we also want to set them "as weapons" as our ability to make sure cooldowns work correctly
					mountAbility.SetMount( tag );
					mountAbility.Show();
				}
				else
				{
					mountAbility.Hide();
				}
			}

			{
				ANCWeapon weapon = localPlayer.GetWeaponAtSlot( WeaponSlot::TacticalSlot );
				if ( IsValid( weapon ) && localPlayer.WeaponSlotIsEnabled( WeaponSlot::TacticalSlot ) )
				{
					tacticalAbility.SetAbility( weapon );
					tacticalAbility.Show();
				}
				else
				{
					tacticalAbility.Hide();
				}
			}

			{
				ANCWeapon weapon = localPlayer.GetWeaponAtSlot( WeaponSlot::GrenadeSlot );
				if ( IsValid( weapon ) && localPlayer.WeaponSlotIsEnabled( WeaponSlot::GrenadeSlot ) )
				{
					grenadeAbility.SetAbility( weapon );
					grenadeAbility.Show();
				}
				else
				{
					grenadeAbility.Hide();
				}
			}
		}
	}

	UFUNCTION()
	void OnTrainingWeaponSlotEnabledChanged( AAS_PlayerEntity asPlayer, int slot, int oldValue, int newValue )
	{
		UpdateOffhandWeapons();
	}

	UFUNCTION()
	void OnMountEnabledChanged( bool oldValue, bool newValue )
	{
		UpdateOffhandWeapons();
	}

	UFUNCTION( NotBlueprintCallable )
	void Callback_OnPlayerCreated( ANCPlayerCharacter Player )
	{
		Callback_OnPlayerChangedTeam( Player, Player.GetTeam(), Player.GetTeam() );
	}

	UFUNCTION( NotBlueprintCallable )
	void Callback_OnPlayerStateDestroyed( ANCPlayerCharacter Player, EEndPlayReason EndPlayReason )
	{
	}

	UFUNCTION( NotBlueprintCallable )
	void Callback_OnPlayerChangedTeam( ANCPlayerCharacter Player, int OldTeamNumber, int NewTeamNumber )
	{
		ANCPlayerCharacter MyPawn = Client_GetLocalPawn();

		if ( !IsValid( MyPawn ) )
			return;

		if ( !IsValid( Player ) )
			return;

		bool isPlayerStateLocal = MyPawn == Player;

		if ( isPlayerStateLocal )
		{
			TArray<ANCPlayerCharacter> players = UNCUtils::Client_GetAllPlayers( this );
			for ( ANCPlayerCharacter player : players )
			{
				if ( MyPawn != player )
					Callback_OnPlayerChangedTeam( player, player.GetTeam(), player.GetTeam() );
			}
			return;
		}
	}

	void DisplayKillMessage( FText message )
	{
#if !RELEASE
		// Temp, to help track down non loc text
		if ( !message.IsFromStringTable() )
			Warning( f"DisplayKillMessage using non-localized text: {message}" );
#endif

		killMessage.DisplayMessage( message );
	}

	void DisplayAssistMessage( FText message )
	{
		assistMessage.DisplayMessage( message );
	}

	void DisplayAxePerfect( FText message )
	{
		axePerfectMessage.SetText( message );
		StopAnimation( axeBadAnim );
		PlayAnimation( axePerfectAnim );
	}

	void DisplayAxeFail( FText message )
	{
		axePerfectMessage.SetText( message );
		StopAnimation( axePerfectAnim );
		PlayAnimation( axeBadAnim );
	}

	bool disableHud = false;
	void DisableHudAnim()
	{
		if ( disableHud )
			return;
		disableHud = true;

		float32 start = GetAnimationCurrentTime( hideHud );
		PlayAnimation( hideHud, start );
	}

	void EnableHudAnim()
	{
		if ( !disableHud )
			return;
		disableHud = false;

		float32 start = GetAnimationCurrentTime( hideHud );
		PlayAnimation( hideHud, start, 1, EUMGSequencePlayMode::Reverse );
	}

	private void DisplaySmallCenterMessage( FText message )
	{
		smallCenterMessage.DisplayMessage( message );
	}

	UFUNCTION()
	void DisplayGenericMessage( FText message, EHUDMessageStyle style )
	{
		switch ( style )
		{
			case EHUDMessageStyle::KILL_MESSAGE:
				DisplayKillMessage( message );
				break;

			case EHUDMessageStyle::SMALL_CENTER:
				DisplaySmallCenterMessage( message );
				break;
		}
	}

	UFUNCTION( BlueprintEvent )
	void OnNotEnoughManaEvent( float manaRequiredFrac )
	{
		DisplaySmallCenterMessage( GetLocalizedText( Localization::HUDMainWidget, f"HUDMAIN_MANA_NOTENOUGH" ) );
	}

	UFUNCTION()
	void AddInputContext( FName inputAction, FText context, bool isHold = false )
	{
		if ( activeInputContexts.Contains( inputAction ) )
		{
			activeInputContexts[inputAction].SetInputContext( inputAction, context );
			return;
		}

		UAS_InputContextWidget contextWidget = Cast<UAS_InputContextWidget>( WidgetBlueprint::CreateWidget( inputContextWidgetClass, GetOwningPlayer() ) );
		contextWidget.SetInputContext( inputAction, context, isHold );
		inputContextBox.AddChildToHorizontalBox( contextWidget );
		activeInputContexts.Add( inputAction, contextWidget );
	}

	UFUNCTION()
	void RemoveInputContext( FName inputAction )
	{
		if ( !activeInputContexts.Contains( inputAction ) )
			return;

		UAS_InputContextWidget widget = activeInputContexts[inputAction];
		widget.RemoveFromParent();
		activeInputContexts.Remove( inputAction );
	}

	UFUNCTION()
	void ToggleInputContext( bool isVisible )
	{
		SetWidgetVisibilitySafe( inputContextBox, isVisible ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Hidden );
	}

	bool shouldShowLoadoutHint = false;
	void DisableLoadoutHint()
	{
		shouldShowLoadoutHint = false;
	}

	void EnableLoadoutHint()
	{
		shouldShowLoadoutHint = true;
	}

	UFUNCTION()
	private void OnDomeEnemyCountChanged( AAS_RaidDomeShield dome, int oldEnemyCount, int newEnemyCount )
	{
		AAS_PlayerEntity localPlayer = Cast<AAS_PlayerEntity>( Client_GetLocalPawn() );
		if ( !IsValid( localPlayer ) || !IsValid( dome ) || dome.GetTeam() != localPlayer.GetTeam() )
			return;

		if ( newEnemyCount > 0 && !enemiesInBaseDeferredWidget.IsShown() )
		{
			enemiesInBaseDeferredWidget.Show();
		}
		else if ( newEnemyCount == 0 && enemiesInBaseDeferredWidget.IsShown() )
		{
			enemiesInBaseDeferredWidget.Hide();
		}
	}

	void OnTrinketUsedFlash( FPlayerEquipmentData trinketData )
	{
		localPlayerStatus.OnTrinketUsedFlash( trinketData );
	}

	void ShowTeammateDisconnectWarning( float32 timeRemaining )
	{
		teammateDisconnectedMessage.ShowReconnectWarning( timeRemaining );
	}

	void HideTeammmateDisconnectWarning( )
	{
		teammateDisconnectedMessage.HideWarning();
	}

	void SetTrainingDisableScopeSwapHint()
	{
		trainingDisableScopeSwapHint = true;
	}

	void ClearTrainingDisableScopeSwapHint()
	{
		trainingDisableScopeSwapHint = false;
	}
}