event void LootItemPickedUpEvent( AAS_LootItemPickup loot, ANCPlayerCharacter user );

UCLASS( Abstract )
class AAS_LootItemPickup : AAS_PlayerPickUp
{
	UPROPERTY()
	FGameplayTag lootIndex = GameplayTags::Loot_Resource_Opal;

	UPROPERTY()
	private FNCNetInt lootDataIndex( -1, -1, 10000 );

	UPROPERTY()
	int pickupAmount = 1;

	UPROPERTY()
	LootItemPickedUpEvent itemPickedUpEvent;

	UFUNCTION( BlueprintOverride )
	void ServerBeginPlay()
	{
		Super::ServerBeginPlay();

		if ( !IsLootIndexValid( lootIndex ) )
		{
			Destroy();
			return;
		}
	}

	UFUNCTION( BlueprintOverride )
	void ClientBeginPlay()
	{
		Super::ClientBeginPlay();

		lootDataIndex.OnReplicated().AddUFunction( this, n"CL_OnLootDataIndexChanged" );
		CL_OnLootDataIndexChanged( -1, lootDataIndex );
	}

	protected bool SV_CanPlayerPickup( AAS_PlayerEntity player ) override
	{
		if( !IsAlive( player ) )
			return false;

		const FLootDataStruct& lootData = GetLootDataByIndex( lootIndex );
		int checkAmount  = GetDesiredLootCount( lootData, pickupAmount );

		FBackpackItemStruct item = MakeBackpackItem( lootIndex, pickupAmount );
		int amountLeft			 = player.FakeAddToAnyBackpack( item );
		return amountLeft != checkAmount;		
	}

	AAS_PlayerEntity FindNextPickupPlayer() const override
	{
		const FLootDataStruct& lootData = GetLootDataByIndex( lootIndex );
		int checkAmount  = GetDesiredLootCount( lootData, pickupAmount );

		for ( AAS_PlayerEntity player : pickupTrigger.playersInside )
		{
			FBackpackItemStruct item = MakeBackpackItem( lootIndex, pickupAmount );
			int amountLeft			 = player.FakeAddToAnyBackpack( item );
			if ( amountLeft != checkAmount )
			{
				return player;
			}
		}

		return nullptr;
	}

	UFUNCTION(BlueprintOverride)
	void SV_GivePlayerPickup()
	{
		Super::SV_GivePlayerPickup();
		
		const FLootDataStruct& lootData = GetLootDataByIndex( lootIndex );
		int leftover					= PerformLootPickupAction( pickupPlayer, MakeBackpackItem( lootIndex, pickupAmount ) );
		int checkAmount					= GetDesiredLootCount( lootData, pickupAmount );

		if ( leftover != checkAmount )
		{
			Server_EmitSoundAtLocation( lootData.pickupSound, GetActorLocation() );
			const FLootDataStruct& pickupData = GetLootDataByIndex( lootIndex );
			FString command			   = f"ServerCommand_NotifyGatherResource {pickupData.intIndex} {checkAmount} {CountItemsInBackpackAndValidBoxes(pickupPlayer, pickupData.index, pickupPlayer, CraftingSource::BACKPACK)}";
			UNCRemoteScriptCommands::SendServerCommand( pickupPlayer, command );

			itemPickedUpEvent.Broadcast( this, pickupPlayer );

			if ( leftover == 0 )
			{
				Destroy();
			}
			else
			{
				SV_SetPickupAmount( leftover );
				EnableGravity();
			}
		}
		else
		{
			EnableGravity();
		}
	}

	UFUNCTION()
	void SV_SetPickupAmount( int amount )
	{
		pickupAmount = amount;
	}

	UFUNCTION()
	void SV_SetLootItemIndex( FGameplayTag index )
	{
		lootIndex = index;

		if ( IsLootIndexValid( lootIndex ) )
		{
			const FLootDataStruct& Data = GetLootDataByIndex( lootIndex );
			lootDataIndex.SetNetValue( Data.intIndex );
			
			FPingableObjectInfo info	   = GetPingableObjectInfoFromLootData( EPlayerPingType::LOOT, Data );
			pingableComponent.pingableInfo = info;

			if ( Data.lootType == ELootType::PrimaryWeapon )
				pingableComponent.pingType = EPlayerPingType::LOOT_WEAPON;
		}
		else
		{
			lootDataIndex.SetNetValue( -1 );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void CL_OnLootDataIndexChanged( int oldValue, int newValue )
	{
		SetPickupMesh();
		UpdatePingableInfo();		
	}

	private void UpdatePingableInfo()
	{
		FLootDataStruct Data = GetLootDataByIntIndex( lootDataIndex );

		FPingableObjectInfo info	   = GetPingableObjectInfoFromLootData( EPlayerPingType::LOOT, Data );
		pingableComponent.pingableInfo = info;

		if ( Data.lootType == ELootType::PrimaryWeapon )
			pingableComponent.pingType = EPlayerPingType::LOOT_WEAPON;
	}

	UFUNCTION()
	private void SetPickupMesh()
	{
		if ( !IsLootIntIndexValid( lootDataIndex ) )
			return;

		FLootDataStruct lootData = GetLootDataByIntIndex( lootDataIndex );
		lootIndex				 = lootData.index;
		pickupMesh.SetStaticMesh( lootData.mesh );

		// Client only, random rotation
		pickupMesh.SetWorldRotation( FRotator( 0, Math::RandRange( 0, 360 ), 0 ) );
	}
}