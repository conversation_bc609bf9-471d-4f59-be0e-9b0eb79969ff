const float32 ENDLESS_NIGHT_SPAWN_LIFETIME = 120.0;
const float32 ENDLESS_NIGHT_MAX_SPAWNS	   = 4;
const float32 ENDLESS_NIGHT_SPAWN_RADIUS   = 500.0f;
const float DEFAULT_ASSET_RADIUS		   = 50.0;

const FLinearColor ENDLESS_NIGHT_SUN_COLOR = FLinearColor( 0.75, 0.27, 0.22 );
const float ENDLESS_NIGHT_LIGHT_LUX		   = 20.0;

UCLASS( Abstract )
class UAS_WeaponContext_RaidUlt_ForwardSpawn : UAS_RaidUltWeaponContext_Base
{
	default cooldownTime = 480;

	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<AAS_MaraRespawnZone> respawnBeaconClass;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset deploySound_1P;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset deploySound_3P;

	UFUNCTION( BlueprintOverride )
	void CodeCallback_OnWeaponPrimaryAttack( ANCWeapon weapon, FWeaponPrimaryAttackInfo attackInfo,
											 FWeaponPrimaryAttackReturnParams& returnInfo )
	{
		Super::CodeCallback_OnWeaponPrimaryAttack( weapon, attackInfo, returnInfo );

		if ( !IsServer() )
			return;

		ConsumeRaidUlt( weapon );

		TrySpawnBeacon( weapon, attackInfo.eyePosition, attackInfo.aimDirection );
	}

	bool CanRaidUltBeUsedHere( ANCWeapon weapon ) override
	{
		AAS_PlayerEntity owner = Cast<AAS_PlayerEntity>( weapon.GetWeaponOwner() );
		if ( IsValid( owner ) && owner.outOfBoundsManager.IsOutOfBounds() )
			return false;
		FVector position;
		return CanSpawnBeacon( weapon, position );
	}

	void TrySpawnBeacon( ANCWeapon weapon, FVector eyePosition, FVector aimDirection )
	{
		FVector positionUsed;
		ANCPlayerCharacter owner = weapon.GetWeaponOwner();
		bool success			 = CanSpawnBeacon( weapon, positionUsed );
		// Print( f"Try spawn: {success}" );

		AAS_MaraRespawnZone zone;
		if ( success )
		{
			if ( IsServer() )
			{
				Server_EmitSoundAtLocation_3P( deploySound_3P, positionUsed, owner );
				zone = CreateEndlessNightSpawnBeacon( owner, positionUsed, FRotator() );
			}
			else
			{
				Client_EmitSoundAtLocation( deploySound_1P, positionUsed );
			}
		}

		return;
	}

	UFUNCTION()
	bool CanSpawnBeacon( ANCWeapon weapon, FVector& position )
	{
		ANCPlayerCharacter owner = weapon.GetWeaponOwner();
		FVector aimPosition		 = owner.GetEyeLocation();
		FVector aimDirection	 = owner.GetEyeForward();

		FVector positionUsed;

		TArray<AActor> ignoreActors;
		ignoreActors.Add( owner );

		FVector modifiedAimDir = aimDirection;
		modifiedAimDir.Z	   = Math::Min( modifiedAimDir.Z, 0 );
		modifiedAimDir.Normalize();

		FHitResult result = SphereTraceSingle( aimPosition, aimPosition + ( modifiedAimDir * 250 ), 40.0f, ETraceTypeQuery::TraceTypeQuery1, false, ignoreActors, true );

		bool success = false;

		if ( result.GetbBlockingHit() )
		{
			if ( UNCUtils::IsTraceHitWalkable( owner.CharacterMovement, result ) )
			{
				positionUsed = result.ImpactPoint;
				success		 = true;
			}
		}

		if ( !success )
		{
			FVector traceStart = result.TraceEnd;
			if ( result.GetbBlockingHit() )
				traceStart = result.TraceStart + ( modifiedAimDir * ( result.Time ) * 0.5f );
			FHitResult downResult = SphereTraceSingle( traceStart,
													 traceStart - FVector( 0, 0, 500 ), 40.0f,
													 ETraceTypeQuery::TraceTypeQuery1,
													 false,
													 ignoreActors,
													 true );

			if ( downResult.GetbBlockingHit() && UNCUtils::IsTraceHitWalkable( owner.CharacterMovement, downResult ) )
			{
				positionUsed = downResult.ImpactPoint;
				success		 = true;
			}
		}

		position = positionUsed;
		// Print( f"Can spawn: {success}" );
		return success;
	}

	UFUNCTION()
	AAS_MaraRespawnZone CreateEndlessNightSpawnBeacon( ANCPlayerCharacter owner, FVector loc, FRotator rotation )
	{
		if ( !IsValid( owner ) )
			return nullptr;

		int teamId = owner.GetTeam();

		TSubclassOf<AAS_MaraRespawnZone> beaconClass = respawnBeaconClass;

		AAS_MaraRespawnZone oldZone = GetMaraRespawnZoneForTeam( teamId );
		if ( IsValid( oldZone ) )
			oldZone.DestroyZone();

		AAS_MaraRespawnZone spawn = Cast<AAS_MaraRespawnZone>( Server_SpawnEntity( beaconClass, owner, loc, rotation ) );

		AAS_ShieldBreaker breacher = GetBreacherForTeam( teamId );
		if ( IsValid( breacher ) )
		{
			spawn.SetBreacher( breacher );

			/*if ( breacher.net_breacherPenetrated )
				IncreaseLives( breacher );
			else
				breacher.OnBreacherPenetrated.AddUFunction( this, n"OnBreacherPenetrated" );*/
		}

		PlayerStatsManager().IncrementCharacterStatForPlayer( owner, GameplayTags::Classes_Class_Mara, GameplayTags::Progression_PlayerStats_Mara_RespawnsPlaced );
		return spawn;
	}

	/*UFUNCTION()
	private void OnBreacherPenetrated( AAS_ShieldBreaker shieldBreaker, AAS_RaidDomeShield dome )
	{
		shieldBreaker.OnBreacherPenetrated.Unbind( this, n"OnBreacherPenetrated" );
		IncreaseLives( shieldBreaker );
	}*/

	void IncreaseLives( AAS_ShieldBreaker shieldBreaker )
	{
		shieldBreaker.AddAttackerLives( 3 );

		TArray<ANCPlayerCharacter> PS = GetPlayersOfTeam( shieldBreaker.GetTeam() );
		for ( ANCPlayerCharacter ps : PS )
		{
			Server_SendGenericMessage( ps, Localization::Character_Mara, "mara_ult_energy_added" );
		}
	}
}

AAS_MaraRespawnZone GetMaraRespawnZoneForTeam( int team )
{
	AAS_MaraRespawnZone result;
	TArray<AAS_MaraRespawnZone> zones;
	GetAllActorsOfClass( zones );
	for ( AAS_MaraRespawnZone zone : zones )
	{
		if ( zone.GetTeam() == team && !zone.net_isBeingDestroyed )
			return zone;
	}

	return result;
}