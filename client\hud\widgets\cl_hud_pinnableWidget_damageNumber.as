UCLASS()
class UAS_DamageNumber_Settings : UAS_PinnableWidgetSettings
{
	UPROPERTY()
	int maxGlowDamage = 150;

	UPROPERTY()
	TSubclassOf<UAS_CommonTextStyle> textStyle;
}

event void FOnDamageNumberModifyWindowClosed( UAS_PinnableWidget_DamageNumber pinWidget );
const float32 DAMAGE_NUMBER_MODIFY_DURATION = 0.35; // 0.5;

UCLASS( Abstract )
class UAS_PinnableWidget_DamageNumber : UAS_PinnableWidget
{
	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_DamageNumberWidget damageNumber;

	UPROPERTY( NotVisible, BlueprintHidden, Transient, Meta = ( BindWidgetAnim ) )
	private UWidgetAnimation floatAwayAnim;

	UPROPERTY( NotVisible, BlueprintHidden, Transient, Meta = ( BindWidgetAnim ) )
	UWidgetAnimation uniqueIntroAnim;

	UPROPERTY( EditDefaultsOnly )
	FOnDamageNumberModifyWindowClosed onModifyWindowClosed;

	default TickFrequency = EWidgetTickFrequency::Never;

	private bool modifyWindowClosed = false;
	private float32 modifyWindowClosedTime = -1;
	private float32 lastFrameTime;

	void OnSettingsUpdated() override
	{
		Super::OnSettingsUpdated();
		damageNumber.SetDamageNumberSettings( Cast<UAS_DamageNumber_Settings>( settings ) );
	}

	void Initialize( AAS_HUD inOwnerHUD )
	{
		damageNumber.Initialize( inOwnerHUD );
	}

	void SetTrackedPosition( FVector inTrackedPosition ) override
	{
		Super::SetTrackedPosition( inTrackedPosition );
		RefreshModifyWindow();
	}

	void SetScreenSpaceOffset( FVector2D newSSOffset )
	{
		AAS_HUD localHud = GetLocalHUD();
		if ( !IsValid( localHud ) )
			return;

		FPinnableWidgetData& data = localHud.pinnedWidgetManager.GetPinnableWidgetData( this );

		data.screenOffset = newSSOffset;
	}

	void AddDamage( int newDamage )
	{
		RefreshModifyWindow();
		damageNumber.AddDamage( newDamage );
	}

	void Appear()
	{
		damageNumber.Appear();
	}

	void SetDamageInfo( const FCachedDealtDamageData damageData )
	{
		damageNumber.SetDamageInfo( damageData );
	}

	void HideDamageText()
	{
		damageNumber.HideDamageText();
	}

	void RefreshModifyWindow()
	{
		System::ClearTimer( this, f"OnDamageNumberModifyWindowClosed" );
		System::SetTimer( this, n"OnDamageNumberModifyWindowClosed", DAMAGE_NUMBER_MODIFY_DURATION, false );
	}

	void EndModifyWindow()
	{
		System::ClearTimer( this, f"OnDamageNumberModifyWindowClosed" );
		OnDamageNumberModifyWindowClosed();
	}

	UFUNCTION()
	void OnDamageNumberModifyWindowClosed()
	{
		onModifyWindowClosed.Broadcast( this );
		modifyWindowClosed	   = true;
		modifyWindowClosedTime = TO_SECONDS( GetTimeMilliseconds() );

		lastFrameTime = modifyWindowClosedTime;

		AAS_HUD localHud = GetLocalHUD();
		AAS_PlayerEntity victimAsPlayer = Cast<AAS_PlayerEntity>( ownerActor );
		if ( IsValid( localHud ) && IsValid( victimAsPlayer ) )
		{
			if ( IsValid( localHud.pinnedWidgetManager ) && localHud.pinnedWidgetManager.HasPinnableWidgetData( this ) )
			{
				FPinnableWidgetData& data   = localHud.pinnedWidgetManager.GetPinnableWidgetData( this );
				data.trackedActorComponent  = nullptr;
				FVector headSocketLoc		= victimAsPlayer.Mesh.GetSocketLocation( n"neck_head" );
				lastTrackedPosition			= headSocketLoc;
			}
		}

		PlayAnimation( floatAwayAnim, 0, 1, EUMGSequencePlayMode::Forward, 1.0 / floatAwayTime, false );

		System::SetTimer( this, n"DestroyDamageNumber", floatAwayTime, false );
	}

	UPROPERTY( EditDefaultsOnly )
	float32 floatAwayTime = 2.0;

	UFUNCTION()
	private void DestroyDamageNumber()
	{
		AAS_HUD localHud = GetLocalHUD();
		localHud.pinnedWidgetManager.RemovePinnableWidget( this );
	}
}