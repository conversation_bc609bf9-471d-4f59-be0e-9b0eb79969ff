UAS_Passive_HelpingHand Passive_HelpingHand()
{
	UAS_Passive_HelpingHand result = Cast<UAS_Passive_HelpingHand>( UNCGameplaySystemsSubsystem::Get_SharedSystem( GetCurrentWorld(), UAS_Passive_HelpingHand::StaticClass() ) );
	return result;
}



UCLASS( Abstract )
class UAS_Passive_HelpingHand : UAS_PassiveBaseClass
{
	// this determines what passive we are scripting
	default passiveTag = GameplayTags::Classes_Passives_HelpingHand;

	TMap<ANCPlayerCharacter, UAS_HelpingHandThread> playerThreadMap;

	UPROPERTY(EditDefaultsOnly)
	TSubclassOf<AAS_HelpingHandBuddy> buddyClass;

	UFUNCTION(BlueprintOverride)
	void Initialize()
	{
		Super::Initialize();
	}

	void Server_OnPassiveStarted(ANCPlayerCharacter player) override
	{
		Super::Server_OnPassiveStarted(player);
		ClearPlayerThread( player );

		UAS_HelpingHandThread thread = Cast<UAS_HelpingHandThread>( CreateThread( UAS_HelpingHandThread::StaticClass(), player ) );
		thread.Init( player );
		playerThreadMap.Add( player, thread );
	}

	void Server_OnPassiveEnded(ANCPlayerCharacter player) override
	{
		Super::Server_OnPassiveEnded(player);
		ClearPlayerThread( player );
	}

	void ClearPlayerThread( ANCPlayerCharacter player )
	{
		if ( playerThreadMap.Contains( player ) )
		{
			UAS_HelpingHandThread thread = playerThreadMap[player];
			if ( IsValid( thread ) && thread.IsRunning() )
				thread.Cancel();
			playerThreadMap.Remove( player );
		}
	}
}







class UAS_HelpingHandThread : UAS_Thread
{
	ANCPlayerCharacter myPlayer;
	bool isDebugMode;

	TArray<UClass> classesToSearchFor;
		default classesToSearchFor.Add( ANCResourceBase::StaticClass() );
		default classesToSearchFor.Add( AAS_DisplayedLootChest::StaticClass() );

	void Init( ANCPlayerCharacter p, bool iDB = false )
	{
		myPlayer = p;
		isDebugMode = iDB;
		Start();
	}

	void OnThreadStart(UNCCoroutine co) override
	{
		Super::OnThreadStart(co);
		co.EndOnDestroyed( myPlayer );

		TArray<AActor> actorsToIgnore;
		FNCFindTargetParameters searchParams = MakeFindTargetParameters( myPlayer.GetEyeLocation(), myPlayer.GetEyeForward(), 700.0f, 45, classesToSearchFor, actorsToIgnore, ENCTargetSortStyle::Angle );
		searchParams.doTraceToTarget = false;
		searchParams.minAABBSizeToDoBoundingBoxTestFor = 50;
		searchParams.onlyTargetEnemies = false;
		searchParams.canReturnResources = true;
		searchParams.mustHaveHealthComponent = false;

		bool didFindLocationOnLastLoop = false;
		while ( true )
		{
			if ( !isDebugMode )
			{
				if ( didFindLocationOnLastLoop )
					co.Wait( 120 );
				else
					co.Wait( 5 );
			}

			bool didFindLocation = false;
			if ( CanSearch() )
			{
				TArray<FNCFoundTargetData> candidates = GetTargetsInRangeWithFOV( searchParams );

				co.Wait( 0.1 );

				int numToCheckPerFrame = 3;
				int frame = 0;

				TArray<AActor> ignoreActors; ignoreActors.Add( myPlayer );
				TArray<FNCFoundTargetData> trueTargets;
				for( auto c : candidates )
				{
					int overflow = frame % numToCheckPerFrame;
					if ( overflow == 0 )
						co.Wait( 0.1 ); //wait until next frame every 3 candidates

					FHitResult losTraceToPlayer = SphereTraceSingle( myPlayer.GetEyeLocation(), c.outLocation, 25, ETraceTypeQuery::WeaponFine, false, ignoreActors, true );
					if ( losTraceToPlayer.bBlockingHit )
						continue; // doesn't have LOS to player

					if ( Distance( c.outLocation, myPlayer.GetActorLocation() ) > 1000 )
						continue; // too far from player
					
					trueTargets.Add( c );

					frame++;
				}

				if ( trueTargets.Num() > 0 )
				{
					for( FNCFoundTargetData candidate : trueTargets )
					{
						if ( IsValid( candidate.actor ) )
						{
							CreateHelpingHand( candidate );
							didFindLocation = true;
							break;
						}
					}
				}
			}

			didFindLocationOnLastLoop = didFindLocation;

			if ( isDebugMode )
				Cancel();
		}
	}

	bool CanSearch()
	{
		if ( IsValid( sv_CombatEventSystem() )  )
		{
			if ( sv_CombatEventSystem().IsPlayerInCombat( myPlayer ) )
				return false;
		}

		return true;
	}

	void CreateHelpingHand( FNCFoundTargetData target )
	{
		if ( IsValid( myPlayer ) )
			myPlayer.PlayBattleChatter( GameplayTags::Audio_VO_BattleChatter_UsingAbility_Passive );
		AAS_HelpingHandBuddy buddy = Cast<AAS_HelpingHandBuddy>( Server_SpawnEntity( Passive_HelpingHand().buddyClass, myPlayer, target.actor.GetActorLocation() ) );
		buddy.SetTarget( target.actor );
	}

	void OnThreadEnd(FNCCoroutineEndParams params) override
	{
		Super::OnThreadEnd(params);
		
	}
}









UCLASS(Abstract)
class AAS_HelpingHandBuddy : AProp_SkeletalMesh
{
	UPROPERTY(DefaultComponent, RootComponent)
    USceneComponent Root;

	UPROPERTY(DefaultComponent)
    USceneComponent ResourceSpawn;

	UPROPERTY(DefaultComponent)
	UHealthComponentNetworked health;

	UPROPERTY( EditDefaultsOnly )
	float spawnRange = 350;

	AActor target;
	AActor lootPickUp;
	FGameplayTag lootTag;
	AAS_PlayerEntity cachedOwner;

	UPROPERTY(EditDefaultsOnly)
	TMap<FGameplayTag, int> resourcesAndAmountToSpawn;

	UPROPERTY(EditDefaultsOnly)
	UNCAudioAsset idleLoop3P;
	UPROPERTY(EditDefaultsOnly)
	UNCAudioAsset pickup1P;
	UPROPERTY(EditDefaultsOnly)
	UNCAudioAsset spawn1P;
	UPROPERTY( EditDefaultsOnly )
    UNCAudioAsset destroySfx;

	UPROPERTY(EditDefaultsOnly)
	UNiagaraSystem spawnVFx;
	UPROPERTY(EditDefaultsOnly)
	UNiagaraSystem deathVFx;

	UPROPERTY( DefaultComponent )
	UNiagaraComponent hairFireball;
	default hairFireball.bAutoActivate = false;

	ASoundActor sustainSfx;
	bool isFriendly;

	UPROPERTY( EditDefaultsOnly )
	TArray<TSubclassOf<UAnimInstance>> animSets;

	UPROPERTY()
	FNCNetInt animSetToUse(0, 0, 10);

	UFUNCTION(BlueprintOverride)
	void ConstructionScript()
	{
		hairFireball.AttachToComponent( SkeletalMeshComponent, n"fx_head" );
	}

	UFUNCTION(BlueprintOverride)
	void ServerBeginPlay()
	{
		health.BP_OnPostReceivedDamage.AddUFunction( this, n"OnPostReceivedDamage" );

		Server_EmitSoundOnEntity_1P( spawn1P, this, GetOwnerPlayer() );
		sustainSfx = Server_EmitSoundOnEntity_ReturnEntity( idleLoop3P, this );

		cachedOwner = GetOwnerPlayer();

		animSetToUse.SetNetValue( Math::RandRange( 0, animSets.Num() - 1 ) );
		OnAnimSetChanged( 0, animSetToUse );
	}

	UFUNCTION(BlueprintOverride)
	void ClientBeginPlay()
	{
		if ( !IsValid( GetOwnerPlayer() ) )
			return;
		if ( !IsValid( Client_GetLocalASPawn() ) )
			return;
		
		isFriendly = IsFriendly( GetOwnerPlayer().GetTeam(), Client_GetLocalASPawn().GetTeam() );
		if ( isFriendly )
		{
			SetHighlightEnabled( this, true );
			SetHighlightIndex( this, EHighlightStencilValue::HIGHLIGHT_FRIENDLY_STENCIL_VALUE );
		}

		int friendlyInt = isFriendly ? 0 : 1;
		hairFireball.SetNiagaraVariableFloat( f"TeamColorFloat", friendlyInt );
		hairFireball.Activate( true );

		animSetToUse.OnReplicated().AddUFunction( this, n"OnAnimSetChanged" );
		OnAnimSetChanged( 0, animSetToUse );

		CreateDynamicMaterialInstances();
	}

	UFUNCTION()
	void CreateDynamicMaterialInstances()
	{
		USkeletalMeshComponent mesh = SkeletalMeshComponent;

		TArray<UMaterialInterface> mats = mesh.GetMaterials();
        for( int i = 0; i<mats.Num(); i++ )
        {
            UMaterialInstanceDynamic m = mesh.CreateDynamicMaterialInstance( i );
            m.SetVectorParameterValue( n"Emmisive Color", isFriendly ? BuddyConst::friendlyColor : BuddyConst::enemyColor );
        }	
	}

	UFUNCTION()
	void SetTarget( AActor t )
	{
		if ( !IsValid( cachedOwner ) )
			return;

		target = t;
		FVector location = GetSpawnLocation( t );
		FVector orientationVector = GetOwnerPlayer().GetActorLocation() - location;
		FRotator rotation = FRotator( 0, orientationVector.ToOrientationRotator().Yaw, 0 );
		SetActorLocationAndRotation( location, rotation );

		if ( IsValid( spawnVFx ) )
		{
			FNiagaraVariablePackage varPackage;
			varPackage.AddVariableFloat( n"TeamColorFloat", 0 );
			Server_SpawnEffectAtLocation_OneShot_SetNiagaraVariables_WithFilter( spawnVFx, GetActorLocation(), GetActorRotation(), varPackage, GetOwnerPlayer(), UNCNetEventFilterFriendly::StaticClass() );
			varPackage.AddVariableFloat( n"TeamColorFloat", 1 );
			Server_SpawnEffectAtLocation_OneShot_SetNiagaraVariables_WithFilter( spawnVFx, GetActorLocation(), GetActorRotation(), varPackage, GetOwnerPlayer(), UNCNetEventFilterEnemy::StaticClass() );
		}

		PlayAnimation( n"spawn" );
		System::SetTimer( this, n"SetToIdleAnim", 1.77, false );
		System::SetTimer( this, n"SpawnResource", 1.2, false );
	}

	UFUNCTION()
	void OnAnimSetChanged( int old, int new )
	{
		if ( new >= animSets.Num() )
			return;

		SkeletalMeshComponent.SetAnimClass( animSets[new] );
	}

	UFUNCTION()
	FVector GetSpawnLocation( AActor t )
	{
		TArray<FVector> locationCandidates;
		for( int i = 0; i<40; i++ )
		{
			FVector targetLoc = t.GetActorLocation() + ( FVector::UpVector * 50 );
			float xMin = targetLoc.X - spawnRange;
			float xMax = targetLoc.X + spawnRange;
			float yMin = targetLoc.Y - spawnRange;
			float yMax = targetLoc.Y + spawnRange;

			float tX = Math::Clamp( Math::RandRange( xMin, xMax ), GetActorLocation().X - spawnRange, GetActorLocation().X + spawnRange );
			float tY = Math::Clamp( Math::RandRange( yMin, yMax ), GetActorLocation().Y - spawnRange, GetActorLocation().Y + spawnRange );

			FVector newLoc = FVector( tX, tY, targetLoc.Z );
			if ( Distance2D( newLoc, GetActorLocation() ) < ( spawnRange / 2.0 ) )
			{
				i--;
				continue; //too close, next candidate but don't increment i
			}

			TArray<AActor> ignoreActors; ignoreActors.Add( this ); ignoreActors.Add( t ); ignoreActors.Add( cachedOwner );
			FVector heightCheck = GetActorLocation() + ( FVector::UpVector * 175 );
			FHitResult heightCapTrace = LineTraceSingle( targetLoc, heightCheck, ETraceTypeQuery::WeaponFine, false, ignoreActors, true );
			float heightCap = heightCheck.Z;
			if ( heightCapTrace.bBlockingHit )
				heightCap = heightCapTrace.ImpactPoint.Z - 50;

			FVector vec = FVector( tX, tY, heightCap );
			FVector groundCheckVec = vec - ( FVector::UpVector * 1250 );
			FHitResult downCheckTrace = LineTraceSingle( vec, groundCheckVec, ETraceTypeQuery::WeaponFine, false, ignoreActors, true  );
			if ( !downCheckTrace.bBlockingHit )
				continue; // no floor, next candidate

			FHitResult losTrace = LineTraceSingle( targetLoc, vec, ETraceTypeQuery::WeaponFine, false, ignoreActors, true );
			if ( losTrace.bBlockingHit )
				continue; // doesn't have LOS to target ( to avoid being put in a wall)

			newLoc.Z = downCheckTrace.ImpactPoint.Z;
			return newLoc;
		}

		return t.GetActorLocation(); //if we can't find a new location, default to cached scan location
	}

	UFUNCTION()
	void SpawnResource()
	{
		FGameplayTag resourceToSpawn = GetResourceToSpawn();
		const FLootDataStruct& data = GetLootDataByIndex( resourceToSpawn );
		if( hack_IsLootAutoPickup(resourceToSpawn) )
		{
			AAS_LootItemPickup p = Cast<AAS_LootItemPickup>( Server_SpawnEntity( Loot().lootItemPickupClass, nullptr, ResourceSpawn.GetWorldLocation() ) );
			p.root.SetComponentTickEnabled( false );
			p.DisableGravity();
			p.SV_SetLootItemIndex( resourceToSpawn );
			p.SV_SetPickupAmount( resourcesAndAmountToSpawn[resourceToSpawn] );
			p.itemPickedUpEvent.AddUFunction( this, n"DestroyAfterPickup_LIP" );
			lootPickUp = p;
		}
		else
		{
			AAS_LootEntity loot = Server_SpawnLoot( MakeBackpackItem( data, resourcesAndAmountToSpawn[resourceToSpawn] ), ResourceSpawn.GetWorldLocation(), ResourceSpawn.GetWorldRotation() );
			loot.DisableGravity();
			loot.onPickedup.AddUFunction( this, n"DestroyAfterPickup_LE" );
			lootPickUp = loot;
		}

		lootTag = data.index;

		System::SetTimer( this, n"CreatePing", 0.25, false );
		System::SetTimer( this, n"DestroyAfterTime", 90, false );
	}

	UFUNCTION()
	void CreatePing()
	{
		if ( IsValid( lootPickUp ) )
			PingManager().CreatePingWithLoot( GetOwnerPlayer(), ResourceSpawn.GetWorldLocation(), EPlayerPingType::LOOT, lootTag, lootPickUp );
	}

	FGameplayTag GetResourceToSpawn()
	{
		TArray<FGameplayTag> possibleResourcesToSpawn;
		AAS_PlayerEntity owner = GetOwnerPlayer();
		if ( !IsValid( owner ) )
			return GameplayTags::Loot_Resource_Opal;
		
		//does the player need coins?
		const FGameplayTag coinTag = GameplayTags::Loot_Resource_Opal;
		int coinCount = owner.CountItemsInAllBackpacks( coinTag );
		for( int i = 3; i>0; i-- )
		{
			//add coins to resource distribution once for every time it's below the threshold (i.e. if a player only has 10 coins they get added 3 times, 2 times for 20 coins, 1 time for 30 coins )
			int coinThreshold = 10 * i;
			if ( coinCount <= coinThreshold )
				possibleResourcesToSpawn.Add( GameplayTags::Loot_Resource_Opal );
		}

		//does the player need shields?
		int purpleShieldCount = owner.CountItemsInAllBackpacks( GameplayTags::Loot_Armor_Level3 );
		int blueShieldCount = owner.CountItemsInAllBackpacks( GameplayTags::Loot_Armor_Level2 );

		if ( purpleShieldCount > 5 )
			possibleResourcesToSpawn.Add( GameplayTags::Loot_Armor_Level4 );
		else if ( blueShieldCount > 5 ) //if we have some purple, let's add more of a chance for lvl 3
			possibleResourcesToSpawn.Add( GameplayTags::Loot_Armor_Level3 );

		if ( TO_SECONDS( GetGameTimeMS() ) > 600 )
			possibleResourcesToSpawn.Add( GameplayTags::Loot_Armor_Level4 ); //also increase chances of level3 after 7 minutes
		else if ( TO_SECONDS( GetGameTimeMS() ) > 420 )
			possibleResourcesToSpawn.Add( GameplayTags::Loot_Armor_Level3 ); //also increase chances of level3 after 7 minutes
		else
			possibleResourcesToSpawn.Add( GameplayTags::Loot_Armor_Level2 );

		//does the player need raid tool ammo?
		ANCWeapon raidTool = owner.GetWeaponAtSlot( WeaponSlot::RaidToolsSlot );
		if ( IsValid( raidTool ) )
		{
			FLootDataStruct lootAmmo = GetLootDataForAmmo( raidTool.GetAmmoSource() );
			int ammo = raidTool.GetClipAmmo() + raidTool.GetStockpileAmmo();
			for( int i = 3; i>0; i-- )
			{
				//add coins to resource distribution once for every time it's below the threshold (i.e. if a player only has 2 ammo they get added 3 times, 2 times for 4 ammo )
				int ammoThreshold = 2 * i;
				if ( ammo <= ammoThreshold )
					possibleResourcesToSpawn.Add( lootAmmo.index );
			}
		}

		const FGameplayTag resourceToSpawn = possibleResourcesToSpawn[ Math::RandRange( 0, possibleResourcesToSpawn.Num() - 1 ) ];
		if ( !resourcesAndAmountToSpawn.Contains( resourceToSpawn ) )
		{
			ScriptError_Silent_WithBug( f"Helping Hand ABility is trying to spawn {resourceToSpawn} but is not in map in BP_HelpingHandBuddy", f"Mark Yampolsky", f"Helping Hand ABility is trying to spawn {resourceToSpawn} but is not in map in BP_HelpingHandBuddy" );
			return GameplayTags::Loot_Resource_Opal;
		}
		return resourceToSpawn;
	}

	UFUNCTION()
	void SetToIdleAnim()
	{
		PlayAnimation( n"idle", 0, true );
	}

	UFUNCTION()
	void DestroyAfterTime()
	{
		if ( IsValid( lootPickUp ) )
			lootPickUp.Destroy();
		Dismiss();
	}

	UFUNCTION()
	void DestroyAfterPickup_LIP( AAS_LootItemPickup loot, ANCPlayerCharacter user )
	{
		AAS_PlayerEntity myPlayer = Cast<AAS_PlayerEntity>( user );
		if ( IsValid( myPlayer ) )
			myPlayer.PlayBattleChatter( GameplayTags::Audio_VO_BattleChatter_PassiveWisp_Interaction );
		Server_EmitSoundAtLocation_1P( pickup1P, loot.GetActorLocation(), user );
		Dismiss();
	}

	UFUNCTION()
	void DestroyAfterPickup_LE( AAS_LootEntity loot, ANCPlayerCharacter user )
	{
		AAS_PlayerEntity myPlayer = Cast<AAS_PlayerEntity>( user );
		if ( IsValid( myPlayer ) )
			myPlayer.PlayBattleChatter( GameplayTags::Audio_VO_BattleChatter_PassiveWisp_Interaction );
		Server_EmitSoundAtLocation_1P( pickup1P, loot.GetActorLocation(), user );
		Dismiss();
	}

	UFUNCTION()
	void Dismiss()
	{
		PlayAnimation( n"dismiss" );
		System::SetTimer( this, n"PlayDismissFX", 1.0, false );
		System::SetTimer( this, n"DestroyFromDismiss", 1.67, false );
	}

	UFUNCTION()
	void PlayDismissFX()
	{
		
	}

	UFUNCTION()
	void OnPostReceivedDamage( UHealthComponent damagedComponent, const FDamageInfo&in damageInfo )
	{
		ScriptAssert(damagedComponent == health, "expected local health component");

		DestroyFromDamage();
	}

	UFUNCTION()
	void DestroyFromDismiss()
	{
		Destroy();
	}

	UFUNCTION()
	void DestroyFromDamage()
	{
		if ( IsValid( destroySfx ) )
            Server_EmitSoundAtLocation( destroySfx, GetActorLocation() );
		if ( IsValid( deathVFx ) )
		{
			FNiagaraVariablePackage varPackage;
			varPackage.AddVariableFloat( n"TeamColorFloat", 0 );
			Server_SpawnEffectAtLocation_OneShot_SetNiagaraVariables_WithFilter( deathVFx, SkeletalMeshComponent.GetBoneTransform( n"neck" ).GetLocation(), GetActorRotation(), varPackage, GetOwnerPlayer(), UNCNetEventFilterFriendly::StaticClass() );
			varPackage.AddVariableFloat( n"TeamColorFloat", 1 );
			Server_SpawnEffectAtLocation_OneShot_SetNiagaraVariables_WithFilter( deathVFx, SkeletalMeshComponent.GetBoneTransform( n"neck" ).GetLocation(), GetActorRotation(), varPackage, GetOwnerPlayer(), UNCNetEventFilterEnemy::StaticClass() );
		}

		Destroy();
	}

	UFUNCTION(BlueprintOverride)
	void EndPlay(EEndPlayReason EndPlayReason)
	{
		if ( IsValid( sustainSfx ) )
			sustainSfx.Destroy();
	}
}