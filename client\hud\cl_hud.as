delegate void FOnAnnouncementSplashAdded( UAS_CenterAnnouncementWidget widget );
event void FOnHUDInit();
event void FOnPawnChanged( AAS_PlayerEntity OldPawn, AAS_PlayerEntity NewPawn );
event void FOnLocalPlayerWeaponModsChanged( ANCWeapon weapon );
event void FOnLocalPlayerSavedWeaponsChanged( TArray<int> changedSlots );

enum EHUDMessageStyle
{
	KILL_MESSAGE,
	SMALL_CENTER,
}

enum EKillMessageType
{
	KILLED,
	ASSIST,
}

UCLASS( Abstract )
class AAS_HUD : AAS_BaseHUD
{
	UPROPERTY( NotVisible )
	protected AAS_PlayerEntity currentPawn;

	FOnHUDInit onHUDInitDelegate;
	FOnPawnChanged onLocalPawnChangedEvent;

	FOnLocalPlayerWeaponModsChanged onLocalPlayerWeaponModsChanged;
	FOnLocalPlayerSavedWeaponsChanged onLocalPlayerSavedWeaponsChanged;

	TMap<FGameplayTag, FScriptDelegateHandle> activeStatusesBegun;
	TMap<FGameplayTag, FScriptDelegateHandle> activeStatusesEnded;

	UPROPERTY( DefaultComponent, EditDefaultsOnly )
	UAS_HUD_DamageArrowComponent damageArrowComponent;

	UPROPERTY( DefaultComponent, EditDefaultsOnly )
	UAS_HUD_MaximapInputManager maximapInputComponent;

	UPROPERTY( DefaultComponent, EditDefaultsOnly )
	UAS_DisplayWidgetManager radialManager;

	UPROPERTY( DefaultComponent, EditDefaultsOnly )
	UAS_PinnedWidgetManagerComponent pinnedWidgetManager;

	UAS_InventoryMenu inventoryMenu;

	UPROPERTY( EditDefaultsOnly, Meta = ( Category = "HUD Widget Classes" ) )
	TSubclassOf<UAS_MenuWidget> maximapMenuClass;

	UPROPERTY( EditDefaultsOnly, Meta = ( Category = "HUD Widget Classes" ) )
	TSubclassOf<UAS_ScoreboardWidget> scoreboardClass;

	UPROPERTY( EditDefaultsOnly, Meta = ( Category = "HUD Widget Classes" ) )
	TSubclassOf<UAS_HitMarkerWidget> hitMarkerWidgetClass;

	UPROPERTY( EditDefaultsOnly, Meta = ( Category = "HUD Widget Classes" ) )
	TSubclassOf<UAS_MainHUDWidget> mainHUDWidgetClass;

	UPROPERTY( EditDefaultsOnly, Meta = ( Category = "HUD Widget Classes" ) )
	TSubclassOf<UAS_ScreenQuote> screenQuoteWidgetClass;

	UPROPERTY( EditDefaultsOnly, Meta = ( Category = "HUD Widget Classes" ) )
	TSubclassOf<UAS_DeadMenu_Basic> deadMenuRespawnSelectionClass;

	UPROPERTY( EditDefaultsOnly, Meta = ( Category = "HUD Widget Classes" ) )
	TSoftClassPtr<UAS_Menu_MatchEnd> matchEndMenuClass;

	UPROPERTY( EditDefaultsOnly, Meta = ( Category = "HUD Widget Classes" ) )
	TSoftClassPtr<UAS_VictoryOrDefeat> victoryOrDefeatClass;

	UPROPERTY( EditDefaultsOnly, Meta = ( Category = "HUD Widget Classes" ) )
	TSubclassOf<UAS_CenterAnnouncementWidget> centerAnnouncementClass;

	// should this be removed? seems like old redunant script that's not used anymore, CreatePinnableWidget pulls the class from the widget settings... not here
	UPROPERTY( EditDefaultsOnly, Meta = ( Category = "HUD Widget Classes" ) )
	TSubclassOf<UAS_PlayerNameWidget> playerNameWidgetClass;
	TMap<AAS_PlayerEntity, UAS_PlayerNameWidget> playerNames;

	UPROPERTY( EditDefaultsOnly, Meta = ( Category = "HUD Widget Classes" ) )
	private TSubclassOf<UAS_WaitingForPlayersWidget> waitingForPlayersClass;
	UAS_WaitingForPlayersWidget waitingForPlayersWidget;

	UAS_MainHUDWidget mainHUDWidget;
	private UAS_ScreenQuote screenQuoteWidget;
	private UAS_HitMarkerWidget hitMarkerWidget;
	private UAS_VictoryOrDefeat victoryOrDefeat;
	private UAS_DeadMenu_Basic deadMenu;
	UAS_Menu_MatchEnd matchEndMenu;

	private TArray<FName> hideAliveHUDRequests;
	private bool menuWantsHudHidden = false;
	private bool hudIsHidden = false;
	private TArray<FName> gameplayHideHudRequests;

	private FAudioResultData lowHealthHandle;
	FNCDeferredScriptAction updateLocalPlayerDeferred;

	TArray<ANCPlayerCharacter> registeredPawns;
	FOnAnnouncementSplashAdded onAnnouncementAdded;

	UFUNCTION( BlueprintOverride )
	void Tick( float DeltaSeconds )
	{
		pinnedWidgetManager.UpdatePinnableWidgets( DeltaSeconds );
	}

	UFUNCTION( BlueprintOverride )
	void OnHUDInitialized()
	{
		updateLocalPlayerDeferred.BindUFunction( this, n"UpdateLocalPlayerVisuals" );

		currentPawn = Cast<AAS_PlayerEntity>( ClientCallbacks().LocalPlayer );
		ScriptCallbacks().localClient_onLocalPlayerTeamChanged.Broadcast( -1, currentPawn.GetTeam() );

		currentPawn.OnEndPlay.AddUFunction( this, n"OnPlayerDestroyed" );

		Cl_Init( this );

		// TODO: Turn on when time to tackle asset manager issues
		// InitPinnableWidgetSettings();

		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"ServerCommandGenericMessage", n"SC_ServerCommandGenericMessage" );
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"ServerCommandPlayerScored", n"SC_OnPlayerScored" );
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"ServerCommand_NotifyBackpackFull", n"SC_OnBackpackFull" );
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"ServerCommand_BaseShieldDisplay", n"SC_BaseShieldDisplay" );
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"ServerCommand_FadeToWhite", n"SC_FadeToWhite" );
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"ServerCommand_FadeFromWhite", n"SC_FadeFromWhite" );
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"ServerCommand_FadeFromColor", n"SC_FadeFromColor" );
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"ServerCommand_FadeInOutWhite", n"SC_FadeInOutWhite" );
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"ServerCommand_FadeFromAny", n"SC_FadeFromAny" );
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"ServerCommandDisplayCenterMessage", n"SC_DisplayCenterMessage" );
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"ServerCommandKilledMessageDev", n"SC_ServerCommandKilledMessageDev" );
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"ServerCommandPlayerKilledMessageDev", n"SC_ServerCommandPlayerKilledMessageDev" );
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"ServerCommandPlayerAssistMessage", n"SC_ServerCommandPlayerAssistMessage" );
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"ServerCommand_SavedItemsChanged", n"SC_SavedItemsChanged" );
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"ServerCommand_NotifyGatherResource", n"SC_OnGather" );
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"ServerCommand_NotifyUltCooldownReduced", n"SC_OnUltCooldownReduced" );
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"ServerCommand_NotifyMatchAutoEnding", n"SC_NotifyMatchAutoEnding" );

		UCL_ScriptCallbacks clientCallbacks = ClientCallbacks();
		if ( IsValid( clientCallbacks ) )
		{
			clientCallbacks.OnLocalControllerReceivedDamage.AddUFunction( this, n"OnLocalPlayerTookDamage" );
			clientCallbacks.OnLocalControllerDealtDamage.AddUFunction( this, n"OnLocalPlayerDealtDamage" );
			clientCallbacks.OnPawnHealthChanged.AddUFunction( this, n"OnPawnHealthChanged" );
			clientCallbacks.OnPlayerChangedTeam.AddUFunction( this, n"OnPlayerChangedTeam" );
			clientCallbacks.OnPawnDied.AddUFunction( this, n"OnPawnDied" );
			clientCallbacks.OnPawnRespawned.AddUFunction( this, n"OnPawnRespawned" );
			clientCallbacks.OnTeamScoreChanged.AddUFunction( this, n"OnTeamScoreChanged" );
			clientCallbacks.OnGetRarityColorFromTag.BindUFunction( this, n"OnGetRarityColorFromTag" );
			clientCallbacks.OnGamePhaseChanged.AddUFunction( this, n"OnGamePhaseChanged" );
		}

		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			scriptCallbacks.client_onTeamEliminatedChanged.AddUFunction( this, n"OnTeamEliminatedChanged" );
			scriptCallbacks.RegisterSignalCallback( Signals::WEAP_LOCAL_PLAYER_MODS_CHANGED, this, n"OnLocalPlayerWeaponModsChangedSignal" );
		}

		ANCPlayerController playerController = Client_GetLocalPlayerController();
		if ( IsValid( playerController ) )
		{
			playerController.OnPossessedPawnChanged.AddUFunction( this, n"OnPawnChanged" );
		}

		pinnedWidgetManager.PinnedWidgetSolverInitialized( playerNameWidgetClass );

		SetNavigationConfig( false, false, false );

		OnLocalPawnSet( nullptr, currentPawn );

		InitAllPawns();

		scriptInitialized = true;
		onHUDInitDelegate.Broadcast();

		UNCClientReadyEvent readyEvent = Cast<UNCClientReadyEvent>( NewObject( GetCurrentWorld(), UNCClientReadyEvent::StaticClass() ) );
		readyEvent.SendToServer();

		ShieldSystem().client_onAnyPlayerShieldChanged.AddUFunction( this, n"OnAnyPawnShieldChanged" );

		OnGamePhaseChanged( -1, GetGamePhase() );

		// Preload menus
		UNCUIManager uiManager = GetUIManager();
		if ( IsValid( uiManager ) )
		{
			if ( maximapMenuClass != nullptr )
			{
				uiManager.PreloadScreenClass( maximapMenuClass );
			}

			if ( deadMenuRespawnSelectionClass != nullptr )
			{
				uiManager.PreloadScreenClass( deadMenuRespawnSelectionClass );
			}
		}

		ProgressionGameSystem().OnLoginComplete();

		playerController.GetLegacyInputComponent().BindAction( n"ToggleCharacterInfo", EInputEvent::IE_Pressed, FInputActionHandlerDynamicSignature( this, n"OnToggleCharacterInfo" ) );
		playerController.GetLegacyInputComponent().BindAction( n"ToggleLoadoutMenu", EInputEvent::IE_Pressed, FInputActionHandlerDynamicSignature( this, n"OnToggleLoadoutMenu" ) );
	}

	bool scriptInitialized = false;
	bool baseWidgetsExist  = false;
	TArray<UUserWidget> baseWidgets;

	UAS_CharacterSelectMenu chracterSelectMenu;
	UAS_MenuWidget baseSelectMenu;
	UAS_DebugMountAnimWidget1P debugMountAnimWidget1P;
	UAS_DebugMountAnimWidget3P debugMountAnimWidget3P;
	UAS_WeaponSightGuide devWeaponSightGuideWidget;
	UUserWidget debugAudioDebugger;

	FNCCoroutineSignal onGamePhaseChangedSignal;
	FNCCoroutineSignal onEpilogue;

	UFUNCTION( BlueprintEvent )
	protected void OnGamePhaseChanged( int OldState, int NewState )
	{
		onGamePhaseChangedSignal.Emit();

		Log( f"LogNCHUD: cl_hud.as OnGamePhaseChanged called with OldState {OldState} and NewState {NewState}" );

		// Note: All camera updates for game phase changes live in UAS_LevelCameraManager

		UAS_UIManager uiManager = GetASUIManager();
		if ( NewState == GamePhase::WAITING_FOR_PLAYERS )
		{
			if ( !IsValid( waitingForPlayersWidget ) )
			{
				waitingForPlayersWidget = Cast<UAS_WaitingForPlayersWidget>( WidgetBlueprint::CreateWidget( waitingForPlayersClass, GetOwningPlayerController() ) );
				waitingForPlayersWidget.AddToViewport( GameConst::ZORDER_HUD );
			}
			waitingForPlayersWidget.Show();
		}
		else if ( OldState == GamePhase::WAITING_FOR_PLAYERS )
		{
			if ( IsValid( waitingForPlayersWidget ) )
			{
				waitingForPlayersWidget.RemoveFromParent();
				waitingForPlayersWidget = nullptr;
			}
		}

		if ( NewState == GamePhase::CHARACTER_SELECT && !IsValid( chracterSelectMenu ) )
		{
			chracterSelectMenu = Cast<UAS_CharacterSelectMenu>( OpenMenu( n"ClassSelectMenu" ) );
		}
		else if ( NewState == GamePhase::BASE_SELECT )
		{
			FGameplayTag alias = GameplayTags::Audio_VO_GameUpdates_TeamObjective_BaseSelect;
			Dialogue().PlayAnnouncerDialogue( alias );
			baseSelectMenu = OpenMenu( n"BaseSelectMenu" );
		}

		if ( OldState == GamePhase::CHARACTER_SELECT )
		{
			if ( IsValid( chracterSelectMenu ) )
			{
				chracterSelectMenu.hideLevelOnClose = false;
				GetUIManager().CloseOpenScreen( chracterSelectMenu );
				chracterSelectMenu = nullptr;
			}
		}
		if ( OldState == GamePhase::BASE_SELECT )
		{
			if ( IsValid( baseSelectMenu ) )
			{
				GetUIManager().CloseOpenScreen( baseSelectMenu );
				baseSelectMenu = nullptr;
			}
		}

		if ( NewState == GamePhase::MATCH_INTRO )
		{
			PushHideHUDRequest( n"MatchIntro" );
		}
		if ( OldState == GamePhase::MATCH_INTRO )
		{
			PopHideHUDRequest( n"MatchIntro" );
		}

		if ( NewState == GamePhase::PREMATCH || OldState == GamePhase::PREMATCH )
		{
			// HACK for NC1-8789.
			// We assume current pawn is local player and all players are alive on phase change to playing
			if ( System::GetGameTimeInSeconds() < 1.0 )
				OnLocalPlayerSpawned( currentPawn );
		}

		if ( NewState < GamePhase::PREMATCH )
		{
			if ( !HasRaidMessagingHudHideRequest( n"MatchStart" ) )
				AddRaidMessagingHudHideRequest( n"MatchStart" );
		}
		else
		{
			if ( HasRaidMessagingHudHideRequest( n"MatchStart" ) )
				RemoveRaidMessagingHudHideRequest( n"MatchStart" );
		}

		if ( NewState == GamePhase::PREMATCH )
		{
			// When we enter the real game, we want to show the raid messaging hud
			//if ( !GetCvarBool( "ScriptDebug.NewRaidMessaging" ) )
			//	AddRaidMessagingHudHideRequest( n"Prematch" );
		}
		if ( OldState == GamePhase::PREMATCH )
		{
			//if ( HasRaidMessagingHudHideRequest( n"Prematch" ) )
			//	RemoveRaidMessagingHudHideRequest( n"Prematch" );
		}

		if ( NewState == GamePhase::WINNER_DETERMINED )
		{
			Thread( this, n"SpawnVictoryOrDefeat" );
			pinnedWidgetManager.RemoveAllPinnableWidgets();
			UpdateSaturation();

			// When the match concludes, we want to close the raid messaging hud
			AddRaidMessagingHudHideRequest( n"WinnerDeterminedPhase" );
		}

		if ( NewState == GamePhase::EPILOGUE )
		{
			Print( "Started Epilogue" );
			onEpilogue.Emit();
			SpawnMatchEndMenu();
			pinnedWidgetManager.RemoveAllPinnableWidgets();
			UpdateSaturation();
		}
	}

	void EnableLoadoutHint()
	{
		mainHUDWidget.EnableLoadoutHint();
	}

	void DisableLoadoutHint()
	{
		// no function to unbind????
		mainHUDWidget.DisableLoadoutHint();
	}

	void UpdateSaturation()
	{
		if ( saturationEffectActive )
		{
			ClearDesaturation();
		}

		if ( initialized )
			mainHUDWidget.UpdateAlivePanel();
	}

	UFUNCTION()
	void SpawnVictoryOrDefeat( UNCCoroutine co )
	{
		co.EndOn( this, onGamePhaseChangedSignal );
		PushHideHUDRequest( n"SpawnVictoryOrDefeat" );

		co.OnCoroutineEnd.AddUFunction( this, n"CleanupVictoryOrDefeat" );
		CloseAllMenus();
		TSubclassOf<UAS_VictoryOrDefeat> vectorOrDefeatClassResolved = System::LoadClassAsset_Blocking( victoryOrDefeatClass );
		victoryOrDefeat												 = Cast<UAS_VictoryOrDefeat>( WidgetBlueprint::CreateWidget( vectorOrDefeatClassResolved, GetOwningPlayerController() ) );
		victoryOrDefeat.AddToViewport( GameConst::ZORDER_SCREEN );
		victoryOrDefeat.Show();

		bool draw = GetWinner() == GameWinner::DRAW;
		if ( !draw )
			OnSpawnVictoryOrDefeat( co );
		else
			co.WaitForever();
	}

	void OnSpawnVictoryOrDefeat( UNCCoroutine co )
	{
		Print( "OnSpawnVictoryOrDefeat -- started" );
		co.EndOn( this, onEpilogue );
		float32 timeLeft = GameModeDefaults().epilogueDelaySeconds;
		timeLeft -= GameConst::WINNER_DETERMINED_CAMERA_SWAP_DELAY;
		co.Wait( GameConst::WINNER_DETERMINED_CAMERA_SWAP_DELAY );
		victoryOrDefeat.HideAnim();
		Print( "OnSpawnVictoryOrDefeat -- fade out #1" );
		ScreenFade().ScreenFadeFromColorToColor( FLinearColor( 1, 1, 1, 0 ), FLinearColor( 1, 1, 1, 1 ), 1 );
		co.OnCoroutineEnd.AddUFunction( this, n"CleanupVictoryOrDefeatFade" );
		timeLeft -= 1.1;
		co.Wait( 1.1 );
		TryLoadMatchEndPodiumGeo( nullptr, NAME_None );
		co.OnCoroutineEnd.Unbind( this, n"CleanupVictoryOrDefeatFade" );
		Print( "OnSpawnVictoryOrDefeat -- fade in #1" );
		ScreenFade().ScreenFadeToColor( FLinearColor( 1, 1, 1, 0 ), 0.5 );
		int defeatedTeam = GetOtherTeam( GetWinner() );
		victoryOrDefeat.SetExplosionCamera( defeatedTeam );
		timeLeft -= 1.0;
		co.Wait( 1.0 );
		victoryOrDefeat.TriggerExplosion( defeatedTeam );
		timeLeft -= 0.3;
		co.Wait( 0.3 );
		victoryOrDefeat.TriggerCameraShake();
		if ( timeLeft > 1.0 )
		{
			co.Wait( timeLeft - 1.0 );
			AAS_BaseExplosionCamera camera = Cast<AAS_BaseExplosionCamera>( victoryOrDefeat.spectateCamera );
			if ( IsValid( camera ) )
				camera.DisableTick();
			co.OnCoroutineEnd.AddUFunction( this, n"CleanupVictoryOrDefeatFade" );
			Print( "OnSpawnVictoryOrDefeat -- fade out #2" );
			ScreenFade().ScreenFadeFromColorToColor( FLinearColor( 1, 1, 1, 0 ), FLinearColor( 1, 1, 1, 1 ), 1 );
		}
		co.Wait( 5 ); // 5 second timeout just in case epilogue doesn't happen
	}

	UFUNCTION()
	private void CleanupVictoryOrDefeatFade( FNCCoroutineEndParams endParams )
	{
		Print( "OnSpawnVictoryOrDefeat -- fade out final" );
		ScreenFade().ScreenFadeToColor( FLinearColor( 1, 1, 1, 0 ), 1 );
	}

	UFUNCTION()
	private void CleanupVictoryOrDefeat( FNCCoroutineEndParams endParams )
	{
		victoryOrDefeat.RemoveFromParent();
		victoryOrDefeat = nullptr;
	}

	void CreateBasewidgets()
	{
		DestroyBaseWidgets();

		mainHUDWidget = Cast<UAS_MainHUDWidget>( WidgetBlueprint::CreateWidget( mainHUDWidgetClass, GetOwningPlayerController() ) );
		mainHUDWidget.AddToViewport();
		baseWidgets.Add( mainHUDWidget );

		screenQuoteWidget = Cast<UAS_ScreenQuote>( WidgetBlueprint::CreateWidget( screenQuoteWidgetClass, GetOwningPlayerController() ) );
		screenQuoteWidget.AddToViewport();
		baseWidgets.Add( screenQuoteWidget );

		hitMarkerWidget = Cast<UAS_HitMarkerWidget>( WidgetBlueprint::CreateWidget( hitMarkerWidgetClass, GetOwningPlayerController() ) );
		hitMarkerWidget.AddToViewport( GameConst::ZORDER_OVER_HUD );
		baseWidgets.Add( hitMarkerWidget );

		baseWidgetsExist = true;

		ScriptCallbacks().localClient_hudOnPlayerReady.Broadcast( this );
	}

	void DestroyBaseWidgets()
	{
		for ( UUserWidget w : baseWidgets )
		{
			w.RemoveFromParent();
		}
		baseWidgets.Empty();

		baseWidgetsExist = false;
	}

	UFUNCTION( NotBlueprintCallable )
	void SetPlayerNameVisibleForEnemies( AAS_PlayerEntity player, bool isVisible )
	{
		if ( !playerNames.Contains( player ) )
		{
			Print( f"Warning! Tried to set player name hidden for a player without a name! {player}" );
			return;
		}

		playerNames[player].isVisibleForEnemies = isVisible;
	}

	UFUNCTION( BlueprintOverride )
	void BlueprintOnUseItemChanged( UUsableItemComponent oldUseComponent, UUsableItemComponent newUseComponent )
	{
		AActor oldOwner = GetComponentOwnerOrNull( oldUseComponent );
		AActor newOwner = GetComponentOwnerOrNull( newUseComponent );

		if ( oldOwner == newOwner )
			return;

		if ( IsClient() )
		{
			UAS_SimpleClientHighlightComponent oldHighlightComp = IsValid( oldOwner ) ? oldOwner.GetSimpleClientHighlightComponent() : nullptr;
			if ( IsValid( oldHighlightComp ) )
				oldHighlightComp.SetIsHighlighted( false );
			UAS_SimpleClientHighlightComponent newHighlightComp = IsValid( newOwner ) ? newOwner.GetSimpleClientHighlightComponent() : nullptr;
			if ( IsValid( newHighlightComp ) )
				newHighlightComp.SetIsHighlighted( true );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void OnPawnDied( ANCPlayerCharacter Pawn )
	{
		AAS_PlayerEntity asPawn = Cast<AAS_PlayerEntity>( Pawn );
		PawnCleanup( asPawn, false );

		if ( Pawn.IsLocallyControlled() )
		{
			OnLocalPlayerDied( asPawn );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void OnPawnRespawned( ANCPlayerCharacter Pawn )
	{
		CreatePlayerNameWidgetIfNeeded( Pawn );

		AAS_PlayerEntity asPawn = Cast<AAS_PlayerEntity>( Pawn );
		if ( playerNames.Contains( asPawn ) )
		{
			SetWidgetVisibilitySafe( playerNames[asPawn], ESlateVisibility::HitTestInvisible );
		}

		OnTeamChanged( Pawn, Pawn.GetTeam() );

		if ( Pawn.EntIndex == currentPawn.EntIndex )
		{
			OnLocalPlayerSpawned( currentPawn );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void CreatePlayerNameWidgetIfNeeded( ANCPlayerCharacter Pawn )
	{
		AAS_PlayerEntity asPawn = Cast<AAS_PlayerEntity>( Pawn );
		if ( IsValid( asPawn ) && !asPawn.IsLocallyControlled() && !playerNames.Contains( asPawn ) )
		{
			UAS_PinnableWidgetSettings settings = GetPinnableWidgetSettingsByName( n"PlayerName" );
			UAS_PinnableWidget newWidget		= pinnedWidgetManager.CreatePinnableWidget( settings, asPawn.GetPlayerMesh3P(), n"neck_head" );
			UAS_PlayerNameWidget w				= Cast<UAS_PlayerNameWidget>( newWidget );
			SetWidgetVisibilitySafe( w, ESlateVisibility::HitTestInvisible );
			w.SetOwnerActor( asPawn );
			playerNames.Add( asPawn, w );

			FHealthChangedInfo healthInfo;
			healthInfo.pawn		 = asPawn;
			healthInfo.oldHealth = 0.0f;
			healthInfo.newHealth = asPawn.GetHealth();

			healthInfo.oldShieldHealth = 0.0f;
			healthInfo.newShieldHealth = asPawn.GetShieldHealth();
			OnPawnHealthChanged( healthInfo );
		}
	}

	bool saturationEffectActive = false;

	UFUNCTION( BlueprintEvent )
	void OnLocalPlayerDied( AAS_PlayerEntity player )
	{
		if ( GetGamePhase() < GamePhase::WINNER_DETERMINED )
		{
			saturationEffectActive = true;
			player.GetTPPCameraComponent().ZoomInputActionUp();
			player.GetTPPCameraComponent().ForceEnableTPP();
			player.GetTPP_Camera().PostProcessSettings.bOverride_ColorSaturation = true;
			player.GetTPP_Camera().PostProcessSettings.ColorSaturation			 = FVector4( 0, 0, 0, 1 );
			player.GetTPP_SpringArm().TargetOffset.Z							 = -75;
		}

		if ( initialized )
			mainHUDWidget.UpdateAlivePanel();
	}

	UFUNCTION()
	private void OnTeamScoreChanged( int TeamId, int NewScore )
	{
	}

	UFUNCTION()
	private FLinearColor OnGetRarityColorFromTag( FGameplayTag rarityTag )
	{
		return GetRarityColor( rarityTag );
	}

	UFUNCTION()
	private void OnTeamEliminatedChanged( int team, bool oldValue, bool newValue )
	{
	}

	void SpawnMatchEndMenu()
	{
		if ( !initialized )
		{
			Print( "SpawnMatchEndMenu -- failed, not initialized" );
			return;
		}

		if ( matchEndMenuClass.IsNull() )
		{
			Print( "SpawnMatchEndMenu -- failed, matchEndMenuClass is null" );
			return;
		}

		if ( !GameModeDefaults().GamemodeRules_MatchSummaryEnabled )
		{
			Print( "SpawnMatchEndMenu -- failed, match summary is not enabled" );
			return;
		}

		CloseAllMenus();
		Print( "SpawnMatchEndMenu -- fade in" );
		ScreenFade().ScreenFadeFromColorToColor( FLinearColor( 1, 1, 1, 1 ), FLinearColor( 1, 1, 1, 0 ), 2 );

		TSubclassOf<UAS_Menu_MatchEnd> matchEndClassResolved = System::LoadClassAsset_Blocking( matchEndMenuClass );
		matchEndMenu										 = Cast<UAS_Menu_MatchEnd>( OpenMenuClass( matchEndClassResolved, true ) );
	}

	UFUNCTION()
	void OnDeadMenuStateChanged( int invalidValue, int newValue ) // invalid because we're calling directly and can't always rely on the prev state
	{
		if ( EDeadMenuState( newValue ) == EDeadMenuState::SHOW )
		{
			if ( initialized )
				SpawnDeadMenu();
		}
		else
		{
			CleanupDeadMenu();
		}
	}

	UFUNCTION( BlueprintEvent )
	void OnLocalPlayerSpawned( AAS_PlayerEntity player )
	{
		if ( !IsValid( player ) )
		{
			return;
		}

		if ( GetGamePhase() < GamePhase::WINNER_DETERMINED )
		{
			ClearDesaturation();
		}

		if ( initialized )
			mainHUDWidget.UpdateAlivePanel();

		ScreenFade().ScreenFadeFromColorToColor( FLinearColor::Black, FLinearColor( 1, 1, 1, 0 ), 1.0 );
	}

	void ClearDesaturation()
	{
		saturationEffectActive												 = false;
		AAS_PlayerEntity player												 = Client_GetLocalASPawn();
		player.GetTPP_SpringArm().TargetOffset.Z							 = 0;
		player.GetTPP_Camera().PostProcessSettings.bOverride_ColorSaturation = false;
		player.GetTPPCameraComponent().ForceDisableTPP( 0 );
	}

	UFUNCTION( BlueprintOverride )
	void OnEscapeButtonPressed()
	{
		if ( matchAutoEnding )
		{
			SaveClientPersistence();
			UNCGameInstance gi = Cast<UNCGameInstance>( GetGameInstance() );
			gi.LeaveMatch();
			return;
		}

		if ( IsValid( GetUIManager() ) && GetUIManager().GetActiveScreen() == nullptr )
		{
			UAS_MenuWidget Menu = Cast<UAS_MenuWidget>( OpenMenu( n"InGameMenu" ) );
		}
		else
		{
			Super::OnEscapeButtonPressed();
		}
	}

	FTimerHandle gamepadStartButtonDownHandle;

	UFUNCTION( BlueprintOverride )
	void OnGamepadStartButtonPressed()
	{
		if ( matchAutoEnding )
		{
			SaveClientPersistence();
			UNCGameInstance gi = Cast<UNCGameInstance>( GetGameInstance() );
			gi.LeaveMatch();
			return;
		}

		if ( GetUIManager().IsActive( GetMenu( n"InGameMenu" ) ) )
		{
			Super::OnGamepadStartButtonPressed();
			return;
		}
		gamepadStartButtonDownHandle = System::SetTimer( this, n"Delayed_OpenOptionsMenu", 0.3, false );
	}

	UFUNCTION( BlueprintOverride )
	void OnGamepadStartButtonReleased()
	{
		if ( System::IsValidTimerHandle( gamepadStartButtonDownHandle ) )
		{
			System::ClearAndInvalidateTimerHandle( gamepadStartButtonDownHandle );
			if ( GetUIManager().GetActiveScreen() == nullptr )
			{
				ToggleInventory();
			}
			else
				Super::OnGamepadStartButtonPressed();
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void Delayed_OpenOptionsMenu()
	{
		System::ClearAndInvalidateTimerHandle( gamepadStartButtonDownHandle );

		UAS_MenuWidget Menu = Cast<UAS_MenuWidget>( OpenMenu( n"InGameMenu" ) );
	}

	float lastLoadoutMenuToggleTime;

	UFUNCTION()
	void OnToggleLoadoutMenu( FKey Key )
	{
		if ( !IsValid( mainHUDWidget ) )
			return;
		if ( !mainHUDWidget.shouldShowLoadoutHint )
			return;

		float curTime = System::GetGameTimeInSeconds();
		if ( curTime - lastLoadoutMenuToggleTime > 0.2 )
		{
			lastLoadoutMenuToggleTime = curTime;
			ToggleLoadoutMenu();
		}
	}

	UFUNCTION()
	void OnToggleCharacterInfo( FKey Key )
	{
		if ( GetGamePhase() < GamePhase::PREMATCH )
			return;

		UAS_UIManager uiManager = GetASUIManager();
		if ( IsValid( uiManager ) )
		{
			if ( uiManager.IsActive( UAS_InventoryMenu::StaticClass() ) )
			{
				// do nothing
				// uiManager.CloseIfActive( UAS_InventoryMenu::StaticClass() );
			}
			else
			{
				uiManager.CloseAllScreensInstantly();
				UAS_InventoryMenu inventory = Cast<UAS_InventoryMenu>( uiManager.OpenAndCacheMenu( GameConst::MENU_NAME_INVENTORY ) );
				if ( IsValid( inventory ) )
					inventory.SetTabCharacterInfo();
			}
		}
	}

	UFUNCTION( BlueprintOverride )
	void OnGamepadBackButtonPressed()
	{
		if ( mainHUDWidget.shouldShowLoadoutHint )
		{
			ToggleLoadoutMenu();
		}
		else
		{
			ToggleMaximap();
		}
	}

	void ToggleLoadoutMenu()
	{
		if ( !initialized )
			return;

		if ( GetUIManager().IsActive( UAS_WeaponLoadoutMenu::StaticClass() ) )
		{
			CloseCurrentMenu();
			return;
		}

		if ( !IsAlive( currentPawn ) )
			return;

		OpenMenu( n"WeaponLoadoutMenu" );
	}

	void ToggleMaximap()
	{
		// Ignore input for opening map until map is relevant
		if ( GetGamePhase() < GamePhase::PREMATCH )
			return;

		UNCUIManager uiManager = GetASUIManager();
		if ( IsValid( uiManager ) )
		{
			if ( uiManager.IsActive( UAS_MaximapMenu::StaticClass() ) )
			{
				uiManager.CloseIfActive( UAS_MaximapMenu::StaticClass() );
			}
			else
			{
				uiManager.OpenNewOrCachedScreenClass( maximapMenuClass, false );
			}
		}
	}

	void ToggleInventory()
	{
		// Ignore input for inventoy until inventory is relevant
		if ( GetGamePhase() < GamePhase::PREMATCH )
			return;

		UAS_UIManager uiManager = GetASUIManager();
		if ( IsValid( uiManager ) )
		{
			if ( uiManager.IsActive( UAS_InventoryMenu::StaticClass() ) )
			{
				uiManager.CloseIfActive( UAS_InventoryMenu::StaticClass() );
			}
			else
			{
				uiManager.CloseAllScreensInstantly();
				uiManager.OpenAndCacheMenu( GameConst::MENU_NAME_INVENTORY );
			}
		}
	}

	UFUNCTION( BlueprintCallable )
	void ToggleUI()
	{
		if ( mainHUDWidget != nullptr )
		{
			if ( GetUIManager().GetActiveScreen() == nullptr )
			{
				if ( !gameplayHideHudRequests.Contains( n"ToggleUI" ) )
				{
					PushHideHUDRequest( n"ToggleUI" );
					AddRaidMessagingHudHideRequest( n"ToggleUI" );
				}
				else
				{
					PopHideHUDRequest( n"ToggleUI" );
					if ( HasRaidMessagingHudHideRequest( n"ToggleUI" ) )
						RemoveRaidMessagingHudHideRequest( n"ToggleUI" );
				}
			}
			else
			{
				GetUIManager().DebugToggleUI();
			}
		}
	}

	UFUNCTION( BlueprintEvent )
	void OnLocalPawnSet( const ANCPlayerCharacter oldPawn, const ANCPlayerCharacter newPawn )
	{
		currentPawn			 = Cast<AAS_PlayerEntity>( newPawn );
		AAS_PlayerEntity old = Cast<AAS_PlayerEntity>( oldPawn );

		if ( IsValid( old ) )
		{
			DestroyBaseWidgets();
			old.OnDestroyed.UnbindObject( this );
			old.OnEndPlay.UnbindObject( this );
			old.net_deadMenuState.OnReplicated().UnbindObject( this );
			for ( TMapIterator<FGameplayTag, FScriptDelegateHandle> elem : activeStatusesBegun )
			{
				old.RemoveStatusEffectHasBegunCallback( elem.Key, elem.Value );
			}
			activeStatusesBegun.Empty();
			for ( TMapIterator<FGameplayTag, FScriptDelegateHandle> elem : activeStatusesEnded )
			{
				old.RemoveStatusEffectHasBegunCallback( elem.Key, elem.Value );
			}
			activeStatusesBegun.Empty();
		}

		if ( IsValid( currentPawn ) )
		{
			if ( !baseWidgetsExist )
				CreateBasewidgets();
			currentPawn.OnDestroyed.AddUFunction( this, n"OnPawnDestroyed" );
			currentPawn.OnEndPlay.AddUFunction( this, n"OnPawnEndPlay" );
			currentPawn.net_deadMenuState.OnReplicated().AddUFunction( this, n"OnDeadMenuStateChanged" );
			activeStatusesBegun.Add( GameplayTags::StatusEffect_OutsideDomeScanned, currentPawn.AddStatusEffectHasBegunCallback( GameplayTags::StatusEffect_OutsideDomeScanned, this, n"OnOutsideDomeStarted" ) );
			activeStatusesEnded.Add( GameplayTags::StatusEffect_OutsideDomeScanned, currentPawn.AddStatusEffectHasEndedCallback( GameplayTags::StatusEffect_OutsideDomeScanned, this, n"OnOutsideDomeEnded" ) );
			OnDeadMenuStateChanged( -1, currentPawn.net_deadMenuState );
		}

		FHealthChangedInfo healthInfo;
		healthInfo.pawn = currentPawn;

		healthInfo.oldHealth = 0.0f;
		healthInfo.newHealth = currentPawn.GetHealth();

		healthInfo.oldShieldHealth = 0.0f;
		healthInfo.newShieldHealth = currentPawn.GetShieldHealth();

		OnPawnHealthChanged( healthInfo );

		// Show the dead menu if the pawn was already dead after reconnecting
		if ( !currentPawn.IsAlive() )
		{
			OnLocalPlayerDied( currentPawn );
		}

		if ( baseWidgetsExist )
		{
			mainHUDWidget.OnPossessedPawnChanged( old, currentPawn );
		}
	}

	UFUNCTION()
	private void OnOutsideDomeStarted( ANCPlayerCharacter player )
	{
		if ( baseWidgetsExist )
		{
			SetWidgetVisibilitySafe( mainHUDWidget.outOfBaseDetectionPanel, ESlateVisibility::HitTestInvisible );
		}
	}

	UFUNCTION()
	private void OnOutsideDomeEnded( ANCPlayerCharacter player )
	{
		if ( baseWidgetsExist )
		{
			SetWidgetVisibilitySafe( mainHUDWidget.outOfBaseDetectionPanel, ESlateVisibility::Collapsed );
		}
	}

	UFUNCTION()
	private void OnAnyPawnShieldChanged( AAS_PlayerEntity player, FShieldItemData newShielddata )
	{
		FHealthChangedInfo healthInfo;
		healthInfo.pawn = player;

		healthInfo.oldHealth = 0.0f;
		healthInfo.newHealth = player.GetHealth();

		healthInfo.oldShieldHealth = 0.0f;
		healthInfo.newShieldHealth = player.GetShieldHealth();

		if ( player.IsLocallyControlled() )
			OnPawnHealthChanged( healthInfo );

		if ( playerNames.Contains( player ) )
		{
			UpdatePlayerNameHealthAndShields( healthInfo );
		}
	}

	void InitAllPawns()
	{
		TArray<AAS_PlayerEntity> allPawns;
		GetAllActorsOfClass( AAS_PlayerEntity::StaticClass(), allPawns );
		for ( AAS_PlayerEntity asPawn : allPawns )
		{
			if ( IsValid( asPawn ) && asPawn.IsReady() )
			{
				OnPawnReady( asPawn );
			}
		}
	}

	void PawnCleanup( AAS_PlayerEntity Pawn, bool destroyed )
	{
		registeredPawns.Remove( Pawn );

		Pawn.OnDestroyed.UnbindObject( this );
		Pawn.OnEndPlay.UnbindObject( this );

		if ( playerNames.Contains( Pawn ) )
		{
			if ( destroyed )
			{
				pinnedWidgetManager.RemovePinnableWidget( playerNames[Pawn] );
				playerNames.Remove( Pawn );
			}
			else
			{
				SetWidgetVisibilitySafe( playerNames[Pawn], ESlateVisibility::Hidden );
			}
		}

		if ( IsValid( Pawn.GetPlayerMesh3P() ) )
			Pawn.GetPlayerMesh3P().SetRenderCustomDepth( false );
		if ( IsValid( Pawn.GetWeapon3P( EViewmodelArm::MainHand ) ) )
			Pawn.GetWeapon3P( EViewmodelArm::MainHand ).SetRenderCustomDepth( false );
		if ( IsValid( Pawn.GetWeapon3P( EViewmodelArm::AltHand ) ) )
			Pawn.GetWeapon3P( EViewmodelArm::AltHand ).SetRenderCustomDepth( false );
	}

	UFUNCTION( NotBlueprintCallable )
	void OnPawnReady( ANCPlayerCharacter Pawn )
	{
		Pawn.OnDestroyed.AddUFunction( this, n"OnPawnDestroyed" );
		Pawn.OnEndPlay.AddUFunction( this, n"OnPawnEndPlay" );
		registeredPawns.Add( Pawn );
		OnTeamChanged( Pawn, Pawn.GetTeam() );
	}

	UFUNCTION()
	private void OnPlayerDestroyed( AActor Actor, EEndPlayReason EndPlayReason )
	{
		ANCPlayerCharacter pawn = Cast<ANCPlayerCharacter>( Actor );
		OnPawnDestroyed( pawn );
		DestroyBaseWidgets();
	}

	UFUNCTION( NotBlueprintCallable )
	void OnPawnEndPlay( AActor Pawn, EEndPlayReason Reason )
	{
		OnPawnDestroyed( Pawn );
	}

	UFUNCTION( NotBlueprintCallable )
	void OnPawnDestroyed( AActor Pawn )
	{
		AAS_PlayerEntity asPawn = Cast<AAS_PlayerEntity>( Pawn );
		Log( f"OnPawnDestroyed() called to clean up widgets for {asPawn.GetPlayerNameAsString()}" );
		PawnCleanup( asPawn, true );

		if ( asPawn.IsLocallyControlled() )
			DestroyBaseWidgets();
		// else
		// 	ScriptError_Silent_WithBug( "OnPawnDestroyed called without calling clientCallbacks.OnPawnDied", "cpineda", f"player - {asPawn.GetPlayerName()}\nclient callbacks - {ClientCallbacks()}, valid? {IsValid(ClientCallbacks())}" );
	}

	UFUNCTION( NotBlueprintCallable )
	void OnTeamChanged( ANCPlayerCharacter PS, int NewTeam )
	{
		if ( !IsValid( currentPawn ) )
			return;

		if ( !registeredPawns.Contains( PS ) )
		{
			OnPawnReady( PS );
			return;
		}

		AAS_PlayerEntity asPawn = Cast<AAS_PlayerEntity>( PS );
		// If the local player changed teams then update everyone else's friendly status.
		// Otherwise we only care about the pawn that changed.
		if ( currentPawn == asPawn )
		{
			TArray<AAS_PlayerEntity> allPawns;
			GetAllActorsOfClass( AAS_PlayerEntity::StaticClass(), allPawns );
			for ( AAS_PlayerEntity pawn : allPawns )
			{
				if ( IsValid( pawn ) && pawn != asPawn )
				{
					const EAffinity affinity = UNCUtils::GetRelationshipBetweenTeams( currentPawn.GetTeam(), pawn.GetTeam() );
					SetPawnIsFriendly( pawn, affinity == EAffinity::Friendly );
				}
			}
		}
		else
		{
			const EAffinity affinity = UNCUtils::GetRelationshipBetweenTeams( currentPawn.GetTeam(), asPawn.GetTeam() );
			SetPawnIsFriendly( asPawn, affinity == EAffinity::Friendly );
		}
	}

	void SetPawnIsFriendly( AAS_PlayerEntity asPlayer, bool isFriendly )
	{
		if ( !IsValid( asPlayer.GetPlayerMesh3P() ) )
			return;

		// only do this for friendlies... not myself
		if ( asPlayer.IsLocallyControlled() )
			return;

		asPlayer.highlightManager.RefreshHighlight();

		if ( IsValid( asPlayer ) && playerNames.Contains( asPlayer ) )
		{
			playerNames[asPlayer].SetIsFriendly( isFriendly );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void OnPlayerChangedTeam( ANCPlayerCharacter Player, int OldTeam, int NewTeam )
	{
		OnTeamChanged( Player, NewTeam );
		if ( Player == currentPawn )
		{
			ScriptCallbacks().localClient_onLocalPlayerTeamChanged.Broadcast( OldTeam, NewTeam );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void OnPawnHealthChanged( FHealthChangedInfo healthInfo )
	{
		const ANCPlayerCharacter char = healthInfo.pawn;

		if ( !IsValid( char ) )
			return;

		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( char );
		if ( playerNames.Contains( asPlayer ) )
		{
			UpdatePlayerNameHealthAndShields( healthInfo );
		}

		if ( asPlayer.IsLocallyControlled() )
		{
			updateLocalPlayerDeferred.ExecuteOnceLater( ENCDeferredScriptActionExecutionTime::UpdateGameObjectsBeforeHUD );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void UpdateLocalPlayerVisuals()
	{
		AAS_PlayerEntity asPlayer = currentPawn;

		if ( !IsValid( asPlayer ) )
			return;

		// Desaturation from health damage comes on quick
		const float32 normalizedHealth	  = asPlayer.GetHealth() / asPlayer.GetMaxHealth();
		const float normalizedHealthEased = Math::EaseIn( 0, 1, normalizedHealth, 2 );
		float saturationMapped			  = Math::GetMappedRangeValueClamped( FVector2D( 1, 0.3 ), FVector2D( 1, 0.3 ), normalizedHealthEased );

		if ( normalizedHealth <= 0 )
		{
			saturationMapped = 1;
		}

		// Slight desaturation from shield damage, capping at 25%
		// Also doing this so if you're not at 100%, desat post process is visible, so on-damage-screen-flash shows up.
		const float safeMaxShieldHealth	   = asPlayer.GetMaxShieldHealth() > 0 ? asPlayer.GetMaxShieldHealth() : 1;
		const float normalizedShieldHealth = asPlayer.GetShieldHealth() / safeMaxShieldHealth;
		saturationMapped				   = Math::GetMappedRangeValueClamped( FVector2D( 0.75, 1 ), FVector2D( Math::Min( saturationMapped, 0.75 ), saturationMapped ), normalizedShieldHealth );

		GetScreenEffect_1PDesaturation().SetSaturationAmount( saturationMapped );

		if ( lowHealthHandle.EventID < 0 && normalizedHealth < 0.51 && IsAlive( asPlayer ) )
		{
			Client_StopSound( lowHealthHandle.EventID );
			lowHealthHandle = Client_EmitSoundOnEntity( Audio().lowHealthSound, asPlayer );
		}
		else if ( lowHealthHandle.EventID >= 0 && ( normalizedHealth >= 0.51 || !IsAlive( asPlayer ) ) )
		{
			Client_StopSound( lowHealthHandle.EventID );
			lowHealthHandle.EventID = -1;
		}
	}

	void UpdatePlayerNameHealthAndShields( FHealthChangedInfo healthInfo )
	{
		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( healthInfo.pawn );
		float32 normalizedHealth  = asPlayer.GetHealthNormalized();
		playerNames[asPlayer].OnHealthChanged( normalizedHealth );

		float32 shieldPercent = 0.0f;
		if ( asPlayer.GetMaxShieldHealth() > 0.0f )
			shieldPercent = healthInfo.newShieldHealth / asPlayer.GetMaxShieldHealth();

		playerNames[asPlayer].OnShieldChanged( shieldPercent, asPlayer );
	}

	UFUNCTION( NotBlueprintCallable )
	void OnLocalPlayerTookDamage( const APawn attackerPawn, const AActor victim, FVector damageSourceLocation, FVector damageImpactLocation, float32 damageAmount, int damageFlags, int scriptDamageFlags )
	{
		AAS_PlayerEntity localPawn = Client_GetLocalASPawn();

		if ( Bitflags::HasFlag( scriptDamageFlags, EScriptDamageFlags::DF_HARDENED ) )
			Client_EmitSoundUI( Abilities().overshieldHitSound_Victim );
		else if ( Bitflags::HasFlag( damageFlags, EDamageFlags::DF_SHIELD_BREAK ) )
			Client_EmitSoundUI( ClGlobals().shieldBreakSound_Victim );
		else if ( Bitflags::HasFlag( scriptDamageFlags, EScriptDamageFlags::DF_ON_FIRE ) )
		{
			Client_EmitSoundUI( ClGlobals().tookDamageSoundFire );
		}
		else if ( Bitflags::HasFlag( scriptDamageFlags, EScriptDamageFlags::DF_ELECTRICAL ) )
		{
			if ( damageAmount < 5.0f )
				Client_EmitSoundUI( ClGlobals().tookDamageSoundElectrical_low );
			else
				Client_EmitSoundUI( ClGlobals().tookDamageSoundElectrical_high );
		}
		else if ( scriptDamageFlags & EScriptDamageFlags::DF_FALL_DAMAGE == 0 )
			Client_EmitSoundUI( ClGlobals().tookDamageSound );

		GetScreenEffect_1PDesaturation().ShowTookDamageFlash();
	}

	UFUNCTION( NotBlueprintCallable )
	void OnLocalPlayerDealtDamage( const APawn attackerPawn, const AActor victim, FVector damageSourceLocation, FVector damageImpactLocation, float32 damageAmount, int damageFlags, int scriptDamageFlags )
	{
		AAS_PlayerEntity playerVictim = Cast<AAS_PlayerEntity>( victim );
		AAS_VehicleMount mountVictim  = Cast<AAS_VehicleMount>( victim );
		AAS_TargetDummy dummyVictim	  = Cast<AAS_TargetDummy>( victim );

		if ( damageAmount > 0 && ShouldPlayDamageDealtEffectOnActor( victim ) )
		{
			const FCachedDealtDamageData damageData = FCachedDealtDamageData( attackerPawn, victim, damageSourceLocation, damageImpactLocation, damageAmount, damageFlags, scriptDamageFlags );
			hitMarkerWidget.PlayDamageDealtEffect( damageData );
		}

		if ( ( IsValid( playerVictim ) || IsValid( mountVictim ) || IsValid( dummyVictim ) ) && !Bitflags::HasFlag( scriptDamageFlags, EScriptDamageFlags::DF_HIDDEN_DAMAGE ) )
		{
			if ( Bitflags::HasFlag( damageFlags, EDamageFlags::DF_KILLSHOT ) )
			{
				if ( IsValid( mountVictim ) )
				{
					FVehicleMountData data = mountVictim.GetMountData();
					Client_EmitSoundUI( data.Asset.Audio.mountKilled1P_vs_3P );
				}
				else
				{
					Client_EmitSoundUI( DoNotShipGlobals().uiImpactSounds.killShot );

					if ( IsValid( playerVictim ) )
					{
						AAS_PlayerEntity localPawn = Client_GetLocalASPawn();
						if ( localPawn.GetStatusEffectValue( GameplayTags::StatusEffect_AlchemistKillStreak ) > 0 )
							Client_EmitSoundUI( Passive_ReplaceGrenade().hotStreakKillSound1P );

						// 1P kill message
						FText message = GetLocalizedText( Localization::HUDMainWidget, f"HUDMAIN_PLAYER_KILL", FFormatArgumentValue( playerVictim.GetPlayerNameAsText() ) );
						mainHUDWidget.DisplayKillMessage( message );
					}
				}
			}
			else if ( Bitflags::HasFlag( scriptDamageFlags, EScriptDamageFlags::DF_DOWNED ) )
				Client_EmitSoundUI( DoNotShipGlobals().uiImpactSounds.downedShot );
			else if ( Bitflags::HasFlag( scriptDamageFlags, EScriptDamageFlags::DF_HARDENED ) )
				Client_EmitSoundUI( Abilities().overshieldHitSound_Attacker );
			else if ( Bitflags::HasFlag( damageFlags, EDamageFlags::DF_SHIELD_BREAK ) )
				Client_EmitSoundUI( ClGlobals().shieldBreakSound_Attacker );
			else if ( Bitflags::HasFlag( damageFlags, EDamageFlags::DF_FALLOFFDAMAGE ) )
				Client_EmitSoundUI( DoNotShipGlobals().uiImpactSounds.ineffectiveShot );
			else if ( Bitflags::HasFlag( scriptDamageFlags, EScriptDamageFlags::DF_HARDENED ) )
				Client_EmitSoundUI( DoNotShipGlobals().uiImpactSounds.ineffectiveShot );
			else if ( IsValid( playerVictim ) && playerVictim.GetIsDowned() )
				Client_EmitSoundUI( DoNotShipGlobals().uiImpactSounds.ineffectiveShot );
		}
	}

	UFUNCTION()
	bool ShouldPlayDamageDealtEffectOnActor( const AActor victim )
	{
		if ( !IsValid( victim ) )
			return false;

		if ( victim.IsA( AAS_PlayerEntity::StaticClass() ) )
			return true;
		if ( victim.IsA( AAS_Dev_MovingTarget::StaticClass() ) )
			return true;
		if ( victim.IsA( AAS_TargetDummy::StaticClass() ) )
			return true;
		if ( victim.IsA( AAS_LightningRod::StaticClass() ) )
			return true;
		if ( victim.IsA( AAS_LittleBuddy::StaticClass() ) )
			return true;
		if ( victim.IsA( AAS_SoulShield::StaticClass() ) )
			return true;
		if ( victim.IsA( AAS_VehicleMount::StaticClass() ) )
			return true;
		if ( victim.IsA( AAS_GoldRushDepositBox::StaticClass() ) )
			return true;
		if ( victim.IsA( ANCDestructible::StaticClass() ) )
			return true;
		if ( victim.IsA( AAS_SoulBond_AOE::StaticClass() ) )
			return true;

		return false;
	}

	// Called by player entity, keeps the hitmarker aligned with reticle
	// Really wants a refactor
	void SetHitmarkerPosition( FVector2D newPosition )
	{
		if ( !IsValid( hitMarkerWidget ) )
		{
			return;
		}

		hitMarkerWidget.SetPositionInViewport( newPosition );
		hitMarkerWidget.SetAlignmentInViewport( FVector2D( 0.5, 0.5 ) );
	}

	UFUNCTION( NotBlueprintCallable )
	void OnPawnChanged( APawn OldPawn, APawn NewPawn )
	{
		AAS_PlayerEntity old = currentPawn;
		currentPawn			 = Cast<AAS_PlayerEntity>( NewPawn );

		FHealthChangedInfo healthInfo;
		healthInfo.pawn = currentPawn;

		if ( IsValid( currentPawn ) )
		{
			healthInfo.oldHealth = currentPawn.GetHealth();
			healthInfo.newHealth = currentPawn.GetHealth();

			healthInfo.oldShieldHealth = currentPawn.GetShieldHealth();
			healthInfo.newShieldHealth = currentPawn.GetShieldHealth();
		}
		else
		{
			healthInfo.oldHealth = 0;
			healthInfo.newHealth = 0;

			healthInfo.oldShieldHealth = 0;
			healthInfo.newShieldHealth = 0;
		}

		OnPawnHealthChanged( healthInfo );

		onLocalPawnChangedEvent.Broadcast( old, currentPawn );

		InitAllPawns();
	}

	void OnPerfectAxeCombo( FText message )
	{
		if ( mainHUDWidget != nullptr )
			mainHUDWidget.DisplayAxePerfect( message );
	}

	void OnAxeComboFail( FText message )
	{
		if ( mainHUDWidget != nullptr )
			mainHUDWidget.DisplayAxeFail( message );
	}

	void OnBackpackFull( FGameplayTag lootTag )
	{
		if ( mainHUDWidget == nullptr )
			return;

		UAS_BackpackComponent fullBackpack = currentPawn.GetBackpackThatAllowsIndex( lootTag );

		if ( IsValid( fullBackpack ) )
			mainHUDWidget.DisplayGenericMessage( GetLocalizedText( Localization::Backpack, fullBackpack.fullMessageKey ), EHUDMessageStyle::SMALL_CENTER );
	}

	AActor baseShieldTrackedActor;
	FTimerHandle baseShieldsTickHandle;
	// currently just using the "base shield" widget, but will eventually give each wall an in world widget
	UFUNCTION( NotBlueprintCallable )
	void SC_BaseComponentHealthDisplay( TArray<FString> args )
	{
		SetWidgetVisibilitySafe( mainHUDWidget.baseRaidShieldOverlay, ESlateVisibility::HitTestInvisible );

		float currentHealth = args[0].ToFloat();
		float maxHealth		= args[1].ToFloat();

		int trackedActorEntIdx = -1;
		if ( args.Num() > 2 )
			trackedActorEntIdx = args[2].ToInt();

		AActor trackedActor = GetEntity( trackedActorEntIdx );
		if ( IsValid( trackedActor ) )
		{
			baseShieldTrackedActor = trackedActor;
			if ( !System::IsValidTimerHandle( baseShieldsTickHandle ) )
				baseShieldsTickHandle = System::SetTimer( this, n"BaseShieldsUpdateTick", 0.05, true );
		}

		if ( GameModeDefaults().GamemodeRules_DisplayWallHealth )
			Print( f"Base structure health - {currentHealth} / {maxHealth}" );

		SetBaseShieldWidgetPercent( currentHealth, maxHealth );
		System::SetTimer( this, n"BaseShieldHide", 2.0, false );
	}

	UFUNCTION()
	private void BaseShieldsUpdateTick()
	{
		if ( !IsValid( baseShieldTrackedActor ) )
		{
			System::ClearAndInvalidateTimerHandle( baseShieldsTickHandle );
			return;
		}

		float32 curHealth = baseShieldTrackedActor.GetHealth();
		float32 maxHealth = baseShieldTrackedActor.GetMaxHealth();
		SetBaseShieldWidgetPercent( curHealth, maxHealth );
	}

	private void SetBaseShieldWidgetPercent( float currentHealth, float maxHealth )
	{
		mainHUDWidget.baseRaidShieldWidget.SetPercent( currentHealth >= maxHealth ? 1.0 : currentHealth / maxHealth );

		mainHUDWidget.baseRaidShieldGeneratorsBox.ClearChildren();
	}

	// Deprecated?
	UFUNCTION( NotBlueprintCallable )
	void SC_BaseShieldDisplay( TArray<FString> args )
	{
		// SetWidgetVisibilitySafe(mainHUDWidget.baseRaidShieldWidget, ESlateVisibility::HitTestInvisible);
		SetWidgetVisibilitySafe( mainHUDWidget.baseRaidShieldOverlay, ESlateVisibility::HitTestInvisible );

		float currentHealth		  = args[0].ToFloat();
		float maxHealth			  = args[1].ToFloat();
		float numShieldGenerators = args[2].ToInt();

		mainHUDWidget.baseRaidShieldWidget.SetPercent( currentHealth > GameConst::RAID_SHIELD_STRENGTH ? 1.0 : currentHealth / GameConst::RAID_SHIELD_STRENGTH );

		float shieldGenHealth = currentHealth - GameConst::RAID_SHIELD_STRENGTH;
		mainHUDWidget.baseRaidShieldGeneratorsBox.ClearChildren();
		for ( int i = 0; i < numShieldGenerators; ++i )
		{
			UAS_ShieldGenWidget shieldGenBar = Cast<UAS_ShieldGenWidget>( WidgetBlueprint::CreateWidget( mainHUDWidget.shieldGeneratorHUDClass, Client_GetLocalPlayerController() ) );
			shieldGenBar.SetPercent( shieldGenHealth > GameConst::RAID_SHIELD_GENERATOR_STRENGTH ? 1.0 : shieldGenHealth / GameConst::RAID_SHIELD_GENERATOR_STRENGTH );
			mainHUDWidget.baseRaidShieldGeneratorsBox.AddChildToHorizontalBox( shieldGenBar );

			shieldGenHealth -= GameConst::RAID_SHIELD_GENERATOR_STRENGTH;
		}

		System::SetTimer( this, n"BaseShieldHide", 2.0, false );
	}

	UFUNCTION( NotBlueprintCallable )
	void BaseShieldHide()
	{

		// SetWidgetVisibilitySafe(mainHUDWidget.baseRaidShieldWidget, ESlateVisibility::Hidden);
		SetWidgetVisibilitySafe( mainHUDWidget.baseRaidShieldOverlay, ESlateVisibility::Hidden );
		System::ClearAndInvalidateTimerHandle( baseShieldsTickHandle );
	}

	UFUNCTION( NotBlueprintCallable )
	void SC_OnBackpackFull( TArray<FString> args )
	{
		int lootIndex		 = args[0].ToInt();
		FLootDataStruct data = GetLootDataByIntIndex( lootIndex );
		OnBackpackFull( data.index );
	}

	UFUNCTION( NotBlueprintCallable )
	private void SC_ServerCommandGenericMessageTest( TArray<FString> args )
	{
	}

	UFUNCTION( NotBlueprintCallable )
	private void SC_ServerCommandGenericMessage( TArray<FString> args )
	{
		if ( args.Num() < 3 )
			return;

		EHUDMessageStyle style = EHUDMessageStyle( args[0].ToInt() );
		FName tableId		   = FName( args[1] );
		FString key			   = args[2];

		// TODO: Verry temp!! Converting FText to string just to limit size of this checkin.
		if ( mainHUDWidget.GetVisibility() != ESlateVisibility::Hidden )
			mainHUDWidget.DisplayGenericMessage( GetLocalizedText_FromServerCommandString( tableId, key, args, 3 ), style );
	}

	UFUNCTION( NotBlueprintCallable )
	private void SC_ServerCommandKilledMessageDev( TArray<FString> args )
	{
		if ( args.Num() < 4 )
			return;

		int playerIndex			 = args[0].ToInt();
		EKillMessageType msgType = EKillMessageType( args[3].ToInt() );

		ANCPlayerCharacter player = GetPlayer( playerIndex );

		FText message;
		bool isAssist = false;
		switch ( msgType )
		{
			case EKillMessageType::KILLED:
				message = GetLocalizedText( Localization::HUDMainWidget, f"HUDMAIN_PLAYER_KILL", FFormatArgumentValue( player.GetPlayerNameAsText() ) );
				break;

			case EKillMessageType::ASSIST:
				message	 = GetLocalizedText( Localization::HUDMainWidget, f"HUDMAIN_PLAYER_ASSIST", FFormatArgumentValue( player.GetPlayerNameAsText() ) );
				isAssist = true;
				break;

			default:
				break;
		}

		if ( mainHUDWidget.GetVisibility() != ESlateVisibility::Hidden )
		{
			if ( isAssist )
				mainHUDWidget.DisplayAssistMessage( message );
			else
				mainHUDWidget.DisplayKillMessage( message );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void SC_ServerCommandPlayerKilledMessageDev( TArray<FString> args )
	{
		if ( args.Num() < 2 )
			return;

		int killerIndex = args[0].ToInt();
		int victimIndex = args[1].ToInt();

		if ( mainHUDWidget.GetVisibility() != ESlateVisibility::Hidden )
		{
			ANCPlayerCharacter killer = GetPlayer( killerIndex );
			ANCPlayerCharacter victim = GetPlayer( victimIndex );

			mainHUDWidget.DisplayKillMessage( GetLocalizedText( Localization::HUDMainWidget, f"HUDMAIN_PLAYER_KILLED_PLAYER", FFormatArgumentValue( killer.GetPlayerNameAsText() ), FFormatArgumentValue( victim.GetPlayerNameAsText() ) ) );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void SC_ServerCommandPlayerAssistMessage( TArray<FString> args )
	{
		if ( args.Num() < 1 )
			return;

		int victimIdx = args[0].ToInt();

		if ( mainHUDWidget.GetVisibility() != ESlateVisibility::Hidden )
		{
			ANCPlayerCharacter victim = GetPlayer( victimIdx );

			mainHUDWidget.DisplayAssistMessage( GetLocalizedText( Localization::HUDMainWidget, f"HUDMAIN_PLAYER_ASSIST", FFormatArgumentValue( victim.GetPlayerNameAsText() ) ) );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void SC_OnPlayerScored( TArray<FString> args )
	{
	}

	UFUNCTION( NotBlueprintCallable )
	private void SC_FadeToWhite( TArray<FString> args )
	{
		float fadeDuration = args[0].ToFloat();

		ScreenFade().ScreenFadeToColor( FLinearColor::White, fadeDuration );
	}

	UFUNCTION( NotBlueprintCallable )
	private void SC_FadeFromWhite( TArray<FString> args )
	{
		float fadeDuration = args[0].ToFloat();

		ScreenFade().ScreenFadeFromColorToColor( FLinearColor::White, FLinearColor( 1, 1, 1, 0 ), fadeDuration );
	}

	UFUNCTION( NotBlueprintCallable )
	private void SC_FadeFromColor( TArray<FString> args )
	{
		float r					= args[0].ToFloat();
		float g					= args[1].ToFloat();
		float b					= args[2].ToFloat();
		float a					= args[3].ToFloat();
		FLinearColor startColor = FLinearColor( r, g, b, a );
		FLinearColor fadeColor	= FLinearColor( r, g, b, 0 );
		float fadeDuration		= args[4].ToFloat();

		ScreenFade().ScreenFadeFromColorToColor( startColor, fadeColor, fadeDuration );
	}

	private float queuedFadeOutTime = 0;
	UFUNCTION( NotBlueprintCallable )
	private void SC_FadeInOutWhite( TArray<FString> args )
	{
		System::ClearTimer( this, f"QueuedFadeOut" );

		float fadeInDuration = args[0].ToFloat();
		float holdDuration	 = args[1].ToFloat();
		queuedFadeOutTime	 = args[2].ToFloat();

		ScreenFade().ScreenFadeToColor( FLinearColor::White, fadeInDuration );

		System::SetTimer( this, n"QueuedFadeOut", fadeInDuration + holdDuration, false );
	}

	UFUNCTION( NotBlueprintCallable )
	private void QueuedFadeOut()
	{
		ScreenFade().ScreenFadeToColor( FLinearColor( 1, 1, 1, 0 ), queuedFadeOutTime );
	}

	UFUNCTION( NotBlueprintCallable )
	private void SC_FadeFromAny( TArray<FString> args )
	{
		float fadeDuration	 = args[0].ToFloat();
		FLinearColor ToColor = ScreenFade().GetCurrentScreenFadeColor();
		ToColor.A			 = 0;

		ScreenFade().ScreenFadeToColor( ToColor, fadeDuration );
	}

	UFUNCTION( NotBlueprintCallable )
	void SC_DisplayCenterMessage( TArray<FString> args )
	{
		if ( args.Num() < 2 )
			return;

		FName tableId	= FName( args[0] );
		FString rowName = args[1];

		DisplayCenterMessage( tableId, rowName, Audio().quest_Complete_3P );
	}

	void DisplayCenterMessage( FName tableId, FString rowName, UNCAudioAsset overrideAudio = nullptr )
	{
		FLinearColor themeColor;
		GetSafeColor( n"gold", themeColor );
		DisplayAnnouncementSplash( GetLocalizedText( tableId, rowName ), FText(), themeColor, -1, 3, overrideAudio );
	}

	UFUNCTION()
	UUserWidget DisplayAnnouncementSplash( FText msgText, FText descriptionText, FLinearColor themeColor, float32 extraHoldTime_mid = -1, float32 extraHoldTime_final = -1, UNCAudioAsset sound = nullptr )
	{
		UAS_CenterAnnouncementWidget currentAnnouncement = Cast<UAS_CenterAnnouncementWidget>( WidgetBlueprint::CreateWidget( centerAnnouncementClass, GetOwningPlayerController() ) );
		currentAnnouncement.InitColors( themeColor );
		currentAnnouncement.SetTitleAndSubtitle( msgText, descriptionText );
		currentAnnouncement.Start( extraHoldTime_mid, extraHoldTime_final, sound );
		currentAnnouncement.onAnimatedOut.AddUFunction( this, n"OnAnnouncementDone" );
		AddSplashMessage( currentAnnouncement );

		if ( IsValid( splashMessageContainer ) )
		{
			onAnnouncementAdded.ExecuteIfBound( currentAnnouncement );
		}
		else
		{
			currentAnnouncement.SetAnchorsInViewport( FAnchors( 0.5, 0.25 ) );
			currentAnnouncement.SetAlignmentInViewport( FVector2D( 0.5, 0.5 ) );
			// currentAnnouncement.SetPositionInViewport( FVector2D( 0, 0 ) );
		}

		return currentAnnouncement;
	}

	UFUNCTION()
	private void OnAnnouncementDone( UAS_CenterAnnouncementWidget widget )
	{
		widget.onAnimatedOut.UnbindObject( this );
		if ( widget == currentSplashMessage )
		{
			ClearCurrentSplashMessage();
		}
	}

	private UPanelWidget splashMessageContainer;
	UNC_DisplayWidget currentSplashMessage;

	void SetSplashMessageContainer( UPanelWidget newContainer, FOnAnnouncementSplashAdded onAddedDelegate )
	{
		splashMessageContainer = newContainer;
		onAnnouncementAdded	   = onAddedDelegate;
	}

	void AddSplashMessage( UNC_DisplayWidget splashMessage )
	{
		if ( IsValid( splashMessageContainer ) )
		{
			for ( int i = splashMessageContainer.GetChildrenCount() - 1; i >= 0; i-- )
			{
				splashMessageContainer.RemoveChildAt( i );
			}
		}
		else
		{
			if ( IsValid( currentSplashMessage ) )
			{
				currentSplashMessage.RemoveFromParent();
			}
		}

		currentSplashMessage = splashMessage;

		if ( IsValid( splashMessageContainer ) )
			splashMessageContainer.AddChild( splashMessage );
		else
			splashMessage.AddToViewport( GameConst::ZORDER_SCREEN );

		splashMessage.Show();
	}

	void ClearCurrentSplashMessage()
	{
		if ( IsValid( currentSplashMessage ) )
		{
			currentSplashMessage.Hide();
			currentSplashMessage = nullptr;
		}
	}

	void DEBUG_ForceFixHUD()
	{
		hudIsHidden = false;

		if ( IsValid( mainHUDWidget ) )
			mainHUDWidget.Show();

		for ( auto ReticleWidgetRef : ReticleWidgetRefs )
		{
			if ( IsValid( ReticleWidgetRef ) )
				ReticleWidgetRef.Show();
		}

		FString details = f"menuWantsHudHidden: {menuWantsHudHidden} \nhideHudRequests count:{gameplayHideHudRequests.Num()}";
		for ( FName request : gameplayHideHudRequests )
		{
			details += f"\n - {request}";
		}

		ScriptError_Silent_WithBug( "Broken HUD info", "cpineda", details );
		Print( details );

		menuWantsHudHidden = false;
		gameplayHideHudRequests.Empty();
	}

	// only the UI manager should call this
	void MenuHideHUD()
	{
		menuWantsHudHidden = true;
		_UpdateGameplayHUDVisibility();
	}

	// only the UI manager should call this
	void MenuShowHUD()
	{
		menuWantsHudHidden = false;
		_UpdateGameplayHUDVisibility();
	}

	void PushHideHUDRequest( FName requestId )
	{
		ScriptAssert( !gameplayHideHudRequests.Contains( requestId ), f"GameplayRequestHideHUD: {requestId} already added" );

		gameplayHideHudRequests.Add( requestId );
		_UpdateGameplayHUDVisibility();
	}

	void PopHideHUDRequest( FName requestId )
	{
		ScriptAssert( gameplayHideHudRequests.Contains( requestId ), f"GameplayRequestHideHUD: {requestId} not present" );

		gameplayHideHudRequests.Remove( requestId );
		_UpdateGameplayHUDVisibility();
	}

	private void _UpdateGameplayHUDVisibility()
	{
		if ( menuWantsHudHidden || gameplayHideHudRequests.Num() > 0 )
			_HideGameplayHUD();
		else
			_ShowGameplayHUD();
	}

	private void _HideGameplayHUD()
	{
		if ( hudIsHidden )
			return;

		hudIsHidden = true;

		if ( IsValid( mainHUDWidget ) )
			mainHUDWidget.Hide();

		for ( auto ReticleWidgetRef : ReticleWidgetRefs )
		{
			if ( IsValid( ReticleWidgetRef ) )
				ReticleWidgetRef.Hide();
		}
	}

	private void _ShowGameplayHUD()
	{
		if ( !hudIsHidden )
			return;

		hudIsHidden = false;

		if ( IsValid( mainHUDWidget ) )
			mainHUDWidget.Show();

		for ( auto ReticleWidgetRef : ReticleWidgetRefs )
		{
			if ( IsValid( ReticleWidgetRef ) )
				ReticleWidgetRef.Show();
		}
	}

	void SpawnDeadMenu()
	{
		if ( !initialized )
			return;

		if ( deadMenuRespawnSelectionClass == nullptr )
			return;

		if ( matchEndMenu != nullptr )
			return;

		CleanupDeadMenu();

		if ( GetGamePhase() >= GamePhase::WINNER_DETERMINED )
			return;

		CloseAllMenus();

		if ( deadMenuRespawnSelectionClass != nullptr )
		{
			UAS_UIManager uiManager = GetASUIManager();
			if ( !IsValid( uiManager ) )
				return;

			deadMenu = Cast<UAS_DeadMenu_Basic>( uiManager.OpenNewOrCachedScreenClass( deadMenuRespawnSelectionClass ) );
		}
	}

	void CleanupDeadMenu()
	{
		if ( deadMenu != nullptr )
		{
			GetUIManager().CloseOpenScreen( deadMenu );
			deadMenu = nullptr;
		}
	}

	FNCCoroutineSignal delayedOpenMenuSignal;

	UAS_MenuWidget OpenMenuClass( TSubclassOf<UAS_MenuWidget> menuClass, bool closeOtherMenus = false, bool onlyOpenIfNoOtherMenusAreOpen = false, bool showPreviousMenu = false ) override
	{
		if ( initialized )
			return Super::OpenMenuClass( menuClass, closeOtherMenus, onlyOpenIfNoOtherMenusAreOpen, showPreviousMenu );
		else
			Thread( this, n"Delayed_OpenMenuClass", menuClass, closeOtherMenus, onlyOpenIfNoOtherMenusAreOpen, showPreviousMenu );

		return nullptr;
	}

	UFUNCTION()
	void Delayed_OpenMenuClass( UNCCoroutine co, TSubclassOf<UAS_MenuWidget> menuClass, bool closeOtherMenus, bool onlyOpenIfNoOtherMenusAreOpen, bool showPreviousMenu )
	{
		delayedOpenMenuSignal.Emit();
		co.EndOn( this, delayedOpenMenuSignal );
		co.Wait( 0.01 );
		OpenMenuClass( menuClass, closeOtherMenus, onlyOpenIfNoOtherMenusAreOpen, showPreviousMenu );
	}

	UFUNCTION( NotBlueprintCallable )
	void SC_SavedItemsChanged( TArray<FString> args )
	{
		TArray<int> changedSlots;
		for ( FString argVal : args )
		{
			int intVal = argVal.ToInt();
			changedSlots.Add( intVal );
		}

		onLocalPlayerSavedWeaponsChanged.Broadcast( changedSlots );
	}

	////////////////////////////////////
	/////// COMPASS AND OBJECTIVES /////
	////////////////////////////////////

	// UFUNCTION(BlueprintCallable)
	// void RegisterActorOnCompass( AActor actor, TSubclassOf<UAS_CompassMarkerWidget> markerWidgetClass = UAS_CompassMarkerWidget::StaticClass() )
	//{
	//     //if ( IsValid( mainHUDWidget.compassWidget ) )
	//     //    mainHUDWidget.compassWidget.RegisterActorToCompass( actor, markerWidgetClass );
	//     //else
	//     //    ScriptError( f"RegisterActorOnCompass called when mainHUDWidget.compassWidget was invalid" );
	// }

	// UFUNCTION(BlueprintCallable)
	// void DeregisterActorFromCompass( AActor actor )
	//{
	//     //if ( !IsValid( mainHUDWidget.compassWidget ) )
	//     //    return;

	//    //mainHUDWidget.compassWidget.DeregisterActorFromCompass( actor );
	//}

	UFUNCTION( NotBlueprintCallable )
	void OnLocalPlayerWeaponModsChangedSignal( FName signalName, UObject sender )
	{
		UAS_BaseWeaponScriptContext context = Cast<UAS_BaseWeaponScriptContext>( sender );
		if ( !IsValid( context ) )
			return;

		ANCWeapon ownerWeapon = context.GetOwnerWeapon();
		if ( !IsValid( ownerWeapon ) )
			return;

		onLocalPlayerWeaponModsChanged.Broadcast( ownerWeapon );
	}

	void PushHideAlivePanel( FName requestId )
	{
		ScriptAssert( !hideAliveHUDRequests.Contains( requestId ), f"PushHideAlivePanel: {requestId} already pushed" );
		hideAliveHUDRequests.AddUnique( requestId );
		if ( baseWidgetsExist )
			mainHUDWidget.UpdateAlivePanel();
	}

	void PopHideAlivePanel( FName requestId )
	{
		ScriptAssert( hideAliveHUDRequests.Contains( requestId ), f"PopHideAlivePanel: {requestId} not found" );
		hideAliveHUDRequests.Remove( requestId );
		if ( baseWidgetsExist )
			mainHUDWidget.UpdateAlivePanel();
	}

	bool ShouldAliveHudBeVisible()
	{
		return hideAliveHUDRequests.Num() == 0;
	}

	void ShowTeammateDisconnectWarning( float32 timeRemaining )
	{
		if ( baseWidgetsExist )
			mainHUDWidget.ShowTeammateDisconnectWarning( timeRemaining );
	}

	void HideTeammmateDisconnectWarning()
	{
		if ( baseWidgetsExist )
			mainHUDWidget.HideTeammmateDisconnectWarning();
	}

	UFUNCTION( NotBlueprintCallable )
	private void SC_OnGather( TArray<FString> args )
	{
		if ( args.Num() < 3 )
			return;

		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			FLootDataStruct lootData = GetLootDataByIntIndex( args[0].ToInt() );
			int amount				 = GetDesiredLootCount( lootData, args[1].ToInt() );
			scriptCallbacks.localClient_OnGatherEvent.Broadcast( lootData, amount, args[2].ToInt() );
		}
	}

	bool matchAutoEnding = false;

	UFUNCTION( NotBlueprintCallable )
	private void SC_NotifyMatchAutoEnding( TArray<FString> args )
	{
		matchAutoEnding = true;

		FLinearColor themeColor;
		GetSafeColor( n"gold", themeColor );
		if ( IsValid( waitingForPlayersWidget ) )
		{
			waitingForPlayersWidget.RemoveFromParent();
			waitingForPlayersWidget = nullptr;
		}
		DisplayAnnouncementSplash( GetLocalizedText( Localization::MatchEnd, "not_enough_players" ), GetLocalizedText( Localization::MatchEnd, "auto_ending_match" ), themeColor, -1, 10.0, Audio().quest_Complete_3P );
	}

	UFUNCTION( NotBlueprintCallable )
	private void SC_OnUltCooldownReduced( TArray<FString> args )
	{
		if ( args.Num() < 1 )
			return;

		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			float32 amount = float32( args[0].ToFloat() );
			scriptCallbacks.localClient_OnUltimateCooldownReduced.Broadcast( amount );
		}
	}
}