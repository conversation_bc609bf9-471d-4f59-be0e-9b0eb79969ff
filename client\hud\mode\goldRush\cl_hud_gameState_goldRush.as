UCLASS( Abstract )
class UAS_GoldRushTopWidget : UUserWidgetDefault
{
	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UHorizontalBox coinWidgetContainer;
	TMap<AAS_CoinDeposit, UAS_CoinDepositWidget> allCoinWidgets;

	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<UAS_CoinDepositWidget> coinWidgetClass;

	FNCDeferredScriptAction sortAction;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		sortAction.BindUFunction( this, n"SortWidgetsDeferred" );

		TArray<AAS_CoinDeposit> allDeposits;
		GetAllActorsOfClass( allDeposits );
		for ( AAS_CoinDeposit c : allDeposits )
		{
			UAS_CoinDepositWidget w = Cast<UAS_CoinDepositWidget>( WidgetBlueprint::CreateWidget( coinWidgetClass, GetOwningPlayer() ) );
			c.net_depositIndex.OnReplicated().AddUFunction( this, n"OnCoinDepositUpdated" );
			w.showText				   = false;
			w.animateOnCompleteDeposit = true;
			UHorizontalBoxSlot slot	   = coinWidgetContainer.AddChildToHorizontalBox( w );
			slot.SetPadding( FMargin( 10, 20 ) );
			w.objectiveText.SetVisibility( ESlateVisibility::Collapsed );
			w.objectiveTextSpacer.SetVisibility( ESlateVisibility::Collapsed );
			w.SetOwnerActor( c );
			allCoinWidgets.Add( c, w );
			c.OnEndPlay.AddUFunction( this, n"OnCoinDepositEndPlay" );
		}
		SortCoinDeposits();
	}

	UFUNCTION()
	private void OnCoinDepositUpdated( int oldValue, int newValue )
	{
		SortCoinDeposits();
	}

	UFUNCTION()
	private void OnCoinDepositEndPlay( AActor Actor, EEndPlayReason EndPlayReason )
	{
		SortCoinDeposits();
	}

	void SortCoinDeposits()
	{
		sortAction.ExecuteOnceLater( ENCDeferredScriptActionExecutionTime::UpdateGameObjectsBeforeHUD );
	}

	UFUNCTION()
	private void SortWidgetsDeferred()
	{
		TMap<AAS_CoinDeposit, UAS_CoinDepositWidget> widgetsCopy = allCoinWidgets;
		TArray<AAS_CoinDeposit> sorted;
		for ( auto elem : allCoinWidgets )
		{
			elem.Value.RemoveFromParent();
			elem.Key.OnEndPlay.Unbind( this, n"OnCoinDepositEndPlay" );
		}
		allCoinWidgets.Empty();

		for ( auto elem : widgetsCopy )
		{
			if ( !IsValid( elem.Key ) )
				continue;

			bool added = false;
			for ( int i = 0; i < sorted.Num(); i++ )
			{
				if ( int( sorted[i].net_depositIndex ) > int( elem.Key.net_depositIndex ) )
				{
					sorted.Insert( elem.Key, i );
					added = true;
					break;
				}
			}
			if ( !added )
			{
				sorted.Add( elem.Key );
			}
		}

		for ( AAS_CoinDeposit box : sorted )
		{
			allCoinWidgets.Add( box, widgetsCopy[box] );
			UHorizontalBoxSlot slot = coinWidgetContainer.AddChildToHorizontalBox( widgetsCopy[box] );
			slot.SetPadding( FMargin( 10, 20 ) );
			box.OnEndPlay.AddUFunction( this, n"OnCoinDepositEndPlay" );
		}
	}
}

UCLASS( Abstract )
class UAS_RaidModeGameState_GoldRush : UAS_GameStateWidget
{
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset coinCollectSound;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	UTextBlock walletLockedText;

	int currentRank = -1;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"NotifyDepositedGold", n"SC_NotifyDepositedGold" );
		UNCRemoteScriptCommands::RegisterServerCommandOnClass( this, n"NotifyTeleportHint", n"SC_NotifyTeleportHint" );

		ScriptCallbacks().client_onNextPhaseTimeChanged.AddUFunction( this, n"OnNextPhaseTimeChanged" );
		OnNextPhaseTimeChanged( 0, GetGameStateEntity().nextPhaseTime );

		ScriptCallbacks().localClient_sbExpireTimeChanged.AddUFunction( this, n"OnShieldBreakerExpireTimeChanged" );
		ScriptCallbacks().client_onRaidObjectiveStateChanged.AddUFunction( this, n"OnTeamObjectiveStateChanged" );

		GetGameStateEntity_GoldRush().net_walletsLocked.OnReplicated().AddUFunction( this, n"OnWalletLockedChanged" );
		OnWalletLockedChanged( false, GetGameStateEntity_GoldRush().net_walletsLocked );
	}

	int cachedNextPhaseTime;

	UFUNCTION()
	private void OnNextPhaseTimeChanged( int oldValue, int newValue )
	{
		cachedNextPhaseTime = newValue;
	}

	UFUNCTION()
	private void OnShieldBreakerExpireTimeChanged( int oldValue, int newValue )
	{
	}

	UFUNCTION()
	private void OnTeamObjectiveStateChanged( int team, int oldValue, int newValue )
	{
	}

	UFUNCTION()
	private void OnWalletLockedChanged( bool oldValue, bool newValue )
	{
		walletLockedText.SetVisibility( newValue ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Hidden );

		if ( IsValid( GetLocalHUD() ) )
		{
			if ( newValue )
				GetLocalHUD().mainHUDWidget.SetScriptedHint( FHUDHintData( GetLocalizedText( Localization::GoldRush, "wallet_safe" ) ), 10.0 );
			else if ( System::GetGameTimeInSeconds() > 5 )
				GetLocalHUD().mainHUDWidget.SetScriptedHint( FHUDHintData( GetLocalizedText( Localization::GoldRush, "wallet_not_safe" ) ), 10.0 );
		}
	}

	UFUNCTION()
	void SC_NotifyDepositedGold( TArray<FString> args )
	{
		int count = args[0].ToInt();
		GetLocalHUD().mainHUDWidget.DisplayGenericMessage( GetLocalizedText( Localization::GoldRush, "added_gold", FFormatArgumentValue( count ) ), EHUDMessageStyle::SMALL_CENTER );
		Client_EmitSoundUI( coinCollectSound );
	}

	UFUNCTION()
	void SC_NotifyTeleportHint( TArray<FString> args )
	{
		GetLocalHUD().mainHUDWidget.SetScriptedHint( FHUDHintData( GetLocalizedText( Localization::GoldRush, "hint_deposit" ) ) );
	}
}