
UCLASS(Abstract)
class AAS_SupplyPack : AAS_PlayerPickUp
{
	void SharedBeginPlay() override
	{
		Super::SharedBeginPlay();

		const FLootDataStruct& lootData = GetLootDataByIndex( GameplayTags::Loot_Ammo_SupplyPack );
		FPingableObjectInfo info	   = GetPingableObjectInfoFromLootData( EPlayerPingType::LOOT, lootData );
		pingableComponent.pingableInfo = info;
	}

	protected bool SV_CanPlayerPickup( AAS_PlayerEntity player ) override
	{
		if( !IsAlive( player ) )
			return false;

		return Server_CanResupplyPlayer( player );
	}

	UFUNCTION(BlueprintOverride)
	protected void SV_GivePlayerPickup( )
	{
		Super::SV_GivePlayerPickup();

		if( Server_TryToResupplyPlayer( pickupPlayer ) )
		{
			Server_EmitSoundAtLocation( Audio().supplyPackPickupSound, GetActorLocation() );
			Destroy();
		}
	}
}