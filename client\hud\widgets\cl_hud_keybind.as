UCLASS( Abstract )
class UAS_KeybindWidget : UUserWidgetDefault
{
	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UTextBlock glyph;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidgetOptional ) )
	private UAS_CommonTextBlock action;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidgetOptional ) )
	private UAS_CommonButton actionButton;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidgetOptional ) )
	private UImage holdIndicator;

	UPROPERTY( NotVisible, BlueprintHidden, Transient, Meta = ( BindWidgetAnimOptional ) )
	private UWidgetAnimation focusedAnimation;

	UPROPERTY( EditInstanceOnly )
	private TSubclassOf<UCommonTextStyle> textStyleOverride = nullptr;

	UPROPERTY( EditInstanceOnly, BlueprintHidden )
	FText actionText;

	UPROPERTY( EditInstanceOnly, BlueprintHidden )
	FName inputAction;

	UPROPERTY( EditInstanceOnly, BlueprintHidden )
	FName inputActionKBM;

	UPROPERTY( EditInstanceOnly, BlueprintHidden, Meta = ( DisplayName = "Show Hold Prompt" ) )
	bool holdButton = false;

	UPROPERTY( EditInstanceOnly, BlueprintHidden, Meta = ( DisplayName = "Show Double Press Prompt" ) )
	bool dblTapButton = false;

	UPROPERTY( EditInstanceOnly, BlueprintHidden )
	bool changeVisibilityBasedOnInputDevice = false;

	UPROPERTY( EditInstanceOnly, BlueprintHidden, Meta = ( EditCondition = "changeVisibilityBasedOnInputDevice" ) )
	private ESlateVisibility mouseAndKeyboardVisibility = ESlateVisibility::HitTestInvisible;

	UPROPERTY( EditInstanceOnly, BlueprintHidden, Meta = ( EditCondition = "changeVisibilityBasedOnInputDevice" ) )
	private ESlateVisibility controllerVisibility = ESlateVisibility::HitTestInvisible;

	UPROPERTY( EditInstanceOnly, BlueprintHidden, Meta = ( EditCondition = "changeVisibilityBasedOnInputDevice" ) )
	private ESlateVisibility mouseAndKeyboardGlyphVisibility = ESlateVisibility::HitTestInvisible;

	UPROPERTY( EditInstanceOnly, BlueprintHidden, Meta = ( EditCondition = "changeVisibilityBasedOnInputDevice" ) )
	private ESlateVisibility controllerGlyphVisibility = ESlateVisibility::HitTestInvisible;

	UPROPERTY( EditInstanceOnly, BlueprintHidden )
	private bool isClickable = false;

	private bool isGamepadInput = true;

	UFUNCTION( BlueprintOverride )
	void PreConstruct( bool IsDesignTime )
	{
		if ( IsValid( action ) )
		{
			action.SetText( actionText );
			if ( IsValid( textStyleOverride ) )
			{
				action.SetStyle( textStyleOverride );
			}
		}

		if ( IsValid( actionButton ) )
		{
			actionButton.SetIsEnabled( isClickable );
		}

		if ( IsValid( holdIndicator ) )
		{
			SetWidgetVisibilitySafe( holdIndicator, holdButton ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
		}
	}

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		// TODO @jmccarty @robin, this is a hack to ensure hud globals are ready
		WaitForHudGlobalsAndInputSystem();

		if ( IsValid( actionButton ) && isClickable )
		{
			actionButton.OnButtonBaseClicked.AddUFunction( this, n"OnActionButtonClicked" );
			actionButton.OnButtonBaseHovered.AddUFunction( this, n"OnActionButtonHovered" );
			actionButton.OnButtonBaseUnhovered.AddUFunction( this, n"OnActionButtonUnhovered" );
		}
	}

	UFUNCTION( BlueprintOverride )
	void Destruct()
	{
		if ( IsValid( actionButton ) && isClickable )
		{
			actionButton.OnButtonBaseClicked.Unbind( this, n"OnActionButtonClicked" );
			actionButton.OnButtonBaseHovered.Unbind( this, n"OnActionButtonHovered" );
			actionButton.OnButtonBaseUnhovered.Unbind( this, n"OnActionButtonUnhovered" );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void WaitForHudGlobalsAndInputSystem()
	{
		const UAS_HudGlobals hudGlobals = GetHUDGlobals();
		UAS_InputSystem inputSystem		= InputSystem();
		if ( !IsValid( hudGlobals ) || !IsValid( inputSystem ) )
		{
			System::SetTimer( this, n"WaitForHudGlobalsAndInputSystem", 0.1f, false );
			return;
		}

		APlayerController controller = GetOwningPlayer();
		if ( IsValid( controller ) )
		{
			UCommonInputSubsystem inputSubsystem = UCommonInputSubsystem::Get( controller.GetLocalPlayer() );
			if ( IsValid( inputSubsystem ) )
			{
				inputSubsystem.OnInputMethodChanged.AddUFunction( this, n"OnInputMethodChanged" );
				OnInputMethodChanged( inputSubsystem.GetCurrentInputType() );
			}
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnInputMethodChanged( ECommonInputType bNewInputType )
	{
		isGamepadInput = !( bNewInputType == ECommonInputType::MouseAndKeyboard );
		Update();

		if ( changeVisibilityBasedOnInputDevice )
		{
			// Change the visibility based on the input type
			SetWidgetVisibilitySafe( this, isGamepadInput ? controllerVisibility : mouseAndKeyboardVisibility );
			SetWidgetVisibilitySafe( glyph, isGamepadInput ? controllerGlyphVisibility : mouseAndKeyboardGlyphVisibility );
		}
	}

	FName GetInputAction() const
	{
		return inputAction;
	}

	void SetInputAction( FName newAction )
	{
		if ( inputAction != newAction )
		{
			inputAction = newAction;
			Update();
		}
	}

	FName GetInputActionKBM() const
	{
		return inputActionKBM;
	}

	void SetInputActionKBM( FName newAction )
	{
		if ( inputActionKBM != newAction )
		{
			inputActionKBM = newAction;
			Update();
		}
	}

	void SetHoldButton( bool newHoldButton )
	{
		if ( holdButton != newHoldButton )
		{
			holdButton = newHoldButton;

			// TODO @jmccarty: Remove this when glyphs are in for hold variants
			if ( IsValid( holdIndicator ) )
			{
				SetWidgetVisibilitySafe( holdIndicator, holdButton ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
			}

			Update();
		}
	}

	void SetDblTapButton( bool newDblTap )
	{
		if ( dblTapButton != newDblTap )
		{
			dblTapButton = newDblTap;
			Update();
		}
	}

	void SetShowGlyph( bool show )
	{
		SetWidgetVisibilitySafe( glyph, show ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed );
	}

	void SetActionText( FText newActionText )
	{
		if ( IsValid( action ) && !actionText.IdenticalTo( newActionText ) )
		{
			actionText = newActionText;
			action.SetText( actionText );
		}
	}

	void UpdateInputActions( FName newInputAction, FName newInputActionKBM )
	{
		SetInputAction( newInputAction );
		SetInputActionKBM( newInputActionKBM );
	}

	void SetIsClickable( bool newIsClickable )
	{
		if ( isClickable != newIsClickable && IsValid( actionButton ) )
		{
			isClickable = newIsClickable;
			actionButton.SetIsEnabled( isClickable );

			if ( isClickable )
			{
				actionButton.OnButtonBaseClicked.AddUFunction( this, n"OnActionButtonClicked" );
				actionButton.OnButtonBaseHovered.AddUFunction( this, n"OnActionButtonHovered" );
				actionButton.OnButtonBaseUnhovered.AddUFunction( this, n"OnActionButtonUnhovered" );
			}
			else
			{
				actionButton.OnButtonBaseClicked.Unbind( this, n"OnActionButtonClicked" );
				actionButton.OnButtonBaseHovered.Unbind( this, n"OnActionButtonHovered" );
				actionButton.OnButtonBaseUnhovered.Unbind( this, n"OnActionButtonUnhovered" );
			}
		}
	}

	void SetChangeVisibilityBasedOnInputDevice(
		bool shouldChangeVisibility,
		ESlateVisibility newMouseAndKeyboardVisibility,
		ESlateVisibility newControllerVisibility,
		ESlateVisibility newMouseAndKeyboardGlyphVisibility,
		ESlateVisibility newControllerGlyphVisibility )
	{
		if ( changeVisibilityBasedOnInputDevice != shouldChangeVisibility )
		{
			changeVisibilityBasedOnInputDevice = shouldChangeVisibility;
			mouseAndKeyboardVisibility		   = newMouseAndKeyboardVisibility;
			controllerVisibility			   = newControllerVisibility;
			mouseAndKeyboardGlyphVisibility	   = newMouseAndKeyboardGlyphVisibility;
			controllerGlyphVisibility		   = newControllerGlyphVisibility;

			APlayerController controller = GetOwningPlayer();
			if ( IsValid( controller ) )
			{
				UCommonInputSubsystem inputSubsystem = UCommonInputSubsystem::Get( controller.GetLocalPlayer() );
				if ( IsValid( inputSubsystem ) )
				{
					OnInputMethodChanged( inputSubsystem.GetCurrentInputType() );
				}
			}

			Update();
		}
	}

	void CopySettingsFromFooterDataItem( const FFooterDataItem& footerDataItem )
	{
		SetActionText( footerDataItem.actionText );
		UpdateInputActions( footerDataItem.inputAction, footerDataItem.inputActionKBM );
		SetIsClickable( true );
		SetChangeVisibilityBasedOnInputDevice(
			footerDataItem.changeVisibilityBasedOnInputDevice,
			footerDataItem.mouseAndKeyboardVisibility,
			footerDataItem.controllerVisibility,
			footerDataItem.mouseAndKeyboardGlyphVisibility,
			footerDataItem.controllerGlyphVisibility );
	}

	private void Update()
	{
		UAS_GlyphSystem glyphSystem = GetGlyphSystem();
		if ( IsValid( glyphSystem ) )
		{
			ECommonInputType inputType = isGamepadInput ? ECommonInputType::Gamepad : ECommonInputType::MouseAndKeyboard;
			FText keyBind			   = glyphSystem.GetKeybindGlyph( inputType, inputAction, inputActionKBM, holdButton, dblTapButton );

			if ( !Text::TextIsEmpty( keyBind ) )
			{
				// Loc note, this is a fine use case of FromString since glyphs aren't localized
				glyph.SetText( keyBind );
				SetWidgetVisibilitySafe( glyph, ESlateVisibility::HitTestInvisible );
			}
			else
			{
				Log( f"Can't find keybind for {inputAction.ToString()} or {inputActionKBM.ToString()}" );
			}
		}
		else
		{
			Log( f"Attempted to create a widget that needs the Input System but it hasn't been created yet." );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnActionButtonClicked( UCommonButtonBase button )
	{
		if ( isClickable )
		{
			AAS_BaseHUD baseHud = GetLocalBaseHUD();
			if ( IsValid( baseHud ) )
			{
				baseHud.SimulateInputPress( inputAction );
			}
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnActionButtonHovered( UCommonButtonBase button )
	{
		if ( IsValid( focusedAnimation ) )
		{
			PlayAnimationForward( focusedAnimation );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnActionButtonUnhovered( UCommonButtonBase button )
	{
		if ( IsValid( focusedAnimation ) )
		{
			PlayAnimationReverse( focusedAnimation, 2.0f );
		}
	}
}