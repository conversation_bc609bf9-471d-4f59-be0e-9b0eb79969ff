event void FEvent_PriorityMessageOnCallback( UAS_PriorityMessageBaseWidget Message );
event void FEvent_PriorityMessageOnCountdownStop( UAS_PriorityMessageBaseWidget Message, EPriorityMessageCountdownStopReason Reason );

UCLASS()
class UAS_PriorityMessageData : UObject
{
	const float PRIORITY_MESSAGE_DISAPPEAR_DELAY_DEFAULT = 4;

	TSubclassOf<UAS_PriorityMessageBaseWidget> widgetClass;

	// not all priority messages support an icon
	UTexture2D iconImage;
	EPriorityMessageTheme theme			= EPriorityMessageTheme::OTHER;
	EPriorityMessagePlacement placement = EPriorityMessagePlacement::CENTER;
	// decides it's order on the edges
	EPriorityMessageLevel priorityLevel = EPriorityMessageLevel::OTHER_LEFT;
	// Used for stopping follow-up messages that are out of order due to timing.
	int priorityStackValue = 0;

	FText header;
	FText subheader;

	// call to action ( CTA ) text
	FText cta;

	// show the call to action ( CTA ) text
	bool showCTA = false;

	// set to true to disappear after a delay, set to false to stay on screen until bumped by another message of equal priority
	bool autoDissappear = false;

	// the delay before auto disappearing
	float autoDissappearDelay = PRIORITY_MESSAGE_DISAPPEAR_DELAY_DEFAULT;

	// only used for center placement to clear the edge of any messages of the same priority
	bool clearEdgeOnAppear = false;

	// only used for center placement to clear the edge of any messages of the same priority
	bool clearEdgeOnDisappear = false;

	// only instantly updates the edge if the followup is an edge, and there is an active message of the same priority to replace
	bool instantEdgeReplace = false;

	// different than InstantEdgeFollowupOfSamePriority -> doesn't do any checks, just creates the followup
	bool instantFollowup = false;

	// for internal use only
	EPriorityMessageTemplate messageTemplate = EPriorityMessageTemplate::CUSTOM;

	// you can have this message create another message on disappear
	EPriorityMessageTemplate followUpTemplate = EPriorityMessageTemplate::_count;

	// you can create a custom followup message instead of using a template
	UAS_PriorityMessageData followUpData = nullptr;

	UNCAudioAsset onAppearSound = nullptr;

	// TODO: DEROSE -> get rid of this and use a custom OnCreate to do the custom timing logic you want ( also because the start time needs to be networked )
	float32 countdownDuration		= -1.0;
	bool countdownUsesRaidScheduler = false;

	FGameplayTag announcerAlias = GameplayTags::Audio; // Initialzing it to this value since and check that it's not this to play.

	// v2 additions
	TMap<int, UAS_PriorityMessageListData> v2_ListEntries;

	int raidEventManagerIndex = -1;

	TOptional<int> endTimeMs;
	
	FEvent_PriorityMessageOnCallback onCreate;
	FEvent_PriorityMessageOnCallback onDestroy;
	FEvent_PriorityMessageOnCountdownStop onCountDownStop;
}

UAS_PriorityMessageData NewPriorityMessageData()
{
	ScriptAssert( IsClient(), "NewPriorityMessageData must be called on client. UAS_PriorityMessageData is a data struct for client messages, there's no reason to create one on the server." );

	UAS_PriorityMessageData newData = Cast<UAS_PriorityMessageData>( NewObject( GetLocalHUD(), UAS_PriorityMessageData::StaticClass() ) );
	if ( IsValid( newData ) )
	{
		newData.widgetClass = PriorityMessage().priorityWidgetRaidReportClass;
	}

	return newData;
}