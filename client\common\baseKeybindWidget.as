UCLASS( Abstract )
class UAS_BaseKeybind : UNC_DisplayWidget
{
	UPROPERTY( EditInstanceOnly, BlueprintHidden )
	private FName inputAction;

	UPROPERTY( EditInstanceOnly, BlueprintHidden )
	private FName inputActionKBM;

	private bool isGamepadInput = false;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		APlayerController controller = GetOwningPlayer();
		if ( IsValid( controller ) )
		{
			UCommonInputSubsystem inputSubsystem = UCommonInputSubsystem::Get( controller.GetLocalPlayer() );
			if ( IsValid( inputSubsystem ) )
			{
				inputSubsystem.OnInputMethodChanged.AddUFunction( this, n"OnInputMethodChanged" );
				isGamepadInput = inputSubsystem.GetCurrentInputType() != ECommonInputType::MouseAndKeyboard;
			}
		}

		// TODO @jmccarty @robin, this is a hack to ensure hud globals are ready
		WaitForHudGlobalsAndInputSystem();
	}

	UFUNCTION( NotBlueprintCallable )
	void WaitForHudGlobalsAndInputSystem()
	{
		const UAS_HudGlobals hudGlobals = GetHUDGlobals();
		UAS_InputSystem inputSystem		= InputSystem();
		if ( !IsValid( hudGlobals ) || !IsValid( inputSystem ) )
		{
			System::SetTimer( this, n"WaitForHudGlobalsAndInputSystem", 0.1f, false );
			return;
		}

		Update();
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnInputMethodChanged( ECommonInputType bNewInputType )
	{
		isGamepadInput = !( bNewInputType == ECommonInputType::MouseAndKeyboard );
		Update();
	}

	void SetInputAction( FName newAction )
	{
		if ( inputAction != newAction )
		{
			inputAction = newAction;
			Update();
		}
	}

	const FName GetInputAction() const
	{
		return inputAction;
	}

	void SetInputActionKBM( FName newAction )
	{
		if ( inputActionKBM != newAction )
		{
			inputActionKBM = newAction;
			Update();
		}
	}

	const FName GetInputActionKBM() const
	{
		return inputAction;
	}

	protected void Update()
	{
		ECommonInputType inputType = isGamepadInput ? ECommonInputType::Gamepad : ECommonInputType::MouseAndKeyboard;
		FText glyph				   = GetKeybindGlyph( inputType, inputAction, inputActionKBM );

		if ( !Text::TextIsEmpty( glyph ) )
		{
			OnUpdate( glyph );
		}
		else
		{
			Log( f"Can't find keybind for {inputAction.ToString()} or {inputActionKBM.ToString()}" );
		}
	}

	protected void OnUpdate( FText glyph )
	{
		// Intentionally empty
	}
}