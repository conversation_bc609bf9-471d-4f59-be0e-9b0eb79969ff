UCLASS( Abstract )
class UAS_SightWardCounter : UNCUserWidget
{
	UPROPERTY(NotVisible, Transient, meta=(BindWidgetAnim))
	UWidgetAnimation anim_wardPlaceStates;

	UPROPERTY(NotVisible, Transient, meta=(BindWidgetAnim))
	UWidgetAnimation anim_replaceWard;

	UPROPERTY(NotVisible, Transient, meta=(BindWidgetAnim))
	UWidgetAnimation anim_show;

	private int curNumWards;

	private const float ANIM_TIME = 0.25;
	private const float ANIM_PLAY_RATE = 1 / ANIM_TIME;

	private int showCount = 1;

	UFUNCTION(BlueprintOverride)
	private void Construct()
	{
		HideInstant();
	}

	void SetNumWardsPlaced( int numWards )
	{
		int safeNumWards = Math::Clamp( numWards, 0, 3 );
		int startTime = curNumWards;
		EUMGSequencePlayMode playMode = EUMGSequencePlayMode::Forward;
		if ( safeNumWards < curNumWards )
		{
			playMode = EUMGSequencePlayMode::Reverse;
			startTime = 3 - curNumWards;
		}

		PlayAnimationTimeRange( anim_wardPlaceStates, startTime, safeNumWards, 1, playMode, 3 );

		curNumWards = safeNumWards;
	}

	void ReplaceWard()
	{
		PlayAnimation( anim_replaceWard, 0, 1, EUMGSequencePlayMode::Forward, 1, true );
	}

	void Show()
	{
		if ( showCount == 0  )
		{
			PlayAnimation( anim_show, 0, 1, EUMGSequencePlayMode::Forward, 5 );
		}

		showCount += 1;
	}

	void Hide()
	{
		if ( showCount == 1 )
		{
			PlayAnimation( anim_show, 0, 1, EUMGSequencePlayMode::Reverse, 5 );
		}

		showCount = Math::Max( 0, showCount - 1 );
	}

	private void HideInstant()
	{
		if ( showCount == 1 )
		{
			PlayAnimation( anim_show, 1, 1, EUMGSequencePlayMode::Reverse, 1 );
		}

		showCount = Math::Max( 0, showCount - 1 );
	}

	bool IsShown()
	{
		return showCount > 0;
	}
}