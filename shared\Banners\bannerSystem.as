UAS_BannerSystem BannerSystem()
{
	UAS_BannerSystem result = Cast<UAS_BannerSystem>( UNCGameplaySystemsSubsystem::Get_SharedSystem( GetCurrentWorld(), UAS_BannerSystem::StaticClass() ) );
	return result;
}

USTRUCT()
struct FWorldBannerList
{
	UPROPERTY()
	TArray<AAS_WorldBanner> list;
}

// TODO: As players leave / rejoin, what happens to this list?
//			Can't remove a texture, since that'll break ordering
//			Gotta re-use what's there.
USTRUCT()
struct FTextureList
{
	UPROPERTY()
	TArray<UTexture> list;
}

USTRUCT()
struct FSharedBannerMaterialDataList
{
	UPROPERTY()
	TArray<FSharedBannerMaterialData> list;
}

USTRUCT()
struct FSharedBannerMaterialData
{
	UPROPERTY()
	TMap<UMaterialInstance, UMaterialInstanceDynamic> dynamicMaterials;

	UPROPERTY()
	UTexture nameTexture;

	UPROPERTY()
	const UNCMTXRuntimeBannerAsset asset;

	UPROPERTY()
	bool mtxUpdatedCallbackSet = false;

	// Shared banners start furled until valid assets get assigned
	UPROPERTY()
	bool isFurled = true;
}

UCLASS()
class UAS_BannerSystem : UNCGameplaySystem_Shared
{
	TArray<AAS_WorldBanner> sv_bannersToInit;

	// These are material instances that banners can use. Try to keep this list to a minimum, our worst case is one of these per-player, which can get bad quick.
	// Each banner must specify the material instance that it uses.
	UPROPERTY( EditDefaultsOnly )
	TArray<UMaterialInstance> bannerMaterialInstances;

	UPROPERTY( EditDefaultsOnly )
	UTexture2D fallbackTexture;

	TArray<UAS_BaseOwnershipComponent> baseOwnershipComps;

	TArray<AAS_BaseSelectMarker> trackedBaseSelectMarkers;

	private FNCCoroutineSignal endSignal;

	UFUNCTION(BlueprintOverride)
	void BeginPlay()
	{
		if ( IsClient() )
		{
			ClientBeginPlay();
		}
	}

	//////////////////////////////////////////////////////////////////////////////////////////////////
	// 
	//	███████╗███████╗██████╗ ██╗   ██╗███████╗██████╗ 
	//	██╔════╝██╔════╝██╔══██╗██║   ██║██╔════╝██╔══██╗
	//	███████╗█████╗  ██████╔╝██║   ██║█████╗  ██████╔╝
	//	╚════██║██╔══╝  ██╔══██╗╚██╗ ██╔╝██╔══╝  ██╔══██╗
	//	███████║███████╗██║  ██║ ╚████╔╝ ███████╗██║  ██║
	//	╚══════╝╚══════╝╚═╝  ╚═╝  ╚═══╝  ╚══════╝╚═╝  ╚═╝
	//////////////////////////////////////////////////////////////////////////////////////////////////

	void RegisterWorldBanner( AAS_WorldBanner newBanner )
	{
		// Server sets team on banner in different ways according to how banner was spawned
		if ( IsServer() && IsValid( newBanner.connectedBaseMarker ) )
		{
			sv_bannersToInit.AddUnique( newBanner );
			if ( !trackedBaseSelectMarkers.Contains( newBanner.connectedBaseMarker ) )
			{
				newBanner.connectedBaseMarker.teamComponent.onTeamChanged.AddUFunction( this, n"OnTrackedBaseTeamChanged" );
				trackedBaseSelectMarkers.AddUnique( newBanner.connectedBaseMarker );
			}
			Thread( this, n"DelayedInitializeMissedBanners" );
		}
	}

	UFUNCTION()
	private void DelayedInitializeMissedBanners( UNCCoroutine co )
	{
		endSignal.Emit();
		co.EndOn( this, endSignal );

		// TODO @Davis - Better way to handle this, is there an event or something?
		// Want a "bases have spawned" event.
		co.Wait( 1.0 );

		if ( sv_bannersToInit.IsEmpty() )
		{
			return;
		}

		for( AAS_BaseSelectMarker marker : trackedBaseSelectMarkers )
		{
			if ( !IsValid( marker ) )
			{
				continue;
			}

			OnTrackedBaseTeamChanged( -1, marker.GetTeam() );
		}
	}
	
	UFUNCTION( NotBlueprintCallable )
	private void OnTrackedBaseTeamChanged( int oldTeam, int newTeam )
	{
		if ( newTeam < 0 )
		{
			return;
		}

		// Update banners
		int numBannersToInit = sv_bannersToInit.Num();
		for( int i = numBannersToInit - 1; i >= 0; i-- )
		{
			AAS_WorldBanner newBanner = sv_bannersToInit[ i ];
			if ( !IsValid( newBanner ) )
			{
				continue;
			}

			// Placed in-world, connected to a base
			if ( !IsValid( newBanner.connectedBaseMarker ) )
			{
				continue;
			}

			if ( newBanner.connectedBaseMarker.GetTeam() != newTeam )
			{
				continue;
			}

			RegisterBannerConnectedToBase( newBanner, newBanner.connectedBaseMarker );
			sv_bannersToInit.RemoveAt( i );
		}
	}

	private void RegisterBannerConnectedToBase( AAS_WorldBanner newBanner, AAS_BaseSelectMarker baseSelectMarker )
	{
		ScriptAssert( newBanner.connectedBaseMarker.GetTeam() >= 0, f"Tried to register a banner to an invalid team! Team must be valid before registering." );
		int team = newBanner.connectedBaseMarker.GetTeam();
		InitializeBanner( newBanner, team );
	}

	private void InitializeBanner( AAS_WorldBanner banner, int team )
	{
		banner.SetTeam( team );
	}


	//////////////////////////////////////////////////////////////////////////////////////////////////
	// 
	//	 ██████╗██╗     ██╗███████╗███╗   ██╗████████╗
	//	██╔════╝██║     ██║██╔════╝████╗  ██║╚══██╔══╝
	//	██║     ██║     ██║█████╗  ██╔██╗ ██║   ██║   
	//	██║     ██║     ██║██╔══╝  ██║╚██╗██║   ██║   
	//	╚██████╗███████╗██║███████╗██║ ╚████║   ██║   
	//	 ╚═════╝╚══════╝╚═╝╚══════╝╚═╝  ╚═══╝   ╚═╝   
	//////////////////////////////////////////////////////////////////////////////////////////////////

	UPROPERTY( NotVisible )
	TMap< int, FSharedBannerMaterialDataList > teamToBannerData;

	UPROPERTY( NotVisible )
	private TMap< UMaterialInstanceDynamic, UAS_BannerFurlThread > dynMatToFurlThread;

	// Dynamically created banners won't have shared materials so that they can do their own unfurl
	private void ClientBeginPlay()
	{
		InitializeSharedBannerData();
		ClientCallbacks().OnPlayerCreated.AddUFunction( this, n"OnPlayerCreated" );
		ClientCallbacks().OnPlayerChangedTeam.AddUFunction( this, n"OnPlayerChangedTeam" );
		ClientCallbacks().OnPlayerDestroyed.AddUFunction( this, n"OnPlayerDestroyed" );
	}

	private void InitializeSharedBannerData()
	{
		TArray<int> allTeams = GetAllTeams();
		int numTeams = allTeams.Num();
		int maxTeamSize = GetMaxTeamSize();
		for( int i = 0; i < numTeams; i++ )
		{
			FSharedBannerMaterialDataList newInstanceList;
			TArray<ANCPlayerCharacter> playersOfTeam = UNCUtils::GetPlayersOfTeam( this, allTeams[ i ] );
			int numPlayersOnTeam = playersOfTeam.Num();

			for( int j = 0; j < maxTeamSize; j++ )
			{
				ANCPlayerCharacter player;
				if ( j < numPlayersOnTeam && IsValid( playersOfTeam[ j ] ) )
				{
					player = playersOfTeam[ j ];
				}

				FSharedBannerMaterialData newData;
				CreateBannerMaterialInstanceData( newData );
				UTexture nameTexture;
				if ( IsValid( player ) )
				{
					nameTexture = UNCBannerTextRenderSubsystem::AllocateNewBannerNameTexture(this, player.GetPlayerName() );
					newData.nameTexture = nameTexture;

					newData.asset = player.MTXCustomizationComponent.GetBannerForPlayer();
					player.MTXCustomizationComponent.OnLoadedMTXAssetsUpdated.AddUFunction( this, n"OnPlayerLoadedMTXAssetsUpdated" );

					UpdateSharedBanner( newData, player );
				}

				newInstanceList.list.Add( newData );
			}

			teamToBannerData.Add( allTeams[ i ], newInstanceList );
		}
	}

	UFUNCTION()
	void OnPlayerChangedTeam( const ANCPlayerCharacter player, int oldTeam, int newTeam )
	{
		OnPlayerCreated( player );
	}

	UFUNCTION()
	void OnPlayerCreated( const ANCPlayerCharacter player )
	{
		if ( !IsValid( player ) )
		{
			return;
		}

		const int team = player.GetTeam();
		TArray<ANCPlayerCharacter> playersOfTeam = GetPlayersOfTeam( team );
		int playerIndex = playersOfTeam.FindIndex( player );
		if ( playerIndex < 0 )
		{
			return;
		}

		// Player on a team that wasn't accounted for, or has an index that's outside of team limits
		if ( !DoesSharedBannerDataExist( team, playerIndex ) )
		{
			return;
		}
		
		FSharedBannerMaterialData& bannerData = GetSharedBannerData( team, playerIndex );

		UpdateSharedBanner( bannerData, player );
	}

	UFUNCTION()
	void OnPlayerLoadedMTXAssetsUpdated( ANCPlayerCharacter player )
	{
		if ( !IsValid( player ) )
		{
			return;
		}

		const int team = player.GetTeam();
		TArray<ANCPlayerCharacter> playersOfTeam = GetPlayersOfTeam( team );
		int playerIndex = playersOfTeam.FindIndex( player );
		if ( playerIndex < 0 )
		{
			return;
		}

		if ( !DoesSharedBannerDataExist( team, playerIndex ) )
		{
			return;
		}

		FSharedBannerMaterialData& bannerData = GetSharedBannerData( team, playerIndex );
		
		UpdateSharedBanner( bannerData, player );
	}

	UFUNCTION()
	void OnPlayerDestroyed( const ANCPlayerCharacter player, EEndPlayReason reason )
	{
		if ( !IsValid( player ) )
		{
			return;
		}

		int team = player.GetTeam();
		TArray<ANCPlayerCharacter> playersOfTeam = GetPlayersOfTeam( team );
		int playerIndex = -1;
		int numPlayersOnTeam = playersOfTeam.Num();
		for( int i = 0; i < numPlayersOnTeam; i++ )
		{
			if ( playersOfTeam[ i ] == player )
			{
				playerIndex = i;
				break;
			}
		}

		if ( !DoesSharedBannerDataExist( team, playerIndex ) )
		{
			return;
		}

		FSharedBannerMaterialData& bannerData = GetSharedBannerData( team, playerIndex );

		UpdateSharedBanner( bannerData, player );
	}

	void CreateBannerMaterialInstanceData( FSharedBannerMaterialData&out data )
	{
		for( UMaterialInstance matInstance : bannerMaterialInstances )
		{
			data.dynamicMaterials.Add( matInstance, nullptr );
		}
	}

	// Can be called to re-initialize if MTX changes
	void UpdateSharedBanner( FSharedBannerMaterialData& bannerData, const ANCPlayerCharacter player )
	{
		bool shouldFurl = false;

		if ( IsValid( player ) )
		{
			if ( !IsValid( bannerData.nameTexture ) )
			{
				bannerData.nameTexture = UNCBannerTextRenderSubsystem::AllocateNewBannerNameTexture(this, player.GetPlayerName() );
			}

			if ( !bannerData.mtxUpdatedCallbackSet )
			{
				player.MTXCustomizationComponent.OnLoadedMTXAssetsUpdated.AddUFunction( this, n"OnPlayerLoadedMTXAssetsUpdated" );
				bannerData.mtxUpdatedCallbackSet = true;
			}

			// FYI: Can still be invalid after setting it
			bannerData.asset = player.MTXCustomizationComponent.GetBannerForPlayer();
		}
		else
		{
			shouldFurl = true;
		}

		UTexture2D textureToUse = fallbackTexture;
		if ( IsValid( bannerData.asset ) )
		{
			textureToUse = bannerData.asset.BaseTexture;
		}
		else
		{
			shouldFurl = true;
		}

		UTexture nameTexture = bannerData.nameTexture;
		for( TMapIterator< UMaterialInstance, UMaterialInstanceDynamic > entry : bannerData.dynamicMaterials )
		{
			UpdateBannerMaterial( entry.Value, nameTexture, textureToUse );
		}

		if ( shouldFurl )
		{
			FurlAllBannerMaterials( bannerData );
		}
		else
		{
			UnFurlAllBannerMaterials( bannerData );
		}
	}

	void UpdateBannerMaterial( UMaterialInstanceDynamic bannerMaterialDynamic, UTexture nameTexture, UTexture2D bannerTexture )
	{
		if ( IsValid( bannerMaterialDynamic ) )
		{
			bannerMaterialDynamic.SetTextureParameterValue( n"ColorTexture", bannerTexture );

			if ( IsValid( nameTexture ) )
			{
				bannerMaterialDynamic.SetTextureParameterValue( n"BannerText", nameTexture );
			}
		}
	}

	void FurlAllBannerMaterials( FSharedBannerMaterialData& data )
	{
		for( TMapIterator<UMaterialInstance, UMaterialInstanceDynamic> entry : data.dynamicMaterials )
		{
			if ( IsValid( entry.Value ) )
			{
				SetBannerMaterialFurlTarget( entry.Value, 0 );
			}
		}

		data.isFurled = true;
	}

	void UnFurlAllBannerMaterials( FSharedBannerMaterialData& data )
	{
		for( TMapIterator<UMaterialInstance, UMaterialInstanceDynamic> entry : data.dynamicMaterials )
		{
			if ( IsValid( entry.Value ) )
			{
				SetBannerMaterialFurlTarget( entry.Value, 1 );
			}
		}

		data.isFurled = false;
	}

	void SetBannerMaterialFurlTarget( UMaterialInstanceDynamic dynMat, float target )
	{
		if ( !IsValid( dynMat ) )
		{
			return;
		}

		CancelBannerMaterialFurl( dynMat );

		ANCPlayerCharacter localPlayer = Client_GetLocalPawn();
		if ( !IsValid( localPlayer ) )
		{
			return;
		}
		
		UAS_BannerFurlThread thread	  = Cast<UAS_BannerFurlThread>( CreateThread( UAS_BannerFurlThread::StaticClass(), localPlayer ) );
		thread.Init( dynMat, target );
		thread.Start();
		dynMatToFurlThread.Add( dynMat, thread );
	}

	private void CancelBannerMaterialFurl( UMaterialInstanceDynamic dynMat )
	{
		if ( dynMatToFurlThread.Contains( dynMat ) )
		{
			dynMatToFurlThread[ dynMat ].Cancel();
		}
	}

	void RemoveBannerMaterialFurlThread( UMaterialInstanceDynamic dynMat )
	{
		if ( dynMatToFurlThread.Contains( dynMat ) )
		{
			dynMatToFurlThread.Remove( dynMat );
		}
	}

	// World banners call this. It will create a dynamic material for the requested material instance if that doesn't already exist.
	bool ClientGetOrCreateSharedDynamicMaterialForBanner( int team, int playerIndex, UMaterialInstance bannerMaterialInstance, UMaterialInstanceDynamic&out outDynMat )
	{
		if ( !DoesSharedBannerDataExist( team, playerIndex ) )
		{
			return false;
		}

		FSharedBannerMaterialData& data = GetSharedBannerData( team, playerIndex );
		if ( !data.dynamicMaterials.Contains( bannerMaterialInstance ) )
		{
			Print(f"Tried to get or create banner dynamic material, but the material instance wasn't registered! {bannerMaterialInstance}", 15.f, FLinearColor::Red );
			return false;
		}

		// When we add a new material, we need to make sure it's up to date. Includes data and furling.
		if ( !IsValid( data.dynamicMaterials[ bannerMaterialInstance ] ) )
		{
			UMaterialInstanceDynamic newDynMat;
			CreateDynamicMaterialForBanner( bannerMaterialInstance, newDynMat );
			data.dynamicMaterials[ bannerMaterialInstance ] = newDynMat;
			
			UTexture2D textureToUse = IsValid( data.asset ) ? data.asset.BaseTexture : nullptr;
			UpdateBannerMaterial( newDynMat, data.nameTexture, textureToUse );

			// Banners start furled, only check if needs to unfurl
			if ( !data.isFurled )
			{
				SetBannerMaterialFurlTarget( newDynMat, 1 );
			}
		}

		outDynMat = data.dynamicMaterials[ bannerMaterialInstance ];
		return true;
	}

	// Provides a one-off dynamic material. The material instance must be in bannerMaterialInstances above.
	void CreateDynamicMaterialForBanner( UMaterialInstance bannerMaterialInstance, UMaterialInstanceDynamic&out outDynMat )
	{
		if ( !bannerMaterialInstances.Contains( bannerMaterialInstance ) )
		{
			return;
		}

		outDynMat = Material::CreateDynamicMaterialInstance( bannerMaterialInstance );
		// All banners start furled
		outDynMat.SetScalarParameterValue( n"FurlFraction", 0 );
	}

	bool ClientGetSharedNameTexture( int team, int playerIndex, UTexture&out nameTexture )
	{
		if ( !DoesSharedBannerDataExist( team, playerIndex ) )
		{
			return false;
		}
		
		const FSharedBannerMaterialData& data = GetSharedBannerData( team, playerIndex );

		nameTexture = data.nameTexture;
		return true;
	}

	FSharedBannerMaterialData& GetSharedBannerData( int team, int playerIndex )
	{
		return teamToBannerData[ team ].list[ playerIndex ];
	}

	bool DoesSharedBannerDataExist( int team, int playerIndex )
	{
		if ( team < 0 || playerIndex < 0 )
		{
			return false;
		}

		if ( !teamToBannerData.Contains( team ) )
		{
			return false;
		}

		int numEntriesForTeam = teamToBannerData[ team ].list.Num();
		if ( numEntriesForTeam <= playerIndex )
		{
			return false;
		}

		return true;
	}
}

USTRUCT()
struct FDynamicMaterialInstanceList
{
	UPROPERTY()
	TArray<UMaterialInstanceDynamic> list;
}


UCLASS()
class UAS_BannerFurlThread : UAS_Thread
{
	private float targetFrac;
	UPROPERTY()
	private UMaterialInstanceDynamic dynMat;

	void Init( UMaterialInstanceDynamic& inDynMat, float inTargetFrac )
	{
		targetFrac = inTargetFrac;
		dynMat = inDynMat;
	}

	void OnThreadStart(UNCCoroutine co) override
	{
		Super::OnThreadStart(co);
		float curFrac = dynMat.GetScalarParameterValue( n"FurlFraction" );
		float dt = 0.016;
		while ( curFrac != targetFrac )
		{
			float newFrac = Math::FInterpConstantTo( curFrac, targetFrac, dt, 1 );
			dynMat.SetScalarParameterValue( n"FurlFraction", newFrac );

			curFrac = newFrac;

			//Print(f"        Setting banner furl frac: {curFrac}");
			co.Wait( dt );
		}
	}

	void OnThreadEnd(FNCCoroutineEndParams params) override
	{
		Super::OnThreadEnd(params);
		BannerSystem().RemoveBannerMaterialFurlThread( dynMat );
	}
}