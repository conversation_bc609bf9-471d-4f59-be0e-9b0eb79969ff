UCLASS()
class UAnimNotifyState_SetMaterialParameters : UAnimNotifyState
{
	UPROPERTY()
	FName parameterName;
	
	UPROPERTY()
	TArray<int> materialIndexes;

	UPROPERTY()
	float32 startValue;
	
	UPROPERTY()
	float32 endValue;

	UFUNCTION(BlueprintOverride)
	bool NotifyBegin(USkeletalMeshComponent MeshComp, UAnimSequenceBase Animation, float TotalDuration,
					 FAnimNotifyEventReference EventReference) const
	{
		for ( int materialIndex : materialIndexes )
		{
			UMaterialInstanceDynamic material = MeshComp.CreateDynamicMaterialInstance( materialIndex );
			if (IsValid(material))
				material.SetScalarParameterValue( parameterName, startValue );
		}

		return true;
	}

	UFUNCTION(BlueprintOverride)
	bool NotifyEnd(USkeletalMeshComponent MeshComp, UAnimSequenceBase Animation,
				   FAnimNotifyEventReference EventReference) const
	{
		for ( int materialIndex : materialIndexes )
		{
			UMaterialInstanceDynamic material = MeshComp.CreateDynamicMaterialInstance( materialIndex );
			if (IsValid(material))
				material.SetScalarParameterValue( parameterName, endValue );
		}

		return true;
	}
}