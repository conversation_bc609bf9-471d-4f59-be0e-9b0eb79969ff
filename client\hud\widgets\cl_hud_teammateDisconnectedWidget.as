
UCLASS( Abstract )
class UAS_TeammateDisconnectedWidget : UNC_DisplayWidget
{
	default StateOnConstruction = EDisplayConstructVisibility::Hidden;
	default HideVisibility		= ESlateVisibility::Collapsed;

	UPROPERTY( Meta = (BindWidget) )
	UAS_CommonTextBlock penaltyTimerText;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden )
	TSubclassOf<UAS_CommonTextStyle> timerActiveStyle;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden )
	TSubclassOf<UAS_CommonTextStyle> timerFinishedStyle;

	float32 remainingTime = -1;
	FTimerHandle countdownTimer;

	void ShowReconnectWarning( float32 timeRemaining )
	{
		if( !System::IsValidTimerHandle( countdownTimer ) )
		{
			remainingTime = timeRemaining;
			SetTimerText();
			countdownTimer = System::SetTimer( this, n"CountdownTick", 1.0f, false );
		}
		else
		{
			// Update to lowest time left before penalty is removed
			if( timeRemaining < remainingTime )
				remainingTime = timeRemaining;

			SetTimerText();
		}

		Show();
	}

	UFUNCTION()
	void CountdownTick()
	{
		remainingTime -= 1.0f;
		SetTimerText();

		if( remainingTime > 0.0f )
			countdownTimer = System::SetTimer( this, n"CountdownTick", 1.0f, false );
		else
			System::ClearAndInvalidateTimerHandle( countdownTimer );
	}

	UFUNCTION()
	void SetTimerText()
	{
		FNumberFormattingOptions options;
		options.SetMinMaxFractionalDigits( 0 );
		options.SetMinMaxIntegralDigits( 2 );
		options.SetRoundingMode( ERoundingMode::FromZero );

		FTimespan timespan = FTimespan( 0, 0, int( Math::Max( remainingTime, 0.0f ) ) );
		FText minutes = FText::AsNumber( timespan.Minutes, options );
		FText seconds = FText::AsNumber( timespan.Seconds, options );

		if( remainingTime <= 0.0f )
			penaltyTimerText.SetStyle( timerFinishedStyle );
		else
			penaltyTimerText.SetStyle( timerActiveStyle );

		penaltyTimerText.SetText( GetLocalizedText( Localization::Utilities, f"minutes_seconds_timer", FFormatArgumentValue( minutes ), FFormatArgumentValue( seconds ) ) );
	}

	void HideWarning( )
	{
		System::ClearAndInvalidateTimerHandle( countdownTimer );
		Hide();
	}
}