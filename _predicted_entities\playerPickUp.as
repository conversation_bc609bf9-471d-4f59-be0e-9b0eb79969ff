
UCLASS(Abstract)
class AAS_PlayerPickUp : ANCDefaultActor
{
	UPROPERTY(DefaultComponent, RootComponent)
	USceneComponent root;

	UPROPERTY(DefaultComponent)
	UStaticMeshComponent pickupMesh;
	default pickupMesh.bCanEverAffectNavigation = false;

	UPROPERTY(DefaultComponent)
	UAS_SphereTrigger pickupTrigger;
	default pickupTrigger.SetRadius( 400 );

	UPROPERTY(DefaultComponent)
	UAS_SphereTrigger highlightTrigger;
	default highlightTrigger.SetRadius( 800 );

	UPROPERTY( DefaultComponent )
	UNCLootMovementComponent LootMovementComponent;
	default LootMovementComponent.ComponentTickEnabled = true;

	UPROPERTY( DefaultComponent )
	UNCNetMovementComponent movementComponent;

	UPROPERTY( DefaultComponent )
	UAS_PingableComponent pingableComponent;
	default pingableComponent.pingType = EPlayerPingType::LOOT;

	UPROPERTY( DefaultComponent )
	UAS_SimpleClientHighlightComponent cl_highlightComponent;
	default cl_highlightComponent.highlightValue = EHighlightStencilValue::HIGHLIGHT_LOOT_STENCIL_VALUE;

	UPROPERTY( EditDefaultsOnly )
	UNiagaraSystem beamFX;
	UNiagaraComponent beam;

	UPROPERTY()
	FNCNetBool net_hasPickupStarted;

	AAS_PlayerEntity pickupPlayer = nullptr;

	UPROPERTY()
	FNCNetBool net_hasGravity( true );

	const float ENABLE_TRIGGER_DELAY = 0.5f;

	UFUNCTION( BlueprintOverride )
	void ServerBeginPlay()
	{
		SharedBeginPlay();

		pickupTrigger.Disable();
		pickupTrigger.onPlayerEntered.AddUFunction( this, n"OnPlayerEntered" );

		highlightTrigger.Disable();
		

		System::SetTimer( this, n"EnableTrigger", ENABLE_TRIGGER_DELAY, false );
	}

	UFUNCTION( BlueprintOverride )
	void ClientBeginPlay()
	{
		SharedBeginPlay();

		highlightTrigger.onPlayerEntered.AddUFunction( this, n"OnPlayerEnteredHighlight" );
		highlightTrigger.onPlayerExited.AddUFunction( this, n"OnPlayerExitedHighlight" );

		if ( !IsValid( beam ) && IsValid( beamFX ) )
		{
			beam = Client_SpawnEffectOnEntity_OneShot( beamFX, this );
			if ( IsValid( beam ) )
			{
				beam.SetNiagaraVariableVec2( "Sprite Size", FVector2D( 10, 100 ) );
				beam.SetNiagaraVariableLinearColor( "Color", GetRarityColor(GameplayTags::Loot_Rarity_Common) );
			}			
			
			net_hasPickupStarted.OnReplicated().AddUFunction( this, n"CL_OnHasPickupStarted" );
			net_hasGravity.OnReplicated().AddUFunction( this, n"CL_OnHasGravityChanged" );
		}
	}

	void SharedBeginPlay()
	{				
		pickupMesh.CollisionEnabled = ECollisionEnabled::PhysicsOnly;
		System::SetTimer( this, n"RestoreCollision", 0.75, false );

		LootMovementComponent.SetUpdatedComponent( GetRootComponent() );

		pickupMesh.SetCollisionResponseToChannel( ECollisionChannel::WeaponMelee, ECollisionResponse::ECR_Ignore );
	}

	UFUNCTION( NotBlueprintCallable )
	private void RestoreCollision()
	{
		pickupMesh.CollisionEnabled = ECollisionEnabled::QueryAndPhysics;
	}

	UFUNCTION( NotBlueprintCallable )
	private void EnableTrigger()
	{
		pickupTrigger.Enable();
		highlightTrigger.Enable();

		if( pickupTrigger.playersInside.Num() > 0 )
			SV_StartMovingPickup( pickupTrigger.playersInside[0] );
	}

	UFUNCTION()
	private void OnPlayerEntered( AAS_PlayerEntity player, UAS_SphereTrigger trigger )
	{
		if( !player.IsAlive() )
			return;

		SV_StartMovingPickup( player );
	}

	UFUNCTION()
	private void OnPlayerEnteredHighlight(AAS_PlayerEntity player, UAS_SphereTrigger trigger)
	{
		cl_highlightComponent.SetIsHighlighted( true );
	}

	UFUNCTION()
	private void OnPlayerExitedHighlight(AAS_PlayerEntity player, UAS_SphereTrigger trigger)
	{
		cl_highlightComponent.SetIsHighlighted( false );
	}

	UFUNCTION(BlueprintEvent)
	protected void SV_GivePlayerPickup()
	{
		ScriptAssert( IsValid( pickupPlayer ), "AAS_PlayerPickUp::SV_GivePlayerPickup - Invalid pickupPlayer" );
	}

	UFUNCTION()
	protected void CL_OnHasPickupStarted( bool oldVal, bool newVal )
	{
		DisableFX();
	}

	protected void DisableFX()
	{
		if( IsValid( beam ) )
			beam.DestroyComponent( beam );
	}

	UFUNCTION()
	void DisableGravity()
	{
		LootMovementComponent.GravityScale = 0.0f;
		LootMovementComponent.SetComponentTickEnabled( false );
		net_hasGravity.SetNetValue( false );
	}

	UFUNCTION()
	void EnableGravity()
	{
		LootMovementComponent.GravityScale = 1.0f;		
		LootMovementComponent.SetComponentTickEnabled( true );
		net_hasGravity.SetNetValue( true );
	}

	UFUNCTION()
	protected void CL_OnHasGravityChanged( bool oldVal, bool newVal )
	{
		if( net_hasGravity )
		{
			LootMovementComponent.GravityScale = 1.0f;
			LootMovementComponent.SetComponentTickEnabled( true );
		}
		else
		{ 
			LootMovementComponent.GravityScale = 0.0f;
			LootMovementComponent.SetComponentTickEnabled( false );
		}		
	}

	UFUNCTION(BlueprintEvent)
	protected void SV_StartMovingPickup( AAS_PlayerEntity player )
	{
		if( !SV_CanPlayerPickup( player ) )
			return;

		net_hasPickupStarted.SetNetValue( true );
		pickupPlayer = player;

		Thread( this, n"SV_MovePickupTowardsPlayer", player );		
	}

	protected bool SV_CanPlayerPickup( AAS_PlayerEntity player )
	{
		return IsAlive( player );
	}

	const float PICKUP_DISTANCE_SQRD = 50.0f; // Game units where item stops moving closer and is added to players inventory
	float currrentDistance			 = MAX_flt;
	float speed;
	float acceleration = 50;

	FNCCoroutineSignal moveCompleteSignal;

	UFUNCTION()
	void SV_MovePickupTowardsPlayer( UNCCoroutine co, AAS_PlayerEntity player )
	{	
		FVector targetLocation = pickupPlayer.GetPawnViewLocation();
		currrentDistance = Distance( GetActorLocation(), targetLocation );
		speed			 = 0;

		co.EndOn( player, player.OnDeathSignal );
		co.OnCoroutineEnd.AddUFunction( this, n"OnMoveThreadEnd" );

		pickupPlayer = player;
		SetActorTickEnabled( true );

		co.Wait( this, moveCompleteSignal );
	}

	UFUNCTION( BlueprintOverride )
	void FixedTick(const FNCFixedTickContext& context)
	{
		if ( !IsValid( pickupPlayer ) )
			return;

		FVector targetLocation = pickupPlayer.GetPawnViewLocation();
		FVector moveDirection = targetLocation - GetActorLocation();
		moveDirection.Normalize();
		float moveLength = Math::Min( speed * context.DeltaSeconds, currrentDistance );
		speed += acceleration;

		SetActorLocation( GetActorLocation() + ( moveDirection * moveLength ) );
		currrentDistance = Distance( GetActorLocation(), targetLocation );
		if ( currrentDistance <= PICKUP_DISTANCE_SQRD )
		{
			SV_GivePlayerPickup();
			moveCompleteSignal.Emit();
		}
	}

	UFUNCTION()
	private void OnMoveThreadEnd( FNCCoroutineEndParams endParams )
	{
		pickupPlayer				= nullptr;
		AAS_PlayerEntity nextPlayer = FindNextPickupPlayer();

		SetActorTickEnabled( false );
		net_hasPickupStarted.SetNetValue( false );
		EnableGravity();

		if ( IsValid( nextPlayer ) )
			SV_StartMovingPickup( nextPlayer );
	}

	AAS_PlayerEntity FindNextPickupPlayer() const
	{
		for ( AAS_PlayerEntity player : pickupTrigger.playersInside )
		{
			if( IsAlive( player ) )
				return player;
		}

		return nullptr;
	}
}