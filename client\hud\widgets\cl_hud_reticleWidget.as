enum EReticleAnimationDeployState
{
	INVALID,
	DEPLOYED,
	UNDEPLOYED
}

struct FReticleAnimationData
{
	UWidgetAnimation animation;
	EReticleAnimationDeployState deployedState;
	EUMGSequencePlayMode enablePlayMode;
	EUMGSequencePlayMode disablePlayMode;
	float playbackRate;
	int numLoopsToPlayEnable;
	int numLoopsToPlayDisable;
	UNCAudioAsset animSound_enable;
	UNCAudioAsset animSound_disable;
	UNCAudioAsset animSound_enabledIdle;
	TOptional<FAudioResultData> enabledIdle_resultData;

	FReticleAnimationData( UWidgetAnimation inAnimation, float inPlaybackRate )
	{
		animation			  = inAnimation;
		enablePlayMode		  = EUMGSequencePlayMode::Forward;
		disablePlayMode		  = EUMGSequencePlayMode::Reverse;
		playbackRate		  = inPlaybackRate;
		numLoopsToPlayEnable  = 1;
		numLoopsToPlayDisable = 1;
	}

	FReticleAnimationData( UWidgetAnimation inAnimation, EUMGSequencePlayMode inDeployPlayMode, EUMGSequencePlayMode inUnDeployPlayMode, float inPlaybackRate )
	{
		animation			  = inAnimation;
		enablePlayMode		  = inDeployPlayMode;
		disablePlayMode		  = inUnDeployPlayMode;
		playbackRate		  = inPlaybackRate;
		numLoopsToPlayEnable  = 1;
		numLoopsToPlayDisable = 1;
	}

	void UpdateChargedIdleSound( bool shouldDeploy )
	{
		bool isIdleSoundPlaying = enabledIdle_resultData.IsSet();
		if ( isIdleSoundPlaying )
			Client_StopSound( enabledIdle_resultData.Value.EventID );

		if ( shouldDeploy && IsValid( animSound_enabledIdle ) )
		{
			enabledIdle_resultData = Client_EmitSoundUI( animSound_enabledIdle );
		}
	}
}

namespace ScopeMaterialParams
{
	const FString AIMPOINT_COLOR_SUFFIX	  = f"Color";
	const FString AIMPOINT_DEPTH_SUFFIX	  = f"DepthStart";
	const FString AIMPOINT_CENTER_SUFFIX  = f"CenterOffset";
	const FString AIMPOINT_TEXTURE_SUFFIX = f"Texture";
}

struct FScopeMaterialParameters
{
	UPROPERTY( EditDefaultsOnly )
	TArray<FScopeMaterialParameters_Aimpoint> aimpointParams;
}

struct FScopeMaterialParameters_Aimpoint
{
	UPROPERTY( EditDefaultsOnly )
	bool setColor;

	UPROPERTY( EditDefaultsOnly, Meta = ( EditCondition = "setColor", EditConditionHides ) )
	FLinearColor color;

	UPROPERTY( EditDefaultsOnly )
	bool setDepth;

	UPROPERTY( EditDefaultsOnly, Meta = ( EditCondition = "setDepth", EditConditionHides ) )
	float32 depthStart;

	UPROPERTY( EditDefaultsOnly )
	bool setCenter;

	UPROPERTY( EditDefaultsOnly, Meta = ( EditCondition = "setCenter", EditConditionHides ) )
	FVector centerOffset;

	UPROPERTY( EditDefaultsOnly )
	bool setTexture;

	UPROPERTY( EditDefaultsOnly, Meta = ( EditCondition = "setTexture", EditConditionHides ) )
	UTexture2D texture;
}

enum EScopeMaterialAnimationPlayMode
{
	SET_ENABLED_STATE,
	PLAY_ENABLED_ANIM,
	SET_DISABLED_STATE,
	PLAY_DISABLED_ANIM,
}

USTRUCT()
struct FReticleWidgetMotionData
{
	UPROPERTY()
	UWidget Widget;

	// The angle at which the widget should move. If used with a shotgun, this will use the radius from the shot index, but override the angle
	UPROPERTY()
	float angle = -1;

	// Scales reticle motion by this amount
	UPROPERTY()
	float Scalar = 1.0;

	// Sets a minimum spread deviation (hopefully saves you from having to manually position it in the BP editor)
	UPROPERTY()
	float spreadOffset = 0.0;

	// False: Widget position is considered its starting point. True: Specify a different position to use as the widget default position. (Useful if you want it to return to <0,0> but have it mocked up elsewhere in reticle)
	UPROPERTY()
	bool overrideInitialPosition = false;

	UPROPERTY( meta = ( EditCondition = "overrideInitialPosition", EditConditionHides ) )
	FVector2D initialPositionOverride = FVector2D::ZeroVector;

	// If set, this widget will use offset data from the weapons shot pattern instead of the angle property
	UPROPERTY()
	int shotPatternIndex = -1;

	// Should widget be scaled instead of positioned?
	// Either uses angle or shot pattern index, depending on if it's in spreadMotionWidgets or shotPatternWidgets.
	UPROPERTY()
	bool scaleWidget;

	// Lets you pick a specific shotgun pellet to use for a radius, while overriding the angle via angle setting above.
	// Use this if you want to place a widget at the correct widget but at a custom angle, instead of having that widget line up perfectly with where the pellet itself would go.
	// Example: I want to represent the nova spread with 5 dots, each having a radius that is the max shotgun pellet radius
	UPROPERTY()
	bool useShotPatternIndexForRadius;

	// Cached at start
	FVector2D InitialPosition;
	FVector2D InitialSize;
	float InitialAngle;
	bool useAngleParam = false;

	// Used in tick
	FVector2D curSpreadAngle;
}

enum EADSFadeOutStyle
{
	NONE,
	FADE_OUT_FOR_ADS,
	FADE_IN_FOR_ADS
}

UCLASS( Abstract )
class UAS_ReticleWidget : UNCReticleWidget
{
	UPROPERTY()
	TArray<FReticleWidgetMotionData> spreadMotionWidgets;

	UPROPERTY()
	TArray<FReticleWidgetMotionData> shotPatternWidgets;

	UPROPERTY( NotVisible, Transient, meta = ( BindWidgetAnimOptional ) )
	private UWidgetAnimation anim_optic_marksman;

	UPROPERTY( NotVisible, Transient, meta = ( BindWidgetAnimOptional ) )
	private UWidgetAnimation anim_optic_skirmish;

	// Must be at least 2 seconds long. time 0 = neutral, 1 = enemy, 2 = friendly
	UPROPERTY( NotVisible, Transient, meta = ( BindWidgetAnimOptional ) )
	private UWidgetAnimation anim_aimTargetState;

	default bIsFocusable = false;

	FVector2D cachedRenderScale = FVector2D( 1, 1 );

	UPROPERTY( NotVisible, BlueprintReadOnly )
	float spreadRadiusPixels = 0.0f;

	UPROPERTY( NotVisible, BlueprintReadOnly )
	float spreadFraction = 0.0f;

	UPROPERTY( BlueprintReadOnly )
	EADSFadeOutStyle fadeOutForADS = EADSFadeOutStyle::NONE;

	UPROPERTY( BlueprintReadOnly )
	bool materialDrivenReticle = false;

	ANCWeapon ownerWeapon;

	const float RETICLE_CANVAS_WIDTH = 1920.0;

	// Hacky -- get an initialization call with the weapon passed in
	bool hasInitialized = false;

	bool doTranslationOffset = false;

	float spreadNormalizeScalar = 0;

	/////////////////// Shotgun pattern ///////////////////
	// Only trace this far ahead of the player. Use for optimization, and to limit how small the reticle can get.
	UPROPERTY( EditDefaultsOnly, Category = "TranslationOffset" )
	float maxTranslationOffsetTraceDistance = 800.0f;

	// Consider this the minimum trace distance. Used to limit how large a translation offset reticle can get.
	UPROPERTY( EditDefaultsOnly, Category = "TranslationOffset" )
	float minTranslationOffsetDistance = 128.0f;

	UPROPERTY( BlueprintReadOnly, Category = "TranslationOffset" )
	float innerTranslationOffsetRadius = MAX_flt;

	UPROPERTY( BlueprintReadOnly, Category = "TranslationOffset" )
	float outerTranslationOffsetRadius = 0;

	/////////////////// Reticle anims ///////////////////

	bool isReticleVisible  = true;
	int reticleAnimStartMS = -1;

	float32 TICK_TIME = 0.016;
	float32 ANIM_TIME = 0.1;
	FNCCoroutineSignal AnimateReticleVisibilityEndSignal;
	private TMap<FName, FReticleAnimationData> reticleAnimations;

	/////////////////// Shared mods ///////////////////

	private TMap<FName, UAS_Thread_ScopeMaterialAnimation> keyToAnimThreadClass_Enabled;
	private TMap<FName, UAS_Thread_ScopeMaterialAnimation> keyToAnimThreadClass_Disabled;
	private TMap<FName, FScopeMaterialParameters> keyToScopeMaterialAnimParams_Enabled;
	private TMap<FName, FScopeMaterialParameters> keyToScopeMaterialAnimParams_Disabled;
	private TMap<FName, UAS_Thread_ScopeMaterialAnimation> activeScopeMaterialAnimations;

	bool lookingAtBasebreakerTarget = false;

	///////////////////////////////////////////////////
	FName VISIBILITY_PARAM 				= n"reticle_is_visible";
/*	have to use a stored value because Material::GetScalarParameterValue( GetGlobalParameters().weaponParameters, VISIBILITY_PARAM ) 
	does not return the correct value	-Mo	*/
	private float curVisibleAlphaValue 	= 1.0;	//this gets reset in constructor below
	
	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		InitializeMotionWidgetArray( spreadMotionWidgets, false );
		InitializeMotionWidgetArray( shotPatternWidgets, true );
		ScriptCallbacks().RegisterSignalCallback( Signals::WEAP_MOD_MENU_OPENED, this, n"OnSignal_WeaponModificationMenuOpened" );
		ScriptCallbacks().RegisterSignalCallback( Signals::WEAP_MOD_MENU_CLOSED, this, n"OnSignal_WeaponModificationMenuClosed" );
		ScriptCallbacks().client_onCrosshairActorChanged.AddUFunction( this, n"OnCrosshairActorChanged" );

		ScriptCallbacks().RegisterSignalCallback( Signals::WEAP_LOCAL_PLAYER_MODS_CHANGED_DELAYED, this, n"Signal_LocalModsChangedDelayed" );
		ScriptCallbacks().RegisterSignalCallback( Signals::WEAP_LOCAL_PLAYER_ADS_CHANGED, this, n"Signal_LocalADSChangedDelayed" );

		RegisterReticleAnimationIfValid( WeaponConst::Optics::MODNAME_MARKSMAN, anim_optic_marksman, EUMGSequencePlayMode::Forward, EUMGSequencePlayMode::Reverse, 100 );
		RegisterReticleAnimationIfValid( WeaponConst::Optics::MODNAME_SKIRMISH, anim_optic_skirmish, EUMGSequencePlayMode::Forward, EUMGSequencePlayMode::Reverse, 100 );

	/*	Start alpha'd out -> rest of logic naturally fades in if it should be visible. Not starting 
		alpha'd out will cause a pop on offhand axe reticle as the primary weapon reticle spawns in 
		at %100 opacity and fades out - Mo 	*/
		curVisibleAlphaValue 	= 0.0;
		SetWeaponCollectionParameter( VISIBILITY_PARAM, curVisibleAlphaValue );
		
		bool isMeleeReticle 	= this.IsA( UAS_ReticleWidgetMeleeWeapon::StaticClass() );
		if ( !materialDrivenReticle && !isMeleeReticle )
			SetRenderOpacity(0);
	}

	protected void InitializeReticle( ANCWeapon weapon )
	{
		if ( !IsValid( weapon ) )
			return;

		int numShotPatternWidgets = shotPatternWidgets.Num();
		FWeaponData weaponData	  = weapon.GetWeaponData();

		if ( shotPatternWidgets.Num() > 0 )
		{
			int numShotPatternIndeces = weaponData.ShotPattern.Num();
			for ( int i = 0; i < numShotPatternWidgets; i++ )
			{
				const FReticleWidgetMotionData data = shotPatternWidgets[i];

				// TODO: Workaround. Fail the initialize if you're trying to access shot pattern indeces that don't exist
				if ( data.shotPatternIndex >= numShotPatternIndeces )
				{
					Warning( f"Warning! Tried to access shot pattern index {data.shotPatternIndex} in reticle {this}, but weapon {weapon.GetWeaponClass()} only has {numShotPatternIndeces} shots in the pattern!" );
					return;
				}

				// do while that only runs on this condition
				while ( !doTranslationOffset )
				{
					if ( data.shotPatternIndex < 0 )
						break;

					if ( weaponData.ShotPattern[data.shotPatternIndex].LaunchPositionOffset.IsZero() )
						break;

					doTranslationOffset = true;
					break;
				}
			}
		}

		ownerWeapon	   = weapon;
		hasInitialized = true;
		UpdateStateAnimations();

		UCl_InteractiveWidgetManager interactiveWidgetManager = GetInteractiveWidgetManager();
		if ( IsValid( weapon.GetWeaponOwner() ) && IsValid( interactiveWidgetManager ) )
		{
			UpdateAimTargetState( weapon, weapon.GetWeaponOwner(), interactiveWidgetManager.GetLastTraceResult().GetActor() );
		}
	}

	void InitializeMotionWidgetArray( TArray<FReticleWidgetMotionData>& inArray, bool angleIsFromShotPattern )
	{
		int numMotionWidgets = inArray.Num();
		for ( int i = 0; i < numMotionWidgets; i++ )
		{
			FReticleWidgetMotionData& newData = inArray[i];
			UCanvasPanelSlot widgetAsPanel	  = Cast<UCanvasPanelSlot>( newData.Widget.Slot );
			if ( IsValid( widgetAsPanel ) )
			{
				newData.InitialPosition = newData.overrideInitialPosition ? newData.initialPositionOverride : widgetAsPanel.GetPosition();
				newData.InitialSize		= widgetAsPanel.GetSize();
				newData.InitialAngle	= newData.Widget.GetRenderTransformAngle();
				newData.useAngleParam	= angleIsFromShotPattern;
				inArray[i]				= newData;
			}
		}
	}

	UFUNCTION( BlueprintOverride )
	private void ReticleTick( ANCWeapon currentWeapon, float dt )
	{
		// HACK -> @Zhitao -> this work around is very reliable, but currentWeapon is null for the melee weapon... why?
		bool isMeleeReticle = this.IsA( UAS_ReticleWidgetMeleeWeapon::StaticClass() );

		if ( !hasInitialized && !isMeleeReticle )
		{
			InitializeReticle( currentWeapon );
		}

		if ( hasInitialized || isMeleeReticle )
		{
			ScriptReticleTick( ownerWeapon, float32( dt ) );
		}
	}

	protected void ScriptReticleTick( ANCWeapon currentWeapon, float32 dt )
	{
		if ( !IsValid( currentWeapon ) )
		{
			hasInitialized = false;
			return;
		}

		ANCPlayerCharacter playerPawn = ownerWeapon.GetWeaponOwner();

		UpdateReticleVisibility( playerPawn, currentWeapon, dt );
		if ( isReticleVisible )
		{
			if ( !IsValid( playerPawn ) )
			{
				return;
			}

			spreadNormalizeScalar = GetSpreadNormalizedForScreen( playerPawn );
			ManageReticleSpread( playerPawn, currentWeapon, dt );
			ManageShotgunPatternWidgets( playerPawn, currentWeapon );

			spreadFraction = currentWeapon.CalculateCurrentSpreadFrac();
			// Run BP last
			ReticleIsActiveTick( playerPawn, currentWeapon, dt );
		}

		float goalOpacity	= GetReticleGoalOpacity( playerPawn );
		if ( !materialDrivenReticle )
		{
			float renderOpacity = GetRenderOpacity();

			if ( renderOpacity != goalOpacity )
			{
				float interpSpeed 	= GetInterpSpeed();
				float alpha 		= Math::FInterpConstantTo( renderOpacity, goalOpacity, dt, interpSpeed );
				SetRenderOpacity( alpha );				
			}
		}

		const float32 elapsedSinceLastAttack = TO_SECONDS( playerPawn.GetTimeMilliseconds() - int( currentWeapon.LastAttackTime ) );
		const float32 timeBetweenShots = currentWeapon.GetWeaponData().timeBetweenShots;
		const float32 recentAttackFrac = float32( Math::Clamp( elapsedSinceLastAttack / ( timeBetweenShots * 5 ), 1.f, 0.f ) );
		//SetWeaponCollectionParameter( n"recent_attack_frac", 1.0 - recentAttackFrac );
		float elapsedVal = Math::Max( elapsedSinceLastAttack, 0 );
		SetWeaponCollectionParameter( n"time_since_last_attack", elapsedVal );
		//SetWeaponCollectionParameter( n"clip_ammo_remaining", elapsedSinceLastAttack );


		int numShotsPerRechamber = currentWeapon.WeaponConfigData.RechamberShotCount;
		int adjustedRechamberShotCounter = currentWeapon.RechamberNeeded ? currentWeapon.RechamberCounter + 1 : currentWeapon.RechamberCounter;
		SetWeaponCollectionParameter( n"remaining_shots_until_rechamber", numShotsPerRechamber - adjustedRechamberShotCounter );
		SetWeaponCollectionParameter( n"clip_ammo_remaining", currentWeapon.ClipAmmo );
	}

	// TODO: Early out if not used
	UFUNCTION( BlueprintEvent )
	void ManageReticleSpread( ANCPlayerCharacter playerPawn, ANCWeapon weapon, float dt )
	{
		AAS_PlayerEntity asPawn = Cast<AAS_PlayerEntity>( playerPawn );
		if ( !IsValid( asPawn ) )
			return;

		if ( spreadMotionWidgets.IsEmpty() && !materialDrivenReticle )
			return;

		// Hack until this can be made in code (where it can have a const)
		check( CanvasSizeBox.WidthOverride == RETICLE_CANVAS_WIDTH );
		UpdateCrosshairSpreadOffset( weapon );

		spreadRadiusPixels = CrosshairSpreadOffset * spreadNormalizeScalar;

		float32 spreadFrac = weapon.CalculateCurrentSpreadFrac();
		SetWeaponCollectionParameter( n"spread_fraction", spreadFrac );
		SetWeaponCollectionParameter( n"spread_pixels", spreadRadiusPixels );

		int numElements = spreadMotionWidgets.Num();
		for ( int i = 0; i < numElements; i++ )
		{
			FReticleWidgetMotionData& currentData = spreadMotionWidgets[i];
			currentData.curSpreadAngle			  = FVector2D( currentData.angle, currentData.angle );
		}

		PositionMotionWidgetsFromData( spreadMotionWidgets );
	}

	// TODO: This can be a lot smarter, and should only run either on tick, or when the weapon state changes
	// 			Unless it's a translation pattern, which tragically does need to run on tick
	void ManageShotgunPatternWidgets( ANCPlayerCharacter playerPawn, ANCWeapon weapon )
	{
		FWeaponData weaponData = weapon.GetWeaponData();
		float distance		   = maxTranslationOffsetTraceDistance;
		// This is set on first tick
		if ( doTranslationOffset )
		{
			// Don't trace for each projectile, rather just use your central look vector as an approximate location for where bullets will hit
			FVector eyeLocation;
			FRotator eyeRotation;
			playerPawn.GetActorEyesViewPoint( eyeLocation, eyeRotation );
			FVector worldAimPoint = eyeLocation + ( eyeRotation.GetForwardVector() * maxTranslationOffsetTraceDistance );
			TArray<AActor> ignoreActors;
			ignoreActors.Add( playerPawn );
			ignoreActors.Add( weapon );
			FHitResult worldAimPointResult = LineTraceSingle( eyeLocation, worldAimPoint, ETraceTypeQuery::WeaponFine, true, ignoreActors, true );

			if ( worldAimPointResult.GetbBlockingHit() )
			{
				worldAimPoint = worldAimPointResult.ImpactPoint;
				distance	  = worldAimPointResult.Distance;
			}

			distance = Math::Clamp( distance, minTranslationOffsetDistance, maxTranslationOffsetTraceDistance );
		}

		int numWidgets			  = shotPatternWidgets.Num();
		float adsScalar			  = playerPawn.IsADS() ? weaponData.ShotPatternADSAngleScalar : 1.0;
		int numShotPatternIndeces = weaponData.ShotPattern.Num();
		for ( int i = 0; i < numWidgets; i++ )
		{
			FReticleWidgetMotionData& motionData = shotPatternWidgets[i];

			// TODO: This is a workaround fix as this area pretty consistently breaks. Pretty undesirable since we have to count shot pattern every time.
			//		 The underlying bug seems to be that reticle widget class changes early or late from the weapon class. Meaning a shotgun reticle could be active when your weapon is an axe.
			//		 Hopefully this brute-force fixes future issues we'll have until this is properly fixed in code.
			if ( motionData.shotPatternIndex >= numShotPatternIndeces )
				continue;

			FShotPatternData patternData = weaponData.ShotPattern[motionData.shotPatternIndex];
			motionData.curSpreadAngle	 = FVector2D::ZeroVector;
			if ( doTranslationOffset )
			{
				motionData.curSpreadAngle.X += Math::RadiansToDegrees( Math::Atan2( patternData.LaunchPositionOffset.X, distance ) );
				motionData.curSpreadAngle.Y += Math::RadiansToDegrees( Math::Atan2( patternData.LaunchPositionOffset.Y, distance ) );
			}

			motionData.curSpreadAngle.X += patternData.LaunchAngle.Yaw * adsScalar;
			motionData.curSpreadAngle.Y += patternData.LaunchAngle.Pitch * adsScalar;

			motionData.useAngleParam = true;
			shotPatternWidgets[i]	 = motionData;
		}

		PositionMotionWidgetsFromData( shotPatternWidgets );
	}

	void PositionMotionWidgetsFromData( TArray<FReticleWidgetMotionData> widgets )
	{
		int numElements = widgets.Num();
		for ( int i = 0; i < numElements; i++ )
		{
			FReticleWidgetMotionData currentData = widgets[i];

			FVector2D offsetFromAngles;
			FVector2D dirFromAngles;
			if ( !currentData.useAngleParam )
			{
				dirFromAngles = GetXYFromAngle( currentData.curSpreadAngle.X, currentData.curSpreadAngle.Y );
				offsetFromAngles = dirFromAngles * spreadRadiusPixels;
			}
			else
			{
				float xSpread	 = GetCrosshairSpreadForAngle( currentData.curSpreadAngle.X ) * spreadNormalizeScalar;
				float ySpread	 = GetCrosshairSpreadForAngle( currentData.curSpreadAngle.Y ) * spreadNormalizeScalar * -1;
				offsetFromAngles = FVector2D( xSpread, ySpread );

				float curSpreadAngleRadius	 = offsetFromAngles.Size();
				innerTranslationOffsetRadius = Math::Min( curSpreadAngleRadius, innerTranslationOffsetRadius );
				outerTranslationOffsetRadius = Math::Max( curSpreadAngleRadius, outerTranslationOffsetRadius );

				if ( currentData.useShotPatternIndexForRadius )
				{
					float spreadRadius = offsetFromAngles.Size();
					dirFromAngles   = GetXYFromAngle( currentData.angle, currentData.angle );
					offsetFromAngles = dirFromAngles * spreadRadius;
				}
			}

			offsetFromAngles *= currentData.Scalar;

			float widgetScale = -1;
			if ( currentData.scaleWidget )
			{
				widgetScale		 = offsetFromAngles.Size() * 2;
				offsetFromAngles = FVector2D::ZeroVector;
			}

			offsetFromAngles += currentData.InitialPosition + ( dirFromAngles * currentData.spreadOffset );

			UCanvasPanelSlot widgetAsCanvasSlot = Cast<UCanvasPanelSlot>( currentData.Widget.Slot );
			widgetAsCanvasSlot.SetPosition( offsetFromAngles );

			if ( currentData.scaleWidget )
			{
				widgetAsCanvasSlot.SetSize( FVector2D( widgetScale, widgetScale ) );
			}
		}
	}

	float GetSpreadNormalizedForScreen( ANCPlayerCharacter asPlayer )
	{
		if ( !asPlayer.IsADS() )
		{
			FVector2D vpSize = WidgetLayout::GetViewportSize();

			// Reallllly need to remove this wtf. Can the non-material reticles be floating widgets so that UMG handles scaling?
			/* 
			// Spread is a ratio of gun spread angle : camera FOV. Camera FOV is measured on the horizontal. So we want the absolute width of the widget to match viewport width.
			FVector2D sizeBoxAbsSize = CanvasSizeBox.GetCachedGeometry().GetAbsoluteSize();

			if ( sizeBoxAbsSize.X > 0 && sizeBoxAbsSize.X < 100000.0 ) // sometimes, sizeBoxAbsSize.X can go to infinity?
			{
				float ratio				 = vpSize.X / sizeBoxAbsSize.X;
				FVector2D newRenderScale = FVector2D( ratio, ratio ) * cachedRenderScale;
				CanvasSizeBox.SetRenderScale( newRenderScale );
				cachedRenderScale = newRenderScale;
			}
			*/

			// New
			float ratioTest = 1920 / vpSize.X;

			float safeRenderScale = Math::Max( cachedRenderScale.X, 0.001 );
			return RETICLE_CANVAS_WIDTH * 0.5f * ratioTest * ( 1.0 / safeRenderScale );
		}
		else
		{
			// Reallllly need to remove this wtf. Can the non-material reticles be floating widgets so that UMG handles scaling?
			//CanvasSizeBox.SetRenderScale( FVector2D( 1, 1 ) );
			return RETICLE_CANVAS_WIDTH * 0.5f;
		}
	}

	FVector2D GetXYFromAngle( float yaw, float pitch )
	{
		float xScale = Math::Sin( Math::DegreesToRadians( yaw ) );
		float yScale = Math::Cos( Math::DegreesToRadians( pitch ) );

		FVector2D newPosition = FVector2D( xScale, yScale );

		return newPosition;
	}

	float GetXFromAngle( float angle )
	{
		float xScale = Math::Cos( Math::DegreesToRadians( angle ) );
		return xScale;
	}

	bool ShouldShowBecauseOfWeaponState( EWeaponState curWeaponState )
	{
		switch ( curWeaponState )
		{
			case EWeaponState::PrimaryAttack:
			case EWeaponState::Idle:
			case EWeaponState::RaiseFromSprint:
			case EWeaponState::Deploy:
			case EWeaponState::Rechambering:
			case EWeaponState::Reloading:
				return true;

			default:
				return false;
		}
	}

	bool ShouldHalfFadeBecauseOfPlayerState( ANCPlayerCharacter pawn )
	{
		if ( pawn.IsInSprinting )
		{
			switch ( pawn.MovementMode )
			{
				case EMovementMode::MOVE_Falling:
				case EMovementMode::MOVE_Flying:
					break;

				default:
					return true;
			}
		}

		if ( pawn.IsMantling() )
		{
			return true;
		}

		return false;
	}

	// TODO: @Davis - This is state tracking to prevent duplicate Hide/Show calls. Should be event based not polling driven.

	// weapon reticle hide counter assumes reticle is visible, if cur state is false, then when it sets to true that'll trigger a show event, which will show an already-shown reticle.
	bool curStateAllowsShow			  = true;
	bool lastCurStateAllowedShow	  = false;
	bool shouldShowOffhandReticle	  = false;
	int lastOffHandReticleTimeMS 	  = -1000;
	bool lastShouldShowOffhandReticle = false;
	bool shouldPartialFadeReticle	  = false;
	bool lastShouldPartialFadeReticle = false;
	bool shouldHideReticleForADS	  = false;
	bool lastShouldHideReticleForADS  = false;

	bool wasReloading = false;
	bool isReloading = false;

	UFUNCTION( NotBlueprintCallable )
	void UpdateReticleVisibility( ANCPlayerCharacter pawn, ANCWeapon weapon, float32 dt )
	{
		// TODO: Get a state changed callback from weapon
		EWeaponState curWeaponState = weapon.GetWeaponState();

		// TODO: Get callbacks from player when state changes
		
		isReloading = weapon.GetWeaponState() == EWeaponState::Reloading && !weapon.IsReadyToFire();

		const float32 elapsedSinceLastAttack = TO_SECONDS( GetTimeMilliseconds() - int( weapon.LastAttackTime ) );
		const UWeaponPrimaryAsset weaponAsset = MakeWeaponId( weapon ).WeaponAsset;
		int mods = weapon.GetActiveModsBitField();

		FWeaponData weaponDataToUse;
		if ( mods > 0 )
		{
			ANCWeapon::ApplyModsToWeaponData( GetCurrentWorld(), weaponAsset, mods, weaponDataToUse );
		}
		else
		{
			weaponDataToUse = weaponAsset.weaponData;
		}

		bool isLastShotInBurst = weapon.BurstIndex == ( 0 ) && weaponDataToUse.burstCount > 0;
		float timeUntilNextShot = isLastShotInBurst ? weaponDataToUse.timeBetweenBursts : weaponDataToUse.timeBetweenShots;
		timeUntilNextShot += 0.1; // hardcoded -- will this work around below bug?

		// There's a tiny misalignment where ready to fire is true a hair after weapon returns to idle. NC1-23138
		bool halfFadeBetweenShots = IsSlowFiringWeapon( weapon );
		bool workaroundHasEnoughTimePassed = elapsedSinceLastAttack - timeUntilNextShot > 0;
		bool shouldHalfFade_weaponState = !weapon.IsReadyToFire() && ( weapon.GetWeaponState() != EWeaponState::PrimaryAttack || halfFadeBetweenShots ) && workaroundHasEnoughTimePassed;
		bool shouldHalfFade_playerState = ShouldHalfFadeBecauseOfPlayerState( pawn );

		lastShouldPartialFadeReticle = shouldPartialFadeReticle;
		shouldPartialFadeReticle	 = shouldHalfFade_playerState || shouldHalfFade_weaponState;
		
		if ( wasReloading && !isReloading )
		{
			SetWeaponCollectionParameter( n"reticle_bright_pulse_time", GetMaterialTime() );
		}

		wasReloading = isReloading;
		SetWeaponCollectionParameter( n"reticle_is_dimmed", shouldPartialFadeReticle ? 1 : 0 );

		if ( shouldPartialFadeReticle != lastShouldPartialFadeReticle )
		{
			if ( shouldHalfFade_playerState || shouldHalfFade_weaponState )
			{
				SetReticleToFaded();
			}
			else
			{
				UnSetReticleToFaded();
			}
		}

		lastCurStateAllowedShow = curStateAllowsShow;
		curStateAllowsShow		= ShouldShowBecauseOfWeaponState( curWeaponState );

		AAS_PlayerEntity player		 = Cast<AAS_PlayerEntity>( pawn );
		ANCMeleeWeapon meleeWeapon	 = Cast<ANCMeleeWeapon>( pawn.GetWeaponAtSlot( WeaponSlot::MeleeSlot ) );
		lastShouldShowOffhandReticle = shouldShowOffhandReticle;
		shouldShowOffhandReticle	 = false;
		if ( IsValid( ClMeleeReticle() ) )
		{
			shouldShowOffhandReticle = ClMeleeReticle().ShouldShowOffhandReticleForTarget( player, meleeWeapon );
		}

		if ( shouldShowOffhandReticle != lastShouldShowOffhandReticle )
		{
			if ( shouldShowOffhandReticle )
			{
				HideReticle();
			}
			else
			{
				ShowReticle();
			}

			lastOffHandReticleTimeMS = GetGameTimeMS();
		}

		if ( curStateAllowsShow != lastCurStateAllowedShow )
		{
			if ( curStateAllowsShow )
			{
				ShowReticle();
			}
			else
			{
				HideReticle();
			}
		}

		lastShouldHideReticleForADS = shouldHideReticleForADS;
		switch ( fadeOutForADS )
		{
			case EADSFadeOutStyle::FADE_OUT_FOR_ADS:
			{
				shouldHideReticleForADS = pawn.IsADS();
				break;
			}
			case EADSFadeOutStyle::FADE_IN_FOR_ADS:
			{
				shouldHideReticleForADS = !pawn.IsADS();
				break;
			}
			default:
			{
				break;
			}
		}

		if ( lastShouldHideReticleForADS != shouldHideReticleForADS )
		{
			if ( shouldHideReticleForADS )
			{
				HideReticle();
			}
			else
			{
				ShowReticle();
			}
		}
				
		float goalVal 			= isReticleVisible ? 1 : 0;
		float interpSpeed 		= GetInterpSpeed();
		float newVal 			= Math::FInterpConstantTo( curVisibleAlphaValue, goalVal, dt, interpSpeed );				
		
		SetWeaponCollectionParameter( VISIBILITY_PARAM, newVal );
		
	/*	the axe reticle squishes on Y as it fades out.  This is a nice way to mirror that language
		I think it also looks nice for weapon switch... but a much faster version.  -Mo		*/
		SetRenderScale( FVector2D( 1, newVal ) );

	/*	have to use a stored value because Material::GetScalarParameterValue( GetGlobalParameters().weaponParameters, VISIBILITY_PARAM ) 
		does not return the correct value	-Mo		*/
		curVisibleAlphaValue = newVal;
	}

	float GetInterpSpeed()
	{
		float32 INTERP_SPEED_SLOW 	= 5;	//this roughly matches the speed of axe fade in anims ( 0.2s )
		float32 INTERP_SPEED_FAST 	= 8;	//better speed for weapon switching
		float32 elapsedTime 		= TO_SECONDS( GetGameTimeMS() - lastOffHandReticleTimeMS );

		return elapsedTime < 0.2 ? INTERP_SPEED_SLOW : INTERP_SPEED_FAST;
	}

	private bool isPartialVisible = false;

	private int reticleHiddenCounter = 0;
	private void HideReticle()
	{
		if ( reticleHiddenCounter == 0 )
		{
			isReticleVisible = false;
		}

		reticleHiddenCounter += 1;
	}

	private void ShowReticle()
	{
		if ( reticleHiddenCounter == 1 )
		{
			isReticleVisible = true;
		}

		reticleHiddenCounter -= 1;
	}

	private void SetReticleToFaded()
	{
		isPartialVisible = true;
	}

	private void UnSetReticleToFaded()
	{
		isPartialVisible = false;
	}

	float lastADSFrac							= 0;
	float curADSFrac							= 0;
	const FVector2D HIDE_FOR_ADS_FADE_OUT_RANGE = FVector2D( 0.0, 0.05 );
	const FVector2D HIDE_FOR_ADS_FADE_IN_RANGE	= FVector2D( 0.2, 0.5 );
	const FVector2D SHOW_FOR_ADS_FADE_OUT_RANGE = FVector2D( 0.0, 0.6 );
	const FVector2D SHOW_FOR_ADS_FADE_IN_RANGE	= FVector2D( 0.6, 1.0 );
	private float GetReticleGoalOpacity( ANCPlayerCharacter ownerPlayer )
	{
		if ( !isReticleVisible )
		{
			return 0;
		}
		else
		{
			if ( isPartialVisible )
			{
				return 0.5;
			}
		}

		lastADSFrac		   = curADSFrac;
		curADSFrac		   = ownerPlayer.GetAdsFraction();
		bool isEnteringADS = curADSFrac > lastADSFrac;
		SetWeaponCollectionParameter( n"is_entering_ads", isEnteringADS ? 1 : 0 );
		SetWeaponCollectionParameter( n"ads_fraction", curADSFrac );
		switch ( fadeOutForADS )
		{
			case EADSFadeOutStyle::FADE_OUT_FOR_ADS:
			{
				FVector2D inputRange = isEnteringADS ? HIDE_FOR_ADS_FADE_OUT_RANGE : HIDE_FOR_ADS_FADE_IN_RANGE;
				return Math::GetMappedRangeValueClamped( inputRange, FVector2D( 1.0, 0.0 ), curADSFrac );
			}

			case EADSFadeOutStyle::FADE_IN_FOR_ADS:
			{
				FVector2D inputRange = isEnteringADS ? SHOW_FOR_ADS_FADE_OUT_RANGE : SHOW_FOR_ADS_FADE_IN_RANGE;
				return Math::GetMappedRangeValueClamped( inputRange, FVector2D( 0, 1 ), curADSFrac );
			}

			default:
			{
				break;
			}
		}

		return 1;
	}

	// Overridden in BP
	UFUNCTION( BlueprintEvent )
	void ReticleIsActiveTick( ANCPlayerCharacter Pawn, ANCWeapon Weapon, float Dt ) {}

	UFUNCTION()
	private void OnCrosshairActorChanged( AAS_HUD hud, AActor oldCrosshairActor, AActor newCrosshairActor )
	{
		if ( !IsValid( ownerWeapon ) )
			return;

		ANCPlayerCharacter weaponOwner = ownerWeapon.GetWeaponOwner();

		if ( !IsValid( weaponOwner ) )
			return;

		UpdateAimTargetState( ownerWeapon, weaponOwner, newCrosshairActor );
	}

	EAffinity previousAimTargetRelationship = EAffinity::None;
	float lastBrightPulseFromEnemyTime = 0;
	private void UpdateAimTargetState( ANCWeapon weapon, ANCPlayerCharacter weaponOwner, AActor aimTarget )
	{
		int ownerTeam = -1;
		if ( IsValid( weaponOwner ) )
		{
			ownerTeam = weaponOwner.GetTeam();
		}

		int aimTargetTeam = -1;
		if ( IsValid( aimTarget ) && aimTarget.CanHaveTeam() )
		{
			aimTargetTeam = aimTarget.GetTeam();
		}

		// Shared mod
		lookingAtBasebreakerTarget = false;

		EAffinity relationship = EAffinity::None;
		if ( IsValid( aimTarget ) )
		{
			AAS_DestructibleItem targetAsDestructibleItem = Cast<AAS_DestructibleItem>( aimTarget );
			ANCDestructible targetAsDestructible		  = Cast<ANCDestructible>( aimTarget );
			AAS_PlayerEntity targetAsPlayer				  = Cast<AAS_PlayerEntity>( aimTarget );
			if ( CL_IsActorStealthed( aimTarget ) )
			{
				relationship = EAffinity::None;
			}
			else if ( ( IsValid( targetAsDestructibleItem ) && targetAsDestructibleItem.shouldShowHostileReticle ) || IsValid( targetAsPlayer ) )
			{
				relationship = UNCUtils::GetRelationshipBetweenTeams( ownerTeam, aimTargetTeam );
			}
			else if ( IsValid( targetAsDestructible ) && UNCUtils::GetRelationshipBetweenTeams( ownerTeam, aimTargetTeam ) != EAffinity::Friendly )
			{
				if ( IsModActiveOnWeapon( weapon, WeaponConst::BaseBreaker::MODNAME_DEFAULT ) )
				{
					relationship			   = EAffinity::Enemy;
					lookingAtBasebreakerTarget = true;
				}
			}
		}

		FLinearColor colorToUse = FLinearColor::White;
		float32 animTime		= 0;
		int colorIndex = 0;
		switch ( relationship )
		{
			case EAffinity::Enemy:
			{
				animTime   = 1;
				colorIndex = 1;
				colorToUse = FLinearColor::Red;
				break;
			}
			case EAffinity::Friendly:
			{
				animTime   = 2;
				colorIndex = 3;
				colorToUse = FLinearColor::Green;
				break;
			}
			default:
				break;
		}
	
		SetWeaponCollectionParameter( n"reticle_color_index", colorIndex );
		SetWeaponCollectionParameter( n"aiming_at_enemy", relationship == EAffinity::Enemy ? 1 : 0 );
		if ( previousAimTargetRelationship != relationship && relationship == EAffinity::Enemy )
		{
			float materialTime = GetMaterialTime();
			if ( ( materialTime - lastBrightPulseFromEnemyTime ) > 1 )
			{
				lastBrightPulseFromEnemyTime = materialTime;
				SetWeaponCollectionParameter( n"reticle_bright_pulse_time", materialTime );
			}
		}

		if ( !materialDrivenReticle )
		{
			// Only color ADS reticle if the widget has an anim built in. Coloring reticles is too risky otherwise.
			bool useAnimInsteadOfColor = IsValid( anim_aimTargetState );
			if ( useAnimInsteadOfColor )
			{
				PlayAnimation( anim_aimTargetState, animTime, 1, EUMGSequencePlayMode::Forward, 0 );
				SetColorAndOpacity( FLinearColor::White );
			}
			else if ( !weaponOwner.IsADS() || useAnimInsteadOfColor )
			{
				SetColorAndOpacity( colorToUse );
			}
			else
			{
				SetColorAndOpacity( FLinearColor::White );
			}
		}

		previousAimTargetRelationship = relationship;

		OnAimTargetStateUpdated();
	}

	protected void OnAimTargetStateUpdated() {}

	UFUNCTION()
	void OnSignal_WeaponModificationMenuOpened( FName signalName, UObject signalSource )
	{
		ANCPlayerCharacter localPlayer = Client_GetLocalPawn();
		if ( IsValid( localPlayer ) && IsValid( ownerWeapon ) )
		{
			HideReticle();
		}
	}

	UFUNCTION()
	void OnSignal_WeaponModificationMenuClosed( FName signalName, UObject signalSource )
	{
		ANCPlayerCharacter localPlayer = Client_GetLocalPawn();
		if ( IsValid( localPlayer ) && IsValid( ownerWeapon ) )
		{
			ShowReticle();
		}
	}

	void RegisterReticleAnimationIfValid( FName animName, UWidgetAnimation animation, EUMGSequencePlayMode deployPlayMode, EUMGSequencePlayMode unDeployPlayMode, float playbackRate )
	{
		if ( IsValid( animation ) )
		{
			RegisterReticleAnimation( animName, animation, deployPlayMode, unDeployPlayMode, playbackRate );
		}
	}

	void RegisterReticleAnimation( FName animName, UWidgetAnimation animation, EUMGSequencePlayMode deployPlayMode, EUMGSequencePlayMode unDeployPlayMode, float playbackRate )
	{
		reticleAnimations.Add( animName, FReticleAnimationData( animation, deployPlayMode, unDeployPlayMode, playbackRate ) );
	}

	void RegisterReticleAnimation( FName animName, FReticleAnimationData animData )
	{
		reticleAnimations.Add( animName, animData );
	}

	void PlayReticleAnimation( FName animationName, bool shouldDeploy )
	{
		EReticleAnimationDeployState state = shouldDeploy ? EReticleAnimationDeployState::DEPLOYED : EReticleAnimationDeployState::UNDEPLOYED;
		PlayReticleAnimationInternal( animationName, state, false );
	}

	void PlayReticleAnimationInstant( FName animationName, bool shouldDeploy )
	{
		EReticleAnimationDeployState state = shouldDeploy ? EReticleAnimationDeployState::DEPLOYED : EReticleAnimationDeployState::UNDEPLOYED;
		PlayReticleAnimationInternal( animationName, state, true );
	}

	protected bool IsReticleAnimationRegistered( FName animationName )
	{
		return reticleAnimations.Contains( animationName );
	}

	private void PlayReticleAnimationInternal( FName animationName, EReticleAnimationDeployState state, bool playInstant )
	{
		if ( !reticleAnimations.Contains( animationName ) )
			return;

		FReticleAnimationData& animData = reticleAnimations[animationName];

		if ( animData.deployedState == state )
			return;

		float startTime				  = 0;
		bool shouldDeploy			  = state == EReticleAnimationDeployState::DEPLOYED;
		EUMGSequencePlayMode playMode = shouldDeploy ? animData.enablePlayMode : animData.disablePlayMode;
		int numLoopsToPlay			  = shouldDeploy ? animData.numLoopsToPlayEnable : animData.numLoopsToPlayDisable;
		if ( playInstant )
		{
			startTime = 1;
		}
		else if ( IsAnimationPlaying( animData.animation ) )
		{
			startTime = GetAnimationCurrentTime( animData.animation );
		}

		PlayAnimation( animData.animation, startTime, numLoopsToPlay, playMode, animData.playbackRate, false );

		UNCAudioAsset soundToPlay = shouldDeploy ? animData.animSound_enable : animData.animSound_disable;
		if ( IsValid( soundToPlay ) )
			Client_EmitSoundUI( soundToPlay );

		animData.UpdateChargedIdleSound( shouldDeploy );

		animData.deployedState = state;
	}

	protected void RegisterScopeMaterialAnimation( FName animName, FScopeMaterialParameters enabledStateParams, FScopeMaterialParameters disabledStateParams, UAS_Thread_ScopeMaterialAnimation animThread_Enabled, UAS_Thread_ScopeMaterialAnimation animThread_Disabled )
	{
		if ( keyToAnimThreadClass_Enabled.Contains( animName ) )
		{
			Warning( f"Warning! Tried to register a scope material animation that has already been registered! Duplicate?" );
			return;
		}

		keyToAnimThreadClass_Enabled.Add( animName, animThread_Enabled );
		keyToAnimThreadClass_Disabled.Add( animName, animThread_Disabled );
		keyToScopeMaterialAnimParams_Enabled.Add( animName, enabledStateParams );
		keyToScopeMaterialAnimParams_Disabled.Add( animName, disabledStateParams );
	}

	void PlayScopeMaterialAnimation( FName key, EScopeMaterialAnimationPlayMode playMode )
	{
		CancelScopeMaterialAnimationThread( key );

		switch ( playMode )
		{
			case EScopeMaterialAnimationPlayMode::SET_ENABLED_STATE:
			{
				InitializeScopeMaterialParameters( keyToScopeMaterialAnimParams_Enabled[key] );
				break;
			}
			case EScopeMaterialAnimationPlayMode::PLAY_ENABLED_ANIM:
			{
				StartScopeMaterialAnimationThread( key, true );
				break;
			}
			case EScopeMaterialAnimationPlayMode::SET_DISABLED_STATE:
			{
				InitializeScopeMaterialParameters( keyToScopeMaterialAnimParams_Disabled[key] );
				break;
			}
			case EScopeMaterialAnimationPlayMode::PLAY_DISABLED_ANIM:
			{
				StartScopeMaterialAnimationThread( key, false );
				break;
			}
		}
	}
	// How to pass params to this??
	// Child class that sets defaults? YUCKERS
	// Need to recycle this object instead of instantiate it every time.
	private void StartScopeMaterialAnimationThread( FName animKey, bool isEnabledAnimation )
	{
		UAS_Thread_ScopeMaterialAnimation threadToUse;
		if ( isEnabledAnimation )
		{
			if ( !keyToAnimThreadClass_Enabled.Contains( animKey ) )
			{
				Warning( f"Warning! No enable scope material animation was registered for key {animKey}" );
				return;
			}

			if ( !IsValid( keyToAnimThreadClass_Enabled[animKey] ) )
			{
				Warning( f"Warning! Enable scope material animation for key {animKey} is invalid!" );
				return;
			}

			threadToUse = keyToAnimThreadClass_Enabled[animKey];
		}
		else
		{
			if ( !keyToAnimThreadClass_Disabled.Contains( animKey ) )
			{
				Warning( f"Warning! No disable scope material animation was registered for key {animKey}" );
				return;
			}

			if ( !IsValid( keyToAnimThreadClass_Disabled[animKey] ) )
			{
				Warning( f"Warning! disable scope material animation for key {animKey} is invalid!" );
				return;
			}

			threadToUse = keyToAnimThreadClass_Disabled[animKey];
		}

		threadToUse.SetReticleWidgetOwner( this );
		threadToUse.Start();
	}

	void CancelScopeMaterialAnimationThread( FName animKey )
	{
		if ( activeScopeMaterialAnimations.Contains( animKey ) )
		{
			activeScopeMaterialAnimations[animKey].Cancel();
		}
	}

	// Should only call from thread itself
	void RegisterActiveScopeAnimationThread( FName animKey, UAS_Thread_ScopeMaterialAnimation newThread )
	{
		if ( !IsValid( newThread ) )
		{
			Warning( f"Warning! Weapon context {this} tried to register scope animation thread with key {animKey}, but the thread itself was invalid!" );
			return;
		}

		CancelScopeMaterialAnimationThread( animKey );

		activeScopeMaterialAnimations.Add( animKey, newThread );
	}

	// Should only call from thread itself
	void UnregisterActiveScopeAnimationThread( FName animKey )
	{
		if ( activeScopeMaterialAnimations.Contains( animKey ) )
		{
			activeScopeMaterialAnimations.Remove( animKey );
		}
	}

	protected void InitializeScopeMaterialParameters( FScopeMaterialParameters inParams )
	{
		if ( !IsValid( ownerWeapon ) )
			return;

		ANCPlayerCharacter ownerPlayer = ownerWeapon.GetWeaponOwner();
		if ( !IsValid( ownerPlayer ) )
			return;

		UNCViewmodelComponent viewmodel = ownerPlayer.GetViewmodelComponent();
		if ( !IsValid( viewmodel.ScopeMaterial ) )
			return;

		UMaterialInstanceDynamic scopeMaterial = viewmodel.ScopeMaterial;
		int numAimpointParams				   = inParams.aimpointParams.Num();
		for ( int i = 0; i < numAimpointParams; i++ )
		{

			FString aimpointPrefix					 = f"Aimpoint{i + 1 :02d}_";
			FScopeMaterialParameters_Aimpoint params = inParams.aimpointParams[i];
			FName paramName							 = NAME_None;

			if ( params.setColor )
			{
				paramName = FName( aimpointPrefix + ScopeMaterialParams::AIMPOINT_COLOR_SUFFIX );
				scopeMaterial.SetVectorParameterValue( paramName, params.color );
			}

			if ( params.setDepth )
			{
				paramName = FName( aimpointPrefix + ScopeMaterialParams::AIMPOINT_DEPTH_SUFFIX );
				scopeMaterial.SetScalarParameterValue( paramName, params.depthStart );
			}

			if ( params.setCenter )
			{
				paramName				  = FName( aimpointPrefix + ScopeMaterialParams::AIMPOINT_CENTER_SUFFIX );
				FLinearColor colorFromVec = FLinearColor( params.centerOffset, 0 );
				scopeMaterial.SetVectorParameterValue( paramName, colorFromVec );
			}

			if ( params.setTexture )
			{
				paramName = FName( aimpointPrefix + ScopeMaterialParams::AIMPOINT_TEXTURE_SUFFIX );
				scopeMaterial.SetTextureParameterValue( paramName, params.texture );
			}
		}
	}

	protected FName GetScopeMaterialAimpointParamName( int index, FString suffix )
	{
		FString aimpointPrefix = f"Aimpoint{index :02d}_";
		return FName( aimpointPrefix + ScopeMaterialParams::AIMPOINT_COLOR_SUFFIX );
	}

	UFUNCTION( NotBlueprintCallable )
	protected void Signal_LocalModsChangedDelayed( FName signalName, UObject signalSource )
	{
		if ( !IsValid( ownerWeapon ) )
			return;

		if ( !IsValid( Client_GetLocalPawn() ) )
			return;

		// Safety, since this is delayed
		if ( Client_GetLocalPawn().GetActiveWeapon() != ownerWeapon )
			return;

		// TODO: Don't run if owner weapon doesn't match weapon context owner
		UpdateStateAnimations();
	}

	protected void UpdateStateAnimations()
	{
		PlayReticleAnimation( WeaponConst::Optics::MODNAME_MARKSMAN, IsModActiveOnWeapon( ownerWeapon, WeaponConst::Optics::MODNAME_MARKSMAN ) );
		PlayReticleAnimation( WeaponConst::Optics::MODNAME_SKIRMISH, IsModActiveOnWeapon( ownerWeapon, WeaponConst::Optics::MODNAME_SKIRMISH ) );
		PlayReticleAnimation( WeaponConst::Optics::MODNAME_SKIRMISH, IsModActiveOnWeapon( ownerWeapon, WeaponConst::Optics::MODNAME_SKIRMISH_REPEATER ) );
	}

	UFUNCTION( NotBlueprintCallable )
	private void Signal_LocalADSChangedDelayed( FName signalName, UObject signalSource )
	{
		if ( !IsValid( ownerWeapon ) || !IsValid( ownerWeapon.WeaponOwner ) )
			return;

		if ( !ownerWeapon.IsEquipped() )
		{
			return;
		}

		OnLocalPlayerADSChanged( ownerWeapon.WeaponOwner.IsADS() );
	}

	protected void OnLocalPlayerADSChanged( bool isADS )
	{
		UCl_InteractiveWidgetManager interactiveWidgetManager = GetInteractiveWidgetManager();
		AActor traceResult									  = nullptr;
		if ( IsValid( interactiveWidgetManager ) )
		{
			traceResult = interactiveWidgetManager.GetLastTraceResult().GetActor();
		}

		UpdateAimTargetState( ownerWeapon, ownerWeapon.GetWeaponOwner(), traceResult );
	}

	private float GetMaterialTime()
	{
		// The material time node starts counting when the editor starts. This will create an offset, which makes it so the material starts counting from 0
		int seconds = 0;
		float decimal = 0;
		Gameplay::GetAccurateRealTime( seconds, decimal );

		decimal *= 100;
		decimal = Math::CeilToFloat( decimal );
		return float( seconds ) + ( decimal * 0.01 );
	}

	UFUNCTION( BlueprintOverride )
	void Destruct()
	{
		ScriptCallbacks().UnRegisterSignalCallback( Signals::WEAP_MOD_MENU_OPENED, this, n"OnSignal_WeaponModificationMenuOpened" );
		ScriptCallbacks().UnRegisterSignalCallback( Signals::WEAP_MOD_MENU_CLOSED, this, n"OnSignal_WeaponModificationMenuClosed" );
		ScriptCallbacks().client_onCrosshairActorChanged.Unbind( this, n"OnCrosshairActorChanged" );

		ScriptCallbacks().UnRegisterSignalCallback( Signals::WEAP_LOCAL_PLAYER_MODS_CHANGED_DELAYED, this, n"Signal_LocalModsChangedDelayed" );
		ScriptCallbacks().UnRegisterSignalCallback( Signals::WEAP_LOCAL_PLAYER_ADS_CHANGED, this, n"Signal_LocalADSChangedDelayed" );

		for ( TMapIterator<FName, FReticleAnimationData>& entry : reticleAnimations )
		{
			FReticleAnimationData animData = entry.GetValue();
			animData.UpdateChargedIdleSound( false );
		}
	}
}

void SetWeaponCollectionParameter( FName parameter, float value )
{
	if ( !IsValid( GetGlobalParameters() ) )
	{
		return;
	}

	if ( !IsValid( GetGlobalParameters().weaponParameters ) )
	{
		return;
	}

	Material::SetScalarParameterValue( GetGlobalParameters().weaponParameters, parameter, value );
}