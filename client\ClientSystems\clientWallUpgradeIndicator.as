class AAS_ClientWallUpgradeIndicator : AActor
{
	UPROPERTY( DefaultComponent, RootComponent )
	protected USceneComponent root;

	UPROPERTY( DefaultComponent )
	protected USceneComponent secondarIndicatorRoot;

	// An annoying chain of const-correctness prevents this from being const
	UPROPERTY( EditDefaultsOnly )
	UAS_PinnableWidgetSettings pinWidgetSettings;


	protected UNiagaraComponent primaryIndicatorFX;
	protected UNiagaraComponent secondaryIndicatorFX;

	void SetIndicatorColor( FLinearColor newColor )
	{
		if ( !IsValid( primaryIndicatorFX ) )
		{
			return;
		}

		primaryIndicatorFX.SetNiagaraVariableLinearColor( f"Color", newColor );
	}

	void SetEffectAtIndex( int index, UNiagaraSystem newFX )
	{
		ScriptAssert( index == 0 || index == 1, f"Tried to set effect at invalid index {index} on client wall indicator!");

		if ( !IsValid( newFX ) )
		{
			return;
		}

		if ( index == 0 )
		{
			SetPrimaryEffect( newFX );
		}
		else if ( index == 1 )
		{
			SetSecondaryEffect( newFX );
		}
	}

	UNiagaraComponent& GetEffectAtIndex( int index )
	{
		ScriptAssert( index == 0 || index == 1, f"Tried to set effect at invalid index {index} on client wall indicator!");

		if ( index == 0 )
		{
			return primaryIndicatorFX;
		}

		return secondaryIndicatorFX;
	}

	private void SetPrimaryEffect( UNiagaraSystem newFX )
	{
		if ( IsValid( primaryIndicatorFX ) && primaryIndicatorFX.GetAsset() == newFX )
		{
			return;
		}

		ClearPrimaryEffect();

		primaryIndicatorFX = Client_SpawnEffectOnEntity_Looping( newFX, this );
	}

	private void SetSecondaryEffect( UNiagaraSystem newFX )
	{
		if ( IsValid( secondaryIndicatorFX ) && secondaryIndicatorFX.GetAsset() == newFX )
		{
			return;
		}

		ClearSecondaryEffect();

		FVector offset = secondarIndicatorRoot.GetRelativeLocation();
		secondaryIndicatorFX = Client_SpawnEffectOnEntity_Looping( newFX, this, NAME_None, offset, secondarIndicatorRoot.GetRelativeRotation(), secondarIndicatorRoot.GetRelativeScale3D() );
	}

	void ClearEffectAtIndex( int index )
	{
		ScriptAssert( index == 0 || index == 1, f"Tried to set effect at invalid index {index} on client wall indicator!");
		
		if ( index == 0 )
		{
			ClearPrimaryEffect();
		}
		else if ( index == 1 )
		{
			ClearSecondaryEffect();
		}
	}

	void ClearPrimaryEffect()
	{
		if ( IsValid( primaryIndicatorFX ) )
		{
			primaryIndicatorFX.DestroyComponent( primaryIndicatorFX );
		}
	}

	void ClearSecondaryEffect()
	{
		if ( IsValid( secondaryIndicatorFX ) )
		{
			secondaryIndicatorFX.DestroyComponent( secondaryIndicatorFX );
		}
	}
}
