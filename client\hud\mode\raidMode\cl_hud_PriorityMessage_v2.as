UCLASS( Abstract )
class UAS_PriorityMessageWidget_v2 : UAS_PriorityMessageWidget
{
	access Internal						= private, UAS_PriorityMessageListItem;
	const float32 HIGHLIGHT_UPDATE_TIME = 0.2;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	UWidgetAnimation Highlight_SlideOutAnim;
	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	UWidgetAnimation Highlight_SlideInAnim;

	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	access:Internal UVerticalBox ListMessageBox;

	UPROPERTY( BlueprintReadOnly, NotEditable, Meta = ( BindWidget ) )
	private USizeBox Highlight_SizeBox;

	private int ListItemPriorityIndex = -1;
	private float HighlightChangeOldHeight = 0.0;
	private int HighLightChangeStartTimeMS = TO_MILLISECONDS( HIGHLIGHT_UPDATE_TIME * -1 );
	private FMargin HighLightChangeOldPadding = FMargin( 0 );
	private FLinearColor HightlightChangeColor = Get_BGColorCTAMessage();

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		Super::Construct();

		Highlight_SizeBox.SetHeightOverride( 0 );
		Set_BGColorIcon( PriorityMessageColor::GRAY_COLOR_ICON_BG );
	}

	private UAS_PriorityMessageListItem __CreateNewListItem( UAS_PriorityMessageListData Data )
	{
		UAS_PriorityMessageListItem NewListItem = Cast<UAS_PriorityMessageListItem>( WidgetBlueprint::CreateWidget( Data.WidgetClass, GetOwningPlayer() ) );
		NewListItem.Init( Data, this );

		return NewListItem;
	}

	TArray<UAS_PriorityMessageListItem> GetListItemWidgets()
	{
		TArray<UWidget> children = ListMessageBox.GetAllChildren();
		TArray<UAS_PriorityMessageListItem> items;
		for ( UWidget child : children )
		{
			UAS_PriorityMessageListItem item = Cast<UAS_PriorityMessageListItem>( child );
			if ( IsValid( item ) )
				items.Add( item );
		}

		return items;
	}

	void AddListItem( UAS_PriorityMessageListData Data, bool SetPriority )
	{
		UAS_PriorityMessageListItem NewListItem = __CreateNewListItem( Data );
		ListMessageBox.AddChild( NewListItem );

		int newPriorityIndex   = SetPriority ? ListMessageBox.GetChildrenCount() - 1 : ListItemPriorityIndex;
		float newItemAnimDelay = 0.1;
		__PostListItemAdd( NewListItem, newPriorityIndex, newItemAnimDelay );
	}

	void InsertListItem( UAS_PriorityMessageListData Data, bool SetPriority, int index = 0 )
	{
		UAS_PriorityMessageListItem NewListItem = __CreateNewListItem( Data );

		TArray<UWidget> children = ListMessageBox.GetAllChildren();
		if ( ListItemPriorityIndex >= index )
			ListItemPriorityIndex++;

		for ( int i = 0; i < children.Num(); i++ )
			ListMessageBox.RemoveChildAt( 0 );

		for ( int i = 0; i < children.Num(); i++ )
		{
			if ( index == i )
				ListMessageBox.AddChild( NewListItem );

			ListMessageBox.AddChild( children[i] );
		}

		int newPriorityIndex   = SetPriority ? index : ListItemPriorityIndex;
		float newItemAnimDelay = 0.1;
		__PostListItemAdd( NewListItem, newPriorityIndex, newItemAnimDelay );
	}

	private void __PostListItemAdd( UAS_PriorityMessageListItem NewListItem, int newPriorityIndex = -1, float animDelay = 0.0 )
	{
		if ( ListMessageBox.GetChildrenCount() == 1 )
			SetListItemPriorityByIndex( 0 );
		else if ( newPriorityIndex != -1 && newPriorityIndex != ListItemPriorityIndex )
		{
			if ( animDelay > 0 )
				SetListItemPriorityByIndex_Delayed( animDelay, newPriorityIndex );
			else
				SetListItemPriorityByIndex( newPriorityIndex );
		}
	}

	void RemoveListItemAtIndex( int index )
	{
		ScriptAssert( index < ListMessageBox.GetChildrenCount(), "RemoveListItemAtIndex index greater than list count" );

		UAS_PriorityMessageListItem ListItem = Cast<UAS_PriorityMessageListItem>( ListMessageBox.GetChildAt( index ) );
		ListItem.Disappear();

		int lastIndex = ListMessageBox.GetChildrenCount() - 2;
		if ( ListItemPriorityIndex > lastIndex )
			SetListItemPriorityByIndex( lastIndex );
	}

	void UpdateListItemAtIndex( int index, UAS_PriorityMessageListData Data, bool SetPriority )
	{
		ScriptAssert( index < ListMessageBox.GetChildrenCount(), "UpdateListItemAtIndex index greater than list count" );

		UAS_PriorityMessageListItem ListItem = Cast<UAS_PriorityMessageListItem>( ListMessageBox.GetChildAt( index ) );

		// since this is only called from AddOrUpdateMessage - only update the item if the template is different... otherwise leave it be to update itself with OnCreate logic
		bool differentCurrentTemplate = ListItem.listItemData.ListTemplate != Data.ListTemplate;
		bool differentUpdateTemplate  = ListItem.IsUpdating() && ListItem.GetUpdateData().ListTemplate != Data.ListTemplate;
		if ( differentCurrentTemplate || differentUpdateTemplate )
			ListItem.UpdateListItem( Data );
		if ( SetPriority )
			SetListItemPriorityByIndex( index );
	}

	/****************************************************************\

	██   ██ ██  ██████  ██   ██ ██      ██  ██████  ██   ██ ████████
	██   ██ ██ ██       ██   ██ ██      ██ ██       ██   ██    ██
	███████ ██ ██   ███ ███████ ██      ██ ██   ███ ███████    ██
	██   ██ ██ ██    ██ ██   ██ ██      ██ ██    ██ ██   ██    ██
	██   ██ ██  ██████  ██   ██ ███████ ██  ██████  ██   ██    ██

	\****************************************************************/
	UFUNCTION( BlueprintOverride )
	void Tick( FGeometry MyGeometry, float InDeltaTime )
	{
		ScriptAssert( ListItemPriorityIndex < ListMessageBox.GetChildrenCount(), "ListItemPriorityIndex greater than list count" );

		if ( ListMessageBox.GetChildrenCount() == 0 || ListItemPriorityIndex < 0 )
			return;

		/*	make sure to not check to see if settings NEED to be updated before setting them.
			there is a large performance gain from not redrawing a widget every frame.		*/

		FMargin newPadding = GetHighlightPadding();
		UOverlaySlot slot  = Cast<UOverlaySlot>( Highlight_SizeBox.Slot );
		if ( newPadding != slot.Padding )
		{
			slot.SetPadding( newPadding );
		}

		float32 newHeight = GetHighlightHeight();
		if ( newHeight != Highlight_SizeBox.HeightOverride )
		{
			Highlight_SizeBox.SetHeightOverride( newHeight );
		}

		FLinearColor highLightColor = GetHighlightColor();
		if ( highLightColor != Get_BGColorCTAMessage() )
		{
			Set_BGColorCTAMessage( highLightColor );
		}
	}

	void SetListItemPriorityByIndex_Delayed( float delay, int index )
	{
		Thread( this, n"SetListItemPriorityByIndexThread", delay, index );
	}

	void SetListItemPriorityByIndex( int index )
	{
		Thread( this, n"SetListItemPriorityByIndexThread", 0, index );
	}

	UFUNCTION()
	private void SetListItemPriorityByIndexThread( UNCCoroutine co, float delay, int index )
	{
		if ( delay > 0 )
			co.Wait( delay );

		HighlightChangeOldHeight   = GetHighlightHeight();
		HighLightChangeOldPadding  = GetHighlightPadding();
		HightlightChangeColor	   = GetHighlightColor();
		HighLightChangeStartTimeMS = GetGameTimeMS();

		ListItemPriorityIndex = index;

		TArray<UWidget> children = ListMessageBox.GetAllChildren();
		for ( int i = 0; i < children.Num(); i++ )
		{
			UAS_PriorityMessageListItem ListItem = Cast<UAS_PriorityMessageListItem>( children[i] );
			if ( i == index )
				ListItem.Prioritize();
			else
				ListItem.Deprioritize();
		}
	}

	FLinearColor GetHighlightColor()
	{
		if ( ListItemPriorityIndex < 0 )
			return Get_BGColorCTAMessage();

		UAS_PriorityMessageListItem ListItem = Cast<UAS_PriorityMessageListItem>( ListMessageBox.GetChildAt( ListItemPriorityIndex ) );

		EPriorityMessageTheme Theme = ListItem.listItemData.Theme;
		if ( Theme == EPriorityMessageTheme::USEPARENT )
			Theme = messageData.theme;

		FLinearColor goal = GetPriorityMessageThemeColors( Theme ).CTA_BG;
		float Alpha		  = GetHighlightLerpAlpha();

		return Math::Lerp( HightlightChangeColor, goal, Alpha );
	}

	FMargin GetHighlightPadding()
	{
		float goalTop = 0;
		for ( int i = 0; i < ListItemPriorityIndex; i++ )
		{
			UAS_PriorityMessageListItem ListItem = Cast<UAS_PriorityMessageListItem>( ListMessageBox.GetChildAt( i ) );
			goalTop += ListItem.GetDesiredSize().Y;
		}

		float Alpha	 = GetHighlightLerpAlpha();
		float curTop = Math::Lerp( HighLightChangeOldPadding.Top, goalTop, Alpha );

		return FMargin( 0, curTop, 0, 0 );
	}

	float32 GetHighlightHeight()
	{
		if ( ListItemPriorityIndex < 0 )
			return 0;

		UAS_PriorityMessageListItem ListItem = Cast<UAS_PriorityMessageListItem>( ListMessageBox.GetChildAt( ListItemPriorityIndex ) );
		float goal							 = ListItem.GetDesiredSize().Y;
		float Alpha							 = GetHighlightLerpAlpha();

		return float32( Math::Lerp( HighlightChangeOldHeight, goal, Alpha ) );
	}

	float GetHighlightLerpAlpha()
	{
		float ElapsedTime = TO_SECONDS( GetGameTimeMS() - HighLightChangeStartTimeMS );

		return Math::GetMappedRangeValueClamped( FVector2D( 0, HIGHLIGHT_UPDATE_TIME ), FVector2D( 0, 1 ), ElapsedTime );
	}
}