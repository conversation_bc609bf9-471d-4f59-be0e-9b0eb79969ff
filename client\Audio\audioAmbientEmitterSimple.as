class AAS_NCAudioAmbientEmitterSimple : AAS_ClientSideActor
{
	UPROPERTY( DefaultComponent, RootComponent )
	USceneComponent root;

#if EDITOR
	UPROPERTY( DefaultComponent )
	UBillboardComponent billboard;
	default billboard.bIsEditorOnly = true;
	default billboard.bHiddenInGame = true;
#endif

	// NCAudio Asset
	UPROPERTY( EditAnywhere )
	UNCAudioAsset audioEvent;

	// Audio Event Vars
	int eventID = 0;

	// Fire and Forget Sound | Non Looping
	UPROPERTY( EditAnywhere )
	bool bOneShotSound = false;

	// Defaults
	default ActorHiddenInGame = true;
	default bNetLoadOnClient  = true;
	default bCanBeDamaged	  = false;

	// Set to false to be used with Ambient Emitter Simple Controller
	UPROPERTY( EditAnywhere )
	bool bAutoStart = true;

	// Displays a Debug Sphere and String of NCAudio Asset at its playing location | EDITOR ONLY
	UPROPERTY( EditAnywhere )
	bool debugDraw = false;

	UFUNCTION( BlueprintOverride )
	void ClientBeginPlay()
	{
		if ( bAutoStart )
		{
			StartEmitter();
		}
	}

	UFUNCTION()
	void StartEmitter()
	{
		if ( !IsClient() || eventID != 0 )
			return;

		FAudioResultData resultData = Client_EmitSoundOnEntity( audioEvent, this );
		eventID						= resultData.EventID;

#if EDITOR
		if ( debugDraw )
		{
			DrawDebugSphere( GetActorLocation(), 100, 5, FLinearColor::Blue, 2.0 );
			DrawDebugString( GetActorLocation() + FVector( 0, 0, 20 ), audioEvent.ToString(), 5, FLinearColor::Yellow );
		}
#endif
	}

	UFUNCTION()
	void StopEmitter()
	{
		if ( !IsClient() || eventID == 0 )
			return;

		if ( !bOneShotSound )
		{
			Client_StopSound( eventID );
			eventID = 0;
		}
		else
		{
			eventID = 0;
		}

#if EDITOR
		if ( debugDraw && !bOneShotSound )
		{
			DrawDebugSphere( GetActorLocation(), 100, 3, FLinearColor::Red, 2.0 );
			DrawDebugString( GetActorLocation() + FVector( 0, 0, 20 ), audioEvent.ToString(), 3, FLinearColor::Green );
		}
#endif
	}

	UFUNCTION( BlueprintOverride )
	void EndPlay( EEndPlayReason EndPlayReason )
	{
		Super::EndPlay( EndPlayReason );
		if ( IsClient() )
		{
			Client_StopSound( eventID );
		}
	}
}
