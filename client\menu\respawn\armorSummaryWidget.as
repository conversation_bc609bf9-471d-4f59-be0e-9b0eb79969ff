UCLASS( Abstract )
class UAS_ArmorSummaryWidget : UNC_DisplayWidget
{
	default StateOnConstruction = EDisplayConstructVisibility::Hidden;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_CommonTextBlock statusText;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_CommonTextBlock currentArmorCount;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage currentArmorImage;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_CommonTextBlock downgradedArmorCount;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage downgradedArmorImage;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UOverlay currentArmorOverlay;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UOverlay downgradedOverlay;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UOverlay trinketOverlay;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_EquipmentStatusWidget equipmentTrinket;

	UPROPERTY( NotVisible, BlueprintHidden, Transient, Meta = ( BindWidgetAnim ) )
	private UWidgetAnimation downgradedAnimation;

	UFUNCTION()
	void Update( float initialShowDelay )
	{
		// First we need to determine the state of the player when they were killed
		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( IsValid( player ) )
		{
			int blueArmor					   = player.CountItemsInAllBackpacks( GameplayTags::Loot_Armor_Level2 );
			int purpleArmor					   = player.CountItemsInAllBackpacks( GameplayTags::Loot_Armor_Level3 );
			int orangeArmor					   = player.CountItemsInAllBackpacks( GameplayTags::Loot_Armor_Level4 );
			int armorCountToUse;
			if ( orangeArmor > 1 )
				armorCountToUse = orangeArmor -1;
			else if ( purpleArmor > 1 )
				armorCountToUse = purpleArmor - 1;
			else
				armorCountToUse = blueArmor - 1;
			
			FPlayerEquipmentData trinket	   = player.GetTrinket();
			FShieldItemData equippedShieldData = player.GetEquippedShieldData();

			FString statusId;
			TArray<FFormatArgumentValue> args;

			if ( orangeArmor == 0 && purpleArmor == 0 && blueArmor == 0 )
			{
				// If the player is out of upgraded shield, we prompt them to upgrade elsewhere, so just early out
				return;
			}

			if ( trinket.IsValid() && trinket.lootTag == GameplayTags::Loot_Equipment_Trinket_Armorer )
			{
				// The player has an armorer trinket, their armor is safe
				statusId = "armor_status_header_protected";

				// Show the trinket
				equipmentTrinket.SetEquipmentInfo( trinket );
				SetWidgetVisibilitySafe( trinketOverlay, ESlateVisibility::HitTestInvisible );
			}
			else if ( orangeArmor > 1 || purpleArmor > 1 || blueArmor > 1  )
			{
				// If the player has more than one upgraded shield, we show how many shields are left (after respawn)
				args.Add( FFormatArgumentValue( armorCountToUse ) );
				statusId = "armor_status_header_count";
			}
			else
			{
				// Otherwise, we want to show that the player is downgrading
				statusId = "armor_status_header";

				// First we need to find what shield they are downgrading to
				FGameplayTag downgradedShield;
				if ( equippedShieldData.gameplayTag == GameplayTags::Loot_Armor_Level4 && purpleArmor > 0 )
				{
					downgradedShield = GameplayTags::Loot_Armor_Level3;
				}
				else if ( equippedShieldData.gameplayTag == GameplayTags::Loot_Armor_Level3 && blueArmor > 0 )
				{
					downgradedShield = GameplayTags::Loot_Armor_Level2;
				}
				else
				{
					downgradedShield = GameplayTags::Loot_Armor_Level1;
				}

				FShieldItemData downgradedShieldData = GetShieldDataForTag( downgradedShield );

				// Show the downgraded count or nothing for blue shields
				FText downgradedText = downgradedShield != GameplayTags::Loot_Armor_Level1 ? FText::AsNumber( player.CountItemsInAllBackpacks( downgradedShield ), GetDefaultNumberFormattingOptionsWithGrouping() ) :
																							 Text::EmptyText;

				// Finally, set the text, the color, and show the overlay
				downgradedArmorCount.SetText( downgradedText );
				downgradedArmorImage.SetColorAndOpacity( downgradedShieldData.GetShieldColor() );
				SetWidgetVisibilitySafe( downgradedOverlay, ESlateVisibility::HitTestInvisible );

				// Play the animation for downgraded armor
				PlayAnimationForward( downgradedAnimation );
			}

			// Show the current armor count if there is armor to show
			currentArmorCount.SetText( armorCountToUse > 0 ? FText::AsNumber( armorCountToUse, GetDefaultNumberFormattingOptionsWithGrouping() ) : Text::EmptyText );

			// Always tint the current armor image
			currentArmorImage.SetColorAndOpacity( equippedShieldData.GetShieldColor() );
			SetWidgetVisibilitySafe( currentArmorOverlay, ESlateVisibility::HitTestInvisible );

			// Set the player facing text
			statusText.SetText( statusId.IsEmpty() ? Text::EmptyText : GetLocalizedText( Localization::Respawn, statusId, args ) );
			Thread( this, n"ShowDelayed", initialShowDelay );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void ShowDelayed( UNCCoroutine co, float initialShowDelay )
	{
		co.Wait( initialShowDelay );
		Show();
	}
}