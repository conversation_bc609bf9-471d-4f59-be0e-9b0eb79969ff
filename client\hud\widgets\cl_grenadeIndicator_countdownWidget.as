UCLASS(Abstract)
class UAS_GrenadeIndicator_CountdownWidget : UUserWidgetDefault
{
    UPROPERTY( BlueprintReadOnly, Meta=(BindWidget) )
    private UTextBlock distanceText;

    private float maxDist = 0.0;
    private float minDist = 0.0;
    private FLinearColor minDistColor;
    private FLinearColor maxDistColor;

    void Initialize( float inMinDistance, float inMaxDistance, FLinearColor inMinDistColor, FLinearColor inMaxDistColor )
    {
        maxDist = inMaxDistance;
        minDist = inMinDistance;
        
        minDistColor = inMinDistColor;
        maxDistColor = inMaxDistColor;
    }

    void SetDistance( float newDistance )
    {
        distanceText.SetText( GetFormattedDistance( newDistance ) );

        float distFrac = Math::GetMappedRangeValueClamped( FVector2D( minDist, maxDist ), FVector2D( 1, 0 ), newDistance );
		float distFracAlpha = Math::GetMappedRangeValueClamped( FVector2D( 0.2, 0 ), FVector2D( 1, 0 ), distFrac );
		float adsFrac = 1.0;
		ANCPlayerCharacter localPawn = Client_GetLocalPawn();
		if ( IsValid( localPawn ) )
		{
			adsFrac = 1.0 - ( localPawn.AdsFraction * 2 );
		}

        FLinearColor textColor = FLinearColor::LerpUsingHSV( minDistColor, maxDistColor, distFrac );
		textColor.A = distFracAlpha * adsFrac;
        FSlateColor curColor = distanceText.GetColorAndOpacity();
        curColor.SpecifiedColor = textColor;
        distanceText.SetColorAndOpacity( curColor );
    }
}