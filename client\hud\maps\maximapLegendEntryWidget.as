UCLASS( Abstract )
class UAS_MaximapLegendEntryWidget : UNCGenericListEntry
{
	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_CommonTextBlock text;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage indicator;

	private UMaterialInstanceDynamic markerMaterial;

	UFUNCTION( BlueprintOverride )
	void OnDataSet( UObject dataObject )
	{
		UAS_MaximapLegendObject legendObject = Cast<UAS_MaximapLegendObject>( dataObject );
		if ( IsValid( legendObject ) )
		{
			FMapElementWidgetData elementData = legendObject.widgetData;
			if ( IsValid( elementData.baseData.MarkerMaterial ) )
			{
				markerMaterial = Material::CreateDynamicMaterialInstance( elementData.baseData.MarkerMaterial );
				if ( IsValid( markerMaterial ) )
				{
					// We want to bypass the size of the icon for the legend so we can show a larger icon
					markerMaterial.SetScalarParameterValue( n"IconSizeX", elementData.legendIconSize.X );
					markerMaterial.SetScalarParameterValue( n"IconSizeY", elementData.legendIconSize.Y );
					indicator.SetBrushFromMaterial( markerMaterial );
				}
			}

			text.SetText( legendObject.widgetData.legendText );
		}
	}
}