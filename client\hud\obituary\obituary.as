namespace Obituary
{
	const FString GENERIC_ = f"";

	const FString PING_EVENT_PLAYER	  = "ping_event_player";
	const FString PING_EVENT_LOCATION = "ping_event_location";
	const FString PING_EVENT_LOOT	  = "ping_event_loot";

	const FString NETWORK_EVENT_DISCONNECT = "network_event_disconnect";

	const FString GAME_EVENT_SHIELDBREAKER_PICKED_UP	 = "game_event_shieldbreaker_picked_up";
	const FString GAME_EVENT_LOOT_UPGRADE				 = "game_event_loot_upgrade";
	const FString GAME_EVENT_PLAYER_KILLED_PLAYER		 = "game_event_player_killed_player";
	const FString GAME_EVENT_PLAYER_KILLED_PLAYER_WEAPON = "game_event_player_killed_player_weapon";
	const FString GAME_EVENT_LOOT_PICKED_UP				 = "game_event_loot_picked_up";
	const FString GAME_EVENT_PLAYER_KILLED_SELF			 = "game_event_player_killed_self";

	const FString RAID_MODE_RAID_EVENT_PLANT  = "raid_mode_raid_event_plant";
	const FString RAID_MODE_RAID_EVENT_DEFUSE = "raid_mode_raid_event_defuse";

	const FString RAID_MODE_GAME_EVENT_SCORE_CHANGED_FRIENDLY = "raid_mode_game_event_score_changed_friendly";
	const FString RAID_MODE_GAME_EVENT_SCORE_CHANGED_ENEMY	  = "raid_mode_game_event_score_changed_enemy";
	const FString RAID_MODE_GAME_EVENT_CARE_PACKAGE_INCOMING  = "raid_mode_game_event_care_package_incoming";
	const FString RAID_MODE_GAME_EVENT_CARE_PACKAGE_LANDED	  = "raid_mode_game_event_care_package_landed";
	const FString RAID_MODE_GAME_EVENT_CARE_PACKAGE_OPENED	  = "raid_mode_game_event_care_package_opened";

	const FString GOLD_RUSH_GAME_EVENT_COURIER_KILL			   = "gold_rush_game_event_courier_kill";
	const FString GOLD_RUSH_GAME_EVENT_DEPOSIT_STARTED_DEFEND  = "gold_rush_game_event_deposit_started_defend";
	const FString GOLD_RUSH_GAME_EVENT_DEPOSIT_STARTED_CONTEST = "gold_rush_game_event_deposit_started_contest";
	const FString GOLD_RUSH_GAME_EVENT_COURIER_COMPLETE		   = "gold_rush_game_event_courier_complete";
	const FString GOLD_RUSH_GAME_EVENT_COURIER_FAIL			   = "gold_rush_game_event_courier_fail";
	const FString GOLD_RUSH_GAME_EVENT_COURIER_DEPOSIT		   = "gold_rush_game_event_courier_deposit";
	const FString GOLD_RUSH_GAME_EVENT_RAID_RESULTS_DEFENDER   = "gold_rush_game_event_raid_results_defender";
	const FString GOLD_RUSH_GAME_EVENT_RAID_RESULTS_ATTACKER   = "gold_rush_game_event_raid_results_attacker";
	const FString GOLD_RUSH_GAME_EVENT_SHIELDBREAKER_CRAFTING  = "gold_rush_game_event_shieldbreaker_crafting";
	const FString GOLD_RUSH_GAME_EVENT_SHIELDBREAKER_SPAWNED   = "gold_rush_game_event_shieldbreaker_spawned";
	const FString GOLD_RUSH_GAME_EVENT_SHIELDBREAKER_CAPTURED  = "gold_rush_game_event_shieldbreaker_captured";
	const FString GOLD_RUSH_GAME_EVENT_COURIERS_SPAWNED		   = "gold_rush_game_event_couriers_spawned";
	const FString GOLD_RUSH_GAME_EVENT_COURIERS_PRE_SPAWN	   = "gold_rush_game_event_couriers_pre_spawn";
	const FString GOLD_RUSH_GAME_EVENT_PLAYER_KILLED_PLAYER	   = "gold_rush_game_event_player_killed_player";

	TArray<FText> GetAllDebugObituaryText()
	{
		// Fake args
		FFormatArgumentValue player1		= FFormatArgumentValue( Localization::GetUnlocalizedTextFromString( f"Player1" ) );
		FFormatArgumentValue player2		= FFormatArgumentValue( Localization::GetUnlocalizedTextFromString( f"Player2" ) );
		FFormatArgumentValue loot			= FFormatArgumentValue( Localization::GetUnlocalizedTextFromString( f"Loot" ) );
		FFormatArgumentValue enemy			= FFormatArgumentValue( FText::FromName( LocColors::ENEMY ) );
		FFormatArgumentValue rareColor		= FFormatArgumentValue( FText::FromName( LocColors::RARITY_RARE ) );
		FFormatArgumentValue rareText		= FFormatArgumentValue( Localization::GetUnlocalizedTextFromString( "Rare" ) );
		FFormatArgumentValue teammate		= FFormatArgumentValue( FText::FromName( SafeColors::PLAYER_INDICATOR_TEAMMATE_ONE ) );
		FFormatArgumentValue smallNumber	= FFormatArgumentValue( 8 );
		FFormatArgumentValue bigNumber		= FFormatArgumentValue( 88 );
		FFormatArgumentValue negativeNumber = FFormatArgumentValue( -88 );
		FFormatArgumentValue weapon			= FFormatArgumentValue( Localization::GetUnlocalizedTextFromString( Glyphs::MCODE_DOT_D ) );

		// Raid mode
		FFormatArgumentValue objective			= FFormatArgumentValue( Localization::GetUnlocalizedTextFromString( Glyphs::VAULT ) );
		FFormatArgumentValue shieldbreakerPlant = FFormatArgumentValue( GetLocalizedText( Localization::Raid, "raid_breaker_planted" ) );

		// Gold rush
		FFormatArgumentValue goldRushAllied	  = FFormatArgumentValue( GetLocalizedText( Localization::GoldRush, "obit_team_text_allied" ) );
		FFormatArgumentValue goldRushEnemy	  = FFormatArgumentValue( GetLocalizedText( Localization::GoldRush, "obit_team_text_enemy" ) );
		FFormatArgumentValue goldRushLocation = FFormatArgumentValue( GetLocalizedText( Localization::GoldRush, "location_a" ) );

		TArray<FText> results;

		results.Add( GetLocalizedText( Localization::Obituary, PING_EVENT_PLAYER, teammate, player1, enemy, player2 ) );
		results.Add( GetLocalizedText( Localization::Obituary, PING_EVENT_LOOT, teammate, player1, rareColor, loot ) );
		results.Add( GetLocalizedText( Localization::Obituary, PING_EVENT_LOCATION, teammate, player1 ) );
		results.Add( GetLocalizedText( Localization::Obituary, NETWORK_EVENT_DISCONNECT, enemy, player2 ) );
		results.Add( GetLocalizedText( Localization::Obituary, GAME_EVENT_SHIELDBREAKER_PICKED_UP, teammate, player1 ) );
		results.Add( GetLocalizedText( Localization::Obituary, GAME_EVENT_LOOT_UPGRADE, rareColor, rareText ) );
		results.Add( GetLocalizedText( Localization::Obituary, GAME_EVENT_PLAYER_KILLED_PLAYER, teammate, player1, enemy, player2 ) );
		results.Add( GetLocalizedText( Localization::Obituary, GAME_EVENT_PLAYER_KILLED_PLAYER_WEAPON, teammate, player1, weapon, enemy, player2 ) );
		results.Add( GetLocalizedText( Localization::Obituary, GAME_EVENT_PLAYER_KILLED_SELF, teammate, player1 ) );

		// Raid mode
		results.Add( GetLocalizedText( Localization::Obituary, RAID_MODE_RAID_EVENT_PLANT, enemy, player2, objective ) );
		results.Add( GetLocalizedText( Localization::Obituary, RAID_MODE_RAID_EVENT_DEFUSE, teammate, player1, objective ) );
		results.Add( GetLocalizedText( Localization::Obituary, RAID_MODE_GAME_EVENT_SCORE_CHANGED_FRIENDLY, shieldbreakerPlant, negativeNumber ) );
		results.Add( GetLocalizedText( Localization::Obituary, RAID_MODE_GAME_EVENT_SCORE_CHANGED_ENEMY, shieldbreakerPlant, negativeNumber ) );
		results.Add( GetLocalizedText( Localization::Obituary, RAID_MODE_GAME_EVENT_CARE_PACKAGE_INCOMING ) );
		results.Add( GetLocalizedText( Localization::Obituary, RAID_MODE_GAME_EVENT_CARE_PACKAGE_LANDED ) );
		results.Add( GetLocalizedText( Localization::Obituary, RAID_MODE_GAME_EVENT_CARE_PACKAGE_OPENED, enemy, player2 ) );
		results.Add( GetLocalizedText( Localization::Obituary, GAME_EVENT_LOOT_PICKED_UP, objective, smallNumber, bigNumber ) );

		// Gold rush
		results.Add( GetLocalizedText( Localization::Obituary, GOLD_RUSH_GAME_EVENT_COURIER_KILL, goldRushEnemy, goldRushLocation ) );
		results.Add( GetLocalizedText( Localization::Obituary, GOLD_RUSH_GAME_EVENT_DEPOSIT_STARTED_DEFEND, goldRushLocation ) );
		results.Add( GetLocalizedText( Localization::Obituary, GOLD_RUSH_GAME_EVENT_DEPOSIT_STARTED_CONTEST, goldRushLocation ) );
		results.Add( GetLocalizedText( Localization::Obituary, GOLD_RUSH_GAME_EVENT_COURIER_COMPLETE, goldRushEnemy, bigNumber, goldRushLocation ) );
		results.Add( GetLocalizedText( Localization::Obituary, GOLD_RUSH_GAME_EVENT_COURIER_FAIL, goldRushAllied, goldRushLocation ) );
		results.Add( GetLocalizedText( Localization::Obituary, GOLD_RUSH_GAME_EVENT_COURIER_DEPOSIT, goldRushAllied, bigNumber, goldRushLocation ) );
		results.Add( GetLocalizedText( Localization::Obituary, GOLD_RUSH_GAME_EVENT_RAID_RESULTS_DEFENDER, bigNumber ) );
		results.Add( GetLocalizedText( Localization::Obituary, GOLD_RUSH_GAME_EVENT_RAID_RESULTS_ATTACKER, bigNumber ) );
		results.Add( GetLocalizedText( Localization::Obituary, GOLD_RUSH_GAME_EVENT_SHIELDBREAKER_CRAFTING ) );
		results.Add( GetLocalizedText( Localization::Obituary, GOLD_RUSH_GAME_EVENT_SHIELDBREAKER_SPAWNED ) );
		results.Add( GetLocalizedText( Localization::Obituary, GOLD_RUSH_GAME_EVENT_SHIELDBREAKER_CAPTURED ) );
		results.Add( GetLocalizedText( Localization::Obituary, GOLD_RUSH_GAME_EVENT_COURIERS_SPAWNED ) );
		results.Add( GetLocalizedText( Localization::Obituary, GOLD_RUSH_GAME_EVENT_COURIERS_PRE_SPAWN ) );

		return results;
	}
}