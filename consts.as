namespace GlobalHUD
{
	const FLinearColor ShadowColor		  = FLinearColor( 0, 0, 0, 0.35 );
	const FLinearColor DisabledColor	  = FLinearColor( 0.3, 0.3, 0.3, 1.0 );
	const FLinearColor UncraftableColor	  = FLinearColor( 0.6, 0.04, 0.00, 0.5 );
	const FLinearColor GlobalAltBlurColor = FLinearColor( 0.25, 0.25, 0.25, 0.5 );
	const FLinearColor GoodHealth		  = FLinearColor( 0.01, 0.6, 0.01, 1.0 );
	const FLinearColor MedHealth		  = FLinearColor( 0.75, 0.45, 0.00 );
	const FLinearColor BadHealth		  = FLinearColor( 0.6, 0.01, 0.01, 1.0 );
	const FLinearColor FriendlyColor	  = FLinearColor( 0.00, 0.72, 1.00 );
	const FLinearColor EnemyColor		  = FLinearColor( 1.00, 0.00, 0.00 );
	const FLinearColor NeutralColor		  = FLinearColor( 0.83, 0.83, 0.83 );
	const FLinearColor WhiteColor		  = FLinearColor( 1.0, 1.0, 1.0 );
	const FLinearColor DefaultUIBlueColor = FLinearColor( 0.00, 0.59, 0.73 );
	const FLinearColor BulletAmmoColor	  = FLinearColor( 0, 0.533276, 0.102242 );
	const FLinearColor HighcalAmmoColor	  = FLinearColor( 0.57758, 0.381326, 0.040915 );
	const FLinearColor SniperAmmoColor	  = FLinearColor( 0.090842, 0.152926, 0.274677 );
	const FLinearColor ShotgunAmmoColor	  = FLinearColor( 0.332452, 0.043735, 0.045186 );
	const FLinearColor RocketAmmoColor	  = FLinearColor( 0.138432, 0.238398, 0.001096 );
}

namespace GamePhase
{
	// Usage of RegisterGamePhase to assign the value allows us to interpret GamePhase values in C++.
	// Please let Jeremy know if the phase name strings or values change as code uses these values
	// to drive automatic hitch detection and trace captures during game phases of interest.

	const int WAITING_FOR_PLAYERS = UNCUtils::RegisterGamePhase( "WAITING_FOR_PLAYERS", 0 );
	const int CHARACTER_SELECT	  = UNCUtils::RegisterGamePhase( "CHARACTER_SELECT", 1 );
	const int BASE_SELECT		  = UNCUtils::RegisterGamePhase( "BASE_SELECT", 2 );
	const int MATCH_INTRO		  = UNCUtils::RegisterGamePhase( "MATCH_INTRO", 3 );
	const int PREMATCH			  = UNCUtils::RegisterGamePhase( "PREMATCH", 4 );
	const int PLAYING			  = UNCUtils::RegisterGamePhase( "PLAYING", 5 );
	const int WINNER_DETERMINED	  = UNCUtils::RegisterGamePhase( "WINNER_DETERMINED", 6 );
	const int EPILOGUE			  = UNCUtils::RegisterGamePhase( "EPILOGUE", 7 );
}

namespace GameConst
{
	const FName TELEPORT_HOME_ACTION = n"ScriptAction0";

	// kinda hacky
	const float32 WINNER_DETERMINED_CAMERA_SWAP_DELAY = 4.0;

	const bool KEEP_COOLDOWN_OVER_DEATH = true;

	const FGameplayTag DEFAULT_CLASS = GameplayTags::Classes_Class_Redmane;
	const FGameplayTag INVALID_CLASS = GameplayTags::Dev_Invalid;
	const FName DEV_SKIP_EVENT		 = n"SkipToNextPhase";

	const int ATTACK_DEFEND_MODE_MAX_PLAYERS	   = 8;
	const int ATTACK_DEFEND_MULTI_TEAM_MAX_PLAYERS = 16;

	const int RAID_MODE_PLAYERS_PER_TEAM			 = 3;
	const int RAID_MODE_PREMATCH_DURATION			 = 60;
	const float32 RAID_MODE_REVEAL_ENEMIES_COUNTDOWN = 10.0;

	const float32 RESPAWN_WAVE_TIME			= 20.0;
	const float32 RESPAWN_RAID_WAVE_TIME	= 30.0;
	const float32 RESPAWN_WAVE_TIME_MINIMUM = 10.0;

	const float32 PLACEMENT_DISTANCE_FROM_DOME_SHIELDBREAKER = 1500.0f;
	const float32 PLACEMENT_DISTANCE_FROM_DOME_EXTENDER		 = 700.0f;

	const float32 RESPAWN_REGEN_TIME			= 15.0;
	const float32 RESPAWN_REGEN_TIME_BASE_DEATH = 30.0;
	const float32 BASE_DEATH_DISTANCE_CHECK		= 10000; // Old Version ~Shield Dome Size - New version 2x Shield Dome Size. Now only triggers if a raid is active and reduces respawn count.

	const int WEAPON_CONDITION_NONE = 0;
	const int LOOT_CONDITION_MAX	= 255;
	// This is reserved. AAS_LootEntity's condition netvar is limited to the number range [0, 256], so we reserve this to represent the "no condition" value of -1.
	const int LOOT_CONDITION_NETVAL_NO_CONDITION = 256;

	const bool PERSISTENCE_BUILDING_KEEP_IN_PLACE = false;

	const float32 COURIER_COOLDOWN_DURATION		= 900.0;
	const float32 COURIER_RECOVERY_CD_DURATION	= 720.0;
	const float32 CONTAINER_LONG_PRESS_DURATION = 1.5;
	
	const bool REGEN_ENABLED = true;

	const float32 REVIVE_TIME				= 5.0;
	const float32 BLEEDOUT_DURATION_SECONDS = 90.0;

	const FVector VECTOR_MAX = FVector( MAX_flt, MAX_flt, MAX_flt );

	const float LOOT_REPLENISH_DURATION	  = 5.0 * 60.0;

	const FVector INVALID_NETWORKED_POS	 = FVector( 12345, 12345, 12345 );
	const FRotator INVALID_NETWORKED_ROT = FRotator( 12345, 12345, 12345 );
	const float FLOATING_POINT_TOLERANCE = 0.001; // world positions only have 3 decimals of precision. This is 10 micrometers

	const int TEAM_BOX_STARTING_SLOTS				 = 16;
	const int TEAM_BOX_SLOT_UPGRADE_AMOUNT			 = 4;
	const int TEAM_BOX_STARTING_WEAPON_SLOTS		 = 8;
	const int TEAM_BOX_WEAPON_SLOT_UPGRADE_AMOUNT	 = 1;
	const int TEAM_BOX_STACK_SCALE					 = 4;
	const int TEAM_BOX_STARTING_RESOURCE_STACK_SCALE = 24;
	const int TEAM_BOX_RESOURCE_STACK_UPGRADE_AMOUNT = 2;
	const int TEAM_BOX_STARTING_HEAL_STACK_SCALE	 = 3;
	const int TEAM_BOX_HEAL_STACK_UPGRADE_AMOUNT	 = 1;
	const int TEAM_BOX_STARTING_AMMO_STACK_SCALE	 = 6;
	const int TEAM_BOX_AMMO_STACK_UPGRADE_AMOUNT	 = 1;

	const int BACKPACK_UPGRADE_01_AMOUNT			 = 3;
	const int BACKPACK_UPGRADE_02_AMOUNT			 = 3;
	const int BACKPACK_UPGRADE_03_AMOUNT			 = 6;
	const int BACKPACK_HEAL_STARTING_SLOTS			 = 4;
	const int BACKPACK_AMMO_STARTING_SLOTS			 = 4;
	const int BACKPACK_WEAPON_STARTING_SLOTS		 = 1;
	const int BACKPACK_WEAPON_SLOT_UPGRADE_AMOUNT	 = 1;
	const int BACKPACK_STARTING_RESOURCE_STACK_SCALE = 1;
	const int BACKPACK_RESOURCE_STACK_UPGRADE_AMOUNT = 1;
	const int BACKPACK_CLASS_STARTING_SLOTS			 = 1;

	const int MAX_ELEMENTS_IN_CUSTOM_SORT = 100;

	const int CRAFTING_SOURCE_DEFAULT = ( CraftingSource::BACKPACK | CraftingSource::PERSONAL_CHESTS | CraftingSource::TEAM_STORAGE );

	const int BASE_VOLUME_UPGRADE_01_AMOUNT = 3;
	const int BASE_VOLUME_UPGRADE_02_AMOUNT = 4;
	const int BASE_VOLUME_UPGRADE_03_AMOUNT = 6;

	const int TURN_IN_VALUE_RELIC			 = 1000.0f;
	const float32 TURN_IN_TECH_SCALAR		 = 0.75f;
	const float32 TURN_IN_BUILDING_SCALAR	 = 1.1f;
	const int MAX_CRYSTAL_SIZE_TURN_IN_VALUE = 5000;
	const int HEADWALL_XP_PER_KILL			 = 50;
	const int HEADWALL_XP_PER_QUEST			 = 300;
	const int HEADWALL_XP_PER_GATHER		 = 15; // Only awards when gathering gives a Wildlight

	const float32 RECYCLER_RECYCLE_TIME = 2.5f;

	const float32 HEAL_AREA_RADIUS		   = 450.0f;
	const float32 HEAL_AREA_DURATION	   = 80.0f;
	const float32 DEPLOYABLE_WALL_LIFETIME = 30.0f;

	const float32 RAID_GOLEM_STARTUP_TIME		  = 0.1;
	const float32 RAID_GOLEM_DISABLED_DEATH_TIMER = 60.0;
	const float RAID_BUILDING_RANGE				  = 8000;

	const float32 RAID_SHIELD_STRENGTH			 = 1000.0f;
	const float32 RAID_SHIELD_REGEN_PER_SECOND	 = 10.0f;
	const float32 RAID_SHIELD_GENERATOR_STRENGTH = 200.0f;

	// DOME SHIELD BREAKER
	const float32 BREAK_DOME_DISTANCE						 = 2000.0;
	const float32 BREAK_DOME_TIME							 = 30.0;
	const float32 VIABLE_DISTANCE_TO_ATTACK_WITH_NO_BREACHER = 10000; // this has to be significantly larger than 2500 since it has to account for the skydive

	const float32 BUILDING_VOLUME_WIDTH = 1600;

	const float32 RETURN_HOME_COOLDOWN_DURATION = 8.0;
	const float32 RETURN_HOME_QUICK_CD_DURATION = 3.0;

	const int HEADWALL_MAX_RANK		  = 100;
	const float32 PARTY_NEAR_DISTANCE = 5200.0;
	const float32 PARTY_FAR_DISTANCE  = 10400.0; // 300000 Distance at which the game considers a party member to far for combat notifications

	const FGameplayTag NO_SHIELD_TAG				= GameplayTags::Loot_Armor_Level0;
	const FGameplayTag DEFAULT_SHIELD_TAG			= GameplayTags::Loot_Armor_Level1;

	const float QUOTE_DEFAULT_HOLD			  = 2;
	const float QUOTE_DEFAULT_FADEIN		  = 1.0;
	const float QUOTE_DEFAULT_FADEOUT		  = 1.5;
	const float QUOTE_DEFAULT_AUTHOR_DELAY	  = QUOTE_DEFAULT_FADEIN + 2;
	const float SPEECH_BUBBLE_DEFAULT_TIMEOUT = 15;

	const int PROTO_EXPEDITION_INDEX	= 0;
	const int PROTO_RESERVED_TEAM_INDEX = 30;

	const int PERSISTENCE_VERSION = 6;

#if EDITOR
	const float PERSISTENCE_SAVE_INTERVAL = 5.0;
#else
	const float PERSISTENCE_SAVE_INTERVAL = 64.0;
#endif

	const float32 FOG_OF_WAR_DISCOVERY_DISTANCE	 = 15000;
	const float32 SCOUT_TOWER_DISCOVERY_DISTANCE = 45000;

	const float32 STRUCTURAL_MULTIPLIER_CANT_FIND_WEAPON = 6.0;

	const float32 CARE_PACKAGE_WARNING_TIME	   = 60.0;	// 60
	const float32 CARE_PACKAGE_WAVE_DELAY_TIME = 300.0; // 300

	////////// ABILITIES
	const float32 AUTOMATED_DRONE_RADIUS			   = 7000;
	const float32 AUTOMATED_DRONE_COOLDOWN_TIME		   = 60.0;
	const float32 AUTOMATED_DRONE_CHARGE_COOLDOWN_TIME = 12.0;

	const int MELEE_MULTI_TARGET_MAX		  = 5;
	const int MELEE_MULTI_TARGET_EX_METER_MAX = 5;
	const int GetMeleeMultiTargetExMeterMax( AAS_PlayerEntity player )
	{
		if ( !IsValid( player ) )
			return MELEE_MULTI_TARGET_EX_METER_MAX;

		if ( player.HasTrinketPassive( GameplayTags::Classes_Passives_AxePower ) )
			return Math::FloorToInt( player.GetTrinket().dataFloat2 );

		return MELEE_MULTI_TARGET_EX_METER_MAX;
	}

	const float32 MELEE_2NADE_HOLD_TIME = 1.5;

	const FGiveWeaponSettings DEFAULT_GIVE_WEAPON_SETTINGS;

	const int INDEX_NONE = -1;
	const int INVALID	 = -1;
	const int UNSET		 = -1;
	const int ZERO		 = 0;

	////////////// HUD
	const int ZORDER_HUD		  = 0;
	const int ZORDER_OVER_HUD	  = 10;
	const int ZORDER_SCREEN		  = 30; // DONT CHANGE, this is a reference to what is set in code
	const int ZORDER_FRAME		  = 35;
	const int ZORDER_RAID_HUD	  = 40;
	const int ZORDER_TRAINING_HUD = 50;
	const int ZORDER_SCREEN_FADE  = 100;

	const int FALSE_SWITCHER_INDEX = 0;
	const int TRUE_SWITCHER_INDEX  = 1;

	const FText FTEXT_EMPTY = Text::EmptyText;

	const FName RARITY_COMMON					 = n"rarity_common";
	const FName RARITY_COMMON_HIGHLIGHT			 = n"rarity_common_highlight";
	const FName RARITY_COMMON_SHIELDBREAK		 = n"rarity_common_shieldbreak";
	const FName RARITY_UNCOMMON					 = n"rarity_uncommon";
	const FName RARITY_UNCOMMON_HIGHLIGHT		 = n"rarity_uncommon_highlight";
	const FName RARITY_UNCOMMON_SHIELDBREAK		 = n"rarity_uncommon_shieldbreak";
	const FName RARITY_RARE						 = n"rarity_rare";
	const FName RARITY_RARE_HIGHLIGHT			 = n"rarity_rare_highlight";
	const FName RARITY_RARE_SHIELDBREAK			 = n"rarity_rare_shieldbreak";
	const FName RARITY_EXTRAORDINARY			 = n"rarity_extraordinary";
	const FName RARITY_EXTRAORDINARY_HIGHLIGHT	 = n"rarity_extraordinary_highlight";
	const FName RARITY_EXTRAORDINARY_SHIELDBREAK = n"rarity_extraordinary_shieldbreak";
	const FName RARITY_ARTIFACT					 = n"rarity_artifact";
	const FName RARITY_ARTIFACT_HIGHLIGHT		 = n"rarity_artifact_highlight";
	const FName RARITY_WORLD					 = n"rarity_world";

	const FName LOC_LOOTTYPE_AMMO				  = n"lootType_ammo";
	const FName LOC_LOOTTYPE_CLASSPLACEABLEITEM	  = n"lootType_classPlaceableItem";
	const FName LOC_LOOTTYPE_GENERICINVENTORYITEM = n"lootType_genericInventoryItem";
	const FName LOC_LOOTTYPE_GRENADE			  = n"lootType_grenade";
	const FName LOC_LOOTTYPE_HEALITEM			  = n"lootType_healItem";
	const FName LOC_LOOTTYPE_PLACEABLEITEM		  = n"lootType_placeableItem";
	const FName LOC_LOOTTYPE_PLAYERSHIELD		  = n"lootType_playerShield";
	const FName LOC_LOOTTYPE_PRIMARYWEAPON		  = n"lootType_primaryWeapon";
	const FName LOC_LOOTTYPE_RESOURCEITEM		  = n"lootType_resourceItem";
	const FName LOC_LOOTTYPE_SHIELDBREACHER		  = n"lootType_shieldBreacher";

	// This needs to match with Menu List array in MenuGlobals asset to associate with a WBP
	const FName MENU_NAME_INVENTORY = n"InventoryPanel";
	const FName MENU_NAME_VENDOR	= n"VendorMenu";

	const FName MONTAGE_SCRIPTED_SLOT1 = n"ScriptedDefault";
	const FName MONTAGE_SCRIPTED_SLOT2 = n"ScriptSlot";

	const float32 GRENADE_COOLDOWN_TIME = 45.0;

	// Amount of time to end base select early and hold on selection before transitioning to prematch
	const float32 BASE_SELECT_BASE_HOLD_TIME = 2.0f;

	const FName WEAP_MOD_CHARGED_CURRENT = n"chargedCurrent";

	const int INVALID_ENTITY_INDEX = -1;

	const FRotator MAYA_UNREAL_ROTATION_CONVERSION = FRotator( 0, 0, -90 );

	// can actually debug visualize in editor if it exists as a const
	const float32 SIEGE_TOWER_CAMERA_ARM_LENGTH = 12286.0f;
	const float32 SIEGE_TOWER_CAMERA_ARM_PITCH	= -23.5f;
	const float32 SIEGE_TOWER_CAMERA_CAM_PITCH	= -16.5f;

	// optimization to bring down shadow cost for tile invalidations
	const int32 RESOURCE_SHADOW_OFFSET_DISABLE_DIST									   = 16000;
	const EShadowCacheInvalidationBehavior RESOURCE_SHADOW_CACHE_INVALIDATION_BEHAVIOR = EShadowCacheInvalidationBehavior::Rigid;

	const FName RESOURCE_FXSCALE_PARAM	= n"GlobalScale";
	const FName RESOURCE_FXOFFSET_PARAM = n"GlobalOffset";
};

enum EOffhandSlot
{
	GRENADE,

	_count
}

namespace WeaponSlot
{
	const int PrimarySlot0				= EWeaponInventorySlot::PrimarySlot0;
	const int PrimarySlot1				= EWeaponInventorySlot::PrimarySlot1;
	const int MeleeSlot					= EWeaponInventorySlot::MeleeSlot;
	const int RaidToolsSlot				= EWeaponInventorySlot::OffhandSlot0;
	const int CustomBackpackGrenadeSlot = EWeaponInventorySlot::OffhandSlot1;
	const int GrenadeSlot				= EWeaponInventorySlot::SpecialSlot0;
	const int TacticalSlot				= EWeaponInventorySlot::SpecialSlot1;
	const int FREE_SLOT_0				= EWeaponInventorySlot::SpecialSlot2;
	const int RaidUltSlot				= EWeaponInventorySlot::SpecialSlot3;
	const int AbilityWeaponSwapSlot		= EWeaponInventorySlot::SpecialSlot4;
	const int AbilityWeaponSwapSlot2	= EWeaponInventorySlot::SpecialSlot5; // deprecated the mount as a weapon ... free'd up this slot
	const int FREE_SLOT_1				= EWeaponInventorySlot::SpecialSlot6; // deprecated the mount death package hack... free'd up this slot
	const int FREE_SLOT_2				= EWeaponInventorySlot::SpecialSlot7;
}

enum EWeaponSlot
{
	PrimarySlot0			  = EWeaponInventorySlot::PrimarySlot0,
	PrimarySlot1			  = EWeaponInventorySlot::PrimarySlot1,
	MeleeSlot				  = EWeaponInventorySlot::MeleeSlot,
	RaidToolsSlot			  = EWeaponInventorySlot::OffhandSlot0,
	CustomBackpackGrenadeSlot = EWeaponInventorySlot::OffhandSlot1,
	GrenadeSlot				  = EWeaponInventorySlot::SpecialSlot0,
	TacticalSlot			  = EWeaponInventorySlot::SpecialSlot1,
	FREE_SLOT_0				  = EWeaponInventorySlot::SpecialSlot2,
	RaidUltSlot				  = EWeaponInventorySlot::SpecialSlot3,
	AbilityWeaponSwapSlot	  = EWeaponInventorySlot::SpecialSlot4,
	AbilityWeaponSwapSlot2	  = EWeaponInventorySlot::SpecialSlot5, // deprecated the mount as a weapon ... free'd up this slot
	FREE_SLOT_1				  = EWeaponInventorySlot::SpecialSlot6, // deprecated the mount death package hack... free'd up this slot
	FREE_SLOT_2				  = EWeaponInventorySlot::SpecialSlot7,
}

namespace BuildToolFlags
{
	const int BTF_NONE		   = 0;
	const int BTF_USE_RAID_ULT = ( 1 << 0 );
}

namespace CraftingSource
{
	const int BACKPACK							= ( 1 << 0 );
	const int PERSONAL_CHESTS					= ( 1 << 1 );
	const int TEAM_STORAGE						= ( 1 << 2 );
	const int TEAM_QUEST_GIVER					= ( 1 << 3 );
	const int BACKPACK_ONLY_INVENTORY_RESOURCES = ( 1 << 4 );
};

enum ELootType
{
	PrimaryWeapon,
	Ammo,
	PlaceableItem,
	Grenade,
	GenericInventoryItem,
	ResourceItem,
	PlayerShield,
	HealItem,
	Recyclable,
	TraderItem,
	UpgradeItem,
	ClassPlaceableItem,
	RaidUltimate,
	RepairKit,
	ShieldBreacher,
	UltimateAccelerant,
	Equipment,
	GameModeResource,

	_count
}

enum ELootIconOverlay
{
	Generic,
	Combat,
	Placeable,
	SiegeWeapon,
	None,

	_count
}

namespace EDamageFlags
{
	/** !!! IMPORTANT !!!
	 * This namespace must be kept in sync with the corresponding code enum "EDamageFlags", found in NCUtils.h.
	 * This hack exists because Unreal only supports the uint8 type for enum classes, but we need to support more than 8 unique flags.
	 */
	const int DF_NONE							= 0;
	const int DF_HEADSHOT						= ( 1 << 0 );
	const int DF_KILLSHOT						= ( 1 << 1 );
	const int DF_FALLOFFDAMAGE					= ( 1 << 2 );
	const int DF_IGNORE_FRIENDLYFIRE_CHECKS		= ( 1 << 3 );
	const int DF_SHIELD_BREAK					= ( 1 << 4 );
	const int DF_FELL_OUT_OF_WORLD				= ( 1 << 5 );
	const int DF_KEEP_CONTENTS					= ( 1 << 6 );
	const int DF_BYPASS_DESTRUCTIBLE_PROTECTION = ( 1 << 7 );
	const int DF_NO_INTERRUPT_EXTENDED_USE		= ( 1 << 8 );
}

namespace EScriptDamageFlags
{
	const int DF_NONE				   = 0;
	const int DF_MELEE				   = ( 1 << 0 );
	const int DF_DOWNED				   = ( 1 << 1 );
	const int DF_FALL_DAMAGE		   = ( 1 << 2 );
	const int DF_ELECTRICAL			   = ( 1 << 3 );
	const int DF_NO_HIT_SLOW		   = ( 1 << 4 );
	const int DF_STRUCTURAL			   = ( 1 << 5 );
	const int DF_KNOCKBACK			   = ( 1 << 6 );
	const int DF_RESOURCE			   = ( 1 << 7 );
	const int DF_NO_DELAY_REGEN		   = ( 1 << 8 );
	const int DF_RESOURCE_NO_GIVE	   = ( 1 << 9 );
	const int DF_HARDENED			   = ( 1 << 10 );
	const int DF_EMP				   = ( 1 << 11 );
	const int DF_EXPLOSIVE			   = ( 1 << 12 );
	const int DF_HIDDEN_DAMAGE		   = ( 1 << 13 );
	const int DF_HARDENED_BREAK		   = ( 1 << 14 );
	const int DF_ON_FIRE			   = ( 1 << 15 );
	const int DF_NO_DAMAGE_ARROW	   = ( 1 << 16 );
	const int DF_CRUSH				   = ( 1 << 17 );
	const int DF_CHIP_DAMAGE		   = ( 1 << 18 ); // This could be removed if FDamageInfo included the victim
	const int DF_DAMAGE_TO_ABILITY	   = ( 1 << 19 );
	const int DF_HACK_SCARYDAMAGE	   = ( 1 << 21 ); // Workaround for NC1-15067
	const int DF_UNIQUE_DAMAGE_NUMBER  = ( 1 << 22 );
	const int DF_NO_DEGRADE_SHIELD	   = ( 1 << 23 );
	const int DF_INSTANT_KILL		   = ( 1 << 24 ); // Not yet set up for players, just target dummies
	const int DF_SOUL_BONDED   = ( 1 << 25 );

	UFUNCTION()
	void InitScriptDamageFlagNames( TArray<FString>& scriptDamageFlagNames )
	{
		scriptDamageFlagNames.Empty();

		// IMPORTANT: This must match the order in EScriptDamageFlags

		scriptDamageFlagNames.Add( "DF_MELEE" );
		scriptDamageFlagNames.Add( "DF_DOWNED" );
		scriptDamageFlagNames.Add( "DF_FALL_DAMAGE" );
		scriptDamageFlagNames.Add( "DF_ELECTRICAL" );
		scriptDamageFlagNames.Add( "DF_NO_HIT_SLOW" );
		scriptDamageFlagNames.Add( "DF_STRUCTURAL" );
		scriptDamageFlagNames.Add( "DF_KNOCKBACK" );
		scriptDamageFlagNames.Add( "DF_RESOURCE" );
		scriptDamageFlagNames.Add( "DF_NO_DELAY_REGEN" );
		scriptDamageFlagNames.Add( "DF_RESOURCE_NO_GIVE" );
		scriptDamageFlagNames.Add( "DF_HARDENED" );
		scriptDamageFlagNames.Add( "DF_EMP" );
		scriptDamageFlagNames.Add( "DF_EXPLOSIVE" );
		scriptDamageFlagNames.Add( "DF_HIDDEN_DAMAGE" );
		scriptDamageFlagNames.Add( "DF_HARDENED_BREAK" );
		scriptDamageFlagNames.Add( "DF_ON_FIRE" );
		scriptDamageFlagNames.Add( "DF_NO_DAMAGE_ARROW" );
		scriptDamageFlagNames.Add( "DF_CRUSH" );
		scriptDamageFlagNames.Add( "DF_CHIP_DAMAGE" );
		scriptDamageFlagNames.Add( "DF_DAMAGE_TO_ABILITY" );
		scriptDamageFlagNames.Add( "DF_HACK_SCARYDAMAGE" );
		scriptDamageFlagNames.Add( "DF_UNIQUE_DAMAGE_NUMBER" );
		scriptDamageFlagNames.Add( "DF_NO_DEGRADE_SHIELD" );
		scriptDamageFlagNames.Add( "DF_INSTANT_KILL" );
		scriptDamageFlagNames.Add( "DF_SOUL_BONDED" );
	}
}

enum EOmniToolIndex
{
	Small,
	Large,
	Sledgehammer,

	_count
}

enum EObjectiveSplashIcon
{
	DEFAULT,

	CRYSTAL_EXTRACTION_TEAMMATE_PORTAL,
	CRYSTAL_EXTRACTION_STARTED_FRIENDLY,
	CRYSTAL_EXTRACTION_STARTED_HOSTILE,
	CRYSTAL_EXTRACTION_STARTED_SELF,

	_count
}

enum EObjectiveSplashStyle
{
	NEUTRAL,
	FRIENDLY,
	HOSTILE,

	_count
}

enum EFootstepType
{
	Player,
	Mount,

	_count
}

namespace PlayerWidgetConst
{
	// Indicis used to determine showing the overshield or the shield
	const int SWICHER_SHIELDS	 = 0;
	const int SWICHER_OVERSHIELD = 1;

	// TODO: What's a good way to handle segments per health etc? Is there a HUD globals somewhere? Currently hardcoded based on material used
	const float32 HEALTH_PER_SEGMENT = 100.0f / 5.0f;

	// TODO @jmccarty: This var is currently set on a per widget basis, but could be a configurable value somewhere like HUD globals
	const float32 NUM_SHIELD_SEGMENTS = 5.0f;
}

namespace CycleDirection
{
	const int BACKWARD = -1;
	const int NONE	   = 0;
	const int FORWARD  = 1;
}

enum ESystemMessage
{
	DEFAULT,
	WARNING,
	ERROR,

	_count UMETA( Hidden )
}

enum ELobbyPlayer
{
	PlayerOne = 0,
	PlayerTwo,
	PlayerThree,

	_count UMETA( Hidden )
}

namespace ConnectionConst
{
	const ELobbyPlayer INVALID_LOBBY_PLAYER = ELobbyPlayer::_count;
}

enum EWarTableSocketIndex
{
	NONE,
	SOCKET_ONE,
	SOCKET_TWO,
	SOCKET_THREE,
	SOCKET_FOUR,
	SOCKET_FIVE,
	SOCKET_SIX,
	SOCKET_SEVEN,
	SOCKET_EIGHT,
	SOCKET_NINE,

	_count UMETA( Hidden )
}

enum EProgressionNavigatorState
{
	NONE,
	PREVIEWING,
	INSPECTING,

	_count UMETA( Hidden )
}

enum ERaidMessageType
{
	ATTACKER_WIN_RAID,
	ATTACKER_WIN_GAME,
	DEFENDER_WIN_RAID
}

enum EInterceptMessageType
{
	INACTION,
	ATTACKER_FAILED
}

// This enum defines the priority of material overrides used in script
// Higher values are higher priority, and will win out if multiple overrides are active
enum EMaterialOverridePriority
{
	NONE			= 0,
	SLADE_HOTSTREAK = 1,
	SKYE_INVIS		= 1,
	ZIPLINE			= 2,
}

namespace SafeColors
{
	const FLinearColor INVALID_SAFE_COLOR	  = FLinearColor( 1.0, 0.0, 1.0 );
	const FName PLAYER_INDICATOR_SELF		  = n"player_indicator_self";
	const FName PLAYER_INDICATOR_TEAMMATE_ONE = n"player_indicator_1";
	const FName PLAYER_INDICATOR_TEAMMATE_TWO = n"player_indicator_2";
	const FName PLAYER_INDICATOR_TEAMMATE_THREE = n"player_indicator_3";
	const FName PLAYER_INDICATOR_ENEMY		  = n"player_indicator_enemy";
	const FName PLAYER_INDICATOR_ANNOUNCER	  = n"player_indicator_announcer";
}

namespace RichText
{
	const FName RichTextTable = n"/Game/Localization/ST_Rich_Text.ST_Rich_Text";

	const FText RICH_TEXT_IMAGE_FORMAT				 = FText::FromStringTable( RichTextTable, "rich_text_image_format" );
	const FText RICH_TEXT_IMAGE_FORMAT_WITH_VARIABLE = FText::FromStringTable( RichTextTable, "rich_text_image_format_with_variable" );
	const FText BODY_TEXT_FORMAT					 = FText::FromStringTable( RichTextTable, "body_text_format" );
	const FText KEYBIND_TEXT_FORMAT					 = FText::FromStringTable( RichTextTable, "keybind_text_format" );

	const FName TELEPORT_HOME_RICH_TEXT_ID = n"teleport_home";

	enum ERichTextColor
	{
		FRIENDLY,
		ENEMY,
		SELF,
		UNCOMMON,
		RARE,
		EXTRAORDINARY,

		_count
	}
}

namespace MaterialParameter
{
	const FName LERP_ALPHA					= n"LerpAlpha";
	const FName STAGE_ALPHA					= n"StageAlpha";
	const FName FILL_COLOR					= n"FillColor";
	const FName PORTRAIT_TEXTURE			= n"PortraitTexture";
	const FName SHOW_SCALAR					= n"ShowScalar";
	const FName ALLOW_DAMAGE_TAIL_PARAMETER = n"AllowDamageTail";
	const FName DAMAGE_TAIL_LERP_ALPHA		= n"DamageTailLerpAlpha";
	const FName TEXTURE						= n"Texture";
	const FName SEGMENT_COUNT				= n"SegmentCount";
	const FName RARITY_COLOR				= n"RarityColor";
	const FName RARITY_HIGHLIGHT_COLOR		= n"RarityHighlightColor";
	const FName SHIELDBREAKER_PROGRESS		= n"ShieldbreakerProgress";
	const FName START_COLOR					= n"StartColor";
	const FName ACCENT_COLOR				= n"AccentColor";
	const FName IN_USE_STATE				= n"InUseState";
	const FName END_COLOR					= n"EndColor";

	const float GetTrueFalseFloat( bool evaluation )
	{
		return evaluation ? 1.0f : 0.0f;
	}
}

#if EDITOR
UCLASS( Config = Game, DefaultConfig )
class UAS_GameDefaults : UDeveloperSettings
{
}
#endif

namespace LobbyCharacterAnimNames
{
	// If you are adding a new name, make sure to add it to the getter below
	const FName INTRO				   = n"Intro";
	const FName PLAYER_VIGNETTE		   = n"PlayerVignette";
	const FName DEFAULT_IDLE		   = n"DefaultIdle";
	const FName PARTY_CHANGED_START_01 = n"PlayerChangedStart01";
	const FName PARTY_CHANGED_LOOP_01  = n"PlayerChangedLoop01";
	const FName PARTY_CHANGED_EXIT_01  = n"PlayerChangedExit01";
	const FName PARTY_CHANGED_START_02 = n"PlayerChangedStart02";
	const FName PARTY_CHANGED_LOOP_02  = n"PlayerChangedLoop02";
	const FName PARTY_CHANGED_EXIT_02  = n"PlayerChangedExit02";
	const FName QUEUEING_START		   = n"QueueingStart";
	const FName QUEUEING_IDLE		   = n"QueueingIdle";

	TArray<FName> GetLobbyCharacterAnimNames()
	{
		TArray<FName> results;

		results.Add( INTRO );
		results.Add( PLAYER_VIGNETTE );
		results.Add( DEFAULT_IDLE );
		results.Add( PARTY_CHANGED_START_01 );
		results.Add( PARTY_CHANGED_LOOP_01 );
		results.Add( PARTY_CHANGED_EXIT_01 );
		results.Add( PARTY_CHANGED_START_02 );
		results.Add( PARTY_CHANGED_LOOP_02 );
		results.Add( PARTY_CHANGED_EXIT_02 );
		results.Add( QUEUEING_START );
		results.Add( QUEUEING_IDLE );

		return results;
	}
}

namespace CollectionCharacterAnimNames
{
	// If you are adding a new name, make sure to add it to the getter below
	const FName DEFAULT_IDLE	= n"DefaultIdle";
	const FName EQUIP			= n"Equip";
	const FName EQUIP_IDLE		= n"EquipIdle";
	const FName INSPECT_IDLE	= n"InspectIdle";
	const FName INSPECT_SPRINT	= n"InspectSprint";
	const FName INSPECT_ULT		= n"InspectUlt";
	const FName INSPECT_POSE_01 = n"InspectPose01";
	const FName INSPECT_POSE_02 = n"InspectPose02";

	TArray<FName> GetCollectionCharacterAnimNames()
	{
		TArray<FName> results;

		results.Add( DEFAULT_IDLE );
		results.Add( EQUIP );
		results.Add( EQUIP_IDLE );
		results.Add( INSPECT_IDLE );
		results.Add( INSPECT_SPRINT );
		results.Add( INSPECT_ULT );
		results.Add( INSPECT_POSE_01 );
		results.Add( INSPECT_POSE_02 );

		return results;
	}
}

namespace StoreCharacterAnimNames
{
	// If you are adding a new name, make sure to add it to the getter below
	const FName DEFAULT_IDLE = n"DefaultIdle";

	TArray<FName> GetStoreCharacterAnimNames()
	{
		TArray<FName> results;

		results.Add( DEFAULT_IDLE );

		return results;
	}
}

namespace CharacterSelectCharacterAnimNames
{
	// If you are adding a new name, make sure to add it to the getter below
	const FName DEFAULT_IDLE   = n"DefaultIdle";
	const FName DEFAULT_SELECT = n"DefaultSelect";

	TArray<FName> GetCharacterSelectCharacterAnimNames()
	{
		TArray<FName> results;

		results.Add( DEFAULT_IDLE );
		results.Add( DEFAULT_SELECT );

		return results;
	}
}

namespace PriorityMessages
{
	const float PRIORITY_MESSAGE_OTHER_RIGHT_DISAPPEAR_DELAY = 15;
	const float PRIORITY_MESSAGE_DISAPPEAR_DELAY_RAID_END_V2 = 6;
}

namespace WeaponPickupFlags
{
	const int NONE			 = 1 << 0;
	const int NO_FIRSTDEPLOY = 1 << 1;
}

namespace ImpactConsts
{
	const FName HEAD_BONE						  = n"neck_head";
	const EPhysicalSurface FLESH_SURFACE		  = EPhysicalSurface::SurfaceType8;
	const EPhysicalSurface FLESH_CRIT_SURFACE	  = EPhysicalSurface::SurfaceType34;
	const EPhysicalSurface SHIELD_SURFACE		  = EPhysicalSurface::SurfaceType35;
	const EPhysicalSurface SHIELD_CRIT_SURFACE	  = EPhysicalSurface::SurfaceType36;
	const EPhysicalSurface HARDENDED_SURFACE	  = EPhysicalSurface::SurfaceType37;
	const EPhysicalSurface HARDENDED_CRIT_SURFACE = EPhysicalSurface::SurfaceType38;
}

enum ECommonUiMpcColors
{
	BLACK,
	WHITE,
	GOLD,
	ENEMY_HIGHLIGHT,
	FRIENDLY_HIGHLIGHT,
	NEUTRAL_HIGHLIGHT,
	POSITIVE,
	NEGATIVE,
	YELLOW,
	OVERTIME,

	RARITY_COMMON,
	RARITY_UNCOMMON,
	RARITY_RARE,
	RARITY_EXTRAORDINARY,
	RARITY_ARTIFACT,

	PURE_WHITE UMETA(DisplayName="PURE_WHITE (Original Icon Color)")
}

namespace CommonUiColorMpcNames
{
	const FName BLACK			   = n"Black100";
	const FName WHITE			   = n"White100";
	const FName GOLD			   = n"Gold100";
	const FName ENEMY_HIGHLIGHT	   = n"EnemyHighlight";
	const FName FRIENDLY_HIGHLIGHT = n"FriendlyHighlight";
	const FName NEUTRAL_HIGHLIGHT  = n"NeutralHighlight";
	const FName POSITIVE		   = n"SystemGreen";
	const FName NEGATIVE		   = n"SystemRed";
	const FName YELLOW			   = n"SystemYellow";
	const FName OVERTIME		   = n"Overtime100";
	
	const FName RARITY_COMMON	   = n"RarityCommon";
	const FName RARITY_UNCOMMON	   = n"RarityUncommon";
	const FName RARITY_RARE	   		= n"RarityRare";
	const FName RARITY_EXTRAORDINARY = n"RarityExtraordinary";
	const FName RARITY_ARTIFACT		= n"RarityArtifact";

	const FName PURE_WHITE 			=n"PureWhite";

	FName GetColorIdFromEnum( ECommonUiMpcColors color )
	{
		switch ( color )
		{
			case ECommonUiMpcColors::BLACK:
				return BLACK;
			case ECommonUiMpcColors::WHITE:
				return WHITE;
			case ECommonUiMpcColors::GOLD:
				return GOLD;
			case ECommonUiMpcColors::ENEMY_HIGHLIGHT:
				return ENEMY_HIGHLIGHT;
			case ECommonUiMpcColors::FRIENDLY_HIGHLIGHT:
				return FRIENDLY_HIGHLIGHT;
			case ECommonUiMpcColors::NEUTRAL_HIGHLIGHT:
				return NEUTRAL_HIGHLIGHT;
			case ECommonUiMpcColors::POSITIVE:
				return POSITIVE;
			case ECommonUiMpcColors::NEGATIVE:
				return NEGATIVE;
			case ECommonUiMpcColors::YELLOW:
				return YELLOW;
			case ECommonUiMpcColors::OVERTIME:
				return OVERTIME;

			case ECommonUiMpcColors::RARITY_COMMON:
				return RARITY_COMMON;
			case ECommonUiMpcColors::RARITY_UNCOMMON:
				return RARITY_UNCOMMON;
			case ECommonUiMpcColors::RARITY_RARE:
				return RARITY_RARE;
			case ECommonUiMpcColors::RARITY_EXTRAORDINARY:
				return RARITY_EXTRAORDINARY;
			case ECommonUiMpcColors::RARITY_ARTIFACT:
				return RARITY_ARTIFACT;	

			case ECommonUiMpcColors::PURE_WHITE:
				return PURE_WHITE;		
		}
	}
}