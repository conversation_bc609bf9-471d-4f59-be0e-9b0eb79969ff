UCLASS( Abstract )
class UAS_ReviveWidget : UUserWidgetDefault
{
	UPROPERTY( NotEditable, BindWidget )
	private UImage progress_bar_plate;

	private UMaterialInstanceDynamic progressBarDynMat;
	private AAS_PlayerEntity myPawn;
	private UAS_RespawnTotemComponent totemComponent;

	UFUNCTION(BlueprintOverride)
	private void Construct()
	{
		progressBarDynMat = progress_bar_plate.GetDynamicMaterial();
	}

	void SetMyPawn( AAS_PlayerEntity Pawn )
	{
		if ( IsValid( totemComponent ) )
		{
			totemComponent.net_reviveBarEndTime.OnReplicated().Unbind( this, n"OnReviveTimeChanged" );
			totemComponent.net_reviveBarStartTime.OnReplicated().Unbind( this, n"OnReviveTimeChanged" );
			totemComponent = nullptr;
		}

		myPawn = Pawn;

		if ( IsValid( myPawn ) )
		{
			totemComponent = myPawn.TotemComponent();
			totemComponent.net_reviveBarEndTime.OnReplicated().AddUFunction( this, n"OnReviveTimeChanged" );
			totemComponent.net_reviveBarStartTime.OnReplicated().AddUFunction( this, n"OnReviveTimeChanged" );
		}
	}

	UFUNCTION()
	private void OnReviveTimeChanged( int oldValue, int newValue )
	{
		UpdateVisibility();
	}

	private void UpdateVisibility()
	{
		if ( IsValid( totemComponent ) )
		{
			float reviveTime = GetReviveTime();

			// TODO @jmccarty: This can be improved, instead of storing revive timestamps on the reviver and revivee, implement
			// a clearer way to communicate that the reviver is doing something important
			if ( reviveTime > 0 && !totemComponent.net_isBeingRevived )
			{
				SetWidgetVisibilitySafe( this, ESlateVisibility::HitTestInvisible );
				return;
			}
		}

		SetWidgetVisibilitySafe( this, ESlateVisibility::Hidden );
	}

	private int GetReviveTime()
	{
		int reviveTime = IsValid( totemComponent ) ? int( totemComponent.net_reviveBarEndTime ) - int( totemComponent.net_reviveBarStartTime ) : 0;
		
		return reviveTime;
	}

	private int GetReviveStartTime()
	{
		int startTime = ( IsValid( totemComponent ) && totemComponent.net_reviveBarStartTime > 0 ) ? int( totemComponent.net_reviveBarStartTime ) : 0;
		
		return startTime;
	}

	UFUNCTION( BlueprintOverride )
	private void Tick( FGeometry MyGeometry, float InDeltaTime )
	{
		float reviveTime	 = GetReviveTime();
		float reviveProgress = reviveTime == 0 ? 0 : ( myPawn.GetTimeMilliseconds() - GetReviveStartTime() ) / reviveTime;

		progressBarDynMat.SetScalarParameterValue( n"ProgressFrac", reviveProgress );
	}
}