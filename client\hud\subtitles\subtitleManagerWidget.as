UCLASS( Abstract )
class UAS_SubtitleManager : UNC_DisplayWidget
{
	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UVerticalBox subtitleContainer;

	UPROPERTY( EditDefaultsOnly, BlueprintHidden )
	private TSubclassOf<UAS_Subtitle> subtitleClass;

	private TMap<UAS_DialogueQueueEntry, UAS_Subtitle> subtitleMap;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		UAS_SubtitleSystem subtitleSystem = GetSubtitleSystem();
		if ( IsValid( subtitleSystem ) )
		{
			subtitleSystem.OnSubtitleAdded.AddUFunction( this, n"OnSubtitleAdded" );
			subtitleSystem.OnSubtitleRemoved.AddUFunction( this, n"OnSubtitleRemoved" );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void OnSubtitleAdded( UAS_DialogueQueueEntry entry )
	{
		if ( !IsValid( subtitleClass ) || !IsValid( entry ) )
			return;

		UAS_Subtitle subtitle = Cast<UAS_Subtitle>( WidgetBlueprint::CreateWidget( subtitleClass, GetOwningPlayer() ) );
		if ( IsValid( subtitle ) )
		{
			// When we create a subtitle, we add it to a map so we can track it later when it should be removed
			subtitle.Init( entry.GetSubtitleData() );
			subtitleContainer.AddChild( subtitle );
			subtitleMap.Add( entry, subtitle );
			subtitle.Show();
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void OnSubtitleRemoved( UAS_DialogueQueueEntry entry )
	{
		if ( !IsValid( entry ) || !subtitleMap.Contains( entry ) )
			return;

		UAS_Subtitle widget = subtitleMap[entry];
		if ( IsValid( widget ) )
		{
			widget.Hide();
		}

		subtitleMap.Remove( entry );
	}
}