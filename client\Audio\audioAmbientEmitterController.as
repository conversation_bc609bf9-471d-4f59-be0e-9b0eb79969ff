enum ENCAudioControllerTriggerMode
{
	Sphere,
	Box
};

class AAS_NCAudioAmbientEmitterSimpleController : AAS_ClientSideActor
{
	UPROPERTY( DefaultComponent, RootComponent )
	USceneComponent root;

	// defaults
	default ActorHiddenInGame = true;
	default bNetLoadOnClient  = true;
	default bCanBeDamaged	  = false;

	// Autolink Emitter Radius | EDITOR ONLY
	UPROPERTY( EditAnywhere, Category = "NCAudio Emitters" )
	float AutoLinkEmitterRadius = 5000.0f;

	// Link your NCAudio Emitter Simple (s)
	UPROPERTY( EditInstanceOnly, Category = "NCAudio Emitters" )
	TArray<AAS_NCAudioAmbientEmitterSimple> linkedEmitters;

	// Will select 1 Random Emitter from the Array of Linked Emitters to play when the player Enters the Trigger
	UPROPERTY( EditAnywhere, Category = "NCAudio Emitters" )
	bool bPlayRandomEmitter = false;

	// Select your Trigger Shape | Sphere or Box
	UPROPERTY( EditAnywhere )
	ENCAudioControllerTriggerMode triggerShape = ENCAudioControllerTriggerMode::Box;

	UAS_SphereTrigger sphereTrigger;

	UAS_BoxTrigger boxTrigger;

	UPROPERTY( EditAnywhere )
	float sphereRadius = 1500.0f;

	UPROPERTY( EditAnywhere )
	FVector boxScale = FVector( 15, 15, 5 );

	// When Enabled will Display Perimiter of the Trigger on Enter (Yellow) + Exit (Red) in PIE
	UPROPERTY( EditAnywhere )
	bool debugDraw = false;

	private ANCPlayerCharacter localPlayer;

	private AAS_NCAudioAmbientEmitterSimple activeRandomEmitter;

	private int lastRandomIndex = -1;

	UFUNCTION( BlueprintOverride )
	void ConstructionScript()
	{
		MakeTriggers();
	}

#if EDITOR
	// Toggle to Autolink Emitters with the Autolink Radius | EDITOR ONLY
	UFUNCTION( CallInEditor, Category = "NCAudio Emitters" )
	void AutoLinkEmitters()
	{
		linkedEmitters.Empty();

		TArray<AActor> allEmitters;
		GetAllActorsOfClass( AAS_NCAudioAmbientEmitterSimple::StaticClass(), allEmitters );

		for ( AActor actor : allEmitters )
		{
			AAS_NCAudioAmbientEmitterSimple emitter = Cast<AAS_NCAudioAmbientEmitterSimple>( actor );
			if ( IsValid( emitter ) && Distance( GetActorLocation(), emitter.GetActorLocation() ) <= AutoLinkEmitterRadius )
			{
				linkedEmitters.Add( emitter );
			}
		}
	}
#endif

#if EDITOR
	// Shows Emitter Link Lines | EDITOR ONLY
	UFUNCTION( CallInEditor, Category = "NCAudio Emitters" )
	void ShowEmitterLinks()
	{

		for ( AAS_NCAudioAmbientEmitterSimple emitter : linkedEmitters )
		{
			if ( !IsValid( emitter ) )
				continue;

			FVector start = GetActorLocation();
			FVector end	  = emitter.GetActorLocation();

			System::DrawDebugArrow( start, end, 1000, FLinearColor::Green, 5.0, 15.0 );
		}
	}
#endif

#if EDITOR
	// Clears Broken Emitter Links | EDITOR ONLY
	UFUNCTION( CallInEditor, Category = "NCAudio Emitters" )
	void ClearBrokenLinks()
	{
		for (int i = 0; i < linkedEmitters.Num(); )
		{
			if (!IsValid(linkedEmitters[i]))
				linkedEmitters.RemoveAtSwap(i);
			else
				i++;
		}
	}
#endif

		void MakeTriggers()
		{
			if ( triggerShape == ENCAudioControllerTriggerMode::Sphere )
			{
				if ( IsValid( boxTrigger ) )
				{
					boxTrigger.DestroyComponent( this );
				}
				sphereTrigger = Cast<UAS_SphereTrigger>( CreateComponent( UAS_SphereTrigger::StaticClass() ) );
				sphereTrigger.SetRadius( sphereRadius );
				sphereTrigger.SetCollisionProfileName( n"Trigger" );
			}
			else
			{
				if ( IsValid( sphereTrigger ) )
				{
					sphereTrigger.DestroyComponent( this );
				}
				boxTrigger = Cast<UAS_BoxTrigger>( CreateComponent( UAS_BoxTrigger::StaticClass() ) );
				boxTrigger.SetWorldScale3D( boxScale );
				boxTrigger.SetCollisionProfileName( n"Trigger" );
			}
		}

		UFUNCTION( BlueprintOverride )
		void ClientBeginPlay()
		{
			// Binds
			if ( triggerShape == ENCAudioControllerTriggerMode::Box )
			{
				if ( !IsValid( boxTrigger ) )
					MakeTriggers();
				boxTrigger.onPlayerEntered.AddUFunction( this, n"OnPlayerEnteredBox" );
				boxTrigger.onPlayerExited.AddUFunction( this, n"OnPlayerExitedBox" );
			}
			else
			{
				if ( !IsValid( sphereTrigger ) )
					MakeTriggers();
				sphereTrigger.onPlayerEntered.AddUFunction( this, n"OnPlayerEnteredSphere" );
				sphereTrigger.onPlayerExited.AddUFunction( this, n"OnPlayerExitedSphere" );
			}
		}

		UFUNCTION( BlueprintOverride )
		void OnHUDInit( ANC_HUD hud )
		{
			Super::OnHUDInit( hud );
			localPlayer = Client_GetLocalPawn();
		}

		UFUNCTION()
		void SelectRandomEmitter()
		{
			activeRandomEmitter = nullptr;

			int emitterCount = linkedEmitters.Num();
			if ( emitterCount == 0 )
				return;

			TArray<AAS_NCAudioAmbientEmitterSimple> possibleEmitters;
			possibleEmitters = linkedEmitters;

			if ( lastRandomIndex != -1 && possibleEmitters.Num() > 1 )
				possibleEmitters.RemoveAt( lastRandomIndex );

			if ( possibleEmitters.Num() > 0 )
			{
				int randomIndex		= RandRangeExlusive( 0, possibleEmitters.Num() );
				activeRandomEmitter = possibleEmitters[randomIndex];
				lastRandomIndex		= randomIndex;
			}
		}

		UFUNCTION()
		void OnPlayerEnteredSphere( AAS_PlayerEntity Player, UAS_SphereTrigger Trigger )
		{
			if ( !IsValid( localPlayer ) )
			{
				return;
			}

			if ( Player == localPlayer )
			{
				if ( bPlayRandomEmitter )
				{
					SelectRandomEmitter();
					if ( IsValid( activeRandomEmitter ) )
						activeRandomEmitter.StartEmitter();
				}
				else
				{
					for ( AAS_NCAudioAmbientEmitterSimple emitter : linkedEmitters )
					{
						if ( IsValid( emitter ) )
							emitter.StartEmitter();
					}
				}
#if EDITOR
				if ( debugDraw )
				{
					DrawDebugSphere( GetActorLocation(), sphereRadius, 2, FLinearColor::Yellow, 6.0 );
				}
#endif
			}
		}

		UFUNCTION()
		void OnPlayerExitedSphere( AAS_PlayerEntity Player, UAS_SphereTrigger Trigger )
		{
			if ( !IsValid( localPlayer ) )
			{
				return;
			}

			if ( Player == localPlayer )
			{
				if ( bPlayRandomEmitter )
				{
					if ( IsValid( activeRandomEmitter ) )
						activeRandomEmitter.StopEmitter();
					activeRandomEmitter = nullptr;
				}
				else
				{
					for ( AAS_NCAudioAmbientEmitterSimple emitter : linkedEmitters )
					{
						if ( IsValid( emitter ) )
							emitter.StopEmitter();
					}
				}
#if EDITOR
				if ( debugDraw )
				{
					DrawDebugSphere( GetActorLocation(), sphereRadius, 2, FLinearColor::Red, 6.0 );
				}
#endif
			}
		}

		UFUNCTION()
		void OnPlayerEnteredBox( AAS_PlayerEntity Player, UAS_BoxTrigger Trigger )
		{
			if ( !IsValid( localPlayer ) )
			{
				return;
			}

			if ( Player == localPlayer )
			{
				if ( bPlayRandomEmitter )
				{
					SelectRandomEmitter();
					if ( IsValid( activeRandomEmitter ) )
						activeRandomEmitter.StartEmitter();
				}
				else
				{
					activeRandomEmitter = nullptr;
					for ( AAS_NCAudioAmbientEmitterSimple emitter : linkedEmitters )
					{
						if ( IsValid( emitter ) )
							emitter.StartEmitter();
					}
				}
#if EDITOR
				if ( debugDraw )
				{

					DrawDebugBox( this.GetActorLocation(), this.GetActorRotation(), boxScale * 100, 3.0, FLinearColor::Yellow, 4.0 );
				}
#endif
			}
		}

		UFUNCTION()
		void OnPlayerExitedBox( AAS_PlayerEntity Player, UAS_BoxTrigger Trigger )
		{
			if ( !IsValid( localPlayer ) )
			{
				return;
			}

			if ( Player == localPlayer )
			{
				if ( bPlayRandomEmitter )
				{
					if ( IsValid( activeRandomEmitter ) )
						activeRandomEmitter.StopEmitter();
					activeRandomEmitter = nullptr;
				}
				else
				{
					for ( AAS_NCAudioAmbientEmitterSimple emitter : linkedEmitters )
					{
						if ( IsValid( emitter ) )
							emitter.StopEmitter();
					}
				}
#if EDITOR
				if ( debugDraw )
				{
					DrawDebugBox( this.GetActorLocation(), this.GetActorRotation(), boxScale * 100, 3.0, FLinearColor::Red, 4.0 );
				}
#endif
			}
		}

		UFUNCTION( BlueprintOverride )
		void EndPlay( EEndPlayReason EndPlayReason )
		{
			Super::EndPlay( EndPlayReason );
			if ( IsClient() )
			{
				if ( bPlayRandomEmitter )
				{
					if ( IsValid( activeRandomEmitter ) )
						activeRandomEmitter.StopEmitter();
					activeRandomEmitter = nullptr;
				}
				else
				{
					for ( AAS_NCAudioAmbientEmitterSimple emitter : linkedEmitters )
					{
						if ( IsValid( emitter ) )
							emitter.StopEmitter();
					}
				}

				if ( triggerShape == ENCAudioControllerTriggerMode::Box )
				{
					if ( IsValid( boxTrigger ) )
					{
						boxTrigger.onPlayerEntered.UnbindObject( this );
						boxTrigger.onPlayerExited.UnbindObject( this );
					}
				}
				else
				{
					if ( IsValid( sphereTrigger ) )
					{
						sphereTrigger.onPlayerEntered.UnbindObject( this );
						sphereTrigger.onPlayerExited.UnbindObject( this );
					}
				}
			}
		}
	}
