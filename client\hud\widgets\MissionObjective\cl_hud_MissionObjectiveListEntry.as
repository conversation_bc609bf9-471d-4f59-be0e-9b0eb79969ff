enum EMissionObjectiveType
{
	INVA<PERSON>ID,
	CHECKBOX,
	PROGRESS
}

namespace MissionObjectiveAnimFlag
{
	const int ANIM_APPEAR			= ( 1 << 0 );
	const int ANIM_REMOVE			= ( 1 << 1 );
	const int ANIM_ACTIVE			= ( 1 << 2 );
	const int ANIM_INACTIVE			= ( 1 << 3 );
	const int ANIM_NAG 				= ( 1 << 4 );
	const int ANIM_NAG_INCORRECT 	= ( 1 << 5 );
	const int ANIM_COMPLETE 		= ( 1 << 6 );
	const int ANIM_CLEAR_COMPLETE 	= ( 1 << 7 );
}

namespace MissionObjectiveAnim
{
	const float32 ANIM_PLAYBACK 	= 0.5;
}

namespace MissionObjectiveStateFlag
{
	const int STATE_IS_ACTIVE			= ( 1 << 0 );
	const int STATE_IS_COMPLETE			= ( 1 << 1 );
	const int STATE_MARKED_FOR_REMOVAL	= ( 1 << 2 );
	const int STATE_MARKED_FOR_DELETE	= ( 1 << 3 );
}

event void FEvent_OnMissionObjectiveUpdateCallback(UAS_MissionObjectiveListObject data);

UCLASS()
class UAS_MissionObjectiveListObject : UObject
{
	private EMissionObjectiveType _objectiveType;
	private FText _objectiveText;
	private FText _progressText;
	private float32 _progressFrac 	= 0;
	
	//states
	private int _objState 	= 0;
	
	//anims
	private int _animRequests = 0;
	
	void OnAnimRequestComplete()
	{
		_animRequests = 0;
	}

	FEvent_OnMissionObjectiveUpdateCallback OnObjectiveUpdated;
	FEvent_OnMissionObjectiveUpdateCallback OnDeleteListEntry;
	

/****************************************************************\

 █████  ██████  ██████ 
██   ██ ██   ██ ██   ██
███████ ██   ██ ██   ██
██   ██ ██   ██ ██   ██
██   ██ ██████  ██████ 
                                                                                                 
\****************************************************************/
	access Panel = private, UAS_MissionObjectivePanelWidget;
	access:Panel void AddCheckboxObjective( FText text, bool isActive )
	{
		SetNewObjectiveData( EMissionObjectiveType::CHECKBOX, text, isActive, FText(), true );
	}

	access:Panel void AddCheckboxObjectiveSilent( FText text, bool isActive )
	{
		SetNewObjectiveData( EMissionObjectiveType::CHECKBOX, text, isActive, FText(), false );
	}

	access:Panel void AddProgressbarObjective( FText text, bool isActive, FText optionalProgressText = FText() )
	{
		SetNewObjectiveData( EMissionObjectiveType::PROGRESS, text, isActive, optionalProgressText, true );
	}

	access:Panel void AddProgressbarObjectiveSilent( FText text, bool isActive, FText optionalProgressText = FText() )
	{
		SetNewObjectiveData( EMissionObjectiveType::PROGRESS, text, isActive, optionalProgressText, false );
	}

	private void SetNewObjectiveData( EMissionObjectiveType type, FText text, bool isActive, FText optionalProgressText, bool playNagAnim )
	{
		_objectiveType 		= type;
		_objectiveText 		= text;
		_objState 			= isActive ? Bitflags::AddFlag(_objState, MissionObjectiveStateFlag::STATE_IS_ACTIVE) : 0;
		_progressText 		= optionalProgressText;

		_animRequests 		= Bitflags::AddFlag(0, MissionObjectiveAnimFlag::ANIM_APPEAR);

		if ( isActive )
			_animRequests 	= Bitflags::AddFlag(_animRequests, MissionObjectiveAnimFlag::ANIM_ACTIVE);
		if ( playNagAnim )
			_animRequests 	= Bitflags::AddFlag(_animRequests, MissionObjectiveAnimFlag::ANIM_NAG);

		OnObjectiveUpdated.Broadcast(this);
	}

/****************************************************************\

 █████   ██████ ████████ ██ ██    ██ ███████ 
██   ██ ██         ██    ██ ██    ██ ██      
███████ ██         ██    ██ ██    ██ █████   
██   ██ ██         ██    ██  ██  ██  ██      
██   ██  ██████    ██    ██   ████   ███████
                                                                                                 
\****************************************************************/
	void SetActive()
	{
		if ( GetIsActive() )
			return;

		_objState 		= Bitflags::AddFlag(_objState, MissionObjectiveStateFlag::STATE_IS_ACTIVE);
		_animRequests 	= Bitflags::AddFlag(_animRequests, MissionObjectiveAnimFlag::ANIM_ACTIVE);
		
		OnObjectiveUpdated.Broadcast(this);
	}
	
	void SetInactive()
	{
		if ( !GetIsActive() )
			return;
		
		_objState 		= Bitflags::ClearFlag(_objState, MissionObjectiveStateFlag::STATE_IS_ACTIVE);
		_animRequests 	= Bitflags::AddFlag(_animRequests, MissionObjectiveAnimFlag::ANIM_INACTIVE);

		OnObjectiveUpdated.Broadcast(this);
	}

/****************************************************************\

██    ██ ██████  ██████   █████  ████████ ███████ 
██    ██ ██   ██ ██   ██ ██   ██    ██    ██      
██    ██ ██████  ██   ██ ███████    ██    █████   
██    ██ ██      ██   ██ ██   ██    ██    ██      
 ██████  ██      ██████  ██   ██    ██    ███████
                                                                                                 
\****************************************************************/
	void UpdateObjectiveText(FText text)
	{
		_objectiveText 	= text;
		_animRequests 	= Bitflags::AddFlag(_animRequests, MissionObjectiveAnimFlag::ANIM_NAG);
		
		OnObjectiveUpdated.Broadcast(this);
	}

	void UpdateObjectiveTextSilent(FText text)
	{
		_objectiveText 	= text;
		
		OnObjectiveUpdated.Broadcast(this);
	}

	void UpdateObjectiveProgress(float32 progressFrac, FText optionalProgressText = FText())
	{
		_progressFrac 	= progressFrac;
		_progressText 	= optionalProgressText;
		_animRequests 	= Bitflags::AddFlag(_animRequests, MissionObjectiveAnimFlag::ANIM_NAG);
		
		OnObjectiveUpdated.Broadcast(this);
	}

	void UpdateObjectiveProgressSilent(float32 progressFrac, FText optionalProgressText = FText())
	{
		_progressFrac 	= progressFrac;
		_progressText 	= optionalProgressText;
		
		
		OnObjectiveUpdated.Broadcast(this);
	}

	void NagObjective()
	{
		_animRequests 	= Bitflags::AddFlag(_animRequests, MissionObjectiveAnimFlag::ANIM_NAG);

		OnObjectiveUpdated.Broadcast(this);
	}

	void NagObjectiveIncorrect()
	{
		_animRequests 	= Bitflags::AddFlag(_animRequests, MissionObjectiveAnimFlag::ANIM_NAG_INCORRECT);

		OnObjectiveUpdated.Broadcast(this);
	}

/****************************************************************\

 ██████  ██████  ███    ███ ██████  ██      ███████ ████████ ███████ 
██      ██    ██ ████  ████ ██   ██ ██      ██         ██    ██      
██      ██    ██ ██ ████ ██ ██████  ██      █████      ██    █████   
██      ██    ██ ██  ██  ██ ██      ██      ██         ██    ██      
 ██████  ██████  ██      ██ ██      ███████ ███████    ██    ███████
                                                                                                 
\****************************************************************/
	void SetObjectiveCompleted( FText optionalProgressText = FText() )
	{
		if ( GetIsCompleted() )
			return;

		ScriptAssert( !Bitflags::HasFlag(_objState, MissionObjectiveStateFlag::STATE_MARKED_FOR_DELETE), "Tried to complete objective marked for deletion" );
		ScriptAssert( !Bitflags::HasFlag(_objState, MissionObjectiveStateFlag::STATE_MARKED_FOR_REMOVAL), "Tried to complete objective marked for removal" );

		_objState 			= Bitflags::AddFlag(_objState, MissionObjectiveStateFlag::STATE_IS_COMPLETE);
		_animRequests 		= Bitflags::AddFlag(_animRequests, MissionObjectiveAnimFlag::ANIM_COMPLETE);

		_progressFrac 	= 1.0;
		_progressText 	= optionalProgressText;
		
		OnObjectiveUpdated.Broadcast(this);
	}

	void SetObjectiveCompletedAndRemove( FText optionalProgressText = FText() )
	{
		if ( GetIsCompleted() )
			return;

		ScriptAssert( !Bitflags::HasFlag(_objState, MissionObjectiveStateFlag::STATE_MARKED_FOR_DELETE), "Tried to complete objective marked for deletion" );
		ScriptAssert( !Bitflags::HasFlag(_objState, MissionObjectiveStateFlag::STATE_MARKED_FOR_REMOVAL), "Tried to complete objective marked for removal" );
		
		_objState 			= Bitflags::AddFlag(_objState, MissionObjectiveStateFlag::STATE_IS_COMPLETE | MissionObjectiveStateFlag::STATE_MARKED_FOR_REMOVAL);
		_animRequests 		= Bitflags::AddFlag(_animRequests, MissionObjectiveAnimFlag::ANIM_COMPLETE);

		_progressFrac 	= 1.0;
		_progressText 	= optionalProgressText;

		OnObjectiveUpdated.Broadcast(this);
	}

	void SetObjectiveCompletedAndInactive( FText optionalProgressText = FText() )
	{
		if ( GetIsCompleted() )
			return;
		
		ScriptAssert( !Bitflags::HasFlag(_objState, MissionObjectiveStateFlag::STATE_MARKED_FOR_DELETE), "Tried to complete objective marked for deletion" );
		ScriptAssert( !Bitflags::HasFlag(_objState, MissionObjectiveStateFlag::STATE_MARKED_FOR_REMOVAL), "Tried to complete objective marked for removal" );
		
		_objState 			= Bitflags::AddFlag(_objState, MissionObjectiveStateFlag::STATE_IS_COMPLETE);
		_objState 			= Bitflags::ClearFlag(_objState, MissionObjectiveStateFlag::STATE_IS_ACTIVE);
		_animRequests 		= Bitflags::AddFlag(_animRequests, MissionObjectiveAnimFlag::ANIM_COMPLETE | MissionObjectiveAnimFlag::ANIM_INACTIVE );
		
		_progressFrac 	= 1.0;
		_progressText 	= optionalProgressText;

		OnObjectiveUpdated.Broadcast(this);
	}

	void ClearObjectiveCompleted( float32 optionalProgressFrac = 0.0, FText optionalProgressText = FText() )
	{
		if ( !GetIsCompleted() )
			return;

		ScriptAssert( !Bitflags::HasFlag(_objState, MissionObjectiveStateFlag::STATE_MARKED_FOR_DELETE), "Tried to complete objective marked for deletion" );
		ScriptAssert( !Bitflags::HasFlag(_objState, MissionObjectiveStateFlag::STATE_MARKED_FOR_REMOVAL), "Tried to complete objective marked for removal" );
	
		_objState 			= Bitflags::ClearFlag(_objState, MissionObjectiveStateFlag::STATE_IS_COMPLETE);
		_animRequests 		= Bitflags::AddFlag(_animRequests, MissionObjectiveAnimFlag::ANIM_CLEAR_COMPLETE | MissionObjectiveAnimFlag::ANIM_NAG);
		
		_progressFrac 	= optionalProgressFrac;
		_progressText 	= optionalProgressText;

		OnObjectiveUpdated.Broadcast(this);
	}

	void ClearObjectiveCompletedSilent( float32 optionalProgressFrac = 0.0, FText optionalProgressText = FText() )
	{
		if ( !GetIsCompleted() )
			return;
		
		ScriptAssert( !Bitflags::HasFlag(_objState, MissionObjectiveStateFlag::STATE_MARKED_FOR_DELETE), "Tried to complete objective marked for deletion" );
		ScriptAssert( !Bitflags::HasFlag(_objState, MissionObjectiveStateFlag::STATE_MARKED_FOR_REMOVAL), "Tried to complete objective marked for removal" );

		_objState 			= Bitflags::ClearFlag(_objState, MissionObjectiveStateFlag::STATE_IS_COMPLETE);
		_animRequests 		= Bitflags::AddFlag(_animRequests, MissionObjectiveAnimFlag::ANIM_CLEAR_COMPLETE);

		_progressFrac 	= optionalProgressFrac;
		_progressText 	= optionalProgressText;

		OnObjectiveUpdated.Broadcast(this);
	}	

	UFUNCTION()
	void RemoveObjective()
	{
		if ( Bitflags::HasFlag(_objState, MissionObjectiveStateFlag::STATE_MARKED_FOR_DELETE))
			return;

		_objState 			= Bitflags::AddFlag(_objState, MissionObjectiveStateFlag::STATE_MARKED_FOR_DELETE);
		_animRequests 		= Bitflags::AddFlag(_animRequests, MissionObjectiveAnimFlag::ANIM_REMOVE);

		OnObjectiveUpdated.Broadcast(this);
	}

	access internal = private, UAS_MissionObjectiveListEntry;
	access:internal void RemoveObjectiveAfterDelay( float32 delay )
	{
		System::SetTimer( this, n"RemoveObjective", delay, false );
	}

	access:internal void DeleteListEntry()
	{
		OnDeleteListEntry.Broadcast(this);
	}

/****************************************************************\

██    ██ ████████ ██ ██      ██ ████████ ██    ██ 
██    ██    ██    ██ ██      ██    ██     ██  ██  
██    ██    ██    ██ ██      ██    ██      ████   
██    ██    ██    ██ ██      ██    ██       ██    
 ██████     ██    ██ ███████ ██    ██       ██
                                                                                                 
\****************************************************************/
	EMissionObjectiveType GetObjectiveType() const
	{
		return _objectiveType;
	}	

	FText GetObjectiveText() const
	{
		return _objectiveText;
	}
	
	FText GetProgressText() const
	{
		return _progressText;
	}
	
	float32 GetProgressFrac() const
	{
		return _progressFrac;
	}
	
	int GetObjectiveState() const
	{
		return _objState;
	}

	int GetAnimRequestFlags() const
	{
		return _animRequests;
	}

	bool GetIsActive() const
	{
		return Bitflags::HasFlag(_objState, MissionObjectiveStateFlag::STATE_IS_ACTIVE);
	}

	bool GetIsCompleted() const
	{
		return Bitflags::HasFlag(_objState, MissionObjectiveStateFlag::STATE_IS_COMPLETE);
	}

	bool GetIsMarkedForDeleteOrRemove() const
	{
		const int combinedFlags = MissionObjectiveStateFlag::STATE_MARKED_FOR_REMOVAL | MissionObjectiveStateFlag::STATE_MARKED_FOR_DELETE;
		return Bitflags::HasFlag( _objState, combinedFlags );
	}
}






/****************************************************************\

██      ██ ███████ ████████     ██     ██ ██████  ██████  
██      ██ ██         ██        ██     ██ ██   ██ ██   ██ 
██      ██ ███████    ██        ██  █  ██ ██████  ██████  
██      ██      ██    ██        ██ ███ ██ ██   ██ ██      
███████ ██ ███████    ██         ███ ███  ██████  ██
                                                                                                 
\****************************************************************/
UCLASS( Abstract )
class UAS_MissionObjectiveListEntry : UNCGenericListEntry
{		
	UPROPERTY( NotVisible, BindWidget )
	private UOverlay AppearOverlay;
	UPROPERTY( NotVisible, BindWidget )
	private USpacer AppearSpacer;

	UPROPERTY( NotVisible, BindWidget )
	private UAS_CommonTextBlock objectiveText;

	UPROPERTY( NotVisible, BindWidget )
	private UAS_CommonTextBlock progressText;
	
	UPROPERTY( NotVisible, BindWidget )
	private UProgressBar progressBar;

	UPROPERTY( NotVisible, BindWidget )
	private UOverlay checkboxOverlay;

	UPROPERTY( NotVisible, Transient, meta=(BindWidgetAnim))
	private UWidgetAnimation animNag;

	UPROPERTY( NotVisible, Transient, meta=(BindWidgetAnim))
	private UWidgetAnimation animNagIncorrect;

	UPROPERTY( NotVisible, Transient, meta=(BindWidgetAnim))
	private UWidgetAnimation animComplete;

	UPROPERTY( NotVisible, Transient, meta=(BindWidgetAnim))
	private UWidgetAnimation animClearComplete;	

	UPROPERTY( NotVisible, Transient, meta=(BindWidgetAnim))
	private UWidgetAnimation animActive;

	UPROPERTY( NotVisible, Transient, meta=(BindWidgetAnim))
	private UWidgetAnimation animStartInactive;

	UPROPERTY( NotVisible, Transient, meta=(BindWidgetAnim))
	private UWidgetAnimation animAppear;

	UPROPERTY(EditDefaultsOnly)
	UNCAudioAsset objectiveNagSFX;

	UPROPERTY(EditDefaultsOnly)
	UNCAudioAsset objectiveNagIncorrectSFX;

	UPROPERTY(EditDefaultsOnly)
	UNCAudioAsset objectiveCompleteSFX;

	UAS_MissionObjectiveListObject _objectiveRef;

	UFUNCTION(BlueprintOverride)
	void Construct()
	{
		//this is bullshit... the first frame of the appear animation does exactly this,
		//but apparently the first frame of the appear animation doesn't play on the first frame.
		//so I have to manually set it here to avoid a pop... FUCK YOU UNREAL - mo.
		AppearOverlay.SetRenderOpacity(0);
		AppearSpacer.SetSize(FVector2D(0,0));
	}


	UFUNCTION(BlueprintOverride)
	void OnDataSet(UObject DataObject)
	{
		Cleanup();

		_objectiveRef = Cast<UAS_MissionObjectiveListObject>(DataObject);
		if (!IsValid(_objectiveRef) )
			return;
		
		//not setup yet
		if ( _objectiveRef.GetObjectiveType() == EMissionObjectiveType::INVALID )
			return;

		OnDataUpdated( _objectiveRef );
		
		_objectiveRef.OnObjectiveUpdated.AddUFunction(this, n"OnDataUpdated");		
	}

	UFUNCTION(BlueprintOverride)
	void OnDataUnset()
	{
		if ( IsValid(_objectiveRef) )
		{
			int objState = _objectiveRef.GetObjectiveState();
			if ( Bitflags::HasFlag( objState, MissionObjectiveStateFlag::STATE_MARKED_FOR_DELETE ) || 
				 Bitflags::HasFlag( objState, MissionObjectiveStateFlag::STATE_MARKED_FOR_REMOVAL ) )
			{
				_objectiveRef.DeleteListEntry();
			}		
		}

		Cleanup();
	}

	void Cleanup()
	{
		if ( IsValid(_objectiveRef) )
			_objectiveRef.OnObjectiveUpdated.Unbind(this, n"OnDataUpdated");
	}
	
	private bool InValidList()
	{
		return IsValid(GetParentList());
	}

	private UCommonListView GetParentList()
	{
		return Cast<UCommonListView>(this.GetParent());
	}

/****************************************************************\

██████   █████  ████████  █████      ██    ██ ██████  ██████   █████  ████████ ███████ 
██   ██ ██   ██    ██    ██   ██     ██    ██ ██   ██ ██   ██ ██   ██    ██    ██      
██   ██ ███████    ██    ███████     ██    ██ ██████  ██   ██ ███████    ██    █████   
██   ██ ██   ██    ██    ██   ██     ██    ██ ██      ██   ██ ██   ██    ██    ██      
██████  ██   ██    ██    ██   ██      ██████  ██      ██████  ██   ██    ██    ███████
                                                                                                 
\****************************************************************/
	UFUNCTION()
	void OnDataUpdated(UAS_MissionObjectiveListObject data)
	{
		FText text 					= data.GetObjectiveText();		
		FText optionalProgressText	= data.GetProgressText();
		float32 progressFrac 		= data.GetProgressFrac();
		int animFlags 				= data.GetAnimRequestFlags();
		
		if ( !objectiveText.GetText().IdenticalTo(text) )		
			objectiveText.SetText( text );
		if ( progressBar.Percent != progressFrac )
			progressBar.SetPercent( progressFrac );
		if ( !progressText.GetText().IdenticalTo(optionalProgressText) )
			progressText.SetText( optionalProgressText );

		if ( Bitflags::HasFlag( animFlags, MissionObjectiveAnimFlag::ANIM_APPEAR ) )
		{
			EMissionObjectiveType  type = data.GetObjectiveType();
			
			//must set update ANY widgets we change, because this WBP is reused in the list
			switch( type )
			{
				case EMissionObjectiveType::CHECKBOX:
					Cast<USizeBoxSlot>(objectiveText.Slot).SetVerticalAlignment( EVerticalAlignment::VAlign_Center );
					SetWidgetVisibilitySafe( checkboxOverlay, ESlateVisibility::HitTestInvisible );
					SetWidgetVisibilitySafe( progressBar, ESlateVisibility::Collapsed );
					SetWidgetVisibilitySafe( progressText, ESlateVisibility::Collapsed );
					break;

				case EMissionObjectiveType::PROGRESS:
					Cast<USizeBoxSlot>(objectiveText.Slot).SetVerticalAlignment( EVerticalAlignment::VAlign_Top );
					SetWidgetVisibilitySafe( checkboxOverlay, ESlateVisibility::Collapsed );
					SetWidgetVisibilitySafe( progressBar, ESlateVisibility::HitTestInvisible );
					SetWidgetVisibilitySafe( progressText, ESlateVisibility::HitTestInvisible );
					break;	

				default:
					ScriptError( f"Type: {type} not handled" );	
					break;
			}
		}

		OnAnimRequested(data);
	}

	
/****************************************************************\

 █████  ███    ██ ██ ███    ███     ██    ██ ██████  ██████   █████  ████████ ███████ 
██   ██ ████   ██ ██ ████  ████     ██    ██ ██   ██ ██   ██ ██   ██    ██    ██      
███████ ██ ██  ██ ██ ██ ████ ██     ██    ██ ██████  ██   ██ ███████    ██    █████   
██   ██ ██  ██ ██ ██ ██  ██  ██     ██    ██ ██      ██   ██ ██   ██    ██    ██      
██   ██ ██   ████ ██ ██      ██      ██████  ██      ██████  ██   ██    ██    ███████ 
                                                                                                 
\****************************************************************/
	const float32 ANIM_DELAY_AFTER_COMPLETE = 1.25;	

	UFUNCTION()
	private void OnAnimRequested(UAS_MissionObjectiveListObject data)
	{
		bool isActive 				= data.GetIsActive();
		int animFlags 				= data.GetAnimRequestFlags();

		//appear / remove
		if ( Bitflags::HasFlag( animFlags, MissionObjectiveAnimFlag::ANIM_REMOVE ) )
		{
			float32 startTime 		= GetAnimationCurrentTime(animAppear);
			PlayAnimation(animAppear, startTime, 1, EUMGSequencePlayMode::Reverse, MissionObjectiveAnim::ANIM_PLAYBACK );
		}	
		else if ( Bitflags::HasFlag( animFlags, MissionObjectiveAnimFlag::ANIM_APPEAR ) )
			PlayAnimation( animAppear, 0, 1, EUMGSequencePlayMode::Forward, MissionObjectiveAnim::ANIM_PLAYBACK );

		//active / inactive
		if ( isActive )
		{
			if ( Bitflags::HasFlag( animFlags, MissionObjectiveAnimFlag::ANIM_ACTIVE ) )
			{
				float32 startTime = GetAnimationCurrentTime(animActive);
				PlayAnimation( animActive, startTime, 1, EUMGSequencePlayMode::Forward, MissionObjectiveAnim::ANIM_PLAYBACK );
			}
		}
		else if ( !isActive )
		{
			if ( Bitflags::HasFlag( animFlags, MissionObjectiveAnimFlag::ANIM_INACTIVE ) )
			{
				float32 startTime = GetAnimationCurrentTime(animActive);
				PlayAnimation( animActive, startTime, 1, EUMGSequencePlayMode::Reverse, MissionObjectiveAnim::ANIM_PLAYBACK );
			}
			else
				PlayAnimation( animStartInactive );
		}

		//nags
		if ( Bitflags::HasFlag( animFlags, MissionObjectiveAnimFlag::ANIM_NAG_INCORRECT ) )
		{
			if ( IsValid(objectiveNagIncorrectSFX) )
				Client_EmitSoundUI( objectiveNagIncorrectSFX );
			PlayAnimation( animNagIncorrect, 0, 1, EUMGSequencePlayMode::Forward, MissionObjectiveAnim::ANIM_PLAYBACK );
		}
		else if ( Bitflags::HasFlag( animFlags, MissionObjectiveAnimFlag::ANIM_NAG ) )
		{
			if ( IsValid(objectiveNagSFX) )
				Client_EmitSoundUI( objectiveNagSFX );
			PlayAnimation( animNag, 0, 1, EUMGSequencePlayMode::Forward, MissionObjectiveAnim::ANIM_PLAYBACK );
		}

		//complete / clear	
		if ( Bitflags::HasFlag( animFlags, MissionObjectiveAnimFlag::ANIM_COMPLETE ) )
		{
			if ( IsValid(objectiveCompleteSFX) )
				Client_EmitSoundUI( objectiveCompleteSFX );
			PlayAnimation( animComplete, 0, 1, EUMGSequencePlayMode::Forward, MissionObjectiveAnim::ANIM_PLAYBACK );
		}
		else if ( Bitflags::HasFlag( animFlags, MissionObjectiveAnimFlag::ANIM_CLEAR_COMPLETE ) )
			PlayAnimation( animClearComplete, 0, 1, EUMGSequencePlayMode::Forward, MissionObjectiveAnim::ANIM_PLAYBACK );

		data.OnAnimRequestComplete();
	}

	UFUNCTION(BlueprintOverride)
	private void OnAnimationFinished(const UWidgetAnimation Animation)
	{
		if ( !IsValid(_objectiveRef) )
			return;

		int objState = _objectiveRef.GetObjectiveState();

		if ( Bitflags::HasFlag( objState, MissionObjectiveStateFlag::STATE_MARKED_FOR_DELETE ) && Animation == animAppear )
		{
			_objectiveRef.DeleteListEntry();
		}
		else if ( Animation == animComplete )
		{
			if (Bitflags::HasFlag( objState, MissionObjectiveStateFlag::STATE_MARKED_FOR_REMOVAL ) )
				_objectiveRef.RemoveObjectiveAfterDelay( ANIM_DELAY_AFTER_COMPLETE);
		}
	}
}