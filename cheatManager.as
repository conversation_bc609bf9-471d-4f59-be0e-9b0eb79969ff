#if !RELEASE
UCLASS()
class USpammyScriptError : UObject
{
	UFUNCTION()
	void Execute( UNCCoroutine co )
	{
		for ( int i = 0; i < NumErrors; ++i )
		{
			UNCScriptSubsystem::ScriptErrorTitled( Error<PERSON><PERSON>le, ErrorOwner, ErrorMsg );
			co.Wait( ErrorFrequency );
		}
	}

	FString ErrorTitle;
	FString ErrorOwner;
	FString ErrorMsg;
	int32 NumErrors;
	float ErrorFrequency;
}
#endif

UCLASS()
class UAS_CheatManager : UNCCheatManager
{
	////////////////////////////////////////////////////////////////
	////////////////////////////////////////////////////////////////
	////
	////      C O N S O L E  C O M M A N D S
	////
	////////////////////////////////////////////////////////////////
	////////////////////////////////////////////////////////////////

	AAS_CineCamTest customCamActor;

	// Generic script test function - modify as you need it
	UFUNCTION( Exec )
	void ScriptTest()
	{
		GetRaidMessagingManager().DisplayTeamWipeToast( Client_GetLocalASPawn().GetTeam() );
	}

	UFUNCTION( Exec )
	void SimulateImpendingAfkKick( float secondsUntilKick )
	{
		UNCRemoteScriptCommands::SendClientCheat( Client_GetLocalPlayerController(), f"ClientCommand_SimulateImpendingAfkKick {secondsUntilKick}" );
	}

	UFUNCTION( Exec )
	void SimulateImpendingAfkKickCancelled()
	{
		UNCRemoteScriptCommands::SendClientCheat( Client_GetLocalPlayerController(), f"ClientCommand_SimulateImpendingAfkKickCancelled" );
	}

	UFUNCTION( Exec )
	void TestPodium()
	{
		TSubclassOf<UAS_Menu_MatchEnd> matchEndClass = System::LoadClassAsset_Blocking( GetLocalHUD().matchEndMenuClass );
		GetLocalHUD().matchEndMenu					 = Cast<UAS_Menu_MatchEnd>( GetLocalHUD().OpenMenuClass( matchEndClass, true ) );
	}

	UFUNCTION( Exec )
	void ForceAttemptReconnectToMatch()
	{
		UNCGameFlowManager flowManager = UNCGameFlowManager::Get();
		if ( IsValid( flowManager ) )
		{
			if ( flowManager.ReconnectToMatch() )
			{
				LogDisplay( f"Reconnecting to match..." );
			}
			else
			{
				LogDisplay( f"Failed to reconnect to match, is there reconnect data?" );
			}
		}
		else
		{
			LogDisplay( f"Failed to get flow manager!" );
		}
	}

	UFUNCTION( Exec )
	void CustomCam()
	{
		if ( IsValid( customCamActor ) )
		{
			customCamActor.Destroy();
			return;
		}

		AAS_PlayerEntity player = Client_GetLocalASPawn();

		if ( !IsValid( customCamActor ) )
		{
			customCamActor = AAS_CineCamTest::Spawn();
			DepthOfFieldSettings();
		}

		customCamActor.AttachToComponent( player.GetCameraComponent(), NAME_None, EAttachmentRule::SnapToTarget );
		Client_GetLocalPlayerController().SetClientOverrideSpectateActor( customCamActor );
	}

	float savedDof_focalDistance;
	float savedDof_depthBlurRadius;
	float savedDof_depthBlurAmount;
	float savedDof_aperture;

	UFUNCTION( Exec )
	void DepthOfFieldSettings( float focalDistance = 15, float depthBlurRadius = 2, float depthBlurAmount = 2, float aperture = 0.0 )
	{
		savedDof_focalDistance	 = focalDistance;
		savedDof_depthBlurRadius = depthBlurRadius;
		savedDof_depthBlurAmount = depthBlurAmount;
		savedDof_aperture		 = aperture;
		DofSettingsUpdate( customCamActor );
	}

	void DofSettingsUpdate( ACameraActor camActor )
	{
		if ( IsValid( camActor ) )
		{
			camActor.CameraComponent.PostProcessSettings.bOverride_NCDepthofFieldViewmodelOnly = true;
			camActor.CameraComponent.PostProcessSettings.NCDepthofFieldViewmodelOnly		   = false;
			camActor.CameraComponent.PostProcessSettings.bOverride_DepthOfFieldFocalDistance   = true;
			camActor.CameraComponent.PostProcessSettings.DepthOfFieldFocalDistance			   = savedDof_focalDistance;
			camActor.CameraComponent.PostProcessSettings.bOverride_DepthOfFieldDepthBlurRadius = true;
			camActor.CameraComponent.PostProcessSettings.DepthOfFieldDepthBlurRadius		   = savedDof_depthBlurRadius;
			camActor.CameraComponent.PostProcessSettings.bOverride_DepthOfFieldDepthBlurAmount = true;
			camActor.CameraComponent.PostProcessSettings.DepthOfFieldDepthBlurAmount		   = savedDof_depthBlurAmount;
			camActor.CameraComponent.PostProcessSettings.bOverride_DepthOfFieldMinFstop		   = true;
			camActor.CameraComponent.PostProcessSettings.DepthOfFieldMinFstop				   = 0;
			camActor.CameraComponent.PostProcessSettings.bOverride_DepthOfFieldFstop		   = true;
			camActor.CameraComponent.PostProcessSettings.DepthOfFieldFstop					   = savedDof_aperture;
		}
	}

	bool tppActive;

	UFUNCTION( Exec )
	void SnorricamStartOnPlayer( int playerIndex = 0, FName socketAttachName = NAME_None )
	{
		if ( IsValid( customCamActor ) )
		{
			customCamActor.Destroy();
		}

		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( !tppActive && playerIndex == 0 )
		{
			tppActive = true;
			player.GetTPPCameraComponent().AddTPPRequest( n"Snorricam" );
		}

		customCamActor = AAS_SnorricamTest::Spawn();
		DepthOfFieldSettings();

		if ( playerIndex > 0 )
		{
			TArray<AAS_PlayerEntity> players = GetAllPlayers();
			players.Remove( player );
			if ( playerIndex - 1 < players.Num() )
				player = players[playerIndex - 1];
		}

		AttachSnorricamToComponent( player.GetPlayerMesh3P(), socketAttachName );
	}

	UFUNCTION( Exec )
	void SnorricamStartOnActor()
	{
		if ( IsValid( customCamActor ) )
		{
			customCamActor.Destroy();
		}

		FHitResult result = TraceLineFromPlayer( Client_GetLocalPawn(), 10000, ETraceTypeQuery::WeaponFine );
		if ( !result.bBlockingHit )
			return;

		if ( !IsValid( result.Actor ) )
			return;

		customCamActor = AAS_SnorricamTest::Spawn();
		DepthOfFieldSettings();
		AttachSnorricamToComponent( result.Actor.GetRootComponent(), NAME_None );
	}

	void AttachSnorricamToComponent( USceneComponent root, FName socketAttachName )
	{
		FVector relativeOffsetLocation	= FVector( 250, 0, 150 );
		FRotator relativeOffsetRotation = FRotator( 0, 180, 0 );

		customCamActor.AttachToComponent( root, socketAttachName, EAttachmentRule::SnapToTarget );
		customCamActor.CameraComponent.SetRelativeLocation( relativeOffsetLocation );
		customCamActor.SetActorRelativeRotation( relativeOffsetRotation );
		Client_GetLocalPlayerController().SetClientOverrideSpectateActor( customCamActor );
	}

	UFUNCTION( Exec )
	void SnorricamStop()
	{
		if ( IsValid( customCamActor ) )
		{
			customCamActor.Destroy();
			if ( tppActive )
			{
				AAS_PlayerEntity player = Client_GetLocalASPawn();
				player.GetTPPCameraComponent().ClearTPPRequest( n"Snorricam" );
				tppActive = false;
			}
		}
	}

	UFUNCTION( Exec )
	void Freecam()
	{
		if ( IsValid( customCamActor ) )
		{
			customCamActor.Destroy();
		}
		else
		{
			customCamActor = AAS_FreecamTest::Spawn();
			DepthOfFieldSettings();
			customCamActor.SetActorLocation( Client_GetLocalASPawn().GetPawnViewLocation() );
			customCamActor.SetActorRotation( Client_GetLocalASPawn().GetViewRotation() );
			Client_GetLocalPlayerController().SetClientOverrideSpectateActor( customCamActor );
		}
	}

	UFUNCTION( Exec )
	void ReloadWeapons()
	{
		UNCRemoteScriptCommands::SendClientCheat( GetPlayerController(), "ReloadWeapons" );
	}

	UFUNCTION( Exec )
	void RespawnAll()
	{
		UNCRemoteScriptCommands::SendClientCheat( GetPlayerController(), "RespawnAll" );
	}

	UFUNCTION( Exec )
	void Respawn()
	{
		UNCRemoteScriptCommands::SendClientCheat( GetPlayerController(), "Respawn" );
	}

	UFUNCTION( Exec )
	void ClearRespawnTime()
	{
		UNCRemoteScriptCommands::SendClientCheat( GetPlayerController(), "ClearRespawnTime" );
	}

	UFUNCTION( Exec )
	void TestMatchIntro( int teamSize )
	{
		GetMatchIntroManager().DebugMatchIntro( teamSize );
	}

	UFUNCTION( Exec )
	void ToggleViewModel()
	{
		ANCPlayerController controller = Cast<ANCPlayerController>( GetPlayerController() );
		ANCPlayerCharacter pawn		   = controller.GetNCPawn();

		if ( !IsValid( pawn ) )
			return;

		bool newVisible = !pawn.GetFirstPersonWeaponComponent().GetbVisible();
		pawn.GetFirstPersonWeaponComponent().SetVisibility( newVisible );
		pawn.GetFirstPersonWeaponSight().SetVisibility( newVisible );

		pawn.GetFirstPersonArmsComponent().SetOwnerNoSee( !newVisible ); // hack
	}

	UFUNCTION( Exec )
	void ResetLocalPersistence()
	{
		if ( IsValid( Cl_RaidPersistence() ) )
		{
			Cl_RaidPersistence().ResetPersistence();
			Cl_RaidPersistence().StopSaveLoop();
		}
	}

	UFUNCTION( Exec )
	void TakeAllWeapons()
	{
		UNCRemoteScriptCommands::SendClientCheat( GetPlayerController(), "TakeAllWeapons" );
	}

	UFUNCTION( Exec )
	void RepairAllDestructibles()
	{
		UNCRemoteScriptCommands::SendClientCheat( GetPlayerController(), "RepairAllDestructibles" );
	}

	UFUNCTION( Exec )
	void ToggleUI()
	{
		GetLocalHUD().ToggleUI();
	}

	// Test script error functionality
	UFUNCTION( Exec )
	void TestScriptErrorTitled( FString errorTitle, FString errorOwner, FString errorMsg )
	{
#if !RELEASE
		UScriptTestingSubsystem::Get().TestScriptErrorTitled( errorTitle, errorOwner, errorMsg );
#endif
	}

	// Test script error functionality
	UFUNCTION( Exec )
	void TestScriptError( FString errorMsg )
	{
#if !RELEASE
		UScriptTestingSubsystem::Get().TestScriptErrorSimple( errorMsg );
#endif
	}

	// Test reporting asserts triggered from within script
	UFUNCTION( Exec )
	void TestCodeAssertFromScript( FString errorMsg, EScriptErrorTestType testType, int32 repeatIndex )
	{
#if !RELEASE
		UScriptTestingSubsystem::Get().TestCodeAssertFromScript( errorMsg, testType, repeatIndex );
#endif
	}

	// Test reporting asserts triggered from within script
	UFUNCTION( Exec )
	void TestCodeCrashFromScript( EScriptErrorTestType testType, int32 repeatIndex )
	{
#if !RELEASE
		UScriptTestingSubsystem::Get().TestCodeCrashFromScript( testType, repeatIndex );
#endif
	}

	UFUNCTION( Exec )
	void OpenMenu( FString menuName )
	{
		GetLocalHUD().OpenMenu( FName( menuName ) );
	}

	// Test a spammy script error
	UFUNCTION( Exec )
	void TestSpammyScriptError( FString errorTitle, FString errorOwner, FString errorMsg, float errorFrequency, int32 repetitions )
	{
#if !RELEASE
		if ( repetitions <= 0 )
		{
			Error( "Repetitions must be > 0" );
			return;
		}

		if ( errorTitle.IsEmpty() )
		{
			Error( "Error title cannot be empty" );
			return;
		}

		if ( errorFrequency <= 0.f )
		{
			Error( "Frequency must be > 0" );
			return;
		}

		USpammyScriptError scriptError = Cast<USpammyScriptError>( NewObject( this, USpammyScriptError::StaticClass() ) );

		scriptError.ErrorTitle	   = errorTitle;
		scriptError.ErrorOwner	   = errorOwner;
		scriptError.ErrorMsg	   = errorMsg;
		scriptError.ErrorFrequency = errorFrequency;
		scriptError.NumErrors	   = repetitions;

		Thread( scriptError, n"Execute" );
#endif
	}

	UFUNCTION( Exec )
	void TestScriptFatal( FString errorMsg )
	{
		UNCScriptSubsystem::ScriptFatal( errorMsg );
	}

	// Test server script error
	UFUNCTION( Exec )
	void TestServerScriptFatal( FString errorMsg )
	{
#if !RELEASE
		UNCRemoteScriptCommands::SendClientCheat( GetPlayerController(), f"TestServerScriptFatal {errorMsg}" );
#endif
	}

	UFUNCTION( Exec )
	void TestScriptException( EScriptErrorTestType testType, int32 repeatIndex = 0 )
	{
#if !RELEASE
		UScriptTestingSubsystem::Get().TestScriptException( testType, repeatIndex );
#endif
	}

	UFUNCTION( Exec )
	void TestScriptExceptionWithCascadingUIException()
	{
#if !RELEASE
		UScriptTestingSubsystem::Get().TestScriptExceptionUICascade( GetPlayerController() );
#endif
	}

	UFUNCTION( Exec )
	void TestUIScriptException()
	{
#if !RELEASE
		UScriptTestingSubsystem::Get().TestUIScriptException( GetPlayerController() );
#endif
	}

	UFUNCTION( Exec )
	void SkipRespawnTime()
	{
		UNCRemoteScriptCommands::SendClientCheat( GetPlayerController(), "SkipRespawnTime" );
	}

	UFUNCTION( Exec )
	void SetClass( int newClassIndex )
	{
		UNCRemoteScriptCommands::SendClientCheat( GetPlayerController(), f"SetClass {newClassIndex}" );
	}

	UFUNCTION( Exec )
	void GetClass()
	{
		ANCPlayerController controller = Cast<ANCPlayerController>( GetPlayerController() );
		ANCPlayerCharacter pawn		   = controller.GetNCPawn();
		AAS_PlayerEntity player		   = Cast<AAS_PlayerEntity>( pawn );

		if ( IsValid( player ) )
		{
			Print( f"Class Index = {player.ClassManager().GetClass()}" );
		}
	}

	UFUNCTION( Exec )
	void TestTimeSpan()
	{
		GetFormattedCountdownTime( 2500 );
	}

	UFUNCTION( Exec )
	void TestSetRaidShieldHealth( int health )
	{
		AAS_PlayerEntity player = Client_GetLocalASPawn();
		AAS_RaidDomeShield dome = GetRaidDomeShieldForTeam( player.GetTeam() );

		if ( IsValid( dome ) )
			dome.SetDomeVisualHealth( health );
		else
		{
			TArray<AAS_RaidDomeShield> OutActors;
			GetAllActorsOfClass( AAS_RaidDomeShield::StaticClass(), OutActors );
			if ( OutActors.Num() > 0 )
				OutActors[0].SetDomeVisualHealth( health );
		}
	}

	UFUNCTION( Exec )
	void TestPulseRaidShield()
	{
		UNCRemoteScriptCommands::SendClientCheat( Client_GetLocalPlayerController(), f"ClientCommand_TestPulseRaidShield" );
	}

	UFUNCTION( Exec )
	void TestBreakRaidShield()
	{
		UNCRemoteScriptCommands::SendClientCheat( Client_GetLocalPlayerController(), f"ClientCommand_TestBreakRaidShield" );
	}

	UFUNCTION( Exec )
	void TestFixRaidShield()
	{
		UNCRemoteScriptCommands::SendClientCheat( Client_GetLocalPlayerController(), f"ClientCommand_TestFixRaidShield" );
	}

	UFUNCTION( Exec )
	void TestDestroyRaidShield()
	{
		UNCRemoteScriptCommands::SendClientCheat( Client_GetLocalPlayerController(), f"ClientCommand_TestDestroyRaidShield" );
	}
	UFUNCTION( Exec )
	void TestUnDestroyRaidShield()
	{
		UNCRemoteScriptCommands::SendClientCheat( Client_GetLocalPlayerController(), f"ClientCommand_TestUnDestroyRaidShield" );
	}

	UFUNCTION( Exec )
	void AddModToActiveWeapon( FName modName )
	{
		Print( f"Add mod to active weapon called!" );
		UNCRemoteScriptCommands::SendClientCheat( Client_GetLocalPlayerController(), f"AddModToActiveWeapon {modName}" );
	}

	UFUNCTION( Exec )
	void RemoveModFromActiveWeapon( FName modName )
	{
		UNCRemoteScriptCommands::SendClientCheat( Client_GetLocalPlayerController(), f"RemoveModFromActiveWeapon {modName}" );
	}

	UFUNCTION( Exec )
	void CycleWeaponDevMesh( FName modName )
	{
		UNCRemoteScriptCommands::SendClientCheat( Client_GetLocalPlayerController(), f"ClientCommand_CycleWeaponDevMesh" );
	}

	UFUNCTION( Exec )
	void TestMusic( FName uniqueID )
	{
		Music().DevTestMusicEvent( uniqueID );
	}

	UFUNCTION( Exec )
	void TestPlayMusicSheetID( FName uniqueID )
	{
		Music().DevPlayMusicAlias( uniqueID );
	}

	UFUNCTION( Exec )
	void TestStopMusicSheetID( FName uniqueID )
	{
		Music().DevStopMusicAlias( uniqueID );
	}

	UFUNCTION( Exec )
	void LogMeIn()
	{
		UNCUIConnectionManager connectionManager = GetConnectionManager();
		if ( IsValid( connectionManager ) )
		{
			UNCUIConnectionAction action = connectionManager.Connect();
			action.OnComplete.AddUFunction( this, n"OnLoginComplete" );
			action.ExecuteAsync();
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnLoginComplete()
	{
		Print( "Login Successful!" );
		ProgressionGameSystem().OnLoginComplete();
	}

	UFUNCTION( Exec )
	void TestTeamCinematic( bool looping = false )
	{
		TeamCinematic().PlayTeamSequence( ETeamSequenceID::TEST, looping );
	}

	UFUNCTION( Exec )
	void StopTeamCinematic()
	{
		TeamCinematic().StopTeamSequence();
	}

	UFUNCTION( Exec )
	void ForceAllPlayersOnMounts()
	{
		UNCRemoteScriptCommands::SendClientCheat( Client_GetLocalPlayerController(), f"ClientCommand_ForceAllPlayersOnMounts" );
	}

	UFUNCTION( BlueprintCallable )
	void ToggleAudioDebugger()
	{
		ANCPlayerController playerController = Client_GetLocalPlayerController();
		AAS_HUD hud							 = Cast<AAS_HUD>( GetLocalHUD() );
		if ( !IsValid( hud ) )
			return;

		if ( !IsValid( hud.debugAudioDebugger ) )
		{
			hud.debugAudioDebugger = Cast<UUserWidget>( WidgetBlueprint::CreateWidget( Audio().audioDebuggerClass, playerController ) );
			hud.debugAudioDebugger.AddToViewport();
			Print( "Toggle Audio Debugger On" );
		}
		else
		{
			hud.debugAudioDebugger.RemoveFromParent();
			hud.debugAudioDebugger = nullptr;
			Print( "Toggle Audio Debugger Off" );
		}
	}

	UFUNCTION( Exec )
	void QueueObituaryMessageDev()
	{
		TryToQueueObituaryMessage( Localization::GetUnlocalizedTextFromString( f"Test message!" ) );
	}

	UFUNCTION( Exec )
	void ShowKillMessage()
	{
		AAS_HUD hud = Cast<AAS_HUD>( GetLocalHUD() );
		if ( !IsValid( hud ) )
		{
			return;
		}

		if ( !IsValid( hud.mainHUDWidget ) )
		{
			return;
		}

		FText message = GetLocalizedText( Localization::HUDMainWidget, f"HUDMAIN_PLAYER_KILL", FFormatArgumentValue( Localization::GetUnlocalizedTextFromString( f"Player name" ) ) );
		hud.mainHUDWidget.DisplayKillMessage( message );
	}

	UFUNCTION( Exec )
	void ShowAssistMessage()
	{
		AAS_HUD hud = GetLocalHUD();
		if ( !IsValid( hud ) )
			return;

		if ( !IsValid( hud.mainHUDWidget ) )
			return;

		FText message = GetLocalizedText( Localization::HUDMainWidget, f"HUDMAIN_PLAYER_ASSIST", FFormatArgumentValue( Localization::GetUnlocalizedTextFromString( f"Player name" ) ) );
		hud.mainHUDWidget.DisplayAssistMessage( message );
	}

	UFUNCTION( Exec )
	void UpdateDesaturationEnabled()
	{
		UAS_ScreenEffect_Desaturation screenEffect = GetScreenEffect_1PDesaturation();
		if ( IsValid( screenEffect ) )
		{
			screenEffect.UpdateIsEnabled();
		}
	}

	UFUNCTION( Exec )
	void TakeDamageFromNearbyLocation()
	{
		UNCRemoteScriptCommands::SendClientCheat( Client_GetLocalPlayerController(), f"ClientCommand_TakeDamageFromNearbyLocation" );
	}

	UFUNCTION( Exec )
	void TestVendorActivityInt( int index )
	{
		TestVendorActivityEnum( EVendorActivity( index ) );
	}

	UFUNCTION( Exec )
	void TestVendorActivityEnum( EVendorActivity activity )
	{
		TArray<AAS_Vendor> vendors;
		GetAllActorsOfClass( vendors );
		for ( AAS_Vendor v : vendors )
			v.FSM_Component.FSM_SetActivity( activity, GameplayTags::Dev_Invalid, true );
	}

	UFUNCTION( Exec )
	void GatherNearbyDestructiblesForVisualizer()
	{
		AAS_PlayerEntity asPlayer = Client_GetLocalASPawn();
		if ( !IsValid( asPlayer ) )
		{
			return;
		}

		asPlayer.WallInteractionList().Dev_GatherNearbyDestructibles();
	}

	UFUNCTION( Exec )
	void TestTeamWipeMessage( int plantingTeam )
	{
		// this is real path... turn this back on to test for real
		UNCRemoteScriptCommands::SendClientCheat( Client_GetLocalPlayerController(), f"TestTeamWipeMessage {plantingTeam}" );

		// GetLocalHUD().DisplayTeamWipeToast(plantingTeam);
	}

	UFUNCTION( Exec )
	void TestRewardUnlock( int rarity )
	{
		FGameplayTag itemId			   = __TestToastItemID( rarity );
		UAS_UIManager uiManager		   = GetASUIManager();
		UAS_BattlepassInspectMenu menu = Cast<UAS_BattlepassInspectMenu>( GetMenu( n"battlepass_inspect_menu" ).Get().GetDefaultObject() );

		UAS_BattlepassRewardUnlockSplash splash = Cast<UAS_BattlepassRewardUnlockSplash>( uiManager.OpenScreenClass( menu.rewardSplashWidget ) );
		splash.TriggerSplash( itemId );
	}

	UFUNCTION( Exec )
	void TestGenericToast( int rarity )
	{
		FGameplayTag itemId			   = __TestToastItemID( rarity );
		UAS_UIManager uiManager		   = GetASUIManager();
		UAS_BattlepassInspectMenu menu = Cast<UAS_BattlepassInspectMenu>( GetMenu( n"battlepass_inspect_menu" ).Get().GetDefaultObject() );

		UAS_BattlepassRewardUnlockSplash splash = Cast<UAS_BattlepassRewardUnlockSplash>( uiManager.OpenScreenClass( menu.genericToastWidget ) );
		splash.TriggerSplash( itemId );
	}

	FGameplayTag __TestToastItemID( int rarity )
	{
		// GameplayTags::MTX_Battlepass_Battlepass1_Page1	gold
		// GameplayTags::MTX_WeaponSkins_DB51_DummySkin1		grey
		// GameplayTags::MTX_WeaponSkins_DB51_DummySkin8 	blue
		// GameplayTags::MTX_WeaponSkins_DB51_DummySkin9		purple
		// GameplayTags::MTX_WeaponSkins_DB51_DummySkin12 	orange

		FGameplayTag itemId;
		if ( rarity == 0 )
			itemId = GameplayTags::MTX_Battlepass_Battlepass1_Page1;

		return itemId;
	}

	UFUNCTION( Exec )
	void TestMatchFound()
	{
		AAS_FrontEndHUD hud = Cast<AAS_FrontEndHUD>( GetLocalBaseHUD() );
		if ( IsValid( hud ) )
		{
			UAS_MatchFoundToastWidget toast = Cast<UAS_MatchFoundToastWidget>( WidgetBlueprint::CreateWidget( Cast<UAS_FrontEndFrame>( hud.frontEndFrame ).matchFoundToastWidget, hud.GetOwningPlayerController() ) );
			if ( IsValid( toast ) )
			{
				toast.AddToViewport( GameConst::ZORDER_FRAME );
				bool devTest = true;
				toast.PlayToLoadscreenAnim( devTest );
			}
		}
	}

	UFUNCTION( Exec )
	void TestReturnLobby()
	{
		AAS_FrontEndHUD hud = Cast<AAS_FrontEndHUD>( GetLocalBaseHUD() );
		if ( IsValid( hud ) )
		{
			UAS_MatchFoundToastWidget toast = Cast<UAS_MatchFoundToastWidget>( WidgetBlueprint::CreateWidget( Cast<UAS_FrontEndFrame>( hud.frontEndFrame ).matchFoundToastWidget, hud.GetOwningPlayerController() ) );
			if ( IsValid( toast ) )
			{
				toast.AddToViewport( GameConst::ZORDER_FRAME );
				toast.PlayFromLoadscreenAnim();
			}
		}
	}

	UFUNCTION( Exec )
	void CompleteNextObjective()
	{
		UNCRemoteScriptCommands::SendClientCheat( Client_GetLocalPlayerController(), f"CompleteNextObjective" );
	}

#if EDITOR
	UFUNCTION( Exec )
	void TestInviteSent( int count = 1 )
	{
		UAS_SocialInviteManager socialInviteManager = GetSocialInviteManager();
		if ( IsValid( socialInviteManager ) )
		{
			for ( int i = 0; i < count; i++ )
			{
				FSocialInviteData inviteData;
				inviteData.senderPlayerHandle = GetLocalOnlinePlayerHandle();
				inviteData.targetDisplayName  = "PlayerName123";
				socialInviteManager.QueueSocialInvite( inviteData );
			}
		}
	}

	UFUNCTION( Exec )
	void TestInviteReceived( int count = 1 )
	{
		UAS_SocialInviteManager socialInviteManager = GetSocialInviteManager();
		if ( IsValid( socialInviteManager ) )
		{
			for ( int i = 0; i < count; i++ )
			{
				FSocialInviteData inviteData;
				inviteData.targetPlayerHandle = GetLocalOnlinePlayerHandle();
				inviteData.senderDisplayName  = "PlayerName123";
				socialInviteManager.QueueSocialInvite( inviteData );
			}
		}
	}

	UFUNCTION( Exec )
	void TestPartyInviteReceived( int count = 1 )
	{
		UAS_SocialInviteManager socialInviteManager = GetSocialInviteManager();
		if ( IsValid( socialInviteManager ) )
		{
			for ( int i = 0; i < count; i++ )
			{
				FSocialInviteData inviteData;
				inviteData.targetPlayerHandle = GetLocalOnlinePlayerHandle();
				inviteData.senderDisplayName  = "PlayerName123";
				inviteData.isPartyInvite	  = true;
				socialInviteManager.QueueSocialInvite( inviteData );
			}
		}
	}

	UFUNCTION( Exec )
	void TestFriendRequestsReceived( int count = 1 )
	{
		UAS_OnlineFriendsListener friendsListener = Cast<UAS_OnlineFriendsListener>( NewObject( this, UAS_OnlineFriendsListener::StaticClass() ) );
		if ( IsValid( friendsListener ) )
		{
			Thread( this, n"TestFriendRequestsReceivedDeferred", friendsListener, count );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void TestFriendRequestsReceivedDeferred( UNCCoroutine co, UAS_OnlineFriendsListener friendsListener, int count )
	{
		co.Wait( 0.01f );
		friendsListener.AddTestFriendRequests( count );
	}

	UFUNCTION( Exec )
	void TestBlockedUsersReceived( int count = 1 )
	{
		UAS_OnlineFriendsListener friendsListener = Cast<UAS_OnlineFriendsListener>( NewObject( this, UAS_OnlineFriendsListener::StaticClass() ) );
		if ( IsValid( friendsListener ) )
		{
			Thread( this, n"TestBlockedUsersReceivedDeferred", friendsListener, count );
		}
	}

	UFUNCTION( NotBlueprintCallable )
	private void TestBlockedUsersReceivedDeferred( UNCCoroutine co, UAS_OnlineFriendsListener friendsListener, int count )
	{
		co.Wait( 0.01f );
		friendsListener.AddTestBlockedUsers( count );
	}

	UFUNCTION( Exec )
	void TestPriorityMessage( EPriorityMessageTemplate template )
	{
		AddTemplateMessage(template);
	}

	UFUNCTION( Exec )
	void DisableHud()
	{
		GetLocalHUD().mainHUDWidget.DisableHudAnim();		
	}

	UFUNCTION( Exec )
	void EnableHud()
	{
		GetLocalHUD().mainHUDWidget.EnableHudAnim();		
	}

#endif
}