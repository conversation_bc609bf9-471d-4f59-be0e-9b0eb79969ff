const float32 TOTEM_DESTROY_TIME	  = 0.5;
const float32 TOTEM_DESTROY_TIME_HEAL = TOTEM_DESTROY_TIME + 1;

AAS_RespawnTotem SpawnRespawnTotemForPlayer( ANCPlayerCharacter player, const FDamageInfo& damageInfo )
{
	FVector offset	 = FVector( 0, 0, -1 * player.CapsuleComponent.GetCapsuleHalfHeight() );
	FVector spawnLoc = player.GetActorLocation() + offset;

	AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( player );
	if ( UNCUtils::CheckForTriggersAtPosition( GetCurrentWorld(), spawnLoc, ANCKillZVolume::StaticClass() ) )
	{
		TOptional<FVector> safePos = asPlayer.GetSafePosition();
		if ( safePos.IsSet() )
		{
			spawnLoc = safePos.GetValue() + FVector( 0, 0, 10 );
		}
	}

	// DrawDebugSphere( spawnLoc, 100, 10 );

	AAS_RespawnTotem totem = Cast<AAS_RespawnTotem>( Server_SpawnEntity( RaidModeDefaults().respawnTotemClass, player, spawnLoc, player.GetViewRotationFlat() ) );
	totem.DropToFloorComponent.SetThrowDirection( FVector::ZeroVector );
	totem.teamComponent.SetTeam( player.GetTeam() );

	int animVal = -1;
	if ( damageInfo.attacker != asPlayer && damageInfo.damageSourceLocation != asPlayer.GetActorLocation() )
	{
		EDamageAnimDirection animDirectionEnumVal = GetDamageAnimDirection( damageInfo.traceHitResult.ImpactPoint, damageInfo.damageSourceLocation, asPlayer.GetActorForwardVector(), asPlayer.GetActorRightVector() );
		animVal									  = int( animDirectionEnumVal );
	}
	totem.net_lastDeathMontageActivity.SetNetValue( animVal );

	return totem;
}

AAS_RespawnTotem SpawnRespawnTotemForDummy( AAS_TargetDummy_Human dummy )
{
	FVector spawnLoc = dummy.GetBoundsCenter();

	FRotator flatRotation = dummy.GetActorRotation();
	flatRotation.Pitch	  = 0;
	flatRotation.Roll	  = 0;

	AActor actor		   = SpawnActor( RaidModeDefaults().respawnTotemClass, spawnLoc, flatRotation, NAME_None, true );
	AAS_RespawnTotem totem = Cast<AAS_RespawnTotem>( actor );
	if ( IsValid( totem ) )
	{
		totem.net_dummyOwner.SetNetValue( dummy );
	}
	FinishSpawningActor( actor );

	totem.DropToFloorComponent.SetThrowDirection( FVector::ZeroVector );
	totem.teamComponent.SetTeam( dummy.GetTeam() );

	totem.net_lastDeathMontageActivity.SetNetValue( int( EDamageAnimDirection::FRONT ) );

	return totem;
}

struct FGhostMeshPerformanceData
{
	AActor owner;
	USkeletalMeshComponent meshComponent;
	UMaterialInterface ghostMaterial;
	UAnimMontage animToPlay;
	FLinearColor color;
	UClass animClass;
	UNiagaraSystem deathFlash;
}

float32
CreateGhostMesh( FGhostMeshPerformanceData inData )
{
	float32 duration = -1;

	if ( !IsValid( inData.owner ) )
	{
		return duration;
	}
	AAS_PlayerEntity ownerPlayer = Cast<AAS_PlayerEntity>( inData.owner );
	USkeletalMesh victimMesh;

	if ( inData.meshComponent.GetSkeletalMeshAsset() == nullptr )
	{
		if ( IsValid( ownerPlayer ) && IsValid( ownerPlayer.CurrentCharacterSkin.Get() ) )
		{
			victimMesh = ownerPlayer.CurrentCharacterSkin.Get().CharacterSkinMesh;
		}
		else
		{
			victimMesh = inData.owner.GetSkeletalMeshComponent().SkeletalMeshAsset;
		}

		if ( IsValid( victimMesh ) )
		{
			inData.meshComponent.SetSkeletalMeshAsset( victimMesh );

			UMaterialInstanceDynamic dynamicMat = inData.meshComponent.CreateDynamicMaterialInstance( 0, inData.ghostMaterial );
			dynamicMat.SetVectorParameterValue( n"Color", inData.color );
			for ( int i = 0; i < inData.meshComponent.GetNumMaterials(); i++ )
			{
				inData.meshComponent.SetMaterial( i, dynamicMat );
			}

			inData.meshComponent.SetAnimClass( inData.animClass );
		}
	}

	if ( IsValid( inData.meshComponent.GetSkeletalMeshAsset() ) )
	{
		duration							  = inData.meshComponent.PlayScriptedMontage( inData.animToPlay );
		UAS_RespawnTotemAnimInstance animInst = Cast<UAS_RespawnTotemAnimInstance>( inData.meshComponent.AnimInstance );
		if ( IsValid( animInst ) )
		{
			animInst.ClearPlayIdle();
		}

		UNiagaraComponent c = Client_SpawnEffectAtLocation_OneShot( inData.deathFlash, inData.meshComponent.GetSocketLocation( n"spine_chest" ) );
		c.SetNiagaraVariableLinearColor( "Color", inData.color );
	}

	return duration;
}

FLinearColor GetDeathPerformanceColor( bool isFriendly )
{
	if ( isFriendly )
	{
		return FLinearColor( 0, 0.4, 1.0 );
	}

	return FLinearColor::Red;
}

struct FSavedBackpackStruct
{
	TArray<FBackpackItemStruct> items;
}

enum ERespawnTotemUseCase
{
	RESPAWN,
	DENY,
	SACRIFICE,
	PROTECTED
}

UCLASS( Abstract )
class AAS_RespawnTotem : AAS_RespawnCamera
{
	private float32 TOTEM_LIFETIME = 20.0;

	UPROPERTY( DefaultComponent )
	UNCNetMovementComponent movementComponent;

	UPROPERTY( DefaultComponent, RootComponent )
	UStaticMeshComponent mesh;
	default mesh.CollisionEnabled = ECollisionEnabled::PhysicsOnly;

	UPROPERTY( DefaultComponent )
	USkeletalMeshComponent ghostMesh;
	default mesh.CollisionEnabled = ECollisionEnabled::NoCollision;

	UPROPERTY( DefaultComponent )
	USphereComponent reticleUseSphere;
	default reticleUseSphere.CollisionProfileName = n"UseTrace";

	UPROPERTY( DefaultComponent, Attach = mesh )
	UUsableItemComponent useComponent;

	UPROPERTY( DefaultComponent )
	UNCLootMovementComponent DropToFloorComponent;
	default DropToFloorComponent.ComponentTickEnabled = true;

	UPROPERTY( DefaultComponent, Attach = mesh )
	UUsableItemComponent sacrificeUseComponent;
	default sacrificeUseComponent.SphereRadius						   = 128;
	default sacrificeUseComponent.UsableDistance					   = 1000;
	default sacrificeUseComponent.IgnoreMaxUseDistanceFromPlayerToItem = true;

	UPROPERTY( DefaultComponent, Attach = useComponent )
	UNiagaraComponent effectComponent;

	UPROPERTY( DefaultComponent, Attach = useComponent )
	UNiagaraComponent effectSyncedComponent;

	UPROPERTY( DefaultComponent )
	UAS_MapMarkerComponent mapMarker;
	default mapMarker.pinnableWidgetVisibilityOptions.OwnerAndTeammates = n"RespawnTotem";

	UPROPERTY( DefaultComponent )
	UAS_TeamComponent teamComponent;
	default teamComponent.ClientBeginPlayWaitForHUDInit = false;

	private FTimerHandle expireTimer;

	bool respawnActivated = false;
	AAS_PlayerEntity respawnUserPlayer;
	bool isSuicide		   = false;
	bool isSacrificeActive = false;

	UPROPERTY()
	FNCNetPlayerHandle net_currentPlayerUser;

	UPROPERTY()
	FNCNetPlayerHandle net_carrier;

	UPROPERTY()
	FNCNetBool net_isBringCarried;

	UPROPERTY()
	FNCNetEntityHandle net_dummyOwner;

	UPROPERTY( EditDefaultsOnly )
	UMaterialInterface carryMaterial;
	TArray<UMaterialInterface> defaultMaterials;

	AAS_PlayerEntity ownerPlayer;
	AAS_TargetDummy_Human ownerTargetDummy;
	FNCCoroutineSignal onExtendedUseEndedSignal;

	UPROPERTY( EditDefaultsOnly )
	UMaterialInterface soulMaterial;

	UPROPERTY( EditDefaultsOnly )
	UClass ghostMeshAnimClass;

	UPROPERTY( EditDefaultsOnly )
	UAnimMontage death_Front;

	UPROPERTY( EditDefaultsOnly )
	UAnimMontage death_Back;

	UPROPERTY( EditDefaultsOnly )
	UAnimMontage death_Right;

	UPROPERTY( EditDefaultsOnly )
	UAnimMontage death_Left;

	UPROPERTY( EditDefaultsOnly )
	UAnimMontage death_Default;

	UPROPERTY( EditDefaultsOnly )
	TMap<EDamageAnimDirection, UAnimMontage> deathAnims;

	UPROPERTY( EditDefaultsOnly )
	UAnimationAsset soulAnim;

	UPROPERTY( EditDefaultsOnly )
	UAnimMontage destroyOrb1P;

	UPROPERTY( EditDefaultsOnly )
	UAnimMontage destroyOrb1P_alt;

	UPROPERTY( EditDefaultsOnly )
	UAnimMontage destroyOrb1P_alt2;

	UPROPERTY( EditDefaultsOnly )
	UAnimMontage destroyOrb3P;

	UPROPERTY()
	FNCNetBool net_isBeingDestroyed( false );

	UPROPERTY( EditDefaultsOnly )
	UNiagaraSystem destroyOrbShatter;

	UPROPERTY( EditDefaultsOnly )
	UAnimMontage sampleDeathActivityMontage; // Used for timing on ghost mesh appear.

	UPROPERTY( EditDefaultsOnly )
	UNiagaraSystem deathFlash;

	UPROPERTY( EditDefaultsOnly )
	UNiagaraSystem sacrificeOrbDisappear;

	UPROPERTY()
	FNCNetInt net_lastDeathMontageActivity( -1, -1, MAX_int32 );

	UPROPERTY( EditDefaultsOnly )
	UAnimMontage reviveOrb1P;

	UPROPERTY( EditDefaultsOnly )
	UAnimMontage reviveOrbFast1P;

	UPROPERTY( EditDefaultsOnly )
	UAnimMontage reviveOrb3P;

	UPROPERTY( EditDefaultsOnly )
	UAnimMontage reviveOrbFast3P;

	UPROPERTY( EditDefaultsOnly )
	UNiagaraSystem reviveOrbVFX1P;

	UPROPERTY( EditDefaultsOnly )
	UNiagaraSystem reviveOrbVFX3P;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset hudReviveStart;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset hudReviveSustain;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset hudReviveFinish;

	bool initialized = false;

	int32 ReviveSustainAudioEventID;

	UNiagaraComponent orb1PComp;
	UNiagaraComponent orb3PComp;

	UPROPERTY( DefaultComponent )
	UAS_NCAudioComponent friendlyLoop;

	UPROPERTY( DefaultComponent )
	UAS_NCAudioComponent enemyLoop;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset deathOrbAppearSoundFriendly;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset deathOrbAppearSoundEnemy;

	UPROPERTY( EditDefaultsOnly )
	UMaterialInterface reviveAnimationSoulMaterial;

	FNCDeferredScriptAction updateEffectAction;

	UFUNCTION( BlueprintOverride )
	void ServerBeginPlay()
	{
		Super::ServerBeginPlay();

		SharedBeginPlay();

		if ( !IsValid( ownerPlayer ) && !IsValid( ownerTargetDummy ) )
		{
			System::SetTimer( this, n"TryExpire", 0.1, false );
			return;
		}
		else if ( IsValid( ownerPlayer ) )
		{
			ownerPlayer.Server_OnRespawned.AddUFunction( this, n"OnPlayerRespawned" );
			ownerPlayer.Server_OnZiplineSpawnBegin.AddUFunction( this, n"OnPlayerZiplineSpawnBegin" );
			ownerPlayer.OnEndPlay.AddUFunction( this, n"OnOwnerEndPlay" );

			Server_EmitSoundAtLocation_WithSendFlags( deathOrbAppearSoundFriendly, useComponent.GetWorldLocation(), FRotator::ZeroRotator, ownerPlayer, ESendEventFlags::SEND_TO_OWNER | ESendEventFlags::SEND_TO_FRIENDLIES );
			Server_EmitSoundAtLocation_WithSendFlags( deathOrbAppearSoundEnemy, useComponent.GetWorldLocation(), FRotator::ZeroRotator, ownerPlayer, ESendEventFlags::SEND_TO_ENEMIES );
		}

		UAS_RespawnTotemComponent totemComponent = GetOwnerTotemComponent();

		totemComponent.net_isTotem.SetNetValue( true );

		useComponent.OnUsed.AddUFunction( this, n"OnUsed" );

		useComponent.OnExtendedUseStarted.AddUFunction( this, n"OnExtendedUseStarted" );
		useComponent.OnExtendedUseEnded.AddUFunction( this, n"OnExtendedUseEnded" );

		sacrificeUseComponent.OnUsed.AddUFunction( this, n"OnSacrificeUsed" );

		StartExpire();
	}

	UFUNCTION()
	private void OnIsBeingDestroyedChanged( bool oldValue, bool newValue )
	{
		if ( newValue )
		{
			effectComponent.Deactivate();
			effectSyncedComponent.Activate();
		}
		else
		{
			effectComponent.Activate();
			effectSyncedComponent.Deactivate();
		}
	}

	UFUNCTION()
	private void OnAnyPlayerChangedTeam( const ANCPlayerCharacter PlayerState, int OldTeam, int NewTeam )
	{
		if ( !IsValid( ownerPlayer ) )
			return;

		if ( PlayerState != ownerPlayer )
			return;

		UpdateEffectColor();
	}

	void UpdateEffectColor()
	{
		updateEffectAction.ExecuteOnceLater( ENCDeferredScriptActionExecutionTime::UpdateGameObjectsBeforeHUD );
	}

	UFUNCTION()
	void UpdateEffectColorDeferred()
	{
		ANCPlayerCharacter localPlayer = Client_GetLocalPawn();
		int localTeam				   = localPlayer.GetTeam();

		bool isFriendly = IsFriendly( teamComponent.GetTeam(), localTeam );

		FLinearColor color = GetDeathPerformanceColor( isFriendly );

		if ( !net_isBeingDestroyed && !net_isBringCarried )
		{
			if ( isFriendly )
			{
				friendlyLoop.Activate();
				enemyLoop.Deactivate();
			}
			else
			{
				friendlyLoop.Deactivate();
				enemyLoop.Activate();
			}
		}
		else
		{
			friendlyLoop.Deactivate();
			enemyLoop.Deactivate();
		}

		if ( net_isBringCarried )
		{
			ghostMesh.SetVisibility( false );
		}

		effectComponent.SetNiagaraVariableLinearColor( "Color", color );
		effectSyncedComponent.SetNiagaraVariableLinearColor( "Color", color );

		AActor owner = IsValid( ownerPlayer ) ? ownerPlayer : ownerTargetDummy;
		if ( IsValid( owner ) && !initialized )
		{
			FGhostMeshPerformanceData data;
			data.animClass	   = ghostMeshAnimClass;
			data.color		   = color;
			data.deathFlash	   = deathFlash;
			data.ghostMaterial = soulMaterial;
			data.meshComponent = ghostMesh;
			data.owner		   = owner;

			UAnimMontage deathToPlay;
			if ( IsValid( owner ) && net_lastDeathMontageActivity > -1 )
			{
				int index = net_lastDeathMontageActivity;

				if ( index >= 0 )
				{
					EDamageAnimDirection direction = EDamageAnimDirection( index );
					if ( deathAnims.Contains( direction ) )
					{
						deathToPlay = deathAnims[direction];
					}
				}

				if ( !IsValid( deathToPlay ) )
				{
					deathToPlay = death_Default;
				}

				data.animToPlay = deathToPlay;

				float duration = CreateGhostMesh( data );
				if ( duration >= 0 )
				{
					System::SetTimer( this, n"PlayIdle", duration, false );
				}

				UNiagaraComponent c = Client_SpawnEffectAtLocation_OneShot( deathFlash, ghostMesh.GetSocketLocation( n"spine_chest" ) );
				c.SetNiagaraVariableLinearColor( "Color", color );
			}

			initialized = true;
		}

		if ( isFriendly )
		{
			effectComponent.SetNiagaraVariableInt( "user.TeamVis", 1 );
			effectSyncedComponent.SetNiagaraVariableInt( "user.TeamVis", 1 );
		}
		else
		{
			effectComponent.SetNiagaraVariableInt( "user.TeamVis", 0 );
			effectSyncedComponent.SetNiagaraVariableInt( "user.TeamVis", 0 );
		}
	}

	UFUNCTION()
	private void PlayIdle()
	{
		if ( IsValid( ghostMesh ) )
		{
			UAS_RespawnTotemAnimInstance animInst = Cast<UAS_RespawnTotemAnimInstance>( ghostMesh.AnimInstance );
			if ( IsValid( animInst ) )
			{
				animInst.SetPlayIdle();
			}
		}
	}

	UFUNCTION( BlueprintOverride )
	void EndPlay( EEndPlayReason EndPlayReason )
	{
		Super::EndPlay( EndPlayReason );

		UAS_RespawnTotemComponent totemComponent = GetOwnerTotemComponent();
		if ( IsServer() )
		{
			totemComponent.net_isTotem.SetNetValue( false );
		}

		if ( isRespawning )
		{
			totemComponent.net_reviveBarStartTime.SetNetValue( 0 );
			totemComponent.net_reviveBarEndTime.SetNetValue( 0 );
			totemComponent.net_isBeingRevived.SetNetValue( false );
			totemComponent.net_isTotem.SetNetValue( false );

			if ( IsValid( net_currentPlayerUser.GetPlayer() ) )
			{
				totemComponent.net_reviveBarStartTime.SetNetValue( 0 );
				totemComponent.net_reviveBarEndTime.SetNetValue( 0 );
				totemComponent.net_isBeingRevived.SetNetValue( false );
			}
		}

		ScriptCallbacks().server_onRespawnUnavailable.Broadcast( ownerPlayer );
		ScriptCallbacks().OnRespawnTotemDestroyed.Broadcast( this );
	}

	UFUNCTION()
	private void OnOwnerEndPlay( AActor Actor, EEndPlayReason EndPlayReason )
	{
		Expire();
	}

	bool isRespawning;

	UFUNCTION()
	private void OnExtendedUseEnded( UUsableItemComponent usedComponent, ANCPlayerCharacter playerUser, bool isNormalUse, EUseInterruptedReason interruptReason )
	{
		onExtendedUseEndedSignal.Emit();
		net_currentPlayerUser.SetNetValue( nullptr );
	}

	UFUNCTION()
	private void OnExtendedUseStarted( UUsableItemComponent component, ANCPlayerCharacter playerUser,
							   bool isNormalUseInput )
	{
		ERespawnTotemUseCase useCase = GetUseCase( playerUser );

		if ( useCase == ERespawnTotemUseCase::RESPAWN )
		{
		}

		net_currentPlayerUser.SetNetValue( playerUser );
	}

	void StartExpire()
	{
		expireTimer								 = System::SetTimer( this, n"TryExpire", TOTEM_LIFETIME, false );
		UAS_RespawnTotemComponent totemComponent = GetOwnerTotemComponent();
		totemComponent.net_totemStartTime.SetNetValue( GetTimeMilliseconds() );
		totemComponent.net_totemEndTime.SetNetValue( TO_MILLISECONDS( TOTEM_LIFETIME ) + GetTimeMilliseconds() );
	}

	UFUNCTION( BlueprintOverride )
	void ClientBeginPlay()
	{
		Super::ClientBeginPlay();

		updateEffectAction.BindUFunction( this, n"UpdateEffectColorDeferred" );

		SharedBeginPlay();
		net_isBringCarried.OnReplicated().AddUFunction( this, n"OnIsBeingCarriedChanged" );
		NCNetComponent.OnPostReplication.AddUFunction( this, n"OnPostReplicationFinished" );
		ClientCallbacks().OnPlayerChangedTeam.AddUFunction( this, n"OnAnyPlayerChangedTeam" );
		net_isBeingDestroyed.OnReplicated().AddUFunction( this, n"OnIsBeingDestroyedChanged" );
		teamComponent.onTeamChanged.AddUFunction( this, n"OnNetTeamIDChanged" );
		net_lastDeathMontageActivity.OnReplicated().AddUFunction( this, n"OnDeathActivitySet" );
		ghostMesh.SetVisibility( false );

		useComponent.GetUseHintDelegate.BindUFunction( this, n"GetUseHint" );
		sacrificeUseComponent.GetUseHintDelegate.BindUFunction( this, n"GetUseHint" );

		Anim().animNotifyCallbacks.cl_OnSpawnActorFromNotify.AddUFunction( this, n"OnSpawnAnimatedActor" );
	}

	UFUNCTION()
	private void OnSpawnAnimatedActor( AActor spawnedActor, USkeletalMeshComponent meshComp, UAnimSequenceBase animation, const UAS_AnimNotifyTrackerBase notifyTracker )
	{
		//	cast the animNotify to your custom child class to get the custom vars off it
		const UAnimNotify_SpawnAnimatedMesh object = Cast<UAnimNotify_SpawnAnimatedMesh>( notifyTracker.GetAnimNotify() );
		if ( !IsValid( object ) )
			return;

		if ( notifyTracker.GetCustomName() != n"soul" )
			return;

		USkeletalMeshComponent mComponent = spawnedActor.GetSkeletalMeshComponent();
		int numMaterials				  = mComponent.GetNumMaterials();
		for ( int i = 0; i < numMaterials; i++ )
		{
			mComponent.SetMaterial( i, reviveAnimationSoulMaterial );
		}
	}

	UFUNCTION()
	private void OnNetTeamIDChanged( int oldValue, int newValue )
	{
		// There was an issue in builds only where the friendly orb would show up as Red.
		UpdateEffectColor();
	}

	UFUNCTION()
	private void OnDeathActivitySet( int oldValue, int newValue )
	{
		ghostMesh.SetVisibility( true );
		UpdateEffectColor();
	}

	UFUNCTION()
	private void OnIsBeingCarriedChanged( bool oldValue, bool newValue )
	{
		UpdateIsBeingCarried();
	}

	UFUNCTION()
	private void OnPostReplicationFinished( const FNCNetReplicationChanges&in changes )
	{
		if ( !changes.ChangedProperties.Contains( n"net_isBringCarried" ) )
			return;

		if ( net_isBringCarried )
		{
			AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( net_carrier.GetPlayer() );
			mesh.SetCollisionEnabled( ECollisionEnabled::NoCollision );
			effectComponent.SetVisibility( false );
			if ( IsValid( asPlayer ) )
			{
				AAS_PlayerEntity localPlayer		  = Client_GetLocalASPawn();
				UNCSkeletalMeshComponent bindUserArms = asPlayer.GetProxyArmsComponent();
				if ( !IsValid( orb1PComp ) && localPlayer == asPlayer )
				{
					orb1PComp = Client_SpawnEffectOnEntity_Looping( reviveOrbVFX1P, asPlayer );
					orb1PComp.AttachToComponent( bindUserArms, n"Lf_hand_prop", EAttachmentRule::SnapToTarget );
					FTransform WorldTransform3P = this.effectComponent.GetWorldTransform();
					orb1PComp.SetWorldTransform( WorldTransform3P );
					UAS_NiagaraCompLerpToAnimStartThread thread = Cast<UAS_NiagaraCompLerpToAnimStartThread>( CreateThread( UAS_NiagaraCompLerpToAnimStartThread::StaticClass(), Anim() ) );
					thread.Init( orb1PComp, 0.5, WorldTransform3P );
				}
				if ( !IsValid( orb3PComp ) && localPlayer != asPlayer )
				{
					UNCSkeletalMeshComponent user3PMesh = asPlayer.GetPlayerMesh3P();
					if ( IsValid( asPlayer ) && IsValid( localPlayer ) )
					{
						bool isFriendly = asPlayer.GetTeam() == localPlayer.GetTeam();
						orb3PComp		= Client_SpawnEffectOnEntity_Looping( reviveOrbVFX3P, asPlayer );
						orb3PComp.AttachToComponent( user3PMesh, n"Lf_hand_prop", EAttachmentRule::SnapToTarget );
						FLinearColor color;
						if ( isFriendly )
						{
							color = FLinearColor( 0, 0.4, 1.0 );
						}
						else
						{
							color = FLinearColor::Red;
						}
						orb3PComp.SetNiagaraVariableLinearColor( "Color", color );
					}
				}
			}
		}
		else
		{
			effectComponent.SetVisibility( true );

			if ( IsValid( orb1PComp ) )
				orb1PComp.DestroyComponent( orb1PComp );
			if ( IsValid( orb3PComp ) )
				orb3PComp.DestroyComponent( orb3PComp );
		}
	}

	void UpdateIsBeingCarried()
	{
		if ( !IsClient() )
			return;

		UpdateEffectColor();

		// Update revive sustain audio
		if ( !net_isBringCarried )
		{
			Client_StopSound( ReviveSustainAudioEventID );
			return;
		}

		if ( ReviveSustainAudioEventID > 0 )
		{
			Client_StopSound( ReviveSustainAudioEventID );
		}

		// Ensure neither reviver nor revivee hear 3P sustain
		ANCPlayerCharacter localPlayer = Client_GetLocalPawn();
		ANCPlayerCharacter revivee	   = GetOwnerPlayer();
		if ( localPlayer == revivee )
			return;

		ANCPlayerCharacter reviver = net_carrier.GetPlayer();
		if ( reviver == nullptr )
			return;
		FAudioResultData resultData = Client_EmitSoundOnEntity( Audio().reviveSustainSound, reviver );
		ReviveSustainAudioEventID	= resultData.EventID;
	}

	void SharedBeginPlay()
	{
		ownerPlayer = GetOwnerPlayer();

		// if ( IsClient() )
		//{
		ownerTargetDummy = Cast<AAS_TargetDummy_Human>( net_dummyOwner.GetEntity() );
		//}

		DropToFloorComponent.AddIgnoredActor( Owner );
		DropToFloorComponent.AddIgnoredActor( this );

		int numMaterials = mesh.GetNumMaterials();
		defaultMaterials.Empty();
		for ( int i = 0; i < numMaterials; i++ )
		{
			defaultMaterials.Add( mesh.GetMaterial( i ) );
		}

		TOTEM_LIFETIME = GameModeDefaults().RespawnRules_RespawnTime;

		useComponent.GetUseDurationDelegate.BindUFunction( this, n"GetUseDuration" );
		useComponent.GetUsabilityStateDelegate.BindUFunction( this, n"GetUsabilityState" );

		sacrificeUseComponent.GetUseDurationDelegate.BindUFunction( this, n"GetUseDuration" );
		sacrificeUseComponent.GetUsabilityStateDelegate.BindUFunction( this, n"GetUsabilityState_Sacrifice" );

		ScriptCallbacks().OnRespawnTotemCreated.Broadcast( this );
	}

	UFUNCTION()
	private FInputUsabilityStates GetUsabilityState( const UUsableItemComponent Component, const ANCPlayerCharacter PlayerUser )
	{
		FInputUsabilityStates usabilityStates;
		ERespawnTotemUseCase useCase = GetUseCase( PlayerUser );
		if ( useCase == ERespawnTotemUseCase::SACRIFICE )
			return usabilityStates;

		if ( PlayerUser.Passives().HasPassive( GameplayTags::Classes_Passives_BusyScriptedAnim ) )
			return usabilityStates;

		ANCPlayerCharacter current = net_currentPlayerUser.GetPlayer();
		if ( current == nullptr || current == PlayerUser )
		{
			usabilityStates.NormalInstantInput = EUsabilityState::USABLE;
			return usabilityStates;
		}
		return usabilityStates;
	}

	UFUNCTION()
	private FInputUsabilityStates GetUsabilityState_Sacrifice( const UUsableItemComponent Component, const ANCPlayerCharacter PlayerUser )
	{
		FInputUsabilityStates usabilityStates;
		ERespawnTotemUseCase useCase = GetUseCase( PlayerUser );
		if ( useCase != ERespawnTotemUseCase::SACRIFICE )
			return usabilityStates;

		ANCPlayerCharacter current = net_currentPlayerUser.GetPlayer();
		if ( current == nullptr || current == PlayerUser )
		{
			usabilityStates.NormalInstantInput = EUsabilityState::USABLE;
			return usabilityStates;
		}
		return usabilityStates;
	}

	UFUNCTION()
	private FText GetUseHint( const UUsableItemComponent Component, const ANCPlayerCharacter PlayerUser, const EUsabilityState UsabilityState )
	{
		ERespawnTotemUseCase useCase = GetUseCase( PlayerUser );

		switch ( useCase )
		{
			case ERespawnTotemUseCase::SACRIFICE:
				return Passive_Sacrifice().GetPlayerUseHint();
			case ERespawnTotemUseCase::DENY:
				return GetLocalizedText( Localization::Respawn, "respawn_destroy" );
			case ERespawnTotemUseCase::RESPAWN:
				return GetLocalizedText( Localization::Respawn, "respawn_respawn" );
			case ERespawnTotemUseCase::PROTECTED:
				return GetLocalizedText( Localization::Character_Koldo, "passive_protected" );
		}
	}

	ERespawnTotemUseCase GetUseCase( const ANCPlayerCharacter playerUser )
	{
		int ownerTeam = IsValid( ownerPlayer ) ? ownerPlayer.GetTeam() : ownerTargetDummy.GetTeam();

		if ( IsFriendly( ownerTeam, playerUser.GetTeam() ) )
		{
			return ERespawnTotemUseCase::RESPAWN;
		}
		else
		{
			AAS_PlayerEntity protector = Passive_GuardianAngel().IsTotemProtected( this, playerUser.GetTeam() );

			bool isAlliedWithProtector = IsValid( protector ) && IsFriendly( protector.GetTeam(), playerUser.GetTeam() );
			if ( IsValid( protector ) && !isAlliedWithProtector )
				return ERespawnTotemUseCase::PROTECTED;
			else if ( playerUser.Passives().HasPassive( GameplayTags::Classes_Passives_Sacrifice ) )
				return ERespawnTotemUseCase::SACRIFICE;
			else
				return ERespawnTotemUseCase::DENY;
		}
	}

	UFUNCTION()
	private float32 GetUseDuration( const UUsableItemComponent component,
							const ANCPlayerCharacter playerUser, bool isNormalUseInput )
	{
		if ( !isNormalUseInput )
			return 0;

		ERespawnTotemUseCase useCase = GetUseCase( playerUser );

		switch ( useCase )
		{
			case ERespawnTotemUseCase::SACRIFICE:
			{
				/*if ( GameModeDefaults().GamemodeRules_RespawnTotemExecuteHealsToFull )
				{
					return TOTEM_DESTROY_TIME_HEAL;
				}

				return TOTEM_DESTROY_TIME;*/
				return 0.0;
			}

			default:
				return 0.0;
		}
	}

	UFUNCTION()
	private void TryExpire()
	{
		if ( isSacrificeActive || IsValid( net_currentPlayerUser.GetPlayer() ) || isRespawning )
			System::SetTimer( this, n"TryExpire", 0.1, false );
		else
			Expire();
	}

	void Expire()
	{
		Destroy();
	}

	UFUNCTION()
	void TotemDropDeathbox( ANCPlayerCharacter Pawn )
	{
		if ( !respawnActivated && IsValid( ownerPlayer ) )
			ownerPlayer.saveLootOnDeathComponent.DropDeathboxOrLooseLoot( isSuicide, GetActorLocation() );
	}

	UFUNCTION()
	void OnPlayerRespawned( ANCPlayerCharacter Pawn )
	{
		AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( Pawn );

		if ( respawnActivated )
		{
			float32 newShield = 50.0f;
			float32 newHealth = 50.0f;

			if ( respawnUserPlayer != nullptr )
			{
				AAS_PlayerEntity trinketedPlayer = nullptr;
				if ( respawnUserPlayer.HasTrinketPassive( GameplayTags::Classes_Passives_Reviver ) )
					trinketedPlayer = respawnUserPlayer;
				else if ( IsValid( player ) && player.HasTrinketPassive( GameplayTags::Classes_Passives_Reviver ) )
					trinketedPlayer = player;

				if ( trinketedPlayer != nullptr )
				{
					trinketedPlayer.Server_OnTrinketUsed();

					newShield = ownerPlayer.GetMaxShieldHealth();
					newHealth = ownerPlayer.GetMaxHealth();
				}
			}

			ownerPlayer.respawnTotem_lastReviveTimeMS = ownerPlayer.GetTimeMilliseconds();

			// Print( f"new shield {newShield}" );
			ownerPlayer.SetHealth( newHealth );
			ownerPlayer.SetShieldHealth( newShield );
			ownerPlayer.healthRegenComponent.OnDamaged( FDamageInfo() );
		}

		Destroy();
	}

	UFUNCTION()
	void OnPlayerZiplineSpawnBegin( ANCPlayerCharacter Pawn )
	{
		Destroy();
	}

	// TODO: Dummy version
	void RespawnOwner( ANCPlayerCharacter playerUser )
	{
		respawnActivated  = true;
		respawnUserPlayer = Cast<AAS_PlayerEntity>( playerUser );

		UAS_RespawnTotemComponent totemComponent = GetOwnerTotemComponent();
		totemComponent.net_reviveBarStartTime.SetNetValue( 0 );
		totemComponent.net_reviveBarEndTime.SetNetValue( 0 );
		totemComponent.net_isBeingRevived.SetNetValue( false );
		totemComponent.net_isTotem.SetNetValue( false );

		AAS_PlayerEntity asPlayerUser					= Cast<AAS_PlayerEntity>( playerUser );
		UAS_RespawnTotemComponent reviverTotemComponent = asPlayerUser.TotemComponent();
		reviverTotemComponent.net_reviveBarStartTime.SetNetValue( 0 );
		reviverTotemComponent.net_reviveBarEndTime.SetNetValue( 0 );
		reviverTotemComponent.net_isBeingRevived.SetNetValue( false );

		FRotator rot = GetActorRotation();
		rot.Pitch	 = 0;
		rot.Roll	 = 0;

		TArray<AActor> ignoreActors;
		ignoreActors.Add( playerUser );
		ignoreActors.Add( this );
		ignoreActors.Add( ownerPlayer );
		ignoreActors.Add( ownerTargetDummy );

		float halfHeight	= 0;
		float capsuleRadius = 0;

		if ( IsValid( ownerPlayer ) )
		{
			halfHeight	  = ownerPlayer.CapsuleComponent.GetScaledCapsuleHalfHeight();
			capsuleRadius = ownerPlayer.CapsuleComponent.GetScaledCapsuleRadius();

			ownerPlayer.PlayBattleChatter( GameplayTags::Audio_VO_BattleChatter_Revive_Response );
		}
		else if ( IsValid( ownerTargetDummy ) )
		{
			// quick hardcode bc seems tricky to get reliable / useful versions of these numbers w/o a capsule component
			halfHeight	  = 80;
			capsuleRadius = 80;
		}

		ScriptCallbacks().server_onRespawnTotemRevived.Broadcast( this, asPlayerUser );

		FVector start			= playerUser.GetActorLocation();
		FVector end				= GetActorLocation();
		FHitResult capsuleTrace = CapsuleTraceSingle( start, end, capsuleRadius, halfHeight, ETraceTypeQuery::WeaponFine, true, ignoreActors, true );
		FVector endPoint		= GetEndLocationFromTrace( capsuleTrace );
		asPlayerUser.saveLootOnDeathComponent.SetPlayerWasRevived();

		if ( IsValid( ownerPlayer ) )
		{
			GetServerScript().RespawnCharacterAtPoint( ownerPlayer, endPoint, rot );
		}
		else if ( IsValid( ownerTargetDummy ) )
		{
			ownerTargetDummy.Respawn();
			ownerTargetDummy.ShowDummy();
			Destroy();
		}
	}

	UAS_PlayerHoldTotemThread currentReviveThread;

	UFUNCTION()
	private void OnUsed( UUsableItemComponent component, ANCPlayerCharacter playerUser )
	{
		ERespawnTotemUseCase useCase = GetUseCase( playerUser );

		switch ( useCase )
		{
			case ERespawnTotemUseCase::RESPAWN:
			{
				currentReviveThread = Cast<UAS_PlayerHoldTotemThread>( CreateThread( UAS_PlayerHoldTotemThread::StaticClass(), this ) );
				currentReviveThread.Init( playerUser, this );
				playerUser.PlayBattleChatter( GameplayTags::Audio_VO_BattleChatter_Revive_Teammate );
			}
			break;

			case ERespawnTotemUseCase::DENY:
			{
				Thread( this, n"StartSyncedDestroyMontage", playerUser );
				break;
			}

			default:
				break;
		}
	}

	UFUNCTION()
	private void OnSacrificeUsed( UUsableItemComponent component, ANCPlayerCharacter playerUser )
	{
		ERespawnTotemUseCase useCase = GetUseCase( playerUser );

		switch ( useCase )
		{
			case ERespawnTotemUseCase::SACRIFICE:
				Passive_Sacrifice().OnTotemUsed( component, playerUser, this );
				break;

			default:
				break;
		}
	}

	UFUNCTION()
	private void OnSacrificeExtendedUseStart( UUsableItemComponent component, ANCPlayerCharacter playerUser,
									  bool isNormalUseInput )
	{
		ERespawnTotemUseCase useCase = GetUseCase( playerUser );
		switch ( useCase )
		{
			case ERespawnTotemUseCase::SACRIFICE:
				Passive_Sacrifice().OnTotemExtendedUseStarted( component, playerUser, this );
				break;

			default:
				break;
		}
	}

	UFUNCTION()
	private void OnSacrificeExtendedUseEnded( UUsableItemComponent component, ANCPlayerCharacter playerUser )
	{
		ERespawnTotemUseCase useCase = GetUseCase( playerUser );
		switch ( useCase )
		{
			case ERespawnTotemUseCase::SACRIFICE:
			{
				Passive_Sacrifice().OnTotemExtendedUseEnded( component, playerUser, this );
				break;
			}

			default:
				break;
		}
		onExtendedUseEndedSignal.Emit();
	}

	UFUNCTION()
	void StartSyncedDestroyMontage( UNCCoroutine co, ANCPlayerCharacter playerUser )
	{
		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>( playerUser );

		ScriptAssert( IsValid( asPlayer ), "INVALID PLAYER?" );
		float distanceScalar = 1.25;
		if ( asPlayer.IsSprinting() )
			distanceScalar = 2;

		float32 lerpMove = 0.15;
		float32 lerpView = 0.15;

		float halfHeight	= playerUser.CapsuleComponent.GetScaledCapsuleHalfHeight();
		float capsuleRadius = playerUser.CapsuleComponent.GetScaledCapsuleRadius();
		FVector start		= playerUser.GetActorLocation();
		FVector orbPos		= useComponent.GetWorldLocation();
		FVector dir			= FVector( start.X - orbPos.X, start.Y - orbPos.Y, 0 );
		dir.Normalize();
		dir			= dir * ( capsuleRadius * distanceScalar );
		FVector end = FVector( orbPos.X + dir.X, orbPos.Y + dir.Y, start.Z ); // + playerUser.GetActorForwardVector() * dist2D;
		TArray<AActor> ignoreActors;
		ignoreActors.Add( playerUser );
		ignoreActors.Add( this );
		float zHeight		   = 158; // playerUser.GetEyeLocation().Z - playerUser.GetFootPosition().Z;
		FHitResult outHit	   = CapsuleTraceSingleByProfile( start, end, capsuleRadius, halfHeight, n"BlockAllDynamic", true, ignoreActors, true, EDrawDebugTrace::None, FLinearColor::Gray, FLinearColor::White, 5 );
		FVector lerpLoc		   = outHit.bBlockingHit ? outHit.ImpactPoint : end;
		FHitResult groundTrace = LineTraceSingleByProfile( lerpLoc + FVector( 0, 0, 1 ), lerpLoc - FVector( 0, 0, 150 ), n"BlockAllDynamic", true, ignoreActors, true );
		if ( groundTrace.bBlockingHit )
			lerpLoc = groundTrace.ImpactPoint;
		FVector lerpEyePos				 = lerpLoc + FVector( 0, 0, zHeight );
		FRotator attackerRot			 = ( orbPos - lerpEyePos ).Rotation(); // + FRotator(-2,0,0);
		UAnimMontage destroyOrb1PMontage = Get1PDestroyAnim();
		// FSyncedMontage1P3PResult result = asPlayer.PlayNetSyncedMontage1P3P(destroyOrb1PMontage, destroyOrb3P, lerpLoc, attackerRot, lerpMove, lerpView );
		FSyncedMontage1P3PResult result = asPlayer.PlayScriptedNetSyncedMontage1P3P( destroyOrb1PMontage, destroyOrb3P, lerpLoc, attackerRot, lerpMove, lerpView );
		// Anim().animNotifyCallbacks.GetOrMakeDelegate(weapon.GetWeaponViewModelMeshComponent(), Passive_Shifter().tacticalStealthDeploy, n"StartStealth" ).AddUFunction( this, n"OnScriptAnimNotify_StartStealth");
		asPlayer.OnDestroyOrbStartedSignal.Emit();
		Anim().animNotifyCallbacks.OnAnimEnd( result.animatedMesh1P, destroyOrb1P ).AddUFunction( this, n"OnScriptAnimNotify_AnimEnd" );
		Anim().animNotifyCallbacks.GetOrMakeDelegate( result.animatedMesh1P, destroyOrb1P, n"DestroyOrb" ).AddUFunction( this, n"OnScriptAnimNotify_DestroyOrb" );
		net_isBeingDestroyed.SetNetValue( true );
		effectComponent.SetVisibility( false );
		effectSyncedComponent.SetVisibility( true );
		// Anim().animNotifyCallbacks.GetOrMakeDelegate(result.animatedMesh1P, GetMontage1P(), n"Impact" ).AddUFunction( this, n"OnScriptAnimNotify_OnImpact");

		// Anim().animNotifyCallbacks.GetOrMakeDelegate(result.animatedMesh1P, GetMontage1P(), n"Impact" ).AddUFunction( this, n"OnScriptAnimNotify_OnPlantSuccess");
		// Anim().animNotifyCallbacks.OnDeath(result.animatedMesh1P, GetMontage1P() ).AddUFunction( this, n"OnScriptAnimNotify_OnPlantFail");
	}

	UFUNCTION()
	private void OnScriptAnimNotify_AnimEnd( USkeletalMeshComponent meshComp, UAnimSequenceBase animation, const UAS_AnimNotifyTrackerBase notifyTracker )
	{
		net_isBeingDestroyed.SetNetValue( false );
		effectComponent.Activate();
		effectSyncedComponent.Deactivate();
	}

	UAnimMontage Get1PDestroyAnim()
	{
		int chance = Math::RandRange( 0, 100 );
		if ( chance > 50 )
		{
			return destroyOrb1P;
		}
		else if ( chance > 25 )
		{
			return destroyOrb1P_alt;
		}
		return destroyOrb1P_alt2;
	}

	UFUNCTION()
	private void OnScriptAnimNotify_DestroyOrb( USkeletalMeshComponent meshComp, UAnimSequenceBase animation, const UAS_AnimNotifyTrackerBase notifyTracker )
	{
		ANCPlayerCharacter playerOwner = Cast<ANCPlayerCharacter>( Anim().GetPlayerFromMeshComponent( meshComp ) );
		DestroyOrb( playerOwner );
	}

	void DestroyOrb( ANCPlayerCharacter playerUser )
	{
		// Server_EmitSoundAtLocation( denyTotemSound, this.GetActorLocation() );

		AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( playerUser );
		ScriptCallbacks().server_onRespawnTotemDenied.Broadcast( this, player );
		player.PlayBattleChatter( GameplayTags::Audio_VO_BattleChatter_KilledEnemyOrb );
		Server_SpawnEffectAtLocation_OneShot( destroyOrbShatter, useComponent.GetWorldLocation(), GetActorRotation() );

		Expire();
	}

	void PlaySacrificeOrbDisappear()
	{
		Server_SpawnEffectAtLocation_OneShot( sacrificeOrbDisappear, useComponent.GetWorldLocation(), GetActorRotation() );
	}

	UAS_RespawnTotemComponent GetOwnerTotemComponent()
	{
		if ( IsValid( ownerPlayer ) )
		{
			return ownerPlayer.TotemComponent();
		}
		else if ( IsValid( ownerTargetDummy ) )
		{
			return ownerTargetDummy.TotemComponent();
		}

		return nullptr;
	}
}

class UAS_PlayerHoldTotemThread : UAS_Thread
{
	AAS_PlayerEntity usingPlayer;
	AActor totemOwner;
	AAS_PlayerEntity totemOwner_Player;
	AAS_TargetDummy totemOwner_Dummy;
	AAS_RespawnTotem totem;
	AFXActor revive1p;
	AFXActor revive3p;

	void Init( ANCPlayerCharacter inPlayer, AAS_RespawnTotem inTotem )
	{
		usingPlayer		  = Cast<AAS_PlayerEntity>( inPlayer );
		totem			  = inTotem;
		totemOwner_Player = totem.GetOwnerPlayer();
		totemOwner_Dummy  = totem.ownerTargetDummy;
		totemOwner		  = IsValid( totemOwner_Player ) ? totemOwner_Player : totemOwner_Dummy;
		Start();
	}

	int statusEffectHandle = -1;

	void OnThreadStart( UNCCoroutine co ) override
	{
		Super::OnThreadStart( co );

		co.EndOnDestroyed( usingPlayer );
		co.EndOnDestroyed( totem );
		co.EndOnDestroyed( totemOwner );
		co.EndOn( usingPlayer, usingPlayer.OnDeathSignal );

		ScriptCallbacks().shared_onPassiveAdded[GameplayTags::Classes_Passives_BusyScriptedAnim].AddUFunction( this, n"OnBusyScriptedAnim" );

		// 1P sounds for revivee
		if ( IsValid( totemOwner_Player ) )
		{
			Server_EmitSoundOnEntity_WithSendFlags( totem.hudReviveStart, totemOwner_Player, totemOwner_Player, ESendEventFlags::SEND_TO_OWNER );

			ASoundActor hudReviveSutain = Server_EmitSoundOnEntity_WithSendFlags_ReturnEntity( totem.hudReviveSustain, totemOwner_Player, totemOwner_Player, ESendEventFlags::SEND_TO_OWNER );
			co.AutoDestroy( hudReviveSutain );
		}

		totem.isRespawning = true;
		totem.useComponent.DisableUsable();
		usingPlayer.SetCanUse( false );

		float32 slowAmount = 0.5;
		int totemUseTime   = GameModeDefaults().RespawnRules_RespawnTotemReviveTimeMS;
		if ( usingPlayer.HasTrinketPassive( GameplayTags::Classes_Passives_Reviver ) )
		{
			FPlayerEquipmentData trinketData = usingPlayer.GetTrinket();
			totemUseTime					 = TO_MILLISECONDS( totem.reviveOrbFast1P.GetPlayLength() );
			slowAmount						 = trinketData.dataFloat2;
		}

		if ( IsValid( totemOwner_Player ) )
		{
			GetServerScript().UpdatePlayerSpectateCamera( totemOwner_Player );
		}

		UAS_RespawnTotemComponent totemComponent = GetOwnerTotemComponent();

		totemComponent.net_reviveBarStartTime.SetNetValue( usingPlayer.GetTimeMilliseconds() );
		totemComponent.net_reviveBarEndTime.SetNetValue( usingPlayer.GetTimeMilliseconds() + totemUseTime );
		totemComponent.net_isBeingRevived.SetNetValue( true );

		UAS_RespawnTotemComponent reviverTotemComponent = usingPlayer.TotemComponent();
		reviverTotemComponent.net_reviveBarStartTime.SetNetValue( usingPlayer.GetTimeMilliseconds() );
		reviverTotemComponent.net_reviveBarEndTime.SetNetValue( usingPlayer.GetTimeMilliseconds() + totemUseTime );
		reviverTotemComponent.net_isBeingRevived.SetNetValue( false );

		// Send 3P start sound to all players EXCEPT revivee
		TArray<ANCPlayerCharacter> receivers3P = GetAllPlayers();
		receivers3P.Remove( totemOwner_Player );
		Server_EmitSoundAtLocation_OnlySendToThesePlayers( Audio().reviveStartSound, totem.GetActorLocation(), FRotator::ZeroRotator, receivers3P, usingPlayer );

		if ( !usingPlayer.Passives().HasPassive( GameplayTags::Classes_Passives_GuardianAngel ) )
			statusEffectHandle = usingPlayer.AddStatusEffect( GameplayTags::StatusEffect_MoveSlow, slowAmount, 999999, 0.0, 0.0 );

		usingPlayer.ScriptHolsterWeapon();
		totem.net_carrier.SetNetValue( usingPlayer );
		totem.net_isBringCarried.SetNetValue( true );
		// totem.SetActorLocation( player.GetFootPosition() + ( player.GetViewRotationFlat().ForwardVector * 32 ) + FVector( 0, 0, 32 ) );
		totem.AttachToActor( usingPlayer, NAME_None, EAttachmentRule::KeepWorld );

		StartSyncedReviveAnimation();

		int endtime = usingPlayer.GetTimeMilliseconds() + totemUseTime + 100;
		ScriptCallbacks().server_onPlayerStartedRevive.Broadcast( totem, usingPlayer );

		while ( usingPlayer.GetTimeMilliseconds() < endtime && usingPlayer.IsUseButtonHeld() )
			co.Wait( 0.1 );

		if ( usingPlayer.GetTimeMilliseconds() < endtime )
		{
			usingPlayer.StopScriptedMontage();
			return;
		}
	}

	UFUNCTION()
	private void OnBusyScriptedAnim( ANCPlayerCharacter _player, FGameplayTag tag )
	{
		if ( usingPlayer == _player && !WasCanceled() )
		{
			ScriptCallbacks().shared_onPassiveAdded[GameplayTags::Classes_Passives_BusyScriptedAnim].Unbind( this, n"OnBusyScriptedAnim" );
			Cancel();
		}
	}

	void OnThreadEnd( FNCCoroutineEndParams params ) override
	{
		Super::OnThreadEnd( params );

		if ( IsValid( totem ) )
			totem.isRespawning = false;

		ScriptCallbacks().server_onPlayerEndedRevive.Broadcast( totem, usingPlayer );

		UAS_RespawnTotemComponent totemComponent = GetOwnerTotemComponent();

		totemComponent.net_reviveBarStartTime.SetNetValue( 0 );
		totemComponent.net_reviveBarEndTime.SetNetValue( 0 );
		totemComponent.net_isBeingRevived.SetNetValue( false );

		UAS_RespawnTotemComponent reviverTotemComponent = usingPlayer.TotemComponent();
		reviverTotemComponent.net_reviveBarStartTime.SetNetValue( 0 );
		reviverTotemComponent.net_reviveBarEndTime.SetNetValue( 0 );
		reviverTotemComponent.net_isBeingRevived.SetNetValue( false );

		if ( statusEffectHandle != -1 )
			usingPlayer.ClearStatusEffect( statusEffectHandle );
		usingPlayer.ScriptDeployWeapon();
		usingPlayer.SetCanUse( true );
		usingPlayer.StopScriptedMontage();

		if ( IsValid( totem ) )
		{
			totem.useComponent.EnableUsable();

			TArray<AActor> ignoreActors;
			ignoreActors.Add( totem );
			ignoreActors.Add( usingPlayer );
			ignoreActors.Add( totemOwner );

			FVector start	 = usingPlayer.GetPawnViewLocation();
			FVector end		 = totem.GetActorLocation();
			FHitResult trace = SphereTraceSingle( start, end, 32, ETraceTypeQuery::WeaponFine, true, ignoreActors, true );
			// DrawDebugLineTrace( trace );
			if ( trace.bBlockingHit )
			{
				FVector fwd = end - start;
				fwd.Normalize();

				// DrawDebugSphere( end, 20, 5 );
				// DrawDebugSphere( trace.ImpactPoint - fwd * 32, 10, 5 );
				totem.SetActorLocation( start );
			}

			totem.net_isBringCarried.SetNetValue( false );
			totem.net_carrier.SetNetValue( nullptr );
			totem.SetActorLocation( usingPlayer.GetFootPosition() + ( usingPlayer.GetViewRotationFlat().ForwardVector * 32 ) + FVector( 0, 0, 32 ) );
			totem.DetachFromActor( EDetachmentRule::KeepWorld );
			totem.SetActorRotation( usingPlayer.GetViewRotationFlat() );
			totem.DropToFloorComponent.SetThrowDirection( usingPlayer.GetViewRotationFlat().ForwardVector );

			if ( IsValid( totemOwner_Player ) )
			{
				GetServerScript().UpdatePlayerSpectateCamera( totemOwner_Player );
			}
		}
	}

	UFUNCTION()
	FScriptedMontage1P3PResult StartSyncedReviveAnimation()
	{
		bool hasReviveTrinket = usingPlayer.HasTrinketPassive( GameplayTags::Classes_Passives_Reviver );
		FScriptedAnimPlayerSettings test;
		test.SetScripted1PDefault( this );
		test.PassiveBusyScriptedAnim	  = false;
		UAnimMontage revive1pMont		  = hasReviveTrinket ? totem.reviveOrbFast1P : totem.reviveOrb1P;
		UAnimMontage revive3pMont		  = hasReviveTrinket ? totem.reviveOrbFast3P : totem.reviveOrb3P;
		FScriptedMontage1P3PResult result = usingPlayer.PlayNetScriptedMontage1P3PWithPlayerSettings( test, revive1pMont, revive3pMont );
		Anim().animNotifyCallbacks.GetOrMakeDelegate( result.animatedMesh1P, revive1pMont, n"RevivePlayer" ).AddUFunction( this, n"OnScriptAnimNotify_SpawnRevivePlayer" );

		return result;
	}

	UFUNCTION()
	private void OnScriptAnimNotify_SpawnRevivePlayer( USkeletalMeshComponent meshComp, UAnimSequenceBase animation, const UAS_AnimNotifyTrackerBase notifyTracker )
	{
		TArray<ANCPlayerCharacter> receivers3P = GetAllPlayers();

		// 1P sound for revive finish
		if ( IsValid( totemOwner_Player ) )
		{
			receivers3P.Remove( totemOwner_Player );
			Server_EmitSoundOnEntity_WithSendFlags( totem.hudReviveFinish, totemOwner_Player, totemOwner_Player, ESendEventFlags::SEND_TO_OWNER );
		}

		// Send 3P finish sound to all players EXCEPT revivee
		Server_EmitSoundAtLocation_OnlySendToThesePlayers( Audio().reviveEndSound, totem.GetActorLocation(), FRotator::ZeroRotator, receivers3P, totemOwner_Player );

		totem.RespawnOwner( usingPlayer );
		usingPlayer.StopScriptedMontage();
	}

	UAS_RespawnTotemComponent GetOwnerTotemComponent()
	{
		if ( IsValid( totemOwner_Player ) )
		{
			return totemOwner_Player.TotemComponent();
		}
		else if ( IsValid( totemOwner_Dummy ) )
		{
			return totemOwner_Dummy.TotemComponent();
		}

		return nullptr;
	}
}