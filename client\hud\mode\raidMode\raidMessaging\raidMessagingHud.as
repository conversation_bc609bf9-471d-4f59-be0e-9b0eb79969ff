UCLASS( Abstract )
class UAS_RaidMessagingHud : UNCScreenWidget
{
	default StateOnConstruction = EDisplayConstructVisibility::Hidden;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_TeamBaseHealth localTeamHealth;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	protected UAS_TeamBaseHealth enemyTeamHealth;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_TeamRaidState localTeamRaidState;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_TeamRaidState enemyTeamRaidState;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UNCDeferredWidget objectiveMarkersDeferredWidget;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_ShieldBreakerTrackerWidget shieldBreakerTracker;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage criticalAlertBanding;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_CommonTextBlock header;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UCommonRichTextBlock subheader;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_TeamRespawnsWidget localTeamRespawns;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_TeamRespawnsWidget enemyTeamRespawns;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_RaidMessagingShieldbreakerIndicatorWidget shieldbreakerIndicator;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_RaidMessagingShapeWidget raidMessagingShape;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	UOverlay gameModeExtrasOverlay;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UNCDeferredWidget localOvertimeTeamDeferredWidget;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UNCDeferredWidget enemyOvertimeTeamDeferredWidget;

	UPROPERTY( NotVisible, BlueprintHidden, Transient, Meta = ( BindWidgetAnim ) )
	private UWidgetAnimation showCriticalAlert;

	UPROPERTY( NotVisible, BlueprintHidden, Transient, Meta = ( BindWidgetAnim ) )
	private UWidgetAnimation raidStarted;

	UPROPERTY( NotVisible, BlueprintHidden, Transient, Meta = ( BindWidgetAnim ) )
	private UWidgetAnimation inUseAnimation;

	UPROPERTY( NotVisible, BlueprintHidden, Transient, Meta = ( BindWidgetAnim ) )
	private UWidgetAnimation shieldbreakerIndicatorAnimation;

	UPROPERTY( NotVisible, EditDefaultsOnly )
	private UMaterialParameterCollection raidMessagingMpc;

	UPROPERTY( NotVisible, EditDefaultsOnly )
	private UCurveFloat animateToCurve;

	// NEW RAID MESSAGING (WIP)
	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	UCommonVisualAttachment splashMessageContainer;
	UNC_DisplayWidget currentSplashMessage;

	private UMaterialInstanceDynamic criticalAlertMaterial;
	private bool wasShowingCriticalAlert = false;
	private bool hadActiveBombOrPlant = false;
	private bool hasUpdatedText;

	private TOptional<int> optAnimateToStartTimeMs;
	private TOptional<UCommonRichTextBlock> optAnimatingToTextBlock;
	private TOptional<FText> optToText;

	private const FName ALERT_PULSE_ACTIVE_PARAMETER = n"AlertPulseActive";
	private const float LOSS_STATE = -1.0f;
	private const float WIN_STATE = 0.0f;
	private const float SUDDEN_DEATH_STATE = 1.0f;
	private const FName ALERT_STATE_PARAMETER = n"AlertState";
	private const int ANIMATE_TO_DURATION_MS = 225;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		// Before binding any events, ensure that our materials are created
		criticalAlertMaterial = CreateDynamicMaterialFromImageBrush( criticalAlertBanding );
		if ( IsValid( criticalAlertMaterial ) )
		{
			criticalAlertBanding.SetBrushFromMaterial( criticalAlertMaterial );
		}

		UAS_ScriptCallbacks scriptCallbacks = ScriptCallbacks();
		if ( IsValid( scriptCallbacks ) )
		{
			scriptCallbacks.shared_OnRaidStarted.AddUFunction( this, n"OnRaidStarted" );
			scriptCallbacks.shared_OnRaidEnded.AddUFunction( this, n"OnRaidEnded" );
		}

		// On construct, just empty out the header and subheader
		header.SetText( Text::EmptyText );
		subheader.SetText( Text::EmptyText );

		AAS_GameModeBase gameMode = GameModeDefaults();
		if ( IsValid( gameMode ) && gameMode.UI_RaidMessaging_ShowCharacterPortraits )
		{
			AAS_PlayerEntity localPlayer = Client_GetLocalASPawn();
			if ( IsValid( localPlayer ) )
			{
				int teamId = localPlayer.GetTeam();

				localOvertimeTeamDeferredWidget.PreloadWidget();

				UAS_OvertimeTeamWidget localTeamWidget = Cast<UAS_OvertimeTeamWidget>( localOvertimeTeamDeferredWidget.Contents.GetChildAt( 0 ) );
				if ( IsValid( localTeamWidget ) )
				{
					localTeamWidget.SetTeam( teamId );
				}

				enemyOvertimeTeamDeferredWidget.PreloadWidget();

				UAS_OvertimeTeamWidget enemyTeamWidget = Cast<UAS_OvertimeTeamWidget>( enemyOvertimeTeamDeferredWidget.Contents.GetChildAt( 0 ) );
				if ( IsValid( enemyTeamWidget ) )
				{
					enemyTeamWidget.SetTeam( GetOtherTeam( teamId ) );
				}
			}
		}
	}

	void SetShieldBreakerIndicatorMaterial( UMaterialInterface material )
	{
		shieldbreakerIndicator.SetIconMaterial( material );
	}

	void SetShieldBreakerIconParameter( FName key, float32 value )
	{
		shieldbreakerIndicator.SetIconScalarParameter( key, value );
	}

	void SetTrackedTeams( int playerTeam, int enemyTeam )
	{
		localTeamHealth.SetTrackedTeam( playerTeam );
		localTeamRaidState.SetTrackedTeamId( playerTeam );
		localTeamRespawns.SetTrackedTeamId( playerTeam );

		enemyTeamHealth.SetTrackedTeam( enemyTeam );
		enemyTeamRaidState.SetTrackedTeamId( enemyTeam );
		enemyTeamRespawns.SetTrackedTeamId( enemyTeam );

		shieldBreakerTracker.SetTrackedTeams( playerTeam, enemyTeam );
	}

	void SetScoreLimits( int scoreLimits )
	{
		localTeamHealth.SetScoreLimit( scoreLimits );
		enemyTeamHealth.SetScoreLimit( scoreLimits );
	}

	void SetHeaderText( FText headerText, bool animateToText = false )
	{
		// TODO @jmccarty: Check if the text is unique first and animate
		header.SetText( headerText );
	}

	void SetSubheaderText( FText subheaderText, bool animateToText = false )
	{
		FText currentText = subheader.GetText();
		if ( animateToText && !currentText.IsEmpty() && !subheaderText.IsEmpty() && !currentText.IdenticalTo( subheaderText ) )
		{
			// We only want to animate to text when the to and from text is valid
			AnimateToText( subheader, subheaderText );
		}
		else
		{
			// Otherwise just straight up set the text
			subheader.SetText( subheaderText );
		}
	}

	// TODO @jmccarty: Ported from raid and objective text widget
	UAS_PingPongTextThread TriggerSubheaderPingPongThread( FText textA, FText textB )
	{
		UAS_PingPongTextThread pingPongThread = Cast<UAS_PingPongTextThread>( CreateThread( UAS_PingPongTextThread::StaticClass(), this ) );
		if ( IsValid( pingPongThread ) )
		{
			// TODO @jmccarty: Swap this to a rich text block or something generic
			// pingPongThread.Init( subheader, textA, textB );
		}
		return pingPongThread;
	}

	void ShowOrHideShieldbreakerIndicator( bool show )
	{
		if ( shieldbreakerIndicator.IsShown() == show )
			return;

		shieldbreakerIndicator.ShowOrHide( show );
		PlayAnimationForwardOrReverse( show, shieldbreakerIndicatorAnimation );
	}

	void ShowOrHideShieldbreakerTracker( bool show )
	{
		shieldBreakerTracker.ShowOrHide( show );
	}

	void ShowOrHideCharacterPortraits( bool show )
	{
		AAS_GameModeBase gameMode = GameModeDefaults();
		if ( IsValid( gameMode ) && gameMode.UI_RaidMessaging_ShowCharacterPortraits )
		{
			localOvertimeTeamDeferredWidget.ShowOrHide( show );
			enemyOvertimeTeamDeferredWidget.ShowOrHide( show );
		}
	}

	void ShowOrHideObjectiveMarkers( bool show )
	{
		objectiveMarkersDeferredWidget.ShowOrHide( show );
	}

	void ShowOrHideRaidingTeamRaidState( bool show )
	{
		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( !IsValid( player ) )
			return;

		if ( show )
		{
			if ( IsTeamRaiding( player.GetTeam() ) )
			{
				localTeamRaidState.ShowOrHide( show );
			}
			else
			{
				enemyTeamRaidState.ShowOrHide( show );
			}
		}
		else
		{
			localTeamRaidState.ShowOrHide( show );
			enemyTeamRaidState.ShowOrHide( show );
		}
	}

	void ShowOrHideRaidingTeamRespawns( bool show )
	{
		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( !IsValid( player ) )
			return;

		if ( show )
		{
			if ( IsTeamRaiding( player.GetTeam() ) )
			{
				localTeamRespawns.ShowOrHide( show );
			}
			else
			{
				enemyTeamRespawns.ShowOrHide( show );
			}
		}
		else
		{
			localTeamRespawns.ShowOrHide( show );
			enemyTeamRespawns.ShowOrHide( show );
		}
	}

	void CheckForCriticalAlert()
	{
		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( !IsValid( player ) )
			return;

		bool isRaiding				 = IsTeamRaiding( player.GetTeam() );
		bool shouldShowCriticalAlert = ShouldShowCriticalAlert();

		SetWidgetVisibilitySafe( criticalAlertBanding, shouldShowCriticalAlert ? ESlateVisibility::SelfHitTestInvisible : ESlateVisibility::Collapsed );

		if ( shouldShowCriticalAlert && IsValid( raidMessagingMpc ) )
		{
			// If we have found that we should show the critical alert, determine which one to show
			float criticalAlertState = isRaiding ? WIN_STATE : LOSS_STATE;
			Material::SetScalarParameterValue( raidMessagingMpc, ALERT_STATE_PARAMETER, criticalAlertState );
		}

		if ( !wasShowingCriticalAlert && shouldShowCriticalAlert )
		{
			PlayAnimationForward( showCriticalAlert, 5.0f );
			wasShowingCriticalAlert = shouldShowCriticalAlert;
		}
		else if ( wasShowingCriticalAlert && !shouldShowCriticalAlert )
		{
			PlayAnimationReverse( showCriticalAlert, 5.0f );
			wasShowingCriticalAlert = shouldShowCriticalAlert;
		}

		if ( IsValid( raidMessagingMpc ) )
		{
			// When the marker is in use on a bomb plant, we want to show the alert pulse that is controlled below
			TArray<ERaidEventFlag> testFlags;
			testFlags.Add( ERaidEventFlag::ATTACKER_BOMB_PLANTED );
			testFlags.Add( ERaidEventFlag::ATTACKER_BOMB_PLANTED_GENERATOR );
			testFlags.Add( ERaidEventFlag::DEFENDER_BOMB_PLANTED );
			testFlags.Add( ERaidEventFlag::DEFENDER_BOMB_PLANTED_GENERATOR );

			int playerTeam = player.GetTeam();
			int raidFlags  = GetCombinedRaidFlagsForTeam( playerTeam, ERaidEventActive::TEAM_ACTIVE );
			bool showPulse = CheckIfAnyFlagsMatch( raidFlags, testFlags );
			Material::SetScalarParameterValue( raidMessagingMpc, ALERT_PULSE_ACTIVE_PARAMETER, showPulse ? 1.0f : 0.0f );
		}
	}

	void CheckForActivePlant()
	{
		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( !IsValid( player ) )
			return;

		int teamId											= player.GetTeam();
		bool isRaiding										= IsTeamRaiding( teamId );
		TArray<UAS_RaidBombInterfaceComponent> activePlants = isRaiding ? GetAllObjectsBeingBombPlantedByAttackingTeam( teamId ) : GetAllObjectsHavingBombPlantedOnDefendingTeam( teamId );
		TArray<AAS_RaidBomb> raidBombs						= isRaiding ? GetAllRaidBombsForAttackers( teamId ) : GetAllRaidBombsForDefenders( teamId );

		bool hasActiveBomb		  = !raidBombs.IsEmpty();
		bool hasActivePlant		  = !activePlants.IsEmpty();
		bool hasActiveBombOrPlant = hasActiveBomb || hasActivePlant;

		if ( hasActiveBombOrPlant && !hadActiveBombOrPlant )
		{
			PlayAnimationForward( inUseAnimation, 4.0f );
			hadActiveBombOrPlant = hasActiveBombOrPlant;
		}
		else if ( !hasActiveBombOrPlant && hadActiveBombOrPlant )
		{
			PlayAnimationReverse( inUseAnimation, 6.0f );
			hadActiveBombOrPlant = hasActiveBombOrPlant;
		}
	}

	void SetShieldbreakerCarrier( AAS_PlayerEntity carrier )
	{
		AAS_PlayerEntity localPlayer = Client_GetLocalASPawn();
		if ( !IsValid( localPlayer ) )
			return;

		int localPlayerTeam = localPlayer.GetTeam();
		int carrierTeam		= carrier.GetTeam();
		bool sameTeam		= localPlayerTeam == carrierTeam;

		raidMessagingShape.ChangeShieldbreakerCarrier( carrier );
		shieldbreakerIndicator.ChangeShieldbreakerCarrier( carrier );
	}

	void ShowSmallTeamHealthWidgetOutOfRaid( bool shouldShowSmallWidgetOutOfRaid )
	{
		enemyTeamHealth.SetShowSmallWidgetOutOfRaid( shouldShowSmallWidgetOutOfRaid );
		localTeamHealth.SetShowSmallWidgetOutOfRaid( shouldShowSmallWidgetOutOfRaid );
	}

	void SetRaidObjectiveState( EObjectiveState objectiveState )
	{
		raidMessagingShape.ChangeRaidObjectiveState( objectiveState );
		shieldbreakerIndicator.ChangeRaidObjectiveState( objectiveState );
	}

	void SetShotClockState( EShotClockState newState )
	{
		raidMessagingShape.ChangeShotClockState( newState );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnRaidStarted( AAS_RaidEventManager_v2 eventManager )
	{
		PlayAnimationForward( raidStarted );
	}

	UFUNCTION( NotBlueprintCallable )
	private void OnRaidEnded( AAS_RaidEventManager_v2 eventManager )
	{
		PlayAnimationReverse( raidStarted );
	}

	private void AnimateToText( UCommonRichTextBlock& textBlock, FText newText )
	{
		if ( !IsValid( textBlock ) )
			return;

		hasUpdatedText			= true;
		optAnimateToStartTimeMs = GetGameTimeMS();
		optAnimatingToTextBlock = textBlock;
		optToText				= newText;
		UpdateAnimateToText();
	}

	UFUNCTION( NotBlueprintCallable )
	private void UpdateAnimateToText()
	{
		if ( optAnimatingToTextBlock.IsSet() && IsValid( animateToCurve ) )
		{
			int timeElapsedMs = GetGameTimeMS() - optAnimateToStartTimeMs.GetValue();
			int countdownMs	  = Math::Max( ANIMATE_TO_DURATION_MS - timeElapsedMs, 0 );

			UCommonRichTextBlock textBlock = optAnimatingToTextBlock.GetValue();
			if ( countdownMs > 0 )
			{
				float perc = float( timeElapsedMs ) / ANIMATE_TO_DURATION_MS;

				// Once we cross the halfway point, flip the text
				if ( !hasUpdatedText && perc >= 0.5 )
				{
					hasUpdatedText = true;
					textBlock.SetText( optToText.GetValue() );
				}

				float opacity = animateToCurve.GetFloatValue( perc );
				optAnimatingToTextBlock.GetValue().SetRenderOpacity( opacity );
				System::SetTimerForNextTick( this, "UpdateAnimateToText" );
			}
			else
			{
				textBlock.SetText( optToText.GetValue() );
				textBlock.SetRenderOpacity( 1.0f );
				optAnimatingToTextBlock.Reset();
			}
		}
	}
}