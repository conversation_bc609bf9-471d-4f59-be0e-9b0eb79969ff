UCLASS( Abstract )
class UAS_ShieldBreakerTrackerWidget : UNC_DisplayWidget
{
	default StateOnConstruction = EDisplayConstructVisibility::Hidden;
	default HideVisibility		= ESlateVisibility::Collapsed;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage localTeamTracker;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UImage enemyTeamTracker;

	private AAS_RaidDomeShield localDome;
	private AAS_RaidDomeShield enemyDome;
	private int trackedLocalTeam = GameConst::INDEX_NONE;
	private int trackedEnemyTeam = GameConst::INDEX_NONE;
	private UMaterialInstanceDynamic localTeamTrackerMaterial;
	private UMaterialInstanceDynamic enemyTeamTrackerMaterial;

	private const float HALF = 0.5f;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		localTeamTrackerMaterial = CreateDynamicMaterialFromImageBrush( localTeamTracker );
		if ( IsValid( localTeamTrackerMaterial ) )
		{
			localTeamTracker.SetBrushFromMaterial( localTeamTrackerMaterial );
			localTeamTrackerMaterial.SetScalarParameterValue( MaterialParameter::LERP_ALPHA, 0.0f );
		}

		enemyTeamTrackerMaterial = CreateDynamicMaterialFromImageBrush( enemyTeamTracker );
		if ( IsValid( enemyTeamTrackerMaterial ) )
		{
			enemyTeamTracker.SetBrushFromMaterial( enemyTeamTrackerMaterial );
			enemyTeamTrackerMaterial.SetScalarParameterValue( MaterialParameter::LERP_ALPHA, 0.0f );
		}
	}

	UFUNCTION( BlueprintOverride )
	void OnShowStart()
	{
		WaitForDomes();
	}

	UFUNCTION( NotBlueprintCallable )
	void WaitForDomes()
	{
		localDome = GetRaidDomeShieldForTeam( trackedLocalTeam );
		enemyDome = GetRaidDomeShieldForTeam( trackedEnemyTeam );

		if ( !IsValid( localDome ) || !IsValid( enemyDome ) )
		{
			System::SetTimerForNextTick( this, "WaitForDomes" );
		}
		else
		{
			TrackShieldbreaker();
		}
	}

	void SetTrackedTeams( int localTeam, int enemyTeam )
	{
		trackedLocalTeam = localTeam;
		trackedEnemyTeam = enemyTeam;
	}

	UFUNCTION( NotBlueprintCallable )
	private void TrackShieldbreaker()
	{
		AAS_PlayerEntity player = Client_GetLocalASPawn();
		if ( !IsValid( localDome ) || !IsValid( enemyDome ) || !IsValid( player ) )
			return;

		FVector shieldbreakerLocation;
		FVector crafterLocation;
		TArray<AAS_SBCrafter> allTables;
		GetAllActorsOfClass( allTables );
		for ( AAS_SBCrafter crafter : allTables )
		{
			if ( crafter.GetSBCrafterState() == ESBCrafterState::HIDDEN )
				continue;

			AActor trackedActor = crafter.net_trackedItem.GetEntity();
			if ( IsValid( trackedActor ) )
			{
				// Save the location of the crafter and the shieldbreaker to track distance
				shieldbreakerLocation = trackedActor.GetActorLocation();
				crafterLocation		  = crafter.GetActorLocation();
				break;
			}
		}

		// Gather some initial locations we will be using
		FVector localDomeLocation = localDome.GetActorLocation();
		FVector enemyDomeLocation = enemyDome.GetActorLocation();

		// Calculate the closest point along the base bounds of each base, pE for enemy and pL for local
		float localRadius = localDome.mapSwapDetectionTrigger.GetBoundsRadius();
		FVector pL		  = localDomeLocation + ( ( shieldbreakerLocation - localDomeLocation ).GetSafeNormal2D() * localRadius );

		float enemyRadius = enemyDome.mapSwapDetectionTrigger.GetBoundsRadius();
		FVector pE		  = enemyDomeLocation + ( ( shieldbreakerLocation - enemyDomeLocation ).GetSafeNormal2D() * enemyRadius );

		// Calculate where the shieldbreaker is in relation to both closest points of each base
		float frac = GetClosestPointOnSegmentFrac( pL, pE, shieldbreakerLocation );

		// The fraction calculated determines which side of the map we are on, we then use that to determine which base to get distance to - < half means we pick the local player base
		FVector chosenPoint	 = frac < HALF ? pL : pE;
		FVector chosenCenter = frac < HALF ? localDomeLocation : enemyDomeLocation;
		float chosenRadius	 = frac < HALF ? localRadius : enemyRadius;

		// Then calculate the distance between the crafter and the chosen point and the shieldbreaker and the chosen point
		float crafterDistance		= crafterLocation.Dist2D( chosenPoint, FVector::UpVector );
		float shieldbreakerDistance = chosenPoint.Dist2D( shieldbreakerLocation, FVector::UpVector );

		if ( chosenCenter.Dist2D( shieldbreakerLocation, FVector::UpVector ) < chosenRadius )
		{
			// However, we want to make sure that the distance stops updating once the player gets close to the base even though their distance from the chosen point changes
			shieldbreakerDistance = 0.0f;
		}

		// Use those distances to calculate how far away we are as a percent
		float progress = crafterDistance > 0.0f ? Math::Max( 0, 1 - ( shieldbreakerDistance / crafterDistance ) ) : 0.0f;

		float localTeamFrac = 0.0f;
		float enemyTeamFrac = 0.0f;
		if ( frac < HALF )
		{
			// If the calculated fraction is less than half, the shieldbreaker is on our side
			localTeamFrac = progress;
		}
		else
		{
			// Otherwise, the shieldbreaker is on the enemy side
			enemyTeamFrac = progress;
		}

		if ( IsValid( localTeamTrackerMaterial ) && IsValid( enemyTeamTrackerMaterial ) )
		{
			// We want to see if the players team has the shieldbreaker to show the right color
			int teamId						 = player.GetTeam();
			int otherTeam					 = GetOtherTeam( teamId );
			bool playersTeamHasShieldbreaker = GetTeamShieldBreacherCountFromTeamID( teamId ) > 0;
			bool enemyTeamHasShieldbreaker	 = GetTeamShieldBreacherCountFromTeamID( otherTeam ) > 0;

			// Finally u pdate the materials with the progress and correct show flag
			localTeamTrackerMaterial.SetScalarParameterValue( MaterialParameter::LERP_ALPHA, localTeamFrac );
			enemyTeamTrackerMaterial.SetScalarParameterValue( MaterialParameter::LERP_ALPHA, enemyTeamFrac );
			localTeamTrackerMaterial.SetScalarParameterValue( MaterialParameter::SHOW_SCALAR, MaterialParameter::GetTrueFalseFloat( enemyTeamHasShieldbreaker ) );
			enemyTeamTrackerMaterial.SetScalarParameterValue( MaterialParameter::SHOW_SCALAR, MaterialParameter::GetTrueFalseFloat( enemyTeamHasShieldbreaker ) );
		}

		// DrawDebug( pL, pE, shieldbreakerLocation, chosenPoint, crafterLocation );

		System::SetTimerForNextTick( this, "TrackShieldbreaker" );
	}

	private void
	DrawDebug( FVector pL, FVector pE, FVector shieldbreakerLocation, FVector chosenPoint, FVector crafterLocation )
	{
		DrawDebugSphere( pL, 100, 0.1, FLinearColor::Purple, 3.0f, true );
		DrawDebugSphere( pE, 100, 0.1, FLinearColor::Purple, 3.0f, true );
		DrawDebugLine( pL, pE, 0.1, FLinearColor::Purple, 50, true );

		FVector cP = GetClosestPointOnSegment( pL, pE, shieldbreakerLocation );
		DrawDebugSphere( cP, 100, 0.1, FLinearColor::Red );

		DrawDebugSphere( chosenPoint, 200, 0.1, FLinearColor::Green, 3.0f, true );
		DrawDebugLine( shieldbreakerLocation * FVector( 1, 1, 0 ), chosenPoint, 0.1, FLinearColor::Green, 50, true );
		DrawDebugLine( crafterLocation, chosenPoint, 0.1, FLinearColor::Yellow, 50, true );
	}
}