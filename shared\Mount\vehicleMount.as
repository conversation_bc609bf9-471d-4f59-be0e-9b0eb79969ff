USTRUCT()
struct FMountVehicleLocalData
{
	UPROPERTY()
	float32 SprintSpeedMax;

	UPROPERTY()
	float32 SprintAccel;

	UPROPERTY()
	float32 BoostSpeedMax;

	UPROPERTY()
	float32 RawBoostSpeedMax;

	UPROPERTY()
	float32 ActiveSprint_BoostSpeedMax;

	UPROPERTY()
	float32 RawSprintSpeedMax;

	UPROPERTY()
	float32 ActiveSprint_SprintSpeedMax;

	UPROPERTY()
	float32 BoostAccel;

	UPROPERTY()
	float32 SprintMaxCadenceSpeed;
}

UCLASS( Abstract )
class AAS_VehicleMount : ANCVehicle
{
	access internal = private, UAS_MountViewModelAnimInstance;

	UPROPERTY( DefaultComponent )
	UAS_TeamComponent teamComponent;

	default HealthComponent.MaxHealth = 100;
	default HealthComponent.Health	  = 100;

	UPROPERTY( DefaultComponent )
	UUsableItemComponent useComponent;
	default useComponent.HintText		  = GetLocalizedText( Localization::Mount, "mount_ride" );
	default useComponent.SphereRadius	  = 64;
	default useComponent.UsableDistance	  = 350;
	default useComponent.RelativeLocation = FVector( 0, 0, 25 );

	UPROPERTY( DefaultComponent )
	UAS_MountAnimComponent animComponent;

	UPROPERTY( EditDefaultsOnly )
	UAS_PinnableWidgetSettings iconPinSettings;

	UPROPERTY( EditDefaultsOnly )
	UAS_PinnableWidgetSettings namePinSettings;

	UPROPERTY( DefaultComponent )
	UAS_MountMotionModel motionModel;

	UPROPERTY()
	private FNCNetString net_CoreDataEntry;

	UPROPERTY()
	FMountVehicleLocalData localData;

	private int32 tppCameraOverrideID = -1;
	private int32 spectateCameraOverrideID = -1;

	// Do not access directly - use GetMountData() instead
	FVehicleMountData CachedMountData;
	FVehicleMountData GetMountData()
	{
		if ( !CachedMountData.isInitialized )
			CachedMountData.Init( GetCurrentVehicleData() );

		return CachedMountData;
	}

	UFUNCTION( BlueprintOverride )
	void ServerBeginPlay()
	{
		AAS_PlayerEntity ownerPlayer = GetOwnerPlayer();
		SetTeam( ownerPlayer.GetTeam() );
		ownerPlayer.mountData.Server_OnMountSummoned( this );

		HealthComponent.BP_OnPreReceivedDamage.BindUFunction( this, n"sv_OnPreDamage" );
		HealthComponent.BP_OnPostReceivedDamage.AddUFunction( this, n"sv_OnPostDamage" );
		HealthComponent.OnNetworkedHealthChanged.AddUFunction( this, n"sv_OnHealthChanged" );

		FVehicleMountData data = GetMountData();

		const float32 startHealth = ownerPlayer.mountData.GetSavedHealth();
		ScriptAssert( startHealth > 0, "Starting health was zero" );
		const float32 maxHealth = data.Core.Gameplay.MaxHealth;

		HealthComponent.SetMaxHealth( maxHealth );
		HealthComponent.SetHealth( startHealth );

		// Print(f"health: {startHealth} max: {data.Core.Gameplay.MaxHealth}");
		SharedBeginPlay();

		// want to make sure we come to a stop and play the idle animation if we're falling or moving
		// the motion model component will set this to true once everything is kosher.
		VehicleIsDormant.SetNetValue( false );

		ScriptCallbacks().shared_OnRaidStarted.AddUFunction( this, n"OnRaidStarted" );
	}

	UFUNCTION()
	private void OnRaidStarted( AAS_RaidEventManager_v2 eventManager )
	{
		if ( eventManager.GetDefenderTeam() != GetTeam() )
			return;
		if ( IsValid( GetPilot() ) )
			return;

		AnimHideMount();
	}

	UFUNCTION( BlueprintOverride )
	void ClientBeginPlay()
	{
		SharedBeginPlay();

		VehicleCooldownEndTime.OnReplicated().AddUFunction( this, n"OnMountCooldownEndTimeChanged" );

		ANCPlayerCharacter pilot = GetPilot();
		if ( Client_GetLocalPawn() == pilot )
		{
			//sh_OnPilotChanged( nullptr, pilot ); Called in Code
			cl_OnPilotChangedWindSFX( nullptr, pilot );
		}

		OnPilotChanged.AddUFunction( this, n"cl_OnPilotChangedWindSFX" );
		net_CoreDataEntry.OnReplicated().AddUFunction( this, n"OnCoreDataEntryChanged" );
	}

#if EDITOR
	UFUNCTION(BlueprintOverride)
	float32 GetCooldownDurationOverride() const
	{
		if (GetCvarBool( f"VehicleMount.DebugKillMount" ))
			return 5.0f;

		return -1.0f;
	}
#endif

	UFUNCTION( NotBlueprintCallable )
	private void OnMountCooldownEndTimeChanged( int oldValue, int newValue )
	{
		GetVehicleOwner().Signal( Signals::ON_MOUNT_COOLDOWN_UPDATE );
	}

	void SharedBeginPlay()
	{
		FGameplayTag mountTag = Mount::GetGameplayTagFromMount( this );
		SetVehicleMountDataClass( Mount::GetMountCodeDataAssetClass( mountTag ) );

		OnPilotChanged.AddUFunction( this, n"sh_OnPilotChanged" );

		motionModel.OnActiveSprintInput.AddUFunction( this, n"OnActiveSprintInput" );
		motionModel.OnStartBraking.AddUFunction( this, n"OnStartBraking" );
		motionModel.OnEndBraking.AddUFunction( this, n"OnEndBraking" );

		useComponent.GetUsabilityStateDelegate.BindUFunction( this, n"GetUsabilityState" );
		useComponent.OnUsed.AddUFunction( this, n"OnUsed" );
	}

	UFUNCTION()
	FInputUsabilityStates GetUsabilityState( const UUsableItemComponent Component, const ANCPlayerCharacter PlayerUser )
	{
		FInputUsabilityStates usabilityStates;
		if ( !IsValid( GetOwnerPlayer() ) || !IsValid( PlayerUser ) )
		{
			return usabilityStates;
		}
		if ( GetOwnerPlayer() == PlayerUser )
		{
			usabilityStates.NormalInstantInput = EUsabilityState::USABLE;
			return usabilityStates;
		}
		return usabilityStates;
	}

	UFUNCTION()
	void OnUsed( const UUsableItemComponent Component, const ANCPlayerCharacter PlayerUser )
	{
		if ( IsServer() )
		{
			AAS_PlayerEntity asPawn = Cast<AAS_PlayerEntity>( PlayerUser );
			sv_Embark( asPawn );
		}
	}

	UFUNCTION( BlueprintOverride )
	void EndPlay( EEndPlayReason EndPlayReason )
	{
		if ( EndPlayReason != EEndPlayReason::Destroyed )
			return;

		CleanupOldPilot();

		if ( IsValid( mount3pFx ) )
			mount3pFx.DestroyComponent( mount3pFx );
	}

	UFUNCTION( BlueprintOverride )
	void Destroyed()
	{
		CleanupOldPilot();
	}

	bool FinalOnChangePilotCalled = false;
	private void CleanupOldPilot()
	{
		if ( FinalOnChangePilotCalled )
			return;

		ANCPlayerCharacter oldPilot = GetPilot();
		if ( !IsValid( oldPilot ) )
			return;

		FinalOnChangePilotCalled = true;

		// broadcast it instead of calling directly so anything bound to it will also run
		OnPilotChanged.Broadcast( oldPilot, nullptr );

		if ( IsServer() && oldPilot.IsPlayerRidingMount() )
			Mount::Dismount( oldPilot );
	}

	UFUNCTION()
	void OnPlayerDeath( const FDamageInfo&in DamageInfo, ANCPlayerCharacter Victim )
	{
		RemovePilot();
		AnimHideMount();
	}

	private float32 GetInactiveSprint_AccelerationCurve( float32 speedRangeMin, float32 speedRangeMax, float32 accRangeMin, float32 accRangeMax, float32 cur2DSpeed )
	{
		/*	why have a curve instead of a constant? Because of drag. As drag increases exponentially
			with speed, if the acceleration isn't strong enough, it won't overcome the drag to get
			to the max speed. Problem is with a constant acceleration I need to have it so high to
			overcome the drag at the top end, that I can't start off really slow. Normally this would
			be fine, but with the "Active Sprint" feature, I want regular acceleration to feel slugish,
			so that hitting the sprint button feels impactful. This curve allows me to start off
			sluggish, but offer enough acceleration at the top end to achieve the top speed.		*/

		float32 accFrac = float32( Math::GetMappedRangeValueClamped( FVector2D( speedRangeMin, speedRangeMax ), FVector2D( accRangeMin, accRangeMax ), cur2DSpeed ) );
		return accFrac;
	}

	UFUNCTION()
	void UpdateMountLocalData( FVehicleMountData data, float32 cur2DSpeed )
	{
		localData.RawBoostSpeedMax			  = data.Core.Physics.BoostSpeedMax;
		localData.ActiveSprint_BoostSpeedMax  = data.Core.Physics.BoostSpeedMax * data.Core.Physics.InactiveSprint_BoostSpeedFrac;
		localData.RawSprintSpeedMax			  = data.Core.Physics.SprintSpeedMax;
		localData.ActiveSprint_SprintSpeedMax = data.Core.Physics.SprintSpeedMax * data.Core.Physics.InactiveSprint_SprintSpeedFrac;

		if ( !GetCvarBool( f"VehicleMount.EnableActiveSprint" ) || ( motionModel.HasActiveSprint() && !motionModel.HasAttackedRecently() ) )
		{
			localData.SprintSpeedMax		= data.Core.Physics.SprintSpeedMax;
			localData.SprintAccel			= data.Core.Physics.SprintAcceleration;
			localData.BoostSpeedMax			= data.Core.Physics.BoostSpeedMax;
			localData.BoostAccel			= data.Core.Physics.BoostAcceleration;
			localData.SprintMaxCadenceSpeed = data.Asset.Audio.SprintMaxCadenceSpeed;
		}
		else
		{
			/////////////////////////////////////////////////////////////////////////
			//	Active Sprint MODIFIERS
			/*	in essence, we modify the values for sprint/boost speed/acc to be lower. That
				way we tune for Active Sprint pressed, and with Active Sprint off, we make everything a little
				bit slower using fractions of the values intended for boost on.	*/

			localData.SprintSpeedMax		= data.Core.Physics.SprintSpeedMax * data.Core.Physics.InactiveSprint_SprintSpeedFrac;
			localData.SprintAccel			= data.Core.Physics.SprintAcceleration * GetInactiveSprint_AccelerationCurve( data.Core.Physics.WalkSpeedMax, localData.SprintSpeedMax, data.Core.Physics.InactiveSprint_SprintAccFracMin, data.Core.Physics.InactiveSprint_SprintAccFracMax, cur2DSpeed );
			localData.BoostSpeedMax			= data.Core.Physics.BoostSpeedMax * data.Core.Physics.InactiveSprint_BoostSpeedFrac;
			localData.BoostAccel			= data.Core.Physics.BoostAcceleration * GetInactiveSprint_AccelerationCurve( localData.SprintSpeedMax, localData.BoostSpeedMax, data.Core.Physics.InactiveSprint_BoostAccFracMin, data.Core.Physics.InactiveSprint_BoostAccFracMax, cur2DSpeed );
			localData.SprintMaxCadenceSpeed = data.Asset.Audio.SprintMaxCadenceSpeed * data.Core.Physics.InactiveSprint_SprintSpeedFrac;
		}
	}

	UFUNCTION( BlueprintOverride )
	float ScriptCalcMaxSpeedPercentage()
	{
		return 100 * motionModel.GetSpeed() / localData.SprintSpeedMax;
	}

	/****************************************************************\

	██████  ██   ██ ██    ██ ███████ ██  ██████ ███████
	██   ██ ██   ██  ██  ██  ██      ██ ██      ██
	██████  ███████   ████   ███████ ██ ██      ███████
	██      ██   ██    ██         ██ ██ ██           ██
	██      ██   ██    ██    ███████ ██  ██████ ███████

	\****************************************************************/
	UFUNCTION( BlueprintOverride )
	FNCVehicleScriptCalcValues ScriptCalcVelocityAndRotation( ANCPlayerCharacter pilot, FVector curVelRaw, FVector2D moveInputRaw, float deltaTime )
	{
		bool READ_ONLY = false;

		int gameTimeMS = GetGameTimeMS();

		FVehicleMountMotionModelCalc calc = motionModel.CalculateMountMotionModel( pilot, curVelRaw, moveInputRaw, deltaTime, gameTimeMS, READ_ONLY );

		if ( IsValid( pilot ) )
			motionModel.HACK_ReturnPlayerViewFromFreelook( pilot, GetMountData(), calc, deltaTime, gameTimeMS );

		FNCVehicleScriptCalcValues values;
		values.Velocity = calc.finalVel;
		values.Rotation = FRotator( 0, calc.finalYaw, calc.extraRollHack );
		return values;
	}

	/****************************************************************\

	 ██████  ███    ██     ██████   █████  ███    ███  █████   ██████  ███████
	██    ██ ████   ██     ██   ██ ██   ██ ████  ████ ██   ██ ██       ██
	██    ██ ██ ██  ██     ██   ██ ███████ ██ ████ ██ ███████ ██   ███ █████
	██    ██ ██  ██ ██     ██   ██ ██   ██ ██  ██  ██ ██   ██ ██    ██ ██
	 ██████  ██   ████     ██████  ██   ██ ██      ██ ██   ██  ██████  ███████

	\****************************************************************/
	UFUNCTION()
	protected void sv_OnPreDamage( FDamageInfo& damageInfo )
	{
		if ( !IsValid( damageInfo.attacker ) )
			return;

		AAS_PlayerEntity owner = GetOwnerPlayer();

		if ( IsValid( owner ) )
		{
			if ( IsFriendly( owner.GetTeam(), damageInfo.attacker.GetTeam() ) &&
				 !Bitflags::HasFlag( damageInfo.damageFlags, EDamageFlags::DF_IGNORE_FRIENDLYFIRE_CHECKS ) )
			{
				damageInfo.damage = 0;
				return;
			}
		}

		// no current rider
		if ( !IsValid( GetPilot() ) )
		{
			damageInfo.damage = 0;
			AnimHideMount();
			return;
		}

		bool isChipDamage = Bitflags::HasFlag( damageInfo.scriptDamageFlags, EScriptDamageFlags::DF_CHIP_DAMAGE );
		if ( isChipDamage )
		{
			damageInfo.damage = 0;
			return;
		}

		if ( damageInfo.damage >= GetHealth() && GetHealth() > 0 )
		{
			damageInfo.damageFlags = Bitflags::AddFlag( damageInfo.damageFlags, EDamageFlags::DF_KILLSHOT );
		}
	}

	UFUNCTION()
	private void sv_OnHealthChanged( float32 oldHealth, float32 newHealth )
	{
		if ( newHealth < oldHealth )
			sv_OnPostDamageCommon( nullptr );
	}

	UFUNCTION()
	private void OnPilotDamaged( UHealthComponent damagedComponent, const FDamageInfo&in damageInfo )
	{
		FDamageInfo damageToMountOnly		= FDamageInfo( damageInfo );
		damageToMountOnly.scriptDamageFlags = Bitflags::AddFlag( damageToMountOnly.scriptDamageFlags, EScriptDamageFlags::DF_CHIP_DAMAGE );
		damageToMountOnly.scriptDamageFlags = Bitflags::AddFlag( damageToMountOnly.scriptDamageFlags, EScriptDamageFlags::DF_HIDDEN_DAMAGE );

		FIncomingDamageAmounts amounts;
		amounts.normalDamage	 = damageInfo.damage;
		amounts.structuralDamage = 0;

		HealthComponent.ReceiveDamage( damageToMountOnly, amounts );
	}

	UFUNCTION()
	protected void sv_OnPostDamage( UHealthComponent damagedComponent, const FDamageInfo&in damageInfo )
	{
		ScriptAssert( damagedComponent == HealthComponent, "expected local health component" );

		ANCPlayerCharacter pilot = GetPilot();

		// call this before damaging pilot so we can set the deathpackage before player dies
		sv_OnPostDamageCommon( damageInfo.attacker );

		if ( IsValid( pilot ) )
		{
			FVehicleMountData data = GetMountData();
			bool isChipDamage = Bitflags::HasFlag( damageInfo.scriptDamageFlags, EScriptDamageFlags::DF_CHIP_DAMAGE );
			if ( !isChipDamage )
			{
				FDamageInfo chipDamage = FDamageInfo( damageInfo );
				chipDamage.damage	   = Math::Min( HealthComponent.GetMaxHealth(), chipDamage.damage );
				chipDamage.damage *= data.Core.Gameplay.ChipDamageMultiplier;
				chipDamage.scriptDamageFlags = Bitflags::AddFlag( chipDamage.scriptDamageFlags, EScriptDamageFlags::DF_HIDDEN_DAMAGE );
				pilot.ReceiveDamage( chipDamage );
			}
		}
	}

	void sv_OnPostDamageCommon( ANCPlayerCharacter attacker )
	{
		ScriptAssert( IsServer(), "SERVER ONLY" );

		AAS_PlayerEntity owner = GetOwnerPlayer();
		if ( !IsValid( owner ) )
			return;

		owner.mountData.RecordDamage( HealthComponent.GetHealth() );

		// on death
		if ( HealthComponent.GetHealth() <= 0.0f && GetVehicleState() != EVehicleActiveState::Vehicle_Dead )
		{
			owner.GetMountComponent().OnDeath();
			DeathPackage( owner );

			if ( IsServer() && IsValid( attacker ) )
			{
				ScriptCallbacks().server_onMountDied.Broadcast( attacker, this );
			}
		}
	}

	/****************************************************************\

	██████  ███████  █████  ████████ ██   ██     ██████   █████   ██████ ██   ██  █████   ██████  ███████
	██   ██ ██      ██   ██    ██    ██   ██     ██   ██ ██   ██ ██      ██  ██  ██   ██ ██       ██
	██   ██ █████   ███████    ██    ███████     ██████  ███████ ██      █████   ███████ ██   ███ █████
	██   ██ ██      ██   ██    ██    ██   ██     ██      ██   ██ ██      ██  ██  ██   ██ ██    ██ ██
	██████  ███████ ██   ██    ██    ██   ██     ██      ██   ██  ██████ ██   ██ ██   ██  ██████  ███████

	\****************************************************************/
	private const float MIN_FALL_SPEED = 250;
	private void DeathPackage( ANCPlayerCharacter oldPilot )
	{
		ScriptAssert( IsServer(), "SERVER ONLY" );

		RemovePilot();

		FVector MAGIC_UP_VEC = FVector( 0, 0, oldPilot.MovementComponent.GetGravityZ() * -0.35 );
		FVector playerVel	 = this.GetVelocity();
		float speed			 = playerVel.Size2D();
		if ( speed < MIN_FALL_SPEED )
		{
			if ( speed < 5 )
				playerVel = oldPilot.GetViewRotationFlat().GetForwardVector();
			else
				playerVel.Normalize();

			playerVel *= MIN_FALL_SPEED;
		}

		oldPilot.BouncePlayer( MAGIC_UP_VEC + playerVel );

		HealthComponent.OnDeathSignal.Emit();
	}

	/****************************************************************\

	███████ ███    ███ ██████   █████  ██████  ██   ██
	██      ████  ████ ██   ██ ██   ██ ██   ██ ██  ██
	█████   ██ ████ ██ ██████  ███████ ██████  █████
	██      ██  ██  ██ ██   ██ ██   ██ ██   ██ ██  ██
	███████ ██      ██ ██████  ██   ██ ██   ██ ██   ██

	\****************************************************************/
	UFUNCTION(BlueprintOverride)
	void PreVehicleEmbark(ANCPlayerCharacter incomingPilot)
	{
		if (IsServer())
		{
			AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( incomingPilot );
			player.OnMountSignal.Emit();

			player.Server_OnPawnDeath.Unbind( this, n"OnPlayerDeath" );
			player.Server_OnPawnDeath.AddUFunction( this, n"OnPlayerDeath" );

			ScriptCallbacks().server_onPreMountStateChanged.Broadcast( player, this, true );

			UAS_Thread_PlayerOnEmbarkThread OnPlayerEmbarkThread = Cast<UAS_Thread_PlayerOnEmbarkThread>( CreateThread( UAS_Thread_PlayerOnEmbarkThread::StaticClass(), this ) );
			OnPlayerEmbarkThread.Init( player, this );
		}
	}

	UFUNCTION(BlueprintOverride)
	void OnVehicleEmbark(ANCPlayerCharacter newPilot)
	{
		if (IsServer())
		{
			if ( ANCPlayerCharacter::UsePredictedMountInput() )
				SetTeam( newPilot.GetTeam() );
			
			newPilot.PlayBattleChatter( GameplayTags::Audio_VO_BattleChatter_Going_on_Mount );

			if ( GetCvarFloat( f"VehicleMount.DebugKillMount" ) > 0 )
				Thread( this, n"DebugKillMount" );
			else if ( GetCvarFloat( f"VehicleMount.DebugForceDismount" ) > 0 )
				Thread( this, n"DebugForceDismount" );
		}
	}

	void sv_Embark( ANCPlayerCharacter player )
	{
		ScriptAssert( IsServer(), "MUST RUN ON SERVER" );

		// NC1-9219 FATAL SCRIPT ERROR: GetTeam: self is not valid
		// owner or asPlayerUser is invalid in some case. Silently log bug instead of asserting
		AAS_PlayerEntity owner		  = GetOwnerPlayer();
		AAS_PlayerEntity asPlayerUser = Cast<AAS_PlayerEntity>( player );
		if ( !IsValid( owner ) || !IsValid( asPlayerUser ) )
		{
			// FString errorMsg = !IsValid( owner ) ? "owner is invalid" : "asPlayerUser is invalid";
			// ScriptError_Silent_WithBug( "AAS_VehicleMount::sv_Embark - Invalid Player", f"mohammad", errorMsg );
			return;
		}

		if ( IsValid( GetPilot() ) )
		{
			ScriptError_Silent_WithBug( "ANCVehicle::SetPilot: Attempting to SetPilot on a vehicle that already has a pilot", "Ravi", "Dupe of bug 10971" );
			return;
		}

		if (ANCPlayerCharacter::UsePredictedMountInput())
		{
			// Does the same actions as below
			// PreVehicleEmbark
			// SetAndAttachPilot
			// OnVehicleEmbark
			PilotEmbark(player);
			return;
		}

		PreVehicleEmbark( player );

		// Set the player as the new pilot for the mount
		{
			SetPilot( player );

			if ( GetBugReproNum() != 8197 && IsValid( player.GetAttachParentActor() ) )
			{
				ScriptError_Silent_WithBug( "Code Mount: INTERNAL SCRIPT EXCEPTION: OnParentActorChangedCallback: Actor BP_NC_CharacterEntity_C_0 already has parent call DetachActor before assigning a new parent", "Ravi", "Dupe of bug 8197" );
				player.DetachFromActor( EDetachmentRule::KeepWorld, EDetachmentRule::KeepWorld, EDetachmentRule::KeepWorld );
			}

			/*	can't attach to entity mesh, has collision issues with attaching... must attach to the collision component which is the root component.
				**UPDATE** not sure if the issue above is still real ( 1/9/25 ) - I attached to VehicleMesh_3P with no obvious issues, but looks identical
				to attaching to root component... so why introduce a possible bug after it's been working */
			player.AttachToComponent( GetRootComponent(), NAME_None, EAttachmentRule::SnapToTarget, EAttachmentRule::KeepRelative, EAttachmentRule::KeepRelative, false );
			SetTeam( player.GetTeam() );
		}

		OnVehicleEmbark( player );
	}

	UFUNCTION()
	void DebugKillMount( UNCCoroutine co )
	{
		float time = GetCvarFloat( f"VehicleMount.DebugKillMount" );
		co.Wait( time );
		KillMe();
	}

	UFUNCTION()
	void DebugForceDismount( UNCCoroutine co )
	{
		float time = GetCvarFloat( f"VehicleMount.DebugForceDismount" );
		co.Wait( time );
		Dismount( GetPilot() );
	}

	/****************************************************************\

	██████  ██ ███████ ███    ███  ██████  ██    ██ ███    ██ ████████
	██   ██ ██ ██      ████  ████ ██    ██ ██    ██ ████   ██    ██
	██   ██ ██ ███████ ██ ████ ██ ██    ██ ██    ██ ██ ██  ██    ██
	██   ██ ██      ██ ██  ██  ██ ██    ██ ██    ██ ██  ██ ██    ██
	██████  ██ ███████ ██      ██  ██████   ██████  ██   ████    ██

	\****************************************************************/
	void RemovePilot()
	{
		if (ANCPlayerCharacter::UsePredictedMountInput())
		{	
			ClearAndDetachPilot();
			return;
		}

		AAS_PlayerEntity pilot = Cast<AAS_PlayerEntity>(GetPilot());
		if (IsValid(pilot))
		{
			/*	detach actor was in the callback, but it needs to be called before clearpilot in order for the player origin to be
			in the right place otherwise sometimes it would shoot through the ceiling geo and put the player into the 2nd floor */
			pilot.SetActorLocation( GetActorLocation() );
			pilot.DetachFromActor( EDetachmentRule::KeepWorld, EDetachmentRule::KeepWorld, EDetachmentRule::KeepWorld );
			pilot.OnDismountSignal.Emit();	
		}

		ClearPilot();
	}

	UFUNCTION(BlueprintOverride)
	void PreVehicleDisembark(ANCPlayerCharacter outgoingPilot)
	{
		if (IsServer())
		{
			ScriptCallbacks().server_onPreMountStateChanged.Broadcast( outgoingPilot, this, false );
		}
	}

	UFUNCTION(BlueprintOverride)
	void OnVehicleDisembark(ANCPlayerCharacter oldPilot)
	{
		if (IsServer())
		{
			if (ANCPlayerCharacter::UsePredictedMountInput())
			{
				AAS_PlayerEntity pilot = Cast<AAS_PlayerEntity>(oldPilot);
				pilot.OnDismountSignal.Emit();
			}

			UAS_Thread_PlayerOnDismountThread OnPlayerDismountThread = Cast<UAS_Thread_PlayerOnDismountThread>( CreateThread( UAS_Thread_PlayerOnDismountThread::StaticClass(), this ) );
			OnPlayerDismountThread.Init( oldPilot, this );
			oldPilot.Server_OnPawnDeath.Unbind( this, n"OnPlayerDeath" );
		}
	}

	access special = protected, Mount;
	access:special void Dismount(ANCPlayerCharacter ncPlayer)
	{
		AAS_PlayerEntity player = Cast<AAS_PlayerEntity>( ncPlayer );
		if ( !IsValid( player ) || player != GetOwnerPlayer() )
			return;

		if (ANCPlayerCharacter::UsePredictedMountInput())
		{
			// Does the same actions as below
			// PreVehicleDisembark
			// ClearAndDetachPilot
			// OnVehicleDisembark
			PilotDisembark();
		}
		else
		{
			PreVehicleDisembark(player);
			RemovePilot();
			OnVehicleDisembark(player);
		}
	}

	void AnimHideMount()
	{
		ScriptAssert( IsServer(), "SERVER ONLY" );
		ScriptAssert( !IsValid(GetPilot()), "Can only be called on a mount without a pilot" );
		PlayMountAnimation(EAnimMontageActivity::ACTM_MOUNT_DISEMBARK);
		SetVehicleState(EVehicleActiveState::Vehicle_Disembark);
	}

	/****************************************************************\

	 ██████  ███    ██      ██████ ██   ██  █████  ███    ██  ██████  ███████
	██    ██ ████   ██     ██      ██   ██ ██   ██ ████   ██ ██       ██
	██    ██ ██ ██  ██     ██      ███████ ███████ ██ ██  ██ ██   ███ █████
	██    ██ ██  ██ ██     ██      ██   ██ ██   ██ ██  ██ ██ ██    ██ ██
	 ██████  ██   ████      ██████ ██   ██ ██   ██ ██   ████  ██████  ███████

	\****************************************************************/
	UFUNCTION()
	protected void sh_OnPilotChanged( ANCNetCharacter oldNetPilot, ANCNetCharacter newNetPilot )
	{
		ANCPlayerCharacter oldPilot	   = Cast<ANCPlayerCharacter>( oldNetPilot );
		ANCPlayerCharacter newPilot	   = Cast<ANCPlayerCharacter>( newNetPilot );
		UAS_VehicleMountAssetData data = Mount::GetMountAssetData( this );

		/////////////////////////////////////////////////////////////////////////////
		//	SHOOTING PROTOTYPE
		//sh_OnPilotChanged_OffhandVMProto( oldPilot, newPilot );

		// SHARED LOGIC
		if ( IsValid( newPilot ) )
		{
			newPilot.IgnoreActorInUseTrace( this, true );
			useComponent.DisableUsable();

			// not sure why I have to do this, but if I don't, the player (and his hitbox ) will be hovering high above
			newPilot.GetPlayerMesh3P().AttachToComponent( VehicleMesh_3P );
		}

		if ( IsValid( oldPilot ) )
		{
			oldPilot.IgnoreActorInUseTrace( this, false );
			Thread( this, n"IgnorePlayerUntilNotOverlapping", oldPilot );

			//@RAVI: not a great way to reset the collision box on the server... I don't like hard coding the offset
			USceneComponent scene = oldPilot.GetRendererRootComponent();
			oldPilot.GetPlayerMesh3P().AttachTo( scene, NAME_None );
			oldPilot.GetPlayerMesh3P().SetWorldScale3D( FVector::OneVector );
		}

		// SERVER / CLIENT SPECIFIC LOGIC
		if ( IsServer() )
		{
			if ( IsValid( newPilot ) )
			{
				ScriptCallbacks().server_onMountStateChanged.Broadcast( newPilot, this, true );
				newPilot.HealthComponent.BP_OnPostReceivedDamage.AddUFunction( this, n"OnPilotDamaged" );
			}

			if ( IsValid( oldPilot ) )
			{
				ScriptCallbacks().server_onMountStateChanged.Broadcast( oldPilot, this, false );
				oldPilot.HealthComponent.BP_OnPostReceivedDamage.Unbind( this, n"OnPilotDamaged" );
			}
		}
		else
		{
			if ( IsValid( newPilot ) )
			{
				ScriptCallbacks().client_onMountStateChanged.Broadcast( newPilot, this, true );

				UpdateNewPilotTPPSettings( newPilot );
			}

			if ( IsValid( oldPilot ) )
			{
				ScriptCallbacks().client_onMountStateChanged.Broadcast( oldPilot, this, false );

				UpdateOldPilotTPPSettings( oldPilot );
			}

			EndSignalFootstepThread.Emit();
			Thread( this, n"cl_FootstepThread", newPilot );
		}
	}

	private void UpdateNewPilotTPPSettings( ANCPlayerCharacter newPilot )
	{
		const float DEFAULT_SPRING_ARM_LENGTH_MOUNT			   = GetCvarBool( "ScriptDebug.LockTPPCamDist" ) ? GetCvarFloat( "ScriptDebug.LockTPPCamDist" ) : 750;
		const float DEFAULT_CAMERA_SETTINGS_LERP_IN_TIME	   = 0.5f;
		const EEasingFunc DEFAULT_CAMERA_SETTINGS_LERP_IN_EASE = EEasingFunc::EaseIn;

		if ( newPilot.IsLocallyControlled() )
		{
			if ( tppCameraOverrideID == -1 )
			{
				FTPPCameraSettings mountTPPCameraSettings;
				mountTPPCameraSettings.Set( ETPPCameraSetting::TargetArmLength, DEFAULT_SPRING_ARM_LENGTH_MOUNT );

				tppCameraOverrideID = newPilot.GetTPPCameraSettings().AddOverrideSettingsWithLerp( n"VehicleMountCameraSettings", mountTPPCameraSettings, DEFAULT_CAMERA_SETTINGS_LERP_IN_TIME, DEFAULT_CAMERA_SETTINGS_LERP_IN_EASE );
			}

			UAS_TPPCameraComponent comp = GetPilot().GetTPPCameraComponent();
			const bool force3P = IsValid( comp ) && comp.IsTPPEnabled();
			
			Force3PMeshVisible(force3P);
		}
	}

	private void UpdateOldPilotTPPSettings( ANCPlayerCharacter oldPilot )
	{
		if ( tppCameraOverrideID != -1 )
		{
			const float DEFAULT_CAMERA_SETTINGS_LERP_OUT_TIME		= 0.5f;
			const EEasingFunc DEFAULT_CAMERA_SETTINGS_LERP_OUT_EASE = EEasingFunc::EaseIn;

			oldPilot.GetTPPCameraSettings().ClearOverrideSettingsWithLerp( tppCameraOverrideID, DEFAULT_CAMERA_SETTINGS_LERP_OUT_TIME, DEFAULT_CAMERA_SETTINGS_LERP_OUT_EASE );

			tppCameraOverrideID = -1;
		}
	}

	UFUNCTION()
	private void IgnorePlayerUntilNotOverlapping( UNCCoroutine co, ANCPlayerCharacter player )
	{
		co.EndOnDestroyed( player );

		player.CapsuleComponent.IgnoreActorWhenMoving( this, true );

		while ( true )
		{
			co.Wait( 0.1 );

			if ( player.IsOverlappingActor( this ) )
				continue;

			player.CapsuleComponent.IgnoreActorWhenMoving( this, false );
			break;
		}
	}

	/****************************************************************\

	 █████   ██████ ████████ ██ ██    ██ ███████     ███████ ██████  ██████  ██ ███    ██ ████████
	██   ██ ██         ██    ██ ██    ██ ██          ██      ██   ██ ██   ██ ██ ████   ██    ██
	███████ ██         ██    ██ ██    ██ █████       ███████ ██████  ██████  ██ ██ ██  ██    ██
	██   ██ ██         ██    ██  ██  ██  ██               ██ ██      ██   ██ ██ ██  ██ ██    ██
	██   ██  ██████    ██    ██   ████   ███████     ███████ ██      ██   ██ ██ ██   ████    ██

	\****************************************************************/
	UFUNCTION()
	private void OnActiveSprintInput( ANCPlayerCharacter player, bool onEmbark )
	{
		Thread( this, n"OnActiveSprintInputThread", player, onEmbark );
	}

	UFUNCTION()
	private void OnActiveSprintInputThread( UNCCoroutine co, ANCPlayerCharacter player, bool onEmbark )
	{
		// this helps the animation/vfx line up better with embark timing
		if ( onEmbark )
			co.Wait( 0.35 );

		if ( !IsValid( player ) )
			return;

		FVehicleMountData data = GetMountData();
		float speed			   = motionModel.GetSpeed();

		EMountMontageACT ACTV = EMountMontageACT::SCRIPT_ACTIVE_SPRINT_IDLE;
		if ( speed > data.Asset.Anim.ActiveSprint_SprintSpeed )
			ACTV = EMountMontageACT::SCRIPT_ACTIVE_SPRINT_SPRINT;
		else if ( speed > data.Asset.Anim.ActiveSprint_WalkSpeed )
			ACTV = EMountMontageACT::SCRIPT_ACTIVE_SPRINT_WALK;

		// server and client
		UAS_VehicleMountAssetData assetData = data.Asset;
		animComponent.sh_OnMountPlayActivity.Broadcast( ACTV, assetData, this );
		animComponent.sh_OnPilotPlayActivity.Broadcast( ACTV, assetData, player, offhandVMProto_OffhandVM );
	}

	/****************************************************************\

	██████  ██████   █████  ██   ██ ██ ███    ██  ██████
	██   ██ ██   ██ ██   ██ ██  ██  ██ ████   ██ ██
	██████  ██████  ███████ █████   ██ ██ ██  ██ ██   ███
	██   ██ ██   ██ ██   ██ ██  ██  ██ ██  ██ ██ ██    ██
	██████  ██   ██ ██   ██ ██   ██ ██ ██   ████  ██████

	\****************************************************************/
	UFUNCTION()
	private void OnStartBraking( ANCPlayerCharacter player )
	{
		if ( !IsValid( player ) )
			return;

		// server and client
		UAS_VehicleMountAssetData data = Mount::GetMountAssetData( this );
		animComponent.sh_OnMountPlayActivity.Broadcast( EMountMontageACT::SCRIPT_BRAKE_START, data, this );
		animComponent.sh_OnPilotPlayActivity.Broadcast( EMountMontageACT::SCRIPT_BRAKE_START, data, player, offhandVMProto_OffhandVM );
	}

	UFUNCTION()
	private void OnEndBraking( ANCPlayerCharacter player )
	{
		if ( !IsValid( player ) )
			return;

		// server and client
		UAS_VehicleMountAssetData data = Mount::GetMountAssetData( this );
		animComponent.sh_OnMountPlayActivity.Broadcast( EMountMontageACT::SCRIPT_BRAKE_END, data, this );
		animComponent.sh_OnPilotPlayActivity.Broadcast( EMountMontageACT::SCRIPT_BRAKE_END, data, player, offhandVMProto_OffhandVM );
	}

	/****************************************************************\

	 ██████  ███████ ███████ ██   ██  █████  ███    ██ ██████      ██████  ██████   ██████  ████████  ██████
	██    ██ ██      ██      ██   ██ ██   ██ ████   ██ ██   ██     ██   ██ ██   ██ ██    ██    ██    ██    ██
	██    ██ █████   █████   ███████ ███████ ██ ██  ██ ██   ██     ██████  ██████  ██    ██    ██    ██    ██
	██    ██ ██      ██      ██   ██ ██   ██ ██  ██ ██ ██   ██     ██      ██   ██ ██    ██    ██    ██    ██
	 ██████  ██      ██      ██   ██ ██   ██ ██   ████ ██████      ██      ██   ██  ██████     ██     ██████

	\****************************************************************/
	access offhandVMProto = private, AAS_VehicleMountOffhandVM, UAS_MountViewModelAnimInstanceBearHack;
	private AAS_VehicleMountOffhandVM offhandVMProto_OffhandVM;

	ANCWeapon modHoldingWeapon;

	private void sh_OnPilotChanged_OffhandVMProto( ANCNetCharacter oldNetPilot, ANCNetCharacter newNetPilot )
	{
		ANCPlayerCharacter oldPilot = Cast<ANCPlayerCharacter>( oldNetPilot );
		ANCPlayerCharacter newPilot = Cast<ANCPlayerCharacter>( newNetPilot );

		if ( IsValid( newPilot ) )
		{
			if ( IsServer() )
			{
				newPilot.Server_OnActiveWeaponChanged.AddUFunction( this, n"OffhandVMProto_OnWeaponChanged" );

				ANCWeapon activeWeapon = newPilot.GetActiveWeapon();
				bool isAllowed		   = IsWeaponAllowedWithOffhandMount( activeWeapon );
				if ( !isAllowed )
				{
					newPilot.EquipLastPrimaryWeapon();
					activeWeapon = nullptr;
				}

				OffhandVMProto_OnWeaponChanged( EViewmodelArm::MainHand, nullptr, activeWeapon );
			}
			if ( !ANCVehicle::Uses1PCodeAnim() && IsClient() && newPilot.IsLocallyControlled() && !IsValid( offhandVMProto_OffhandVM ) )
			{
				offhandVMProto_OffhandVM = Cast<AAS_VehicleMountOffhandVM>( SpawnActor( AAS_VehicleMountOffhandVM::StaticClass() ) );
				offhandVMProto_OffhandVM.InitArms( newPilot, this );
				newPilot.Client_OnActiveWeaponChanged.AddUFunction( this, n"OffhandVMProto_OnWeaponChanged" );
			}
		}

		if ( IsValid( oldPilot ) )
		{
			if ( IsServer() )
			{
				oldPilot.Server_OnActiveWeaponChanged.Unbind( this, n"OffhandVMProto_OnWeaponChanged" );
				OffhandVMProto_OnWeaponChanged( EViewmodelArm::MainHand, nullptr, nullptr );
			}
			if ( IsClient() && oldPilot.IsLocallyControlled() )
			{
				oldPilot.Client_OnActiveWeaponChanged.Unbind( this, n"OffhandVMProto_OnWeaponChanged" );
			}
		}
	}

	bool IsWeaponAllowedWithOffhandMount( ANCWeapon weapon )
	{
		if ( !IsValid( weapon ) )
			return false;

		bool isAllowed = ( weapon.GetWeaponSlot() != WeaponSlot::RaidToolsSlot &&
						   weapon.GetWeaponSlot() != WeaponSlot::RaidUltSlot );

		if ( isAllowed && Mount::NotAllowedOffhandWeapon( weapon.GetWeaponClass() ) )
			isAllowed = false;
		else if ( !isAllowed && Mount::IsAllowedOffhandWeapon( weapon.GetWeaponClass() ) )
			isAllowed = true;

		return isAllowed;
	}

	UFUNCTION()
	private void OffhandVMProto_OnWeaponChanged( EViewmodelArm vmArm, ANCWeapon OldActiveWeapon, ANCWeapon NewActiveWeapon )
	{
		if ( vmArm != EViewmodelArm::MainHand )
			return;

		motionModel.local_holdThrow = false;

		FVehicleMountData data = GetMountData();

		// Unbind from old weapon
		if ( IsValid( modHoldingWeapon ) && modHoldingWeapon != NewActiveWeapon )
		{
			if ( IsServer() )
				modHoldingWeapon.RemoveMod( data.Asset.Anim.WeaponModName );

			if ( IsServer() || modHoldingWeapon.GetWeaponOwner().IsLocallyControlled() )
				OnWeaponStateChange_UnbindHack( modHoldingWeapon );

			modHoldingWeapon = nullptr;
		}

		if ( IsValid( NewActiveWeapon ) )
		{
			/*	RAVI -> this doesn't feel good because it waits for the weapon to change to receive this event...
				we need predictive dismount when the player hits the button not after the current weapon is holstered	*/
			bool isAllowed = IsWeaponAllowedWithOffhandMount( NewActiveWeapon );
			if ( !isAllowed )
			{
				if ( IsServer() )
					Dismount( GetPilot() );
			}
			else if ( NewActiveWeapon.HasMod( data.Asset.Anim.WeaponModName ) )
			{
				if ( IsServer() )
					NewActiveWeapon.AddMod( data.Asset.Anim.WeaponModName );

				if ( IsServer() || NewActiveWeapon.GetWeaponOwner().IsLocallyControlled() )
					OnWeaponStateChange_AddUFunctionHack( NewActiveWeapon, this, n"OffhandVMProto_OnWeaponStateChanged" );

				modHoldingWeapon = NewActiveWeapon;
			}
		}
	}
	////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
	/* 	@ravi - we don't have a callback on weapons to bind to for when the weapon state changes... so I made a
		fake one using thread objects. Obviously this is bad and we should just get a real code feature for it 	*/
	TMap<ANCWeapon, UAS_Thread_HackOnWeaponStateChange> HackOnWeaponStateChangeThreads;
	private void OnWeaponStateChange_AddUFunctionHack( ANCWeapon weapon, UObject bindObj, FName functionName )
	{
		UAS_Thread_HackOnWeaponStateChange OnWeaponStateChange = Cast<UAS_Thread_HackOnWeaponStateChange>( CreateThread( UAS_Thread_HackOnWeaponStateChange::StaticClass(), this ) );
		OnWeaponStateChange.AddUFunction( weapon, bindObj, functionName );
		HackOnWeaponStateChangeThreads.Add( weapon, OnWeaponStateChange );
	}

	private void OnWeaponStateChange_UnbindHack( ANCWeapon weapon )
	{
		if ( HackOnWeaponStateChangeThreads.Contains( weapon ) )
			HackOnWeaponStateChangeThreads[weapon].Cancel();
	}
	////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

	UFUNCTION()
	private void OffhandVMProto_OnWeaponStateChanged( ANCWeapon weapon, EWeaponState oldState, EWeaponState newState )
	{
		if ( !IsValid( GetPilot() ) || weapon != GetPilot().GetActiveWeapon() )
			return;

		if ( oldState == EWeaponState::TossPrepHold && newState != EWeaponState::TossPrepHold )
			motionModel.local_holdThrow = false;
		else if ( newState == EWeaponState::TossPrepHold )
			motionModel.local_holdThrow = true;

		motionModel.OnUpdateFreelook();
	}

	/****************************************************************\

	███████  ██████   ██████  ████████ ███████ ████████ ███████ ██████  ███████
	██      ██    ██ ██    ██    ██    ██         ██    ██      ██   ██ ██
	█████   ██    ██ ██    ██    ██    ███████    ██    █████   ██████  ███████
	██      ██    ██ ██    ██    ██         ██    ██    ██      ██           ██
	██       ██████   ██████     ██    ███████    ██    ███████ ██      ███████

	\****************************************************************/
	private UNiagaraComponent mount3pFx;
	private bool UpdateMountVFXThreadRunning = false;
	private FNCCoroutineSignal EndSignalFootstepThread;

	UFUNCTION()
	void cl_FootstepThread( UNCCoroutine co, ANCPlayerCharacter pilot )
	{
		ScriptAssert( IsClient(), "CLIENT ONLY" );
		if ( !IsValid( pilot ) )
			return;

		co.EndOnDestroyed( pilot );
		co.EndOn( this, EndSignalFootstepThread );

		FVehicleMountData data = GetMountData();
		int lastFootStepTime   = 0;

		while ( true )
		{
			co.Wait( 0.05 );

			if ( IsFalling() )
				continue;

			float speed2D = motionModel.GetSpeed();
			if ( speed2D < data.Asset.Audio.WalkMinCadenceSpeed )
				continue;

			FVector2D inputRange  = FVector2D( data.Asset.Audio.WalkMinCadenceSpeed, localData.SprintMaxCadenceSpeed );
			FVector2D outputRange = FVector2D( data.Asset.Audio.WalkMaxCadenceDelay, data.Asset.Audio.SprintMinCadenceDelay );
			float cadenceTime	  = Math::GetMappedRangeValueClamped( inputRange, outputRange, speed2D );
			float elapsedTime	  = TO_SECONDS( GetGameTimeMS() - lastFootStepTime );

			if ( elapsedTime >= cadenceTime )
			{
				UNCAudioAsset sound1p = IsSprinting() ? data.Asset.Audio.GallopFootstep1p : data.Asset.Audio.TrotFootstep1p;
				UNCAudioAsset sound3p = IsSprinting() ? data.Asset.Audio.GallopFootstep3p : data.Asset.Audio.TrotFootstep3p;
				UNCAudioAsset sound	  = pilot.IsLocallyControlled() ? sound1p : sound3p;

				if ( !ANCVehicle::UsesCodeAudio() )
				{
					FAudioResultData resultData = Client_EmitSoundOnEntity( sound, this );
				}

				// optimization -> fx don't linger around long enough to see them behind you if you turn
				if ( cl_ShouldPlayFootstepVFX( pilot ) )
					cl_TrySpawnFootstepVFX( pilot, data );

				lastFootStepTime = GetGameTimeMS();

				if ( GetCvarBool( f"VehicleMount.EnableScreenshake" ) )
					cl_PlayScreenShakeOnFootStep( pilot, data, speed2D );
			}
		}
	}

	void cl_PlayScreenShakeOnFootStep( ANCPlayerCharacter pilot, FVehicleMountData mountData, float speed2D )
	{
		FVector2D inputRange = FVector2D( mountData.Asset.Audio.WalkMinCadenceSpeed, localData.SprintMaxCadenceSpeed );

		FScreenShakeStruct ShakeData;
		ShakeData.RotationFrequency	   = Math::GetMappedRangeValueClamped( inputRange, FVector2D( 5.0, 7.0 ), speed2D );
		ShakeData.RotationAmplitude	   = Math::GetMappedRangeValueClamped( inputRange, FVector2D( 0.4, 1.25 ), speed2D );
		ShakeData.RotationAmplitudeMod = FRotator( 0.1, 0, 1 );

		ShakeData.Duration	   = 0.5;
		ShakeData.BlendInTime  = 0.1;
		ShakeData.BlendOutTime = 0.4;

		pilot.PlayScreenShakeAdvanced( ShakeData );
	}

	void cl_TrySpawnFootstepVFX( ANCPlayerCharacter pilot, FVehicleMountData data )
	{
		ScriptAssert( IsClient(), "MUST run on client" );

		if ( UpdateMountVFXThreadRunning )
			return;

		if ( !IsValid( mount3pFx ) )
			mount3pFx = Client_SpawnEffectOnRendererComponent_Looping( data.Asset.FX.mountSprintLoopFx, VehicleMesh_3P, n"root_Socket" );

		Thread( this, n"cl_UpdateFootstepVFXThread", pilot );
	}

	UFUNCTION()
	private void cl_UpdateFootstepVFXThread( UNCCoroutine co, ANCPlayerCharacter pilot )
	{
		UpdateMountVFXThreadRunning = true;

		co.EndOnDestroyed( pilot );
		co.EndOn( this, this.HealthComponent.OnDeathSignal );
		co.OnCoroutineEnd.AddUFunction( this, n"cl_OnThreadEndUpdateFootstepVFX" );

		mount3pFx.Activate();

		while ( true )
		{
			if ( !cl_ShouldPlayFootstepVFX( pilot ) )
				return;

			if ( ANCVehicle::UsesCodeVFX() )
			{
				if ( mount3pFx.IsActive() )
					mount3pFx.Deactivate();
			}
			else
			{
				if ( !mount3pFx.IsActive() )
					mount3pFx.Activate();

				if ( IsFalling() && !mount3pFx.IsPaused() )
					mount3pFx.SetPaused( true );

				if ( !IsFalling() && mount3pFx.IsPaused() )
					mount3pFx.SetPaused( false );

				float sprintAlpha = Math::GetMappedRangeValueClamped( FVector2D( 600, 900 ), FVector2D( 0, 1 ), motionModel.GetSpeed() );
				mount3pFx.SetNiagaraVariableFloat( "spawnRate", sprintAlpha );
			}

			co.Wait( 0.05 );
		}
	}

	UFUNCTION()
	private void cl_OnThreadEndUpdateFootstepVFX( FNCCoroutineEndParams endParams )
	{
		ScriptAssert( IsClient(), "MUST run on client" );

		UpdateMountVFXThreadRunning = false;
		if ( IsValid( mount3pFx ) )
			mount3pFx.Deactivate();
	}

	bool cl_ShouldPlayFootstepVFX( ANCPlayerCharacter pilot )
	{
		ScriptAssert( IsClient(), "MUST run on client" );

		if ( !pilot.IsLocallyControlled() )
			return true;

		UAS_TPPCameraComponent comp = pilot.GetTPPCameraComponent();
		if ( !IsValid( comp ) )
			return false;

		return comp.IsTPPEnabled();
	}

	/****************************************************************\

	██     ██ ██ ███    ██ ██████      ███████ ███████ ██   ██
	██     ██ ██ ████   ██ ██   ██     ██      ██       ██ ██
	██  █  ██ ██ ██ ██  ██ ██   ██     ███████ █████     ███
	██ ███ ██ ██ ██  ██ ██ ██   ██          ██ ██       ██ ██
	 ███ ███  ██ ██   ████ ██████      ███████ ██      ██   ██

	\****************************************************************/
	bool isPlayingWindSFX = false; // these work because they're localClient values only
	int windSFXEventID	  = -1;	   // these work because they're localClient values only
	UFUNCTION()
	private void cl_OnPilotChangedWindSFX( ANCNetCharacter oldNetPilot, ANCNetCharacter newNetPilot )
	{
		ScriptAssert( IsClient(), "CLIENT ONLY" );

		ANCPlayerCharacter oldPilot = Cast<ANCPlayerCharacter>( oldNetPilot );
		ANCPlayerCharacter newPilot = Cast<ANCPlayerCharacter>( newNetPilot );

		if ( IsValid( newPilot ) && newPilot.IsLocallyControlled() )
		{
			Thread( this, n"WindSFXUpdateThread", newPilot );
		}

		if ( IsValid( oldPilot ) && oldPilot.IsLocallyControlled() )
		{
			endsignalWindSFXUpdateThread.Emit();

			FVehicleMountData data = GetMountData();

			StopWindSound();
			Client_SetRTPCValue( oldPilot, WindSpeedRTPC, 0.0f, 5 );
			Client_EmitSoundOnEntity( data.Asset.Audio.WindDismountOneShot, oldPilot );
		}
	}

	FNCCoroutineSignal endsignalWindSFXUpdateThread;

	UFUNCTION()
	void WindSFXUpdateThread( UNCCoroutine co, ANCPlayerCharacter pilot )
	{
		endsignalWindSFXUpdateThread.Emit();

		co.EndOnDestroyed( pilot );
		co.EndOn( this, endsignalWindSFXUpdateThread );

		FVehicleMountData data = GetMountData();
		float inActiveSprint   = localData.ActiveSprint_SprintSpeedMax;
		float activeSprint	   = localData.RawSprintSpeedMax;
		float activeBoost	   = localData.RawBoostSpeedMax - 5.0;

		while ( true )
		{
			float curSpeed	= motionModel.GetSpeed();
			float threshold = data.Asset.Audio.WindRTPCLowToHighEndThreshold;
			float windAlpha;
			if ( curSpeed <= activeSprint )
			{
				windAlpha = Math::GetMappedRangeValueClamped( FVector2D( inActiveSprint, activeSprint ), FVector2D( 0, 1.0 ), curSpeed );
				windAlpha = Math::EaseOut( 1, threshold, windAlpha, data.Asset.Audio.WindRTPCLowEndExponent );
			}
			else
			{
				windAlpha = Math::GetMappedRangeValueClamped( FVector2D( activeSprint, activeBoost ), FVector2D( 0, 1.0 ), curSpeed );
				windAlpha = Math::EaseIn( threshold, 100, windAlpha, data.Asset.Audio.WindRTPCHighEndExponent );
			}

			if ( curSpeed >= data.Asset.Audio.WindSFXMinSpeed )
			{
				Client_SetRTPCValue( pilot, WindSpeedRTPC, windAlpha, data.Asset.Audio.WindSFXInterpolationTimeMS );
				StartWindSound( pilot );

				if ( GetCvarBool( f"VehicleMount.DebugWindSFX" ) )
					Print( f"Min: {inActiveSprint} | Max: {activeBoost} | speed: {curSpeed} | windAlpha: {windAlpha}" );
			}
			else if ( isPlayingWindSFX )
			{
				Client_SetRTPCValue( pilot, WindSpeedRTPC, 0.0f, data.Asset.Audio.WindSFXInterpolationTimeMS );
				StopWindSound();
			}

			co.Wait( 0.1 ); // doesn't need to tick every frame
		}
	}

	void StartWindSound( ANCPlayerCharacter pilot )
	{
		ScriptAssert( IsClient(), "CLIENT ONLY" );

		if ( isPlayingWindSFX )
			return;

		FVehicleMountData data		= GetMountData();
		FAudioResultData resultData = Client_EmitSoundOnEntity( data.Asset.Audio.WindSustain, pilot );
		windSFXEventID				= resultData.EventID;
		isPlayingWindSFX			= true;
	}

	UFUNCTION()
	void StopWindSound()
	{
		ScriptAssert( IsClient(), "CLIENT ONLY" );

		if ( !isPlayingWindSFX || windSFXEventID == -1 )
			return;

		FVehicleMountData data = GetMountData();
		isPlayingWindSFX	   = false;

		Client_StopSound( windSFXEventID );
	}

	/****************************************************************\

	██    ██ ████████ ██ ██      ██ ████████ ██    ██
	██    ██    ██    ██ ██      ██    ██     ██  ██
	██    ██    ██    ██ ██      ██    ██      ████
	██    ██    ██    ██ ██      ██    ██       ██
	 ██████     ██    ██ ███████ ██    ██       ██

	\****************************************************************/
	private FVehicleMountData GetCurrentVehicleData()
	{
		FGameplayTag tag			  = Mount::GetGameplayTagFromMount( this );
		bool isEkon = tag == GameplayTags::Mounts_Ekon;
		FVehicleMountData defaultData = isEkon ? Mount::GetMountVehicleData( tag, Mount::VEHICLE_CORE_DATA_DEFAULT_EKON ) : Mount::GetMountVehicleData( tag, Mount::VEHICLE_CORE_DATA_DEFAULT );

		if ( GetCoreDataEntryName() != NAME_None )
		{
			FName coreName = FName( net_CoreDataEntry.ToString() );
			if ( !Mount().CoreDataLibrary.Contains( coreName ) )
			{
				Warning( f"Core Library is missing entry: {coreName}" );
				return defaultData;
			}

			FVehicleMountData newData( Mount().CoreDataLibrary[coreName], defaultData.Asset );
			return newData;
		}

		return defaultData;
	}

	void SetCustomCoreData( FName libraryEntry )
	{
		ScriptAssert( IsServer(), "SERVER_ONLY" );

		FString oldValue = net_CoreDataEntry.ToString();
		net_CoreDataEntry.SetNetValue( libraryEntry.ToString() );

		OnCoreDataEntryChanged( oldValue, libraryEntry.ToString() );
	}

	void ClearCustomCoreData()
	{
		ScriptAssert( IsServer(), "SERVER_ONLY" );

		FString oldValue = net_CoreDataEntry.ToString();
		if ( FName( oldValue ) == NAME_None )
			return;

		net_CoreDataEntry.SetNetValue( NAME_None.ToString() );

		OnCoreDataEntryChanged( oldValue, net_CoreDataEntry.ToString() );
	}

	FName GetCoreDataEntryName()
	{
		return FName( net_CoreDataEntry.ToString() );
	}

	UFUNCTION()
	private void OnCoreDataEntryChanged( FString oldValue, FString newValue )
	{
		FVehicleMountData newData = GetCurrentVehicleData();

		motionModel.CachedMountData.Init( newData );
		CachedMountData.Init( newData );
	}

	UFUNCTION( BlueprintPure )
	bool IsSprinting()
	{
		return motionModel.IsSprinting();
	}

	UFUNCTION( BlueprintPure )
	bool IsFalling() const
	{
		return motionModel.IsFalling();
	}

	float GetBoostSpeedAlpha()
	{
		float Alpha;

		if ( !GetCvarBool( f"VehicleMount.EnableActiveSprint" ) )
		{
			float deltaSprintBoost = localData.BoostSpeedMax - localData.SprintSpeedMax;
			Alpha				   = Math::GetMappedRangeValueClamped( FVector2D( localData.SprintSpeedMax, localData.SprintSpeedMax + ( deltaSprintBoost * 0.5 ) ), FVector2D( 0, 1 ), GetVelocity().Size2D() );
		}
		else
		{
			float speed2D = GetVelocity().Size2D();
			Alpha		  = Math::GetMappedRangeValueClamped( FVector2D( localData.SprintSpeedMax, localData.RawBoostSpeedMax ), FVector2D( 0.0, 1 ), speed2D );
			Alpha		  = Math::EaseIn( 0, 1, Alpha, 2 );
		}

		return Alpha;
	}
}

/****************************************************************\

██     ██ ███████  █████  ██████      ██   ██  █████   ██████ ██   ██ ███████
██     ██ ██      ██   ██ ██   ██     ██   ██ ██   ██ ██      ██  ██  ██
██  █  ██ █████   ███████ ██████      ███████ ███████ ██      █████   ███████
██ ███ ██ ██      ██   ██ ██          ██   ██ ██   ██ ██      ██  ██       ██
 ███ ███  ███████ ██   ██ ██          ██   ██ ██   ██  ██████ ██   ██ ███████

\****************************************************************/
/* 	@ravi - we don't have a callback on weapons to bind to for when the weapon state changes... so I made a fake one
	using thread objects. Obviously this is bad and we should just get a real code feature for it 	*/
event void
FEvent_HackOnWeaponStateChange( ANCWeapon weapon, EWeaponState oldState, EWeaponState newState );
class UAS_Thread_HackOnWeaponStateChange : UAS_Thread
{
	ANCWeapon weapon;
	UObject bindObj;
	FName functionName;
	FEvent_HackOnWeaponStateChange _delegate;

	void AddUFunction( ANCWeapon inWeapon, UObject inBindObj, FName inFunctionName )
	{
		weapon		 = inWeapon;
		bindObj		 = inBindObj;
		functionName = inFunctionName;

		_delegate.AddUFunction( bindObj, functionName );
		Start();
	}

	void OnThreadStart( UNCCoroutine co ) override
	{
		Super::OnThreadStart( co );

		co.EndOnDestroyed( weapon );

		EWeaponState oldState = weapon.GetWeaponState();
		while ( IsValid( bindObj ) )
		{
			EWeaponState newState = weapon.GetWeaponState();
			if ( newState != oldState )
				_delegate.Broadcast( weapon, oldState, newState );

			oldState = newState;

			co.Wait( 0.01 );
		}
	}

	void OnThreadEnd( FNCCoroutineEndParams params ) override
	{
		Super::OnThreadEnd( params );
		if ( IsValid( bindObj ) )
			_delegate.Unbind( bindObj, functionName );
	}
}

class UAS_Thread_PlayerOnEmbarkThread : UAS_Thread
{
	ANCPlayerCharacter player;
	AAS_VehicleMount mount;

	void Init( ANCPlayerCharacter inPlayer, AAS_VehicleMount inMount )
	{
		player = inPlayer;
		mount  = inMount;
		Start();
	}

	void OnThreadStart( UNCCoroutine co ) override
	{
		Super::OnThreadStart( co );

		co.EndOn( player, Cast<AAS_PlayerEntity>( player ).OnDeathSignal );
		co.EndOnDestroyed( player );
		co.EndOnDestroyed( mount );

		if ( player.IsCrouching() )
			player.DisableCrouch();

		player.HolsterWeapons();

		FVehicleMountData data = mount.GetMountData();

		co.Wait( data.Core.Gameplay.WeaponEmbarkHolsterTime );
	}

	void OnThreadEnd( FNCCoroutineEndParams params ) override
	{
		Super::OnThreadEnd( params );

		if ( !IsValid( player ) )
			return;

		player.EnableCrouch();

		if ( IsAlive( player ) )
			player.DeployWeapons();
	}
}

class UAS_Thread_PlayerOnDismountThread : UAS_Thread
{
	ANCPlayerCharacter player;
	FVehicleMountData data;

	void Init( ANCPlayerCharacter inPlayer, AAS_VehicleMount inMount )
	{
		player = inPlayer;
		data   = inMount.GetMountData();

		int activeSlot		 = player.GetActiveWeaponSlot();
		bool isActivePrimary = activeSlot == WeaponSlot::PrimarySlot0 || activeSlot == WeaponSlot::PrimarySlot1;
		if ( !isActivePrimary )
			return;

		if ( !IsValid( player.GetActiveWeapon() ) )
			return;

		// reloading / other feel good stuff? don't interrupt
		EWeaponState state = player.GetActiveWeapon().GetWeaponState();
		switch ( state )
		{
			case EWeaponState::Reloading:
			case EWeaponState::Holster:
			case EWeaponState::TossPrep:
			case EWeaponState::TossPrepHold:
			case EWeaponState::Deploy:
			case EWeaponState::Rechambering:
			case EWeaponState::Toss:
			case EWeaponState::TossCallback:
			case EWeaponState::TossCatch:
			case EWeaponState::Overheat:
				return;

			default:
			{
				// do nothing
			}
			break;
		}

		Start();
	}

	void OnThreadStart( UNCCoroutine co ) override
	{
		Super::OnThreadStart( co );

		co.EndOn( player, Cast<AAS_PlayerEntity>( player ).OnDeathSignal );
		co.EndOnDestroyed( player );

		player.HolsterWeapons();

		co.Wait( data.Core.Gameplay.WeaponDismountHolsterTime );
	}

	void OnThreadEnd( FNCCoroutineEndParams params ) override
	{
		Super::OnThreadEnd( params );

		if ( !IsValid( player ) )
			return;

		if ( IsAlive( player ) )
			player.DeployWeapons();
	}
}