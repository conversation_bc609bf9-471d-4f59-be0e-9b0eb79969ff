UCLASS()
class UAS_WeaponContext_RaidUlt_LittleBuddy : UAS_RaidUltWeaponContext_Base
{
    default cooldownTime = 360;

    UPROPERTY(EditDefaultsOnly)
    TSubclassOf<AAS_LittleBuddy> buddyClass;

    bool didFindBuddyPosition = false;
    int lastSpawnAttemptTime;
    FVector cachedSpawnPosition;

    TArray<int> slotsToDisable;
    default slotsToDisable.Add( WeaponSlot::PrimarySlot0 );
    default slotsToDisable.Add( WeaponSlot::PrimarySlot1 );
    default slotsToDisable.Add( WeaponSlot::MeleeSlot );
    default slotsToDisable.Add( WeaponSlot::RaidToolsSlot );
    default slotsToDisable.Add( WeaponSlot::CustomBackpackGrenadeSlot );
    default slotsToDisable.Add( WeaponSlot::GrenadeSlot );
    default slotsToDisable.Add( WeaponSlot::TacticalSlot );
    default slotsToDisable.Add( WeaponSlot::AbilityWeaponSwapSlot );
    default slotsToDisable.Add( WeaponSlot::AbilityWeaponSwapSlot2 );
    default slotsToDisable.Add( WeaponSlot::FREE_SLOT_1 );
    bool didDisableWeapons = false;


    UFUNCTION(BlueprintOverride)
    void CodeCallback_OnWeaponActivate(ANCWeapon weapon)
    {
		Super::CodeCallback_OnWeaponActivate(weapon);
		
        if ( !IsServer() )
            return;

        if ( GetGameTimeMS() - lastSpawnAttemptTime < 500 )
            return; //spawn debounce for reactivation;

        didFindBuddyPosition = false;
        AAS_PlayerEntity myPlayer = Cast<AAS_PlayerEntity>( weapon.GetWeaponOwner() );
        bool success = CanSpawnBuddy( weapon, cachedSpawnPosition );

        if ( success )
        {
            lastSpawnAttemptTime = GetGameTimeMS();
            TrySpawnBuddy( weapon, cachedSpawnPosition );
            ConsumeRaidUlt( weapon );
			PlayBattleChatter( weapon );
        }

		for ( int slot : slotsToDisable )
			myPlayer.ServerDisableWeaponSlot( slot, FName( f"Una Raid Ult {slot}" ) );
        didDisableWeapons = true;
    }

    UFUNCTION(BlueprintOverride)
    void CodeCallback_OnWeaponPrimaryAttack(ANCWeapon weapon, FWeaponPrimaryAttackInfo attackInfo,
                                            FWeaponPrimaryAttackReturnParams& returnInfo)
    {
        Super::CodeCallback_OnWeaponPrimaryAttack(weapon, attackInfo, returnInfo);
        
        if ( !IsServer() )
            return;

        if ( didDisableWeapons )
        {
            AAS_PlayerEntity myPlayer = Cast<AAS_PlayerEntity>( weapon.GetWeaponOwner() );
            for ( int slot : slotsToDisable )
                myPlayer.ServerEnableWeaponSlot( slot, FName( f"Una Raid Ult {slot}" ) );
            didDisableWeapons = false;
        }
    }

    UFUNCTION(BlueprintOverride)
    void CodeCallback_OnWeaponDeactivate(ANCWeapon weapon)
    {
        Super::CodeCallback_OnWeaponDeactivate(weapon);
        
        if ( !IsServer() )
            return;

        if ( didDisableWeapons )
        {
            AAS_PlayerEntity myPlayer = Cast<AAS_PlayerEntity>( weapon.GetWeaponOwner() );
            for ( int slot : slotsToDisable )
                myPlayer.ServerEnableWeaponSlot( slot, FName( f"Una Raid Ult {slot}" ) );
            didDisableWeapons = false;
        }
    }




    bool CanRaidUltBeUsedHere(ANCWeapon weapon) override
	{
        AAS_PlayerEntity owner = Cast<AAS_PlayerEntity>( weapon.GetWeaponOwner() );
		if ( IsValid( owner ) && owner.outOfBoundsManager.IsOutOfBounds() )
			return false;
		FVector position;
		return CanSpawnBuddy( weapon, position );
	}

    void TrySpawnBuddy( ANCWeapon weapon, FVector& position )
    {
        AAS_PlayerEntity myPlayer = Cast<AAS_PlayerEntity>( weapon.GetWeaponOwner() );

        AAS_LittleBuddy buddy = Cast<AAS_LittleBuddy>( Server_SpawnEntity( buddyClass, myPlayer, position, myPlayer.GetActorRotation() ) );
        buddy.Deploy(); 

		PlayerStatsManager().IncrementCharacterStatForPlayer( myPlayer, GameplayTags::Classes_Class_Una, GameplayTags::Progression_PlayerStats_Una_UltSummoned );  
    }

	UFUNCTION()
	bool CanSpawnBuddy( ANCWeapon weapon, FVector& position )
	{
        bool success = false;
        AAS_PlayerEntity myPlayer = Cast<AAS_PlayerEntity>( weapon.GetWeaponOwner() );

        FWeaponData data = GetWeaponData( weapon.GetWeaponClass() );
        FNCProjectilePathResults result;

        float launchSpeed = data.ProjectileData.InitialSpeed;
        if ( myPlayer.IsSprinting() )
            launchSpeed = launchSpeed * 20;

        FVector launchVelocity = weapon.GetWeaponFireAngles().Vector() * launchSpeed;

        FNCPredictProjectilePathParams params;
        params.StartLocation = myPlayer.GetEyeLocation();
        params.LaunchVelocity = launchVelocity;
        params.DragCoefficient = data.ProjectileData.DragCoefficient;
        params.bReturnPhysicsMaterial = false;
        params.bTraceWithCollision = true;
        params.bTraceWithChannel = true;
        params.TraceChannel = ECollisionChannel::ECC_PhysicsBody;
        params.bTraceComplex = false;
        params.ActorsToIgnore.Add(myPlayer);
        params.SimFrequency = 20.0f;
        params.MaxSimTime = 10.0f;
        params.OverrideGravityZ = 0.0f;
        params.DrawDebugType = EDrawDebugTrace::None;
        params.DrawDebugTime = 10.0f;
        params.ProjectileRadius = data.ProjectileData.CollisionRadius;
        UNCUtils::PredictProjectilePath_WithParams(myPlayer, result, params);

        position = GetBackupSpawnLocation( result.hitResult.ImpactPoint );
        return true;
	}

    UFUNCTION()
	FVector GetBackupSpawnLocation( FVector originalLocation )
	{
		TArray<FVector> locationCandidates;
		for( int i = 0; i<10; i++ )
		{
			float xMin = originalLocation.X - 250;
			float xMax = originalLocation.X + 250;
			float yMin = originalLocation.Y - 250;
			float yMax = originalLocation.Y + 250;

			float tX = Math::Clamp( Math::RandRange( xMin, xMax ), originalLocation.X - 250, originalLocation.X + 250 );
			float tY = Math::Clamp( Math::RandRange( yMin, yMax ), originalLocation.Y - 250, originalLocation.Y + 250 );

			TArray<AActor> ignoreActors;
			FVector heightCheck = originalLocation + ( FVector::UpVector * 175 );
			FHitResult heightCapTrace = LineTraceSingle( originalLocation + (FVector::UpVector * 50), heightCheck, ETraceTypeQuery::WeaponFine, false, ignoreActors, true );
			float heightCap = heightCheck.Z;
			if ( heightCapTrace.bBlockingHit )
				heightCap = heightCapTrace.ImpactPoint.Z - 50;

			FVector vec = FVector( tX, tY, heightCap );
			FVector groundCheckVec = vec - ( FVector::UpVector * 1250 );
			FHitResult downCheckTrace = LineTraceSingle( vec, groundCheckVec, ETraceTypeQuery::WeaponFine, false, ignoreActors, true  );
			if ( !downCheckTrace.bBlockingHit )
				continue; // no floor, next candidate

			FVector newLoc = downCheckTrace.ImpactPoint;
			if ( Distance2D( newLoc, originalLocation ) < 50 )
				continue; //too close, next candidate;
			
			return newLoc;
		}

		return originalLocation; //if we can't find a new location, default to cached scan location
	}
}