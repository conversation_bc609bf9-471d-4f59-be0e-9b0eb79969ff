event void FOnShieldBreakerThreadUpdate( int countdownMs );

class UAS_ShieldBreakerCraftingAndCooldownThread : UAS_Thread
{
	FOnShieldBreakerThreadUpdate OnShieldBreakerThreadUpdate;

	private int lastCountdownSeconds = GameConst::INDEX_NONE;
	private bool runCraftingThread = false;

	// Just for readability, break out each init function into their own call
	void Init( bool newRunCraftingThread )
	{
		runCraftingThread = newRunCraftingThread;
		Start();
	}

	void OnThreadStart( UNCCoroutine co ) override
	{
		Super::OnThreadStart( co );

		AAS_PlayerEntity localPlayer = Cast<AAS_PlayerEntity>( Client_GetLocalPawn() );
		if ( !IsValid( localPlayer ) )
		{
			Cancel();
			return;
		}

		co.EndOnDestroyed( localPlayer );

		int team							  = localPlayer.GetTeam();
		AAS_TeamStateManager_RaidMode manager = GetTeamStateManager_RaidMode( team );
		if ( <PERSON><PERSON><PERSON><PERSON>( manager ) )
		{
			int countdownMs = 0;

			// If we are running a craftng thread we will loop until we are no longer crafting, otherwise we will use the countdown timer
			EObjectiveState currentState = manager.GetRaidObjectiveState();
			bool loopCondition			 = true;
			while ( loopCondition )
			{
				loopCondition = runCraftingThread ? currentState == EObjectiveState::ShieldBreakerCrafting : countdownMs >= 0;

				FVector position = localPlayer.GetActorLocation();

				// The nearest crafter depends on if we are running the crafting thread or not
				AAS_SBCrafter nearestCrafter = runCraftingThread ?
												   FindDisplayStateShieldBreakerCrafter( position, ESBCrafterState::CRAFTING ) :
												   FindDisplayShieldBreakerCrafterOnCooldown( position );

				if ( IsValid( nearestCrafter ) )
				{
					countdownMs			 = nearestCrafter.GetCurrentCountdownTimeMS();
					int countdownSeconds = Math::CeilToInt( TO_SECONDS( countdownMs ) );
					if ( countdownSeconds != lastCountdownSeconds )
					{
						// Only send out the events every major second
						lastCountdownSeconds = countdownSeconds;
						OnShieldBreakerThreadUpdate.Broadcast( countdownMs );
					}

					UCL_GlobalParameterSystem globalParameters = GetGlobalParameters();
					if ( IsValid( globalParameters ) && IsValid( globalParameters.gameplayParameters ) && currentState == EObjectiveState::ShieldBreakerCrafting )
					{
						int duration = nearestCrafter.CRAFTING_DURATION;
						float alpha	 = duration > 0 ? float( countdownMs ) / duration : 0.0f;
						Material::SetScalarParameterValue( globalParameters.gameplayParameters, MaterialParameter::SHIELDBREAKER_PROGRESS, 1.0f - alpha );
					}
				}

				co.Wait( 0.1f );
			}
		}
	}
}