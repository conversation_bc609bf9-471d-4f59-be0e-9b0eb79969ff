UCLASS( Abstract )
class UAS_EquipmentStatusWidget : UUserWidgetDefault
{
	UPROPERTY()
	UTexture2D icon;

	UPROPERTY( BlueprintReadOnly, BindWidget )
	UImage equipmentIcon;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	UWidgetAnimation OnUsedAnim;

	void SetEquipmentInfo( FPlayerEquipmentData data )
	{
		FLinearColor color;
		if ( data.IsValid() )
		{
			FLootDataStruct lootData = GetLootDataForEquipment( data );
			color					 = GetRarityColor( lootData.rarity );
		}
		else
		{
			color = GlobalHUD::ShadowColor;
		}

		FSlateBrush newBrush	= GetNewBrushThatIsCopyOf( equipmentIcon.Brush );
		newBrush.ResourceObject = icon;
		newBrush.TintColor		= color;
		equipmentIcon.SetBrush( newBrush );

		SetWidgetVisibilitySafe( equipmentIcon, ESlateVisibility::HitTestInvisible );		
	}

	void PlayOnUsedFlash( FPlayerEquipmentData data )
	{
		PlayAnimation( OnUsedAnim );
	}
}