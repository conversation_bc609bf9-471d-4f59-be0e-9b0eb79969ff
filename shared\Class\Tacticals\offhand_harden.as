namespace HardenConst
{
	const float32 HARDEN_AOE_RADIUS	  = 750;
	const float32 HARDEN_AOE_LIFETIME = 60.0;

	const float32 HARDEN_EFFECT_LIFETIME = 90.0;
	const float32 HARDEN_MAX_HEALTH		 = 15;

	const FLinearColor HARDEN_COLOR = FLinearColor(0.275, 0.00, 1.00);
}




UCLASS()
class UAS_WeaponContext_Harden : UAS_WeaponContext_TacticalAbility
{
	default cooldownTime = 60.0f;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset raise_enemy3P;

	FScriptDelegateHandle inputHandle;
	bool isInputHandleSet = false;

	ANCWeapon cachedWeapon;
	AAS_PlayerEntity cachedOwner;

	UFUNCTION( BlueprintOverride )
	void CodeCallback_OnWeaponActivate( ANCWeapon weapon )
	{
		AAS_PlayerEntity asPawn = Cast<AAS_PlayerEntity>( weapon.GetWeaponOwner() );
		cachedOwner				= asPawn;
		cachedWeapon			= weapon;

		if ( IsClient() )
		{
			AAS_HUD myHUD = GetLocalHUD();
			if ( IsValid( myHUD ) )
			{
				myHUD.mainHUDWidget.AddInputContext( n"SpecialWeapon1", GetLocalizedText( Localization::Character_Mara, "tactical_throw" ), true );
				myHUD.mainHUDWidget.AddInputContext( n"Fire", GetLocalizedText( Localization::Character_Mara, "tactical_use" ) );
			}
		}

		if ( IsValid( cachedWeapon ) && cachedWeapon.HasMod( n"Consume") )
			cachedWeapon.RemoveMod( n"Consume" );
		inputHandle		 = asPawn.AddButtonPressedCallback( n"Fire", this, n"OnFirePressed" );
		isInputHandleSet = true;

		cachedWeapon.AddMod( n"EarlyCancel" );

		if ( IsServer() )
			Server_EmitSoundOnEntity_WithSendFlags( raise_enemy3P, asPawn, asPawn, ESendEventFlags::SEND_TO_ENEMIES );
	}

	UFUNCTION( BlueprintOverride )
	void CodeCallback_OnWeaponDeactivate( ANCWeapon weapon )
	{
		if ( IsClient() )
		{
			AAS_HUD myHUD = GetLocalHUD();
			if ( IsValid( myHUD ) )
			{
				myHUD.mainHUDWidget.RemoveInputContext( n"SpecialWeapon1" );
				myHUD.mainHUDWidget.RemoveInputContext( n"Fire" );
			}
		}

		AAS_PlayerEntity asPawn = Cast<AAS_PlayerEntity>( weapon.GetWeaponOwner() );
		if ( isInputHandleSet )
			asPawn.RemoveButtonPressedCallback( n"Fire", inputHandle );
		isInputHandleSet = false;
	}

	UFUNCTION(BlueprintOverride)
	void CodeCallback_OnWeaponTossRelease(ANCWeapon weapon, FWeaponPrimaryAttackInfo attackInfo, FWeaponPrimaryAttackReturnParams& returnInfo)
	{
		Super::CodeCallback_OnWeaponTossRelease(weapon, attackInfo, returnInfo);

		if ( !IsServer() )
			return;
			
		if ( !IsValid( cachedWeapon ) )
			return;
		if ( !IsValid( cachedOwner ) )
			return;

		cachedOwner.PlayBattleChatter( GameplayTags::Audio_VO_BattleChatter_UsingAbility_Tactical );

		if ( cachedWeapon.HasMod( n"EarlyCancel" ) )
            cachedWeapon.RemoveMod( n"EarlyCancel" );
	}

	UFUNCTION()
	void OnFirePressed()
	{
		if ( !IsValid( cachedOwner ) )
			return;
		if ( !IsValid( cachedWeapon ) )
			return;

		if ( IsServer() && CanHardenPlayer( cachedOwner, nullptr ) )
		{
			bool didHarden = HardenPlayer( cachedOwner, true );
			if ( didHarden )
			{
				cachedWeapon.SetClipAmmo( cachedWeapon.GetClipAmmo() - 1 );
				StartCooldown( cachedWeapon );
				PlayBattleChatter( cachedWeapon );
			}
		}

		cachedWeapon.AddMod( n"Consume" );
		cachedOwner.EquipLastPrimaryWeapon();
	}
}



















UCLASS()
class UAS_ProjectileContext_Harden : UNCProjectileScriptContext
{
	UFUNCTION( BlueprintOverride )
	void CodeCallback_OnProjectileImpact( ANCProjectile projectile, FHitResult impactResult )
	{
		if ( !IsServer() )
			return;

		ANCWeapon ownerWeapon = Cast<ANCWeapon>( projectile.GetOwner() );
		if ( !IsValid( ownerWeapon ) )
			return;

		if ( !IsValid( ownerWeapon.GetWeaponOwner() ) )
			return;

		Server_SpawnEntity( Abilities().hardenedGlobals.aoeClass, ownerWeapon.GetWeaponOwner(), projectile.GetActorLocation() );
		projectile.Destroy();
	}
}

















UCLASS( Abstract )
class AAS_HardenAOE : ANCDefaultActor
{
	UPROPERTY( DefaultComponent, RootComponent )
	USceneComponent root;

	UPROPERTY( DefaultComponent )
	UAS_SphereTrigger trigger;
	default trigger.SetRadius( HardenConst::HARDEN_AOE_RADIUS );

	UPROPERTY( DefaultComponent )
	UMoverComponent mover;

	UPROPERTY( DefaultComponent )
	UNiagaraComponent effect;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset startSound;
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset sustainSound;

	UPROPERTY( DefaultComponent )
	UNCNetMovementComponent ncNetMovementComponent;

	UPROPERTY( DefaultComponent )
	UAS_PingableComponent pingableComponent;
	default pingableComponent.pingType = EPlayerPingType::LOOT;

	ASoundActor sustainActor;

	UFUNCTION(BlueprintOverride)
	void ClientBeginPlay()
	{
		/*if ( !IsValid( GetOwnerPlayer() ) )
			return;

		if ( !IsFriendly( Client_GetLocalASPawn().GetTeam(), GetOwnerPlayer().GetTeam() ) )
			effect.SetIntParameter( n"TeamVis", 0 );*/
	}

	UFUNCTION( BlueprintOverride )
	void ServerBeginPlay()
	{
		if ( IsValid( startSound ) )
			Server_EmitSoundAtLocation( startSound, GetActorLocation() );
		if ( IsValid( sustainSound ) )
			sustainActor = Server_EmitSoundAtLocation_ReturnEntity( sustainSound, GetActorLocation() );

		// check for any players already in trigger radius
		TArray<AAS_PlayerEntity> playersInRange = GetAllPlayersInRange( GetActorLocation(), HardenConst::HARDEN_AOE_RADIUS );
		TArray<AAS_PlayerEntity> playersInRangeSorted;
		for ( auto p : playersInRange )
		{
			int insertIndex = -1;
			if ( playersInRangeSorted.Num() <= 0 )
				insertIndex = 0;
			else
			{
				float distanceToP = DistanceSqrd( GetActorLocation(), p.GetActorLocation() );
				for ( int i = 0; i < playersInRangeSorted.Num(); i++ )
				{
					float distanceToI = DistanceSqrd( GetActorLocation(), playersInRangeSorted[i].GetActorLocation() );
					if ( distanceToP < distanceToI )
					{
						insertIndex = i;
						break;
					}
				}
			}

			if ( insertIndex != -1 )
				playersInRangeSorted.Insert( p, insertIndex );
			else
				playersInRangeSorted.Add( p );
		}

		//go through sorted players checking for affinity
		for ( auto p : playersInRangeSorted )
		{
			if ( !CanHardenPlayer( p, this, true ) )
				continue;

			TriggerHardenPlayer( p );
			return;
		}

		//didn't find friendly to attach to? now let's check for everyone else
		//go through sorted players checking for affinity
		for ( auto p : playersInRangeSorted )
		{
			if ( !CanHardenPlayer( p, this ) )
				continue;

			TriggerHardenPlayer( p );
			return;
		}

		trigger.onPlayerEntered.AddUFunction( this, n"OnPlayerEntered" );
		System::SetTimer( this, n"ShutdownSphere", HardenConst::HARDEN_AOE_LIFETIME, false );
	}

	AAS_PlayerEntity playerToHarden;

	UFUNCTION()
	void OnPlayerEntered( AAS_PlayerEntity asPawn, UAS_SphereTrigger t )
	{
		if ( !CanHardenPlayer( asPawn, this ) )
			return;
		TriggerHardenPlayer( asPawn );
	}

	UFUNCTION()
	void TriggerHardenPlayer( AAS_PlayerEntity asPawn )
	{
		trigger.Disable();
		playerToHarden = asPawn;
		mover.OnMoveComplete.AddUFunction( this, n"HardenMoveComplete" );
		mover.MoveTo( asPawn.GetActorLocation(), 0.5, 0.1, 0.1 );
	}

	UFUNCTION()
	void HardenMoveComplete( UNCLerpComponent c )
	{
		if ( HardenPlayer( playerToHarden ) )
		{
			if ( playerToHarden != GetOwnerPlayer() && playerToHarden.GetTeam() == GetOwnerPlayer().GetTeam() )
				PlayerStatsManager().IncrementCharacterStatForPlayer( GetOwnerPlayer(), GameplayTags::Classes_Class_Mara, GameplayTags::Progression_PlayerStats_Mara_AlliesHardened );
			ShutdownSphere();
		}
		else
		{
			mover.OnMoveComplete.Clear();
			playerToHarden = nullptr;
			trigger.Enable();
		}
	}

	UFUNCTION()
	void ShutdownSphere()
	{
		if ( IsValid( sustainActor ) )
			sustainActor.Destroy();
		Destroy();
	}
}






















UFUNCTION()
bool HardenPlayer( AAS_PlayerEntity player, bool didConsume = false )
{
	if ( IsValid( player ) && player.IsAlive() && player.GetStatusEffectValue( GameplayTags::StatusEffect_Hardened ) <= 0.0 )
	{
		AAS_HardenedTracker tracker = Cast<AAS_HardenedTracker>( Server_SpawnEntity( Abilities().hardenedGlobals.trackerClass, player ) );
		tracker.HardenPlayer( didConsume );
		return true;
	}
	return false;
}

UFUNCTION()
bool CanHardenPlayer( AAS_PlayerEntity asPawn, AAS_HardenAOE aoe, bool shouldCheckAffinity = false )
{
	if ( !IsValid( asPawn ) )
		return false;

	int team = asPawn.GetTeam();
	if ( IsValid( aoe ) )
	{
		ANCPlayerCharacter ownerPlayer = aoe.GetOwnerPlayer();
		team						   = ownerPlayer.GetTeam();
	}

	if ( shouldCheckAffinity && !IsFriendly( asPawn.GetTeam(), team ) )
		return false;
	if ( asPawn.GetStatusEffectValue( GameplayTags::StatusEffect_Hardened ) > 0.0 )
		return false;

	return true;
}

AAS_HardenedTracker GetTrackerFromPlayer( ANCPlayerCharacter player )
{
	UAS_SharedScriptList list = SharedScriptList();
	if ( IsValid( list ) && list.playerHardenedTrackers.Contains( player ) )
		return list.playerHardenedTrackers[player];
	return nullptr;
}




























event void FOnHardenedApplied( AAS_PlayerEntity p );
event void FOnHardenedBroken( AAS_PlayerEntity p );
event void FOnHardenedEnded( AAS_PlayerEntity p );
event void FOnHardenedHealthChanged( ANCPlayerCharacter p, float32 health );

UCLASS( Abstract )
class AAS_HardenedTracker : ANCDefaultActor
{
	UPROPERTY( DefaultComponent, RootComponent )
	USceneComponent root;
	UPROPERTY( DefaultComponent )
	UNiagaraComponent effect;

	AAS_PlayerEntity asOwner;

	FOnHardenedApplied server_onHardenedApplied;
	FOnHardenedBroken server_onHardenedBroken;
	FOnHardenedEnded server_onHardenedEnded;

	FOnHardenedHealthChanged shared_onHardenedHealthChanged;

	UPROPERTY()
	FNCNetFloat health;
	int hardenedEffectID;

	UPROPERTY( EditDefaultsOnly )
	UNiagaraSystem hitEffect;
	UPROPERTY( EditDefaultsOnly )
	UNiagaraSystem statusEffect;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset healStart1P;
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset healStart1P_Consume;
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset healStart3P;
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset healEnd1P;
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset healEnd3P;

	TArray<ASoundActor> sustainSounds;
	AFXActor idleFXActor;

	UFUNCTION( BlueprintOverride )
	void ServerBeginPlay()
	{
		SharedBeginPlay();

		asOwner.AddStatusEffectHasBegunCallback( GameplayTags::StatusEffect_Hardened, this, n"Server_OnPlayerHardenedBegin" );
		asOwner.AddStatusEffectHasEndedCallback( GameplayTags::StatusEffect_Hardened, this, n"Server_OnPlayerHardenedEnd" );
	}

	UFUNCTION( BlueprintOverride )
	void ClientBeginPlay()
	{
		SharedBeginPlay();

		if ( asOwner.IsLocallyControlled() )
			CreateScreenEffect();

		asOwner.shieldControlManagerComponent.RegisterShieldControlEffect( GameplayTags::StatusEffect_Hardened, 2 );
		health.OnReplicated().AddUFunction( this, n"OnHardenedHealthChanged" );
	}

	UFUNCTION()
	void SharedBeginPlay()
	{
		asOwner = Cast<AAS_PlayerEntity>( GetOwnerPlayer() );

		UAS_SharedScriptList list = SharedScriptList();
		list.playerHardenedTrackers.Add( asOwner, this );
	}

	UFUNCTION( BlueprintOverride )
	void EndPlay( EEndPlayReason EndPlayReason )
	{
		if ( IsClient() )
		{
			if ( IsValid( asOwner ) )
			{
				asOwner.shieldControlManagerComponent.DeregisterShieldControlEffect( GameplayTags::StatusEffect_Hardened );
			}
		}

		UAS_SharedScriptList list = SharedScriptList();
		if ( IsValid( list ) && list.playerHardenedTrackers.Contains( asOwner ) )
			list.playerHardenedTrackers.Remove( asOwner );

		if ( IsValid( idleFXActor ) )
			idleFXActor.Destroy();
	}

	UFUNCTION( BlueprintEvent )
	void HardenPlayer( bool didConsume = false )
	{
		hardenedEffectID = asOwner.AddStatusEffect( GameplayTags::StatusEffect_Hardened, didConsume ? 10.0 : 1.0, HardenConst::HARDEN_EFFECT_LIFETIME, 0.0, 0 );
	
		UNCAudioAsset localStartSound = didConsume ? healStart1P_Consume : healStart1P;
		if ( IsValid( localStartSound ) && IsValid( healStart3P ) )
			sustainSounds = Server_EmitSoundOnEntity_1P3P_ReturnEntities( localStartSound, healStart3P, asOwner, asOwner );
	}

	UFUNCTION()
	void Server_OnPlayerHardenedBegin( ANCPlayerCharacter p )
	{
		health.SetNetValue( HardenConst::HARDEN_MAX_HEALTH );
		FPreReceievedDamageDelegateBP preDamage;
		preDamage.BindUFunction( this, n"OnHardenedPreDamage" );

		if ( !asOwner.asHealthComponent.IsScriptPreDamageEventRegistered( n"Harden" ) )
			asOwner.asHealthComponent.AddScriptPreDamageEvent( n"Harden", preDamage );

		if ( IsValid( idleFXActor ) )
			idleFXActor.Destroy();
		idleFXActor = Server_SpawnEffectOnEntity_Looping_WithFlags( statusEffect, asOwner, asOwner, ESendEventFlags::SEND_TO_FRIENDLIES | ESendEventFlags::SEND_TO_ENEMIES );
		idleFXActor.AttachToComponent( asOwner.GetPlayerMesh3P(), n"spine_chest" );

		server_onHardenedApplied.Broadcast( asOwner );
	}

	UFUNCTION()
	void Server_OnPlayerHardenedEnd( ANCPlayerCharacter p )
	{
		health.SetNetValue( 0 );

		if ( asOwner.asHealthComponent.IsScriptPreDamageEventRegistered( n"Harden" ) )
			asOwner.asHealthComponent.RemoveScriptPreDamageEvent( n"Harden" );

		for ( auto sound : sustainSounds )
		{
			if ( IsValid( sound ) )
				sound.Destroy();
		}

		if ( IsValid( healEnd1P ) && IsValid( healEnd3P ) )
			Server_EmitSoundOnEntity_1P3P( healEnd1P, healEnd3P, asOwner, asOwner );

		if ( IsValid( idleFXActor ) )
			idleFXActor.Destroy();

		server_onHardenedEnded.Broadcast( asOwner );
		Destroy();
	}

	UFUNCTION()
	void OnHardenedPreDamage( FDamageInfo& dmgInfo )
	{
		if ( Bitflags::HasFlag( dmgInfo.scriptDamageFlags, EScriptDamageFlags::DF_FALL_DAMAGE ) )
			return;
		if ( Bitflags::HasFlag( dmgInfo.damageFlags, EDamageFlags::DF_FELL_OUT_OF_WORLD ) )
			dmgInfo.damage = dmgInfo.damage + health; //do extra damage to kill player if they fall off world

		AAS_PlayerEntity attacker = Cast<AAS_PlayerEntity>( dmgInfo.attacker );
		if ( IsValid( attacker ) && IsFriendly( attacker.GetTeam(), asOwner.GetTeam() ) )
			return;

		float totalDamage			  = dmgInfo.damage;
		float remainingDamage		  = totalDamage - Math::Min( totalDamage, health );
		float damageTakenByOvershield = totalDamage - remainingDamage;

		dmgInfo.damage			  = remainingDamage + 0.01; // this is so hacky but we can figure out how to do this feedback properly later
		dmgInfo.scriptDamageFlags = Bitflags::AddFlag( dmgInfo.scriptDamageFlags, EScriptDamageFlags::DF_HARDENED );

		health.SetNetValue( Math::Max( health - damageTakenByOvershield, 0 ) );

		shared_onHardenedHealthChanged.Broadcast( asOwner, health );

		if ( health <= 0 )
		{
			dmgInfo.scriptDamageFlags = Bitflags::AddFlag( dmgInfo.scriptDamageFlags, EScriptDamageFlags::DF_HARDENED_BREAK );
			asOwner.ClearStatusEffect( hardenedEffectID );
			server_onHardenedBroken.Broadcast( asOwner );
		}
	}

	UFUNCTION()
	void OnHardenedHealthChanged( float32 old, float32 new )
	{
		ScriptCallbacks().client_onHardenedHealthChanged.Broadcast( asOwner, old, new );
		shared_onHardenedHealthChanged.Broadcast( asOwner, new );
	}

	UFUNCTION()
	void CreateScreenEffect()
	{
		UAS_ScreenEffect_MaraShield statusIndicatorScreenEffect = GetScreenEffect_MaraShield();
		if ( !IsValid( statusIndicatorScreenEffect ) )
			return;

		FScreenStatusIndicatorData newStatusIndicatorData;
		newStatusIndicatorData.fullscreenAmount		   = 1.0;
		newStatusIndicatorData.fullscreenDepth		   = 0.2;
		newStatusIndicatorData.fullscreenGlowIntensity = 1.0;

		newStatusIndicatorData.baseColor			 = FLinearColor(0.14, 0.08, 0.19);
		newStatusIndicatorData.glowColor			 = FLinearColor(0.53, 0.35, 0.68);
		//newStatusIndicatorData.glow_contrast		 = 1
		newStatusIndicatorData.modulateTextureAmount = 0.5f;

		newStatusIndicatorData.modulateScaleX = 8.0f;
		newStatusIndicatorData.modulateScaleY = 0.2f;
		newStatusIndicatorData.modulateSpeedX = 0.0;
		newStatusIndicatorData.modulateSpeedY = -0.2;
		

		int screenEffectHandle = statusIndicatorScreenEffect.RegisterStatusIndicator( newStatusIndicatorData );
		FScreenStatusIndicatorData& registeredData = statusIndicatorScreenEffect.GetStatusIndicatorData( screenEffectHandle );
		float curTime							   = GetGameTimeMS();
		float endTime							   = curTime + TO_MILLISECONDS( 7.0f );
		registeredData.LerpDepth( 0.0, curTime, endTime, true );
	}
}

UCLASS()
class UAS_ScreenEffect_MaraShield : UAS_ScreenEffect_StatusIndicator
{

}

UAS_ScreenEffect_MaraShield GetScreenEffect_MaraShield()
{
    UAS_ScreenEffect uncasted = ScreenEffectManager().GetScreenEffect( UAS_ScreenEffect_MaraShield::StaticClass() );
    return Cast<UAS_ScreenEffect_MaraShield>( uncasted );
}