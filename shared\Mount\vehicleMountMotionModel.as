enum EMountMotionModelLocoIndex
{
	BACK_PEDDLE,
	IDLE,
	WALK_SPRINT, 

	_count
}

enum EMountMotionModelStrafeIndex
{
	STRAFE_LEFT,
	STRAFE_RIGHT,
}

USTRUCT()
struct FVehicleMountMotionModelCalc
{
////////////////////////////////////////////////
//	PHYSICS
	UPROPERTY()
	FVector finalVel;
	
	UPROPERTY()
	float finalYaw;
	
	UPROPERTY()
	float finalRoll;

	UPROPERTY()
	float extraRollHack;
		
	UPROPERTY()
	float finalSpeed;

////////////////////////////////////////////////
//	VIEW MODEL		
	UPROPERTY()
	float animBP_DeltaGoalYaw;

	UPROPERTY()
	float animBP_DeltaViewYaw;	

	UPROPERTY()
	float viewmodelSpeed;

	UPROPERTY()
	bool isBraking;

	UPROPERTY()
	bool isFalling;

	UPROPERTY()
	bool isFreeLook;

	UPROPERTY()
	bool isFreeLookReturn;		
}


struct FTrackAveVelHack
{
	int trackMS;
	FVector trackPos;

	FTrackAveVelHack( AActor actor, int initTime = -1 )
	{
		trackMS 	= initTime == -1 ? GetGameTimeMS() : initTime;
		trackPos	= actor.GetActorLocation();
	}

	float GetAverageVelZ( FTrackAveVelHack otherTrackZ )
	{
		float elapsedTime 	= TO_SECONDS( this.trackMS - otherTrackZ.trackMS );
		float zTravel 		= this.trackPos.Z - otherTrackZ.trackPos.Z;
		
		return elapsedTime > 0 ? zTravel / elapsedTime : zTravel;
	}

	FVector GetAverageVel( FTrackAveVelHack otherTrackZ )
	{
		float elapsedTime 	= TO_SECONDS( this.trackMS - otherTrackZ.trackMS );
		FVector travelVec 	= this.trackPos - otherTrackZ.trackPos;
		
		return elapsedTime > 0 ? travelVec / elapsedTime : travelVec;
	}
}

void Init_MountMotionModelNetworking( UNCBaseServerScript serverScript )
{
	UNCRemoteScriptCommands::RegisterClientCheatGlobal( serverScript, n"ClientMountMotionModelSetTankFreelookTime", n"CC_ClientMountMotionModelSetTankFreelookTime" );
}

UFUNCTION()
void CC_ClientMountMotionModelSetTankFreelookTime( ANCPlayerCharacter PS, TArray<FString> args )
{
	int entityIndex 		= args[0].ToInt();	
	AAS_VehicleMount mount 	= Cast<AAS_VehicleMount>( GetEntity( entityIndex ) );
	if ( !IsValid( mount ) )
		return;

	if ( mount.GetPilot() != PS )
		return;

	int timeMS = args[1].ToInt();
	mount.motionModel.sv_SetTankFreelookTime( timeMS );
}

event void FEvent_OnPhysicsEvent( const ANCPlayerCharacter pilot );
event void FEvent_OnActiveSprintInput( const ANCPlayerCharacter pilot, bool onEmbark );

/****************************************************************\

 ██████ ██       █████  ███████ ███████ 
██      ██      ██   ██ ██      ██      
██      ██      ███████ ███████ ███████ 
██      ██      ██   ██      ██      ██ 
 ██████ ███████ ██   ██ ███████ ███████
                                  
\****************************************************************/	
UCLASS()
class UAS_MountMotionModel : UActorComponent
{
	access internal = private, AAS_VehicleMount;

	UPROPERTY()
	private FNCNetBool net_isSprinting(false);

	UPROPERTY()
	private FNCNetInt net_ActiveSprintStartTimeMS(0,0,MAX_int32);

	UPROPERTY()
	private FNCNetBool net_resumeActiveSprintOnADSRelease(false);
		
	private bool local_LandedAfterEmbark 	= false;
	private int local_landedMS 				= -1;
	private bool local_isBunnyHopping		= false;
	private bool local_EjectPressed			= false;	
	private bool local_ExitPressed			= false;
	private bool local_SlidePressed			= false;
		bool GetWasSlidePressed(){ return local_SlidePressed; }
	private int local_lastAttackTimeMS = -10000;
	
	private bool local_isADSInputPressed 	= false;
	access:internal bool local_holdThrow 	= false;
	private int local_freelookStopTimeMS;
	
	private FScriptDelegateHandle sprintInputHandle;
	private FScriptDelegateHandle freelookInputHandle;
	private FScriptDelegateHandle freelookReleaseHandle;
	private FScriptDelegateHandle ejectInputHandle;
	private FScriptDelegateHandle crouchInputHandle;
	private FScriptDelegateHandle crouchReleaseHandle;	

	UPROPERTY()
	private FNCNetFloat net_lastActivePilotViewYaw( 0.0f, -360.0f, 360.0f );

	UPROPERTY()
	private FNCNetFloat net_lastActiveTankYaw( 0.0f, -360.0f, 360.0f );

	UPROPERTY()
	private FNCNetFloat net_animRoll( 0.0f, -90.0f, 90.0f );

	private float32 local_animRoll = 0;

	private FVector local_embarkVelocity = FVector::ZeroVector;
	private float 	local_embarkSpeed;
	private int 	local_embarkTimeMS;
	private float 	local_idleGoalYaw;	
	private int 	sv_inputNullMS = -1;
	private float 	local_animBP_DeltaGoalYaw;
	private float 	local_animBP_DeltaViewYaw;
	private float 	local_prevInputStrafeMag;
	private bool 	local_wasBraking;
	private int 	local_TankFreelookTimeMS;

	FMountVehicleLocalData localData;
	
	/* 	this is a hack to track the REAL Z vel because curVelRaw returns a zero Z when not falling
		tracking height and averaging over time helps smooth out small fluctiations in geo changes */
	const int MAX_TRACKED_VEL_COUNT		= 15;	//roughly track 0.5s worth of data
	TArray<FTrackAveVelHack> local_TrackedVel;	

	FEvent_OnActiveSprintInput OnActiveSprintInput;
	FEvent_OnPhysicsEvent OnStartBraking;
	FEvent_OnPhysicsEvent OnEndBraking;

	// Do not access directly - use GetOwnerMount() instead
	UPROPERTY()
	AAS_VehicleMount CachedOwnerMount;
	default CachedOwnerMount = nullptr;

	AAS_VehicleMount GetOwnerMount()
	{
		if ( CachedOwnerMount == nullptr )
		{
			CachedOwnerMount = Cast<AAS_VehicleMount>(GetOwner());
		}

		return CachedOwnerMount;
	}

	// Do not access directly - use GetMountData() instead	
	FVehicleMountData CachedMountData;	
	FVehicleMountData GetMountData()
	{
		if (!CachedMountData.isInitialized)
			CachedMountData.Init( GetOwnerMount().GetMountData() );

		return CachedMountData;
	}

	UFUNCTION(BlueprintOverride)
	void BeginPlay()
	{
		AAS_VehicleMount ownerMount = GetOwnerMount();
		ScriptAssert( IsValid( ownerMount ), "WTF how is the owner of a component not valid?" );

		if ( IsServer() )
		{
			if (ANCVehicle::UsesCodeMovement())
			{
				ownerMount.SetVehicleMoveType( EVehicleMoveType::VEHICLE_MOVE_TYPE_MOUNT );
				ownerMount.SetVehicleMovementMode( ENCVehicleMovementMode::Falling );
			}
			else
			{
				ownerMount.SetVehicleMoveType( EVehicleMoveType::VEHICLE_MOVE_TYPE_SCRIPT );
			}
		}

		if ( IsClient() )
		{
			if ( Client_GetLocalPawn() == ownerMount.GetOwnerPlayer() )
			{
				net_animRoll.OnReplicated().AddUFunction( this, n"cl_OnAnimRollReplicated");
				sh_OnPilotChanged( nullptr, GetPilot() );
			}
		}

		ownerMount.OnPilotChanged.AddUFunction( this, n"sh_OnPilotChanged" );
		local_idleGoalYaw = ownerMount.GetActorRotation().Yaw;
		
		TArray<AAS_PlayerEntity> nearbyPlayers = GetAllPlayersInRange( ownerMount.GetActorLocation(), 200 );
		for( AAS_PlayerEntity player : nearbyPlayers )
			Thread( ownerMount, n"IgnorePlayerUntilNotOverlapping", player );
	}

	UFUNCTION()
	private void sh_OnPilotChanged( ANCNetCharacter oldNetPilot, ANCNetCharacter newNetPilot )
	{		
		ANCPlayerCharacter oldPilot = Cast<ANCPlayerCharacter>( oldNetPilot );
		ANCPlayerCharacter newPilot = Cast<ANCPlayerCharacter>( newNetPilot );

		if ( IsValid(oldPilot ) )
		{
			ImpartVelocityOnDismountingPlayer( oldPilot );
			ClearInputCallbacks(oldPilot);
		}
			
		if ( IsValid(newPilot) )
		{			
			local_embarkVelocity 	= newPilot.GetMovementComponentVelocity();
			local_embarkSpeed 		= local_embarkVelocity.Size2D();
			local_embarkTimeMS 		= GetGameTimeMS();
			
			InitRealVelocity();

			SetupInput( newPilot );

			//this is a work around for bug NC1-7757, where ownerMount.GetActorRotation().Yaw is 0 on client beginplay
			local_idleGoalYaw = newPilot.GetViewRotation().Yaw;

			FVehicleMountData data 		= GetMountData();
				
			bool isSprinting 	= newPilot.IsSprinting();
			float sprintBuffer 	= data.Core.Physics.WalkSpeedMax + 100;
			bool atSpeed 		= newPilot.GetMovementComponentVelocity().Size2D() > sprintBuffer;
	
			if ( IsServer() )
				SetSprinting( isSprinting || atSpeed );

			//automatically set the embark active sprint
			if ( isSprinting || atSpeed )
			{
				bool onEmbark = true;
				__OnActiveSprintInput(newPilot, onEmbark );
			}
		}

		if ( IsServer() )
		{
			if ( IsValid(newPilot) )
			{
				float yaw = newPilot.GetViewRotation().Yaw;
				net_lastActivePilotViewYaw.SetNetValue( yaw );
				net_lastActiveTankYaw.SetNetValue( yaw );
			}
		}

		if ( !ANCVehicle::UsesCodeMovement() && IsClient() )
		{			
			FVehicleMountData data 		= GetMountData();

			//unlocking view is handled internally in the lockView function because sh_OnPilotChanged doesn't consistently hit on the client			
			if ( IsValid(newPilot) && newPilot == Client_GetLocalPawn() )
				newPilot.LockViewToVehicle( data.Core.Physics.LockViewMaxYawDelta * -1, data.Core.Physics.LockViewMaxYawDelta, data.Core.Physics.LockViewMinPitchDelta );
		}
	}

/****************************************************************\

██████  ██   ██ ██    ██ ███████ ██  ██████ ███████ 
██   ██ ██   ██  ██  ██  ██      ██ ██      ██      
██████  ███████   ████   ███████ ██ ██      ███████ 
██      ██   ██    ██         ██ ██ ██           ██ 
██      ██   ██    ██    ███████ ██  ██████ ███████
                                  
\****************************************************************/		
	//animBP can calculate this at higher framerate than script
	FVehicleMountMotionModelCalc CalculateMountMotionModel(const ANCPlayerCharacter pilot, FVector curVelRaw, FVector2D moveInputRawIn, float deltaTime, int gameTimeMS, bool READ_ONLY = true )
	{	
		FVector2D moveInputRaw = IsValid(pilot) ? moveInputRawIn : FVector2D::ZeroVector;

		AAS_VehicleMount ownerMount 	= GetOwnerMount();
		FVehicleMountData mountData 	= GetMountData();

#if EDITOR		
		if ( GetCvarBool(f"VehicleMount.DebugKillMount") && ownerMount.GetVehicleState() != EVehicleActiveState::Vehicle_Dead )
		{
			moveInputRaw = FVector2D(1,0);
			if ( IsServer() )
				SetSprinting( true );
		}
#endif

		FVehicleMountMotionModelCalc calc;
		if ( !IsValid(ownerMount) )
			return calc;
		
		///////////////////////////////////////////////////////////////////////////
		//	PRELIM DATA	
		
		/*	this is a hack because I can't confidently say when embarkVelocity has been applied because
			this function gets called multiple times from the server and client through the code callback 
			alone, but also from the animBP and other places. So I use the embarkTime or when the cur vel
			is greater to stop applying the embark vel 	*/
		float embarkElapsedTime = TO_SECONDS( gameTimeMS - local_embarkTimeMS );
		local_embarkVelocity 	= curVelRaw.Size2D() > local_embarkVelocity.Size2D() || embarkElapsedTime > 0.2 ? FVector::ZeroVector : local_embarkVelocity;

		//init some data
		const bool isServer 						= IsServer();
		const bool isClient							= IsClient();
		const bool IN_PREDICTION 					= (isServer || (isClient && IsValid(pilot) && UNCUtils::InPrediction(pilot)));
		const bool isFreeLook						= IsFreeLook();
		const bool isFreeLookReturn					= IsFreeLookReturn();
		FVector 	initialVel 						= local_embarkVelocity.Size2D() > curVelRaw.Size2D() ? local_embarkVelocity : curVelRaw;
		FVector 	curVel2D 						= FVector( initialVel.X, initialVel.Y, 0 );
		float32		cur2DSpeed 						= float32(curVel2D.Size());
		float 		curYaw 							= ownerMount.GetActorRotation().Yaw;
		
		ownerMount.UpdateMountLocalData(mountData, cur2DSpeed);
		localData = ownerMount.localData;

		//these are not consts... they're treated like consts, but they adjust based on Active Sprint
		float32 	dataPhysicsSprintSpeedMax		= localData.SprintSpeedMax;
		float32 	dataPhysicsSprintSpeedAcc		= localData.SprintAccel;
		float32 	dataPhysicsBoostSpeedMax		= localData.BoostSpeedMax;
		float32 	dataPhysicsBoostSpeedAcc		= localData.BoostAccel;

		bool 		atMinSpeedToUpdateTurn			= cur2DSpeed > mountData.Core.Physics.MinSpeedToUpateTurn;
		FVector2D 	speedRangeWalk2Sprint 			= FVector2D(mountData.Core.Physics.WalkSpeedMax,dataPhysicsSprintSpeedMax);
		FVector2D 	speedRangeWalk2SprintBuffered 	= FVector2D(mountData.Core.Physics.WalkSpeedMax + 50,dataPhysicsSprintSpeedMax - 50);
		float 		inputMag						= Math::Min( moveInputRaw.Size(), 1.0 );
		

		///////////////////////////////////////////////////////////////////////////
		//	READ / WRITE PRELIM DATA
			/*	Why not just keep this a read only function that doesn't set data?
				These specific vars need to be set so that the calculations moving
				forward have the most up to date state of things. */
			
		bool falling = IsFalling();		
		if( IsServer() && !ownerMount.VehicleIsDormant && !IsValid(pilot) && !falling && cur2DSpeed == 0.f )
			ownerMount.VehicleIsDormant.SetNetValue(true);

		//this is a hack to track the REAL Z vel because curVelRaw returns a zero Z when not falling			
		if ( !READ_ONLY && IN_PREDICTION )
			TrackRealVelocity( ownerMount, gameTimeMS, falling );

		if ( falling )
			local_landedMS = -1;
		else
			local_LandedAfterEmbark = true;
		
		FVector jumpVelocity 	= FVector::ZeroVector;
		if ( IsValid(pilot) && PressedJump( pilot, gameTimeMS, falling ) && !falling )
		{
			float averageZVel 	= GetAverageTrackedZVel();
			const FVector pilotJumpVelocity = GetJumpVelocity( pilot, mountData );
			jumpVelocity 		= averageZVel < 0 ? pilotJumpVelocity : pilotJumpVelocity + FVector(0,0,averageZVel * mountData.Core.Physics.JumpUphillVelAddFrac);
			if ( !READ_ONLY )
			{
				ownerMount.SetVehicleJumpedThisFrame(true);
				ownerMount.SetVehicleMovementMode( ENCVehicleMovementMode::Falling );
			}
			falling = IsFalling();	//don't trust just setting this to true, want to get the code value during reconciliation.

			#if EDITOR	
				if ( !READ_ONLY && IsServer() && GetCvarBool( f"VehicleMount.DebugJump" ) )
					Thread( this, n"DebugJumpThread", pilot, jumpVelocity );
			#endif
		}
		
		if ( !READ_ONLY && IN_PREDICTION && local_landedMS == -1 && !falling )
			local_landedMS = gameTimeMS;

		if ( !READ_ONLY && IN_PREDICTION && local_landedMS > 0 )
		{
			float elapsedTimeJump 	= TO_SECONDS( gameTimeMS - local_landedMS );
			local_isBunnyHopping 	= elapsedTimeJump < mountData.Core.Physics.JumpBunnyHopProtectionTime;
		}

		//default input vars for no pilot / no input
		bool isInputReverse 	= false;		
		FVector inputRaw2D 		= FVector::ZeroVector;
		FVector inputDir2D 		= FVector::ZeroVector;
		
		//These values should not be used outside of if( IsValid(pilot) )
		//would init to invalid, but that's not an option.
		float inputReverseDot 	= 0;
		float pilotViewYaw 		= 0;
		float pilotAimYaw 		= 0;
		FVector pilotAim2D;
		FVector inputRawTank;
				
		if ( IsValid(pilot) )
		{		
			pilotViewYaw 	= pilot.GetViewRotation().Yaw;
			if ( !READ_ONLY && isServer && !isFreeLook && !isFreeLookReturn )
				net_lastActivePilotViewYaw.SetNetValue( pilotViewYaw );	//set this before using GetPilotAimYaw

			//get input
			pilotAimYaw		 			= GetPilotAimYaw(pilot, isFreeLook, isFreeLookReturn);
			pilotAim2D 					= FRotator( 0, pilotAimYaw, 0 ).GetForwardVector();		
			inputRaw2D					= GetMoveInputRaw2D(moveInputRaw, mountData);
			
			//decipher if input is braking / reversing
			inputReverseDot				= FVector::ForwardVector.DotProduct(inputRaw2D);
			float inputReverseDotMove	= Math::GetMappedRangeValueClamped( speedRangeWalk2SprintBuffered, FVector2D(mountData.Core.Physics.inputReverseDotWalk, mountData.Core.Physics.inputReverseDotSprint), cur2DSpeed );
			isInputReverse				= inputReverseDot < inputReverseDotMove;	
		
			///////////////////////////////////////////////////////////////////////////
			// 	ADJUST INPUT

			/*	strafe input has a stronger impact on turning than look input, that's because strafe input can
				change instantly, where as you can only turn your view or move your mouse so fast. This is to 
				modify that strafe input so that CHANGES in strafe input are lerped instead of instantly used */
			{
				//these values are hard coded because I don't feel this is really a tunable setting. it just looks bad any other way.
				float deltaStrafeInput 	= DotToAngles(inputRaw2D.Y) - DotToAngles(local_prevInputStrafeMag);
				float interpSpeed 		= Math::GetMappedRangeValueClamped( FVector2D(90,120), FVector2D(5,10), Math::Abs(deltaStrafeInput) );
				float newInputStrafeMag = Math::FInterpTo( local_prevInputStrafeMag, inputRaw2D.Y, deltaTime, interpSpeed );
				if ( !READ_ONLY && IN_PREDICTION )
					local_prevInputStrafeMag = newInputStrafeMag;

				/*	directly affecting inputRaw2D.Y can cause strange results, especially when switching strafe
					vectors. Ex: (0,1) -> (0,-1). What we want to do is get the angle of inputRaw2D.Y, and adjust 
					it by our lerp, then grab the new forward vector of that adjustment. */
				float adjustYaw 		= DotToAngles(inputRaw2D.Y) - DotToAngles(newInputStrafeMag);
				adjustYaw 				= isInputReverse ? adjustYaw * -1 : adjustYaw;
				float inputRaw2DYaw 	= inputRaw2D.Rotation().Yaw;
				float inputAdjust2DYaw 	= inputRaw2DYaw + adjustYaw;
				inputRaw2D 				= FRotator( 0, inputAdjust2DYaw, 0 ).GetForwardVector() * inputMag;
			}

			inputDir2D 		= GetMoveInputWorldDir2D( pilotAimYaw, inputRaw2D ); 
		}		

		//still sprinting?
		if ( !READ_ONLY && isServer )
		{
			/*	kill sprint on no input... but when strafing L->R or vise versa, input becomes 0 for a moment. 
			So make sure there's been no input for 5 frames (0.08s). No one will notice a 5 frame delay in 
			ending sprint. On the other hand, unintentionally ending sprint when changing strafe dir feels bad.	*/
			if (  inputMag < mountData.Core.Physics.MinInput && sv_inputNullMS < 0 )
				sv_inputNullMS = gameTimeMS;
			else if ( inputMag >= mountData.Core.Physics.MinInput && sv_inputNullMS > 0 )
				sv_inputNullMS = -1;

			const float NO_INPUT_END_SPRINT_THRESHOLD = 0.08;
			bool hasInput 			= inputMag >= mountData.Core.Physics.MinInput;
			bool inInputGraceWindow = TO_SECONDS( gameTimeMS - sv_inputNullMS ) < NO_INPUT_END_SPRINT_THRESHOLD;
			bool inEmbarkWindow 	= embarkElapsedTime <= 0.3;

			bool NoInputEndSprint 	= !hasInput && !inInputGraceWindow && !inEmbarkWindow;
			if ( isInputReverse || NoInputEndSprint )
				SetSprinting(false);	
		}
		
		if ( IsValid(pilot) )
		{
			/*	there's a buffer between hard strafe and reverse where we actually want to cap the input so we
				don't start turning "backward" while strafing... it's very disorientating */
			bool shouldCapNegativeInput = inputReverseDot < mountData.Core.Physics.inputReverseDotCap;
			inputRawTank 				= inputRaw2D;
			if ( shouldCapNegativeInput && !isInputReverse )
			{
				//use the inputReverseDotCap to create a normalized input vector, then mult by inputMag
				float capX 			= mountData.Core.Physics.inputReverseDotCap;
				float capY 			= (1-Math::Abs(mountData.Core.Physics.inputReverseDotCap)) * Math::SignNeverZero(moveInputRaw.Y);
				FVector inputCap2D	= FVector( capX, capY, 0 ) * inputMag;	
				inputRawTank 		= inputCap2D;

				//redo this calculation
				inputDir2D 			= GetMoveInputWorldDir2D( pilotAimYaw, inputCap2D );
			}
		}

		//seperate 2D and Z vel;	
		FVector curDir2D 		= curVel2D;
		curDir2D.Normalize();
		//if we have jump vel, zero out initialVel.Z... but most likely don't need to do this because initialvel.z is always zero when we're not falling.
		float curSpeedZ			= jumpVelocity == FVector::ZeroVector ? initialVel.Z : 0;
		FVector curVelZ 		= FVector(0,0,curSpeedZ);

		///////////////////////////////////////////////////////////////////////////
		// 	TANK CONTROLS
		// 	if we are using tank controls, then...
		if ( IsValid(pilot) && GetCvarBool(f"VehicleMount.EnableTankControls") )
		{
			if ( !READ_ONLY && IN_PREDICTION )
				RecordTankLookInput(pilot);
			
			//get the new "look dir" which is the tank yaw			
			float newtankYaw = GetNewTankYaw(pilot, mountData, inputRawTank, curVel2D, atMinSpeedToUpdateTurn, isInputReverse, deltaTime);
			if ( !READ_ONLY && isServer && IN_PREDICTION )
				net_lastActiveTankYaw.SetNetValue( newtankYaw );

			//... actually turn the player's view based on move input
			if ( !READ_ONLY )
			{			
				FRotator tankRot 		= pilot.GetViewRotation();	
				tankRot.Yaw 			= newtankYaw;
				tankRot.Pitch 			= GetNewTankPitch(pilot, mountData, atMinSpeedToUpdateTurn, isInputReverse, deltaTime, falling);
				FRotator viewRot 		= pilot.GetViewRotation();	

				float tankFreelookAlpha = GetTankControlsFreelookAlpha();
				FRotator newViewRot 	= Math::LerpShortestPath( tankRot, viewRot, tankFreelookAlpha );
				newViewRot.Roll 		= 0; //insure it's always zero, LerpShortestPath can do weird things
				
				ANCPlayerCharacter readWritePilot = GetPlayer(pilot.GetPlayerIndex());
				readWritePilot.SetViewRotation( newViewRot );		
			}

			//...simplify inputRaw2D to just forward or backward ( no strafing in tank controls )	
			inputRawTank 		= !isInputReverse ? FVector::ForwardVector * inputMag : FVector::ForwardVector * -inputMag;

			//... recalculate these values
			pilotAimYaw 		= newtankYaw;
			pilotViewYaw 		= pilot.GetViewRotation().Yaw;
			pilotAim2D 			= FRotator( 0, pilotAimYaw, 0 ).GetForwardVector();		
			inputDir2D 			= GetMoveInputWorldDir2D( pilotAimYaw, inputRawTank );
		}

		///////////////////////////////////////////////////////////////////////////
		// 	GOAL DIR
		//get goal and delta yaw	
		float deltaCur2ViewYaw;
		if ( IsValid(pilot) )
			deltaCur2ViewYaw = Math::FindDeltaAngleDegrees(curYaw, pilotViewYaw);
		else
			deltaCur2ViewYaw = 0;
			
		float curYawDotCurVel 	= curDir2D.DotProduct(ownerMount.GetActorForwardVector());
		bool keepCurGoalDir 	= curYawDotCurVel > 0 && atMinSpeedToUpdateTurn; //don't change goal to instantly reverse at speed when stick deflects the other way.
	
		//calculate local_idleGoalYaw before calling GetGoalYaw
		if ( !READ_ONLY && IN_PREDICTION )
			local_idleGoalYaw 	= atMinSpeedToUpdateTurn ? curYaw : Math::Abs(deltaCur2ViewYaw) > mountData.Core.Physics.idleTurnInPlaceTheta && !(isFreeLook || isFreeLookReturn) ? pilotViewYaw : local_idleGoalYaw;
		
		float goalYaw;
		if ( IsValid(pilot) )
		 	goalYaw	= GetGoalYaw( mountData, keepCurGoalDir, atMinSpeedToUpdateTurn, isInputReverse, inputDir2D, inputMag, pilotAimYaw, local_idleGoalYaw );
		else
			goalYaw = curYaw;


	#if EDITOR	
		if( GetCvarBool(f"VehicleMount.DebugLookAnims") )
			goalYaw = pilotAimYaw;
	#endif	
		float deltaYaw 			= Math::FindDeltaAngleDegrees(curYaw, goalYaw);

		//get some direction data
		FVector goalDir2D 		= FRotator(0, goalYaw, 0 ).GetForwardVector();
		float goalDotCur		= goalDir2D.DotProduct( curDir2D );	
		bool isMoveReverse 		= goalDotCur < 0 && isInputReverse && !keepCurGoalDir;
		bool isBraking			= isInputReverse && !isMoveReverse && atMinSpeedToUpdateTurn;

		if ( IsValid(pilot) )
		{
			//hard coded values that should not be tweeked... numbers below 90 cause jitter bugs when capping inputDir2D.
			const float32 CAP_MAX_DETLA_YAW_FORWARD		= 120;		
			const float32 CAP_MAX_DETLA_YAW_REVERSE 	= 90;

			//catch over turning and cap it ( this helps with weird sliding/drifting from combo of max turn + max strafe)
			float capMaxDeltaYaw 	 = isMoveReverse ? CAP_MAX_DETLA_YAW_REVERSE : CAP_MAX_DETLA_YAW_FORWARD;
			if ( Math::Abs(deltaYaw) > capMaxDeltaYaw )
			{
				//cap inputDir2D
				FVector curRight 	= ownerMount.GetActorRightVector();
				float dotAim		= curRight.DotProduct( pilotAim2D );
				float capYaw 		= (isMoveReverse ? capMaxDeltaYaw + 180 : capMaxDeltaYaw) * Math::SignNeverZero(dotAim);
				float fixInputYaw 	= curYaw + capYaw;
				inputDir2D 			= FRotator(0,fixInputYaw,0).GetForwardVector() * inputMag;

				//redo these calculations
				goalYaw 			= GetGoalYaw( mountData, keepCurGoalDir, atMinSpeedToUpdateTurn, isInputReverse, inputDir2D, inputMag, pilotAimYaw, local_idleGoalYaw );
				deltaYaw 			= Math::FindDeltaAngleDegrees(curYaw, goalYaw);
				goalDir2D 			= FRotator(0, goalYaw, 0 ).GetForwardVector();
				goalDotCur			= goalDir2D.DotProduct( curDir2D );		
			}
		}
		
	/*	before we get to the next step, record the delta yaw for animation purposes, because it's the 
		"truest" delta yaw. The next step caps delta yaw at different speeds, to insure we don't overturn		*/
		float deltaYaw_PreGoalMaxCap = deltaYaw;

		//this is to cap delta and goal yaw based on tweaked data
		float goalMaxDeltaYawFor 	= !atMinSpeedToUpdateTurn ? mountData.Core.Physics.GoalMaxDeltaYawTurnInPlace : Math::GetMappedRangeValueClamped( speedRangeWalk2SprintBuffered, FVector2D(mountData.Core.Physics.GoalMaxDeltaYawWalk, mountData.Core.Physics.GoalMaxDeltaYawSprint), cur2DSpeed );
		float goalMaxDeltaYaw 		= isMoveReverse ? mountData.Core.Physics.GoalMaxDeltaYawReverse : goalMaxDeltaYawFor;
		if( Math::Abs(deltaYaw) > goalMaxDeltaYaw )
		{
			float newDeltaYaw 	= goalMaxDeltaYaw * Math::SignNeverZero( deltaYaw );
			float diffYaw		= Math::FindDeltaAngleDegrees(deltaYaw, newDeltaYaw);
			
			//redo these calculations
			deltaYaw 			= newDeltaYaw;
			goalYaw 			= FRotator(0,goalYaw,0).Compose( FRotator(0, diffYaw, 0) ).Yaw;
			goalDir2D 			= FRotator(0, goalYaw, 0 ).GetForwardVector();
			goalDotCur			= goalDir2D.DotProduct( curDir2D );		
		}
		
		///////////////////////////////////////////////////////////////////////////
		// 	ACCELERATION
		//calculate delta in acceleration based on input
			/*	we calculate speed independant of movement vector, because we're assuming that a mount at
				full gallop will stay at full gallop and just take a wide arc to turn. But because of this
				we need to take "reversing" into concideration, otherwise we will slide backward at full speed 	*/
				
		const float NOINPUT_MOMENTUM_MAG 	= 0.25;	 //not eligible for "tweaking"... this insure the mount doesn't slide around.
		float mountMomentum 				= Math::GetMappedRangeValueClamped( FVector2D(mountData.Core.Physics.MinSpeedToUpateTurn, dataPhysicsSprintSpeedMax), FVector2D(0, NOINPUT_MOMENTUM_MAG), cur2DSpeed );
		float moveMagnitude 				= Math::Max( inputMag, mountMomentum ); 	//if no input, make sure to give some goalMagnitude so we don't just slide in a direction we're not facing 
		float reverseLowSpeed 				= mountData.Core.Physics.ReverseSpeedMax + 50;		//give it some buffer so just in case we're a bit above reverse speed, we don't kick in the extreme drag to stop us instantly

		const bool isSprinting = IsSprinting();
		const bool hasBoost = HasBoost();

			/*	if reverse, just take inputmag. If the goal is in the opposite direction we're headed and we're 
				not providing input, then goalMagnitude is overriden by 1 so we don't slide backward at high speed. 
				at low speed though, don't override 1 so drag can naturally bring us to a standstill... if we don't
				take low speed into account, we come to an instant stop when reversing and then letting go of input. */

		// This is a multiplier that gets applied to your non-active sprint speed.
		// We know you're in non-active sprint because ADS takes you out of it.
		// If you're not sprinting at all, this has no effect.
		float adsPenalty = 0.0;

		if ( IsValid(pilot) )
		{
			ANCWeapon activeWeapon = pilot.GetActiveWeapon();
			if ( IsValid( activeWeapon ) && GetCvarBool( f"VehicleMount.EnableADSSlowdown" ) )
			{
				// Workaround way to handle settings. Eventually it should be built into the weapon settings.
				float32 maxSpeedReductionFrac = mountData.Core.Physics.DefaultSpeedMaxReductionWhenADS;
				if ( mountData.Core.Physics.WeaponToSpeedMaxReductionWhenADS.Contains( activeWeapon.GetWeaponClass() ) )
				{
					maxSpeedReductionFrac = mountData.Core.Physics.WeaponToSpeedMaxReductionWhenADS[ activeWeapon.GetWeaponClass() ];
				}

				// To be consistent with other weapon speed slow fracs, this will be something like 0.7, representing that you keep 70% of your speed. Subtract it from 1 to get how much speed to remove.
				// The penalty ramps up based on ADS fraction, so that it's a little smoother. This would hardly be feel-able though.
				const float weaponPenalty = Math::GetMappedRangeValueClamped( FVector2D( 0, 1 ), FVector2D( 0, 1.0 - maxSpeedReductionFrac ), pilot.AdsFraction );
				const float maxADSPenaltyAmount = ( dataPhysicsSprintSpeedMax - mountData.Core.Physics.WalkSpeedMax ) * weaponPenalty;

				// The ADS penalty will never make you go slower than walk speed
				adsPenalty = Math::Min( dataPhysicsSprintSpeedMax - mountData.Core.Physics.WalkSpeedMax, maxADSPenaltyAmount );
			}
		}

		const float STOP_SLIDE_VAL	= 1.0; // at extreme turns, the goalDotCur can become negative and we need to set the mag to 1 to insure we do not loose grip and slide backward
		bool stillEmbarking 		= embarkElapsedTime < 0.6; 
		float reverseSpeed 			= isBraking ? mountData.Core.Physics.BrakingDeceleration : mountData.Core.Physics.ReverseAcceleration;
		float groundAcc 			= isInputReverse ? reverseSpeed : !isSprinting ? mountData.Core.Physics.WalkAcceleration : !hasBoost ? dataPhysicsSprintSpeedAcc : Math::GetMappedRangeValueClamped( FVector2D(dataPhysicsSprintSpeedMax, dataPhysicsBoostSpeedMax), FVector2D(dataPhysicsSprintSpeedAcc, dataPhysicsBoostSpeedAcc), cur2DSpeed);
		float max2DSpeed 			= isInputReverse ? mountData.Core.Physics.ReverseSpeedMax : isSprinting ? hasBoost ? dataPhysicsBoostSpeedMax : dataPhysicsSprintSpeedMax - adsPenalty : mountData.Core.Physics.WalkSpeedMax;
		max2DSpeed	 				= stillEmbarking ? Math::Min( local_embarkSpeed, max2DSpeed ) : max2DSpeed;	//don't take off super quick while embarking

		if ( IsValid(pilot) )
		{
			float boostStatusValue = pilot.GetStatusEffectValue( GameplayTags::StatusEffect_MountSpeedGateBoost );
			if ( !stillEmbarking )
			{
				float speedBoostScale = Math::GetMappedRangeValueClamped( FVector2D(0,1), FVector2D(1, MountSpeedGate::MAX_SPEED_SCALE), boostStatusValue );
				max2DSpeed *= speedBoostScale;
				float speedBoostAccelScale = Math::GetMappedRangeValueClamped( FVector2D(0,1), FVector2D(1, MountSpeedGate::MAX_ACCEL_SCALE), boostStatusValue );
				groundAcc *= speedBoostAccelScale;
			}
		}

		float goalMagnitude			= isInputReverse ? inputMag : goalDotCur >= 0 ? moveMagnitude : cur2DSpeed > reverseLowSpeed ? STOP_SLIDE_VAL : moveMagnitude; 
		bool isDead 				= ownerMount.GetVehicleState() == EVehicleActiveState::Vehicle_Dead;
		float minGroundDrag 		= inputMag < mountData.Core.Physics.MinInput ? Math::GetMappedRangeValueClamped( FVector2D(0, mountData.Core.Physics.NoInputGroundDragSpeed), FVector2D(mountData.Core.Physics.NoInputGroundDrag, mountData.Core.Physics.MinGroundDrag), cur2DSpeed ) : mountData.Core.Physics.MinGroundDrag;
		float minDrag 				= isDead ? mountData.Core.Physics.MinDeathSlideDrag : falling ? mountData.Core.Physics.MinAirDrag : minGroundDrag;
		float acceleration 			= (falling ? mountData.Core.Physics.AirAcceleration : groundAcc) * goalMagnitude;


		if ( isInputReverse )	//if braking: negative acc | moving but at max speed: zero out acc | Otherwise regular acceleration
			acceleration 	= isBraking ? acceleration * -1 : cur2DSpeed > max2DSpeed ? 0 : acceleration;
		else
		{
			FVector2D overMaxSpeedOutMap	= FVector2D( mountData.Core.Physics.OverMaxWalkSpeedDrag, mountData.Core.Physics.OverMaxSprintSpeedDrag );

			//If you're in ADS and over the speed cap, slow the player down extra quickly. Most of the time you'll be over max speed (which is like a goal speed) *because* you're in ADS.
			if ( IsADSInputPressed() )
			{
				overMaxSpeedOutMap.Y *= mountData.Core.Physics.OverMaxSpeedAndInADSDragScalar;
			}

			float overMaxSpeedDrag 			= Math::GetMappedRangeValueClamped(speedRangeWalk2Sprint, overMaxSpeedOutMap, cur2DSpeed); 
			//if we're at max speed, reduce acceleration applied. I know it says overMaxSpeedDrag... but it's not true drag calculation... 
			//this drag actually decreases with speed... it's just a feel good thing with going from boost to sprint... or sprint to run
			//that doesn't effect the normal drag calculations.
			acceleration 					= cur2DSpeed > max2DSpeed ? Math::Max( 0, acceleration - overMaxSpeedDrag ) : acceleration;
		}

		//So switching from a highspeed left to right ( or vise versa ) turn quickly can cause you to loose traction
		//this is an agressive drift compensation to fix that edge case
		float driftDot  		= AnglesToDot( 60 );
		bool isLoosingGrip 		= !falling && cur2DSpeed > mountData.Core.Physics.WalkSpeedMax && goalDotCur < driftDot;
		float driftingAlpha 	= !isLoosingGrip ? 0 : Math::GetMappedRangeValueClamped( FVector2D(driftDot, -0.5), FVector2D(0.0, 1.0 ), goalDotCur );		
		bool isDrifting 		= driftingAlpha > 0.9;
		float driftDecel		= !isDrifting ? 0 : GetDriftComponesationDeceleration(acceleration, driftingAlpha, cur2DSpeed );	//if we're drifting, don't continue to accelerate in the wrong dir... in fact, decelerate quickly. 
		
		//compute drag 
		float dragCoefficient 	= falling ? mountData.Core.Physics.DragCoefficientAir : mountData.Core.Physics.DragCoefficientGround; //resistence to change...
		float finalDrag 		= Math::Max( minDrag, 0.5 * Math::Square(cur2DSpeed) * dragCoefficient);
		
		//combine acceleration and drag to get final speed
		float deltaAcc			= acceleration -( driftDecel + finalDrag );					//when reverse input at high speed, acc will be negative. That plus drag will slow us down real fast.
		float deltaToMaxSpeed 	= cur2DSpeed < max2DSpeed ? max2DSpeed - cur2DSpeed : 0; 	//don't go over max speed
		float deltaAccThisFrame = Math::Min( deltaToMaxSpeed, deltaAcc * deltaTime );		//this will handle max acc, acc to max speed, and negative acc due to drag or reversing
		float finalSpeed 		= Math::Max( 0, cur2DSpeed + deltaAccThisFrame );

		////////////////////////////////////
		//	VELOCITY
			/*	we want to add a fraction of our new input vel to our normalized current vel, the greater the fraction, 
				the more responsive the mount will feel. We want to be very responsive at low speed, and lose that grip
				at higher speed. But we're not a car... so we don't actually want to heavily drift at boost speeds so we
				need to fake it and bring some of that grip back at boost speeds */
		FVector2D velFracInBst 	= FVector2D( dataPhysicsSprintSpeedMax, dataPhysicsBoostSpeedMax );
		FVector2D velFracOutSpr = FVector2D( mountData.Core.Physics.InputAlphaOnDirAtWalkVelFrac, mountData.Core.Physics.InputAlphaOnDirAtSprintVelFrac );
		FVector2D velFracOutBst = FVector2D( mountData.Core.Physics.InputAlphaOnDirAtSprintVelFrac, mountData.Core.Physics.InputAlphaOnDirAtBoostVelFrac );
		FVector2D velFracInMap 	= cur2DSpeed < dataPhysicsSprintSpeedMax ? speedRangeWalk2Sprint : velFracInBst;
		FVector2D velFracOutMap = cur2DSpeed < dataPhysicsSprintSpeedMax ? velFracOutSpr : velFracOutBst;
		float newVelStrength	= falling ? mountData.Core.Physics.InputAlphaOnDirInAirFrac : isBraking ? mountData.Core.Physics.InputAlphaOnDirWhenBrakingFrac : Math::GetMappedRangeValueClamped( velFracInMap, velFracOutMap, cur2DSpeed ); 
				
		//combine everything to get new 2D velocity
		FVector moveDir2D 		= isInputReverse && !isBraking ? goalDir2D * -1 : goalDir2D;
		FVector inputFrac2D 	= moveDir2D * goalMagnitude * newVelStrength;
		FVector finalDir2D 		= curDir2D + inputFrac2D;
		finalDir2D.Normalize();
		FVector finalVel2D		= finalDir2D * finalSpeed;

		//add all Z forces and finalize velocity
		FVector finalVelZ 		= curVelZ + jumpVelocity;
		FVector finalVel		= finalVel2D + finalVelZ;

		////////////////////////////////////
		//	YAW TURNING
			/*	while yaw is technically "physics" it probably has the greatest impact on animation so it's not absurd
				to change yaw timings to make the animation feel better. It's a fine balance between what looks good
				and what feels good. The final rotation is determined after roll is calculated in the anim data section.	*/

		//how quickly we turn is directly related to the strength of delta yaw
		FVector2D yawMinRampOut = FVector2D(mountData.Core.Physics.WalkYawTurnMin, mountData.Core.Physics.SprintYawTurnMin);
		float yawSpeedMin		= falling ? mountData.Core.Physics.AirYawTurnMin : Math::GetMappedRangeValueClamped( speedRangeWalk2Sprint, yawMinRampOut, cur2DSpeed);
		FVector2D yawMaxRampOut = FVector2D(mountData.Core.Physics.WalkYawTurnMax, mountData.Core.Physics.SprintYawTurnMax);
		float yawSpeedMax		= falling ? mountData.Core.Physics.AirYawTurnMax : Math::GetMappedRangeValueClamped( speedRangeWalk2Sprint, yawMaxRampOut, cur2DSpeed);
		float yawSpeed 			= Math::GetMappedRangeValueClamped( FVector2D(0,goalMaxDeltaYaw), FVector2D(yawSpeedMin,yawSpeedMax), Math::Abs(deltaYaw));
		float yawThisFrame 		= yawSpeed * deltaTime;	
		float deltaYawThisFrame = Math::Min( Math::Abs(deltaYaw), yawThisFrame ) * Math::SignNeverZero(deltaYaw);	
		
		//finalize yaw
		float finalYaw 			= curYaw + deltaYawThisFrame;

	/****************************************************************\
	 __  _  _ _ _  _    ___   __  ___  __ 
	|__| |\ | | |\/|    |  \ |__|  |  |__| 
	|  | | \| | |  |    |__/ |  |  |  |  | 
                                     					
	\****************************************************************/
		
		//get goal and delta roll	
		float maxRollApply		= GetMaxAnimRollToApply(mountData, cur2DSpeed, falling);
		float curRoll 			= GetAnimRoll();
		float goalRoll 			= Math::GetMappedRangeValueClamped( FVector2D(0,yawSpeed), FVector2D(0,maxRollApply), Math::Abs(deltaYaw)) * Math::SignNeverZero(deltaYaw);
		float deltaRoll			= goalRoll - curRoll;
		deltaRoll 				= Math::Abs(deltaRoll) > 180 ? (Math::Abs(deltaRoll) - 180) * Math::SignNeverZero(deltaRoll)*-1 : deltaRoll;

		//how quickly we roll is directly related to the strength of delta yaw
		FVector2D rollinRampOut = FVector2D(mountData.Asset.Anim.WalkRollTurn, mountData.Asset.Anim.SprintRollTurn);
		float rollSpeed			= falling ? mountData.Asset.Anim.AirRollTurn : !atMinSpeedToUpdateTurn ? mountData.Asset.Anim.IdleRollTurn: Math::GetMappedRangeValueClamped( speedRangeWalk2Sprint, rollinRampOut, cur2DSpeed);
		float rollThisFrame 	= rollSpeed * deltaTime;
		float deltaRollThisFrame = Math::Min( Math::Abs(deltaRoll), rollThisFrame ) * Math::SignNeverZero(deltaRoll);

		//finalize roll		
		float finalRoll 		= curRoll + deltaRollThisFrame;
	
		//Only WRITE the new roll from the code callback - and even then only on prediction (which is handled inside the function )
		if ( !READ_ONLY )
			SetAnimRoll( finalRoll );
		
		//fraction of roll to apply on top of 3p anim		
		float extraRollHack 	= finalRoll * mountData.Asset.Anim.LeanScale3P;

		/*	I don't trust reconciliation frames for data being sent to the viewmodel. 
			I've noticed that it causes a lot of rubberbanding in the viewmodel anims.
			This is obviously not a shipping solution. Or maybe it is? Maybe I don't 
			understand how reconciliation is supposed to be used. 	*/
		bool useCodeMount = ANCVehicle::UsesCodeMovement();
		if ( (!READ_ONLY && IN_PREDICTION) || useCodeMount)	
		{
			local_animBP_DeltaViewYaw = deltaCur2ViewYaw;
			local_animBP_DeltaGoalYaw = deltaYaw_PreGoalMaxCap;

			bool braking = useCodeMount ? ownerMount.VehicleMountAnimData.IsBraking : isBraking;
		
			if ( (braking && GetLocomotionSpeed() >= mountData.Asset.Anim.BrakeAnim_SprintSpeed ) && !local_wasBraking )
			{
				local_wasBraking = true;
				OnStartBraking.Broadcast(pilot);
			}
			else if ( (!braking || GetLocomotionSpeed() < mountData.Asset.Anim.BrakeAnim_SprintSpeed ) && local_wasBraking )
			{
				local_wasBraking = false;
				OnEndBraking.Broadcast(pilot);
			}
		}

	///////////////////////////////////////////////////////////////////////////
	//	PACKAGE DATA		
		calc.finalVel 				= finalVel;
		calc.finalYaw 				= finalYaw;
		calc.finalRoll 				= finalRoll;
		calc.extraRollHack 			= extraRollHack;
		calc.finalSpeed 			= finalSpeed;
		calc.isBraking 				= isBraking;
		calc.isFalling 				= falling;
		calc.isFreeLook				= isFreeLook;
		calc.isFreeLookReturn		= isFreeLookReturn;
		//anim
		calc.animBP_DeltaGoalYaw 	= local_animBP_DeltaGoalYaw;
		calc.animBP_DeltaViewYaw 	= local_animBP_DeltaViewYaw;
		calc.viewmodelSpeed 		= GetLocomotionSpeed();

#if EDITOR
		if ( GetCvarBool(f"VehicleMount.DebugAcceleration") )
			DevPrintTimeToMaxSpeeds(calc);
		if ( GetCvarBool(f"VehicleMount.DebugDrift") )
			DevPrintDriftDistance( isDrifting, driftingAlpha, driftDecel );
		if( GetCvarBool(f"VehicleMount.DebugSpeed") && IsServer() )
		{
			float devMaxSpeed = HasBoost() ? dataPhysicsBoostSpeedMax : dataPhysicsSprintSpeedMax;
			Print( f"Speed: {finalSpeed} | Max: { devMaxSpeed }", 0.045);
		}
		if( GetCvarBool(f"VehicleMount.DebugGoalDir") && IsServer() )	
			DevPrintGoalDir( calc, inputDir2D, goalDir2D, finalDir2D, curYaw, deltaYaw );
		if ( GetCvarBool(f"VehicleMount.DebugTurnRadius") )
			DebugTurnRadius();	
		if ( GetCvarBool(f"VehicleMount.DebugLean") )
			DebugLean();	
		DebugLookAnims( deltaTime );
#endif

		return calc;
	}

	private FVector GetMoveInputWorldDir2D( float pilotAimYaw, FVector inputRaw2D )
	{
		FVector inputDir2D 		= inputRaw2D.RotateAngleAxis(pilotAimYaw,FVector::UpVector);

		return inputDir2D;
	}	

	private float GetPilotAimYaw( const ANCPlayerCharacter pilot, bool isFreeLook, bool isFreeLookReturn )
	{				
		if ( isFreeLook || isFreeLookReturn )
			return net_lastActivePilotViewYaw;

		return pilot.GetViewRotation().Yaw;
	}

	private float GetDriftComponesationDeceleration( float curAcceleration, float driftingAlpha, float cur2DSpeed )
	{
		FVector2D driftInMap 	= FVector2D(localData.SprintSpeedMax, localData.BoostSpeedMax);
		/*	drift decel WAY harder at speeds above sprint... in reality the faster you go, the longer you drift,
			but we're not car... and that feels stupid. The boost speed is gamey, so lets not get hung up on realism
			*/
		FVector2D driftOutMap 	= FVector2D(0.3,5.0); 
		
		return curAcceleration * driftingAlpha * Math::GetMappedRangeValueClamped(driftInMap, driftOutMap, cur2DSpeed);
	}

	private float GetGoalYaw( FVehicleMountData data, bool keepCurGoalDir, bool atMinSpeedToUpdateTurn, bool isInputReverse, FVector inputDir2D, float inputMag, float pilotAimYaw, float idleGoalYaw )
	{		
		if ( isInputReverse )
		{
			if ( keepCurGoalDir )
				return pilotAimYaw;
			else
				return (inputDir2D*-1).Rotation().Yaw;
		}  
		else if ( inputMag > data.Core.Physics.MinInput )
		{
			return inputDir2D.Rotation().Yaw;
		}
		else if ( atMinSpeedToUpdateTurn )
			return pilotAimYaw;
		else
			return idleGoalYaw;
	}

	private FVector GetMoveInputRaw2D( FVector2D moveInputRaw, FVehicleMountData data )
	{
		FVector realRaw 	= FVector( moveInputRaw.X, moveInputRaw.Y, 0 );
		float inputRawYaw 	= realRaw.Rotation().Yaw;
		float inputRawMag 	= realRaw.Size();
		
		float modAlpha 		= Math::GetMappedRangeValueClamped( FVector2D(data.Core.Physics.inputDeadZoneStartYaw,data.Core.Physics.inputDeadZoneStopYaw), FVector2D(0, 1), Math::Abs(inputRawYaw) );
		float modYaw 		= Math::EaseIn( 0, data.Core.Physics.inputDeadZoneStopYaw, modAlpha, data.Core.Physics.inputDeadZoneEaseExp ) * Math::SignNeverZero(inputRawYaw);
		float finalYaw  	= Math::Abs(inputRawYaw) >= data.Core.Physics.inputDeadZoneStopYaw ? inputRawYaw : modYaw; 

		//Print( f"rawYaw: {inputRawYaw} : finalYaw: {finalYaw}" );
		FVector modRaw 		= FRotator(0, finalYaw, 0 ).GetForwardVector() * inputRawMag;
				
		return modRaw;	
	}

	private void InitRealVelocity()
	{
		local_TrackedVel.Empty();
	}

	private void TrackRealVelocity( AAS_VehicleMount owner, int gameTimeMS, bool isFalling )
	{
		if ( isFalling )
			local_TrackedVel.Empty();
		
		FTrackAveVelHack curloc		= FTrackAveVelHack( owner, gameTimeMS );
		local_TrackedVel.Add(curloc);
		if ( local_TrackedVel.Num() > MAX_TRACKED_VEL_COUNT + 1 )
			local_TrackedVel.RemoveAt(0);
	}

	private float GetAverageTrackedZVel()
	{
		float averageZVel = 0;

		//we're averaging the Z values over time to smooth out sudden changes in geometry
		if ( local_TrackedVel.Num() > 1 )
		{					
			for ( int i = 1; i < local_TrackedVel.Num(); i++ )
			{
				float trackedVel = local_TrackedVel[i].GetAverageVelZ(local_TrackedVel[i-1]);
				averageZVel += trackedVel;
			}

			averageZVel /= ( local_TrackedVel.Num() - 1 );
		} 

		return averageZVel;
	}

	private FVector GetAverageTrackedVel()
	{
		FVector averageVel = FVector::ZeroVector;

		//we're averaging the Z values over time to smooth out sudden changes in geometry
		if ( local_TrackedVel.Num() > 1 )
		{					
			for ( int i = 1; i < local_TrackedVel.Num(); i++ )
			{
				FVector trackedVel = local_TrackedVel[i].GetAverageVel(local_TrackedVel[i-1]);
				averageVel += trackedVel;
			}

			averageVel /= ( local_TrackedVel.Num() - 1 );
		} 

		return averageVel;
	}

/****************************************************************\

 █████  ███    ██ ██ ███    ███ 
██   ██ ████   ██ ██ ████  ████ 
███████ ██ ██  ██ ██ ██ ████ ██ 
██   ██ ██  ██ ██ ██ ██  ██  ██ 
██   ██ ██   ████ ██ ██      ██
                                  
\****************************************************************/	
	private void SetAnimRoll( float roll )
	{
		//this shouldn't be something script should worry about, but if I don't check for prediction... the client will roll WAY too fast
		if ( IsServer() )
			net_animRoll.SetNetValue(roll);
		if ( IsClient() && IsValid(GetPilot()) && UNCUtils::InPrediction(GetPilot()) )
			local_animRoll = float32(roll);
	}
	
	float GetAnimRoll()
	{
		if ( IsServer() || GetPilot() != Client_GetLocalPawn() )
			return net_animRoll;

		//this shouldn't be something script should worry about, but if I don't check for predicted client, it feels bad	
		return local_animRoll;
	}

	UFUNCTION()
	void cl_OnAnimRollReplicated( float32 oldVal, float32 newVal )
	{
		local_animRoll = newVal;
	}

	UFUNCTION(BlueprintPure)
	FVector GetAnimDynamicExternalForce()
	{
		FVector forward = GetOwnerMount().GetActorRotation().GetForwardVector();
		float speed = GetLocomotionSpeed();
		FVector result = forward * speed * 10.0f + FVector(0, 0, speed);
		return result;
	}

	float GetMaxAnimRollToApply( FVehicleMountData data, float cur2DSpeed, bool isFalling )
	{		
		FVector2D speedRampIn 			= FVector2D(data.Core.Physics.WalkSpeedMax,localData.SprintSpeedMax);
		FVector2D rollMaxRampOut 		= FVector2D(data.Asset.Anim.WalkRollMax, data.Asset.Anim.SprintRollMax);
		bool atMinSpeedToUpdateTurn 	= cur2DSpeed > data.Core.Physics.MinSpeedToUpateTurn;

		float maxRoll 	= isFalling ? data.Asset.Anim.AirRollMax : !atMinSpeedToUpdateTurn ? data.Asset.Anim.IdleRollMax : Math::GetMappedRangeValueClamped( speedRampIn, rollMaxRampOut, cur2DSpeed);
		
		return maxRoll;
	}

/****************************************************************\

██████  ██ ███████ ███    ███  ██████  ██    ██ ███    ██ ████████     ██ ███████ ██      ██ ██████  ███████ 
██   ██ ██ ██      ████  ████ ██    ██ ██    ██ ████   ██    ██       ██  ██      ██      ██ ██   ██ ██      
██   ██ ██ ███████ ██ ████ ██ ██    ██ ██    ██ ██ ██  ██    ██      ██   ███████ ██      ██ ██   ██ █████   
██   ██ ██      ██ ██  ██  ██ ██    ██ ██    ██ ██  ██ ██    ██     ██         ██ ██      ██ ██   ██ ██      
██████  ██ ███████ ██      ██  ██████   ██████  ██   ████    ██    ██     ███████ ███████ ██ ██████  ███████
                                  
\****************************************************************/	
	private void ImpartVelocityOnDismountingPlayer( ANCPlayerCharacter oldPilot )
	{		
		AAS_VehicleMount ownerMount = GetOwnerMount();
		FVector initialVel 			= ownerMount.GetVelocity();
		FVehicleMountData data 		= GetMountData(); 
		float32 maxSpeed			= data.Core.Physics.BoostSpeedMax + 100; //add a little buffer for vertical speed... don't want to cap the 2D speed unless it's extreme.
		float32 dismountMag 		= float32(Math::Min( initialVel.Size(), maxSpeed ));

		//cap dismount speed to boost speed max ( no crazy speed gate values )	
		initialVel.Normalize();
		initialVel *= dismountMag;

		if ( local_EjectPressed )
		{			
			FVector ejectBoost 		= GetEjectVelocity(oldPilot);
			FVector ejectVelocity	= initialVel + ejectBoost;

			oldPilot.SetMovementMode( EMovementMode::MOVE_Falling );
			oldPilot.SetMovementComponentVelocity( ejectVelocity );
#if EDITOR	
			if ( IsServer() && GetCvarBool( f"VehicleMount.DebugJump" ) )
				DebugJump( oldPilot, ejectVelocity );
#endif			
		}
		else
		{
			if ( local_ExitPressed )	
			{
				//the exit button is hold crouch. So we need to disable crouch momentarily, until we can decide if it was just a tap and we want to slide.
				if ( IsServer() )
					oldPilot.DisableCrouch();			

				UAS_VehicleMountMotionModel_ThreadSystem system = UAS_VehicleMountMotionModel_ThreadSystem::Get();
				Thread( system, n"__HACK_GuaranteeSlideOnDismount", oldPilot, ownerMount, initialVel );
			}
			
			// Your eye height lerps back (via reconciliation) to default height, which also creates an illusion of "falling faster" when disembarking. This'll be fixed later.

			ENCVehicleMovementMode curMoveMode = ownerMount.GetVehicleMovementMode();
			FVector velocityToUse = initialVel;

			if ( GetCvarBool( f"VehicleMount.EnableKickOffMountInAir" ) )
			{
				//bug fix for https://wildlight.atlassian.net/browse/NC1-19827
				//the mount can fly across the mount by spamming X
				//dismounting gives you a little bump that feels good on jumps... but can be abused to get infinite upward velocity
				//this debounce gives you the good feeling when you need it, but prevents the exploit
				const float32 KICKOFF_MOUNT_IN_AIR_DEBOUNCE 	= 1.0f;
				AAS_PlayerEntity player = Cast<AAS_PlayerEntity>(oldPilot);				
				float32 elapseTime = TO_SECONDS(GetGameTimeMS() - player.mountData.lastKickoffMountInAirTimeMS );

				if ( elapseTime > KICKOFF_MOUNT_IN_AIR_DEBOUNCE && curMoveMode == ENCVehicleMovementMode::Falling )
				{
					float initialVelSpeed2D = initialVel.Size2D();
					float hackEpsilon = 50;
					if ( initialVelSpeed2D > ( data.Core.Physics.WalkSpeedMax + hackEpsilon ) )
					{
						FVector kickOffMountImpulse = GetKickOffMountVelocity( oldPilot, velocityToUse, 50 );
						velocityToUse += kickOffMountImpulse;
						player.mountData.lastKickoffMountInAirTimeMS = GetGameTimeMS();
					}
				}
			}

			// Print( f"initialVel: {initialVel} velocityToUse: {velocityToUse}");

			oldPilot.SetMovementMode( curMoveMode == ENCVehicleMovementMode::Walking ? EMovementMode::MOVE_Walking : EMovementMode::MOVE_Falling );
			oldPilot.SetMovementComponentVelocity( velocityToUse );
		}

#if EDITOR			
		if ( IsServer() && GetCvarBool( f"VehicleMount.DebugSlide" ) )
			Thread( this, n"DebugSlide", oldPilot );
#endif
	}	

	private void DebugJump( ANCPlayerCharacter oldPilot, FVector velocity )
	{
		FVector jumpPos			= GetDebugJumpLoc(oldPilot);
		debugJumpEject 			= true;
		debugJumpAtApex 		= false;
		float height 			= jumpPos.Z - startDebugJumpPos.Z; 
		
		FString text 			= FString( f"Speed: {Math::RoundToInt(velocity.Size2D()) } \nJump Z: {Math::RoundToInt(jumpPos.Z)} \nJump Height:{Math::RoundToInt(height)} \nJump Vel: {Math::RoundToInt(velocity.Z)}");
		
		DrawDebugString( jumpPos, text, GetDebugJumpDuration(), DEBUG_JUMP_COLOR_PLAYER );
		DrawDebugLine( jumpPos, jumpPos + FVector(0,0,-height), GetDebugJumpDuration(), DEBUG_JUMP_COLOR_PLAYER, DEBUG_JUMP_THICK );
		DrawDebugLine( oldDebugJumpPos, jumpPos, GetDebugJumpDuration(), DEBUG_JUMP_COLOR_MOUNT, DEBUG_JUMP_THICK );
		oldDebugJumpPos 	= jumpPos;
		debugJumpLaunchPos 	= jumpPos;
	}

/****************************************************************\

██████  ██       █████  ██    ██ ███████ ██████      ██    ██ ██ ███████ ██     ██ 
██   ██ ██      ██   ██  ██  ██  ██      ██   ██     ██    ██ ██ ██      ██     ██ 
██████  ██      ███████   ████   █████   ██████      ██    ██ ██ █████   ██  █  ██ 
██      ██      ██   ██    ██    ██      ██   ██      ██  ██  ██ ██      ██ ███ ██ 
██      ███████ ██   ██    ██    ███████ ██   ██       ████   ██ ███████  ███ ███ 
                                  
\****************************************************************/	
	//hack because this isn't smooth at all... 
	void HACK_ReturnPlayerViewFromFreelook(ANCPlayerCharacter pilot, FVehicleMountData data, FVehicleMountMotionModelCalc calc, float deltaTime, int gameTimeMS)
	{
		if ( calc.isFreeLook || !calc.isFreeLookReturn )
			return;

		// don't know why... but even when it stops blending there's tons of time left over where this function is still running
		// FreelookReturnBlendTimeBufferHack helps mitigate that		
		float elapsedTime 		= TO_SECONDS( gameTimeMS - local_freelookStopTimeMS );
		FVector2D viewBlendIn 	= FVector2D(0, data.Core.Physics.FreelookReturnBlendTime + data.Core.Physics.FreelookReturnBlendTimeBufferHack );
		float viewBlendAlpha 	= Math::GetMappedRangeValueClamped( viewBlendIn, FVector2D(0,1), elapsedTime );
		float viewBlendAlphaExp = Math::EaseIn( 0, 1, viewBlendAlpha, 2); 

		float aimYaw 			= GetPilotAimYaw( pilot, calc.isFreeLook, calc.isFreeLookReturn );
		FRotator viewRotation 	= pilot.GetViewRotation();
		FRotator goalRotation 	= viewRotation;
		goalRotation.Yaw 		= aimYaw;
		FRotator rotationThisFrame = Math::LerpShortestPath( viewRotation, goalRotation, viewBlendAlphaExp );
		
		pilot.SetViewRotation(rotationThisFrame);
	}

/****************************************************************\

████████  █████  ███    ██ ██   ██      ██████  ██████  ███    ██ ████████ ██████   ██████  ██      ███████ 
   ██    ██   ██ ████   ██ ██  ██      ██      ██    ██ ████   ██    ██    ██   ██ ██    ██ ██      ██      
   ██    ███████ ██ ██  ██ █████       ██      ██    ██ ██ ██  ██    ██    ██████  ██    ██ ██      ███████ 
   ██    ██   ██ ██  ██ ██ ██  ██      ██      ██    ██ ██  ██ ██    ██    ██   ██ ██    ██ ██           ██ 
   ██    ██   ██ ██   ████ ██   ██      ██████  ██████  ██   ████    ██    ██   ██  ██████  ███████ ███████ 
                                                                                                           
\****************************************************************/	
	const float32 tankControlsFreelookFreelookReturnDelayTime = 0.25;
	const float32 tankControlsFreelookFreelookReturnBlendTime = 0.8;

	float GetTankControlsFreelookAlpha()
	{
		if ( IsFreeLook() )
			return 1.0;

		float elapsedTankFreeLookTime 	= TO_SECONDS( GetGameTimeMS() - local_TankFreelookTimeMS );
		float viewBlendAlpha 			= Math::Min( 1.0, Math::Max( 0, elapsedTankFreeLookTime - tankControlsFreelookFreelookReturnDelayTime ) / tankControlsFreelookFreelookReturnBlendTime );
		float viewBlendAlphaExp 		= Math::EaseIn( 0, 1, viewBlendAlpha, 2); 
		
		float FreelookAlpha = 1.0 - viewBlendAlphaExp;
		return FreelookAlpha;
	}

	void RecordTankLookInput(const ANCPlayerCharacter pilot)
	{
		int delayBuffer = TO_MILLISECONDS( tankControlsFreelookFreelookReturnDelayTime );
		if ( local_TankFreelookTimeMS < local_freelookStopTimeMS - delayBuffer )
			local_TankFreelookTimeMS = local_freelookStopTimeMS - delayBuffer;

		if ( IsServer() )
			return;
		
		ScriptAssert ( IsClient(), "MUST RUN ON CLIENT" );

		//X is left/right, Y is up/down... look axis is NOT normalized
		FVector lookInputRaw 		= pilot.GetInputAxisTurn();
		bool minLookInput 			= Math::Abs(lookInputRaw.X) > 0.35;

		if ( minLookInput && !IsFreeLook() )
		{
			local_TankFreelookTimeMS = GetGameTimeMS();
			//if ( IsValid(Client_GetLocalPlayerController()))
			//	UNCRemoteScriptCommands::SendClientCheat( Client_GetLocalPlayerController(), f"ClientMountMotionModelSetTankFreelookTime {CachedOwnerMount.GetEntIndex()} {local_TankFreelookTimeMS}" );
		}
	}
	
	float GetNewTankYaw( const ANCPlayerCharacter pilot, FVehicleMountData data, FVector inputRawTank, FVector curVel2D, bool atMinSpeedToUpdateTurn, bool isInputReverse, float deltaTime)
	{
		AAS_VehicleMount ownerMount = GetOwnerMount();

		float tankFreelookAlpha 	= GetTankControlsFreelookAlpha();
		float tankTurnYaw 			= inputRawTank.Rotation().Yaw;
		
		if ( GetCvarBool(f"VehicleMount.LockTankControlsInFreelook") && tankFreelookAlpha > 0 )
			tankTurnYaw = 0;
		else if ( isInputReverse )
		{
			//...prelim braking calc, if braking... then don't turn
			float tankVelDotCur		= curVel2D.DotProduct( ownerMount.GetActorForwardVector().GetSafeNormal2D() );	
			bool isTankReverse 		= tankVelDotCur < 0 && isInputReverse;
			bool isTankBraking 		= isInputReverse && !isTankReverse && atMinSpeedToUpdateTurn;
			if ( isTankBraking )
				tankTurnYaw = 0;
			else
			{
				FVector inputReverse = inputRawTank;
				inputReverse.X *= -1;
				tankTurnYaw = inputReverse.Rotation().Yaw;
			}
		}
					
		//get how much we need to turn
		float yawTankDelta 			= Math::GetMappedRangeValueClamped( FVector2D(0,90), FVector2D(0,data.Core.Physics.LockViewMaxYawDelta), Math::Abs(tankTurnYaw)) * Math::SignNeverZero(tankTurnYaw);
		float yawTankTurnSpeed 		= 2.5;
		float yawDeltaThisFrame 	= Math::Min( data.Core.Physics.LockViewMaxYawDelta, Math::Abs(yawTankDelta * deltaTime * yawTankTurnSpeed) ) * Math::SignNeverZero(tankTurnYaw);

		/*	since we're not using real player view (which is capped to data.Core.Physics.LockViewMaxYawDelta)
			for turning, we need to cap the yaw Delta manually.		*/
		float curTankYaw 			= tankFreelookAlpha > 0.0 ? float(net_lastActiveTankYaw) : pilot.GetViewRotation().Yaw;	
		float curYaw 				= ownerMount.GetActorRotation().Yaw;		
		float curTankDelta 			= Math::FindDeltaAngleDegrees(curYaw, curTankYaw);
		float maxDeltaThisFrame 	= data.Core.Physics.LockViewMaxYawDelta - Math::Abs(curTankDelta);
		yawDeltaThisFrame 			= Math::Min( Math::Abs(yawDeltaThisFrame), maxDeltaThisFrame ) * Math::SignNeverZero(yawDeltaThisFrame);
		
		//final calcs
		float newtankYaw = curTankYaw + yawDeltaThisFrame;
		if ( newtankYaw > 180.0 )
			newtankYaw -= 360.0;
		else if ( newtankYaw < -180.0 )
			newtankYaw += 360.0;	

		return newtankYaw;
	}

	float GetNewTankPitch( const ANCPlayerCharacter pilot, FVehicleMountData data, bool atMinSpeedToUpdateTurn, bool isInputReverse, float deltaTime, bool isFalling )
	{
		float curViewPitch	= pilot.GetViewRotation().Pitch;	
		if ( !GetCvarBool(f"VehicleMount.TankControlsAutoPitch") )
			return curViewPitch;

		FVector trackedVel 			= GetAverageTrackedVel();
		float trackedPitch 			= trackedVel.Rotation().Pitch -5;
		float newViewPitch 			= isFalling ? -35 : isInputReverse ? trackedPitch * -1 : trackedPitch;
		float pitchViewDelta 		= atMinSpeedToUpdateTurn ? newViewPitch - curViewPitch : 0;
		float rawSprintSpeedMax 	= localData.ActiveSprint_SprintSpeedMax;
		float pitchViewSpeed 		= isFalling ? 1.5 : Math::GetMappedRangeValueClamped( FVector2D(data.Core.Physics.WalkSpeedMax,rawSprintSpeedMax), FVector2D(1.0,5.0), GetSpeed());
		float pitchDeltaThisFrame 	= pitchViewDelta * deltaTime * pitchViewSpeed;
		
		float newTankPitch 			= curViewPitch + pitchDeltaThisFrame;
		newTankPitch 				= Math::Min( newTankPitch, __viewPitchMax );
		newTankPitch 				= Math::Max( newTankPitch, data.Core.Physics.LockViewMinPitchDelta );

		return newTankPitch;		
	}

	void sv_SetTankFreelookTime( int time )
	{
		ScriptAssert( IsServer(), "MUST RUN ON SERVER");
		local_TankFreelookTimeMS = time;
	}

/****************************************************************\

 █████   ██████ ████████ ██ ██    ██ ███████     ███████ ██████  ██████  ██ ███    ██ ████████ 
██   ██ ██         ██    ██ ██    ██ ██          ██      ██   ██ ██   ██ ██ ████   ██    ██    
███████ ██         ██    ██ ██    ██ █████       ███████ ██████  ██████  ██ ██ ██  ██    ██    
██   ██ ██         ██    ██  ██  ██  ██               ██ ██      ██   ██ ██ ██  ██ ██    ██    
██   ██  ██████    ██    ██   ████   ███████     ███████ ██      ██   ██ ██ ██   ████    ██
                                  
\****************************************************************/	
	private void __OnActiveSprintInput(ANCPlayerCharacter player, bool onEmbark = false )
	{
		if ( IsServer() )
			net_resumeActiveSprintOnADSRelease.SetNetValue(false);

		if ( !GetCvarBool(f"VehicleMount.EnableActiveSprint") )
			return;

		AAS_PlayerEntity pilot = Cast<AAS_PlayerEntity>(player);
		if ( !IsValid(pilot) )
			return;

		if ( IsServer() )
			sv_TryAddActiveSprint(pilot);

		OnActiveSprintInput.Broadcast(player, onEmbark);
	}

	private void sv_TryAddActiveSprint( AAS_PlayerEntity pilot )
	{
		ScriptAssert( IsServer(), "MUST RUN ON SERVER" );

		net_ActiveSprintStartTimeMS.SetNetValue(GetGameTimeMS());

		//calling this again extends the length of the speed boost
		Thread( this, n"sv_RemoveActiveSprintOnDelayOrDismount", pilot );
	}

	FNCCoroutineSignal EndActiveSprintThreadSignal;

	UFUNCTION()
	private void sv_RemoveActiveSprintOnDelayOrDismount( UNCCoroutine co, AAS_PlayerEntity pilot )
	{
		ScriptAssert( IsServer(), "MUST RUN ON SERVER" );

		FVehicleMountData data 			= GetMountData();

		EndActiveSprintThreadSignal.Emit();
		co.EndOn( this, EndActiveSprintThreadSignal );

		co.AddWait( pilot.GetHealthComponent(), pilot.GetHealthComponent().OnDeathSignal );
		co.AddWait( pilot, pilot.OnDismountSignal );
		co.AddWait( data.Core.Physics.ActiveSprintDuration );
		co.AwaitAny();

		if ( !IsValid(pilot) )
			return;

		sv_ClearActiveSprint();
	}

	void sv_ClearActiveSprint()
	{
		ScriptAssert( IsServer(), "MUST RUN ON SERVER" );

		if ( IsADSInputPressed() && HasActiveSprint() )
			net_resumeActiveSprintOnADSRelease.SetNetValue(true);

		net_ActiveSprintStartTimeMS.SetNetValue(0);
		EndActiveSprintThreadSignal.Emit();
	}

/****************************************************************\

██ ███    ██ ██████  ██    ██ ████████ 
██ ████   ██ ██   ██ ██    ██    ██    
██ ██ ██  ██ ██████  ██    ██    ██    
██ ██  ██ ██ ██      ██    ██    ██    
██ ██   ████ ██       ██████     ██
                                  
\****************************************************************/	
	private void SetupInput( ANCPlayerCharacter playerUser )
	{		
		local_EjectPressed 	= false;
		sv_inputNullMS 		= -1;

		ClearInputCallbacks( playerUser );
		
		sprintInputHandle 		= playerUser.AddButtonPressedCallback( n"Sprint", this, n"OnSprintInput" );
		ejectInputHandle 		= playerUser.AddButtonPressedCallback( n"Jump", this, n"OnEjectInput" );
		freelookInputHandle 	= playerUser.AddButtonPressedCallback( n"Aim", this, n"OnADSInput" );
		freelookReleaseHandle 	= playerUser.AddButtonReleasedCallback( n"Aim", this, n"OnADSInputRelease" );
		crouchInputHandle 		= playerUser.AddButtonPressedCallback( n"Crouch", this, n"OnCrouchInput" );
		crouchReleaseHandle		= playerUser.AddButtonReleasedCallback( n"Crouch", this, n"OnCrouchRelease" );

		if ( GetCvarBool( f"VehicleMount.EnableShootingExitsActiveSprint" ) )
		{
			AAS_PlayerEntity asPlayerUser = Cast<AAS_PlayerEntity>( playerUser );
			asPlayerUser.onPlayerPrimaryAttack.AddUFunction( this, n"OnPrimaryAttack" );
		}
	}	

	private void ClearInputCallbacks( ANCPlayerCharacter playerUser )
	{
		if ( playerUser.ButtonPressedCallback_DoesExist( n"Sprint", sprintInputHandle) )
			playerUser.RemoveButtonPressedCallback( n"Sprint", sprintInputHandle );
		
		if ( playerUser.ButtonPressedCallback_DoesExist( n"Jump", ejectInputHandle) )
			playerUser.RemoveButtonPressedCallback( n"Jump", ejectInputHandle );

		if ( playerUser.ButtonPressedCallback_DoesExist( n"Aim", freelookInputHandle) )
			playerUser.RemoveButtonPressedCallback( n"Aim", freelookInputHandle );

		if ( playerUser.ButtonReleasedCallback_DoesExist( n"Aim", freelookReleaseHandle) )
			playerUser.RemoveButtonReleasedCallback( n"Aim", freelookReleaseHandle );

		if ( playerUser.ButtonPressedCallback_DoesExist( n"Crouch", crouchInputHandle) )
			playerUser.RemoveButtonPressedCallback( n"Crouch", crouchInputHandle );

		AAS_PlayerEntity asPlayerUser = Cast<AAS_PlayerEntity>( playerUser );
		asPlayerUser.onPlayerPrimaryAttack.Unbind( this, n"OnPrimaryAttack" );
	}

	UFUNCTION()
	private void OnSprintInput( ANCPlayerCharacter player )
	{
		if ( IsServer() )
			SetSprinting( true );
		
		if ( IsADSInputPressed() || player.AdsFraction > 0 )
			return;
			
		if ( !IsADSInputPressed() )
			__OnActiveSprintInput(player);
	}

	bool local_slideCooldownMsgSent 	= false;
	int crouchInputTimeMS 				= 0;

	UFUNCTION()
	private void OnCrouchInput( ANCPlayerCharacter player )
	{
		if ( !player.IsPlayerRidingMount() )
			return;
			
		//@ravi -> wish this HOLD CROUCH TO EXIT functionality only existed on controller
		crouchInputTimeMS = GetGameTimeMS();		
		local_ExitPressed = true;
		
		if ( IsServer() )
			Mount::Dismount(player);
	}

	const float32 CROUCH_EXIT_HOLD_TIME = 0.2;
	UFUNCTION()
	private void OnCrouchRelease( ANCPlayerCharacter player )
	{
		if ( !GetCvarBool( f"VehicleMount.EnableSlideOffMount" ) )
			return;

		float elapsedTime = TO_SECONDS( GetGameTimeMS() - crouchInputTimeMS );
		if ( elapsedTime > CROUCH_EXIT_HOLD_TIME )
			return;

		if ( IsTrickCooldownOnEmbark() )
		{
			if ( IsServer() && !local_ejectCooldownMsgSent )
			{
				local_slideCooldownMsgSent 	= true;
				float timeLeft 				= GetTrickCooldownOnEmbarkTimeLeft();
				UNCRemoteScriptCommands::SendServerCommand( player, f"ServerCommand_ShowMountCooldown mount_slide_cooldown {timeLeft}" );
			}			
		}
		else if ( IsSprinting() || player.IsSprinting() )
			local_SlidePressed = true;		
	}

	bool local_ejectCooldownMsgSent = false;
	UFUNCTION()
	private void OnEjectInput( ANCPlayerCharacter player )
	{
		if ( !GetCvarBool( f"VehicleMount.EnableEject" ) )
			return;

		if ( local_EjectPressed )
			return;

		if ( !IsFalling() )
			return;

		if ( !local_LandedAfterEmbark || local_isBunnyHopping )
			return;

		if ( IsTrickCooldownOnEmbark() )
		{
			if ( IsServer() && !local_ejectCooldownMsgSent )
			{
				local_ejectCooldownMsgSent 	= true;
				float timeLeft 				= GetTrickCooldownOnEmbarkTimeLeft();
				UNCRemoteScriptCommands::SendServerCommand( player, f"ServerCommand_ShowMountCooldown mount_eject_cooldown {timeLeft}" );
			}			
		}
		else	
			local_EjectPressed 	= true;

		if ( IsServer() )
			Mount::Dismount(player);
	}

	// Requires "VehicleMount.EnableShootingExitsActiveSprint"
	UFUNCTION()
	private void OnPrimaryAttack( AAS_PlayerEntity player, ANCWeapon weapon )
	{
		// This gets called on server and client, originating from baseWeaponScriptContext. If this gets replaced
		// by a code callback, this may need to become a netvar
		local_lastAttackTimeMS = player.GetTimeMilliseconds();
	}

	private float GetTrickCooldownOnEmbarkElapsedTime()
	{
		return TO_SECONDS( GetGameTimeMS() - local_embarkTimeMS );
	}

	private float GetTrickCooldownOnEmbarkTimeLeft()
	{
		FVehicleMountData data 	= GetMountData();	
		float elapsedCooldownTime 	= GetTrickCooldownOnEmbarkElapsedTime();

		return data.Core.Physics.TrickCooldownOnEmbark - elapsedCooldownTime;
	}

	private bool IsTrickCooldownOnEmbark()
	{
		FVehicleMountData data 	= GetMountData();	
		float elapsedCooldownTime 	= GetTrickCooldownOnEmbarkElapsedTime();

		return elapsedCooldownTime < data.Core.Physics.TrickCooldownOnEmbark;
	}

	private bool PressedJump( const ANCPlayerCharacter pilot, int gameTimeMS, bool isFalling )
	{
		if ( isFalling )
			return false;

		float elapsedTimeEmbark	= TO_SECONDS( gameTimeMS - local_embarkTimeMS );
		bool inEmbarkWindow 	= elapsedTimeEmbark <= 0.75;
		if ( inEmbarkWindow )
			return false;

		//better for prediction to use this instead of setting a bool from a callback	
		return pilot.CheckForJumpButtonPressed();
	}

	private FVector GetJumpVelocity( const ANCPlayerCharacter playerUser, FVehicleMountData data )
	{
		float desiredHeight = local_isBunnyHopping ? data.Core.Physics.JumpHeightBunnyHop : data.Core.Physics.JumpHeightDefault;
		float gravity 		= playerUser.MovementComponent.GetGravityZ() * -1;
		float impulseZ		= Math::Sqrt( 2 * gravity * desiredHeight);

		return FVector( 0, 0, impulseZ );
	}

	private FVector GetEjectVelocity( ANCPlayerCharacter playerUser ) 
	{
		if ( IsClient())
			return FVector::ZeroVector;

		AAS_VehicleMount ownerMount 	= GetOwnerMount();
		FVehicleMountData data 		= GetMountData();
		
		float gravity 	= playerUser.MovementComponent.GetGravityZ() * -1;
		
		//time to apex assuming we're still moving upward
		float curVelZ 			= ownerMount.GetVelocity().Z;
		float timetoApex 		= curVelZ / gravity;
		float apexHeightFromNow = ( curVelZ * timetoApex ) - (0.5 * gravity * Math::Square(timetoApex) );
		float ejectHeightTotal 	= data.Core.Physics.EjectHeightAddMax + apexHeightFromNow;
		
		float desiredHeight 	= curVelZ > 0 ? Math::Max( 0, ejectHeightTotal ) : data.Core.Physics.EjectHeightAddMax;
		float impulseZ			= Math::Sqrt( 2 * gravity * desiredHeight);
		
		return FVector( 0, 0, impulseZ - curVelZ );
	}
	
	private FVector GetKickOffMountVelocity( ANCPlayerCharacter playerUser, FVector inVelocity, float desiredHeightHack ) 
	{
		if ( IsClient())
			return FVector::ZeroVector;
		
		FVehicleMountData data 		= GetMountData();

		// Backwards impulse
		float speed2D = inVelocity.Size2D();
		FVector backJumpDir = inVelocity.GetSafeNormal2D();
		const float hackPlayerSprintMax = 650;
		const float targetSpeedWhenMaxSprinting = 950;

		float maxStrength = targetSpeedWhenMaxSprinting - data.Core.Physics.SprintSpeedMax;
		// don't let this accelerate you
		float slowToSprintSpeedStrength = Math::Min( 0, hackPlayerSprintMax - speed2D );
		float kickBackStrengthToUse = Math::Max( slowToSprintSpeedStrength, maxStrength );

		FVector kickBackwardsImpulse = backJumpDir * kickBackStrengthToUse;
		
		// Upwards impulse
		float gravity 	= playerUser.MovementComponent.GetGravityZ() * -1;
		//time to apex assuming we're still moving upward
		float curVelZ 			= inVelocity.Z;
		float timetoApex 		= curVelZ / gravity;
		float apexHeightFromNow = ( curVelZ * timetoApex ) - (0.5 * gravity * Math::Square(timetoApex) );
		float ejectHeightTotal 	= desiredHeightHack + apexHeightFromNow;
		
		float desiredHeight 	= curVelZ > 0 ? Math::Max( 0, ejectHeightTotal ) : desiredHeightHack;
		float impulseZ			= Math::Sqrt( 2 * gravity * desiredHeight);

		FVector kickOffHeightImpulse = FVector( 0, 0, impulseZ - curVelZ );
		
		return kickOffHeightImpulse + kickBackwardsImpulse;
	}

	UFUNCTION()
	private void OnADSInput( ANCPlayerCharacter player )
	{
		local_isADSInputPressed = true;
		if ( IsServer() )
			sv_ClearActiveSprint();

		OnUpdateFreelook();
	}

	UFUNCTION()
	private void OnADSInputRelease( ANCPlayerCharacter player )
	{
		if(!local_isADSInputPressed)
			return;

		local_isADSInputPressed = false;
		local_freelookStopTimeMS = GetGameTimeMS();
		
		if ( net_resumeActiveSprintOnADSRelease )
			__OnActiveSprintInput(player);

		OnUpdateFreelook();
	}

	bool IsADSInputPressed()
	{
		return local_isADSInputPressed;
	}

	bool IsFreeLook()
	{
#if EDITOR		
		if ( GetCvarBool(f"VehicleMount.DebugLookAnims") )
			return true;
#endif

		return local_isADSInputPressed || local_holdThrow;
	}

	bool IsFreeLookReturn()
	{
#if EDITOR		
		if ( !GetCvarBool(f"VehicleMount.FreeLookReturn"))
			return false;
#endif

		float elapsedTime 	= TO_SECONDS( GetGameTimeMS() - local_freelookStopTimeMS );
		return elapsedTime < GetMountData().Core.Physics.FreelookReturnBlendTime;
	}

	access:internal void OnUpdateFreelook()
	{
		if ( !IsClient() )
			return;

		if ( ANCVehicle::UsesCodeMovement() )
			return;
		
		ANCPlayerCharacter pilot = GetPilot();
		if ( pilot != Client_GetLocalPawn() )
			return;

		FVehicleMountData data 	= GetMountData();

		const float32 LOCK_YAW_CAP = 179.9;	//not sure why it has to be this instead of 180... but 180 does bad things
		if ( IsFreeLook() )
			pilot.LockViewToVehicle( LOCK_YAW_CAP * -1, LOCK_YAW_CAP, data.Core.Physics.LockViewMinPitchDelta );
		else
			pilot.LockViewToVehicle( data.Core.Physics.LockViewMaxYawDelta * -1, data.Core.Physics.LockViewMaxYawDelta, data.Core.Physics.LockViewMinPitchDelta );
	}

/****************************************************************\

██    ██ ████████ ██ ██      ██ ████████ ██    ██ 
██    ██    ██    ██ ██      ██    ██     ██  ██  
██    ██    ██    ██ ██      ██    ██      ████   
██    ██    ██    ██ ██      ██    ██       ██    
 ██████     ██    ██ ███████ ██    ██       ██  
                                  
\****************************************************************/	
	private void SetSprinting( bool value )
	{
		ScriptAssert( IsServer(), "SERVER ONLY" );

		if ( value == net_isSprinting )
			return;
		
		net_isSprinting.SetNetValue( value );
		if ( value == false )
			sv_ClearActiveSprint();
	}

	UFUNCTION(BlueprintPure)
	bool IsSprinting()
	{
		return net_isSprinting;
	}

	bool HasBoost()
	{
		AAS_PlayerEntity pilot = Cast<AAS_PlayerEntity>(GetPilot());
		if ( !IsValid(pilot) )
			return false;

		return pilot.GetBoostManager().HasBoost();
	}

	bool HasActiveSprint()
	{
		return net_ActiveSprintStartTimeMS > 0;
	}
	
	float32 GetActiveSprintTimeLeftAsFraction()
	{
		if ( net_ActiveSprintStartTimeMS == 0 )
			return 0;
		
		FVehicleMountData data = GetMountData();
		
		float32 elapsedTime = TO_SECONDS(GetGameTimeMS() - net_ActiveSprintStartTimeMS);
		float32 timeLeft 	= data.Core.Physics.ActiveSprintDuration - elapsedTime;

		if ( timeLeft <= 0 )
			return 0.0f;

		return timeLeft / data.Core.Physics.ActiveSprintDuration;
	}

	float32 GetBoostSpeedAsFraction()
	{
		FVehicleMountData data = GetMountData();

		float speedBuffer 	= 10;
		float curSpeed 		= GetSpeed();
		float boostFrac 	= Math::GetMappedRangeValueClamped( FVector2D(data.Core.Physics.SprintSpeedMax + speedBuffer, data.Core.Physics.BoostSpeedMax - speedBuffer), FVector2D( 0.0, 1.0), curSpeed );

		return float32(boostFrac);
	}

	float32 GetSprintSpeedAsFraction()
	{
		FVehicleMountData data 	= GetMountData();
		float curSpeed 			= GetSpeed();
		float frac 				= Math::GetMappedRangeValueClamped( FVector2D( data.Core.Physics.WalkSpeedMax, data.Core.Physics.SprintSpeedMax ), FVector2D( 0, 1 ), curSpeed );

		return float32( frac );
	}

	bool HasAttackedRecently()
	{
		ANCPlayerCharacter pilot = GetPilot();
		if ( !IsValid( pilot ) )
		{
			return false;
		}

		FVehicleMountData data 	= GetMountData();
		const float elapsedSinceLastAttack = TO_SECONDS( pilot.GetTimeMilliseconds() - local_lastAttackTimeMS );
		return elapsedSinceLastAttack <= data.Core.Gameplay.ShootingSlowdownPenaltyTime;
	}

	UFUNCTION(BlueprintPure)
	bool IsFalling()
	{		
		AAS_VehicleMount ownerMount = GetOwnerMount();
		
		if ( IsValid(ownerMount) )
			return ownerMount.GetVehicleMovementMode() == ENCVehicleMovementMode::Falling;

		return false;
	}	

	ANCPlayerCharacter GetPilot()
	{
		AAS_VehicleMount ownerMount = GetOwnerMount();
		if ( IsValid(ownerMount) )
			return ownerMount.GetPilot();
		return nullptr;
	}

	UFUNCTION(BlueprintPure)
	float GetSpeed()
	{
#if EDITOR	
		if ( GetCvarBool(f"VehicleMount.DebugLookAnims") )
		{
			if ( IsClient() && IsValid(GetPilot()) && Client_GetLocalPawn() == GetPilot() )
				return debug_lookAnimSpeed;
		}
#endif

		return GetOwnerMount().GetVelocity().Size2D();
	}

	//can go negative
	UFUNCTION(BlueprintPure)
	float GetLocomotionSpeed()
	{
#if EDITOR	
		if ( GetCvarBool(f"VehicleMount.DebugLookAnims") )
		{
			if ( IsClient() && IsValid(GetPilot()) && Client_GetLocalPawn() == GetPilot() )
				return debug_lookAnimSpeed;
		}
#endif		

		AAS_VehicleMount ownerMount = GetOwnerMount();

		FVector vel 	= ownerMount.GetVelocity();
		float speed 	= vel.Size2D();
		float dirDotVel = vel.DotProduct(ownerMount.GetActorForwardVector());

		float viewmodelSpeed = dirDotVel > 0 ? speed : speed * -1;
		return viewmodelSpeed;
	}

	UFUNCTION(BlueprintPure)
	float GetLocomotionTime()
	{
		if ( IsValid(GetPilot()))
			return GetPilot().GetLocomotionTime(); //I HAVE NO IDEA WHAT THIS IS

		return 0;	
	}

	UFUNCTION(BlueprintPure)
	FRotator GetPilotLookDelta()
	{
		FRotator lookDelta;
		if ( !IsValid(GetPilot()) )
			return lookDelta;

		FRotator viewRot 	= GetPilot().GetViewRotation();
		float mountYaw 		= GetOwnerMount().GetActorRotation().Yaw;
		float mountRoll 	= GetAnimRoll();

		float deltaYaw 		= Math::FindDeltaAngleDegrees(mountYaw, viewRot.Yaw);
		
		FVector viewF2D		= FRotator( 0, viewRot.Yaw, 0 ).GetForwardVector();
		FVector mountR2D 	= FRotator( 0, mountYaw, 0 ).GetRightVector();
		float deltaDot 		= viewF2D.DotProduct( mountR2D );
		float pitchAlpha 	= Math::Abs( deltaDot );

		float adjustRoll 	= ( mountRoll * pitchAlpha ) * Math::SignNeverZero( deltaDot ) * -1;
		float deltaPitch 	= Math::FindDeltaAngleDegrees(adjustRoll, viewRot.Pitch);

		lookDelta.Yaw 		= deltaYaw;
		lookDelta.Pitch 	= deltaPitch;

		return lookDelta;
	}

	int GetEmbarkTimeMS()
	{
		return local_embarkTimeMS;
	}

/****************************************************************\

██████  ███████ ██████  ██    ██  ██████  
██   ██ ██      ██   ██ ██    ██ ██       
██   ██ █████   ██████  ██    ██ ██   ███ 
██   ██ ██      ██   ██ ██    ██ ██    ██ 
██████  ███████ ██████   ██████   ██████ 
                                  
\****************************************************************/	
	private float debug_lookAnimSpeed;
	private float debug_lookAnimRoll;
	private bool debug_lookAnimHiddenModels;
	private void DebugLookAnims( float deltaTime )
	{
		if ( !IsValid(GetPilot()))
			return;
		
		if ( !GetCvarBool(f"VehicleMount.DebugLookAnims") )
		{
			if ( !IsClient() || !UNCUtils::InPrediction(GetPilot()))
			{
				if ( debug_lookAnimHiddenModels )
				{
					GetPilot().GetFirstPersonWeaponComponent().SetHiddenInGame(false);	
					GetPilot().GetViewmodelArms().SetHiddenInGame(false);	
					debug_lookAnimHiddenModels = false;
				}
			}
			return;
		}
		
		AAS_VehicleMount ownerMount = GetOwnerMount();

		FRotator curRot 	= ownerMount.GetActorRotation();
		debug_lookAnimRoll 	= 0;//-45;
		ownerMount.SetActorRotation( FRotator(0, curRot.Yaw, 0));
		SetAnimRoll(debug_lookAnimRoll);

		if ( !IsClient() || !UNCUtils::InPrediction(GetPilot()))
			return;

		GetPilot().GetFirstPersonWeaponComponent().SetHiddenInGame(true);	
		GetPilot().GetViewmodelArms().SetHiddenInGame(true);	
		debug_lookAnimHiddenModels = true;

		FVehicleMountData data 		= GetMountData();
		FVector inputVec 	= GetPilot().GetInputAxisMove();
		debug_lookAnimSpeed += inputVec.X * deltaTime * data.Core.Physics.WalkAcceleration;
		debug_lookAnimSpeed = Math::Min( debug_lookAnimSpeed, localData.BoostSpeedMax );
		debug_lookAnimSpeed = Math::Max( debug_lookAnimSpeed, -data.Core.Physics.ReverseSpeedMax );
		debug_lookAnimSpeed = localData.BoostSpeedMax;

		float debugtime 		= 0.05;
		FVector debugPos 		= GetPilot().GetPlayerMesh3P().GetSocketLocation(n"neck");//OwnerMount.GetActorLocation();

		FVector viewDir 		= GetPilot().GetViewRotation().GetForwardVector() * 400;
		//FRotator rot = GetOwnerMount().GetActorForwardVector().Rotation();
		//rot.Pitch = -50;
		//rot.Yaw += -180;
		//FVector viewDir 		= rot.GetForwardVector() * 400;
		FLinearColor viewColor 	= FLinearColor::LucBlue;
		DrawDebugLine( debugPos, debugPos+viewDir, debugtime, viewColor, 2 );

		FVector stringPos 		= debugPos + viewDir + FVector(0,0,100);
		FRotator deltaRot 		= GetPilotLookDelta();
		FString debugString 	= f"Yaw: {deltaRot.Yaw} | Pitch {deltaRot.Pitch}";
		DrawDebugString( stringPos, debugString, debugtime, viewColor );		

		Print( debugString, debugtime);
	}

	private void DevPrintGoalDir( FVehicleMountMotionModelCalc calc, FVector inputDir2D, FVector goalDir2D, FVector finalDir2D, float curYaw, float deltaYaw )	
	{
		float debugtime 		= 0.04;
		FVector debugPos 		= GetOwnerMount().GetActorLocation();
		FVector viewDir 		= IsValid(GetPilot()) ? GetPilot().GetViewRotation().GetForwardVector() * 600 : FVector::ZeroVector;
		FVector stringPos 		= debugPos + viewDir + FVector(0,0,100);

		if ( IsValid(GetPilot()) )
		{
			float aimYaw 			= GetPilotAimYaw( GetPilot(), IsFreeLook(), IsFreeLookReturn() );
			FVector aimDir 			= FRotator( 0,aimYaw,0).GetForwardVector() * 400;
			FLinearColor aimColor 	= FLinearColor::LucBlue;
			DrawDebugLine( debugPos, debugPos+aimDir, debugtime, aimColor, 9 );
			DrawDebugString( stringPos, f"Aim Dir", debugtime, aimColor );
		}
		
		FVector curDir 			= FRotator( 0,curYaw,0).GetForwardVector() * 600;
		FLinearColor curColor 	= FLinearColor::Yellow;
		DrawDebugLine( debugPos, debugPos+curDir, debugtime, curColor, 7 );

		FVector newDir 			= FRotator(0,calc.finalYaw,0).GetForwardVector() * 800;	
		FLinearColor newColor 	= FLinearColor(1,0.5,0);
		DrawDebugLine( debugPos, debugPos+newDir, debugtime, FLinearColor(1,0.5,0), 5 );

		FVector goaldir 		= goalDir2D * 950;
		FLinearColor goalColor 	= FLinearColor::Red;
		DrawDebugLine( debugPos, debugPos+goaldir, debugtime, goalColor, 3 );

		FVector velDir 			= finalDir2D * 1100;	
		FLinearColor velColor 	= FLinearColor::Green;
		DrawDebugLine( debugPos, debugPos+velDir, debugtime, velColor, 2 );

		FVector inputDir 		= inputDir2D * 1250;	
		FLinearColor inputColor = FLinearColor::Purple;
		DrawDebugLine( debugPos, debugPos+inputDir, debugtime, inputColor, 1 );

		
		DrawDebugString( stringPos, f"\nCur Dir", debugtime, curColor );
		DrawDebugString( stringPos, f"\n\nNew Dir", debugtime, newColor );
		DrawDebugString( stringPos, f"\n\n\nGoal Dir | delta yaw[{deltaYaw}]", debugtime, goalColor );
		DrawDebugString( stringPos, f"\n\n\n\nVel Dir", debugtime, velColor );
		DrawDebugString( stringPos, f"\n\n\n\n\nInput Dir", debugtime, inputColor );
	}

	private void DebugLean()
	{
		if ( !(IsClient() && IsValid(GetPilot()) && UNCUtils::InPrediction(GetPilot())) )
			return;

		float debugtime 		= 0.04;
		FVector debugPos 		= GetOwnerMount().GetActorLocation();
		FVector viewDir 		= GetPilot().GetViewRotation().GetForwardVector().GetSafeNormal2D() * 1000;
		debugPos 				= debugPos + viewDir;
		FVector stringPos 		= debugPos + FVector(0,0,100);
				
		FVector curDir 			= GetPilot().GetViewRotation().GetForwardVector().GetSafeNormal2D() * 600;
		FLinearColor curColor 	= FLinearColor::Yellow;
		DrawDebugLine( debugPos, debugPos+curDir, debugtime, curColor, 3 );

		FVector upDir 			= FVector::UpVector * 800;	
		FLinearColor upColor 	= FLinearColor::LucBlue;
		DrawDebugLine( debugPos, debugPos+upDir, debugtime, upColor, 3 );

		FVector leanDir			= FRotator(0, GetPilot().GetViewRotation().Yaw, GetAnimRoll() ).GetUpVector() * 800;	
		FLinearColor leanColor 	= FLinearColor::Green;
		DrawDebugLine( debugPos, debugPos+leanDir, debugtime, leanColor, 5 );

		DrawDebugString( stringPos, f"Cur Dir", debugtime, curColor );
		//DrawDebugString( stringPos, f"\nLean Dir | animRoll[{GetAnimRoll()}] | leanAlpha[{GetAnimLean3PAlpha(GetSpeed())}]", debugtime, leanColor );
		DrawDebugString( stringPos, f"\nLean Dir | animRoll[{GetAnimRoll()}]", debugtime, leanColor );
	}

	int devTimeWalkMS = 0;
	int devTimeSprintMS = 0;
	int devTimeBoostMS = 0;
	private void DevPrintTimeToMaxSpeeds( FVehicleMountMotionModelCalc calc)
	{
		FVehicleMountData data 		= GetMountData();

		if(IsServer())
		{
			if ( calc.finalSpeed == 0 )
			{
				devTimeWalkMS 	= 0;
				devTimeSprintMS = 0;
			}
			if ( devTimeWalkMS == 0 && calc.finalSpeed > 0 ) 
				devTimeWalkMS 	= GetGameTimeMS(); 

			if ( devTimeSprintMS == 0 && calc.finalSpeed > 0 ) 	
				devTimeSprintMS = GetGameTimeMS();

			if ( calc.finalSpeed < localData.SprintSpeedMax)
				devTimeBoostMS 	= -1;
			if ( calc.finalSpeed > localData.SprintSpeedMax && devTimeBoostMS < 0 ) 
				devTimeBoostMS = GetGameTimeMS();

			if ( calc.finalSpeed >= data.Core.Physics.WalkSpeedMax - 1.0 && devTimeWalkMS > 0 )
			{
				Print( f"(s) to Walk: {TO_SECONDS(GetGameTimeMS() - devTimeWalkMS)}");
				devTimeWalkMS = -1;
			}
			if ( calc.finalSpeed >= localData.SprintSpeedMax - 1.0 && devTimeSprintMS > 0 )
			{
				Print( f"(s) to Sprint: {TO_SECONDS(GetGameTimeMS() - devTimeSprintMS)}");
				devTimeSprintMS = -1;
			}
			if ( calc.finalSpeed >= localData.BoostSpeedMax - 1.0 && devTimeBoostMS > 0 )
			{
				Print( f"(s) to Boost: {TO_SECONDS(GetGameTimeMS() - devTimeBoostMS)}");
				devTimeBoostMS = 0;
			}
		}
	}

	int devTimeDriftMS = -1;
	FVector devDriftPos;
	private void DevPrintDriftDistance( bool isDrifting, float driftAlpha, float driftDecel )
	{
		AAS_VehicleMount ownerMount = GetOwnerMount();
		FVehicleMountData data 		= GetMountData();

		if(IsServer())
		{
			if ( isDrifting && devTimeDriftMS < 0 )
			{
				//Print( f"dragToStop: {dragToStop}");
				devTimeDriftMS = GetGameTimeMS();
				devDriftPos = ownerMount.GetActorLocation();
				
			}
			if ( isDrifting )
			{
				Print( f"driftAlpha: {driftAlpha} | driftDecel: {driftDecel}", 10);
			}
			if ( !isDrifting && devTimeDriftMS > 0 )
			{
				float dist = Distance2D( ownerMount.GetActorLocation(), devDriftPos );
				Print( f"Drift TIME: {TO_SECONDS(GetGameTimeMS()-devTimeDriftMS)}	| Drift DIST: {dist}", 10);
				devTimeDriftMS = -1;
			}
		}
	}

	TArray<FVector> devPos;
	int devPosMS = 0;
	private void DebugTurnRadius()
	{
		if( !IsClient() )
			return;

		while ( devPos.Num() >= 300 )
			devPos.RemoveAt(0);

		FVector newPos = GetOwnerMount().GetActorLocation();
		if ( devPos.Num() > 0 )
			DrawDebugLine( devPos[devPos.Num() - 1], newPos, 6.0 );
		
		float elapsedTime = TO_SECONDS(GetGameTimeMS() - devPosMS);
		if ( devPos.Num() > 50 && elapsedTime >= 1.0 )
		{
			int index 	= 0;
			float dist 	= 0;

			for( int i = 0; i < devPos.Num(); i+=3 )
			{
				float testDist = Distance2D( devPos[i], newPos);
				if( testDist > dist )
				{
					dist = testDist;
					index = i;
				}
			}
			devPosMS = GetGameTimeMS();

			DrawDebugLine( devPos[index], newPos, 0.5, FLinearColor::Red );
			Print( f"TURN Radius: {dist}", 1.1);
		}

		devPos.Add(newPos);
	}

	UFUNCTION()
	private void DebugSlide( UNCCoroutine co, ANCPlayerCharacter oldPilot )
	{
		co.EndOnDestroyed(oldPilot);

		int startSlideMS 			= GetGameTimeMS() + TO_MILLISECONDS(5.0);
		FVector oldDebugSlidePos 	= FVector::ZeroVector;
		bool startedSlide 			= false;
		FVector startDebugSlidePos;
		float startDebugSlideSpeed;
				
		while(true)
		{
			float elapsedTime = TO_SECONDS( GetGameTimeMS() - startSlideMS );
			if ( elapsedTime > 6 )
				return;

			if ( !startedSlide && oldPilot.IsSliding() )
			{
				startedSlide 			= true;
				startSlideMS 			= GetGameTimeMS();
				startDebugSlidePos 		= oldPilot.GetActorLocation();
				startDebugSlideSpeed 	= oldPilot.GetVelocity().Size2D();
			}
			
			FLinearColor color 	= oldPilot.IsSliding() ? FLinearColor::LucBlue : FLinearColor::Red;
			FVector newPos 		= oldPilot.GetActorLocation();	
			if ( oldDebugSlidePos != FVector::ZeroVector )
				DrawDebugLine( oldDebugSlidePos, newPos, 12.0, color );
			
			oldDebugSlidePos 	= newPos;

			if ( startedSlide && !oldPilot.IsSliding() )
			{
				float32 totalDist = Distance2D( startDebugSlidePos, newPos );
				Print( f"Total Slide Time: {elapsedTime} | Dist: {totalDist} | Initial Speed: {startDebugSlideSpeed}");
				return;
			}

			co.Wait( 0.01 );
		}
	}

	FVector oldDebugJumpPos;
	FVector startDebugJumpPos;
	FVector debugJumpApexPos;
	FVector debugJumpLaunchPos;
	bool debugJumpEject;
	bool debugJumpAtApex;
	const float DEBUG_JUMP_THICK = 3;
	const FLinearColor DEBUG_JUMP_COLOR_MOUNT 	= FLinearColor::LucBlue;
	const FLinearColor DEBUG_JUMP_COLOR_PLAYER 	= FLinearColor::Red;

	UFUNCTION()
	private void DebugJumpThread( UNCCoroutine co, ANCPlayerCharacter oldPilot, FVector initialJumpVel )
	{
		co.EndOnDestroyed(oldPilot);
		
		oldDebugJumpPos 		= GetDebugJumpLoc(oldPilot);
		startDebugJumpPos 		= oldDebugJumpPos;
		debugJumpLaunchPos 		= startDebugJumpPos;
		debugJumpEject 			= false;
		debugJumpAtApex 		= false;
		AAS_VehicleMount mount 	= Mount::GetPilotedMount( oldPilot );
		FString text 			= FString( f"Speed: {Math::RoundToInt(mount.motionModel.GetSpeed()) } \nJump Z: {Math::RoundToInt(oldDebugJumpPos.Z)} \nJump Vel: {Math::RoundToInt(initialJumpVel.Z)}");
		
		DrawDebugString( oldDebugJumpPos, text, GetDebugJumpDuration(), DEBUG_JUMP_COLOR_MOUNT );
		
		while( true )
		{
			bool playerJump = oldPilot.GetMovementComponent().IsFalling();
			bool mountJump 	= IsValid(mount) && mount.IsFalling();

			FLinearColor color 	= debugJumpEject ? DEBUG_JUMP_COLOR_PLAYER : DEBUG_JUMP_COLOR_MOUNT;
			FVector newPos 		= GetDebugJumpLoc(oldPilot);

			if ( !playerJump && !mountJump )
			{
				FVector landStartPos 	= startDebugJumpPos;
				landStartPos.Z 			= debugJumpApexPos.Z;
				FVector landEndPos 		= newPos;
				landEndPos.Z 			= debugJumpApexPos.Z;
				FVector offset 			= FVector(0,0,10);
				FVector dir 			= oldDebugJumpPos - landStartPos;
				FString textTD 			= FString( f"Total Dist: {dir.Size()}");
				dir.Normalize();

				DrawDebugString( landEndPos, textTD, GetDebugJumpDuration(), color );
				DrawDebugLine( landEndPos + offset, landEndPos - offset, GetDebugJumpDuration(), color, DEBUG_JUMP_THICK );
				DrawDebugLine( landStartPos + offset, landStartPos - offset, GetDebugJumpDuration(), color, DEBUG_JUMP_THICK );
				DrawDebugLine( landStartPos + offset, landEndPos + offset, GetDebugJumpDuration(), color, DEBUG_JUMP_THICK );
								
				return;
			}
			
			if ( oldDebugJumpPos != FVector::ZeroVector )
				DrawDebugLine( oldDebugJumpPos, newPos, GetDebugJumpDuration(), color, DEBUG_JUMP_THICK );

			if ( newPos.Z < oldDebugJumpPos.Z && newPos != debugJumpLaunchPos && !debugJumpAtApex)
			{
				debugJumpAtApex 	= true;
				float height 		= oldDebugJumpPos.Z-debugJumpLaunchPos.Z;
				FString textH		= FString( f"Apex Height: {Math::RoundToInt(height) }\nApex Dist: {Distance2D(oldDebugJumpPos,debugJumpLaunchPos)}");
				FVector botPos 		= oldDebugJumpPos + FVector(0,0,-height);
				FVector apexStrtPos = debugJumpLaunchPos;
				apexStrtPos.Z 		= botPos.Z;
				debugJumpApexPos 	= oldDebugJumpPos;
				DrawDebugString( oldDebugJumpPos, textH, GetDebugJumpDuration(), color );
				DrawDebugLine( oldDebugJumpPos, botPos, GetDebugJumpDuration(), color, DEBUG_JUMP_THICK );
				DrawDebugLine( botPos, apexStrtPos, GetDebugJumpDuration(), color, DEBUG_JUMP_THICK );
			}
			
			oldDebugJumpPos 	= newPos;

			co.Wait( 0.01 );
		}
	}

	private float GetDebugJumpDuration()
	{
		return Math::Max( 30, GetCvarFloat(f"VehicleMount.DebugJump") );
	}

	private FVector GetDebugJumpLoc(ANCPlayerCharacter oldPilot)
	{		 	
		AAS_VehicleMount mount = Mount::GetPilotedMount( oldPilot );

		float halfHeight = IsValid( mount ) ? Cast<UCapsuleComponent>(mount.GetComponentByClass( UCapsuleComponent::StaticClass() )).CapsuleHalfHeight : oldPilot.CapsuleComponent.CapsuleHalfHeight;
		return (IsValid( mount ) ? mount.GetActorLocation() : oldPilot.GetActorLocation()) + FVector(0,0,-halfHeight);
	}
}


/****************************************************************\

██   ██  █████   ██████ ██   ██ ███████ 
██   ██ ██   ██ ██      ██  ██  ██      
███████ ███████ ██      █████   ███████ 
██   ██ ██   ██ ██      ██  ██       ██ 
██   ██ ██   ██  ██████ ██   ██ ███████ 
                                  
\****************************************************************/	
const float32 __viewPitchMin 	= -75.0;
const float32 __viewPitchMax 	= 85.0;
const float32 __viewYawMin 		= 0.0;
const float32 __viewYawMax 		= 359.999;
mixin void LockViewToVehicle( ANCPlayerCharacter self, float32 localYawMin, float32 localYawMax, float32 localPitchMin = __viewPitchMin, float32 localPitchMax = __viewPitchMax, float32 lerpTime = 0.5 )
{
	ScriptAssert( IsClient(), "CLIENT ONLY" );
	ScriptAssert( IsValid(self), "self not valid" );

	Cast<AAS_PlayerEntity>(self).endSignalLockViewToVehicleHackThread.Emit();

	float32 viewPitchMin = Math::Max( __viewPitchMin, localPitchMin);
	float32 viewPitchMax = Math::Min( __viewPitchMax, localPitchMax);

	UAS_VehicleMountMotionModel_ThreadSystem system = UAS_VehicleMountMotionModel_ThreadSystem::Get();
	Thread( system, n"__LockViewToVehicleHackThread", self, localYawMin, localYawMax, viewPitchMin, viewPitchMax, lerpTime );
}

mixin void UnlockViewFromVehicle( ANCPlayerCharacter self )
{
	ScriptAssert( IsClient(), "CLIENT ONLY" );
	ScriptAssert( IsValid(self), "self not valid" );

	ANCPlayerController controller = Client_GetLocalPlayerController();
	if ( !IsValid(controller ) )
		return;

	ANCPlayerCameraManager camManager = controller.GetCameraManager();
	if ( !IsValid(camManager ) )
		return;

	camManager.ViewPitchMin 	= __viewPitchMin;
	camManager.ViewPitchMax 	= __viewPitchMax;	
	camManager.ViewYawMin		= __viewYawMin;
	camManager.ViewYawMax		= __viewYawMax;

	Cast<AAS_PlayerEntity>(self).endSignalLockViewToVehicleHackThread.Emit();
}

UCLASS()
class UAS_VehicleMountMotionModel_ThreadSystem : UScriptWorldSubsystem
{
	//needs to live outside the class so it can still work when the class is destroyed
	UFUNCTION()
	void __HACK_GuaranteeSlideOnDismount( UNCCoroutine co, ANCPlayerCharacter oldPilot, AAS_VehicleMount oldMount, FVector initialVel )
	{		
		co.EndOnDestroyed(oldPilot);	

		int startSlideCheckMS 		= GetGameTimeMS();
		const float holdInputbuffer = 0.01;
			
		while(true)
		{
			float elapsedTime = TO_SECONDS( GetGameTimeMS() - startSlideCheckMS );
			if ( !IsValid(oldMount) || elapsedTime > oldMount.motionModel.CROUCH_EXIT_HOLD_TIME + holdInputbuffer )
			{
				oldPilot.EnableCrouch();
				return;
			}

			if ( oldMount.motionModel.GetWasSlidePressed() )
			{			
				oldPilot.EnableCrouch();

				if ( IsServer() )
				{
					FVehicleMountData data = oldMount.GetMountData();

					FVector slideBoost 		= initialVel.GetSafeNormal2D() * data.Core.Physics.slide2DBoost;
					FVector slideVelocity	= initialVel + slideBoost;

					if ( !oldPilot.IsSliding() ) 
						oldPilot.ServerForceStartSlide();
					oldPilot.SetMovementComponentVelocity( slideVelocity );
				}
			}

			co.Wait( 0.01 );
		}
	}

	UFUNCTION()
	void __LockViewToVehicleHackThread( UNCCoroutine co, ANCPlayerCharacter self, float32 localYawMin, float32 localYawMax, float32 localPitchMin, float32 localPitchMax, float32 lerpTime )
	{
		AAS_PlayerEntity asPlayer = Cast<AAS_PlayerEntity>(self);
		
		co.EndOn( asPlayer, asPlayer.endSignalLockViewToVehicleHackThread );
		co.EndOnDestroyed(self);
		
		ANCVehicle vehicle 		= Mount::GetPilotedMount(self);
		if ( !IsValid(vehicle) )
		{
			self.UnlockViewFromVehicle();
			return;
		}

		float vehYaw			= vehicle.GetActorRotation().Yaw;
		float pilotViewYaw 		= self.GetViewRotation().Yaw;
		float deltaCur2ViewYaw 	= Math::FindDeltaAngleDegrees(vehYaw, pilotViewYaw);

		float rampDeltaMin 		= deltaCur2ViewYaw-localYawMin;
		float lockedYawMin		= localYawMin + rampDeltaMin;
		float startYawMin 		= Math::Min( localYawMin, lockedYawMin );
		
		float rampDeltaMax 		= deltaCur2ViewYaw-localYawMax;
		float lockedYawMax		= localYawMax + rampDeltaMax;
		float startYawMax 		= Math::Max( localYawMax, lockedYawMax );		

		int startTimeMS = GetGameTimeMS();
		while( true )
		{
			co.Wait( 0.016 );

			ANCPlayerController controller = Client_GetLocalPlayerController();// Cast<ANCPlayerController>(Gameplay::GetPlayerController(self.GetPlayerIndex()));
			if ( !IsValid(controller ) )
				continue;

			ANCPlayerCameraManager camManager = controller.GetCameraManager();
			if ( !IsValid(camManager ) )
				continue;

			//pitch can set on the world... yaw needs to be updated in local space every frame	
			camManager.ViewPitchMin 	= localPitchMin;
			camManager.ViewPitchMax 	= localPitchMax;

			vehicle = Mount::GetPilotedMount(self);
			if ( !IsValid(vehicle) )
			{
				self.UnlockViewFromVehicle();
				return;
			}

			float32 elapsedTime 	= TO_SECONDS( GetGameTimeMS() - startTimeMS);
			float alpha 			= Math::GetMappedRangeValueClamped( FVector2D(0,lerpTime), FVector2D(0,1), elapsedTime);
			float lerpYawMin 		= Math::Lerp( startYawMin, localYawMin, alpha );
			float lerpYawMax 		= Math::Lerp( startYawMax, localYawMax, alpha );

			float curYaw			= vehicle.GetActorRotation().Yaw;
			float deltaYaw 			= Math::FindDeltaAngleDegrees(0, curYaw);
			float deltaMin 			= Math::FindDeltaAngleDegrees(localYawMin, lerpYawMin + deltaYaw);
			float deltaMax 			= Math::FindDeltaAngleDegrees(localYawMax, lerpYawMax + deltaYaw);

			float clampYawMin		= localYawMin + deltaMin;
			float clampYawMax		= localYawMax + deltaMax;
			camManager.ViewYawMin 	= clampYawMin;
			camManager.ViewYawMax 	= clampYawMax;
		}			
	}
}
