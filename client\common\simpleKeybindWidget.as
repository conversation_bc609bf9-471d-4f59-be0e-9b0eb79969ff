UCLASS( Abstract )
class UAS_SimpleKeybind : UAS_BaseKeybind
{
	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_CommonTextBlock keybind;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidgetOptional ) )
	private UImage holdIcon;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidgetOptional ) )
	private UImage dblTapIcon;

	UPROPERTY( EditInstanceOnly, BlueprintHidden )
	private bool holdButton = false;

	UPROPERTY( EditInstanceOnly, BlueprintHidden )
	private bool dblTapButton = false;

	UFUNCTION( BlueprintOverride )
	void PreConstruct( bool IsDesignTime )
	{
		SetWidgetVisibilitySafe( holdIcon, holdButton ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Hidden );
		SetWidgetVisibilitySafe( dblTapIcon, dblTapButton ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Hidden );
	}

	void SetHoldButton( bool newHoldButton )
	{
		if ( holdButton != newHoldButton )
		{
			holdButton = newHoldButton;
			Update();
		}
	}

	void SetDblTapButton( bool newDblTap )
	{
		if ( dblTapButton != newDblTap )
		{
			dblTapButton = newDblTap;
			Update();
		}
	}

	void OnUpdate( FText glyph ) override
	{
		Super::OnUpdate( glyph );
		keybind.SetText( glyph );
		SetWidgetVisibilitySafe( holdIcon, holdButton ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Hidden );
		SetWidgetVisibilitySafe( dblTapIcon, dblTapButton ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Hidden );
	}
}