UCLASS( Abstract )
class UAS_DisplayedWeaponCardWidget : UAS_WeaponCardWidget
{
	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UAS_KeybindWidget keybind;

	UPROPERTY( NotVisible, BlueprintHidden, Meta = ( BindWidget ) )
	private UTextBlock ammoText;

	UPROPERTY( NotVisible, BindWidget )
	private UHorizontalBox iconAndAmmoContainer;

	UPROPERTY( NotVisible, BlueprintHidden, Transient, Meta = ( BindWidgetAnim ) )
	private UWidgetAnimation anim_equipped;

	private bool isEquipped = false;

	UFUNCTION( BlueprintOverride )
	protected void Construct()
	{
		Super::Construct();

		SetWidgetVisibilitySafe(keybind,  ESlateVisibility::Collapsed );
		SetWidgetVisibilitySafe(ammoText,  ESlateVisibility::Collapsed );

		PlayAnimation( anim_equipped, 0, 1, EUMGSequencePlayMode::Forward, 0 );

		UCommonInputSubsystem inputSubsystem = GetInputSubsystem();
		if ( IsValid( inputSubsystem ) )
		{
			inputSubsystem.OnInputMethodChanged.AddUFunction( this, n"OnInputMethodChanged" );
		}
	}

		bool usingController	 = false;
	UFUNCTION( NotBlueprintCallable )
	private void OnInputMethodChanged( ECommonInputType bNewInputType )
	{
		ANCPlayerCharacter localPlayer = Client_GetLocalPawn();
		if ( !IsValid( localPlayer ) )
		{
			return;
		}

		ANCWeapon activeWeapon = localPlayer.GetActiveWeapon();
		if ( !IsValid( activeWeapon ) )
		{
			return;
		}

		APlayerController controller = GetOwningPlayer();
		if ( IsValid( controller ) )
		{
			UCommonInputSubsystem inputSubsystem = UCommonInputSubsystem::Get( controller.GetLocalPlayer() );
			if ( IsValid( inputSubsystem ) )
			{
				usingController = inputSubsystem.GetCurrentInputType() != ECommonInputType::MouseAndKeyboard;
			}
		}
		
		UpdateKeybind( localPlayer, activeWeapon );
	}

	protected void Update() override
	{
		if ( !IsValid( equippedWeapon ) )
			return;

		Super::Update();

		ANCPlayerCharacter localPlayer = Client_GetLocalPawn();
		if ( !IsValid( localPlayer ) )
		{
			return;
		}

		ANCWeapon activeWeapon = localPlayer.GetActiveWeapon();
		if ( !IsValid( activeWeapon ) )
		{
			return;
		}

		if ( activeWeapon == equippedWeapon && !isEquipped )
		{
			isEquipped = true;
			PlayAnimation( anim_equipped, 1, 1, EUMGSequencePlayMode::Forward, 0 );
		}
		else if ( activeWeapon != equippedWeapon && isEquipped )
		{
			isEquipped = false;
			PlayAnimation( anim_equipped, 0, 1, EUMGSequencePlayMode::Forward, 0 );
		}

		UpdateKeybind( localPlayer, activeWeapon );
		UpdateAmmoText();
	}

	private void UpdateKeybind( ANCPlayerCharacter localPlayer, ANCWeapon activeWeapon )
	{
		if ( !IsValid( localPlayer ) || !IsValid( activeWeapon ) || !IsValid( equippedWeapon ) )
			return;

		bool showRaidToolKeybind = IsRaidTool();
		bool hideKeybind		 = false;

		if ( isEquipped && usingController)
		{
			hideKeybind = true;
		}

		FName inputAction;
		FName inputActionKbm;
		bool holdButton = false;

		if ( showRaidToolKeybind )
		{
			inputAction	   = n"CycleGrenade";
			inputActionKbm = FName();
			holdButton	   = usingController;
		}
		else
		{
			int slot = equippedWeapon.GetWeaponSlot();
			inputAction	   = n"AltUseSwitchWeapon";
			inputActionKbm = slot == int(EWeaponSlot::PrimarySlot0) ? n"PrimaryWeapon0" : n"PrimaryWeapon1";
			holdButton	   = false;
		}

		keybind.SetInputAction( inputAction );
		keybind.SetInputActionKBM( inputActionKbm );
		keybind.SetHoldButton( holdButton );
		SetWidgetVisibilitySafe(keybind,  hideKeybind ? ESlateVisibility::Collapsed : ESlateVisibility::HitTestInvisible );
	}

	private void UpdateAmmoText()
	{
		if ( !IsValid( equippedWeapon ) )
			return;

		int clipAmmo	  = equippedWeapon.GetClipAmmo();
		int stockpileAmmo = equippedWeapon.GetStockpileAmmo();

		ammoText.SetText( FText::AsNumber( clipAmmo + stockpileAmmo, FNumberFormattingOptions::DefaultNoGrouping() ) );
	}

	protected void OnLocalWeaponClipAmmoChanged( ANCWeapon weapon, int oldValue, int newValue ) override
	{
		if ( weapon == equippedWeapon )
		{
			UpdateAmmoText();
		}
	}

	protected void OnLocalWeaponStockpileAmmoChanged( ANCWeapon weapon, int oldValue, int newValue ) override
	{
		if ( weapon == equippedWeapon )
		{
			UpdateAmmoText();
		}
	}

	protected void OnBackpackContentsChanged( UBackpackComponent newBackpack ) override
	{
		UpdateAmmoText();
	}

	protected void SetWeapon(ANCWeapon newWeapon) override
	{
		Super::SetWeapon(newWeapon);

		ESlateVisibility ammoTextVisibility = IsRaidTool() ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed;
		SetWidgetVisibilitySafe( ammoText, ammoTextVisibility );
	}

	protected void SetWeaponData(FWeaponData inWeaponData, FBackpackItemStruct inItemData) override
	{
		Super::SetWeaponData(inWeaponData, inItemData);
		
		ESlateVisibility ammoTextVisibility = IsRaidTool() ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Collapsed;
		SetWidgetVisibilitySafe( ammoText, ammoTextVisibility );
	}

	protected void EmptyWeaponCard( bool isEmpty ) override
	{
		Super::EmptyWeaponCard( isEmpty );

		SetWidgetVisibilitySafe( iconAndAmmoContainer, isEmpty ? ESlateVisibility::Collapsed : ESlateVisibility::SelfHitTestInvisible );
		SetWidgetVisibilitySafe( keybind, isEmpty ? ESlateVisibility::Collapsed : ESlateVisibility::HitTestInvisible );
	}
}