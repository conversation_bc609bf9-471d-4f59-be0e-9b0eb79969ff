UCLASS( NotBlueprintable )
class UAS_PriorityMessageBaseWidget : UNC_DisplayWidget
{
	default StateOnConstruction = EDisplayConstructVisibility::Hidden;

	UAS_PriorityMessageData messageData;

	private FTimerHandle countdownHandle;
	private int countdownStartMS;
	private float32 countdownLength;
	private bool countdownEnabled;
	private bool countdownIsVisible = false;
	private bool isDisappearing = false;
	private bool hasPriority = true;
	private bool ctaIsVisible = false;

	void Initialize( UAS_PriorityMessageData data )
	{
		messageData = data;
		Show();
	}

	UFUNCTION(BlueprintOverride)
	void OnHideStart()
	{
		isDisappearing = true;
	}

	UFUNCTION( BlueprintOverride )
	void OnHideEnd()
	{
		isDisappearing = false;
		RemoveFromParent();
		messageData.onDestroy.Broadcast( this );
	}

	bool IsMessageDisappearing()
	{
		return isDisappearing;
	}

	bool MessageHasPriority()
	{
		return hasPriority;
	}

	void DePrioritize()
	{
		hasPriority = false;
	}

	void RePrioritize()
	{
		hasPriority = true;
	}

	void StartCountdown_ByDuration( int inStartTimeMS, float duration, float delayShowAnim = 0.0 )
	{
		countdownLength	 = float32( duration );
		countdownStartMS = inStartTimeMS;
		countdownEnabled = true;

		System::ClearAndInvalidateTimerHandle( countdownHandle );

		CountdownTick();

		if ( delayShowAnim > 0 )
		{
			System::SetTimer( this, n"ShowCountDown", delayShowAnim, false );
		}
		else
		{
			ShowCountDown();
		}
	}

	void StartCountdown_ByEndTime( int inEndTimeMS, float delayShowAnim = 0.0 )
	{
		int startTimeMS	 = GetTimeMilliseconds();
		countdownLength	 = TO_SECONDS( inEndTimeMS - startTimeMS );
		countdownStartMS = startTimeMS;
		countdownEnabled = true;

		System::ClearAndInvalidateTimerHandle( countdownHandle );

		CountdownTick();

		if ( delayShowAnim > 0 )
		{
			System::SetTimer( this, n"ShowCountDown", delayShowAnim, false );
		}
		else
		{
			ShowCountDown();
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void CountdownTick()
	{
		int elapsedTimeMS	= GetTimeMilliseconds() - countdownStartMS;
		float32 elapsedTime = TO_SECONDS( elapsedTimeMS );
		float32 timeLeft	= Math::Max( countdownLength - elapsedTime, 0.0f );

		FNumberFormattingOptions options;
		options.SetMinMaxFractionalDigits( 0 );
		options.SetMinMaxIntegralDigits( 2 );
		options.SetRoundingMode( ERoundingMode::FromZero );

		if ( timeLeft > 0 )
		{
			if ( countdownEnabled )
			{
				countdownHandle = System::SetTimerForNextTick( this, "__CountdownTick" );
			}
		}
		else
		{
			countdownEnabled = false;
			messageData.onCountDownStop.Broadcast( this, EPriorityMessageCountdownStopReason::ENDED );
		}

		OnCountdownTick( countdownStartMS, countdownLength );
	}

	void OnCountdownTick( int startMs, float32 length )
	{
		// Intentionally empty
	}

	void StopCountdown()
	{
		System::ClearAndInvalidateTimerHandle( countdownHandle );
		if ( countdownEnabled )
		{
			countdownEnabled = false;
			messageData.onCountDownStop.Broadcast( this, EPriorityMessageCountdownStopReason::DISABLED );

			CountdownTick();
		}
	}

	UFUNCTION( NotBlueprintCallable )
	void ShowCountDown()
	{
		if ( countdownIsVisible )
			return;

		countdownIsVisible = true;
	}

	void HideCountdown()
	{
		if ( !countdownIsVisible )
			return;

		countdownIsVisible = false;
	}

	void StopAndHideCountdown()
	{
		StopCountdown();
		HideCountdown();
	}

	bool IsCountdownVisible() const
	{
		return countdownIsVisible;
	}

	void SetCTA( FText ctaText )
	{
	}

	void ShowCTA()
	{
		if ( ctaIsVisible )
			return;

		ctaIsVisible = true;
	}

	bool IsCtaVisible()
	{
		return ctaIsVisible;
	}

	void SetAndShowCTA( FText ctaText )
	{
		SetCTA( ctaText );
		ShowCTA();
	}

	void HideCTA()
	{
		if ( !ctaIsVisible )
			return;

		ctaIsVisible = false;
	}

	void SetHeader( FText header )
	{
		// Intentionally empty
	}

	void SetSubheader( FText subheader )
	{
		// Intentionally empty
	}

	// TODO @jmccarty: Remove this grossness
	FLinearColor GetCtaBackgroundColor()
	{
		return FLinearColor::Transparent;
	}
}