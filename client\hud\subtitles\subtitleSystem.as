event void FOnSubtitleEvent( UAS_DialogueQueueEntry entry );

UAS_SubtitleSystem GetSubtitleSystem()
{
	UAS_SubtitleSystem result = Cast<UAS_SubtitleSystem>( UNCGameplaySystemsSubsystem::Get_ClientSystem( GetCurrentWorld(), UAS_SubtitleSystem::StaticClass() ) );
	return result;
}

UCLASS( Abstract )
class UAS_SubtitleSystem : UNCGameplaySystem_Client
{
	FOnSubtitleEvent OnSubtitleAdded;
	FOnSubtitleEvent OnSubtitleRemoved;

	private TMap<UAS_DialogueQueueEntry, int> activeSubtitles;

	private const int MIN_DURATION = 3000;

	void AddDialogueEntry( UAS_DialogueQueueEntry entry )
	{
		// Ignore any future updates if subtitles aren't enabled
		if ( !GetCvarBool( "ScriptDebug.EnableSubtitles" ) )
			return;

		entry.onDialogueStarted.AddUFunction( this, n"OnDialogueStarted" );
		entry.onDialogueEnded.AddUFunction( this, n"OnDialogueEnded" );
	}

	UFUNCTION( NotBlueprintCallable )
	void OnDialogueStarted( UAS_DialogueQueueEntry entry )
	{
		if ( !IsValid( entry ) )
			return;

		// When dialogue starts, capture the timestamp and send out the event
		activeSubtitles.Add( entry, GetGameTimeMS() );
		OnSubtitleAdded.Broadcast( entry );
	}

	UFUNCTION( NotBlueprintCallable )
	void OnDialogueEnded( UAS_DialogueQueueEntry entry, EDialogueEndReason endReason )
	{
		if ( !IsValid( entry ) || !activeSubtitles.Contains( entry ) )
			return;

		int timestamp	   = activeSubtitles[entry];
		int activeDuration = GetGameTimeMS() - timestamp;
		Thread( this, n"WaitForDuration", entry, activeDuration );
	}

	UFUNCTION( NotBlueprintCallable )
	private void WaitForDuration( UNCCoroutine co, UAS_DialogueQueueEntry entry, int activeDuration )
	{
		// When dialogue ends, we may need to keep the subtitle around a bit longer if it was really short
		co.Wait( TO_SECONDS( Math::Max( MIN_DURATION - activeDuration, 0 ) ) );
		RemoveDialogueEntry( entry );
	}

	private void RemoveDialogueEntry( UAS_DialogueQueueEntry entry )
	{
		if ( !IsValid( entry ) )
			return;

		activeSubtitles.Remove( entry );
		OnSubtitleRemoved.Broadcast( entry );

		// Unbind the events for the entry
		entry.onDialogueStarted.Unbind( this, n"OnDialogueStarted" );
		entry.onDialogueEnded.Unbind( this, n"OnDialogueEnded" );
	}
}