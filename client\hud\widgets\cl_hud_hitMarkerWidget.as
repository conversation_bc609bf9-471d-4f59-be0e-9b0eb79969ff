UCLASS( Abstract )
class UAS_HitMarkerWidget : UUserWidgetDefault
{
	UPROPERTY( BlueprintReadOnly, BindWidget )
	UAS_DamageStatusIndicator damageStatusIndicator;

	UPROPERTY( BlueprintReadOnly, BindWidget )
	UImage shieldBreakIndicator;

	UPROPERTY( BlueprintReadOnly, BindWidget )
	UImage hitmarker;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	UWidgetAnimation onDamagedAnimation;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	UWidgetAnimation onDamagedWithSlowWeaponAnimation;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	UWidgetAnimation onKilledAnimation;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	UWidgetAnimation onHeadshotAnimation;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	UWidgetAnimation onIneffectiveDamageAnimation;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	UWidgetAnimation onAbilityDamageAnimation;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	UWidgetAnimation onKilledMountAnimation;

	UPROPERTY( BlueprintReadOnly, Meta = ( BindWidgetAnim ), Transient )
	UWidgetAnimation onShieldBreakAnimation;

	private UMaterialInstanceDynamic hitmarkerDynMat;
	private UMaterialInstanceDynamic shieldBreakDynMat;

	UFUNCTION(BlueprintOverride)
	void Construct()
	{
		hitmarkerDynMat = hitmarker.GetDynamicMaterial();
		shieldBreakDynMat = shieldBreakIndicator.GetDynamicMaterial();
	}

	UFUNCTION( BlueprintEvent )
	void PlayDamageDealtEffect( const FCachedDealtDamageData damageData )
	{
		const AActor victim = damageData.victim;
		const int scriptDamageFlags = damageData.scriptDamageFlags;
		const int damageFlags = damageData.damageFlags;

		damageStatusIndicator.SetDamageInfo( damageData );

		if ( Bitflags::HasFlag( scriptDamageFlags, EScriptDamageFlags::DF_HIDDEN_DAMAGE ) )
		{
			return;
		}

		const bool victimIsMount = IsValid( victim ) && victim.IsA( ANCVehicle::StaticClass() );

		const bool isKillshot = Bitflags::HasFlag( damageFlags, EDamageFlags::DF_KILLSHOT );
		const bool isCrit = Bitflags::HasFlag( damageFlags, EDamageFlags::DF_HEADSHOT );
		const bool isShieldBreak = Bitflags::HasFlag( damageFlags, EDamageFlags::DF_SHIELD_BREAK );

		bool victimHasShield = false;
		if ( IsValid( victim ) && ( victim.IsA( ANCPlayerCharacter::StaticClass() ) || victim.IsA( AAS_TargetDummy::StaticClass() ) )  )
		{
			// TODO: Once NC1-18943 is fixed, remove the damage subtraction
			UHealthComponent healthComp = victim.GetHealthComponent();
			if ( IsValid( healthComp ) )
			{
				victimHasShield = healthComp.GetShieldHealth() > 0 && healthComp.GetHealth() > 0;
				//victimHasShield = ( victimPlayer.GetShieldHealth() - damageData.damageAmount ) > 0 && victimPlayer.GetHealth() > 0;
				if ( victim.IsActorShieldValid() )
				{
					FGameplayTag shieldTag = victim.GetEquippedShieldData().gameplayTag;
					const FLootDataStruct& shieldLootData = GetLootDataByIndex( shieldTag );

					// 5 - 9 is common -> artifact
					int lootLevel = GetLootRarityLevel( shieldLootData );
					hitmarkerDynMat.SetScalarParameterValue( n"hitmarker_extra_color_index", 5 + lootLevel );
					shieldBreakDynMat.SetScalarParameterValue( n"color_index", 5 + lootLevel );
				}
			}
		}

		const bool victimIsDestructible = IsValid( victim ) && victim.IsA( ANCDestructible::StaticClass() );
		if ( victimIsDestructible )
		{
			const bool hasStructuralFlag = Bitflags::HasFlag( damageData.scriptDamageFlags, EScriptDamageFlags::DF_STRUCTURAL );
			if ( hasStructuralFlag )
			{
				hitmarkerDynMat.SetScalarParameterValue( n"hitmarker_extra_color_index", 5 );
			}
			// No hitmarker for damage to destructibles without a structural weapon
			else
			{
				return;
			}
		}

		// 1 means unset, the material uses this to know whether to show extra color or not
		float32 healthFracToUse = 1;

		// False = color on outside. True = color on inside.
		bool shouldFlipColorFill = false;
		if ( IsValid( victim ) )
		{
			const UHealthComponent healthComponent = Cast<UHealthComponent>( victim.GetComponentByClass( UHealthComponent::StaticClass() ) );
			if ( IsValid( healthComponent ) )
			{
				float32 outHealthFrac, outShieldFrac = 0;
				GetForecastedShieldAndHealth( healthComponent, damageData.damageAmount,outHealthFrac, outShieldFrac );

				if ( outShieldFrac > 0 || isShieldBreak )
				{
					shouldFlipColorFill = false;
					healthFracToUse = 0.5;
				}
				else
				{
					healthFracToUse = 1 - outHealthFrac;
					shouldFlipColorFill = true;
				}


				if ( !victimHasShield && !victimIsDestructible && !victimIsMount )
				{
					// Red
					//hitmarkerDynMat.SetScalarParameterValue( n"hitmarker_extra_color_index", 1 );

					// Grey
					hitmarkerDynMat.SetScalarParameterValue( n"hitmarker_extra_color_index", 5 );
				}
			}
		}

		if ( isKillshot )
		{
			shouldFlipColorFill = false;
			if ( victimIsMount )
			{
				PlayHitIndicatorAnimation( onKilledMountAnimation );
			}
			else
			{
				PlayHitIndicatorAnimation( onKilledAnimation );
			}
		}
		else if ( isCrit )
		{
			PlayHitIndicatorAnimation( onHeadshotAnimation );
			//shouldFlipColorFill = false;
			//healthFracToUse = 0.f;
		}
		else if ( Bitflags::HasFlag( damageFlags, EDamageFlags::DF_FALLOFFDAMAGE ) )
		{
			PlayHitIndicatorAnimation( onIneffectiveDamageAnimation );
		}
		else if ( Bitflags::HasFlag( scriptDamageFlags, EScriptDamageFlags::DF_DAMAGE_TO_ABILITY ) )
		{
			PlayHitIndicatorAnimation( onAbilityDamageAnimation );
		}
		else
		{
			// Hack - want a feature from code to do this reliably - The equipped weapon may not match what caused the damage :(
			bool playSlowerHitmarker = false;
			ANCPlayerCharacter localPlayer = Client_GetLocalPawn();
			if ( IsValid( localPlayer ) )
			{
				const ANCWeapon equippedWeapon = localPlayer.GetActiveWeapon();
				playSlowerHitmarker = IsSlowFiringWeapon( equippedWeapon );
			}

			if ( playSlowerHitmarker )
			{
				PlayHitIndicatorAnimation( onDamagedWithSlowWeaponAnimation );
			}
			else
			{
				PlayHitIndicatorAnimation( onDamagedAnimation );
			}
		}

		if ( isShieldBreak )
		{
			if ( victim != Client_GetLocalPawn() )
			{
				PlayAnimationForward( onShieldBreakAnimation, 2 );
			}
		}
		
		hitmarkerDynMat.SetScalarParameterValue( n"hitmarker_health_frac", healthFracToUse );

		// Lazy workaround. Reset base color. (Animations will override if they need to)
		hitmarkerDynMat.SetScalarParameterValue( n"hitmarker_flip_color_fill", shouldFlipColorFill ? 1 : 0 );


		// Default / crit are additive, this will cause them to change size based on whether there's a shield
		float baseRadius = 20;

		if ( victimIsDestructible )
		{
			baseRadius = 80;
		}

		hitmarkerDynMat.SetScalarParameterValue( n"hitmarker_radius_pixels", baseRadius );
		hitmarkerDynMat.SetScalarParameterValue( n"hitmarker_is_destructible_damage", victimIsDestructible ? 1 : 0 );
		hitmarkerDynMat.SetScalarParameterValue( n"hitmarker_is_kill", isKillshot ? 1 : 0 );
		hitmarkerDynMat.SetScalarParameterValue( n"hitmarker_is_crit", isCrit ? 1 : 0 );
		hitmarkerDynMat.SetScalarParameterValue( n"hitmarker_is_mount_damage", victimIsMount ? 1 : 0 );
		hitmarkerDynMat.SetScalarParameterValue( n"hitmarker_is_shield_damage", victimHasShield ? 1 : 0 );
		hitmarkerDynMat.SetScalarParameterValue( n"hitmarker_dist_between_splits", victimHasShield ? 4 : 0 );

	}

	UWidgetAnimation currentPlayingAnim;

	void PlayHitIndicatorAnimation( UWidgetAnimation animation )
	{
		if (IsValid(currentPlayingAnim))
			StopAnimation(currentPlayingAnim);

		currentPlayingAnim = animation;

		PlayAnimation( animation, 0.0, 1, EUMGSequencePlayMode::Forward, 1, true );
	}
}