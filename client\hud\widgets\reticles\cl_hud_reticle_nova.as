
UCLASS(Abstract)
class UAS_Reticle_Nova : UAS_ReticleWidget
{
	// This could be done through a TArray set up in BP construct... alas laziness
	
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta=(BindWidget) )
	UImage center;
	
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta=(BindWidget) )
	UImage pip_1;
	
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta=(BindWidget) )
	UImage pip_2;
	
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta=(BindWidget) )
	UImage pip_3;
	
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta=(BindWidget) )
	UImage pip_4;
	
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta=(BindWidget) )
	UImage pip_5;

	UPROPERTY( BlueprintReadOnly, NotEditable, Meta=(BindWidget) )
	UImage starLine_1;
	
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta=(BindWidget) )
	UImage starLine_2;
	
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta=(BindWidget) )
	UImage starLine_3;
	
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta=(BindWidget) )
	UImage starLine_4;
	
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta=(BindWidget) )
	UImage starLine_5;
	
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta=(BindWidget) )
	UImage holoBorder_1;
	
	UPROPERTY( BlueprintReadOnly, NotEditable, Meta=(BindWidget) )
	UImage holoBorder_2;

	UPROPERTY( EditDefaultsOnly )
	FLinearColor chokeReticleColor = FLinearColor( 1.0, 0.7, 0, 1 );
	
	void InitializeReticle(ANCWeapon weapon) override
	{
		Super::InitializeReticle(weapon);
		
		TArray<FName> activeMods;
		weapon.GetActiveMods( activeMods );
		if ( activeMods.Contains( n"ADS_Choke") )
		{
			FSlateBrush newBrush = GetNewBrushThatIsCopyOf( center.Brush );
			newBrush.TintColor = chokeReticleColor;

			center.SetBrush( newBrush );
			pip_1.SetBrush( newBrush );
			pip_2.SetBrush( newBrush );
			pip_3.SetBrush( newBrush );
			pip_4.SetBrush( newBrush );
			pip_5.SetBrush( newBrush );

			newBrush = GetNewBrushThatIsCopyOf( starLine_1.Brush );
			newBrush.TintColor = chokeReticleColor;
			starLine_1.SetBrush( newBrush );
			starLine_2.SetBrush( newBrush );
			starLine_3.SetBrush( newBrush );
			starLine_4.SetBrush( newBrush );
			starLine_5.SetBrush( newBrush );
			
			newBrush = GetNewBrushThatIsCopyOf( holoBorder_1.Brush );
			newBrush.TintColor = chokeReticleColor;
			newBrush.TintColor.SpecifiedColor.A = 0.05;
			holoBorder_1.SetBrush( newBrush );
			holoBorder_2.SetBrush( newBrush );
		}
	}
}