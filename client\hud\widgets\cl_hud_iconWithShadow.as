UCLASS(Abstract)
class UAS_IconWithShadow : UUserWidgetDefault
{
	UPROPERTY( NotVisible, Meta = ( BindWidget ) )
	protected UImage icon;
	
	UPROPERTY( NotVisible, Meta = ( BindWidget ) )
	protected UImage shadow;

	UPROPERTY( EditInstanceOnly, Category = "IconSettings" )
	FSlateBrush iconBrush;

	// This will modify the image size and render scale so that this widget takes up the desired size in your layout.
	// -1 in either X or Y indicates "use image size"
	// Cannot set a size override of 0
	UPROPERTY( EditInstanceOnly, Category = "IconSettings", Meta=(EditCondition="layoutSizeOverride", EditConditionHides) )
	FDeprecateSlateVector2D layoutSizeOverride;
	default layoutSizeOverride = iconBrush.ImageSize;

	UPROPERTY( EditInstanceOnly, Category = "IconSettings" )
	FDeprecateSlateVector2D desiredSize;
	default desiredSize = iconBrush.ImageSize;

	UPROPERTY( EditInstanceOnly, Category = "IconSettings" )
	bool doLayoutSizeOverride = false;

	UPROPERTY( EditInstanceOnly, Category = "IconSettings" )
	EHorizontalAlignment iconHorizontalAlignment;

	UPROPERTY( EditInstanceOnly, Category = "IconSettings" )
	EVerticalAlignment iconVerticalAlignment;

	UPROPERTY( EditInstanceOnly, Category = "ShadowSettings" )
	FLinearColor shadowColor;
	default shadowColor = GlobalHUD::ShadowColor;

	UPROPERTY( EditInstanceOnly, Category = "ShadowSettings" )
	FVector2D shadowOffset;
	default shadowOffset = FVector2D( 1, 1 );

	UPROPERTY( EditInstanceOnly, Category = "ShadowSettings" )
	FVector2D shadowScale;
	default shadowScale = FVector2D( 1, 1 );

	UPROPERTY( EditInstanceOnly, Category = "ShadowSettings" )
	bool shadowCounterRotates;
	default shadowCounterRotates = false;

	UPROPERTY( EditInstanceOnly, Category = "ShadowSettings", Meta=(EditCondition="shadowCounterRotates", EditConditionHides) )
	float widgetRenderAngle;
	default widgetRenderAngle = 0;

	UFUNCTION(BlueprintOverride)
	void PreConstruct(bool IsDesignTime)
	{
		FSlateBrush modifiedIconBrush = GetNewBrushThatIsCopyOf( iconBrush );

		if ( doLayoutSizeOverride )
		{
			modifiedIconBrush.ImageSize = layoutSizeOverride;
		}

		FVector2D imageSizeVec = modifiedIconBrush.ImageSize.ToVector2D();
		FVector2D desiredSizeVec = desiredSize.ToVector2D();
		FVector2D newRenderScale = FVector2D( desiredSizeVec.X / imageSizeVec.X, desiredSizeVec.Y / imageSizeVec.Y );
		icon.SetRenderScale( newRenderScale );

		icon.SetBrush( modifiedIconBrush );

		float xOffset, yOffset = 0;
		float signFlip = 1;
		switch( iconHorizontalAlignment )
		{
			case EHorizontalAlignment::HAlign_Left:
			{
				signFlip = -1;
			}
			fallthrough;

			case EHorizontalAlignment::HAlign_Right:
			{
				xOffset = desiredSizeVec.X * 0.5 - imageSizeVec.X * 0.5;
				xOffset *= signFlip;
				break;
			}
			default:
				xOffset = 0;
		}
		signFlip = 1;
		switch( iconVerticalAlignment )
		{
			case EVerticalAlignment::VAlign_Bottom:
			{
				signFlip = -1;
			}
			fallthrough;

			case EVerticalAlignment::VAlign_Top:
			{
				yOffset = desiredSizeVec.Y * 0.5 - imageSizeVec.Y * 0.5;
				yOffset *= signFlip;
				break;
			}

			default:
				yOffset = 0;
		}

		FVector2D iconRenderTransOffset = FVector2D( xOffset, yOffset );
		icon.SetRenderTranslation( iconRenderTransOffset );

		UpdateShadowIcon();
	}

	void SetIcon( UObject newIcon )
	{
		FSlateBrush changedBrush = GetNewBrushThatIsCopyOf( icon.Brush );
		changedBrush.ResourceObject = newIcon;
		icon.SetBrush( changedBrush );
		UpdateShadowIcon();
	}

	void UpdateShadowIcon()
	{
		FSlateBrush shadowBrush = GetNewBrushThatIsCopyOf( icon.Brush );
		shadowBrush.TintColor = shadowColor;
		shadow.SetBrush( shadowBrush );
		shadow.SetRenderScale( icon.RenderTransform.Scale * shadowScale );
		
		FVector2D renderTranslation = FVector2D( icon.RenderTransform.Translation + ( shadowOffset / shadow.RenderTransform.Scale ) );
		FVector rotatedTrans = FVector( renderTranslation.X, renderTranslation.Y, 0 );
		rotatedTrans = rotatedTrans.RotateAngleAxis( -widgetRenderAngle, FVector::UpVector );
		renderTranslation = FVector2D( rotatedTrans.X, rotatedTrans.Y );

		shadow.SetRenderTranslation( renderTranslation );
	}
}