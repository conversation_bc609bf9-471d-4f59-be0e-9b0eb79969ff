UCLASS( Abstract )
class UAS_KillMessage : UNC_DisplayWidget
{
	default StateOnConstruction = EDisplayConstructVisibility::Hidden;
	default HideVisibility		= ESlateVisibility::Collapsed;

	UPROPERTY( BlueprintReadOnly, NotEditable, BindWidget )
	private URichTextBlock messageText;

	UFUNCTION( BlueprintOverride )
	void Construct()
	{
		messageText.SetText( Text::EmptyText );
	}

	UFUNCTION()
	void DisplayMessage( FText msgText )
	{
		messageText.SetText( msgText );
		Show();
	}

	UFUNCTION( BlueprintOverride )
	void OnShowEnd()
	{
		Hide();
	}
}