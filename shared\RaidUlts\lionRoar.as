const float32 BUILD_RADIUS = 5000;

UAS_SharedSystem_LionRoarManager LionRoarManager()
{
	return Cast<UAS_SharedSystem_LionRoarManager>( UNCGameplaySystemsSubsystem::Get_SharedSystem( GetCurrentWorld(), UAS_SharedSystem_LionRoarManager::StaticClass() ) );
}

class UAS_SharedSystem_LionRoarManager : UNCGameplaySystem_Shared
{
	TMap<ANCDestructible, int> trackedDestructibles;

	UPROPERTY( EditDefaultsOnly )
	UNiagaraSystem explosiveImpact;

	UPROPERTY( EditDefaultsOnly )
	UImpactTablePrimaryAsset explosiveImpactTable;

	UPROPERTY( EditDefaultsOnly )
	private FNCMeleeResponseBehavior meleeResponseBehavior;

	//network
	private AAS_LionRoarManagerNetworkActor networkActor;

	UFUNCTION(BlueprintOverride)
	void BeginPlay()
	{
		if ( IsServer() )
			networkActor = Cast<AAS_LionRoarManagerNetworkActor>( Server_SpawnEntity( AAS_LionRoarManagerNetworkActor::StaticClass() ) );
	}

	void cl_InitNetworkActor(AAS_LionRoarManagerNetworkActor networkActorIn )
	{
		networkActor = networkActorIn;
	}

	void RegisterDestructible( ANCDestructible destructible )
	{
		ScriptAssert( IsServer(), "SERVER ONLY" );

		if ( !trackedDestructibles.Contains( destructible ) )
			trackedDestructibles.Add( destructible, 0 );

		if ( trackedDestructibles[destructible] == 0 )
		{
			destructible.OnDestructiblePreDmg.BindUFunction( this, n"OnDestructiblePreDamage" );
			networkActor.RegisterDestructible(destructible);
		}

		trackedDestructibles[destructible]++;
	}

	void UnregisterDestructible( ANCDestructible destructible )
	{
		ScriptAssert( IsServer(), "SERVER ONLY" );

		trackedDestructibles[destructible]--;

		if ( trackedDestructibles[destructible] == 0 )
		{
			destructible.OnDestructiblePreDmg.Clear();
			networkActor.UnregisterDestructible(destructible);
		}
	}

	bool IsRegisteredActor( AActor actor )
	{
		if ( !IsValid(networkActor) )
			return false;

		for ( FNetRegisteredLionRoarDestructible entry : networkActor.net_RegisteredDestructibles )
		{
			if ( entry.net_Destructible.GetEntity() == actor )
				return true;
		}

		return false;
	}

	FNCMeleeResponseBehavior GetMeleeResponseBehavior() const
	{
		return meleeResponseBehavior;
	}

	UFUNCTION()
	private void OnDestructiblePreDamage( FDamageInfo& damageInfo )
	{
		if ( !UNCUtils::CheckForTriggersAtPosition( this, damageInfo.traceHitResult.ImpactPoint, AAS_LionRoarCapsuleCollision::StaticClass() ) )
		{
			return;
		}

		damageInfo.damage *= 10;
		DoDamageVFX( damageInfo );
	}

	void DoDamageVFX( const FDamageInfo& damageInfo )
	{
		if ( IsValid( damageInfo.attacker ) )
		{
			if ( IsValid( damageInfo.traceHitResult.PhysMaterial ) )
			{
				Server_PlayImpactTableAtPoint( damageInfo.attacker.NCNetComponent, explosiveImpactTable, damageInfo.traceHitResult.ImpactPoint, damageInfo.traceHitResult.ImpactNormal.Rotation(), damageInfo.traceHitResult.PhysMaterial );
			}
			else
			{
				Server_SpawnEffectAtLocation_OneShot( explosiveImpact, damageInfo.traceHitResult.ImpactPoint );
			}
		}
		else
		{
			Server_SpawnEffectAtLocation_OneShot( explosiveImpact, damageInfo.traceHitResult.ImpactPoint );
		}
	}
}

UCLASS( Abstract )
class AAS_LionRoarCapsuleCollision : AActor
{
	UPROPERTY( DefaultComponent, RootComponent )
	USceneComponent root;

	UPROPERTY( DefaultComponent )
	UDecalComponent decalMaterial;

	UPROPERTY( DefaultComponent )
	UCapsuleComponent collisionCapsule;
	default collisionCapsule.CollisionEnabled	  = ECollisionEnabled::QueryOnly;
	default collisionCapsule.CollisionProfileName = n"OverlapAllDynamic";
	default collisionCapsule.CapsuleHalfHeight	  = BUILD_RADIUS;
	default collisionCapsule.CapsuleRadius		  = 500;
	default collisionCapsule.RelativeLocation	  = FVector( collisionCapsule.CapsuleHalfHeight, 0, 0 );
	default collisionCapsule.RelativeRotation	  = FRotator( 90, 0, 0 );


	TArray<AActor> GetTargetActors( ANCPlayerCharacter owner )
	{
		FVector start = GetActorLocation();// + ( GetActorForwardVector() * 500 );
		FVector end = start + ( GetActorForwardVector() * BUILD_RADIUS * 1.75 );

		TArray<AActor> IgnoreActors;
		IgnoreActors.Add( owner );

		TArray<EObjectTypeQuery> ObjectTypes_Dome;
		ObjectTypes_Dome.Add( EObjectTypeQuery::WorldDynamic );

		//run sphere trace to check for dome collision
		TArray<FHitResult> SearchedObjects_Dome;
		SearchedObjects_Dome = SphereTraceMultiForObjects( GetActorLocation(), end, 500 * 0.25, ObjectTypes_Dome, false, IgnoreActors, true );

		for( auto t : SearchedObjects_Dome )
		{
			if ( IsValid( t.Actor ) && t.Actor.IsA( AAS_RaidDomeShield::StaticClass() ) )
			{
				end = t.Location;
				break; //update length of collision to stop on the raid dome sphere
			}
		}

		TArray<AActor> actors;

		float32 newEndDistance = Distance( start, end );
		TArray<AActor> overlappingActors;
		collisionCapsule.GetOverlappingActors( overlappingActors );
		for( auto o : overlappingActors )
		{
			if ( Distance( start, o.GetActorLocation() ) < newEndDistance )
				actors.Add( o );
		}

		TArray<EObjectTypeQuery> ObjectTypes;
		ObjectTypes.Add( EObjectTypeQuery::Pawn );

		TArray<FHitResult> SearchedObjects;
		SearchedObjects = SphereTraceMultiForObjects( GetActorLocation(), end, 500, ObjectTypes, false, IgnoreActors, true );

		for ( FHitResult _object : SearchedObjects )
		{
			if ( !IsValid( _object.Actor ) )
				continue;
			if ( !_object.Actor.IsA( AAS_PlayerEntity::StaticClass() ) )
				continue;
			actors.Add( _object.Actor );
		}

		TArray<AActor> targets = GenerateTargetsFromList( actors, owner );
		return targets;
	}

	TArray<AActor>
	GenerateTargetsFromList( TArray<AActor> list, ANCPlayerCharacter ownerPlayer )
	{
		TArray<AActor> n;

		for ( auto t : list )
		{
			if ( !IsValid( t ) )
				continue;
			if ( t.IsA( AAS_Vendor::StaticClass() ) )
				continue;
			UHealthComponent health = Cast<UHealthComponent>( t.GetComponent( UHealthComponent::StaticClass() ) );
			if ( !IsValid( health ) )
				continue;
			if ( health.GetHealth() <= 0 )
				continue;
			if ( IsValid(t.GetKinematicPhysicsTreeComponent()))	//don't bust physics trees falling over
				continue;
			n.Add( t );
		}

		return n;
	}
}

UCLASS( Abstract )
class AAS_LionRoar : ANCDefaultActor
{
	UPROPERTY( DefaultComponent, RootComponent )
	USceneComponent root;

	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<AAS_LionRoarComponentMarker> markerClass;

	UPROPERTY( EditDefaultsOnly )
	TSubclassOf<AAS_LionRoarCapsuleCollision> capsuleClass;

	UPROPERTY( DefaultComponent )
	UChildActorComponent capsuleActorComponent;
	default capsuleActorComponent.ChildActorClass = capsuleClass;

	UPROPERTY( EditDefaultsOnly )
	UNiagaraSystem roarEffectAsset;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset lionRoarStart1P;
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset lionRoarStart3P;
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset lionRoarTunnelSound;
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset initialStructureImpact1P;
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset initialStructureImpact3P;
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset subsequentStructureImpact1P;
	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset subsequentStructureImpact3P;

	TArray<ANCDestructible> codeDestructibles;

	UPROPERTY( EditDefaultsOnly )
	UNiagaraSystem explodeEffect;

	UPROPERTY()
	FNCNetBool isBeingDestroyed;

	UPROPERTY( EditDefaultsOnly )
	float32 fadeTime;
	default fadeTime = 2.0;

	UPROPERTY( EditDefaultsOnly )
	float32 damageToPlayers = 40.0f;
	UPROPERTY( EditDefaultsOnly )
	float32 damageToStructures = 5000.0f;

	UFUNCTION( BlueprintOverride )
	void ServerBeginPlay()
	{
		// wait for components to initialize
		Thread( this, n"DamageTargets" );
		Server_EmitSoundAtLocation_1P3P( lionRoarStart1P, lionRoarStart3P, this.GetActorLocation(), GetOwnerPlayer() );
		Server_SpawnEffectAtLocation_OneShot( roarEffectAsset, GetActorLocation(), GetActorRotation() );

		// debug fade on decal
		//System::SetTimer( this, n"DebugFade", 5.0, false );
		System::SetTimer( this, n"DestroyRoar", 3.0, false );
	}

	UFUNCTION( BlueprintOverride )
	void ClientBeginPlay()
	{
		capsuleActorComponent.ChildActor.SetActorLocation( GetActorLocation() );
		capsuleActorComponent.ChildActor.SetActorRotation( GetActorRotation() );
		capsuleActorComponent.ChildActor.SetActorEnableCollision( false );
		isBeingDestroyed.OnReplicated().AddUFunction( this, n"OnIsBeingDestroyedChanged" );
	}

	UFUNCTION()
	void DebugFade()
	{
		isBeingDestroyed.SetNetValue( true );
	}

	ANCWeapon ownerWeapon;
	void SetOwnerWeapon( ANCWeapon inOwnerWeapon )
	{
		ownerWeapon = inOwnerWeapon;
	}

	AAS_ShieldBreaker breacher;
	void SetBreacher( AAS_ShieldBreaker inBreacher )
	{
		breacher = inBreacher;
		breacher.OnEndPlay.AddUFunction( this, n"OnBreacherEndPlay" );
	}

	UFUNCTION()
	void DestroyRoar()
	{
		isBeingDestroyed.SetNetValue( true );
		System::SetTimer( this, n"DestroyAfterTime", fadeTime, false );
	}

	UFUNCTION()
	private void OnBreacherEndPlay( AActor Actor, EEndPlayReason EndPlayReason )
	{
		DestroyRoar();
	}

	UFUNCTION()
	void DestroyAfterTime()
	{
		Destroy();
	}

	UFUNCTION()
	void OnIsBeingDestroyedChanged( bool old, bool new )
	{
		if ( !new )
			return;

		Thread( this, n"Thread_FadeOutDecal" );
	}

	UFUNCTION()
	void Thread_FadeOutDecal( UNCCoroutine co )
	{
		AActor childCapsule = capsuleActorComponent.ChildActor;
		if ( !IsValid( childCapsule ) )
			return;
		UDecalComponent decal = Cast<UDecalComponent>( childCapsule.GetComponentByClass( UDecalComponent::StaticClass() ) );
		if ( !IsValid( decal ) )
			return;

		UMaterialInstanceDynamic dynDecal = decal.CreateDynamicMaterialInstance();

		int startTime = GetGameTimeMS();
		int endTime	  = GetGameTimeMS() + TO_MILLISECONDS( float32( fadeTime ) );
		while ( GetGameTimeMS() < endTime )
		{
			float fade = float( GetGameTimeMS() - startTime ) / float( endTime - startTime );
			dynDecal.SetScalarParameterValue( n"DecalFadeOut", fade );
			co.Wait( 0.1 );
		}
	}

	UFUNCTION( BlueprintOverride )
	void EndPlay( EEndPlayReason EndPlayReason )
	{
		for ( UAS_StructureHealthComponent component : affectedHealthComponents )
		{
			if ( IsValid( component ) )
				component.onStructuralPreDamage_modifyDamage.UnbindObject( this );
		}

		for ( ANCDestructible destructible : codeDestructibles )
		{
			if ( IsValid( destructible ) )
				LionRoarManager().UnregisterDestructible( destructible );
		}

		for ( AActor actor : actorsSpawned )
		{
			if ( IsValid( actor ) )
				actor.Destroy();
		}
	}

	TArray<UAS_StructureHealthComponent> affectedHealthComponents;
	TArray<AActor> actorsSpawned;

	UFUNCTION()
	private void ApplyWeakenedDamage( FDamageInfo damageInfo, float32 originalDamage, float32& outDamage )
	{
		if ( !UNCUtils::CheckForTriggersAtPosition( this, damageInfo.traceHitResult.ImpactPoint, AAS_LionRoar::StaticClass() ) )
		{
			return;
		}

		outDamage *= 10.0;
		LionRoarManager().DoDamageVFX( damageInfo );
	}

	UFUNCTION( NotBlueprintCallable )
	void DamageTargets( UNCCoroutine co )
	{
		co.Wait( 0.1 );

		AAS_LionRoarCapsuleCollision capsuleActor = Cast<AAS_LionRoarCapsuleCollision>( capsuleActorComponent.ChildActor );
		TArray<AActor> actors					  = capsuleActor.GetTargetActors( GetOwnerPlayer() );
		capsuleActor.collisionCapsule.SetCollisionProfileName( n"Trigger" );

		TArray<AActor> sortedActors = actors;

		for ( int i = 0; i < sortedActors.Num(); i++ )
		{
			for ( int j = i + 1; j < sortedActors.Num(); j++ )
			{
				if ( Distance2D( sortedActors[i].GetActorLocation(), GetActorLocation() ) > Distance2D( sortedActors[j].GetActorLocation(), GetActorLocation() ) )
				{
					AActor temp		= sortedActors[i];
					sortedActors[i] = sortedActors[j];
					sortedActors[j] = temp;
				}
			}
		}

		int MAX_PER_FRAME  = 1;
		int count		   = 0;
		bool isFirstFrame  = true;
		int numberAffected = 0;

		for ( AActor actor : sortedActors )
		{
			if ( !IsValid( actor ) )
				continue;

			UAS_StructureHealthComponent sHealthComponent = Cast<UAS_StructureHealthComponent>( actor.GetComponentByClass( UAS_StructureHealthComponent::StaticClass() ) );
			UAS_ShieldHealthComponent pHealthComponent = Cast<UAS_ShieldHealthComponent>( actor.GetComponentByClass( UAS_ShieldHealthComponent::StaticClass() ) );
			FVector pos;

			FDamageInfo info;
			info.attacker			  = GetOwnerPlayer();
			info.damageSourceLocation = GetActorLocation();
			info.damage = damageToStructures;
			info.scriptDamageFlags	   = EScriptDamageFlags::DF_KNOCKBACK | EScriptDamageFlags::DF_STRUCTURAL | EScriptDamageFlags::DF_RESOURCE;
			if ( IsValid( ownerWeapon ) )
				info.damageWeaponClassName = ownerWeapon.GetWeaponClass();

			if ( IsValid( sHealthComponent ) && actor.Class.IsChildOf( ANCDefaultActor::StaticClass() ) )
			{
				affectedHealthComponents.Add( sHealthComponent );
				sHealthComponent.onStructuralPreDamage_modifyDamage.AddUFunction( this, n"ApplyWeakenedDamage" );

				ANCDefaultActor netActor		   = Cast<ANCDefaultActor>( sHealthComponent.GetOwner() );
				AAS_LionRoarComponentMarker marker = Cast<AAS_LionRoarComponentMarker>( Server_SpawnEntity( markerClass, GetOwnerPlayer() ) );
				marker.SetActor( netActor );
				actorsSpawned.Add( marker );
				pos = netActor.GetBoundsCenter();
			}
			else if ( actor.Class.IsChildOf( ANCDestructible::StaticClass() ) )
			{
				ANCDestructible destuctible = Cast<ANCDestructible>( actor );
				codeDestructibles.Add( destuctible );
				LionRoarManager().RegisterDestructible( destuctible );
				pos = destuctible.GetBoundsCenter();
				numberAffected++;
			}
			else if ( IsValid( pHealthComponent ) && actor.IsA( AAS_PlayerEntity::StaticClass() ) )
			{
				AAS_PlayerEntity tPlayer = Cast<AAS_PlayerEntity>( actor );
				FHitResult hit = HackGetImpact( GetOwnerPlayer().GetEyeLocation(), tPlayer.GetActorLocation() );
				info.traceHitResult = hit;
				info.damage = damageToPlayers;

				tPlayer.AddStatusEffect( GameplayTags::StatusEffect_MoveSlow, 1.0, 1.0, 0.0, 0.25 );
				tPlayer.AddStatusEffect( GameplayTags::StatusEffect_ScreenShake, 1.0, 1.0, 0.0, 0 );
			}

			UHealthComponent hComp = actor.GetHealthComponent();
			if ( IsValid( hComp ) )
				hComp.ReceiveDamageSimple( info );

			Server_SpawnEffectAtLocation_OneShot( explodeEffect, pos );
			if ( isFirstFrame )
				Server_EmitSoundAtLocation_1P3P( initialStructureImpact1P, initialStructureImpact3P, pos, GetOwnerPlayer() );
			else
				Server_EmitSoundAtLocation_1P3P( subsequentStructureImpact1P, subsequentStructureImpact3P, pos, GetOwnerPlayer() );

			count++;
			if ( count >= MAX_PER_FRAME )
			{
				isFirstFrame = false;
				count		 = 0;
				co.Wait( 0.1 );
			}
		}

		ANCPlayerCharacter owner = GetOwnerPlayer();

		if ( IsAlive( owner ) )
		{
			co.EndOn( owner, owner.HealthComponent.OnDeathSignal );
			co.Wait( 2.0 );

			if ( numberAffected > 0 )
			{
				owner.PlayBattleChatter( GameplayTags::Audio_VO_BattleChatter_UsingAbility_Ultimate );
			}
		}
	}

	FHitResult HackGetImpact( FVector Start, FVector End )
	{
		FVector Dir = End - Start;
		Dir.Normalize();
		FVector cEnd = End + ( Dir * 10 );

		TArray<AActor> IgnoreActors;
		IgnoreActors.Add( GetOwnerPlayer() );

		FHitResult outHit = LineTraceSingle( Start, cEnd, ETraceTypeQuery::WeaponFine, true, IgnoreActors, true );
		return outHit;
	}
}

UCLASS( Abstract )
class AAS_LionRoarComponentMarker : ANCDefaultActor
{
	UPROPERTY()
	FNCNetEntityHandle actorToCopy;

	UPROPERTY( DefaultComponent )
	USceneComponent root;

	UPROPERTY( EditDefaultsOnly )
	UMaterialInstance damageMaterial;

	UPROPERTY( EditDefaultsOnly )
	UNCAudioAsset shakeSound;
	TArray<USceneComponent> sceneComps;

	void SetActor( ANCDefaultActor actor )
	{
		actorToCopy.SetNetValue( actor );

		FVector pos;
		FVector extent;
		actor.GetActorBounds( true, pos, extent );
	}

	UFUNCTION( BlueprintOverride )
	void ClientBeginPlay()
	{
		actorToCopy.OnReplicated().AddUFunction( this, n"OnCopyActorChanged" );
	}

	UFUNCTION( BlueprintOverride )
	void EndPlay( EEndPlayReason EndPlayReason )
	{
		if ( IsClient() )
		{
			Client_StopSound( soundId );

			ClearVisualEffects();
		}
	}

	void ApplyVisualEffects( const AActor actor )
	{
		actor.GetComponentsByClass( USceneComponent::StaticClass(), sceneComps );
		for ( USceneComponent component : sceneComps )
		{
			if ( component.IsA( UAS_StructureUpgradeVisual::StaticClass() ) )
			{
				UAS_StructureUpgradeVisual upgradeVisual = Cast<UAS_StructureUpgradeVisual>( component );
				if ( upgradeVisual.levelForVisual != 1 )
					continue; // ignore upgrade visuals for future levels
			}
			UStaticMeshComponent meshComponent = Cast<UStaticMeshComponent>( component );
			if ( IsValid( meshComponent ) )
			{
				meshComponent.NCSetReceivesNCSpecialDecals( true );
			}
		}
	}

	void ClearVisualEffects()
	{
		for ( USceneComponent component : sceneComps )
		{
			if ( !IsValid( component ) )
				continue;

			if ( component.IsA( UAS_StructureUpgradeVisual::StaticClass() ) )
			{
				UAS_StructureUpgradeVisual upgradeVisual = Cast<UAS_StructureUpgradeVisual>( component );
				if ( upgradeVisual.levelForVisual != 1 )
					continue; // ignore upgrade visuals for future levels
			}
			UStaticMeshComponent meshComponent = Cast<UStaticMeshComponent>( component );
			if ( IsValid( meshComponent ) )
			{
				meshComponent.NCSetReceivesNCSpecialDecals( false );
			}
		}
		sceneComps.Empty();
	}

	int soundId;

	UFUNCTION()
	private void OnCopyActorChanged( const AActor oldValue, const AActor newValue )
	{
		Client_StopSound( soundId );
		ClearVisualEffects();

		if ( IsValid( newValue ) )
		{
			FAudioResultData results = Client_EmitSoundAtLocation_WithOwner( shakeSound, newValue, newValue.GetActorLocation() );
			soundId					 = results.EventID;
			SetActorLocation( newValue.GetActorLocation() );
			root.SetWorldLocation( newValue.GetActorLocation() );

			ApplyVisualEffects( newValue );
		}
	}

	UFUNCTION()
	private void OnIsActiveChanged( bool oldValue, bool newValue )
	{
		root.SetHiddenInGame( true );
		root.SetVisibility( false );
	}
}

USTRUCT()
struct FNetRegisteredLionRoarDestructible
{
	UPROPERTY(Transient)
	FNCNetEntityHandle net_Destructible;
}

UCLASS()
class AAS_LionRoarManagerNetworkActor : ANCDefaultActor
{
	access internal = private, UAS_SharedSystem_LionRoarManager;

	UPROPERTY()
	TArray<FNetRegisteredLionRoarDestructible> net_RegisteredDestructibles;

	UFUNCTION(BlueprintOverride)
	void ClientBeginPlay()
	{
		LionRoarManager().cl_InitNetworkActor( this );
	}

	access:internal void RegisterDestructible( ANCDestructible destructible )
	{
		ScriptAssert( IsServer(), "SERVER ONLY" );

		FNetRegisteredLionRoarDestructible netEntry;
		netEntry.net_Destructible.SetNetValue(destructible);
		net_RegisteredDestructibles.Add( netEntry );
	}

	access:internal void UnregisterDestructible( ANCDestructible destructible )
	{
		ScriptAssert( IsServer(), "SERVER ONLY" );

		int removeIdx = -1;
		for ( int i = 0; i < net_RegisteredDestructibles.Num(); i++ )
		{
			FNetRegisteredLionRoarDestructible entry = net_RegisteredDestructibles[i];
			if ( entry.net_Destructible.GetEntity() == destructible )
			{
				removeIdx = i;
				break;
			}
		}

		if ( removeIdx != -1 )
			net_RegisteredDestructibles.RemoveAt(removeIdx);
	}
}